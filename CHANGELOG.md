# Changelog

All notable changes to the DataStatPro project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.1.0] - 2025-06-15

### Added
- Guest Access functionality for users to explore the application without registration
- Pivot Analysis feature for advanced data exploration and summarization
- Sample Size Calculators for research planning and statistical power analysis
- Enhanced Logistic Regression capabilities with additional diagnostics and visualizations
- Epidemiological Calculator (Epi Calculator) for epidemiological measures and study designs
  - Cross-Sectional study calculations
  - Case-Control study calculations
  - Cohort study calculations
  - Sample Size & Power Calculator for epidemiological studies

### Improved
- Enhanced user interface with better navigation and accessibility
- Optimized performance for statistical calculations
- Improved data visualization components
- Better mobile responsiveness
- Updated documentation with new feature guides

### Fixed
- Various bugs in statistical calculations
- UI rendering issues on different screen sizes
- Data import/export edge cases
- Performance issues with large datasets

## [1.0.0] - 2025-05-02

### Added
- Initial release of Statistica with the following features:
  - Complete data management module
    - Data import (CSV)
    - Data export (CSV, JSON)
    - Data editor with spreadsheet-like interface
    - Data transformation tools
  - Descriptive statistics module
    - Basic descriptive statistics
    - Frequency tables
    - Cross-tabulation
    - Normality tests
  - Inferential statistics module
    - t-tests (one-sample, independent, paired)
    - ANOVA
    - Non-parametric tests
    - Statistical assumption checker
  - Correlation analysis module
    - Correlation matrix with heatmap
    - Simple linear regression
    - Logistic regression
  - Data visualization module
    - Bar charts
    - Pie charts
    - Histograms
    - Box plots
    - Scatter plots
- Modern UI with Material-UI components
- Responsive design for desktop and tablet
- Comprehensive documentation
- Sample data generators

### Improved
- Enhanced performance for large datasets using virtualization and lazy loading
- Improved accessibility features for screen readers and keyboard navigation
- Optimized statistical calculations for better performance
- Added more descriptive error messages and user feedback
- Enhanced data visualization with better tooltips and interactive elements
- Improved mobile responsiveness for small screens

### Fixed
- Issues with CSV parsing for special characters
- Performance bottlenecks in correlation matrix calculation
- Memory leaks in data visualization components
- Incorrect p-value calculation in logistic regression
- UI layout issues on mobile devices
- Data export errors with large datasets
- Normality test calculation edge cases

### Technical Features
- React 18 with TypeScript
- Context API for state management
- Custom hooks for reusable logic
- Recharts and D3.js for visualizations
- JStat for statistical calculations
- Material-UI v5 for component library
- Vite for fast development and building

## [0.9.0] - 2025-04-15

### Added
- Beta version with core functionality
- Initial implementation of all primary modules
- Basic testing and documentation

### Fixed
- Various bugs and performance issues identified during beta testing

## [0.8.0] - 2025-03-20

### Added
- Alpha version with limited features
- Early implementation of data management and descriptive statistics
- Prototype UI design