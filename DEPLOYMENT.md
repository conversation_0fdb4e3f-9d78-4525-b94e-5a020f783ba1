# Statistica Deployment Guide for Shared Hosting

## Build Configuration

The application has been configured for deployment on shared hosting with the following optimizations:

1. **Relative Asset Paths**: The Vite configuration uses `base: './'` to ensure all assets use relative paths, making the application work correctly in subdirectories.

2. **Optimized Build Settings**:
   - Assets are organized in the `assets` directory
   - Source maps are disabled for production
   - CSS code splitting is enabled
   - Manual chunk splitting for vendor libraries to improve loading performance

3. **Build Process**: The TypeScript checking has been made optional with separate build commands:
   - `npm run build`: Quick build without TypeScript checking
   - `npm run build:check`: Full build with TypeScript checking

## Deployment Steps

1. **Build the Application**:
   ```
   npm run build
   ```

2. **Upload Files to Hosting**:
   - Upload the entire contents of the `dist` directory to your web hosting service
   - Make sure to maintain the directory structure

3. **Server Configuration**:
   - If using Apache, no additional configuration is needed
   - If using Nginx, ensure proper configuration for serving static files

4. **Testing**:
   - After uploading, test the application by navigating to your domain
   - Verify that all assets load correctly
   - Test core functionality to ensure everything works as expected

## Troubleshooting

- **404 Errors for Assets**: Ensure all files from the `dist` directory were uploaded with the correct structure
- **API Connection Issues**: If connecting to backend APIs, make sure CORS is properly configured
- **Blank Page**: Check browser console for JavaScript errors

## Additional Notes

- The application is configured to work in any subdirectory on your hosting
- All assets use relative paths, so no additional configuration is needed
- The build is optimized for size and performance with code splitting