# DataStatPro - Statistical Analysis Web Application

Statistica is a comprehensive web application for statistical analysis with a modern, sleek design and user-friendly UI. It provides a wide range of analytical tools for data exploration, visualization, and advanced statistical analysis.

![Statistica App](docs/assets/screenshots/statistica-dashboard.png)

## Features

### User Access
- Full-featured registered user accounts with data saving capabilities
- Guest Access functionality for exploring the application without registration
- User profile management and customizable settings

### Data Management
- Direct data entry capabilities
- Import/export functionality from CSV and other formats
- Data editing, recoding, and transformations
- Sample dataset generation for learning and testing

### Descriptive Statistics
- **Qualitative Data Analysis**: Frequency tables, cross-tabulations
- **Quantitative Data Analysis**: Mean, median, mode, variance, standard deviation, skewness, kurtosis, normality tests
- Comprehensive summary statistics with interpretations

### Statistical Inference
- One-sample t-test
- Independent samples t-test
- Paired samples t-test
- One-way ANOVA
- Non-parametric alternatives (Mann-<PERSON> U, <PERSON>, <PERSON><PERSON><PERSON>-Wallis)
- Statistical assumption checks and diagnostics

### Correlational Analysis
- Correlation matrices with heatmap visualization
- Simple linear regression with diagnostic plots
- Enhanced logistic regression for binary outcomes with advanced diagnostics and visualizations
- Model summaries and interpretations

### Data Visualization
- Interactive and customizable charts:
  - Bar charts with multiple series support
  - Pie charts and donut charts
  - Histograms with normal curve overlays
  - Box plots with outlier detection
  - Scatter plots with regression lines
- Export options for publications and reports

### Pivot Analysis
- Interactive pivot tables for data exploration
- Drag-and-drop interface for creating custom views
- Multiple aggregation methods (sum, average, count, etc.)
- Visual representation of aggregated data

### Sample Size Calculators
- One-sample calculators (proportion, mean)
- Two-sample calculators (proportions, means)
- Paired sample calculators
- Multi-group sample size estimation
- Power analysis and visualization

### Epidemiological Calculator (Epi Calculator)
- Cross-sectional study calculations (prevalence, prevalence ratio)
- Case-control study calculations (odds ratio, adjusted odds ratio)
- Cohort study calculations (risk ratio, risk difference, attributable risk)
- Matched case-control analysis
- Sample size and power calculations for epidemiological studies

## Technology Stack

- **Frontend Framework**: React v18 with TypeScript
- **UI Components**: Material-UI v5
- **Visualization Libraries**: Recharts, D3.js
- **Statistical Computation**: JStat, Math.js
- **Data Parsing**: PapaParse
- **Build Tool**: Vite

## Getting Started

### Prerequisites
- Node.js (v16 or later)
- npm (v8 or later)

### Installation

1. Clone the repository
   ```
   git clone https://github.com/yourusername/DataStatPro.git
   cd DataStatPro
   ```

2. Install dependencies
   ```
   npm install
   ```

3. Start the development server
   ```
   npm run dev -- --port 8001 --host 0.0.0.0
   ```

4. Open your browser and navigate to `http://localhost:8001`

### Building for Production

1. Create an optimized production build
   ```
   npm run build
   ```

2. Preview the production build locally
   ```
   npm run preview
   ```

## Usage

1. **Import Data**: Start by importing your dataset via CSV or generate sample data
2. **Explore Data**: Use descriptive statistics to understand your data's characteristics
3. **Visualize Data**: Create charts to visualize distributions and relationships
4. **Perform Analysis**: Run statistical tests to answer your research questions
5. **Export Results**: Save or export your results for reporting

For detailed instructions on using each feature, please refer to the [User Guide](docs/USER_GUIDE.md).

## Project Structure

```
DataStatPro/
├── docs/                      # Documentation
│   ├── USER_GUIDE.md         # Comprehensive user guide
│   └── TESTING.md            # Testing documentation
├── src/
│   ├── components/
│   │   ├── Auth/              # Authentication components
│   │   ├── Layout/            # Main layout components
│   │   ├── DataManagement/    # Data import, export, editing
│   │   ├── DescriptiveStats/  # Descriptive statistics components
│   │   ├── InferentialStats/  # Statistical inference components
│   │   ├── CorrelationAnalysis/# Correlation and regression tools
│   │   ├── Visualization/     # Visualization components
│   │   ├── PivotAnalysis/     # Pivot table analysis components
│   │   ├── SampleSizeCalculators/ # Sample size calculation tools
│   │   ├── EpiCalc/           # Epidemiological calculators
│   │   ├── ResultsManager/    # Analysis results management
│   │   └── UI/                # Reusable UI components
│   ├── context/               # React context providers
│   ├── pages/                 # Page components
│   ├── types/                 # TypeScript type definitions
│   ├── utils/                 # Utility functions and statistical methods
│   ├── theme.ts               # Theme configuration
│   ├── App.tsx                # Main application component
│   └── main.tsx               # Entry point
├── public/                    # Static assets
├── index.html                 # HTML entry point
└── package.json               # Project configuration
```

## Key Components

### Data Management

The data management module allows users to:

- Import data from CSV files
- Generate sample datasets for practice
- Edit data in a spreadsheet-like interface
- Transform variables using various methods
- Export data in multiple formats

### Statistical Analysis

Statistica provides a comprehensive set of statistical tools:

- **Descriptive Statistics**: Summarize and understand your data's properties
- **Inferential Statistics**: Test hypotheses and make inferences about populations
- **Correlation Analysis**: Examine relationships between variables and create predictive models

### Visualization

The visualization module offers interactive charts that can be customized with:

- Different color schemes
- Custom titles and labels
- Various display options
- Export capabilities

## Contributing

We welcome contributions to Statistica! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- JStat for statistical computations
- Material-UI for the component library
- D3.js and Recharts for visualization capabilities
- The open-source community for various libraries and tools

## Support

For questions, issues, or feature requests, please:

1. Check the [User Guide](docs/USER_GUIDE.md) for documentation
2. Search existing [Issues](https://github.com/yourusername/DataStatPro/issues) before creating a new one
3. Create a detailed issue if you find a bug or want to request a feature