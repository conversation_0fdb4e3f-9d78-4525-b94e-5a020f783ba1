# Analysis Assistant Training System

## Overview

The Analysis Assistant Training System is an interactive solution designed to improve the accuracy of statistical analysis suggestions by building a curated database of question-answer mappings through user training sessions.

## Problem Solved

**Original Issues:**
- When asked "How do I test if age is correlated with test scores?", the system suggested "normality test" and "Kruskal-Wallis test" instead of correlation analyses
- When asked "What test for categorical data?", most suggestions were irrelevant to categorical data analysis
- The hardcoded scenario matching was providing incorrect or irrelevant suggestions

**Solution:**
- Interactive training system to capture correct analysis recommendations
- Curated suggestions database that prioritizes validated mappings
- Improved query processing that uses trained data first, then falls back to original logic

## System Components

### 1. Training Database (`src/utils/trainingDatabase.ts`)
- **TrainingDatabaseManager**: Singleton class managing all training data
- **Data Structures**: Questions, Answers, Sessions, Curated Suggestions
- **Storage**: Local storage with export/import functionality
- **Features**: Question management, answer validation, session tracking, statistics

### 2. Training Interface (`src/components/InferentialStats/TrainingInterface.tsx`)
- **Interactive UI** for creating and managing training questions
- **Answer Management** for capturing correct analysis recommendations
- **Session Management** for organizing training workflows
- **Statistics Dashboard** showing training progress and accuracy

### 3. Training Data Initializer (`src/utils/trainingDataInitializer.ts`)
- **Pre-populated Data** with common statistical questions and correct answers
- **Initialization Logic** that runs on first load
- **Curated Suggestions Generation** from validated training data

### 4. Enhanced Analysis Assistant (`src/components/InferentialStats/AnalysisAssistant.tsx`)
- **Improved Query Processing** using curated database first
- **Training Interface Access** via dedicated training button
- **Fallback Logic** to original scenarios when no curated matches found

## How to Use

### Accessing the Training System

1. **Open Analysis Assistant** in the application sidebar
2. **Click the "Training" button** in the Analysis Assistant header
3. **Training Interface opens** in a full-screen dialog

### Training Workflow

#### 1. Questions Tab
- **Add Questions**: Create new statistical questions with keywords and patterns
- **Edit Questions**: Modify existing questions and their metadata
- **Add Answers**: Provide correct analysis recommendations for each question

#### 2. Sessions Tab
- **Create Sessions**: Organize questions into training sessions
- **Track Progress**: Monitor completion status and progress
- **Manage Workflows**: Structure training activities

#### 3. Curated Tab
- **View Suggestions**: See all curated question-answer mappings
- **Generate from Training**: Convert validated training data to curated suggestions
- **Export/Import**: Backup and share training data

#### 4. Statistics Tab
- **Training Metrics**: View questions, answers, validation status
- **Accuracy Scores**: Monitor system improvement over time
- **Data Management**: Clear or reset training data

### Testing the System

#### Using the Demo Component
```typescript
import TrainingDemo from './components/InferentialStats/TrainingDemo';
// Render the demo component to test the system
```

#### Manual Testing
1. **Test Problematic Queries**:
   - "How do I test if age is correlated with test scores?"
   - "What test for categorical data?"
   - "Test correlation between variables"

2. **Verify Suggestions**:
   - Check that correlation queries suggest CORR1 (Correlation Matrix), VIZ4 (Scatter Plot)
   - Check that categorical queries suggest CAT1 (Chi-Square), DESC2 (Frequency Tables)

#### Programmatic Testing
```typescript
import { runComprehensiveTest, testSpecificQuery } from './utils/testTrainingSystem';

// Run all tests
runComprehensiveTest();

// Test specific query
testSpecificQuery("How do I test if age is correlated with test scores?");
```

## Pre-populated Training Data

The system comes with pre-populated training data for common statistical questions:

### Correlation Questions
- "How do I test if age is correlated with test scores?" → Correlation Matrix, Scatter Plot, Linear Regression
- "Test correlation between variables" → Correlation Matrix, Scatter Plot

### Categorical Data Questions
- "What test for categorical data?" → Chi-Square Test, Frequency Tables, Cross-Tabulation
- "How to test association between categorical variables?" → Chi-Square Test, Cross-Tabulation

### Group Comparison Questions
- "How to compare means between two groups?" → Independent T-Test, Mann-Whitney U Test
- "Compare pre and post measurements" → Paired T-Test, Wilcoxon Signed-Rank Test

### Normality Testing Questions
- "Is my data normally distributed?" → Normality Tests, Histograms, Descriptive Analysis

### Multiple Groups Questions
- "What test should I use for multiple groups?" → One-Way ANOVA, Kruskal-Wallis Test

## Technical Implementation

### Query Processing Flow
1. **User enters query** in Analysis Assistant
2. **System checks curated database** for matching patterns
3. **If matches found**: Return curated suggestions with high confidence
4. **If no matches**: Fall back to original scenario-based processing
5. **Results ranked** by relevance score and confidence

### Database Schema
```typescript
interface TrainingQuestion {
  id: string;
  question: string;
  keywords: string[];
  patterns?: string[];
  category: string;
  difficulty: string;
  context?: string;
}

interface TrainingAnswer {
  id: string;
  questionId: string;
  analysisId: string;
  priority: 'high' | 'medium' | 'low';
  reason: string;
  validated: boolean;
}

interface CuratedSuggestion {
  questionPattern: string;
  keywords: string[];
  regexPatterns: RegExp[];
  suggestions: {
    analysisId: string;
    priority: number;
    reason: string;
    confidence: number;
  }[];
  category: string;
  validated: boolean;
}
```

### Storage and Persistence
- **Local Storage**: All training data stored in browser local storage
- **Export/Import**: JSON format for backup and sharing
- **Real-time Updates**: Changes immediately reflected in suggestions
- **Data Validation**: Ensures data integrity and consistency

## Benefits

### For Users
- **More Accurate Suggestions**: Curated database provides relevant recommendations
- **Contextual Help**: Explanations for why specific analyses are recommended
- **Learning Tool**: Training interface helps understand statistical concepts

### For Developers
- **Maintainable System**: Decoupled from hardcoded scenarios
- **Extensible**: Easy to add new questions and analyses
- **Testable**: Comprehensive testing framework included
- **Data-Driven**: Decisions based on validated training data

### For Organizations
- **Customizable**: Adapt to specific domain knowledge and preferences
- **Collaborative**: Multiple users can contribute to training data
- **Quality Control**: Validation system ensures accuracy
- **Continuous Improvement**: System gets better with more training

## Future Enhancements

1. **Machine Learning Integration**: Use ML models for pattern recognition
2. **Collaborative Training**: Multi-user training sessions
3. **Domain-Specific Training**: Specialized training for different fields
4. **Advanced Analytics**: Detailed usage and accuracy metrics
5. **API Integration**: Connect to external statistical knowledge bases

## Troubleshooting

### Common Issues
1. **No Suggestions Found**: Add more training data for the query type
2. **Incorrect Suggestions**: Review and update existing training answers
3. **Performance Issues**: Clear old training data or optimize database
4. **Import/Export Problems**: Check JSON format and file permissions

### Support
- Check browser console for error messages
- Use the demo component to test functionality
- Review training statistics for data quality issues
- Export training data for backup before making major changes
