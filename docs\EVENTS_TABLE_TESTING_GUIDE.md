# Events Table Refactoring - Testing & Verification Guide

## 🧪 **Pre-Deployment Testing**

### 1. Database Migration Testing
Before applying the migration to production:

```sql
-- Test the migration on a development/staging environment first
-- Run: supabase/migrations/20250705000000_refactor_events_table_login_only.sql

-- Verify table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'events' AND table_schema = 'public';

-- Check constraints
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_name = 'events' AND table_schema = 'public';

-- Verify indexes
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'events' AND schemaname = 'public';
```

### 2. Application Code Testing
Test the updated AuthContext and AppRouter:

```bash
# Run the development server
npm run dev

# Check browser console for any errors
# Verify no TypeScript compilation errors
```

## 🔍 **Post-Deployment Verification**

### 1. Login Event Logging Test
**Steps:**
1. Sign out of the application
2. Sign back in with valid credentials
3. Check the events table for new login events

**Expected Result:**
```sql
-- Should see new 'app_open' event
SELECT * FROM public.events 
WHERE user_id = 'your-user-id' 
ORDER BY timestamp DESC 
LIMIT 5;
```

### 2. Page Navigation Test
**Steps:**
1. Navigate between different pages in the app
2. Check the events table

**Expected Result:**
- No new `page_view` events should be created
- Only login-related events should exist

### 3. Event Type Constraint Test
**Steps:**
Try to insert a non-login event manually:

```sql
-- This should FAIL due to constraint
INSERT INTO public.events (user_id, event_type, details) 
VALUES ('your-user-id', 'page_view', '{"test": true}');
```

**Expected Result:**
- Error: `new row for relation "events" violates check constraint "events_event_type_check"`

### 4. RLS Policy Test
**Steps:**
1. Sign in as User A
2. Try to query events for User B

```sql
-- This should return no results (RLS blocks access)
SELECT * FROM public.events WHERE user_id = 'other-user-id';
```

### 5. Login Statistics Function Test
**Steps:**
```sql
-- Test the utility function
SELECT * FROM public.get_user_login_stats('your-user-id');
```

**Expected Result:**
- Returns login statistics for the user
- Shows total logins, first/last login dates, recent activity

## 🚨 **Error Scenarios to Test**

### 1. Invalid Event Type
```typescript
// This should be prevented by TypeScript
logLoginEvent('invalid_event'); // Should show TS error
```

### 2. Unauthenticated User
```typescript
// Sign out, then try to log event
// Should log warning and return early
```

### 3. Database Connection Issues
- Test behavior when Supabase is temporarily unavailable
- Verify error handling doesn't crash the app

## 📊 **Performance Verification**

### 1. Database Size Check
```sql
-- Check table size before and after cleanup
SELECT 
  schemaname,
  tablename,
  attname,
  n_distinct,
  correlation
FROM pg_stats 
WHERE tablename = 'events';

-- Check row count
SELECT COUNT(*) FROM public.events;
```

### 2. Query Performance
```sql
-- Test index usage
EXPLAIN ANALYZE SELECT * FROM public.events 
WHERE user_id = 'test-user-id' 
ORDER BY timestamp DESC;
```

## ✅ **Success Criteria**

### Database Level:
- [ ] Events table has proper constraints
- [ ] Only login events exist in the table
- [ ] Indexes are created and functional
- [ ] RLS policies are active and working
- [ ] Login statistics function works

### Application Level:
- [ ] Login events are logged successfully
- [ ] Page navigation doesn't create events
- [ ] No TypeScript errors
- [ ] No runtime errors in console
- [ ] Authentication flow works normally

### Performance:
- [ ] Database size reduced from cleanup
- [ ] Query performance improved
- [ ] No noticeable app slowdown

## 🔧 **Troubleshooting Common Issues**

### Issue: Migration Fails
**Solution:**
- Check if events table exists
- Verify user permissions
- Run migration step by step

### Issue: Login Events Not Logged
**Solution:**
- Check browser console for errors
- Verify user authentication status
- Check Supabase connection

### Issue: TypeScript Errors
**Solution:**
- Ensure AuthContextType interface is updated
- Verify function name changes are consistent
- Check import statements

### Issue: RLS Blocks Legitimate Access
**Solution:**
- Verify user authentication
- Check RLS policy conditions
- Test with different user accounts

## 📋 **Rollback Testing**

If issues are found, test the rollback procedure:

1. **Run rollback migration:**
```sql
-- File: supabase/migrations/20250705000001_rollback_events_table_refactor.sql
```

2. **Revert code changes:**
- Restore original AuthContext.tsx
- Restore original AppRouter.tsx

3. **Verify rollback success:**
- Events table accepts any event type
- Page navigation logging works
- No application errors

## 📞 **Support & Escalation**

If critical issues arise:
1. **Immediate**: Apply rollback migration
2. **Document**: Record the specific error/issue
3. **Investigate**: Check logs and error messages
4. **Fix**: Address root cause before re-attempting

## 📅 **Testing Timeline**

- **Pre-deployment**: 30 minutes
- **Post-deployment verification**: 15 minutes  
- **Performance monitoring**: 24 hours
- **Full validation**: 1 week

This comprehensive testing ensures the events table refactoring is successful and doesn't impact user experience.
