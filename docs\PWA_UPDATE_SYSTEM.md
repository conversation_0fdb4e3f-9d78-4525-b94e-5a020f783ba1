# PWA Update System Documentation

## Overview

DataStatPro now includes a comprehensive Progressive Web App (PWA) update system that ensures users always have access to the latest version of the application. The system provides automatic update detection, manual update controls, and graceful update handling.

## Features

### 1. Automatic Update Detection
- **Periodic Checks**: Automatically checks for updates every 30 minutes
- **Smart Detection**: Uses service worker registration to detect new versions
- **Background Processing**: Update checks happen in the background without disrupting user workflow

### 2. Manual Update Controls
- **Update Button**: Available in both app headers (logged-in and guest users)
- **Check for Updates**: Users can manually trigger update checks
- **Force Refresh**: Option to force a complete app refresh

### 3. Enhanced Notifications
- **Rich Notifications**: Detailed update notifications with progress indicators
- **User Choice**: Users can choose when to install updates
- **Error Handling**: Clear error messages and retry mechanisms

### 4. Version Management
- **Version Display**: Shows current app version and build information
- **Update History**: Tracks last update check times
- **Build Information**: Displays build date and version details

## Components

### Core Hook: `usePWAUpdate`
Located in `src/hooks/usePWAUpdate.ts`

**Key Features:**
- Manages PWA update state and actions
- Handles service worker registration and updates
- Provides version information and update preferences
- Implements retry logic and error handling

**Usage:**
```typescript
import { usePWAUpdate } from '../hooks/usePWAUpdate';

const {
  needRefresh,
  isUpdating,
  updateServiceWorker,
  checkForUpdates,
  currentVersion
} = usePWAUpdate();
```

### UI Components

#### 1. UpdateNotification (`src/components/PWA/UpdateNotification.tsx`)
- Displays update availability notifications
- Shows progress during updates
- Handles user interactions (update/dismiss)
- Provides detailed update information

#### 2. UpdateButton (`src/components/PWA/UpdateButton.tsx`)
- Manual update trigger button
- Available in three variants: button, icon, menu
- Shows update status and progress
- Includes settings for auto-updates

#### 3. VersionInfo (`src/components/PWA/VersionInfo.tsx`)
- Displays current version information
- Shows last update check time
- Available in compact and detailed views
- Includes offline status indicator

#### 4. UpdateSettings (`src/components/PWA/UpdateSettings.tsx`)
- Comprehensive update management interface
- Auto-update toggle
- Cache management options
- Advanced troubleshooting tools

## Configuration

### Vite PWA Configuration (`vite.config.ts`)
```typescript
VitePWA({
  registerType: 'prompt', // User-controlled updates
  strategies: 'generateSW',
  workbox: {
    skipWaiting: false, // Allow user control
    clientsClaim: false, // Better update control
    runtimeCaching: [...] // Enhanced caching strategies
  }
})
```

### Environment Variables
- `__APP_VERSION__`: Injected app version from package.json
- `__BUILD_DATE__`: Build timestamp for version tracking

## User Experience Flow

### 1. Initial Load
1. Service worker registers automatically
2. App caches resources for offline use
3. Version information is displayed in UI

### 2. Update Detection
1. Periodic background checks (every 30 minutes)
2. Manual checks via update button
3. New version detected → notification appears

### 3. Update Installation
1. User sees update notification
2. User can choose to:
   - Install immediately
   - Dismiss and install later
   - View update details
3. Update installs with progress feedback
4. App refreshes with new version

### 4. Error Handling
1. Update failures show clear error messages
2. Retry mechanisms available
3. Force refresh option as fallback
4. Cache clearing for troubleshooting

## Storage and Persistence

### LocalStorage Keys
- `pwa-auto-update-enabled`: Auto-update preference
- `pwa-last-update-check`: Timestamp of last check
- `pwa-update-dismissed`: Update notification dismissal state
- `pwa-current-version`: Currently installed version

### Cache Management
- Automatic cleanup of outdated caches
- Manual cache clearing option
- Smart cache invalidation on updates

## Testing

### Test File: `src/tests/pwa-update-test.html`
Comprehensive test page for verifying PWA update functionality:
- Service worker status checking
- Update detection testing
- Cache management verification
- Version information display

### Testing Scenarios
1. **Fresh Install**: Verify service worker registration
2. **Update Detection**: Test automatic and manual update checks
3. **Update Installation**: Verify smooth update process
4. **Error Handling**: Test failure scenarios and recovery
5. **Offline Functionality**: Verify offline capabilities

## Deployment Considerations

### Production Deployment
1. Ensure proper HTTPS setup (required for service workers)
2. Configure proper cache headers for static assets
3. Test update mechanism in production environment
4. Monitor update success rates and user feedback

### Version Management
1. Update version in `package.json` for each release
2. Build process automatically injects version information
3. Service worker detects version changes automatically

## Troubleshooting

### Common Issues

#### 1. Updates Not Detected
- Check service worker registration
- Verify network connectivity
- Clear browser cache manually
- Check for console errors

#### 2. Update Installation Fails
- Use force refresh option
- Clear all caches via settings
- Check for JavaScript errors
- Verify service worker is active

#### 3. Version Information Incorrect
- Verify build process includes version injection
- Check environment variable configuration
- Ensure proper cache invalidation

### Debug Tools
- Browser DevTools → Application → Service Workers
- Network tab for update request monitoring
- Console for error messages and logs
- PWA test page for comprehensive diagnostics

## Best Practices

### For Users
1. Keep auto-updates enabled for security
2. Install updates promptly when notified
3. Use manual check if experiencing issues
4. Clear cache if app behaves unexpectedly

### For Developers
1. Test update mechanism thoroughly before deployment
2. Monitor update success rates in production
3. Provide clear user communication about updates
4. Implement proper error handling and recovery

## Future Enhancements

### Planned Features
1. **Update Scheduling**: Allow users to schedule update times
2. **Selective Updates**: Choose which features to update
3. **Update Rollback**: Ability to revert to previous version
4. **Update Analytics**: Track update success and user behavior
5. **Progressive Updates**: Staged rollout of updates

### Technical Improvements
1. **Differential Updates**: Only download changed files
2. **Background Sync**: Better offline update handling
3. **Update Compression**: Reduce update download sizes
4. **Smart Caching**: More intelligent cache management
