# DataStatPro - Quick Start Guide

This guide will help you get started with DataStatPro quickly and begin analyzing your data in minutes.

## Installation

### Option 1: Use the Online Version

Visit [https://datastatpro.com](https://datastatpro.com) to use DataStatpro without installation.

### Option 2: Run Locally

1. Clone the repository
   ```bash
   git clone https://github.com/yourusername/DataStatPro.git
   cd DataStatPro
   ```

2. Install dependencies
   ```bash
   npm install
   ```

3. Start the development server
   ```bash
   npm run dev -- --port 8001 --host 0.0.0.0
   ```

4. Open your browser and navigate to `http://localhost:8001`

## Getting Started in 5 Minutes

### Step 1: Import Data

1. Click on "Data Management" in the sidebar
2. Select "Import Data" tab
3. Choose one of the following options:
   - Upload a CSV file by clicking "Select CSV File"
   - Generate sample data by clicking "Generate Basic Sample" or one of the specialized datasets

![Import Data](assets/screenshots/import-data.png)

### Step 2: Explore Your Data

1. Go to "Descriptive Statistics" in the sidebar
2. Select "Descriptives" tab
3. Choose your dataset and variables
4. View summary statistics including mean, median, standard deviation, etc.

### Step 3: Visualize Your Data

1. Go to "Data Visualization" in the sidebar
2. Choose a chart type (Bar Chart, Histogram, Scatter Plot, etc.)
3. Select variables to display
4. Customize your chart using the options panel

![Visualization](assets/screenshots/visualization.png)

### Step 4: Perform Statistical Analysis

1. Go to the appropriate section based on your analysis needs:
   - "Descriptive Statistics" for summaries and distributions
   - "Inferential Statistics" for hypothesis tests
   - "Correlation Analysis" for relationships between variables

2. Select the specific test or analysis you want to perform
3. Configure the test parameters
4. Interpret the results using the provided information

## Sample Workflows

### Analyzing Group Differences

1. Import your data
2. Go to "Inferential Statistics" → "t-Tests"
3. Select "Independent Samples t-Test"
4. Choose your dependent variable (measurement)
5. Select your grouping variable
6. Run the test and interpret the results

### Examining Relationships Between Variables

1. Import your data
2. Go to "Correlation Analysis" → "Correlation Matrix"
3. Select multiple variables to analyze
4. View the correlation matrix and heatmap
5. For detailed analysis, go to "Linear Regression" and select specific variables

### Creating Publication-Ready Charts

1. Import your data
2. Go to "Data Visualization" and select your preferred chart type
3. Configure variables and appearance settings
4. Use the customization options to refine the chart
5. Export the chart using the download button

## Key Features by Section

### Data Management

- **Import Data**: Load CSV files or generate sample data
- **Data Editor**: View and modify your dataset in a spreadsheet interface
- **Transform Data**: Apply transformations like standardization, logarithm, etc.
- **Export Data**: Save your data in various formats

### Descriptive Statistics

- **Descriptives**: Calculate summary statistics
- **Frequencies**: Analyze categorical data
- **Cross Tabulation**: Examine relationships between categorical variables
- **Normality Test**: Check if your data follows a normal distribution

### Inferential Statistics

- **t-Tests**: Compare means (one-sample, independent, paired)
- **ANOVA**: Compare multiple groups
- **Non-parametric Tests**: For data that doesn't meet parametric assumptions
- **Assumption Checker**: Verify if your data meets test requirements

### Correlation Analysis

- **Correlation Matrix**: See relationships between multiple variables
- **Linear Regression**: Predict continuous outcomes
- **Logistic Regression**: Predict binary outcomes

### Data Visualization

- **Bar Chart**: For categorical data
- **Pie Chart**: For proportions
- **Histogram**: For distributions
- **Box Plot**: For data spread and outliers
- **Scatter Plot**: For relationships between variables

## Keyboard Shortcuts

- `Alt + D`: Go to Data Management
- `Alt + S`: Go to Descriptive Statistics
- `Alt + I`: Go to Inferential Statistics
- `Alt + C`: Go to Correlation Analysis
- `Alt + V`: Go to Data Visualization
- `Alt + H`: Go to Home

## Need More Help?

- Check the comprehensive [User Guide](USER_GUIDE.md)
- Visit our [website](https://datastatpro.com) for tutorials
- Join our community forum at [community.datastatpro.com](https://community.datastatpro.com)
- Contact <NAME_EMAIL>