# Revised Stripe Payment Integration Plan for DataStatPro

## Overview
This plan integrates Stripe payment processing with the existing pricing tier structure, including automatic educational account detection and annual billing discounts.

## 1. Database Schema Updates ✅

### Key Changes:
- **Fixed AccountType Integration**: Updated `handle_new_user()` trigger to properly set `accounttype` field
- **Educational Email Detection**: Automatic detection of .edu and other educational domains
- **Subscription Tracking**: New `subscriptions` table for Stripe integration
- **Account Type Sync**: Automatic sync between Stripe subscription status and `accounttype`

### Migration File: `20250702000000_add_stripe_integration_support.sql`
- Ensures `accounttype` field exists with proper constraints
- Adds `stripe_customer_id` to profiles table
- Creates `subscriptions` table with RLS policies
- Implements educational email domain detection
- Auto-syncs account types based on subscription status

## 2. Stripe Product Configuration

### Products & Prices to Create:

#### Pro Account
- **Monthly**: $10.00/month (`price_pro_monthly`)
- **Annual**: $96.00/year ($8.00/month, 20% discount) (`price_pro_annual`)

#### Educational Account  
- **Monthly**: $5.00/month (`price_edu_monthly`)
- **Annual**: $48.00/year ($4.00/month, 20% discount) (`price_edu_annual`)

### Stripe Setup Commands:
```bash
# Create Pro products
stripe products create --name "DataStatPro Pro Account" --description "Professional statistical analysis with cloud features"

# Create Educational products  
stripe products create --name "DataStatPro Educational Account" --description "Educational discount for .edu email addresses"

# Create prices (replace PRODUCT_ID with actual IDs)
stripe prices create --product PRODUCT_ID --unit-amount 1000 --currency usd --recurring interval=month --lookup-key price_pro_monthly
stripe prices create --product PRODUCT_ID --unit-amount 9600 --currency usd --recurring interval=year --lookup-key price_pro_annual
stripe prices create --product PRODUCT_ID --unit-amount 500 --currency usd --recurring interval=month --lookup-key price_edu_monthly  
stripe prices create --product PRODUCT_ID --unit-amount 4800 --currency usd --recurring interval=year --lookup-key price_edu_annual
```

## 3. Environment Variables

```env
# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Price IDs (from Stripe dashboard)
STRIPE_PRO_MONTHLY_PRICE_ID=price_...
STRIPE_PRO_ANNUAL_PRICE_ID=price_...
STRIPE_EDU_MONTHLY_PRICE_ID=price_...
STRIPE_EDU_ANNUAL_PRICE_ID=price_...
```

## 4. Frontend Components Updates

### Updated PricingPage.tsx Structure:
```typescript
const pricingTiers = [
  {
    id: 'guest',
    name: 'Guest Access',
    price: 'Free',
    // ... existing config
  },
  {
    id: 'standard', 
    name: 'Standard Account',
    price: 'Free',
    // ... existing config
  },
  {
    id: 'pro',
    name: 'Pro Account',
    monthlyPrice: '$10',
    annualPrice: '$96',
    annualSavings: '20%',
    billingOptions: ['monthly', 'annual'],
    // ... updated config
  },
  {
    id: 'edu',
    name: 'Educational Account', 
    monthlyPrice: '$5',
    annualPrice: '$48',
    annualSavings: '20%',
    billingOptions: ['monthly', 'annual'],
    emailRequirement: 'Educational email required',
    // ... updated config
  }
];
```

### New Components Needed:
1. **BillingToggle.tsx** - Monthly/Annual toggle switch
2. **StripeCheckoutButton.tsx** - Handles subscription creation
3. **SubscriptionManager.tsx** - Manage active subscriptions
4. **BillingHistory.tsx** - View past invoices
5. **EducationalVerification.tsx** - Verify .edu email eligibility

## 5. Supabase Edge Functions

### Required Functions:

#### 1. create-checkout-session
```typescript
// Handle subscription creation with educational validation
export default async function handler(req: Request) {
  const { priceId, userEmail, billingCycle } = await req.json();
  
  // Validate educational eligibility for edu prices
  if (priceId.includes('edu') && !isEducationalEmail(userEmail)) {
    return new Response('Educational email required', { status: 400 });
  }
  
  // Create Stripe checkout session
  const session = await stripe.checkout.sessions.create({
    // ... configuration
  });
  
  return new Response(JSON.stringify({ url: session.url }));
}
```

#### 2. stripe-webhooks
```typescript
// Handle subscription lifecycle events
export default async function handler(req: Request) {
  const sig = req.headers.get('stripe-signature');
  const event = stripe.webhooks.constructEvent(body, sig, webhookSecret);
  
  switch (event.type) {
    case 'customer.subscription.created':
    case 'customer.subscription.updated':
      await syncSubscriptionStatus(event.data.object);
      break;
    case 'customer.subscription.deleted':
      await handleSubscriptionCancellation(event.data.object);
      break;
  }
}
```

#### 3. create-customer-portal
```typescript
// Generate Stripe Customer Portal links
export default async function handler(req: Request) {
  const { customerId } = await req.json();
  
  const session = await stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: `${process.env.SITE_URL}/app#/settings/billing`,
  });
  
  return new Response(JSON.stringify({ url: session.url }));
}
```

## 6. Authentication Logic Updates

### AuthContext Modifications:
```typescript
// Add subscription status tracking
const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual' | null>(null);

// Enhanced feature access logic
const canAccessProFeatures = useMemo(() => {
  // Guest users can access Pro features with sample data
  if (isGuest) return true;
  
  // Authenticated users need active subscription or edu account
  if (user && accountType) {
    return (accountType === 'pro' || accountType === 'edu') && 
           (subscriptionStatus === 'active' || subscriptionStatus === null);
  }
  
  return false;
}, [isGuest, user, accountType, subscriptionStatus]);
```

## 7. Educational Account Validation

### Automatic Detection:
- Implemented in database trigger via `is_educational_email()` function
- Supports common patterns: `.edu`, `.ac.edu`, `.edu.au`, `.ac.uk`, etc.
- Users with educational emails automatically get `accounttype = 'edu'`

### Frontend Validation:
```typescript
const isEducationalEmail = (email: string): boolean => {
  const eduPatterns = [
    /\.edu$/i,
    /\.ac\.edu$/i, 
    /\.edu\.au$/i,
    /\.ac\.uk$/i,
    /\.edu\.[a-z]{2,}$/i,
    /\.ac\.[a-z]{2,}$/i
  ];
  
  return eduPatterns.some(pattern => pattern.test(email));
};
```

## 8. Pricing Display Updates

### Annual Discount Highlighting:
```typescript
const PricingCard = ({ tier }) => (
  <Card>
    <CardContent>
      <Typography variant="h6">{tier.name}</Typography>
      
      {tier.billingOptions && (
        <Box sx={{ mb: 2 }}>
          <BillingToggle 
            value={billingCycle}
            onChange={setBillingCycle}
          />
          
          <Typography variant="h4" color="primary">
            {billingCycle === 'annual' ? tier.annualPrice : tier.monthlyPrice}
          </Typography>
          
          {billingCycle === 'annual' && (
            <Chip 
              label={`Save ${tier.annualSavings}`}
              color="success"
              size="small"
            />
          )}
        </Box>
      )}
    </CardContent>
  </Card>
);
```

## 9. Testing Strategy

### Stripe Test Mode:
- Use test price IDs for development
- Test cards for different scenarios:
  - `****************` - Successful payment
  - `****************` - Card declined
  - `****************` - Insufficient funds

### Test Scenarios:
1. **Educational Email Detection**: Test various .edu domain formats
2. **Subscription Creation**: Both monthly and annual billing
3. **Subscription Updates**: Upgrade/downgrade between plans
4. **Payment Failures**: Handle failed payments gracefully
5. **Cancellation Flow**: Proper account type reversion
6. **Webhook Delivery**: Ensure reliable event processing

## 10. Implementation Phases

### Phase 1: Database & Backend (2-3 days)
- Run migration for subscription support
- Create Supabase Edge Functions
- Set up Stripe products and webhooks

### Phase 2: Frontend Components (3-4 days)  
- Update PricingPage with billing toggles
- Create subscription management components
- Implement educational email validation

### Phase 3: Integration & Testing (2-3 days)
- Connect frontend to Stripe Checkout
- Test complete subscription lifecycle
- Implement error handling and edge cases

### Phase 4: Production Deployment (1-2 days)
- Switch to live Stripe keys
- Configure production webhooks
- Monitor initial transactions

## Next Steps

1. **Run the database migration** to add subscription support
2. **Configure Stripe products** with the specified pricing structure  
3. **Set up webhook endpoints** in Supabase Edge Functions
4. **Update PricingPage** to show monthly/annual options
5. **Test educational email detection** with various domain formats

This revised plan maintains your existing user experience while adding robust payment processing with automatic educational account detection and annual billing discounts.
