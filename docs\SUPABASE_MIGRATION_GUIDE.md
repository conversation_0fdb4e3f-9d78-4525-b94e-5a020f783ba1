# Supabase Migration Guide: Updating Profiles Table

This guide explains how to update your Supabase database schema to align with the changes made to the registration form in the DataStatPro application.

## Overview of Changes

The registration form in `Register.tsx` has been updated to remove the gender field and properly implement the country dropdown selection. To ensure the database schema matches these changes, we need to:

1. Remove the `gender` field from the `profiles` table
2. Ensure the `country` field is properly configured to store values from the country dropdown
3. Update the database trigger that creates user profiles during registration

## Step-by-Step Migration Guide

### 1. Access Your Supabase Project

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Select your DataStatPro project
3. Navigate to the SQL Editor

### 2. Apply the Migration Script

Copy and paste the following SQL script into the SQL Editor and run it:

```sql
-- Migration to update the profiles table structure
-- Remove gender field and ensure country field is properly configured

-- First, check if the gender column exists and remove it if it does
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns 
             WHERE table_schema = 'public' 
             AND table_name = 'profiles' 
             AND column_name = 'gender') THEN
    ALTER TABLE public.profiles DROP COLUMN gender;
  END IF;
END $$;

-- Next, check if the country column exists, if not add it
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_schema = 'public' 
                 AND table_name = 'profiles' 
                 AND column_name = 'country') THEN
    ALTER TABLE public.profiles ADD COLUMN country text;
  END IF;
END $$;

-- Update the handle_new_user function to match the new structure
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    username, 
    full_name, 
    institution, 
    country, 
    avatar_url, 
    updated_at
  ) VALUES (
    new.id, 
    new.email, -- Default username to email
    new.raw_user_meta_data->>'full_name', 
    new.raw_user_meta_data->>'institution', 
    new.raw_user_meta_data->>'country',
    null, -- Default avatar_url to null
    now()
  );
  RETURN new;
END;
$$;

-- Ensure the trigger exists
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'on_auth_user_created') THEN
    CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
  END IF;
END $$;

-- Add comment to explain the migration
COMMENT ON TABLE public.profiles IS 'Stores user profile information with updated fields (removed gender, ensured country field)';
```

### 3. Verify the Changes

1. Navigate to the **Table Editor** in your Supabase dashboard
2. Select the `profiles` table in the `public` schema
3. Verify that:
   - The `gender` column has been removed
   - The `country` column exists

### 4. Test User Registration

To ensure the changes are working correctly:

1. Create a test user account through your application
2. Check the `profiles` table to verify that a new profile was created with the correct data
3. Confirm that the country value from the dropdown is properly stored in the database

## How These Changes Align with the Registration Form

The registration form in `Register.tsx` collects the following information:

```typescript
const [email, setEmail] = useState('');
const [password, setPassword] = useState('');
const [confirmPassword, setConfirmPassword] = useState('');
const [fullName, setFullName] = useState('');
const [institution, setInstitution] = useState('');
const [country, setCountry] = useState('');
```

This information is passed to the Supabase `signUp` function as metadata:

```typescript
const metadata = {
  full_name: fullName,
  institution,
  country,
};

const { error, user } = await signUp(email, password, { data: metadata });
```

The database trigger then extracts this metadata and stores it in the `profiles` table, ensuring that user profile information is properly captured during registration.

## Troubleshooting

If you encounter any issues during the migration:

1. Check the SQL Editor for error messages
2. Verify that the `profiles` table exists and has the expected structure
3. Ensure that the `handle_new_user` function and `on_auth_user_created` trigger are properly created
4. Test the registration process to confirm that profile data is being correctly stored

If problems persist, you may need to check the Supabase logs for more detailed error information.