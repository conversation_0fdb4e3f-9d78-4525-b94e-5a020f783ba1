# User Dataset Storage

This feature allows registered users to store unlimited local datasets and move up to two datasets to their cloud account in JSON format, retaining all data editing information like Variable Type, Variable Role, Description, etc.

## Features

- Store unlimited local datasets in browser storage
- Move up to two datasets to cloud storage with a 2MB size limit per dataset
- Datasets retain all metadata including variable types, roles, and descriptions
- Intuitive UI with separate tabs for local and cloud datasets
- Visual storage usage indicators
- Secure storage using Supabase backend

## Implementation Details

### Database Schema

Datasets are stored in the `user_datasets` table with the following schema:

```sql
CREATE TABLE user_datasets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  dataset_name VARCHAR(255) NOT NULL,
  dataset_content JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,

  CONSTRAINT unique_user_dataset_name UNIQUE (user_id, dataset_name)
);
```

### Size Limit

The maximum size for each dataset is 2MB. This is enforced at the application level before saving to the database.

### Dataset Count Limit

Users are limited to 2 saved datasets. This is enforced at the application level before saving to the database.

### User Interface

The dataset management UI provides the following features:

1. **Tabbed Interface** - Clearly separates "Local Datasets" (first tab) and "My Cloud Datasets" (second tab)
2. **Storage Usage Indicator** - Shows how much of your cloud storage quota is being used
3. **Dataset Count Indicator** - Shows how many datasets you have in cloud storage (out of 2)
4. **Cloud/Local Labels** - Each dataset is clearly labeled as cloud or local
5. **Move to Cloud** button - For each local dataset to easily move it to your cloud account
6. **Delete** buttons - Separate buttons for deleting from cloud or local storage
7. **Informational Cards** - Helpful information about cloud and local storage

### How It Works

1. **Local Datasets**:
   - Stored in your browser's local storage
   - Available only on the current device
   - No limit to how many local datasets you can create or import
   - Will be lost if you clear your browser data

2. **Cloud Datasets**:
   - Stored in your user account on the server
   - Available from any device when you log in
   - Limited to 2 datasets with a maximum size of 2MB each
   - Persist even if you clear your browser data

3. **Moving Datasets to Cloud**:
   - When you move a dataset to cloud storage, it is removed from local storage
   - The dataset will then appear in the "My Cloud Datasets" tab
   - You cannot have the same dataset in both local and cloud storage

### Storage Optimization

The feature is optimized for both local browser storage and Supabase's cloud storage limitations:

- **Local Storage**: Uses browser's localStorage for unlimited local datasets (subject to browser limits)
- **Cloud Storage**: Datasets are stored in the database as JSONB, which is efficient for small to medium datasets
- **Size Limit**: Each cloud dataset is limited to 2MB to prevent exceeding Supabase's 500MB database limit
- **Storage Monitoring**: Visual indicators show cloud storage usage to help users manage their data

### Security

- Row-level security (RLS) ensures users can only access their own datasets
- Authentication is required for all dataset operations
- Datasets are automatically loaded when a user logs in

### Troubleshooting

If you encounter errors like "new row violates row-level security policy" when saving datasets or "403 Unauthorized" when uploading dataset files, you need to run the SQL migration to fix the RLS policies. This can be done in the Supabase SQL Editor:

```sql
-- Fix RLS policies for the user_datasets table
DROP POLICY IF EXISTS "Authenticated users can manage their own datasets" ON user_datasets;
DROP POLICY IF EXISTS "Users can insert their own datasets" ON user_datasets;
DROP POLICY IF EXISTS "Users can view and modify their own datasets" ON user_datasets;

-- Create a more permissive policy for INSERT
CREATE POLICY "Users can insert their own datasets"
ON user_datasets FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Create a policy for SELECT, UPDATE, DELETE
CREATE POLICY "Users can view and modify their own datasets"
ON user_datasets FOR ALL
TO authenticated
USING (auth.uid() = user_id);

-- Enable RLS on the table (in case it was disabled)
ALTER TABLE user_datasets ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions to authenticated users
GRANT ALL ON user_datasets TO authenticated;

-- Fix RLS policies for the userdatasets storage bucket
-- Note: We don't need to explicitly enable RLS on the bucket
-- Instead, we create policies on storage.objects for the specific bucket

-- Create policy for uploading datasets
CREATE POLICY "Users can upload their own datasets"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'userdatasets' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy for viewing datasets
CREATE POLICY "Users can view their own datasets"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'userdatasets' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy for updating datasets
CREATE POLICY "Users can update their own datasets"
ON storage.objects FOR UPDATE
TO authenticated
USING (
  bucket_id = 'userdatasets' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy for deleting datasets
CREATE POLICY "Users can delete their own datasets"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'userdatasets' AND
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Grant necessary permissions to authenticated users for the userdatasets bucket
GRANT ALL ON storage.objects TO authenticated;
```

This SQL script is available in the `supabase/migrations/20250621160000_fix_user_datasets_and_storage_rls.sql` file.

### Best Practices for Dataset Storage

1. **Use Local Storage for Work in Progress**: Keep datasets you're actively working on in local storage
2. **Move to Cloud for Important Data**: Only move finalized, important datasets to cloud storage
3. **Clean Your Data**: Remove unnecessary columns and rows before moving to cloud to reduce size
4. **Monitor Usage**: Keep an eye on the storage usage indicator to avoid hitting cloud storage limits
5. **Regular Cleanup**: Delete unused datasets from your cloud account to free up space
6. **Export Backups**: For important datasets, consider exporting them as CSV/Excel files as additional backups

## Usage

1. **Saving a Dataset**:
   - Select a dataset from the list or create/import a new one
   - Click the "Save to Account" button in the Datasets tab

2. **Loading Saved Datasets**:
   - Datasets saved to your account are automatically loaded when you log in
   - Datasets saved to your account are marked with a cloud icon

3. **Deleting Saved Datasets**:
   - Click the cloud icon with an X next to a saved dataset to delete it from your account