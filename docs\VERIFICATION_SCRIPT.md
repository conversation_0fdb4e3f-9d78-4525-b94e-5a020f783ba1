# Educational Tier Implementation - Verification Script

## 🔍 **Step-by-Step Verification Guide**

### **Phase 1: Database Verification**

#### **1.1 Check Schema Changes**
```sql
-- Verify edu_subscription_type column exists
SELECT 
  column_name,
  data_type,
  column_default,
  is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public'
  AND table_name = 'profiles' 
  AND column_name = 'edu_subscription_type';

-- Expected: Should return one row showing the column exists
```

#### **1.2 Check Account Type Constraints**
```sql
-- Verify updated accounttype constraint
SELECT 
  tc.constraint_name,
  cc.check_clause
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc 
  ON tc.constraint_name = cc.constraint_name
WHERE tc.table_schema = 'public'
  AND tc.table_name = 'profiles'
  AND tc.constraint_name = 'profiles_accounttype_check';

-- Expected: Should show constraint allowing 'standard', 'pro', 'edu', 'edu_pro'
```

#### **1.3 Check Educational User Initialization**
```sql
-- Check existing educational users
SELECT 
  accounttype,
  edu_subscription_type,
  COUNT(*) as user_count
FROM public.profiles 
WHERE accounttype IN ('edu', 'edu_pro')
GROUP BY accounttype, edu_subscription_type;

-- Expected: All 'edu' users should have edu_subscription_type = 'free'
```

#### **1.4 Test Educational Email Function**
```sql
-- Test educational email detection
SELECT 
  '<EMAIL>' as test_email,
  public.is_educational_email('<EMAIL>') as should_be_true
UNION ALL
SELECT 
  '<EMAIL>' as test_email,
  public.is_educational_email('<EMAIL>') as should_be_false;

-- Expected: First should return true, second should return false
```

### **Phase 2: Frontend Verification**

#### **2.1 Access Test Interface**
1. **Navigate to Test Page**:
   - Go to `/app#/edu-tier-test`
   - Should only be available in development environment

2. **Verify Test Page Loads**:
   - Page should display account information
   - Feature access permissions should be shown
   - Feature gate tests should be visible

#### **2.2 Test Different Account Types**

##### **Guest User (Not Logged In)**
- ✅ `canAccessAdvancedAnalysis`: true (sample data)
- ✅ `canAccessPublicationReady`: true (sample data)
- ❌ `canAccessCloudStorage`: false
- ✅ `isEducationalUser`: false

##### **Standard User**
- ❌ `canAccessAdvancedAnalysis`: false
- ❌ `canAccessPublicationReady`: false
- ❌ `canAccessCloudStorage`: false
- ❌ `isEducationalUser`: false

##### **Educational Free User (accounttype = 'edu')**
- ✅ `canAccessAdvancedAnalysis`: true
- ❌ `canAccessPublicationReady`: false
- ❌ `canAccessCloudStorage`: false
- ✅ `isEducationalUser`: true
- ✅ `educationalTier`: 'free'

##### **Educational Pro User (accounttype = 'edu_pro')**
- ✅ `canAccessAdvancedAnalysis`: true
- ✅ `canAccessPublicationReady`: true
- ✅ `canAccessCloudStorage`: true
- ✅ `isEducationalUser`: true
- ✅ `educationalTier`: 'pro'

##### **Regular Pro User (accounttype = 'pro')**
- ✅ `canAccessAdvancedAnalysis`: true
- ✅ `canAccessPublicationReady`: true
- ✅ `canAccessCloudStorage`: true
- ❌ `isEducationalUser`: false

### **Phase 3: Component Integration Testing**

#### **3.1 Feature Gate Components**
1. **Test AdvancedAnalysisGate**:
   - Educational users should see content
   - Standard users should see upgrade prompt
   - Guest users should see content (sample data)

2. **Test PublicationReadyGate**:
   - Educational Pro users should see content
   - Educational Free users should see upgrade prompt
   - Standard users should see upgrade prompt
   - Guest users should see content (sample data)

#### **3.2 Account Status Display**
1. **Check AccountStatusCard**:
   - Educational users should see tier information
   - Upgrade options should be appropriate for user type
   - Feature access should be clearly displayed

2. **Check Pricing Page**:
   - Educational tier should show "Advanced Analysis Free"
   - Upgrade path should show $10/month (no discount)
   - Feature breakdown should be accurate

### **Phase 4: User Flow Testing**

#### **4.1 New Educational User Registration**
1. **Register with .edu email**:
   - Should automatically get `accounttype = 'edu'`
   - Should get `edu_subscription_type = 'free'`
   - Should have Advanced Analysis access immediately

2. **Verify Feature Access**:
   - Can access Advanced Analysis features
   - Cannot access Publication Ready features
   - Sees appropriate upgrade prompts

#### **4.2 Educational User Upgrade Flow**
1. **From Educational Free to Educational Pro**:
   - Should see $10/month upgrade option
   - Should maintain educational account status
   - Should unlock Publication Ready features

2. **Subscription Cancellation**:
   - Should revert to Educational Free
   - Should maintain Advanced Analysis access
   - Should lose Publication Ready access

### **Phase 5: Backward Compatibility Testing**

#### **5.1 Existing User Impact**
1. **Existing .edu users**:
   - Should maintain current access during transition
   - Should be initialized with `edu_subscription_type = 'free'`
   - Should see new educational tier information

2. **Existing Pro users**:
   - Should maintain all current access
   - Should see no changes in functionality
   - Should continue to work with existing subscriptions

#### **5.2 Legacy Code Compatibility**
1. **canAccessProFeatures property**:
   - Should still work for backward compatibility
   - Should return true for users with both Advanced Analysis AND Publication Ready
   - Should maintain existing component functionality

### **Phase 6: Error Handling & Edge Cases**

#### **6.1 Database Edge Cases**
1. **Users with missing edu_subscription_type**:
   - Should handle gracefully
   - Should default to appropriate values

2. **Invalid account type combinations**:
   - Should be prevented by database constraints
   - Should handle gracefully in frontend

#### **6.2 Frontend Edge Cases**
1. **Network errors during profile fetch**:
   - Should handle gracefully
   - Should not break permission logic

2. **Subscription status changes**:
   - Should update permissions correctly
   - Should refresh user interface appropriately

## ✅ **Success Criteria**

### **Database Layer**
- [ ] All verification queries return expected results
- [ ] Educational email detection works correctly
- [ ] Account type constraints are properly enforced
- [ ] Existing users are properly initialized

### **Frontend Layer**
- [ ] All permission properties work correctly for each account type
- [ ] Feature gates display appropriate content/prompts
- [ ] Account status displays show correct information
- [ ] Upgrade flows work for educational users

### **User Experience**
- [ ] Educational users get clear value proposition
- [ ] Upgrade paths are intuitive and well-explained
- [ ] No disruption to existing user workflows
- [ ] Consistent messaging across all components

### **Integration**
- [ ] Stripe integration remains compatible
- [ ] Subscription lifecycle works correctly
- [ ] Database and frontend stay in sync
- [ ] Performance is not negatively impacted

## 🚨 **Common Issues & Solutions**

### **Issue: Educational users not getting Advanced Analysis access**
**Solution**: Check `canAccessAdvancedAnalysis` logic in AuthContext

### **Issue: Feature gates not displaying correctly**
**Solution**: Verify component imports and permission props

### **Issue: Database constraints failing**
**Solution**: Check account type values and constraint definitions

### **Issue: Existing users losing access**
**Solution**: Verify backward compatibility logic and migration scripts

---

**Use this verification script to systematically test the educational tier implementation before deploying to production.**
