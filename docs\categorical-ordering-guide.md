# Categorical Variable Ordering Guide

## Overview

DataStatPro implements a comprehensive categorical variable ordering system that ensures consistent category display across all analysis components. This guide provides developers with the standardized patterns and utilities to maintain this consistency.

## Core Principle

**All categorical variables should respect user-defined category ordering throughout the application.** This means that if a user defines a custom order like "High School → College → Graduate → PhD" in the Variable Editor, this same logical ordering must be maintained in:

- All statistical analysis results
- All visualization charts and legends  
- All regression coefficient tables
- All frequency tables and cross-tabulations
- Any other component that displays categorical data

## Centralized Utilities

### Primary Functions (from `utils/dataUtilities.ts`)

#### 1. `getOrderedCategories(column: Column, data: DataRow[]): string[]`
**Use Case**: When you have a Column object and dataset
```typescript
const orderedCategories = getOrderedCategories(educationColumn, dataset.data);
// Returns: ["High School", "College", "Graduate", "PhD"] (if custom order defined)
// Fallback: Alphabetical order if no custom order
```

#### 2. `getOrderedCategoriesByColumnId(columnId: string, dataset: Dataset): string[]`
**Use Case**: When you have a column ID (most common in components)
```typescript
const orderedCategories = getOrderedCategoriesByColumnId(selectedFactor, activeDataset);
// Automatically finds column and returns ordered categories
```

#### 3. `getOrderedCategoriesByColumnName(columnName: string, dataset: Dataset): string[]`
**Use Case**: When you have a column name
```typescript
const orderedCategories = getOrderedCategoriesByColumnName("Education", dataset);
```

#### 4. `getOrderedChartData(categoryColumnId, valueColumnId, dataset, aggregationMethod)`
**Use Case**: For visualizations (bar charts, pie charts)
```typescript
const chartData = getOrderedChartData(categoryVariable, null, activeDataset, 'count');
// Returns: [{ category: "High School", value: 25 }, { category: "College", value: 40 }, ...]
```

#### 5. `createOrderedCrossTabulation(rowColumnId, colColumnId, dataset)`
**Use Case**: For cross-tabulation analysis
```typescript
const { rowCategories, columnCategories, frequencies } = 
  createOrderedCrossTabulation(rowVariable, columnVariable, dataset);
```

## Implementation Patterns

### Pattern 1: Replace Manual Category Extraction

**❌ OLD WAY (Alphabetical):**
```typescript
const uniqueValues = [...new Set(data.map(row => String(row[column.name])))].sort();
```

**✅ NEW WAY (Ordered):**
```typescript
const orderedCategories = getOrderedCategoriesByColumnId(columnId, dataset);
```

### Pattern 2: Update Frequency Analysis

**❌ OLD WAY:**
```typescript
const frequencies = calculateFrequencies(values);
const categories = Object.keys(frequencies).sort();
```

**✅ NEW WAY:**
```typescript
const { categories, frequencies } = calculateOrderedFrequencies(columnId, dataset);
```

### Pattern 3: Update Visualization Data

**❌ OLD WAY:**
```typescript
const processedData = Object.entries(frequencies).map(([category, count]) => ({
  category, value: count
}));
```

**✅ NEW WAY:**
```typescript
const processedData = getOrderedChartData(categoryColumnId, null, dataset, 'count');
```

### Pattern 4: Update Regression Category Processing

**❌ OLD WAY:**
```typescript
const getUniqueCategories = (columnId: string): string[] => {
  // ... manual extraction and sorting
  return Array.from(uniqueValues).sort();
};
```

**✅ NEW WAY:**
```typescript
const getUniqueCategories = (columnId: string): string[] => {
  if (!currentDataset) return [];
  return getOrderedCategoriesByColumnId(columnId, currentDataset);
};
```

## Component Update Checklist

When creating or updating analysis components, ensure:

### ✅ Required Updates

1. **Import the utilities:**
   ```typescript
   import { getOrderedCategoriesByColumnId, getOrderedChartData } from '../../utils/dataUtilities';
   ```

2. **Replace manual category extraction** with ordered utilities

3. **Update frequency calculations** to use ordered functions

4. **Update visualization data preparation** to use `getOrderedChartData`

5. **Update cross-tabulation** to use `createOrderedCrossTabulation`

6. **Test with custom category ordering** to verify consistency

### ✅ Verification Steps

1. **Create a categorical variable** with custom ordering in Variable Editor
2. **Run your analysis component** with that variable
3. **Verify categories appear in the defined order** (not alphabetical)
4. **Check all outputs**: tables, charts, statistical results
5. **Ensure consistency** across different views/tabs in your component

## Examples by Component Type

### Visualization Components
```typescript
// Bar Chart / Pie Chart
const chartData = getOrderedChartData(categoryVariable, valueVariable, dataset, aggregationMethod);
```

### Statistical Analysis Components  
```typescript
// ANOVA, t-tests, etc.
const factorValues = getOrderedCategoriesByColumnId(selectedFactor, dataset);
```

### Regression Components
```typescript
// Linear/Logistic Regression
const getUniqueCategories = (columnId: string) => 
  getOrderedCategoriesByColumnId(columnId, currentDataset);
```

### Cross-tabulation Components
```typescript
// Cross-tabulation analysis
const { rowCategories, columnCategories } = 
  createOrderedCrossTabulation(rowVariable, columnVariable, dataset);
```

## Backward Compatibility

The system maintains full backward compatibility:

- **Existing variables** without custom ordering continue to use alphabetical sorting
- **No breaking changes** to existing functionality
- **Gradual adoption** - components can be updated incrementally
- **Fallback behavior** - always defaults to alphabetical if no custom order defined

## Testing Guidelines

### Manual Testing
1. Create categorical variable with logical order (e.g., "Poor → Fair → Good → Excellent")
2. Define custom category order in Variable Editor
3. Use variable in your analysis component
4. Verify order is maintained in all outputs

### Automated Testing
```typescript
// Test that ordered categories are respected
const testColumn = { 
  id: 'test', 
  name: 'Rating', 
  type: DataType.CATEGORICAL,
  categoryOrder: ['Poor', 'Fair', 'Good', 'Excellent']
};
const result = getOrderedCategories(testColumn, testData);
expect(result).toEqual(['Poor', 'Fair', 'Good', 'Excellent']);
```

## Common Pitfalls to Avoid

1. **Don't use `Object.keys().sort()`** for categorical data
2. **Don't manually extract unique values** without checking for custom ordering
3. **Don't assume alphabetical order** is always correct
4. **Always use the centralized utilities** for consistency
5. **Test with custom-ordered variables** not just default data

## Future Development

When creating new analysis components:

1. **Start with ordered utilities** from the beginning
2. **Follow the established patterns** in this guide
3. **Test with custom category ordering** during development
4. **Document any new categorical handling** patterns you create

This ensures that DataStatPro maintains consistent categorical variable ordering across all current and future analysis components.
