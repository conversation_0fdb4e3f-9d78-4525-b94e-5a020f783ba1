# 🔄 Routing System Migration Guide

## Quick Reference

### Before (Old System)
```typescript
// In App.tsx - 200+ line switch statement
switch (activePage) {
  case 'data-management':
    if (activeSubPage === '') {
      return <DataManagementPage onNavigate={navigateToPage} />;
    }
    return <DataManagement initialTab={activeSubPage} onNavigate={navigateToPage} />;
  case 'stats':
    // ... more cases
}
```

### After (New System)
```typescript
// In App.tsx - Clean router call
return (
  <AppRouter
    activePage={activePage}
    activeSubPage={activeSubPage}
    onNavigate={navigateToPage}
  />
);
```

## Adding New Routes

### 1. Choose the Right Route Module
- **Core features**: `src/routing/routes/coreRoutes.ts`
- **Data operations**: `src/routing/routes/dataManagementRoutes.ts`
- **Statistics**: `src/routing/routes/statisticsRoutes.ts`
- **Visualizations**: `src/routing/routes/visualizationRoutes.ts`
- **Correlations**: `src/routing/routes/correlationRoutes.ts`
- **Advanced tools**: `src/routing/routes/advancedRoutesSimple.ts`

### 2. Add Lazy Import
```typescript
const MyNewComponent = lazy(() => import('../../components/MyNewComponent'));
```

### 3. Add Route Configuration
```typescript
{
  path: 'my-new-feature',
  component: MyNewComponent,
  requiresAuth: false,    // Set based on requirements
  allowGuest: true,       // Allow guest users?
  allowPublic: false,     // Allow non-authenticated users?
  metadata: {
    title: 'My New Feature',
    description: 'What this feature does',
    category: 'analysis',  // For navigation grouping
    icon: 'FeatureIcon',   // Material-UI icon name
    order: 5               // Display order in navigation
  }
}
```

### 4. Add Sub-routes (Optional)
```typescript
{
  path: 'my-feature',
  component: MyFeaturePage,
  // ... other properties
  children: [
    {
      path: 'my-feature/sub-feature',
      component: MySubFeature,
      requiresAuth: false,
      allowGuest: true,
      props: { initialTab: 'sub-feature' },
      metadata: {
        title: 'Sub Feature',
        category: 'analysis'
      }
    }
  ]
}
```

## Route Security

### Authentication Levels
```typescript
// Public access (no login required)
requiresAuth: false,
allowGuest: false,
allowPublic: true

// Guest access (guest login required)
requiresAuth: false,
allowGuest: true,
allowPublic: false

// Full authentication required
requiresAuth: true,
allowGuest: false,
allowPublic: false
```

### Custom Route Guards
```typescript
// In RouteGuards.ts
export const myCustomGuard: RouteGuard = {
  name: 'myCustom',
  check: (route: RouteConfig, context: NavigationContext): RouteGuardResult => {
    if (/* your condition */) {
      return { allowed: true };
    }
    return {
      allowed: false,
      redirectTo: 'dashboard',
      reason: 'Custom access denied'
    };
  }
};

// Apply to routes
const guardResult = applyRouteGuards(route, context, [myCustomGuard, ...defaultGuards]);
```

## Navigation

### Basic Navigation (No Changes)
```typescript
// These work exactly the same as before
onNavigate('dashboard');
onNavigate('data-management');
onNavigate('data-management/import');
onNavigate('stats/frequency');
```

### Programmatic Navigation
```typescript
// In components
const { navigateToPage } = props;
navigateToPage('my-new-feature');

// With sub-pages
navigateToPage('my-feature/sub-feature');
```

## Route Metadata Usage

### Getting Route Information
```typescript
import { getRouteMetadata, getBreadcrumbs } from '../routing/routeConfig';

// Get metadata for current route
const metadata = getRouteMetadata('data-management', 'import');
console.log(metadata?.title); // "Import Data"

// Get breadcrumbs
const breadcrumbs = getBreadcrumbs('data-management', 'import');
// [{ title: "Data Management", path: "data-management", current: false },
//  { title: "Import Data", path: "data-management/import", current: true }]
```

### Navigation Menu Integration
```typescript
import { getNavigationRoutes, getRoutesByCategory } from '../routing/routeConfig';

// Get all navigation routes
const navRoutes = getNavigationRoutes();

// Get routes by category
const routesByCategory = getRoutesByCategory();
console.log(routesByCategory.analysis); // All analysis routes
```

## Testing Routes

### Router Test Page
Navigate to `#router-test` to see:
- Total routes registered
- Routes by category
- Quick navigation testing
- Route metadata validation

### Unit Testing Routes
```typescript
import { routeRegistry, findRoute } from '../routing/RouteRegistry';
import { applyRouteGuards } from '../routing/RouteGuards';

// Test route registration
const route = findRoute('my-feature');
expect(route).toBeDefined();
expect(route?.metadata?.title).toBe('My Feature');

// Test route guards
const context = {
  navigateToPage: jest.fn(),
  currentPage: 'my-feature',
  currentSubPage: '',
  isAuthenticated: true,
  isGuest: false,
  user: mockUser
};

const result = applyRouteGuards(route!, context);
expect(result.allowed).toBe(true);
```

## Troubleshooting

### Common Issues

1. **Route Not Found**
   - Check if route is registered in route module
   - Verify import path is correct
   - Ensure component exists

2. **Access Denied**
   - Check route security settings
   - Verify user authentication state
   - Review route guard logic

3. **Component Not Loading**
   - Check lazy import path
   - Verify component export
   - Look for console errors

### Debug Tools

```typescript
// Check registered routes
import { routeRegistry } from '../routing/RouteRegistry';
console.log('All routes:', routeRegistry.getAllRoutes());

// Check route matching
import { findRoute } from '../routing/RouteRegistry';
console.log('Found route:', findRoute('my-page', 'my-subpage'));
```

## Best Practices

1. **Route Organization**: Group related routes in the same module
2. **Naming**: Use kebab-case for route paths (`my-feature`, not `myFeature`)
3. **Metadata**: Always provide meaningful titles and descriptions
4. **Security**: Set appropriate authentication requirements
5. **Testing**: Test routes with different user states
6. **Documentation**: Update this guide when adding new patterns

## Migration Checklist

- [ ] Route added to appropriate module
- [ ] Lazy import configured
- [ ] Route metadata complete
- [ ] Security settings correct
- [ ] Navigation tested
- [ ] Error handling verified
- [ ] Documentation updated
