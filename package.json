{"name": "datastatpro", "private": true, "version": "1.0.0", "description": "A comprehensive statistical analysis web application with modern UI", "type": "module", "author": "DataStatPro Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/datastatpro/datastatpro"}, "keywords": ["statistics", "data-analysis", "visualization", "react", "typescript", "material-ui"], "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@stdlib/stats": "^0.3.3", "@stripe/stripe-js": "^7.4.0", "@supabase/supabase-js": "^2.49.4", "@tensorflow/tfjs": "^4.22.0", "@types/d3": "^7.4.3", "@types/file-saver": "^2.0.7", "@types/react-plotly.js": "^2.6.3", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.13", "d3": "^7.9.0", "date-fns": "^4.1.0", "dom-to-image": "^2.6.0", "file-saver": "^2.0.5", "globalthis": "^1.0.4", "html2canvas": "^1.4.1", "js.survival": "^1.0.0", "jstat": "^1.9.6", "mathjs": "^12.4.3", "mermaid": "^11.6.0", "ml-matrix": "^6.12.1", "ml-regression-simple-linear": "^3.0.1", "papaparse": "^5.5.2", "pivottable": "^2.23.0", "plotly.js": "^3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-markdown": "^10.1.0", "react-pivottable": "^0.11.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^7.6.1", "react-slick": "^0.30.3", "recharts": "^2.15.3", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "slick-carousel": "^1.8.1", "statsmodels-js": "^0.4.2", "stdlib": "^0.6.0", "unified": "^11.0.5", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.3", "@types/dom-to-image": "^2.6.7", "@types/papaparse": "^5.3.15", "@types/plotly.js": "^3.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-pivottable": "^0.11.6", "@types/uuid": "^9.0.8", "@vitejs/plugin-react": "^4.3.0", "jest": "^29.5.0", "sharp": "^0.34.2", "supabase": "^2.30.4", "terser": "^5.43.1", "typescript": "^5.0.2", "vite": "^5.4.19", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-pwa": "^0.21.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}