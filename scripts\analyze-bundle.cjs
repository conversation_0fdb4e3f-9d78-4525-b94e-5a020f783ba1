#!/usr/bin/env node

/**
 * Bundle analysis script for DataStatPro
 * Analyzes build output and provides performance recommendations
 * 
 * Usage: node scripts/analyze-bundle.cjs
 */

const fs = require('fs');
const path = require('path');

const DEPLOY_ASSETS_DIR = path.join(__dirname, '..', 'deploy_assets', 'assets');

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeBundle() {
  console.log('📊 Analyzing DataStatPro bundle...\n');

  if (!fs.existsSync(DEPLOY_ASSETS_DIR)) {
    console.error('❌ Build assets not found. Please run "npm run build" first.');
    process.exit(1);
  }

  const files = fs.readdirSync(DEPLOY_ASSETS_DIR);
  const jsFiles = files.filter(file => file.endsWith('.js'));
  const cssFiles = files.filter(file => file.endsWith('.css'));

  let totalJSSize = 0;
  let totalCSSSize = 0;
  const largeChunks = [];
  const chunkAnalysis = [];

  console.log('🔍 JavaScript Chunks:');
  console.log('─'.repeat(80));

  jsFiles.forEach(file => {
    const filePath = path.join(DEPLOY_ASSETS_DIR, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    totalJSSize += size;

    const analysis = {
      name: file,
      size: size,
      formattedSize: formatBytes(size),
      type: 'js'
    };

    // Identify chunk types based on naming
    if (file.includes('plotly')) {
      analysis.category = 'Charts (Plotly)';
      analysis.recommendation = 'Lazy load only when charts are needed';
    } else if (file.includes('tensorflow')) {
      analysis.category = 'Machine Learning';
      analysis.recommendation = 'Lazy load only for AI features';
    } else if (file.includes('mathjs') || file.includes('math-lib')) {
      analysis.category = 'Math Library';
      analysis.recommendation = 'Consider lighter alternatives for basic math';
    } else if (file.includes('mui')) {
      analysis.category = 'UI Framework';
      analysis.recommendation = 'Tree-shake unused components';
    } else if (file.includes('react')) {
      analysis.category = 'React Core';
      analysis.recommendation = 'Core dependency - optimized';
    } else if (file.includes('mermaid')) {
      analysis.category = 'Diagrams';
      analysis.recommendation = 'Lazy load for diagram features';
    } else {
      analysis.category = 'Other';
      analysis.recommendation = 'Review for optimization opportunities';
    }

    chunkAnalysis.push(analysis);

    if (size > 500 * 1024) { // Files larger than 500KB
      largeChunks.push(analysis);
    }

    console.log(`${analysis.formattedSize.padEnd(12)} ${file}`);
  });

  console.log('\n🎨 CSS Files:');
  console.log('─'.repeat(80));

  cssFiles.forEach(file => {
    const filePath = path.join(DEPLOY_ASSETS_DIR, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    totalCSSSize += size;

    console.log(`${formatBytes(size).padEnd(12)} ${file}`);
  });

  console.log('\n📈 Bundle Summary:');
  console.log('─'.repeat(80));
  console.log(`Total JavaScript: ${formatBytes(totalJSSize)}`);
  console.log(`Total CSS: ${formatBytes(totalCSSSize)}`);
  console.log(`Total Bundle: ${formatBytes(totalJSSize + totalCSSSize)}`);
  console.log(`Number of JS chunks: ${jsFiles.length}`);

  if (largeChunks.length > 0) {
    console.log('\n⚠️  Large Chunks (>500KB):');
    console.log('─'.repeat(80));
    largeChunks.forEach(chunk => {
      console.log(`${chunk.formattedSize.padEnd(12)} ${chunk.name}`);
      console.log(`   Category: ${chunk.category}`);
      console.log(`   💡 ${chunk.recommendation}`);
      console.log('');
    });
  }

  // Performance recommendations
  console.log('\n🚀 Performance Recommendations:');
  console.log('─'.repeat(80));

  const recommendations = [
    '1. ✅ Plotly.js is properly code-split - ensure lazy loading',
    '2. ✅ TensorFlow.js is separated - load only for AI features',
    '3. ✅ MathJS is chunked - consider lighter alternatives',
    '4. 📦 Consider using dynamic imports for rarely used features',
    '5. 🗜️  Enable Brotli compression on your server',
    '6. 🔄 Implement service worker caching for static assets',
    '7. 📱 Consider different bundles for mobile vs desktop'
  ];

  recommendations.forEach(rec => console.log(rec));

  // Bundle size targets
  console.log('\n🎯 Bundle Size Targets:');
  console.log('─'.repeat(80));
  console.log('Initial bundle (critical path): < 200KB gzipped');
  console.log('Total JavaScript: < 1MB gzipped');
  console.log('Largest single chunk: < 500KB gzipped');

  const initialBundle = chunkAnalysis.find(chunk => 
    chunk.name.includes('main') || chunk.name.includes('index')
  );

  if (initialBundle) {
    const gzippedEstimate = initialBundle.size * 0.3; // Rough gzip estimate
    console.log(`\nCurrent initial bundle: ~${formatBytes(gzippedEstimate)} gzipped`);
    
    if (gzippedEstimate > 200 * 1024) {
      console.log('⚠️  Initial bundle is larger than recommended 200KB');
    } else {
      console.log('✅ Initial bundle size looks good');
    }
  }

  console.log('\n📊 Next steps:');
  console.log('1. Run PageSpeed Insights to measure real-world performance');
  console.log('2. Monitor Core Web Vitals in production');
  console.log('3. Consider implementing resource hints (preload, prefetch)');
  console.log('4. Test on slow networks and devices');
}

if (require.main === module) {
  analyzeBundle();
}

module.exports = { analyzeBundle };
