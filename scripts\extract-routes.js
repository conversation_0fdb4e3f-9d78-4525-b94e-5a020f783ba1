#!/usr/bin/env node

/**
 * Extract all routes from the DataStatPro application for SEO sitemap generation
 * This script analyzes the route configuration files and outputs all public routes
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base URL for the application
const BASE_URL = 'https://www.datastatpro.com';

// Route files to analyze
const routeFiles = [
  'src/routing/routes/coreRoutes.ts',
  'src/routing/routes/dataManagementRoutes.ts', 
  'src/routing/routes/statisticsRoutes.ts',
  'src/routing/routes/visualizationRoutes.ts',
  'src/routing/routes/correlationRoutes.ts',
  'src/routing/routes/advancedRoutes.ts',
  'src/routing/routes/advancedRoutesSimple.ts'
];

// Function to extract routes from a TypeScript file
function extractRoutesFromFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const routes = [];
    
    // Simple regex to extract route objects
    // This is a basic implementation - in a real scenario you might want to use a proper TypeScript parser
    const routeRegex = /{\s*path:\s*['"`]([^'"`]+)['"`][^}]*allowPublic:\s*true[^}]*metadata:\s*{[^}]*title:\s*['"`]([^'"`]+)['"`][^}]*description:\s*['"`]([^'"`]+)['"`][^}]*}/gs;
    
    let match;
    while ((match = routeRegex.exec(content)) !== null) {
      const [, routePath, title, description] = match;
      
      // Skip hidden routes
      if (match[0].includes('hidden: true')) {
        continue;
      }
      
      routes.push({
        path: routePath,
        title: title,
        description: description,
        file: path.basename(filePath)
      });
    }
    
    return routes;
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return [];
  }
}

// Main function to extract all routes
function extractAllRoutes() {
  const allRoutes = [];
  
  console.log('🔍 Extracting routes from configuration files...\n');
  
  routeFiles.forEach(file => {
    console.log(`📁 Processing: ${file}`);
    const routes = extractRoutesFromFile(file);
    console.log(`   Found ${routes.length} public routes`);
    allRoutes.push(...routes);
  });
  
  console.log(`\n✅ Total routes extracted: ${allRoutes.length}\n`);
  
  return allRoutes;
}

// Function to generate sitemap URLs
function generateSitemapUrls(routes) {
  const urls = [];
  
  // Add homepage
  urls.push({
    loc: BASE_URL + '/',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'daily',
    priority: '1.0',
    title: 'Homepage'
  });
  
  // Add static pages
  const staticPages = [
    { path: '/privacy', title: 'Privacy Policy', priority: '0.3' },
    { path: '/terms', title: 'Terms of Service', priority: '0.3' }
  ];
  
  staticPages.forEach(page => {
    urls.push({
      loc: BASE_URL + page.path,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'monthly',
      priority: page.priority,
      title: page.title
    });
  });
  
  // Process application routes
  routes.forEach(route => {
    // Convert route path to URL path
    let urlPath = route.path;
    
    // Handle special cases
    if (urlPath === 'dashboard') {
      urlPath = '/app#dashboard';
    } else if (urlPath === 'home') {
      // Skip home as it's mapped to dashboard
      return;
    } else {
      urlPath = `/app#${urlPath}`;
    }
    
    // Determine priority based on route category and importance
    let priority = '0.7';
    if (route.path.includes('dashboard')) priority = '0.9';
    else if (route.path.includes('data-management')) priority = '0.8';
    else if (route.path.includes('stats') || route.path.includes('inferential')) priority = '0.8';
    else if (route.path.includes('charts') || route.path.includes('visualization')) priority = '0.7';
    else if (route.path.includes('correlation')) priority = '0.7';
    else if (route.path.includes('advanced')) priority = '0.6';
    
    // Determine change frequency
    let changefreq = 'weekly';
    if (route.path.includes('dashboard')) changefreq = 'daily';
    else if (route.path.includes('auth') || route.path.includes('help')) changefreq = 'monthly';
    
    urls.push({
      loc: BASE_URL + urlPath,
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: changefreq,
      priority: priority,
      title: route.title,
      description: route.description
    });
  });
  
  return urls;
}

// Function to output results
function outputResults(routes, urls) {
  console.log('📊 ROUTE ANALYSIS RESULTS\n');
  console.log('=' .repeat(50));
  
  // Group routes by file
  const routesByFile = {};
  routes.forEach(route => {
    if (!routesByFile[route.file]) {
      routesByFile[route.file] = [];
    }
    routesByFile[route.file].push(route);
  });
  
  Object.keys(routesByFile).forEach(file => {
    console.log(`\n📁 ${file}:`);
    routesByFile[file].forEach(route => {
      console.log(`   • ${route.path} - "${route.title}"`);
    });
  });
  
  console.log('\n' + '=' .repeat(50));
  console.log('🌐 SITEMAP URLS\n');
  
  urls.forEach(url => {
    console.log(`${url.loc} (${url.priority}) - ${url.title}`);
  });
  
  console.log(`\n✅ Total URLs for sitemap: ${urls.length}`);
}

// Run the extraction
if (import.meta.url === `file://${process.argv[1]}`) {
  const routes = extractAllRoutes();
  const urls = generateSitemapUrls(routes);
  outputResults(routes, urls);

  // Save results to JSON for further processing
  const results = {
    extractedAt: new Date().toISOString(),
    totalRoutes: routes.length,
    totalUrls: urls.length,
    routes: routes,
    urls: urls
  };

  fs.writeFileSync('route-analysis.json', JSON.stringify(results, null, 2));
  console.log('\n💾 Results saved to route-analysis.json');
}

export { extractAllRoutes, generateSitemapUrls };
