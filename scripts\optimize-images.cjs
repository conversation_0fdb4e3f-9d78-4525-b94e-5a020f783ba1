#!/usr/bin/env node

/**
 * Image optimization script for DataStatPro
 * Converts PNG images to WebP format for better performance
 * 
 * Usage: node scripts/optimize-images.js
 * 
 * Requirements: Install sharp package
 * npm install --save-dev sharp
 */

const fs = require('fs');
const path = require('path');

// Check if sharp is available
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.error('❌ Sharp is not installed. Please run: npm install --save-dev sharp');
  process.exit(1);
}

const PUBLIC_DIR = path.join(__dirname, '..', 'public');
const IMAGES_TO_OPTIMIZE = [
  'logo.png',
  'advanced-analysis.png',
  'beautiful-charts.png',
  'cross-platform.png',
  'data-management.png',
  'favicon.png'
];

async function optimizeImage(imagePath, outputPath, quality = 80) {
  try {
    const stats = fs.statSync(imagePath);
    const originalSize = stats.size;
    
    await sharp(imagePath)
      .webp({ quality, effort: 6 })
      .toFile(outputPath);
    
    const newStats = fs.statSync(outputPath);
    const newSize = newStats.size;
    const savings = ((originalSize - newSize) / originalSize * 100).toFixed(1);
    
    console.log(`✅ ${path.basename(imagePath)} -> ${path.basename(outputPath)}`);
    console.log(`   ${(originalSize / 1024).toFixed(1)}KB -> ${(newSize / 1024).toFixed(1)}KB (${savings}% smaller)`);
    
    return { originalSize, newSize, savings: parseFloat(savings) };
  } catch (error) {
    console.error(`❌ Failed to optimize ${imagePath}:`, error.message);
    return null;
  }
}

async function generateOptimizedImages() {
  console.log('🖼️  Optimizing images for DataStatPro...\n');
  
  let totalOriginalSize = 0;
  let totalNewSize = 0;
  let optimizedCount = 0;
  
  for (const imageName of IMAGES_TO_OPTIMIZE) {
    const imagePath = path.join(PUBLIC_DIR, imageName);
    
    if (!fs.existsSync(imagePath)) {
      console.log(`⚠️  Skipping ${imageName} (not found)`);
      continue;
    }
    
    const webpName = imageName.replace(/\.(png|jpg|jpeg)$/i, '.webp');
    const webpPath = path.join(PUBLIC_DIR, webpName);
    
    // Skip if WebP already exists and is newer than original
    if (fs.existsSync(webpPath)) {
      const originalStat = fs.statSync(imagePath);
      const webpStat = fs.statSync(webpPath);
      
      if (webpStat.mtime > originalStat.mtime) {
        console.log(`⏭️  Skipping ${imageName} (WebP is up to date)`);
        continue;
      }
    }
    
    const result = await optimizeImage(imagePath, webpPath);
    if (result) {
      totalOriginalSize += result.originalSize;
      totalNewSize += result.newSize;
      optimizedCount++;
    }
    
    console.log(''); // Empty line for readability
  }
  
  if (optimizedCount > 0) {
    const totalSavings = ((totalOriginalSize - totalNewSize) / totalOriginalSize * 100).toFixed(1);
    console.log(`🎉 Optimization complete!`);
    console.log(`📊 Total: ${(totalOriginalSize / 1024).toFixed(1)}KB -> ${(totalNewSize / 1024).toFixed(1)}KB`);
    console.log(`💾 Saved: ${((totalOriginalSize - totalNewSize) / 1024).toFixed(1)}KB (${totalSavings}% reduction)`);
    console.log(`📁 Optimized ${optimizedCount} images`);
  } else {
    console.log('ℹ️  No images needed optimization');
  }
}

// Create a simple image component helper
function generateImageComponentHelper() {
  const helperContent = `/**
 * Optimized Image Component with WebP support and lazy loading
 * Auto-generated by optimize-images.js
 */

import React, { useState } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
  loading?: 'lazy' | 'eager';
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  style,
  loading = 'lazy'
}) => {
  const [imageError, setImageError] = useState(false);
  
  // Generate WebP source path
  const webpSrc = src.replace(/\\.(png|jpg|jpeg)$/i, '.webp');
  
  const handleError = () => {
    setImageError(true);
  };
  
  if (imageError) {
    // Fallback to original image if WebP fails
    return (
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        style={style}
        loading={loading}
      />
    );
  }
  
  return (
    <picture>
      <source srcSet={webpSrc} type="image/webp" />
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        style={style}
        loading={loading}
        onError={handleError}
      />
    </picture>
  );
};

export default OptimizedImage;
`;

  const componentPath = path.join(__dirname, '..', 'src', 'components', 'UI', 'OptimizedImage.tsx');
  fs.writeFileSync(componentPath, helperContent);
  console.log(`📝 Generated OptimizedImage component at ${componentPath}`);
}

// Run the optimization
async function main() {
  await generateOptimizedImages();
  generateImageComponentHelper();
  
  console.log('\\n🚀 Next steps:');
  console.log('1. Replace <img> tags with <OptimizedImage> component');
  console.log('2. Update image references to use the new component');
  console.log('3. Test that WebP images load correctly');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { optimizeImage, generateOptimizedImages };
