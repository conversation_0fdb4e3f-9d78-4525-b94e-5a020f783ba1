#!/usr/bin/env node

/**
 * SEO Verification Script for DataStatPro
 * Verifies sitemap structure, meta tags, and SEO implementation
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const BASE_URL = 'https://www.datastatpro.com';
const SITEMAP_PATH = 'public/sitemap.xml';
const ROBOTS_PATH = 'public/robots.txt';
const INDEX_HTML_PATH = 'index.html';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(title, 'bold');
  console.log('='.repeat(60));
}

// Verify sitemap.xml structure and content
function verifySitemap() {
  logSection('🗺️  SITEMAP VERIFICATION');
  
  try {
    const sitemapContent = fs.readFileSync(SITEMAP_PATH, 'utf8');
    
    // Basic XML structure check
    if (!sitemapContent.includes('<?xml version="1.0" encoding="UTF-8"?>')) {
      log('❌ Missing XML declaration', 'red');
      return false;
    }
    
    if (!sitemapContent.includes('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">')) {
      log('❌ Missing or incorrect urlset declaration', 'red');
      return false;
    }
    
    // Extract URLs
    const urlMatches = sitemapContent.match(/<loc>(.*?)<\/loc>/g);
    if (!urlMatches) {
      log('❌ No URLs found in sitemap', 'red');
      return false;
    }
    
    const urls = urlMatches.map(match => match.replace(/<\/?loc>/g, ''));
    
    log(`✅ Found ${urls.length} URLs in sitemap`, 'green');
    
    // Verify URL structure
    const appRoutes = urls.filter(url => url.includes('/app#'));
    const staticPages = urls.filter(url => !url.includes('/app#') && url !== BASE_URL + '/');
    
    log(`📱 App routes: ${appRoutes.length}`, 'blue');
    log(`📄 Static pages: ${staticPages.length}`, 'blue');
    log(`🏠 Homepage: ${urls.includes(BASE_URL + '/') ? 'Present' : 'Missing'}`, 
        urls.includes(BASE_URL + '/') ? 'green' : 'red');
    
    // Check for required routes
    const requiredRoutes = [
      '/app#dashboard',
      '/app#data-management',
      '/app#stats',
      '/app#charts',
      '/app#inferential-stats',
      '/app#correlation-analysis'
    ];
    
    log('\n📋 Required Routes Check:', 'bold');
    requiredRoutes.forEach(route => {
      const fullUrl = BASE_URL + route;
      const present = urls.includes(fullUrl);
      log(`   ${present ? '✅' : '❌'} ${route}`, present ? 'green' : 'red');
    });
    
    // Check for proper lastmod dates
    const lastmodMatches = sitemapContent.match(/<lastmod>(.*?)<\/lastmod>/g);
    if (lastmodMatches) {
      const dates = lastmodMatches.map(match => match.replace(/<\/?lastmod>/g, ''));
      const currentDate = new Date().toISOString().split('T')[0];
      const recentDates = dates.filter(date => date === currentDate);
      log(`📅 URLs with current date (${currentDate}): ${recentDates.length}/${dates.length}`, 
          recentDates.length > 0 ? 'green' : 'yellow');
    }
    
    return true;
  } catch (error) {
    log(`❌ Error reading sitemap: ${error.message}`, 'red');
    return false;
  }
}

// Verify robots.txt
function verifyRobots() {
  logSection('🤖 ROBOTS.TXT VERIFICATION');
  
  try {
    const robotsContent = fs.readFileSync(ROBOTS_PATH, 'utf8');
    
    // Check basic structure
    const hasUserAgent = robotsContent.includes('User-agent: *');
    const hasAllow = robotsContent.includes('Allow: /');
    const hasSitemap = robotsContent.includes(`Sitemap: ${BASE_URL}/sitemap.xml`);
    
    log(`${hasUserAgent ? '✅' : '❌'} User-agent directive`, hasUserAgent ? 'green' : 'red');
    log(`${hasAllow ? '✅' : '❌'} Allow directive`, hasAllow ? 'green' : 'red');
    log(`${hasSitemap ? '✅' : '❌'} Sitemap reference`, hasSitemap ? 'green' : 'red');
    
    return hasUserAgent && hasAllow && hasSitemap;
  } catch (error) {
    log(`❌ Error reading robots.txt: ${error.message}`, 'red');
    return false;
  }
}

// Verify HTML meta tags
function verifyMetaTags() {
  logSection('🏷️  META TAGS VERIFICATION');
  
  try {
    const htmlContent = fs.readFileSync(INDEX_HTML_PATH, 'utf8');
    
    // Required meta tags
    const requiredTags = [
      { name: 'title', pattern: /<title>.*DataStatPro.*<\/title>/ },
      { name: 'description', pattern: /<meta name="description" content=".*"/ },
      { name: 'keywords', pattern: /<meta name="keywords" content=".*"/ },
      { name: 'og:title', pattern: /<meta property="og:title" content=".*"/ },
      { name: 'og:description', pattern: /<meta property="og:description" content=".*"/ },
      { name: 'og:image', pattern: /<meta property="og:image" content=".*"/ },
      { name: 'twitter:card', pattern: /<meta property="twitter:card" content=".*"/ },
      { name: 'canonical', pattern: /<link rel="canonical" href=".*"/ }
    ];
    
    log('📋 Required Meta Tags:', 'bold');
    requiredTags.forEach(tag => {
      const present = tag.pattern.test(htmlContent);
      log(`   ${present ? '✅' : '❌'} ${tag.name}`, present ? 'green' : 'red');
    });
    
    // Check JSON-LD structured data
    const hasJsonLd = htmlContent.includes('<script type="application/ld+json">');
    const hasSchemaOrg = htmlContent.includes('"@context": "https://schema.org"');
    const hasSoftwareApp = htmlContent.includes('"@type": "SoftwareApplication"');
    
    log('\n📊 Structured Data:', 'bold');
    log(`   ${hasJsonLd ? '✅' : '❌'} JSON-LD script`, hasJsonLd ? 'green' : 'red');
    log(`   ${hasSchemaOrg ? '✅' : '❌'} Schema.org context`, hasSchemaOrg ? 'green' : 'red');
    log(`   ${hasSoftwareApp ? '✅' : '❌'} SoftwareApplication type`, hasSoftwareApp ? 'green' : 'red');
    
    return true;
  } catch (error) {
    log(`❌ Error reading HTML file: ${error.message}`, 'red');
    return false;
  }
}

// Generate SEO report
function generateReport() {
  logSection('📊 SEO AUDIT SUMMARY');
  
  const sitemapOk = verifySitemap();
  const robotsOk = verifyRobots();
  const metaTagsOk = verifyMetaTags();
  
  const overallScore = [sitemapOk, robotsOk, metaTagsOk].filter(Boolean).length;
  const totalChecks = 3;
  
  log(`\n🎯 Overall SEO Score: ${overallScore}/${totalChecks}`, 
      overallScore === totalChecks ? 'green' : 'yellow');
  
  if (overallScore === totalChecks) {
    log('🎉 All SEO checks passed! Your site is ready for deployment.', 'green');
  } else {
    log('⚠️  Some SEO issues found. Please review the above sections.', 'yellow');
  }
  
  // Recommendations
  log('\n💡 SEO Recommendations:', 'bold');
  log('   • Submit sitemap to Google Search Console', 'blue');
  log('   • Monitor Core Web Vitals performance', 'blue');
  log('   • Add more descriptive alt text to images', 'blue');
  log('   • Consider adding FAQ schema markup', 'blue');
  log('   • Set up Google Analytics for tracking', 'blue');
  
  return overallScore === totalChecks;
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  log('🔍 Starting SEO Verification for DataStatPro...', 'bold');
  
  const success = generateReport();
  
  process.exit(success ? 0 : 1);
}

export { verifySitemap, verifyRobots, verifyMetaTags, generateReport };
