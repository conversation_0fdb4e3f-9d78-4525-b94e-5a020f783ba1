import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Chip,
  Box,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Alert,
  Grid
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Star as StarIcon,
  School as SchoolIcon,
  Upgrade as UpgradeIcon,
  Info as InfoIcon,
  Lock as LockIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { config, isStripeEnabled, getStripePriceId } from '../../config/environment';
import StripeCheckoutButton from '../Payment/StripeCheckoutButton';

interface UpgradeOption {
  id: 'pro' | 'edu';
  name: string;
  monthlyPrice: string;
  annualPrice: string;
  features: string[];
  requiresEduEmail?: boolean;
  icon: React.ReactNode;
  color: string;
}

interface AccountStatusCardProps {
  showUpgradeOptions?: boolean;
  compact?: boolean;
  showQuickActions?: boolean;
}

const AccountStatusCard: React.FC<AccountStatusCardProps> = ({
  showUpgradeOptions = true,
  compact = false,
  showQuickActions = false
}) => {
  const {
    user,
    accountType,
    subscriptionData,
    subscriptionStatus,
    hasActiveSubscription,
    nextPaymentDate,
    billingCycle,
    canUpgradeAccount,
    isEducationalUser,
    educationalTier,
    canAccessAdvancedAnalysis,
    canAccessPublicationReady
  } = useAuth();

  const getUpgradeOptions = (): UpgradeOption[] => {
    // For educational users who are on free tier
    if (isEducationalUser && accountType === 'edu') {
      return [
        {
          id: 'pro',
          name: 'Educational Pro',
          monthlyPrice: '$10',
          annualPrice: '$96',
          features: [
            'Keep all current Advanced Analysis features',
            'Add Publication Ready tools',
            'Add Cloud Storage',
            'Priority Support',
            'Perfect for academic papers & research'
          ],
          icon: <SchoolIcon />,
          color: '#1976d2'
        }
      ];
    }

    // For standard users
    if (accountType === 'standard') {
      return [
        {
          id: 'pro',
          name: 'Pro Account',
          monthlyPrice: '$10',
          annualPrice: '$96',
          features: [
            'Advanced Analysis',
            'Publication Ready',
            'Cloud Storage',
            'Priority Support'
          ],
          icon: <StarIcon />,
          color: '#1976d2'
        }
      ];
    }

    return [];
  };

  const upgradeOptions = getUpgradeOptions();

  const getAccountTypeDisplay = () => {
    switch (accountType) {
      case 'pro':
        return {
          label: 'PRO',
          color: 'primary' as const,
          description: 'Full Pro features'
        };
      case 'edu_pro':
        return {
          label: 'EDUCATIONAL PRO',
          color: 'primary' as const,
          description: 'Full Pro features for education'
        };
      case 'edu':
        return {
          label: 'EDUCATIONAL FREE',
          color: 'secondary' as const,
          description: 'Advanced Analysis included'
        };
      case 'standard':
        return {
          label: 'STANDARD',
          color: 'default' as const,
          description: 'Basic features'
        };
      default:
        return {
          label: 'GUEST',
          color: 'default' as const,
          description: 'Sample data access'
        };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleEmailUpgrade = (planName: string) => {
    const subject = `Account Upgrade Request - ${planName}`;
    const body = `Hi DataStatPro Team,

I would like to upgrade my account to ${planName}.

Account Details:
- Email: ${user?.email}
- Current Plan: ${accountType?.toUpperCase()}
- Requested Plan: ${planName}

Please provide instructions for upgrading my account.

Thank you!`;

    const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoUrl);
  };

  if (compact) {
    return (
      <Card sx={{ minWidth: 275 }}>
        <CardContent sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Current Plan
              </Typography>
              <Chip 
                label={getAccountTypeDisplay().label} 
                color={getAccountTypeDisplay().color}
                size="small"
              />
            </Box>
            {canUpgradeAccount && showQuickActions && (
              <Button
                size="small"
                variant="outlined"
                startIcon={<UpgradeIcon />}
                onClick={() => handleEmailUpgrade('Pro Account')}
              >
                Upgrade
              </Button>
            )}
          </Box>
          {hasActiveSubscription && nextPaymentDate && (
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
              Next payment: {formatDate(nextPaymentDate)}
            </Typography>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Account Status
        </Typography>
        
        {/* Current Plan Display */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
            <Chip
              label={getAccountTypeDisplay().label}
              color={getAccountTypeDisplay().color}
              size="medium"
            />
            {hasActiveSubscription && (
              <Chip
                label="ACTIVE"
                color="success"
                size="small"
                icon={<CheckIcon />}
              />
            )}
          </Box>

          {/* Educational Tier Information */}
          {isEducationalUser && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SchoolIcon fontSize="small" />
                Educational Account Features
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {canAccessAdvancedAnalysis ? (
                    <CheckIcon fontSize="small" color="success" />
                  ) : (
                    <CloseIcon fontSize="small" color="error" />
                  )}
                  <Typography variant="body2">
                    Advanced Analysis {canAccessAdvancedAnalysis ? '(Included Free)' : '(Not Available)'}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {canAccessPublicationReady ? (
                    <CheckIcon fontSize="small" color="success" />
                  ) : (
                    <LockIcon fontSize="small" color="warning" />
                  )}
                  <Typography variant="body2">
                    Publication Ready {canAccessPublicationReady ? '(Pro Subscription)' : '(Upgrade Required)'}
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}

          {/* Subscription Details */}
          {subscriptionData && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Billing Cycle
                  </Typography>
                  <Typography variant="body1" sx={{ textTransform: 'capitalize' }}>
                    {billingCycle}
                  </Typography>
                </Grid>
                {nextPaymentDate && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Next Payment
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(nextPaymentDate)}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </Box>

        {/* Upgrade Options */}
        {canUpgradeAccount && showUpgradeOptions && (
          <>
            <Divider sx={{ mb: 3 }} />
            <Typography variant="h6" gutterBottom>
              Upgrade Your Account
            </Typography>
            
            {!isStripeEnabled() && (
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  Payment processing is currently in development. Click upgrade buttons to contact us via email.
                </Typography>
              </Alert>
            )}

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {upgradeOptions.map((option) => (
                <Card key={option.id} variant="outlined" sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        bgcolor: `${option.color}20`,
                        color: option.color,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0
                      }}
                    >
                      {option.icon}
                    </Box>
                    
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {option.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {option.monthlyPrice}/month or {option.annualPrice}/year (save 20%)
                      </Typography>
                      
                      {option.requiresEduEmail && (
                        <Typography variant="caption" color="warning.main" sx={{ display: 'block', mb: 1 }}>
                          <InfoIcon sx={{ fontSize: 14, mr: 0.5 }} />
                          Requires educational email (.edu domain)
                        </Typography>
                      )}

                      <List dense sx={{ py: 0 }}>
                        {option.features.slice(0, 3).map((feature, index) => (
                          <ListItem key={index} sx={{ px: 0, py: 0.25 }}>
                            <ListItemIcon sx={{ minWidth: 20 }}>
                              <CheckIcon sx={{ fontSize: 16, color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText 
                              primary={feature}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  </Box>
                  
                  <CardActions sx={{ px: 0, pt: 2 }}>
                    {isStripeEnabled() ? (
                      <StripeCheckoutButton
                        priceId={getStripePriceId(option.id, 'monthly')}
                        planName={option.name}
                        billingCycle="monthly"
                        requiresEducationalEmail={option.requiresEduEmail}
                      >
                        Upgrade to {option.name}
                      </StripeCheckoutButton>
                    ) : (
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => handleEmailUpgrade(option.name)}
                        sx={{ bgcolor: option.color }}
                      >
                        Upgrade to {option.name}
                      </Button>
                    )}
                  </CardActions>
                </Card>
              ))}
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default AccountStatusCard;
