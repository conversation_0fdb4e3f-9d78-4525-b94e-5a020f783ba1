import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  Paper,
  Stack
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class AdminErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error
    console.error('Admin Dashboard Error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Box sx={{ p: 3, width: '100%' }}>
          <Paper elevation={0} variant="outlined" sx={{ p: 4, borderRadius: 2 }}>
            <Stack spacing={3} alignItems="center" textAlign="center">
              <ErrorIcon sx={{ fontSize: 48, color: 'error.main' }} />
              
              <Typography variant="h5" color="error.main" gutterBottom>
                Something went wrong
              </Typography>
              
              <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 600 }}>
                An error occurred while loading this admin dashboard section. 
                This might be due to a database connection issue or a temporary problem.
              </Typography>

              <Alert severity="error" sx={{ width: '100%', maxWidth: 600 }}>
                <Typography variant="body2">
                  <strong>Error:</strong> {this.state.error?.message || 'Unknown error occurred'}
                </Typography>
              </Alert>

              <Stack direction="row" spacing={2}>
                <Button
                  variant="contained"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleReset}
                  color="primary"
                >
                  Try Again
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={() => window.location.reload()}
                  color="secondary"
                >
                  Reload Page
                </Button>
              </Stack>

              {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                <Box sx={{ mt: 3, width: '100%' }}>
                  <Typography variant="h6" gutterBottom>
                    Error Details (Development Only)
                  </Typography>
                  <Paper 
                    sx={{ 
                      p: 2, 
                      bgcolor: 'grey.100', 
                      fontFamily: 'monospace', 
                      fontSize: '0.75rem',
                      overflow: 'auto',
                      maxHeight: 200
                    }}
                  >
                    <pre>{this.state.error?.stack}</pre>
                    <pre>{this.state.errorInfo.componentStack}</pre>
                  </Paper>
                </Box>
              )}
            </Stack>
          </Paper>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default AdminErrorBoundary;
