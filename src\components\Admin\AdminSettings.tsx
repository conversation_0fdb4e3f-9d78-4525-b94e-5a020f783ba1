import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Security as SecurityIcon,
  Person as PersonIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';
import { useAuth } from '../../context/AuthContext';

interface AdminUser {
  id: string;
  username: string | null;
  full_name: string | null;
  institution: string | null;
  country: string | null;
  avatar_url: string | null;
  updated_at: string;
  is_admin: boolean;
}

const AdminSettings: React.FC = () => {
  const { user, refreshAdminStatus } = useAuth();
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [addAdminDialogOpen, setAddAdminDialogOpen] = useState(false);
  const [newAdminEmail, setNewAdminEmail] = useState('');
  const [addingAdmin, setAddingAdmin] = useState(false);

  useEffect(() => {
    fetchAdminUsers();
  }, []);

  const fetchAdminUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase.rpc('get_admin_users');
      
      if (error) {
        throw error;
      }

      setAdminUsers(data || []);
    } catch (err: any) {
      console.error('Error fetching admin users:', err);
      setError(err.message || 'Failed to load admin users');
    } finally {
      setLoading(false);
    }
  };

  const handleAddAdmin = async () => {
    if (!newAdminEmail.trim()) return;

    try {
      setAddingAdmin(true);

      // For now, show a message that this feature requires manual database update
      // In a production environment, you would implement a proper user lookup
      setError(
        'Adding admin users via email is not yet implemented. ' +
        'Please use the Supabase dashboard to manually set is_admin = true for the user profile, ' +
        'or use the User Management tab to promote an existing user to admin.'
      );

    } catch (err: any) {
      console.error('Error adding admin:', err);
      setError(err.message || 'Failed to add admin user');
    } finally {
      setAddingAdmin(false);
    }
  };

  const handleRemoveAdmin = async (adminId: string) => {
    if (adminId === user?.id) {
      setError('You cannot remove your own admin privileges');
      return;
    }

    if (!confirm('Are you sure you want to remove admin privileges from this user?')) {
      return;
    }

    try {
      const { error } = await supabase.rpc('update_user_admin_status', {
        target_user_id: adminId,
        new_admin_status: false
      });

      if (error) {
        throw error;
      }

      await fetchAdminUsers();
    } catch (err: any) {
      console.error('Error removing admin:', err);
      setError(err.message || 'Failed to remove admin privileges');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Admin Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage admin users and system settings
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Admin Users Management */}
        <Grid item xs={12}>
          <Card elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SecurityIcon />
                  Admin Users
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={fetchAdminUsers}
                    disabled={loading}
                  >
                    Refresh
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setAddAdminDialogOpen(true)}
                  >
                    Add Admin
                  </Button>
                </Box>
              </Box>

              <Alert severity="warning" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  <strong>Important:</strong> Admin users have full system access. Only grant admin privileges to trusted users.
                  You cannot remove your own admin privileges for security reasons.
                </Typography>
              </Alert>

              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>User</TableCell>
                      <TableCell>Institution</TableCell>
                      <TableCell>Country</TableCell>
                      <TableCell>Last Updated</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {adminUsers.map((admin) => (
                      <TableRow key={admin.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {admin.full_name || 'No name'}
                            </Typography>
                            {admin.username && (
                              <Typography variant="caption" color="text.secondary">
                                @{admin.username}
                              </Typography>
                            )}
                            {admin.id === user?.id && (
                              <Chip
                                label="You"
                                size="small"
                                color="primary"
                                variant="outlined"
                                sx={{ ml: 1 }}
                              />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          {admin.institution || 'Not specified'}
                        </TableCell>
                        <TableCell>
                          {admin.country || 'Not specified'}
                        </TableCell>
                        <TableCell>
                          {formatDate(admin.updated_at)}
                        </TableCell>
                        <TableCell align="center">
                          {admin.id !== user?.id && (
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleRemoveAdmin(admin.id)}
                              title="Remove Admin Privileges"
                            >
                              <DeleteIcon />
                            </IconButton>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* System Information */}
        <Grid item xs={12} md={6}>
          <Card elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <InfoIcon />
                System Information
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemIcon>
                    <SettingsIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Admin Dashboard Version"
                    secondary="1.0.0"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <SecurityIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Security Level"
                    secondary="High - RLS Enabled"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Total Admin Users"
                    secondary={`${adminUsers.length} active`}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Security Guidelines */}
        <Grid item xs={12} md={6}>
          <Card elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <WarningIcon />
                Security Guidelines
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemText
                    primary="Regular Review"
                    secondary="Review admin user list monthly"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Principle of Least Privilege"
                    secondary="Only grant admin access when necessary"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Account Security"
                    secondary="Ensure admin accounts use strong passwords"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Activity Monitoring"
                    secondary="Monitor admin actions and system changes"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Add Admin Dialog */}
      <Dialog open={addAdminDialogOpen} onClose={() => setAddAdminDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Admin User</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Note:</strong> Adding admin users by email is not yet implemented.
                Use the User Management tab to promote existing users to admin, or manually
                update the database via Supabase dashboard.
              </Typography>
            </Alert>

            <TextField
              fullWidth
              label="User Email"
              type="email"
              value={newAdminEmail}
              onChange={(e) => setNewAdminEmail(e.target.value)}
              placeholder="Enter the email of the user to make admin"
              helperText="Feature coming soon - use User Management tab instead"
              disabled
            />

            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="body2">
                Admin privileges grant full system access. Only assign to trusted users.
              </Typography>
            </Alert>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddAdminDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminSettings;
