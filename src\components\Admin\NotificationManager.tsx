import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  Switch,
  FormControlLabel,
  Grid,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';
import { useAuth } from '../../context/AuthContext';
import type { Notification } from '../../hooks/useNotifications';
import NotificationRichText from '../UI/NotificationRichText';
import YouTubeLink from '../UI/YouTubeLink';
import { findYouTubeLinks } from '../../utils/youtubeUtils';

interface NotificationFormData {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  target_audience: 'all' | 'pro' | 'edu' | 'standard' | 'guest';
  priority: number;
  expires_at: string;
}

const NotificationManager: React.FC = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingNotification, setEditingNotification] = useState<Notification | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState<NotificationFormData>({
    title: '',
    message: '',
    type: 'info',
    target_audience: 'all',
    priority: 0,
    expires_at: ''
  });

  // Fetch all notifications (including inactive ones for admin view)
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        setError('Failed to fetch notifications');
        console.error('Error fetching notifications:', error);
        return;
      }

      setNotifications(data || []);
    } catch (err) {
      setError('Failed to fetch notifications');
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  const handleSubmit = async () => {
    if (!formData.title.trim() || !formData.message.trim()) {
      setError('Title and message are required');
      return;
    }

    try {
      const notificationData = {
        title: formData.title.trim(),
        message: formData.message.trim(),
        type: formData.type,
        target_audience: formData.target_audience,
        priority: formData.priority,
        expires_at: formData.expires_at || null,
        created_by: user?.id
      };

      if (editingNotification) {
        // Update existing notification
        const { error } = await supabase
          .from('notifications')
          .update(notificationData)
          .eq('id', editingNotification.id);

        if (error) throw error;
        setSuccess('Notification updated successfully');
      } else {
        // Create new notification
        const { error } = await supabase
          .from('notifications')
          .insert([notificationData]);

        if (error) throw error;
        setSuccess('Notification created successfully');
      }

      setDialogOpen(false);
      setEditingNotification(null);
      resetForm();
      fetchNotifications();
    } catch (err) {
      setError('Failed to save notification');
      console.error('Error saving notification:', err);
    }
  };

  const handleEdit = (notification: Notification) => {
    setEditingNotification(notification);
    setFormData({
      title: notification.title,
      message: notification.message,
      type: notification.type,
      target_audience: notification.target_audience,
      priority: notification.priority,
      expires_at: notification.expires_at ? notification.expires_at.split('T')[0] : ''
    });
    setDialogOpen(true);
  };

  const handleToggleActive = async (notification: Notification) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_active: !notification.is_active })
        .eq('id', notification.id);

      if (error) throw error;
      setSuccess(`Notification ${notification.is_active ? 'deactivated' : 'activated'}`);
      fetchNotifications();
    } catch (err) {
      setError('Failed to update notification status');
      console.error('Error:', err);
    }
  };

  const handleDelete = async (notification: Notification) => {
    if (!confirm('Are you sure you want to delete this notification?')) return;

    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notification.id);

      if (error) throw error;
      setSuccess('Notification deleted successfully');
      fetchNotifications();
    } catch (err) {
      setError('Failed to delete notification');
      console.error('Error:', err);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      message: '',
      type: 'info',
      target_audience: 'all',
      priority: 0,
      expires_at: ''
    });
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingNotification(null);
    resetForm();
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'info';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Notification Manager</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setDialogOpen(true)}
        >
          Add Notification
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Title</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Audience</TableCell>
              <TableCell>Priority</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {notifications.map((notification) => (
              <TableRow key={notification.id}>
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                    {notification.title}
                  </Typography>
                  <Box sx={{ maxWidth: 300 }}>
                    <NotificationRichText
                      text={notification.message.length > 100 ?
                        notification.message.substring(0, 100) + '...' :
                        notification.message
                      }
                      variant="caption"
                      color="text.secondary"
                      showYouTubePreview={false}
                    />
                  </Box>
                  {findYouTubeLinks(notification.message).length > 0 && (
                    <Chip
                      label={`${findYouTubeLinks(notification.message).length} video link(s)`}
                      size="small"
                      color="error"
                      variant="outlined"
                      sx={{ mt: 0.5 }}
                    />
                  )}
                </TableCell>
                <TableCell>
                  <Chip 
                    label={notification.type} 
                    color={getTypeColor(notification.type) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>{notification.target_audience}</TableCell>
                <TableCell>{notification.priority}</TableCell>
                <TableCell>
                  <Chip 
                    label={notification.is_active ? 'Active' : 'Inactive'}
                    color={notification.is_active ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {new Date(notification.created_at).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <IconButton onClick={() => handleEdit(notification)} size="small">
                    <EditIcon />
                  </IconButton>
                  <IconButton onClick={() => handleToggleActive(notification)} size="small">
                    {notification.is_active ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                  <IconButton onClick={() => handleDelete(notification)} size="small" color="error">
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingNotification ? 'Edit Notification' : 'Add New Notification'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Message"
                value={formData.message}
                onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                helperText="YouTube links will be automatically converted to clickable video links with previews"
              />
            </Grid>
            {formData.message && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Preview:
                </Typography>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    backgroundColor: 'grey.50',
                    border: '1px dashed',
                    borderColor: 'grey.300'
                  }}
                >
                  <NotificationRichText
                    text={formData.message}
                    variant="body2"
                    color="text.secondary"
                    showYouTubePreview={true}
                  />
                  {findYouTubeLinks(formData.message).length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Divider sx={{ mb: 1 }} />
                      <Typography variant="caption" color="text.secondary">
                        ✓ {findYouTubeLinks(formData.message).length} YouTube link(s) detected and will be enhanced
                      </Typography>
                    </Box>
                  )}
                </Paper>
              </Grid>
            )}
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                >
                  <MenuItem value="info">Info</MenuItem>
                  <MenuItem value="success">Success</MenuItem>
                  <MenuItem value="warning">Warning</MenuItem>
                  <MenuItem value="error">Error</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Target Audience</InputLabel>
                <Select
                  value={formData.target_audience}
                  onChange={(e) => setFormData({ ...formData, target_audience: e.target.value as any })}
                >
                  <MenuItem value="all">All Users</MenuItem>
                  <MenuItem value="pro">Pro Users</MenuItem>
                  <MenuItem value="edu">Educational Users</MenuItem>
                  <MenuItem value="standard">Standard Users</MenuItem>
                  <MenuItem value="guest">Guest Users</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                type="number"
                label="Priority"
                value={formData.priority}
                onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 0 })}
                helperText="Higher numbers = higher priority"
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                type="date"
                label="Expires On (Optional)"
                value={formData.expires_at}
                onChange={(e) => setFormData({ ...formData, expires_at: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingNotification ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default NotificationManager;
