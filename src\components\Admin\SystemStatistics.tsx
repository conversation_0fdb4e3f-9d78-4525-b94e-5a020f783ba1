import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  useTheme,
  alpha,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Storage as StorageIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';

interface SystemStats {
  total_users: number;
  total_profiles: number;
  admin_users: number;
  standard_users: number;
  pro_users: number;
  edu_users: number;
  edu_pro_users: number;
  users_with_datasets: number;
  total_datasets: number;
  users_last_7_days: number;
  users_last_30_days: number;
  active_users_last_7_days: number;
  active_users_last_30_days: number;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  icon: React.ReactNode;
  color: string;
  change?: {
    value: number;
    period: string;
    positive: boolean;
  };
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, subtitle, icon, color, change }) => {
  const theme = useTheme();

  return (
    <Card 
      elevation={0} 
      variant="outlined"
      sx={{ 
        height: '100%',
        borderRadius: 2,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4]
        }
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ 
            p: 1.5, 
            borderRadius: 2, 
            backgroundColor: alpha(color, 0.1),
            color: color,
            mr: 2
          }}>
            {icon}
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h4" component="div" fontWeight="bold" color={color}>
              {typeof value === 'number' ? value.toLocaleString() : value}
            </Typography>
            <Typography variant="h6" color="text.primary">
              {title}
            </Typography>
          </Box>
        </Box>
        
        <Typography variant="body2" color="text.secondary" gutterBottom>
          {subtitle}
        </Typography>

        {change && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Chip
              size="small"
              label={`${change.positive ? '+' : ''}${change.value} ${change.period}`}
              color={change.positive ? 'success' : 'error'}
              variant="outlined"
            />
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

const SystemStatistics: React.FC = () => {
  const theme = useTheme();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSystemStats();
    
    // Set up periodic refresh every 5 minutes
    const interval = setInterval(fetchSystemStats, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const fetchSystemStats = async () => {
    try {
      setError(null);

      const { data, error } = await supabase.rpc('get_user_statistics');
      
      if (error) {
        throw error;
      }

      setStats(data);
    } catch (err: any) {
      console.error('Error fetching system statistics:', err);
      setError(err.message || 'Failed to load system statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 300 }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={48} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading system statistics...
          </Typography>
        </Box>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Error Loading System Statistics
        </Typography>
        <Typography>
          {error}
        </Typography>
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert severity="warning">
        <Typography>
          No system statistics available.
        </Typography>
      </Alert>
    );
  }

  const growthMetrics = [
    {
      title: 'User Growth',
      current: stats.users_last_7_days,
      previous: stats.users_last_30_days - stats.users_last_7_days,
      period: '7 days vs previous 23 days'
    },
    {
      title: 'Active Users',
      current: stats.active_users_last_7_days,
      previous: stats.active_users_last_30_days - stats.active_users_last_7_days,
      period: '7 days vs previous 23 days'
    }
  ];

  const accountTypeBreakdown = [
    { type: 'Standard', count: stats.standard_users, color: theme.palette.grey[600] },
    { type: 'Pro', count: stats.pro_users, color: theme.palette.warning.main },
    { type: 'Educational', count: stats.edu_users, color: theme.palette.info.main },
    { type: 'Educational Pro', count: stats.edu_pro_users, color: theme.palette.secondary.main },
    { type: 'Admin', count: stats.admin_users, color: theme.palette.error.main }
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          System Statistics
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Detailed analytics and performance metrics
        </Typography>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4} lg={3}>
          <MetricCard
            title="Total Users"
            value={stats.total_users}
            subtitle="All registered accounts"
            icon={<PeopleIcon />}
            color={theme.palette.primary.main}
            change={{
              value: stats.users_last_7_days,
              period: 'this week',
              positive: true
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={3}>
          <MetricCard
            title="Active Users"
            value={stats.active_users_last_7_days}
            subtitle="Active in last 7 days"
            icon={<TrendingUpIcon />}
            color={theme.palette.success.main}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={3}>
          <MetricCard
            title="Total Datasets"
            value={stats.total_datasets}
            subtitle="User-created datasets"
            icon={<StorageIcon />}
            color={theme.palette.info.main}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={3}>
          <MetricCard
            title="Data Adoption"
            value={`${((stats.users_with_datasets / stats.total_users) * 100).toFixed(1)}%`}
            subtitle="Users with datasets"
            icon={<AssessmentIcon />}
            color={theme.palette.warning.main}
          />
        </Grid>
      </Grid>

      {/* Detailed Breakdown */}
      <Grid container spacing={3}>
        {/* Account Types */}
        <Grid item xs={12} md={6}>
          <Card elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PeopleIcon />
                Account Type Breakdown
              </Typography>
              
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Account Type</TableCell>
                      <TableCell align="right">Count</TableCell>
                      <TableCell align="right">Percentage</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {accountTypeBreakdown.map((item) => (
                      <TableRow key={item.type}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box
                              sx={{
                                width: 12,
                                height: 12,
                                borderRadius: '50%',
                                backgroundColor: item.color
                              }}
                            />
                            {item.type}
                          </Box>
                        </TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                          {item.count.toLocaleString()}
                        </TableCell>
                        <TableCell align="right">
                          {((item.count / stats.total_users) * 100).toFixed(1)}%
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Growth Metrics */}
        <Grid item xs={12} md={6}>
          <Card elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TimelineIcon />
                Growth Metrics
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                {growthMetrics.map((metric, index) => {
                  const changePercent = metric.previous > 0 
                    ? ((metric.current - metric.previous) / metric.previous * 100).toFixed(1)
                    : '0';
                  const isPositive = parseFloat(changePercent) >= 0;

                  return (
                    <Box key={index} sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body1" fontWeight="bold">
                          {metric.title}
                        </Typography>
                        <Chip
                          size="small"
                          label={`${isPositive ? '+' : ''}${changePercent}%`}
                          color={isPositive ? 'success' : 'error'}
                          variant="outlined"
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Current: {metric.current} | Previous: {metric.previous}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {metric.period}
                      </Typography>
                    </Box>
                  );
                })}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* System Health */}
        <Grid item xs={12}>
          <Card elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SpeedIcon />
                System Health Indicators
              </Typography>
              
              <Grid container spacing={3} sx={{ mt: 1 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main" fontWeight="bold">
                      {((stats.active_users_last_7_days / stats.total_users) * 100).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Weekly Active Rate
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="info.main" fontWeight="bold">
                      {((stats.users_with_datasets / stats.total_users) * 100).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Data Engagement
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main" fontWeight="bold">
                      {(((stats.pro_users + stats.edu_pro_users) / stats.total_users) * 100).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Premium Adoption
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="secondary.main" fontWeight="bold">
                      {stats.total_datasets > 0 ? (stats.total_datasets / stats.users_with_datasets).toFixed(1) : '0'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Avg Datasets/User
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SystemStatistics;
