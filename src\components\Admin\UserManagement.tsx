import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Button,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Pagination,
  Tooltip,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Star as StarIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';

interface User {
  id: string;
  email: string | null; // Made nullable since it might not be available
  username: string | null;
  full_name: string | null;
  institution: string | null;
  country: string | null;
  avatar_url: string | null;
  updated_at: string | null; // Made nullable for safety
  is_admin: boolean | null; // Made nullable for safety
  accounttype: string | null;
  created_at: string | null; // Made nullable since it's a fallback field
  last_sign_in_at: string | null;
}

interface EditUserDialogProps {
  open: boolean;
  user: User | null;
  onClose: () => void;
  onSave: (userId: string, updates: { accounttype: string; is_admin: boolean }) => Promise<void>;
}

const EditUserDialog: React.FC<EditUserDialogProps> = ({ open, user, onClose, onSave }) => {
  const [accountType, setAccountType] = useState<string>('standard');
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user) {
      setAccountType(user.accounttype || 'standard');
      setIsAdmin(user.is_admin || false);
    }
  }, [user]);

  const handleSave = async () => {
    if (!user) return;

    setSaving(true);
    try {
      await onSave(user.id, { accounttype: accountType, is_admin: isAdmin });
      onClose();
    } catch (error) {
      console.error('Error saving user:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Edit User: {user?.full_name || user?.email}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Account Type</InputLabel>
            <Select
              value={accountType}
              label="Account Type"
              onChange={(e) => setAccountType(e.target.value)}
            >
              <MenuItem value="standard">Standard</MenuItem>
              <MenuItem value="pro">Pro</MenuItem>
              <MenuItem value="edu">Educational</MenuItem>
              <MenuItem value="edu_pro">Educational Pro</MenuItem>
            </Select>
          </FormControl>

          <FormControlLabel
            control={
              <Switch
                checked={isAdmin}
                onChange={(e) => setIsAdmin(e.target.checked)}
                color="error"
              />
            }
            label="Admin Privileges"
            sx={{ mb: 2 }}
          />

          {isAdmin && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Warning:</strong> Admin privileges grant full system access. 
                Only assign to trusted users.
              </Typography>
            </Alert>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={saving}>
          Cancel
        </Button>
        <Button 
          onClick={handleSave} 
          variant="contained" 
          disabled={saving}
          startIcon={saving ? <CircularProgress size={16} /> : undefined}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const pageSize = 25;

  useEffect(() => {
    fetchUsers();
  }, [page, searchTerm]);

  const fetchUsers = async (retryCount = 0) => {
    try {
      if (retryCount === 0) {
        setLoading(true);
      }
      setError(null);

      const offset = (page - 1) * pageSize;
      const { data, error } = await supabase.rpc('get_all_users', {
        page_size: pageSize,
        page_offset: offset,
        search_term: searchTerm || null
      });

      if (error) {
        throw error;
      }

      setUsers(data || []);

      // Calculate total pages (this is a simplified approach)
      // In a real implementation, you'd want a separate count query
      setTotalPages(Math.max(1, Math.ceil((data?.length || 0) / pageSize)));
      console.log('✅ Users loaded successfully');
    } catch (err: any) {
      console.error('Error fetching users:', err);

      // Retry logic for connection issues
      if (retryCount < 2 && (err.message?.includes('network') || err.message?.includes('connection'))) {
        console.log(`🔄 Retrying users fetch (attempt ${retryCount + 1})`);
        setTimeout(() => fetchUsers(retryCount + 1), 1000 * (retryCount + 1));
        return;
      }

      setError(err.message || 'Failed to load users. Please try refreshing the page.');
    } finally {
      if (retryCount === 0) {
        setLoading(false);
      }
    }
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setEditDialogOpen(true);
  };

  const handleSaveUser = async (userId: string, updates: { accounttype: string; is_admin: boolean }) => {
    try {
      // Update account type
      const { error: accountError } = await supabase.rpc('update_user_account_type', {
        target_user_id: userId,
        new_account_type: updates.accounttype
      });

      if (accountError) {
        throw accountError;
      }

      // Update admin status
      const { error: adminError } = await supabase.rpc('update_user_admin_status', {
        target_user_id: userId,
        new_admin_status: updates.is_admin
      });

      if (adminError) {
        throw adminError;
      }

      // Refresh users list
      await fetchUsers();
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  };

  const getAccountTypeChip = (accountType: string | null) => {
    const type = accountType || 'standard';
    const config = {
      standard: { label: 'Standard', color: 'default' as const, icon: <PersonIcon /> },
      pro: { label: 'Pro', color: 'warning' as const, icon: <StarIcon /> },
      edu: { label: 'Educational', color: 'info' as const, icon: <SchoolIcon /> },
      edu_pro: { label: 'Edu Pro', color: 'secondary' as const, icon: <SchoolIcon /> }
    };

    const { label, color, icon } = config[type as keyof typeof config] || config.standard;

    return (
      <Chip
        label={label}
        color={color}
        size="small"
        icon={icon}
        variant="outlined"
      />
    );
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not available';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading && users.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 300 }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={48} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading users...
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      {/* Header */}
      <Box sx={{
        mb: 3,
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        gap: 2
      }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ fontSize: { xs: '1.5rem', sm: '2rem' } }}>
            User Management
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
            Manage user accounts, permissions, and account types
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchUsers}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Search */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search users by name, email, or institution..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ maxWidth: { xs: '100%', sm: 500 } }}
          size="small"
        />
      </Box>

      {/* Users Table */}
      <Box sx={{ width: '100%', overflow: 'auto' }}>
        <TableContainer
          component={Paper}
          variant="outlined"
          sx={{
            borderRadius: 2,
            maxHeight: { xs: '60vh', lg: '70vh' },
            minWidth: 800, // Ensure table doesn't get too cramped
            transition: 'max-height 0.3s ease-in-out'
          }}
        >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold', bgcolor: 'grey.50' }}>User</TableCell>
                <TableCell sx={{ fontWeight: 'bold', bgcolor: 'grey.50' }}>Account Type</TableCell>
                <TableCell sx={{ fontWeight: 'bold', bgcolor: 'grey.50' }}>Admin</TableCell>
                <TableCell sx={{ fontWeight: 'bold', bgcolor: 'grey.50' }}>Institution</TableCell>
                <TableCell sx={{ fontWeight: 'bold', bgcolor: 'grey.50' }}>Created</TableCell>
                <TableCell sx={{ fontWeight: 'bold', bgcolor: 'grey.50' }}>Last Sign In</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold', bgcolor: 'grey.50' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id} hover>
                  <TableCell sx={{ minWidth: 200 }}>
                    <Box>
                      <Typography variant="body2" fontWeight="bold" sx={{ wordBreak: 'break-word' }}>
                        {user.full_name || 'No name'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ wordBreak: 'break-all' }}>
                        {user.email || user.username || 'No email available'}
                      </Typography>
                      {user.username && user.username !== user.email && (
                        <Typography variant="caption" color="text.secondary" display="block" sx={{ wordBreak: 'break-word' }}>
                          @{user.username}
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell sx={{ minWidth: 120 }}>
                    {getAccountTypeChip(user.accounttype)}
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    {user.is_admin === true ? (
                      <Chip
                        label="Admin"
                        color="error"
                        size="small"
                        icon={<SecurityIcon />}
                        variant="outlined"
                      />
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        User
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell sx={{ minWidth: 150 }}>
                    <Typography variant="body2" sx={{ wordBreak: 'break-word' }}>
                      {user.institution || 'Not specified'}
                    </Typography>
                    {user.country && (
                      <Typography variant="caption" color="text.secondary" display="block">
                        {user.country}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    <Typography variant="body2">
                      {formatDate(user.created_at)}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    <Typography variant="body2">
                      {formatDate(user.last_sign_in_at)}
                    </Typography>
                  </TableCell>
                  <TableCell align="center" sx={{ minWidth: 80 }}>
                    <Tooltip title="Edit User">
                      <IconButton
                        size="small"
                        onClick={() => handleEditUser(user)}
                        color="primary"
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={(_, newPage) => setPage(newPage)}
            color="primary"
          />
        </Box>
      )}

      {/* Edit User Dialog */}
      <EditUserDialog
        open={editDialogOpen}
        user={selectedUser}
        onClose={() => {
          setEditDialogOpen(false);
          setSelectedUser(null);
        }}
        onSave={handleSaveUser}
      />
    </Box>
  );
};

export default UserManagement;
