import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  FormControlLabel,
  Checkbox,
  TextField,
  Divider,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Chip,
  FormLabel,
  RadioGroup,
  Radio,
  FormGroup,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Slider,
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  Timeline as TimelineIcon,
  Help as HelpIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Info as InfoIcon,
  TableChart as TableChartIcon,
  Score as ScoreIcon,
  Analytics as AnalyticsIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  ExpandMore as ExpandMoreIcon,
  AccountTree as AccountTreeIcon,
  TrendingUp as TrendingUpIcon,
  Build as BuildIcon,
  Psychology as PsychologyIcon,
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType, Column } from '../../../types';
import {
  confirmatoryFactorAnalysisService,
  CFAResults,
  CFAModelSpecification,
  CFAData,
  FitIndices,
  PathCoefficient,
  ModificationIndex
} from '../../../utils/services/confirmatoryFactorAnalysisService';
import Plot from 'react-plotly.js';
import { Data, Layout } from 'plotly.js';

interface Factor {
  id: string;
  name: string;
  indicators: string[];
}

interface ModelConstraints {
  allowCorrelatedErrors: boolean;
  allowFactorCorrelations: boolean;
  constrainVariances: boolean;
  meanStructure: boolean;
}

interface DisplayOptions {
  showStandardized: boolean;
  showUnstandardized: boolean;
  showStandardErrors: boolean;
  showPValues: boolean;
  showModificationIndices: boolean;
  showResidualCorrelations: boolean;
  significanceLevel: number;
}

interface ExportOptions {
  summary: boolean;
  fitIndices: boolean;
  pathCoefficients: boolean;
  modificationIndices: boolean;
  residualCorrelations: boolean;
  modelSpecification: boolean;
}

const ConfirmatoryFactorAnalysis: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();

  // State for analysis configuration
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [factors, setFactors] = useState<Factor[]>([]);
  const [estimationMethod, setEstimationMethod] = useState<string>('ml');
  const [modelConstraints, setModelConstraints] = useState<ModelConstraints>({
    allowCorrelatedErrors: false,
    allowFactorCorrelations: true,
    constrainVariances: false,
    meanStructure: false,
  });

  // State for display options
  const [displayOptions, setDisplayOptions] = useState<DisplayOptions>({
    showStandardized: true,
    showUnstandardized: false,
    showStandardErrors: true,
    showPValues: true,
    showModificationIndices: true,
    showResidualCorrelations: false,
    significanceLevel: 0.05,
  });

  // State for results and UI
  const [loading, setLoading] = useState<boolean>(false);
  const [initializingPython, setInitializingPython] = useState<boolean>(false);
  const [pythonReady, setPythonReady] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [cfaResults, setCfaResults] = useState<CFAResults | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);
  const [showModelSpecDialog, setShowModelSpecDialog] = useState<boolean>(false);
  const [showExportDialog, setShowExportDialog] = useState<boolean>(false);
  const [showPathDiagramDialog, setShowPathDiagramDialog] = useState<boolean>(false);

  // State for draggable element positions
  const [elementPositions, setElementPositions] = useState<{
    factors: { [key: string]: { x: number; y: number } };
    indicators: { [key: string]: { x: number; y: number } };
    errors: { [key: string]: { x: number; y: number } };
  }>({
    factors: {},
    indicators: {},
    errors: {}
  });

  const [isDragging, setIsDragging] = useState(false);
  const [draggedElement, setDraggedElement] = useState<{
    type: 'factor' | 'indicator' | 'error';
    id: string;
    traceIndex: number;
  } | null>(null);
  const [dragOffset, setDragOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // Drag and drop handlers
  const handleElementDrag = useCallback((elementType: 'factor' | 'indicator' | 'error', elementId: string, newX: number, newY: number) => {
    // Constrain to diagram boundaries
    const constrainedX = Math.max(0.05, Math.min(0.95, newX));
    const constrainedY = Math.max(0.1, Math.min(0.9, newY));

    setElementPositions(prev => ({
      ...prev,
      [elementType === 'factor' ? 'factors' : elementType === 'indicator' ? 'indicators' : 'errors']: {
        ...prev[elementType === 'factor' ? 'factors' : elementType === 'indicator' ? 'indicators' : 'errors'],
        [elementId]: { x: constrainedX, y: constrainedY }
      }
    }));
  }, []);

  // Plot reference for manual updates
  const plotRef = useRef<any>(null);

  // Handle mouse down on plot elements
  const handlePlotClick = useCallback((data: any) => {
    if (data.points && data.points.length > 0) {
      const point = data.points[0];
      if (point.customdata && typeof point.customdata === 'string') {
        const [type, id] = point.customdata.split(':');
        setDraggedElement({
          type: type as 'factor' | 'indicator' | 'error',
          id,
          traceIndex: point.curveNumber
        });
        setIsDragging(true);
        document.body.style.cursor = 'grabbing';

        // Add mouse move listener to document
        const handleMouseMove = (e: MouseEvent) => {
          if (plotRef.current) {
            const plotDiv = plotRef.current.el;
            const rect = plotDiv.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width;
            const y = 1 - (e.clientY - rect.top) / rect.height; // Invert Y axis

            // Update element position
            handleElementDrag(type as 'factor' | 'indicator' | 'error', id, x, y);
          }
        };

        const handleMouseUp = () => {
          setIsDragging(false);
          setDraggedElement(null);
          document.body.style.cursor = 'default';
          document.removeEventListener('mousemove', handleMouseMove);
          document.removeEventListener('mouseup', handleMouseUp);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
      }
    }
  }, [handleElementDrag]);

  // Handle hover for cursor changes
  const handlePlotHover = useCallback((data: any) => {
    if (!isDragging && data.points && data.points.length > 0) {
      const point = data.points[0];
      if (point.customdata && typeof point.customdata === 'string') {
        document.body.style.cursor = 'grab';
      }
    }
  }, [isDragging]);

  // Handle unhover
  const handlePlotUnhover = useCallback(() => {
    if (!isDragging) {
      document.body.style.cursor = 'default';
    }
  }, [isDragging]);

  const resetElementPositions = useCallback(() => {
    setElementPositions({
      factors: {},
      indicators: {},
      errors: {}
    });
  }, []);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    summary: true,
    fitIndices: true,
    pathCoefficients: true,
    modificationIndices: true,
    residualCorrelations: false,
    modelSpecification: true,
  });

  // Initialize Python environment
  const initializePython = useCallback(async () => {
    if (pythonReady || initializingPython) return;

    setInitializingPython(true);
    try {
      await confirmatoryFactorAnalysisService.initialize();
      setPythonReady(true);
    } catch (error) {
      console.error('Failed to initialize Python environment:', error);
      setError('Failed to initialize Python environment. Please refresh the page and try again.');
    } finally {
      setInitializingPython(false);
    }
  }, [pythonReady, initializingPython]);

  // Initialize Python on component mount
  useEffect(() => {
    initializePython();
  }, [initializePython]);

  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('cfa_results');
    if (savedResults) {
      try {
        const parsedResults = JSON.parse(savedResults);
        setCfaResults(parsedResults);
      } catch (error) {
        console.error('Error parsing saved CFA results:', error);
        localStorage.removeItem('cfa_results');
      }
    }

    const savedModel = localStorage.getItem('cfa_model');
    if (savedModel) {
      try {
        const parsedModel = JSON.parse(savedModel);
        setFactors(parsedModel);
      } catch (error) {
        console.error('Error parsing saved CFA model:', error);
        localStorage.removeItem('cfa_model');
      }
    }
  }, []);

  // Get numeric columns for analysis
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setFactors([]);
    setCfaResults(null);
    localStorage.removeItem('cfa_results');
    localStorage.removeItem('cfa_model');

    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };

  // Handle estimation method change
  const handleEstimationMethodChange = (event: SelectChangeEvent<string>) => {
    setEstimationMethod(event.target.value);
    setCfaResults(null);
    localStorage.removeItem('cfa_results');
  };

  // Handle model constraint change
  const handleModelConstraintChange = (constraint: keyof ModelConstraints) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setModelConstraints(prev => ({
      ...prev,
      [constraint]: event.target.checked
    }));
    setCfaResults(null);
    localStorage.removeItem('cfa_results');
  };

  // Handle display option change
  const handleDisplayOptionChange = (option: keyof DisplayOptions) => (
    event: React.ChangeEvent<HTMLInputElement> | Event,
    checked?: boolean | number
  ) => {
    if (option === 'significanceLevel') {
      const value = typeof checked === 'number' ? checked : 0.05;
      setDisplayOptions(prev => ({
        ...prev,
        significanceLevel: value
      }));
    } else {
      const isChecked = typeof checked === 'boolean' ? checked : (event.target as HTMLInputElement).checked;
      setDisplayOptions(prev => ({
        ...prev,
        [option]: isChecked
      }));
    }
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Add new factor
  const addFactor = () => {
    const newFactor: Factor = {
      id: `factor_${Date.now()}`,
      name: `Factor ${factors.length + 1}`,
      indicators: []
    };
    setFactors(prev => [...prev, newFactor]);
  };

  // Remove factor
  const removeFactor = (factorId: string) => {
    setFactors(prev => prev.filter(f => f.id !== factorId));
    setCfaResults(null);
    localStorage.removeItem('cfa_results');
  };

  // Update factor name
  const updateFactorName = (factorId: string, name: string) => {
    setFactors(prev => prev.map(f => 
      f.id === factorId ? { ...f, name } : f
    ));
    localStorage.setItem('cfa_model', JSON.stringify(factors));
  };

  // Add indicator to factor
  const addIndicatorToFactor = (factorId: string, indicatorId: string) => {
    // Remove indicator from other factors first
    setFactors(prev => prev.map(f => ({
      ...f,
      indicators: f.indicators.filter(ind => ind !== indicatorId)
    })));

    // Add to selected factor
    setFactors(prev => prev.map(f => 
      f.id === factorId 
        ? { ...f, indicators: [...f.indicators, indicatorId] }
        : f
    ));
    
    setCfaResults(null);
    localStorage.removeItem('cfa_results');
    localStorage.setItem('cfa_model', JSON.stringify(factors));
  };

  // Remove indicator from factor
  const removeIndicatorFromFactor = (factorId: string, indicatorId: string) => {
    setFactors(prev => prev.map(f => 
      f.id === factorId 
        ? { ...f, indicators: f.indicators.filter(ind => ind !== indicatorId) }
        : f
    ));
    setCfaResults(null);
    localStorage.removeItem('cfa_results');
    localStorage.setItem('cfa_model', JSON.stringify(factors));
  };

  // Check if model is valid
  const isModelValid = () => {
    if (factors.length === 0) return false;
    
    // Each factor should have at least 2 indicators
    for (const factor of factors) {
      if (factor.indicators.length < 2) return false;
    }

    // Check identification: need at least 3 indicators per factor or multiple factors
    if (factors.length === 1 && factors[0].indicators.length < 3) return false;

    return true;
  };

  // Run confirmatory factor analysis
  const runCFA = async () => {
    if (!currentDataset || !isModelValid()) {
      setError('Please specify a valid model. Each factor needs at least 2 indicators, and single-factor models need at least 3 indicators.');
      return;
    }

    if (!pythonReady) {
      setError('Python environment is not ready. Please wait for initialization to complete.');
      return;
    }

    setLoading(true);
    setError(null);
    setCfaResults(null);

    try {
      // Prepare data for analysis
      const allIndicators = factors.flatMap(f => f.indicators);
      const selectedColumns = allIndicators
        .map(id => currentDataset.columns.find(col => col.id === id))
        .filter(Boolean) as Column[];

      const variables: { [key: string]: number[] } = {};

      // Extract data for each variable
      selectedColumns.forEach((col) => {
        const values: number[] = [];
        currentDataset.data.forEach(row => {
          const value = row[col.name];
          if (typeof value === 'number' && !isNaN(value)) {
            values.push(value);
          }
        });
        variables[col.name] = values;
      });

      // Check if all variables have the same length
      const lengths = Object.values(variables).map(v => v.length);
      if (new Set(lengths).size > 1) {
        throw new Error('Variables have different numbers of valid observations.');
      }

      if (lengths[0] < 50) {
        throw new Error('Insufficient data for CFA. Need at least 50 valid observations.');
      }

      // Prepare model specification
      const modelSpec: CFAModelSpecification = {
        factors: factors.map(f => ({
          name: f.name,
          indicators: f.indicators.map(ind => {
            const col = selectedColumns.find(c => c.id === ind);
            return col ? col.name : ind;
          })
        })),
        constraints: modelConstraints
      };

      // Prepare analysis data
      const analysisData: CFAData = {
        variables,
        variable_names: selectedColumns.map(col => col.name),
        model_specification: modelSpec,
        estimation_method: estimationMethod,
        significance_level: displayOptions.significanceLevel
      };

      // Run confirmatory factor analysis
      const results = await confirmatoryFactorAnalysisService.runCFA(analysisData);

      setCfaResults(results);
      localStorage.setItem('cfa_results', JSON.stringify(results));

    } catch (err) {
      console.error('CFA error:', err);
      setError(`Error in confirmatory factor analysis: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };

  // Export results
  const exportResults = () => {
    if (!cfaResults) return;

    let content = 'CONFIRMATORY FACTOR ANALYSIS RESULTS\n';
    content += '=====================================\n\n';

    if (exportOptions.summary) {
      content += 'SUMMARY\n';
      content += '-------\n';
      content += `Estimation Method: ${cfaResults.estimation_method.toUpperCase()}\n`;
      content += `Number of Factors: ${cfaResults.n_factors}\n`;
      content += `Number of Observed Variables: ${cfaResults.n_observed_variables}\n`;
      content += `Number of Parameters: ${cfaResults.n_parameters}\n`;
      content += `Number of Observations: ${cfaResults.n_observations}\n`;
      content += `Degrees of Freedom: ${cfaResults.degrees_of_freedom}\n`;
      content += `Converged: ${cfaResults.converged ? 'Yes' : 'No'}\n`;
      content += `Iterations: ${cfaResults.iterations}\n\n`;
    }

    if (exportOptions.modelSpecification) {
      content += 'MODEL SPECIFICATION\n';
      content += '-------------------\n';
      factors.forEach(factor => {
        content += `${factor.name}:\n`;
        factor.indicators.forEach(ind => {
          const col = numericColumns.find(c => c.id === ind);
          content += `  - ${col?.name || ind}\n`;
        });
      });
      content += '\n';
    }

    if (exportOptions.fitIndices) {
      content += 'FIT INDICES\n';
      content += '-----------\n';
      content += `Chi-square: ${cfaResults.fit_indices.chi_square.toFixed(3)} (df = ${cfaResults.fit_indices.degrees_of_freedom})\n`;
      content += `p-value: ${cfaResults.fit_indices.p_value.toFixed(4)}\n`;
      content += `CFI: ${cfaResults.fit_indices.cfi.toFixed(3)}\n`;
      content += `TLI: ${cfaResults.fit_indices.tli.toFixed(3)}\n`;
      content += `RMSEA: ${cfaResults.fit_indices.rmsea.toFixed(3)} [${cfaResults.fit_indices.rmsea_ci_lower.toFixed(3)}, ${cfaResults.fit_indices.rmsea_ci_upper.toFixed(3)}]\n`;
      content += `SRMR: ${cfaResults.fit_indices.srmr.toFixed(3)}\n`;
      content += `AIC: ${cfaResults.fit_indices.aic.toFixed(1)}\n`;
      content += `BIC: ${cfaResults.fit_indices.bic.toFixed(1)}\n\n`;
    }

    if (exportOptions.pathCoefficients) {
      content += 'FACTOR LOADINGS\n';
      content += '---------------\n';
      cfaResults.factor_loadings.forEach(loading => {
        content += `${loading.from} -> ${loading.to}: `;
        if (displayOptions.showStandardized) {
          content += `β = ${loading.standardized_estimate.toFixed(3)}`;
        }
        if (displayOptions.showUnstandardized) {
          content += ` (B = ${loading.unstandardized_estimate.toFixed(3)})`;
        }
        if (displayOptions.showStandardErrors) {
          content += ` SE = ${loading.standard_error.toFixed(3)}`;
        }
        if (displayOptions.showPValues) {
          content += ` p = ${loading.p_value < 0.001 ? '< 0.001' : loading.p_value.toFixed(4)}`;
        }
        content += '\n';
      });
      content += '\n';

      if (cfaResults.factor_correlations && cfaResults.factor_correlations.length > 0) {
        content += 'FACTOR CORRELATIONS\n';
        content += '-------------------\n';
        cfaResults.factor_correlations.forEach(corr => {
          content += `${corr.from} <-> ${corr.to}: r = ${corr.standardized_estimate.toFixed(3)}`;
          if (displayOptions.showPValues) {
            content += ` p = ${corr.p_value < 0.001 ? '< 0.001' : corr.p_value.toFixed(4)}`;
          }
          content += '\n';
        });
        content += '\n';
      }
    }

    if (exportOptions.modificationIndices && cfaResults.modification_indices) {
      content += 'MODIFICATION INDICES\n';
      content += '--------------------\n';
      cfaResults.modification_indices
        .filter(mi => mi.modification_index > 3.84) // Chi-square critical value for p < 0.05
        .forEach(mi => {
          content += `${mi.parameter}: MI = ${mi.modification_index.toFixed(3)}, EPC = ${mi.expected_parameter_change.toFixed(3)}\n`;
        });
      content += '\n';
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'cfa_results.txt';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    setShowExportDialog(false);
  };

  // Get fit interpretation
  const getFitInterpretation = () => {
    if (!cfaResults) return '';

    const fit = cfaResults.fit_indices;
    let interpretation = '';

    // Chi-square test
    interpretation += `Chi-square Test: χ²(${cfaResults.fit_indices.degrees_of_freedom}) = ${fit.chi_square.toFixed(2)}, p = ${fit.p_value.toFixed(4)}\n`;
    if (fit.p_value > 0.05) {
      interpretation += 'The chi-square test is non-significant, suggesting good model fit.\n';
    } else {
      interpretation += 'The chi-square test is significant, but this may be due to large sample size. Consider other fit indices.\n';
    }

    // CFI
    interpretation += `\nComparative Fit Index (CFI): ${fit.cfi.toFixed(3)}\n`;
    if (fit.cfi >= 0.95) {
      interpretation += 'CFI indicates excellent fit.\n';
    } else if (fit.cfi >= 0.90) {
      interpretation += 'CFI indicates acceptable fit.\n';
    } else {
      interpretation += 'CFI indicates poor fit. Consider model modifications.\n';
    }

    // TLI
    interpretation += `\nTucker-Lewis Index (TLI): ${fit.tli.toFixed(3)}\n`;
    if (fit.tli >= 0.95) {
      interpretation += 'TLI indicates excellent fit.\n';
    } else if (fit.tli >= 0.90) {
      interpretation += 'TLI indicates acceptable fit.\n';
    } else {
      interpretation += 'TLI indicates poor fit.\n';
    }

    // RMSEA
    interpretation += `\nRoot Mean Square Error of Approximation (RMSEA): ${fit.rmsea.toFixed(3)} [${fit.rmsea_ci_lower.toFixed(3)}, ${fit.rmsea_ci_upper.toFixed(3)}]\n`;
    if (fit.rmsea <= 0.05) {
      interpretation += 'RMSEA indicates excellent fit.\n';
    } else if (fit.rmsea <= 0.08) {
      interpretation += 'RMSEA indicates acceptable fit.\n';
    } else if (fit.rmsea <= 0.10) {
      interpretation += 'RMSEA indicates mediocre fit.\n';
    } else {
      interpretation += 'RMSEA indicates poor fit.\n';
    }

    // SRMR
    interpretation += `\nStandardized Root Mean Square Residual (SRMR): ${fit.srmr.toFixed(3)}\n`;
    if (fit.srmr <= 0.05) {
      interpretation += 'SRMR indicates excellent fit.\n';
    } else if (fit.srmr <= 0.08) {
      interpretation += 'SRMR indicates acceptable fit.\n';
    } else {
      interpretation += 'SRMR indicates poor fit.\n';
    }

    // Overall assessment
    interpretation += '\nOverall Model Fit Assessment:\n';
    const goodFitCount = [
      fit.cfi >= 0.95,
      fit.tli >= 0.95,
      fit.rmsea <= 0.05,
      fit.srmr <= 0.05
    ].filter(Boolean).length;

    const acceptableFitCount = [
      fit.cfi >= 0.90,
      fit.tli >= 0.90,
      fit.rmsea <= 0.08,
      fit.srmr <= 0.08
    ].filter(Boolean).length;

    if (goodFitCount >= 3) {
      interpretation += 'The model demonstrates excellent fit to the data.\n';
    } else if (acceptableFitCount >= 3) {
      interpretation += 'The model demonstrates acceptable fit to the data.\n';
    } else {
      interpretation += 'The model fit is questionable. Consider examining modification indices for potential improvements.\n';
    }

    return interpretation;
  };

  // Generate path diagram data with completely redesigned layout
  const getPathDiagramData = () => {
    if (!cfaResults) return null;

    const traces: Data[] = [];
    const annotations: any[] = [];

    // Calculate positions for factors and indicators with much better spacing
    const nFactors = factors.length;
    const factorPositions: { [key: string]: { x: number; y: number } } = {};
    const indicatorPositions: { [key: string]: { x: number; y: number } } = {};

    // Calculate layout dimensions based on content
    const totalIndicators = factors.reduce((sum, factor) => sum + factor.indicators.length, 0);
    const maxIndicatorsPerFactor = Math.max(...factors.map(f => f.indicators.length));

    // Use a wider canvas to accommodate all elements without overlap (increased for larger elements)
    const canvasWidth = Math.max(1400, totalIndicators * 100); // Increased dynamic width for larger elements
    const canvasHeight = 600;

    // Position factors horizontally with generous spacing
    const factorY = 0.8;
    const factorSpacing = Math.min(200, canvasWidth * 0.8 / Math.max(nFactors - 1, 1));
    const factorStartX = (canvasWidth - (nFactors - 1) * factorSpacing) / 2;

    factors.forEach((factor, index) => {
      const defaultX = nFactors === 1 ? canvasWidth / 2 : factorStartX + index * factorSpacing;
      const defaultPos = { x: defaultX / canvasWidth, y: factorY };

      // Use custom position if available, otherwise use default
      factorPositions[factor.name] = elementPositions.factors[factor.name] || defaultPos;
    });

    // Position indicators with no overlap - use absolute positioning
    let allIndicators: { name: string; factorName: string; x: number; y: number }[] = [];
    let currentX = 100; // Start position

    factors.forEach((factor) => {
      const factorPos = factorPositions[factor.name];
      const nIndicators = factor.indicators.length;

      // Calculate the total width needed for this factor's indicators (increased for larger rectangles)
      const indicatorSpacing = 140; // Increased spacing to accommodate larger rectangles
      const totalWidth = (nIndicators - 1) * indicatorSpacing;
      const startX = (factorPos.x * canvasWidth) - totalWidth / 2;

      factor.indicators.forEach((indicatorId, index) => {
        const col = numericColumns.find(c => c.id === indicatorId);
        const indicatorName = col?.name || indicatorId;

        const defaultX = nIndicators === 1 ? factorPos.x * canvasWidth : startX + index * indicatorSpacing;
        const defaultY = 0.4 * canvasHeight; // Fixed Y position for all indicators
        const defaultPos = { x: defaultX / canvasWidth, y: defaultY / canvasHeight };

        // Use custom position if available, otherwise use default
        const position = elementPositions.indicators[indicatorName] || defaultPos;

        indicatorPositions[indicatorName] = position;
        allIndicators.push({
          name: indicatorName,
          factorName: factor.name,
          x: position.x,
          y: position.y
        });
      });
    });

    // Draw latent factors (circles) with drag functionality
    const factorNames = Object.keys(factorPositions);

    // Create individual traces for each factor to enable individual dragging
    factorNames.forEach((factorName) => {
      const pos = factorPositions[factorName];

      // Smart text handling for factor names
      let displayName = factorName;
      let fontSize = 14;

      // Adjust font size based on name length to fit within circle
      if (factorName.length > 10) {
        displayName = factorName.substring(0, 10) + '...';
        fontSize = 12;
      } else if (factorName.length > 6) {
        fontSize = 13;
      }

      // Add factor circle
      traces.push({
        x: [pos.x],
        y: [pos.y],
        mode: 'markers',
        type: 'scatter',
        marker: {
          size: 90, // Slightly larger for better text accommodation
          color: isDragging ? 'rgba(33, 150, 243, 0.3)' : 'rgba(33, 150, 243, 0.1)',
          line: { color: '#1976d2', width: 3 },
          symbol: 'circle'
        },
        hoverinfo: 'text',
        hovertext: `Latent Factor: ${factorName} (Drag to move)`,
        name: `Factor_${factorName}`,
        showlegend: false,
        customdata: [`factor:${factorName}`]
      } as Data);

      // Add factor name text on top of circle
      traces.push({
        x: [pos.x],
        y: [pos.y],
        mode: 'text',
        type: 'scatter',
        text: [displayName],
        textposition: 'middle center',
        textfont: {
          size: fontSize,
          color: '#1976d2',
          family: 'Arial, sans-serif'
        },
        hoverinfo: 'skip',
        showlegend: false
      } as Data);
    });

    // Factor variance indicators removed as requested

    // Draw observed variables (rectangles) with individual drag functionality and better text handling
    allIndicators.forEach((indicator) => {
      // Smart text handling for variable names
      let displayName = indicator.name;
      let fontSize = 10;

      // Adjust font size and text based on name length
      if (indicator.name.length > 12) {
        displayName = indicator.name.substring(0, 12) + '...';
        fontSize = 9;
      } else if (indicator.name.length > 8) {
        fontSize = 9;
      }

      // Add rectangle marker
      traces.push({
        x: [indicator.x],
        y: [indicator.y],
        mode: 'markers',
        type: 'scatter',
        marker: {
          size: 60, // Increased size for better visibility and text accommodation
          color: isDragging ? 'rgba(76, 175, 80, 0.3)' : 'rgba(76, 175, 80, 0.15)',
          line: { color: '#388e3c', width: 2 },
          symbol: 'square'
        },
        hoverinfo: 'text',
        hovertext: `Observed Variable: ${indicator.name} (Drag to move)`,
        name: `Indicator_${indicator.name}`,
        showlegend: false,
        customdata: [`indicator:${indicator.name}`]
      } as Data);

      // Add text label on top of rectangle
      traces.push({
        x: [indicator.x],
        y: [indicator.y],
        mode: 'text',
        type: 'scatter',
        text: [displayName],
        textposition: 'middle center',
        textfont: {
          size: fontSize,
          color: '#2e7d32', // Darker green for better contrast
          family: 'Arial, sans-serif'
        },
        hoverinfo: 'skip',
        showlegend: false
      } as Data);
    });

    // Draw factor loadings (arrows from factors to indicators)
    const loadingLines: { x: number[]; y: number[]; loading: number; from: string; to: string }[] = [];

    cfaResults.factor_loadings.forEach(loading => {
      const factorPos = factorPositions[loading.from];
      const indicatorPos = indicatorPositions[loading.to];

      if (factorPos && indicatorPos) {
        loadingLines.push({
          x: [factorPos.x, indicatorPos.x],
          y: [factorPos.y, indicatorPos.y],
          loading: loading.standardized_estimate,
          from: loading.from,
          to: loading.to
        });
      }
    });

    // Add loading paths with cleaner arrows
    loadingLines.forEach((line, index) => {
      const lineWidth = Math.abs(line.loading) * 3 + 1.5; // Scale line width by loading magnitude
      const lineColor = line.loading > 0 ? '#1976d2' : '#d32f2f'; // Material-UI blue/red

      // Main loading line
      traces.push({
        x: line.x,
        y: line.y,
        mode: 'lines',
        type: 'scatter',
        line: {
          color: lineColor,
          width: lineWidth
        },
        hoverinfo: 'text',
        hovertext: `${line.from} → ${line.to}<br>Loading: ${line.loading.toFixed(3)}`,
        showlegend: false,
        name: `Loading ${index + 1}`
      } as Data);

      // Add arrow head at the indicator end
      const dx = line.x[1] - line.x[0];
      const dy = line.y[1] - line.y[0];
      const length = Math.sqrt(dx * dx + dy * dy);
      const unitX = dx / length;
      const unitY = dy / length;

      // Position arrow closer to indicator
      const arrowX = line.x[1] - 0.03 * unitX;
      const arrowY = line.y[1] - 0.03 * unitY;

      annotations.push({
        x: arrowX,
        y: arrowY,
        text: '',
        showarrow: true,
        arrowhead: 2,
        arrowsize: 1.2,
        arrowwidth: 2,
        arrowcolor: lineColor,
        ax: arrowX - 0.02 * unitX,
        ay: arrowY - 0.02 * unitY
      });

      // Add loading value annotation
      const midX = (line.x[0] + line.x[1]) / 2;
      const midY = (line.y[0] + line.y[1]) / 2;

      // Position text to the side of the line
      const perpX = -unitY * 0.025; // Perpendicular offset
      const perpY = unitX * 0.025;

      annotations.push({
        x: midX + perpX,
        y: midY + perpY,
        text: line.loading.toFixed(2),
        showarrow: false,
        font: { size: 10, color: lineColor, family: 'Arial, sans-serif' },
        bgcolor: 'rgba(255,255,255,0.9)',
        bordercolor: lineColor,
        borderwidth: 1,
        borderpad: 1
      });
    });

    // Draw factor correlations (simplified curved lines between factors)
    if (cfaResults.factor_correlations && cfaResults.factor_correlations.length > 0) {
      cfaResults.factor_correlations.forEach(corr => {
        const pos1 = factorPositions[corr.from];
        const pos2 = factorPositions[corr.to];

        if (pos1 && pos2) {
          // Create a simple curved line above the factors
          const midX = (pos1.x + pos2.x) / 2;
          const midY = Math.max(pos1.y, pos2.y) + 0.08; // Smaller curve above factors

          // Simple 3-point curve
          const curveX = [pos1.x, midX, pos2.x];
          const curveY = [pos1.y, midY, pos2.y];

          const lineWidth = Math.abs(corr.standardized_estimate) * 3 + 1;
          const lineColor = corr.standardized_estimate > 0 ? '#7b1fa2' : '#f57c00'; // Purple/Orange

          traces.push({
            x: curveX,
            y: curveY,
            mode: 'lines',
            type: 'scatter',
            line: {
              color: lineColor,
              width: lineWidth,
              dash: 'dash'
            },
            hoverinfo: 'text',
            hovertext: `${corr.from} ↔ ${corr.to}<br>Correlation: ${corr.standardized_estimate.toFixed(3)}`,
            showlegend: false
          } as Data);

          // Add correlation value annotation at the peak
          annotations.push({
            x: midX,
            y: midY + 0.01,
            text: corr.standardized_estimate.toFixed(2),
            showarrow: false,
            font: { size: 10, color: lineColor, family: 'Arial, sans-serif' },
            bgcolor: 'rgba(255,255,255,0.8)',
            bordercolor: lineColor,
            borderwidth: 1,
            borderpad: 1
          });
        }
      });
    }

    // Draw error terms with drag functionality
    allIndicators.forEach((indicator, index) => {
      const errorId = `error_${indicator.name}`;
      const defaultErrorPos = { x: indicator.x, y: indicator.y - 0.15 }; // Moved further down for larger rectangles
      const errorPos = elementPositions.errors[errorId] || defaultErrorPos;

      // Add error term circle
      traces.push({
        x: [errorPos.x],
        y: [errorPos.y],
        mode: 'markers',
        type: 'scatter',
        marker: {
          size: 25, // Slightly larger for better visibility
          color: isDragging ? 'rgba(244, 67, 54, 0.3)' : 'rgba(244, 67, 54, 0.15)',
          line: { color: '#d32f2f', width: 1.5 },
          symbol: 'circle'
        },
        hoverinfo: 'text',
        hovertext: `Error term for ${indicator.name} (Drag to move)`,
        showlegend: false,
        customdata: [`error:${errorId}`]
      } as Data);

      // Add error term label
      traces.push({
        x: [errorPos.x],
        y: [errorPos.y],
        mode: 'text',
        type: 'scatter',
        text: [`e${index + 1}`],
        textposition: 'middle center',
        textfont: { size: 9, color: '#d32f2f', family: 'Arial, sans-serif' },
        hoverinfo: 'skip',
        showlegend: false
      } as Data);

      // Draw straight line from indicator to error term
      traces.push({
        x: [indicator.x, errorPos.x],
        y: [indicator.y, errorPos.y],
        mode: 'lines',
        type: 'scatter',
        line: {
          color: '#d32f2f',
          width: 1.5
        },
        showlegend: false,
        hoverinfo: 'skip'
      } as Data);
    });

    // Calculate dynamic dimensions based on content
    const dynamicWidth = Math.max(1000, totalIndicators * 100);
    const dynamicHeight = 600;

    return {
      data: traces,
      layout: {
        title: {
          text: 'Confirmatory Factor Analysis Path Diagram',
          font: { size: 16, family: 'Arial, sans-serif', color: '#333' },
          x: 0.5,
          xanchor: 'center'
        },
        showlegend: false,
        xaxis: {
          showgrid: false,
          zeroline: false,
          showticklabels: false,
          range: [0, 1],
          fixedrange: false // Allow scrolling/panning
        },
        yaxis: {
          showgrid: false,
          zeroline: false,
          showticklabels: false,
          range: [0.1, 0.9],
          fixedrange: false // Allow scrolling/panning
        },
        height: dynamicHeight,
        width: dynamicWidth,
        annotations: annotations,
        plot_bgcolor: 'white',
        paper_bgcolor: 'white',
        margin: { l: 60, r: 60, t: 80, b: 60 },
        font: { family: 'Arial, sans-serif' },
        hovermode: 'closest',
        dragmode: 'pan', // Allow panning when not dragging elements
        staticPlot: false
      } as Partial<Layout>
    };
  };

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Confirmatory Factor Analysis
      </Typography>

      {initializingPython && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Box>
            <Typography variant="body2" gutterBottom>
              Initializing Python environment for statistical analysis...
            </Typography>
            <LinearProgress sx={{ mt: 1 }} />
          </Box>
        </Alert>
      )}

      {!pythonReady && !initializingPython && (
        <Alert severity="warning" sx={{ mb: 2 }} action={
          <Button color="inherit" size="small" onClick={initializePython} startIcon={<RefreshIcon />}>
            Retry
          </Button>
        }>
          Python environment not ready. CFA requires Python libraries to be loaded.
        </Alert>
      )}

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Dataset Selection & Model Specification
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={8}>
            <Box display="flex" alignItems="center" gap={2} mt={2}>
              <Typography variant="subtitle2">
                Model Structure ({factors.length} factors)
              </Typography>
              <Button
                size="small"
                startIcon={<AddIcon />}
                onClick={addFactor}
                disabled={!currentDataset}
              >
                Add Factor
              </Button>
              <Button
                size="small"
                startIcon={<AccountTreeIcon />}
                onClick={() => setShowModelSpecDialog(true)}
                disabled={!currentDataset}
              >
                Model Builder
              </Button>
            </Box>

            {factors.length > 0 && (
              <Box mt={2}>
                {factors.map(factor => (
                  <Card key={factor.id} variant="outlined" sx={{ mb: 1 }}>
                    <CardContent sx={{ py: 1 }}>
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Box display="flex" alignItems="center" gap={2} flex={1}>
                          <TextField
                            size="small"
                            label="Factor Name"
                            value={factor.name}
                            onChange={(e) => updateFactorName(factor.id, e.target.value)}
                            sx={{ width: 150 }}
                          />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Indicators ({factor.indicators.length}):
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                              {factor.indicators.map(ind => {
                                const col = numericColumns.find(c => c.id === ind);
                                return (
                                  <Chip
                                    key={ind}
                                    label={col?.name || ind}
                                    size="small"
                                    onDelete={() => removeIndicatorFromFactor(factor.id, ind)}
                                  />
                                );
                              })}
                            </Box>
                          </Box>
                        </Box>
                        <IconButton
                          size="small"
                          onClick={() => removeFactor(factor.id)}
                          color="error"
                        >
                          <RemoveIcon />
                        </IconButton>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            )}

            {!isModelValid() && factors.length > 0 && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                Invalid model specification. Each factor needs at least 2 indicators, and single-factor models need at least 3 indicators for identification.
              </Alert>
            )}
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        <Typography variant="subtitle1" gutterBottom>
          Analysis Configuration
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="estimation-method-label">Estimation Method</InputLabel>
              <Select
                labelId="estimation-method-label"
                id="estimation-method"
                value={estimationMethod}
                label="Estimation Method"
                onChange={handleEstimationMethodChange}
              >
                <MenuItem value="ml">Maximum Likelihood (ML)</MenuItem>
                <MenuItem value="gls">Generalized Least Squares (GLS)</MenuItem>
                <MenuItem value="wls">Weighted Least Squares (WLS)</MenuItem>
                <MenuItem value="mlr">Robust Maximum Likelihood (MLR)</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={8}>
            <FormControl component="fieldset" margin="normal">
              <FormLabel component="legend">Model Constraints</FormLabel>
              <FormGroup row>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={modelConstraints.allowFactorCorrelations}
                      onChange={handleModelConstraintChange('allowFactorCorrelations')}
                    />
                  }
                  label="Allow Factor Correlations"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={modelConstraints.allowCorrelatedErrors}
                      onChange={handleModelConstraintChange('allowCorrelatedErrors')}
                    />
                  }
                  label="Allow Correlated Errors"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={modelConstraints.meanStructure}
                      onChange={handleModelConstraintChange('meanStructure')}
                    />
                  }
                  label="Include Mean Structure"
                />
              </FormGroup>
            </FormControl>
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        <Typography variant="subtitle1" gutterBottom>
          Display Options
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showStandardized}
                    onChange={handleDisplayOptionChange('showStandardized')}
                  />
                }
                label="Show Standardized Estimates"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showUnstandardized}
                    onChange={handleDisplayOptionChange('showUnstandardized')}
                  />
                }
                label="Show Unstandardized Estimates"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showStandardErrors}
                    onChange={handleDisplayOptionChange('showStandardErrors')}
                  />
                }
                label="Show Standard Errors"
              />
            </FormGroup>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showPValues}
                    onChange={handleDisplayOptionChange('showPValues')}
                  />
                }
                label="Show p-values"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showModificationIndices}
                    onChange={handleDisplayOptionChange('showModificationIndices')}
                  />
                }
                label="Show Modification Indices"
              />
            </FormGroup>

            <Box mt={2}>
              <Typography gutterBottom>Significance Level: {displayOptions.significanceLevel}</Typography>
              <Slider
                value={displayOptions.significanceLevel}
                onChange={(_, value) => handleDisplayOptionChange('significanceLevel')(_, Array.isArray(value) ? value[0] : value)}
                min={0.01}
                max={0.10}
                step={0.01}
                marks={[
                  { value: 0.01, label: '0.01' },
                  { value: 0.05, label: '0.05' },
                  { value: 0.10, label: '0.10' }
                ]}
                valueLabelDisplay="auto"
              />
            </Box>
          </Grid>
        </Grid>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<PsychologyIcon />}
            onClick={runCFA}
            disabled={loading || !pythonReady || !isModelValid()}
          >
            {loading ? 'Running Analysis...' : 'Run Confirmatory Factor Analysis'}
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {cfaResults && !loading && (
        <>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="cfa results tabs">
                <Tab label="Summary" icon={<AssessmentIcon />} iconPosition="start" />
                <Tab label="Path Coefficients" icon={<TimelineIcon />} iconPosition="start" />
                <Tab label="Model Fit" icon={<TrendingUpIcon />} iconPosition="start" />
                <Tab label="Modification Indices" icon={<BuildIcon />} iconPosition="start" />
                <Tab label="Interpretation" icon={<InfoIcon />} iconPosition="start" />
              </Tabs>
            </Box>

            {tabValue === 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Analysis Summary
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Model Information
                        </Typography>

                        <TableContainer>
                          <Table size="small">
                            <TableBody>
                              <TableRow>
                                <TableCell>Estimation Method</TableCell>
                                <TableCell>{cfaResults.estimation_method.toUpperCase()}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Number of Factors</TableCell>
                                <TableCell>{cfaResults.n_factors}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Observed Variables</TableCell>
                                <TableCell>{cfaResults.n_observed_variables}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Parameters Estimated</TableCell>
                                <TableCell>{cfaResults.n_parameters}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Degrees of Freedom</TableCell>
                                <TableCell>{cfaResults.degrees_of_freedom}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Sample Size</TableCell>
                                <TableCell>{cfaResults.n_observations}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Converged</TableCell>
                                <TableCell>{cfaResults.converged ? 'Yes' : 'No'}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Iterations</TableCell>
                                <TableCell>{cfaResults.iterations}</TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Key Fit Indices
                        </Typography>

                        <Box mb={2}>
                          <Typography variant="body2" gutterBottom>
                            Comparative Fit Index (CFI)
                          </Typography>
                          <Typography variant="h5" color={
                            cfaResults.fit_indices.cfi >= 0.95 ? 'success.main' :
                            cfaResults.fit_indices.cfi >= 0.90 ? 'warning.main' : 'error.main'
                          }>
                            {cfaResults.fit_indices.cfi.toFixed(3)}
                          </Typography>
                        </Box>

                        <Box mb={2}>
                          <Typography variant="body2" gutterBottom>
                            RMSEA [90% CI]
                          </Typography>
                          <Typography variant="h5" color={
                            cfaResults.fit_indices.rmsea <= 0.05 ? 'success.main' :
                            cfaResults.fit_indices.rmsea <= 0.08 ? 'warning.main' : 'error.main'
                          }>
                            {cfaResults.fit_indices.rmsea.toFixed(3)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            [{cfaResults.fit_indices.rmsea_ci_lower.toFixed(3)}, {cfaResults.fit_indices.rmsea_ci_upper.toFixed(3)}]
                          </Typography>
                        </Box>

                        <Box>
                          <Typography variant="body2" gutterBottom>
                            SRMR
                          </Typography>
                          <Typography variant="h5" color={
                            cfaResults.fit_indices.srmr <= 0.05 ? 'success.main' :
                            cfaResults.fit_indices.srmr <= 0.08 ? 'warning.main' : 'error.main'
                          }>
                            {cfaResults.fit_indices.srmr.toFixed(3)}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Box mt={3}>
                  <Button
                    variant="outlined"
                    startIcon={<AccountTreeIcon />}
                    onClick={() => setShowPathDiagramDialog(true)}
                    sx={{ mr: 2 }}
                  >
                    View Path Diagram
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={() => setShowExportDialog(true)}
                  >
                    Export Results
                  </Button>
                </Box>
              </Box>
            )}

            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Path Coefficients
                </Typography>

                <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                  Factor Loadings
                </Typography>

                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Factor</TableCell>
                        <TableCell>Indicator</TableCell>
                        {displayOptions.showStandardized && <TableCell align="right">Std. Loading</TableCell>}
                        {displayOptions.showUnstandardized && <TableCell align="right">Unstd. Loading</TableCell>}
                        {displayOptions.showStandardErrors && <TableCell align="right">SE</TableCell>}
                        <TableCell align="right">z-score</TableCell>
                        {displayOptions.showPValues && <TableCell align="right">p-value</TableCell>}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {cfaResults.factor_loadings.map((loading, index) => (
                        <TableRow key={index}>
                          <TableCell>{loading.from}</TableCell>
                          <TableCell>{loading.to}</TableCell>
                          {displayOptions.showStandardized && (
                            <TableCell align="right" sx={{
                              fontWeight: Math.abs(loading.standardized_estimate) > 0.7 ? 'bold' : 'normal',
                              color: Math.abs(loading.standardized_estimate) > 0.7 ? 'primary.main' : 'text.primary'
                            }}>
                              {loading.standardized_estimate.toFixed(3)}
                            </TableCell>
                          )}
                          {displayOptions.showUnstandardized && (
                            <TableCell align="right">
                              {loading.unstandardized_estimate.toFixed(3)}
                            </TableCell>
                          )}
                          {displayOptions.showStandardErrors && (
                            <TableCell align="right">
                              {loading.standard_error.toFixed(3)}
                            </TableCell>
                          )}
                          <TableCell align="right">
                            {loading.z_score.toFixed(2)}
                          </TableCell>
                          {displayOptions.showPValues && (
                            <TableCell align="right" sx={{
                              color: loading.p_value < displayOptions.significanceLevel ? 'success.main' : 'text.primary'
                            }}>
                              {loading.p_value < 0.001 ? '< 0.001' : loading.p_value.toFixed(4)}
                            </TableCell>
                          )}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {cfaResults.factor_correlations && cfaResults.factor_correlations.length > 0 && (
                  <Box mt={3}>
                    <Typography variant="subtitle2" gutterBottom>
                      Factor Correlations
                    </Typography>

                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Factor 1</TableCell>
                            <TableCell>Factor 2</TableCell>
                            {displayOptions.showStandardized && <TableCell align="right">Correlation</TableCell>}
                            {displayOptions.showStandardErrors && <TableCell align="right">SE</TableCell>}
                            <TableCell align="right">z-score</TableCell>
                            {displayOptions.showPValues && <TableCell align="right">p-value</TableCell>}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {cfaResults.factor_correlations.map((corr, index) => (
                            <TableRow key={index}>
                              <TableCell>{corr.from}</TableCell>
                              <TableCell>{corr.to}</TableCell>
                              {displayOptions.showStandardized && (
                                <TableCell align="right" sx={{
                                  color: Math.abs(corr.standardized_estimate) > 0.5 ? 'warning.main' : 'text.primary'
                                }}>
                                  {corr.standardized_estimate.toFixed(3)}
                                </TableCell>
                              )}
                              {displayOptions.showStandardErrors && (
                                <TableCell align="right">
                                  {corr.standard_error.toFixed(3)}
                                </TableCell>
                              )}
                              <TableCell align="right">
                                {corr.z_score.toFixed(2)}
                              </TableCell>
                              {displayOptions.showPValues && (
                                <TableCell align="right" sx={{
                                  color: corr.p_value < displayOptions.significanceLevel ? 'success.main' : 'text.primary'
                                }}>
                                  {corr.p_value < 0.001 ? '< 0.001' : corr.p_value.toFixed(4)}
                                </TableCell>
                              )}
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>
                )}
              </Box>
            )}

            {tabValue === 2 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Model Fit Assessment
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={8}>
                    <TableContainer component={Paper} variant="outlined">
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Fit Index</TableCell>
                            <TableCell align="right">Value</TableCell>
                            <TableCell>Interpretation</TableCell>
                            <TableCell align="center">Status</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          <TableRow>
                            <TableCell>Chi-square (df)</TableCell>
                            <TableCell align="right">
                              {cfaResults.fit_indices.chi_square.toFixed(2)} ({cfaResults.fit_indices.degrees_of_freedom})
                            </TableCell>
                            <TableCell>p = {cfaResults.fit_indices.p_value.toFixed(4)}</TableCell>
                            <TableCell align="center">
                              <Chip 
                                size="small" 
                                label={cfaResults.fit_indices.p_value > 0.05 ? "Good" : "Poor"}
                                color={cfaResults.fit_indices.p_value > 0.05 ? "success" : "error"}
                              />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>CFI</TableCell>
                            <TableCell align="right">{cfaResults.fit_indices.cfi.toFixed(3)}</TableCell>
                            <TableCell>≥ 0.95 excellent, ≥ 0.90 acceptable</TableCell>
                            <TableCell align="center">
                              <Chip 
                                size="small" 
                                label={
                                  cfaResults.fit_indices.cfi >= 0.95 ? "Excellent" :
                                  cfaResults.fit_indices.cfi >= 0.90 ? "Acceptable" : "Poor"
                                }
                                color={
                                  cfaResults.fit_indices.cfi >= 0.95 ? "success" :
                                  cfaResults.fit_indices.cfi >= 0.90 ? "warning" : "error"
                                }
                              />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>TLI</TableCell>
                            <TableCell align="right">{cfaResults.fit_indices.tli.toFixed(3)}</TableCell>
                            <TableCell>≥ 0.95 excellent, ≥ 0.90 acceptable</TableCell>
                            <TableCell align="center">
                              <Chip 
                                size="small" 
                                label={
                                  cfaResults.fit_indices.tli >= 0.95 ? "Excellent" :
                                  cfaResults.fit_indices.tli >= 0.90 ? "Acceptable" : "Poor"
                                }
                                color={
                                  cfaResults.fit_indices.tli >= 0.95 ? "success" :
                                  cfaResults.fit_indices.tli >= 0.90 ? "warning" : "error"
                                }
                              />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>RMSEA</TableCell>
                            <TableCell align="right">
                              {cfaResults.fit_indices.rmsea.toFixed(3)}<br />
                              <Typography variant="caption" color="text.secondary">
                                [{cfaResults.fit_indices.rmsea_ci_lower.toFixed(3)}, {cfaResults.fit_indices.rmsea_ci_upper.toFixed(3)}]
                              </Typography>
                            </TableCell>
                            <TableCell>≤ 0.05 excellent, ≤ 0.08 acceptable</TableCell>
                            <TableCell align="center">
                              <Chip 
                                size="small" 
                                label={
                                  cfaResults.fit_indices.rmsea <= 0.05 ? "Excellent" :
                                  cfaResults.fit_indices.rmsea <= 0.08 ? "Acceptable" :
                                  cfaResults.fit_indices.rmsea <= 0.10 ? "Mediocre" : "Poor"
                                }
                                color={
                                  cfaResults.fit_indices.rmsea <= 0.05 ? "success" :
                                  cfaResults.fit_indices.rmsea <= 0.08 ? "warning" : "error"
                                }
                              />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>SRMR</TableCell>
                            <TableCell align="right">{cfaResults.fit_indices.srmr.toFixed(3)}</TableCell>
                            <TableCell>≤ 0.05 excellent, ≤ 0.08 acceptable</TableCell>
                            <TableCell align="center">
                              <Chip 
                                size="small" 
                                label={
                                  cfaResults.fit_indices.srmr <= 0.05 ? "Excellent" :
                                  cfaResults.fit_indices.srmr <= 0.08 ? "Acceptable" : "Poor"
                                }
                                color={
                                  cfaResults.fit_indices.srmr <= 0.05 ? "success" :
                                  cfaResults.fit_indices.srmr <= 0.08 ? "warning" : "error"
                                }
                              />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>AIC</TableCell>
                            <TableCell align="right">{cfaResults.fit_indices.aic.toFixed(1)}</TableCell>
                            <TableCell>Lower values indicate better fit</TableCell>
                            <TableCell align="center">-</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>BIC</TableCell>
                            <TableCell align="right">{cfaResults.fit_indices.bic.toFixed(1)}</TableCell>
                            <TableCell>Lower values indicate better fit</TableCell>
                            <TableCell align="center">-</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Overall Fit Assessment
                        </Typography>

                        {(() => {
                          const fit = cfaResults.fit_indices;
                          const goodFitCount = [
                            fit.cfi >= 0.95,
                            fit.tli >= 0.95,
                            fit.rmsea <= 0.05,
                            fit.srmr <= 0.05
                          ].filter(Boolean).length;

                          const acceptableFitCount = [
                            fit.cfi >= 0.90,
                            fit.tli >= 0.90,
                            fit.rmsea <= 0.08,
                            fit.srmr <= 0.08
                          ].filter(Boolean).length;

                          if (goodFitCount >= 3) {
                            return (
                              <Alert severity="success">
                                <Typography variant="body2">
                                  <strong>Excellent Fit</strong>
                                  <br />
                                  The model demonstrates excellent fit to the data with {goodFitCount}/4 indices meeting stringent criteria.
                                </Typography>
                              </Alert>
                            );
                          } else if (acceptableFitCount >= 3) {
                            return (
                              <Alert severity="warning">
                                <Typography variant="body2">
                                  <strong>Acceptable Fit</strong>
                                  <br />
                                  The model demonstrates acceptable fit to the data with {acceptableFitCount}/4 indices meeting minimum criteria.
                                </Typography>
                              </Alert>
                            );
                          } else {
                            return (
                              <Alert severity="error">
                                <Typography variant="body2">
                                  <strong>Poor Fit</strong>
                                  <br />
                                  The model fit is questionable. Consider examining modification indices for potential improvements.
                                </Typography>
                              </Alert>
                            );
                          }
                        })()}
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}

            {tabValue === 3 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Modification Indices
                </Typography>

                {cfaResults.modification_indices && cfaResults.modification_indices.length > 0 ? (
                  <>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Modification indices suggest parameter changes that would improve model fit. 
                      Values above 3.84 are significant at p &lt; 0.05.
                    </Typography>

                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Parameter</TableCell>
                            <TableCell align="right">Modification Index</TableCell>
                            <TableCell align="right">Expected Parameter Change</TableCell>
                            <TableCell>Recommendation</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {cfaResults.modification_indices!
                            .sort((a, b) => b.modification_index - a.modification_index)
                            .map((mi, index) => (
                              <TableRow key={index} sx={{
                                backgroundColor: mi.modification_index > 10 ? 'error.light' :
                                                mi.modification_index > 3.84 ? 'warning.light' : 'transparent'
                              }}>
                                <TableCell>{mi.parameter}</TableCell>
                                <TableCell align="right" sx={{
                                  fontWeight: mi.modification_index > 3.84 ? 'bold' : 'normal'
                                }}>
                                  {mi.modification_index.toFixed(3)}
                                </TableCell>
                                <TableCell align="right">
                                  {mi.expected_parameter_change.toFixed(3)}
                                </TableCell>
                                <TableCell>
                                  {mi.modification_index > 10 ? (
                                    <Chip size="small" label="Consider strongly" color="error" />
                                  ) : mi.modification_index > 3.84 ? (
                                    <Chip size="small" label="Consider" color="warning" />
                                  ) : (
                                    <Chip size="small" label="Not significant" color="default" />
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    <Box mt={2}>
                      <Alert severity="info">
                        <Typography variant="body2">
                          <strong>Important:</strong> Only make theoretically justified modifications. 
                          Adding parameters solely to improve fit can lead to overfitting and loss of model interpretability.
                        </Typography>
                      </Alert>
                    </Box>
                  </>
                ) : (
                  <Alert severity="info">
                    No modification indices available. This may indicate excellent model fit or that 
                    modification indices were not calculated for this analysis.
                  </Alert>
                )}
              </Box>
            )}

            {tabValue === 4 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Interpretation Guide
                </Typography>

                <Paper elevation={0} variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'background.paper' }}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                    {getFitInterpretation()}
                  </Typography>
                </Paper>

                <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
                  Understanding Confirmatory Factor Analysis
                </Typography>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle2">Model Fit Indices</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" paragraph>
                      <strong>Chi-square Test:</strong> Tests exact fit hypothesis. Non-significant values (p &gt; 0.05) suggest good fit, 
                      but this test is sensitive to sample size.
                    </Typography>
                    <Typography variant="body2" paragraph>
                      <strong>CFI (Comparative Fit Index):</strong> Compares your model to a baseline model. 
                      Values ≥ 0.95 indicate excellent fit, ≥ 0.90 acceptable fit.
                    </Typography>
                    <Typography variant="body2" paragraph>
                      <strong>TLI (Tucker-Lewis Index):</strong> Similar to CFI but penalizes complex models. 
                      Values ≥ 0.95 indicate excellent fit, ≥ 0.90 acceptable fit.
                    </Typography>
                    <Typography variant="body2" paragraph>
                      <strong>RMSEA (Root Mean Square Error of Approximation):</strong> Measures approximate fit. 
                      Values ≤ 0.05 excellent, ≤ 0.08 acceptable, ≤ 0.10 mediocre.
                    </Typography>
                    <Typography variant="body2">
                      <strong>SRMR (Standardized Root Mean Square Residual):</strong> Average standardized residual. 
                      Values ≤ 0.05 excellent, ≤ 0.08 acceptable.
                    </Typography>
                  </AccordionDetails>
                </Accordion>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle2">Parameter Interpretation</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" paragraph>
                      <strong>Factor Loadings:</strong> Represent the relationship between latent factors and observed indicators. 
                      Standardized loadings &gt; 0.7 are considered strong, &gt; 0.5 moderate, &gt; 0.3 weak.
                    </Typography>
                    <Typography variant="body2" paragraph>
                      <strong>Factor Correlations:</strong> Show relationships between latent factors. 
                      High correlations (&gt; 0.85) may suggest factors are not distinct.
                    </Typography>
                    <Typography variant="body2">
                      <strong>Standard Errors and p-values:</strong> Indicate precision and significance of parameter estimates. 
                      Small p-values (&lt; 0.05) suggest the parameter is significantly different from zero.
                    </Typography>
                  </AccordionDetails>
                </Accordion>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle2">Model Modification</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" paragraph>
                      <strong>Modification Indices:</strong> Suggest parameter additions that would improve fit. 
                      Only consider modifications that are theoretically justified.
                    </Typography>
                    <Typography variant="body2" paragraph>
                      <strong>Expected Parameter Change:</strong> Shows the approximate value the parameter would take if added to the model.
                    </Typography>
                    <Typography variant="body2">
                      <strong>Guidelines:</strong> Modifications should be theory-driven, not purely data-driven. 
                      Consider parsimony and avoid overfitting.
                    </Typography>
                  </AccordionDetails>
                </Accordion>
              </Box>
            )}
          </Paper>
        </>
      )}

      {/* Model Specification Dialog */}
      <Dialog
        open={showModelSpecDialog}
        onClose={() => setShowModelSpecDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Model Builder</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            Drag and drop variables to assign them to factors. Each factor should have at least 2 indicators.
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Available Variables
              </Typography>
              <List dense>
                {numericColumns
                  .filter(col => !factors.some(f => f.indicators.includes(col.id)))
                  .map(column => (
                    <ListItem key={column.id}>
                      <ListItemText primary={column.name} />
                      <Select
                        size="small"
                        value=""
                        displayEmpty
                        onChange={(e) => {
                          if (e.target.value) {
                            addIndicatorToFactor(e.target.value as string, column.id);
                          }
                        }}
                      >
                        <MenuItem value="" disabled>
                          Assign to factor
                        </MenuItem>
                        {factors.map(factor => (
                          <MenuItem key={factor.id} value={factor.id}>
                            {factor.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </ListItem>
                  ))}
              </List>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Factor Structure
              </Typography>
              {factors.map(factor => (
                <Card key={factor.id} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Typography variant="subtitle2" gutterBottom>
                      {factor.name}
                    </Typography>
                    {factor.indicators.length === 0 ? (
                      <Typography variant="body2" color="text.secondary" fontStyle="italic">
                        No indicators assigned
                      </Typography>
                    ) : (
                      <List dense>
                        {factor.indicators.map(ind => {
                          const col = numericColumns.find(c => c.id === ind);
                          return (
                            <ListItem key={ind}>
                              <ListItemIcon>
                                <IconButton
                                  size="small"
                                  onClick={() => removeIndicatorFromFactor(factor.id, ind)}
                                >
                                  <RemoveIcon />
                                </IconButton>
                              </ListItemIcon>
                              <ListItemText primary={col?.name || ind} />
                            </ListItem>
                          );
                        })}
                      </List>
                    )}
                  </CardContent>
                </Card>
              ))}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowModelSpecDialog(false)}>Done</Button>
        </DialogActions>
      </Dialog>

      {/* Path Diagram Dialog */}
      <Dialog
        open={showPathDiagramDialog}
        onClose={() => setShowPathDiagramDialog(false)}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle>
          Path Diagram
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
            Drag elements to reposition. Use mouse wheel to zoom, shift+drag to pan. Double-click to reset view.
          </Typography>
          <Box sx={{ mt: 1 }}>
            <Button
              size="small"
              onClick={resetElementPositions}
              sx={{ mr: 1 }}
            >
              Reset Layout
            </Button>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ height: '100%', overflow: 'hidden', p: 1 }}>
          {cfaResults && (
            <Box
              sx={{
                width: '100%',
                height: '100%',
                overflow: 'auto',
                backgroundColor: 'white',
                border: '1px solid #e0e0e0',
                borderRadius: 1
              }}
            >
              <Plot
                ref={plotRef}
                data={getPathDiagramData()?.data || []}
                layout={getPathDiagramData()?.layout || {}}
                config={{
                  displayModeBar: true,
                  modeBarButtonsToRemove: ['select2d', 'lasso2d'],
                  displaylogo: false,
                  responsive: false,
                  scrollZoom: true,
                  doubleClick: 'reset+autosize',
                  toImageButtonOptions: {
                    format: 'png',
                    filename: 'cfa_path_diagram',
                    height: 800,
                    width: 1200,
                    scale: 2
                  }
                }}
                style={{ width: '100%', height: '100%' }}
                useResizeHandler={true}
                onHover={handlePlotHover}
                onUnhover={handlePlotUnhover}
                onClick={handlePlotClick}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPathDiagramDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Export Results Dialog */}
      <Dialog
        open={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Export Results</DialogTitle>
        <DialogContent>
          <FormControl component="fieldset">
            <FormLabel component="legend">Select items to export:</FormLabel>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.summary}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, summary: e.target.checked }))}
                  />
                }
                label="Summary Statistics"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.modelSpecification}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, modelSpecification: e.target.checked }))}
                  />
                }
                label="Model Specification"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.fitIndices}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, fitIndices: e.target.checked }))}
                  />
                }
                label="Fit Indices"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.pathCoefficients}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, pathCoefficients: e.target.checked }))}
                  />
                }
                label="Path Coefficients"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.modificationIndices}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, modificationIndices: e.target.checked }))}
                    disabled={!cfaResults?.modification_indices}
                  />
                }
                label="Modification Indices"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.residualCorrelations}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, residualCorrelations: e.target.checked }))}
                  />
                }
                label="Residual Correlations"
              />
            </FormGroup>
          </FormControl>

          <Box mt={2}>
            <Typography variant="body2" color="text.secondary">
              Results will be exported as a formatted text file.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowExportDialog(false)}>Cancel</Button>
          <Button onClick={exportResults} variant="contained" startIcon={<DownloadIcon />}>
            Export
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ConfirmatoryFactorAnalysis;
