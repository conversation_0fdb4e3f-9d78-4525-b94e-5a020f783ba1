import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  FormControlLabel,
  Checkbox,
  TextField,
  Divider,
  RadioGroup,
  Radio,
  Chip,
  Tab,
  Tabs,
  Slider,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Help as HelpIcon,
  ExpandMore as ExpandMoreIcon,
  Hub as HubIcon,
  Download as DownloadIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType, Column } from '../../../types'; // VariableRole was unused
import Plot from 'react-plotly.js';
import type { Data, Layout } from 'plotly.js'; // Using 'type' for type-only imports

// Types for cluster analysis
interface ClusteringResults {
  method: 'kmeans' | 'hierarchical' | 'dbscan';
  clusters: number[];
  centroids?: number[][];
  numClusters: number;
  silhouetteScore: number;
  daviesBouldinScore: number;
  inertia?: number;
  clusterSizes: { [cluster: number]: number };
  clusterProfiles: { [cluster: number]: ClusterProfile };
  scatterData: ScatterPoint[];
  elbowData?: ElbowPoint[];
  silhouetteData?: SilhouettePoint[];
  dendrogramData?: any; // Kept as any, complex structure
  variables: Column[];
  normalizedData: number[][];
  originalData: any[];
  parameters: {
    distance?: string;
    linkage?: string;
    epsilon?: number;
    minSamples?: number;
  };
}

interface ScatterPoint {
  x: number;
  y: number;
  cluster: number;
  label: string;
  index: number; // Original index in dataset
}

interface ElbowPoint {
  k: number;
  inertia: number;
  silhouette: number;
}

interface SilhouettePoint {
  index: number; // Index in the subset of data used for clustering
  originalIndex: number; // Index in the original full dataset
  cluster: number;
  silhouette: number;
  label: string; // e.g., 'C${cluster}'
}


interface ClusterProfile {
  means: { [variable: string]: number };
  stds: { [variable: string]: number };
  size: number;
  percentage: number;
}

const ClusterAnalysis: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();
  
  // State for data selection
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [selectedVariables, setSelectedVariables] = useState<string[]>([]);
  const [labelVariable, setLabelVariable] = useState<string>('');
  
  // State for clustering configuration
  const [clusteringMethod, setClusteringMethod] = useState<'kmeans' | 'hierarchical' | 'dbscan'>('kmeans');
  const [numClusters, setNumClusters] = useState<number>(3);
  const [distanceMetric, setDistanceMetric] = useState<'euclidean' | 'manhattan' | 'cosine'>('euclidean');
  const [linkageMethod, setLinkageMethod] = useState<'single' | 'complete' | 'average' | 'ward'>('ward');
  const [epsilon, setEpsilon] = useState<number>(0.5);
  const [minSamples, setMinSamples] = useState<number>(5);
  const [standardizeData, setStandardizeData] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<number>(0);
  
  // State for display options
  const [displayOptions, setDisplayOptions] = useState({
    showScatterPlot: true,
    showClusterProfiles: true,
    showSilhouetteAnalysis: true,
    showElbowMethod: true,
    showDendrogram: true,
    showClusterSizes: true,
    showValidationMetrics: true,
    // show3DVisualization: false // Not implemented
  });
  
  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [clusteringResults, setClusteringResults] = useState<ClusteringResults | null>(null);
  const [optimalClustersSearch, setOptimalClustersSearch] = useState<boolean>(false);
  
  // Load saved results from localStorage
  useEffect(() => {
    const savedResults = localStorage.getItem('cluster_analysis_results');
    if (savedResults) {
      try {
        const parsedResults = JSON.parse(savedResults);
        setClusteringResults(parsedResults);
        // Restore some config from saved results if needed, e.g., method
        if (parsedResults.method) setClusteringMethod(parsedResults.method);
        if (parsedResults.parameters?.distance) setDistanceMetric(parsedResults.parameters.distance);
        // ... any other parameters you want to restore
      } catch (error) {
        console.error('Error parsing saved cluster analysis results:', error);
        localStorage.removeItem('cluster_analysis_results');
      }
    }
  }, []);
  
  // Get numeric columns from current dataset
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];
  
  // Get all columns for labeling
  const allColumns = currentDataset?.columns || [];
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedVariables([]);
    setLabelVariable('');
    setClusteringResults(null);
    localStorage.removeItem('cluster_analysis_results');
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };
  
  // Handle variable selection change
  const handleVariableChange = (event: SelectChangeEvent<typeof selectedVariables>) => {
    const value = event.target.value;
    setSelectedVariables(typeof value === 'string' ? value.split(',') : value);
    setClusteringResults(null);
    localStorage.removeItem('cluster_analysis_results');
  };
  
  // Handle label variable change
  const handleLabelVariableChange = (event: SelectChangeEvent<string>) => {
    setLabelVariable(event.target.value);
  };
  
  // Handle clustering method change
  const handleMethodChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setClusteringMethod(event.target.value as any);
    setClusteringResults(null);
    localStorage.removeItem('cluster_analysis_results');
  };
  
  // Handle number of clusters change
  const handleNumClustersChange = (_event: Event, newValue: number | number[]) => {
    setNumClusters(newValue as number);
    setClusteringResults(null);
    localStorage.removeItem('cluster_analysis_results');
  };
  
  // Handle distance metric change
  const handleDistanceMetricChange = (event: SelectChangeEvent<string>) => {
    setDistanceMetric(event.target.value as any);
    setClusteringResults(null);
    localStorage.removeItem('cluster_analysis_results');
  };
  
  // Handle linkage method change
  const handleLinkageMethodChange = (event: SelectChangeEvent<string>) => {
    setLinkageMethod(event.target.value as any);
    setClusteringResults(null);
    localStorage.removeItem('cluster_analysis_results');
  };
  
  // Handle epsilon change
  const handleEpsilonChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEpsilon(parseFloat(event.target.value));
    setClusteringResults(null);
    localStorage.removeItem('cluster_analysis_results');
  };
  
  // Handle min samples change
  const handleMinSamplesChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setMinSamples(parseInt(event.target.value, 10));
    setClusteringResults(null);
    localStorage.removeItem('cluster_analysis_results');
  };
  
  // Handle display option change
  const handleDisplayOptionChange = (option: keyof typeof displayOptions) => {
    setDisplayOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };
  
  // Distance calculation functions
  const euclideanDistance = (a: number[], b: number[]): number => Math.sqrt(a.reduce((sum, val, i) => sum + Math.pow(val - b[i], 2), 0));
  const manhattanDistance = (a: number[], b: number[]): number => a.reduce((sum, val, i) => sum + Math.abs(val - b[i]), 0);
  const cosineDistance = (a: number[], b: number[]): number => {
    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
    const normA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
    const normB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
    if (normA === 0 || normB === 0) return 1; // Or handle as error/specific case
    return 1 - (dotProduct / (normA * normB));
  };
  
  const getDistance = (a: number[], b: number[]): number => {
    switch (distanceMetric) {
      case 'manhattan': return manhattanDistance(a, b);
      case 'cosine': return cosineDistance(a, b);
      default: return euclideanDistance(a, b);
    }
  };
  
  // K-means clustering implementation
  const kMeansClustering = (data: number[][], k: number, maxIterations: number = 100): { clusters: number[]; centroids: number[][]; inertia: number; } => {
    const n = data.length;
    const d = data[0].length;
    let centroids: number[][] = [];
    const usedIndices = new Set<number>();
    while (centroids.length < k && centroids.length < n) {
      const idx = Math.floor(Math.random() * n);
      if (!usedIndices.has(idx)) {
        centroids.push([...data[idx]]);
        usedIndices.add(idx);
      }
    }
    if (centroids.length < k) { // Fallback if not enough unique points
        for(let i=centroids.length; i<k; i++) centroids.push([...data[Math.floor(Math.random() * n)]]);
    }

    let clusters = new Array(n).fill(0);
    for (let iter = 0; iter < maxIterations; iter++) {
      let changed = false;
      for (let i = 0; i < n; i++) {
        let minDist = Infinity;
        let bestCluster = 0;
        for (let j = 0; j < k; j++) {
          const dist = getDistance(data[i], centroids[j]);
          if (dist < minDist) {
            minDist = dist;
            bestCluster = j;
          }
        }
        if (clusters[i] !== bestCluster) {
          clusters[i] = bestCluster;
          changed = true;
        }
      }
      if (!changed) break;
      for (let j = 0; j < k; j++) {
        const clusterPoints = data.filter((_, i) => clusters[i] === j);
        if (clusterPoints.length > 0) {
          for (let dim = 0; dim < d; dim++) {
            centroids[j][dim] = clusterPoints.reduce((sum, point) => sum + point[dim], 0) / clusterPoints.length;
          }
        } else { // Handle empty cluster: reinitialize centroid
            centroids[j] = [...data[Math.floor(Math.random() * n)]];
        }
      }
    }
    let inertia = 0;
    for (let i = 0; i < n; i++) inertia += Math.pow(getDistance(data[i], centroids[clusters[i]]), 2);
    return { clusters, centroids, inertia };
  };

  // Hierarchical clustering implementation (simplified agglomerative)
  // This version does NOT produce dendrogram data, only final clusters.
  const hierarchicalClustering = (data: number[][], targetK: number): number[] => {
    const n = data.length;
    if (n === 0) return [];
    if (targetK >= n) return data.map((_, i) => i); // Each point is a cluster

    // Initialize each point as a cluster
    let clusters: number[][] = data.map(point => [point[0]]); // Store indices of points in each cluster
    let pointToClusterMap: number[] = Array.from({ length: n }, (_, i) => i); // point_index -> cluster_id
    let activeClusters: number[] = Array.from({ length: n }, (_, i) => i); // list of active cluster_ids

    // Precompute distances between all initial points
    const distMatrix: number[][] = Array(n).fill(0).map(() => Array(n).fill(Infinity));
    for (let i = 0; i < n; i++) {
        for (let j = i + 1; j < n; j++) {
            distMatrix[i][j] = distMatrix[j][i] = getDistance(data[i], data[j]);
        }
    }
    
    // Function to calculate distance between two clusters
    const calculateClusterDistance = (cluster1Indices: number[], cluster2Indices: number[]): number => {
        let minD = Infinity, maxD = -Infinity, sumD = 0, count = 0;
        for (const i1 of cluster1Indices) {
            for (const i2 of cluster2Indices) {
                const d = getDistance(data[i1], data[i2]);
                minD = Math.min(minD, d);
                maxD = Math.max(maxD, d);
                sumD += d;
                count++;
            }
        }
        if (linkageMethod === 'single') return minD;
        if (linkageMethod === 'complete') return maxD;
        if (linkageMethod === 'average') return sumD / count;
        // Ward's method is more complex and involves change in SSE, not directly implemented here with this structure
        // Falling back to average for 'ward' if not fully implemented
        return sumD / count; 
    };


    let currentNumClusters = n;
    const clusterPointMembers: Map<number, number[]> = new Map(Array.from({length:n}, (_,i)=> [i, [i]]));


    while (currentNumClusters > targetK) {
        if (currentNumClusters <= 1) break;

        let minDist = Infinity;
        let merge1Idx = -1, merge2Idx = -1;

        for (let i = 0; i < activeClusters.length; i++) {
            for (let j = i + 1; j < activeClusters.length; j++) {
                const c1Id = activeClusters[i];
                const c2Id = activeClusters[j];
                const c1Members = clusterPointMembers.get(c1Id)!;
                const c2Members = clusterPointMembers.get(c2Id)!;
                
                const dist = calculateClusterDistance(c1Members, c2Members);

                if (dist < minDist) {
                    minDist = dist;
                    merge1Idx = i; // Store index in activeClusters array
                    merge2Idx = j;
                }
            }
        }
        
        if (merge1Idx === -1 || merge2Idx === -1) break; // No more clusters to merge

        const clusterId1 = activeClusters[merge1Idx];
        const clusterId2 = activeClusters[merge2Idx];

        // Merge clusterId2 into clusterId1
        const members2 = clusterPointMembers.get(clusterId2)!;
        clusterPointMembers.get(clusterId1)!.push(...members2);
        clusterPointMembers.delete(clusterId2);

        // Update pointToClusterMap for points that were in clusterId2
        members2.forEach(pointIdx => {
            pointToClusterMap[pointIdx] = clusterId1;
        });

        // Remove clusterId2 from activeClusters
        activeClusters.splice(merge2Idx, 1); // remove larger index first
        activeClusters.splice(merge1Idx, 1);
        activeClusters.push(clusterId1); // Add the merged cluster back (conceptually)
                                         // Or rather, the list of active cluster IDs is now shorter.
                                         // Simpler: filter out clusterId2
        activeClusters = activeClusters.filter(id => id !== clusterId2);


        currentNumClusters--;
    }

    // Renumber final clusters from 0 to targetK-1
    const finalClusterIds = Array.from(clusterPointMembers.keys());
    const remapping: { [oldId: number]: number } = {};
    finalClusterIds.forEach((oldId, newId) => {
        remapping[oldId] = newId;
    });

    const finalAssignments = new Array(n).fill(0);
    for(let i=0; i<n; i++) {
        finalAssignments[i] = remapping[pointToClusterMap[i]];
    }
    return finalAssignments;
  };
  
  // DBSCAN clustering implementation
  const dbscanClustering = (data: number[][], eps: number, minPts: number): number[] => {
    const n = data.length;
    const NOISE = -1;
    const UNDEFINED = -2;
    const clusters = new Array(n).fill(UNDEFINED); // -2: undefined, -1: noise
    let clusterId = 0;

    const getNeighbors = (pointIdx: number): number[] => {
        const neighbors: number[] = [];
        for (let i = 0; i < n; i++) {
            if (pointIdx !== i && getDistance(data[pointIdx], data[i]) <= eps) {
                neighbors.push(i);
            }
        }
        return neighbors;
    };

    for (let i = 0; i < n; i++) {
        if (clusters[i] !== UNDEFINED) continue; // Already processed

        const neighbors = getNeighbors(i);

        if (neighbors.length < minPts -1) { // -1 because point itself is part of minPts
            clusters[i] = NOISE;
            continue;
        }

        // Core point, start a new cluster
        clusters[i] = clusterId;
        let queue = [...neighbors];
        
        let head = 0;
        while(head < queue.length){
            const currentPointIdx = queue[head++];

            if(clusters[currentPointIdx] === NOISE){
                clusters[currentPointIdx] = clusterId; // Change noise to border point
            }
            if(clusters[currentPointIdx] !== UNDEFINED) continue; // Already processed or assigned

            clusters[currentPointIdx] = clusterId;
            const currentNeighbors = getNeighbors(currentPointIdx);
            if(currentNeighbors.length >= minPts -1){ // If this point is also a core point
                queue.push(...currentNeighbors.filter(p => clusters[p] === UNDEFINED || clusters[p] === NOISE));
            }
        }
        clusterId++;
    }
    return clusters;
  };
  
  // Calculate silhouette score
  const calculateSilhouetteScore = (data: number[][], clusters: number[]): number => {
    const n = data.length;
    if (n === 0) return 0;
    const uniqueClusterLabels = [...new Set(clusters)].filter(c => c !== -1);
    const k = uniqueClusterLabels.length;

    if (k <= 1) return 0; // Silhouette is not well-defined for 1 cluster or only noise

    let totalSilhouette = 0;
    let validPoints = 0;

    for (let i = 0; i < n; i++) {
        if (clusters[i] === -1) continue; // Skip noise points

        const ownClusterLabel = clusters[i];
        let a_i = 0;
        let ownClusterSize = 0;
        for (let j = 0; j < n; j++) {
            if (i !== j && clusters[j] === ownClusterLabel) {
                a_i += getDistance(data[i], data[j]);
                ownClusterSize++;
            }
        }
        a_i = ownClusterSize > 0 ? a_i / ownClusterSize : 0;

        let b_i = Infinity;
        for (const otherClusterLabel of uniqueClusterLabels) {
            if (otherClusterLabel === ownClusterLabel) continue;

            let avgDistToOtherCluster = 0;
            let otherClusterSize = 0;
            for (let j = 0; j < n; j++) {
                if (clusters[j] === otherClusterLabel) {
                    avgDistToOtherCluster += getDistance(data[i], data[j]);
                    otherClusterSize++;
                }
            }
            if (otherClusterSize > 0) {
                b_i = Math.min(b_i, avgDistToOtherCluster / otherClusterSize);
            }
        }

        if (ownClusterSize === 0) { // Point is the only one in its cluster
            totalSilhouette += 0; // Or handle as per specific definition (e.g., score of 0)
        } else if (b_i === Infinity) { // All other clusters are empty or this is the only cluster
            totalSilhouette += 0;
        } else {
            totalSilhouette += (b_i - a_i) / Math.max(a_i, b_i);
        }
        validPoints++;
    }
    return validPoints > 0 ? totalSilhouette / validPoints : 0;
  };
  
  // Calculate Davies-Bouldin score
  const calculateDaviesBouldinScore = (data: number[][], clusters: number[], centroids: number[][]): number => {
    const uniqueClusterLabels = [...new Set(clusters)].filter(c => c !== -1);
    const k = uniqueClusterLabels.length;
    if (k <= 1) return 0;

    const actualCentroids = uniqueClusterLabels.map(label => centroids[label]);

    const clusterScatters: number[] = new Array(k).fill(0);
    const clusterSizes: number[] = new Array(k).fill(0);

    for (let i = 0; i < data.length; i++) {
        const clusterLabel = clusters[i];
        if (clusterLabel === -1) continue;
        const clusterIndex = uniqueClusterLabels.indexOf(clusterLabel);
        if (clusterIndex === -1) continue; // Should not happen if uniqueClusterLabels is correct

        clusterScatters[clusterIndex] += getDistance(data[i], actualCentroids[clusterIndex]);
        clusterSizes[clusterIndex]++;
    }

    for (let i = 0; i < k; i++) {
        clusterScatters[i] = clusterSizes[i] > 0 ? clusterScatters[i] / clusterSizes[i] : 0;
    }

    let dbIndex = 0;
    for (let i = 0; i < k; i++) {
        let maxRatio = 0;
        for (let j = 0; j < k; j++) {
            if (i === j) continue;
            const separation = getDistance(actualCentroids[i], actualCentroids[j]);
            if (separation > 0) { // Avoid division by zero
                const ratio = (clusterScatters[i] + clusterScatters[j]) / separation;
                maxRatio = Math.max(maxRatio, ratio);
            }
        }
        dbIndex += maxRatio;
    }
    return k > 0 ? dbIndex / k : 0;
  };
  
  // Normalize data
  const normalizeData = (data: number[][]): number[][] => {
    if (!standardizeData || data.length === 0) return data;
    const n = data.length;
    const d = data[0].length;
    const normalized: number[][] = Array(n).fill(0).map(() => Array(d).fill(0));
    const means: number[] = new Array(d).fill(0);
    const stds: number[] = new Array(d).fill(0);

    for (let j = 0; j < d; j++) {
        for (let i = 0; i < n; i++) means[j] += data[i][j];
        means[j] /= n;
        for (let i = 0; i < n; i++) stds[j] += Math.pow(data[i][j] - means[j], 2);
        stds[j] = Math.sqrt(stds[j] / n);
        if (stds[j] === 0) stds[j] = 1; // Avoid division by zero
    }
    for (let i = 0; i < n; i++) {
        for (let j = 0; j < d; j++) {
            normalized[i][j] = (data[i][j] - means[j]) / stds[j];
        }
    }
    return normalized;
  };
  
  // Run clustering analysis
  const runClustering = () => {
    if (!currentDataset || selectedVariables.length < 1) { // Allow 1 variable for profile, but 2 for scatter
      setError('Please select at least 1 variable for clustering (2 for scatter plot).');
      return;
    }
    
    setLoading(true);
    setError(null);
    setClusteringResults(null);
    
    // Simulate async operation for UI update
    setTimeout(() => {
        try {
          const selectedColumns = selectedVariables
            .map(id => currentDataset.columns.find(col => col.id === id))
            .filter(Boolean) as Column[];
          
          const rawData: number[][] = [];
          const validRowIndices: number[] = [];
          
          currentDataset.data.forEach((row, index) => {
            const values = selectedColumns.map(col => row[col.name]);
            if (values.every(val => typeof val === 'number' && !isNaN(val))) {
              rawData.push(values as number[]);
              validRowIndices.push(index);
            }
          });
          
          if (rawData.length < Math.max(numClusters, 5)) { // Ensure enough data for chosen clusters
            throw new Error(`Insufficient data for clustering. Need at least ${Math.max(numClusters,5)} valid data points.`);
          }
          
          const dataForClustering = normalizeData(rawData);
          
          let clusters: number[];
          let tempCentroids: number[][] | undefined;
          let inertia: number | undefined;
          let actualNumClusters = numClusters; // For DBSCAN, this will be determined by the algo
          
          if (clusteringMethod === 'kmeans') {
            if (rawData.length < numClusters) throw new Error(`Not enough data points (${rawData.length}) for ${numClusters} clusters in K-Means.`);
            const result = kMeansClustering(dataForClustering, numClusters);
            clusters = result.clusters;
            tempCentroids = result.centroids;
            inertia = result.inertia;
          } else if (clusteringMethod === 'hierarchical') {
            if (rawData.length < numClusters) throw new Error(`Not enough data points (${rawData.length}) for ${numClusters} clusters in Hierarchical.`);
            clusters = hierarchicalClustering(dataForClustering, numClusters);
          } else { // dbscan
            clusters = dbscanClustering(dataForClustering, epsilon, minSamples);
            actualNumClusters = [...new Set(clusters.filter(c => c !== -1))].length;
            if (actualNumClusters === 0 && clusters.some(c => c === -1)) {
                setError("DBSCAN resulted in all points being classified as noise. Try adjusting Epsilon or Min Samples.");
                // No need to throw, just inform and set minimal results
            }
          }

          // Calculate centroids if not k-means or if needed for other methods
          if (!tempCentroids || clusteringMethod !== 'kmeans') {
            const uniqueClusterLabels = [...new Set(clusters.filter(c => c !== -1))].sort((a,b)=>a-b);
            actualNumClusters = uniqueClusterLabels.length;
            tempCentroids = new Array(actualNumClusters).fill(0).map(() => new Array(dataForClustering[0].length).fill(0));
            const counts = new Array(actualNumClusters).fill(0);

            if (actualNumClusters > 0) {
                dataForClustering.forEach((point, i) => {
                    const label = clusters[i];
                    if (label !== -1) {
                        const clusterIdx = uniqueClusterLabels.indexOf(label);
                        if (clusterIdx !== -1) { // Should always be found
                            point.forEach((val, dim) => tempCentroids![clusterIdx][dim] += val);
                            counts[clusterIdx]++;
                        }
                    }
                });
                tempCentroids.forEach((centroid, cIdx) => {
                    if (counts[cIdx] > 0) {
                        centroid.forEach((_, dim) => centroid[dim] /= counts[cIdx]);
                    }
                });
            }
          }
          
          const silhouetteScore = actualNumClusters > 1 ? calculateSilhouetteScore(dataForClustering, clusters) : 0;
          const daviesBouldinScore = actualNumClusters > 1 && tempCentroids && tempCentroids.length > 0 ? calculateDaviesBouldinScore(dataForClustering, clusters, tempCentroids) : 0;
          
          const clusterSizes: { [cluster: number]: number } = {};
          clusters.forEach(c => {
            if (c !== -1) clusterSizes[c] = (clusterSizes[c] || 0) + 1;
          });
          
          const clusterProfiles: { [cluster: number]: ClusterProfile } = {};
          const uniqueValidClusters = [...new Set(clusters.filter(c => c !== -1))].sort((a,b)=>a-b);
          
          uniqueValidClusters.forEach(clusterId => {
            const clusterIndicesInRawData = clusters
              .map((c, i) => (c === clusterId ? validRowIndices[i] : -1)) // Map to original data indices
              .filter(i => i !== -1);
            
            const clusterPointsInRawData = clusterIndicesInRawData.map(originalIdx => {
                const rawDataIdx = validRowIndices.indexOf(originalIdx);
                return rawData[rawDataIdx];
            });

            if (clusterPointsInRawData.length === 0) return;

            const means: { [variable: string]: number } = {};
            const stds: { [variable: string]: number } = {};
            
            selectedColumns.forEach((col, varIdx) => {
              const values = clusterPointsInRawData.map(p => p[varIdx]);
              means[col.name] = values.reduce((sum, v) => sum + v, 0) / values.length;
              stds[col.name] = Math.sqrt(
                values.reduce((sum, v) => sum + Math.pow(v - means[col.name], 2), 0) / values.length
              ) || 0; // Ensure std is not NaN if all values are same
            });
            
            clusterProfiles[clusterId] = {
              means, stds,
              size: clusterSizes[clusterId],
              percentage: (clusterSizes[clusterId] / rawData.length) * 100
            };
          });
          
          const scatterData: ScatterPoint[] = dataForClustering.map((point, i) => ({
            x: point[0], // Assumes at least one variable
            y: point.length > 1 ? point[1] : 0, // Use 0 for y if only one var
            cluster: clusters[i],
            label: labelVariable && currentDataset.columns.find(col => col.id === labelVariable)
                   ? String(currentDataset.data[validRowIndices[i]][currentDataset.columns.find(col => col.id === labelVariable)!.name])
                   : `Point ${validRowIndices[i]}`,
            index: validRowIndices[i]
          }));
          
          let elbowData: ElbowPoint[] | undefined;
          if (clusteringMethod === 'kmeans' && displayOptions.showElbowMethod && rawData.length > 1) {
            elbowData = [];
            const maxK = Math.min(10, rawData.length -1, dataForClustering.length -1); // Ensure k < n
            if (maxK >=1) {
                for (let k_elbow = 1; k_elbow <= maxK; k_elbow++) {
                    if (dataForClustering.length < k_elbow) break; // Cannot have more clusters than points
                    const result = kMeansClustering(dataForClustering, k_elbow);
                    elbowData.push({
                      k: k_elbow,
                      inertia: result.inertia,
                      silhouette: k_elbow > 1 ? calculateSilhouetteScore(dataForClustering, result.clusters) : 0
                    });
                }
            }
          }
          
          const silhouettePlotData: SilhouettePoint[] = [];
          if (displayOptions.showSilhouetteAnalysis && actualNumClusters > 1) {
            clusters.forEach((cluster, i) => {
              if (cluster !== -1) { // Only for non-noise points
                const pointData = dataForClustering[i];
                let a_i = 0;
                let ownClusterSize = 0;
                let b_i = Infinity;

                // Calculate a_i
                for(let j=0; j<dataForClustering.length; j++) {
                    if (i !==j && clusters[j] === cluster) {
                        a_i += getDistance(pointData, dataForClustering[j]);
                        ownClusterSize++;
                    }
                }
                a_i = ownClusterSize > 0 ? a_i / ownClusterSize : 0;

                // Calculate b_i
                for (const otherClusterLabel of uniqueValidClusters) {
                    if (otherClusterLabel === cluster) continue;
                    let avgDistToOtherCluster = 0;
                    let otherClusterSize = 0;
                    for (let j = 0; j < dataForClustering.length; j++) {
                        if (clusters[j] === otherClusterLabel) {
                            avgDistToOtherCluster += getDistance(pointData, dataForClustering[j]);
                            otherClusterSize++;
                        }
                    }
                    if (otherClusterSize > 0) {
                        b_i = Math.min(b_i, avgDistToOtherCluster / otherClusterSize);
                    }
                }
                const silhouetteVal = (ownClusterSize === 0 || b_i === Infinity) ? 0 : (b_i - a_i) / Math.max(a_i, b_i);
                silhouettePlotData.push({
                  index: i, // index within the clustered data subset
                  originalIndex: validRowIndices[i],
                  cluster,
                  silhouette: silhouetteVal,
                  label: `C${cluster}` // original: `C${cluster}`
                });
              }
            });
            silhouettePlotData.sort((a, b) => {
              if (a.cluster !== b.cluster) return a.cluster - b.cluster;
              return b.silhouette - a.silhouette; // Higher silhouette first
            });
          }
          
          const results: ClusteringResults = {
            method: clusteringMethod, clusters, centroids: tempCentroids,
            numClusters: actualNumClusters, silhouetteScore, daviesBouldinScore,
            inertia, clusterSizes, clusterProfiles, scatterData,
            elbowData, silhouetteData: silhouettePlotData,
            dendrogramData: undefined, // Not implemented
            variables: selectedColumns, normalizedData: dataForClustering, originalData: rawData,
            parameters: {
              distance: distanceMetric,
              linkage: clusteringMethod === 'hierarchical' ? linkageMethod : undefined,
              epsilon: clusteringMethod === 'dbscan' ? epsilon : undefined,
              minSamples: clusteringMethod === 'dbscan' ? minSamples : undefined
            }
          };
          
          setClusteringResults(results);
          localStorage.setItem('cluster_analysis_results', JSON.stringify(results));
          setLoading(false);
        } catch (err) {
          setError(`Error in cluster analysis: ${err instanceof Error ? err.message : String(err)}`);
          setLoading(false);
        }
    }, 50); // Small timeout to allow UI to update to loading state
  };
  
  // Find optimal number of clusters
  const findOptimalClusters = () => {
    if (!currentDataset || selectedVariables.length < 1) {
      setError('Please select at least 1 variable for clustering.');
      return;
    }
    if (clusteringMethod !== 'kmeans') {
        setError('Optimal cluster search is primarily designed for K-Means using the Elbow method.');
        return;
    }
    
    setOptimalClustersSearch(true);
    
    // Rerun clustering for elbow method if not already present or needs refresh
    // This is a simplified version; a full search might involve running K-Means for various K
    // and then analyzing elbowData. Here, we assume elbowData is already computed via runClustering
    // if displayOptions.showElbowMethod was true.

    if (clusteringResults?.elbowData && clusteringResults.elbowData.length > 1) {
        const elbow = clusteringResults.elbowData;
        let bestK = numClusters; // Default to current
        // Find "elbow" point: largest second derivative (change in slope)
        // Or where silhouette score is maximized if available
        
        // Using inertia drop:
        const inertias = elbow.map(d => d.inertia);
        if (inertias.length > 2) {
            let maxDiff = -Infinity;
            // Iterate from k=2 up to maxK-1 to find the "elbow"
            // (where the rate of decrease of inertia slows down most significantly)
            for (let i = 1; i < inertias.length - 1; i++) {
                // Rate of decrease from k-1 to k
                const decrease1 = inertias[i-1] - inertias[i];
                // Rate of decrease from k to k+1
                const decrease2 = inertias[i] - inertias[i+1];
                const diffOfDecrease = decrease1 - decrease2; // We want this to be large
                if (diffOfDecrease > maxDiff) {
                    maxDiff = diffOfDecrease;
                    bestK = elbow[i].k; // The k at this point is the elbow
                }
            }
        } else if (elbow.length > 0) {
            bestK = elbow[0].k; // Not enough points for complex elbow, maybe suggest based on max silhouette
        }
        
        // Also consider silhouette score if available
        const silhouettes = elbow.filter(d => d.k > 1).map(d => ({ k: d.k, score: d.silhouette }));
        if (silhouettes.length > 0) {
            const bestSilhouetteK = silhouettes.reduce((max, p) => p.score > max.score ? p : max, silhouettes[0]).k;
            // Could choose between elbow K and silhouette K, or provide both
            // For now, let's prioritize elbow and inform about silhouette
            setError(`Suggested optimal K (Elbow): ${bestK}. Max Silhouette K: ${bestSilhouetteK}.`);
            setNumClusters(bestK);
        } else {
            setError(`Suggested optimal K (Elbow): ${bestK}.`);
            setNumClusters(bestK);
        }
    } else {
      setError('Run analysis with "Show Elbow Method" enabled to find optimal K.');
    }
    setOptimalClustersSearch(false);
  };
  
  // Generate interpretation
  const getInterpretation = () => {
    if (!clusteringResults) return '';
    const { method, numClusters, silhouetteScore, daviesBouldinScore, clusterSizes, clusterProfiles } = clusteringResults;
    let interpretation = `A ${method} clustering analysis was performed on ${clusteringResults.originalData.length} data points using ${clusteringResults.variables.length} variable(s): ${clusteringResults.variables.map(v => v.name).join(', ')}. `;
    if (method === 'dbscan') {
      const noiseCount = clusteringResults.clusters.filter(c => c === -1).length;
      interpretation += `The algorithm identified ${numClusters} clusters and ${noiseCount} noise points. `;
    } else {
      interpretation += `The data was partitioned into ${numClusters} clusters. `;
    }
    interpretation += `\n\nValidation Metrics:\n- Silhouette Score: ${silhouetteScore.toFixed(3)} `;
    if (silhouetteScore > 0.7) interpretation += `(Strong structure)\n`;
    else if (silhouetteScore > 0.5) interpretation += `(Reasonable structure)\n`;
    else if (silhouetteScore > 0.25) interpretation += `(Weak structure)\n`;
    else interpretation += `(No substantial structure)\n`;
    interpretation += `- Davies-Bouldin Score: ${daviesBouldinScore.toFixed(3)} (Lower is better)\n`;
    interpretation += `\nCluster Distribution:\n`;
    Object.entries(clusterSizes).forEach(([cluster, size]) => {
      const percentage = (size / clusteringResults.originalData.length * 100).toFixed(1);
      interpretation += `- Cluster ${parseInt(cluster) + 1}: ${size} points (${percentage}%)\n`;
    });
    interpretation += `\nCluster Profiles (mean values for original data):\n`;
    Object.entries(clusterProfiles).forEach(([cluster, profile]) => {
      interpretation += `\nCluster ${parseInt(cluster) + 1}:\n`;
      Object.entries(profile.means).forEach(([varName, mean]) => {
        interpretation += `  - ${varName}: ${mean.toFixed(2)} (Std: ${profile.stds[varName].toFixed(2)})\n`;
      });
    });
    return interpretation;
  };
  
  // Export clusters
  const exportClusters = () => {
    if (!clusteringResults || !currentDataset) return;
    const headers = [...currentDataset.columns.map(col => col.name), 'Cluster_Assignment'];
    // Map cluster assignments back to original full dataset
    const clusterAssignments = new Array(currentDataset.data.length).fill('N/A');
    clusteringResults.scatterData.forEach(sd => { // scatterData contains original indices
        clusterAssignments[sd.index] = sd.cluster === -1 ? 'Noise' : `Cluster_${sd.cluster + 1}`;
    });

    const rows = currentDataset.data.map((row, index) => {
      return [...Object.values(row), clusterAssignments[index]];
    });
    const csv = [headers, ...rows].map(row => row.join(',')).join('\n');
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${currentDataset.name}_clusters.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };
  
  // Color palette for clusters
  const clusterColors = [
    theme.palette.primary.main, theme.palette.secondary.main, theme.palette.error.main,
    theme.palette.warning.main, theme.palette.success.main, theme.palette.info.main,
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFA07A', '#20B2AA'
  ];
  const noiseColor = theme.palette.grey[500];

  const plotlyBaseLayout: Partial<Layout> = {
    autosize: true,
    paper_bgcolor: theme.palette.background.paper,
    plot_bgcolor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[200], // Slightly different for plot area
    font: { color: theme.palette.text.primary },
    legend: { bgcolor: theme.palette.background.default, bordercolor: theme.palette.divider, font: {color: theme.palette.text.secondary}},
    xaxis: { gridcolor: theme.palette.divider, linecolor: theme.palette.divider, zerolinecolor: theme.palette.divider, tickfont: {color: theme.palette.text.secondary} },
    yaxis: { gridcolor: theme.palette.divider, linecolor: theme.palette.divider, zerolinecolor: theme.palette.divider, tickfont: {color: theme.palette.text.secondary} },
  };
  
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>Cluster Analysis</Typography>
      
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>Data Selection</Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select labelId="dataset-select-label" value={selectedDatasetId} label="Dataset" onChange={handleDatasetChange} disabled={datasets.length === 0}>
                {datasets.length === 0 ? <MenuItem value="" disabled>No datasets available</MenuItem> : datasets.map(dataset => <MenuItem key={dataset.id} value={dataset.id}>{dataset.name} ({dataset.data.length} rows)</MenuItem>)}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="variables-label">Variables for Clustering</InputLabel>
              <Select labelId="variables-label" multiple value={selectedVariables} label="Variables for Clustering" onChange={handleVariableChange} disabled={!currentDataset}
                renderValue={(selected) => selected.map(id => numericColumns.find(col => col.id === id)?.name).filter(Boolean).join(', ')}>
                {numericColumns.length === 0 ? <MenuItem value="" disabled>No numeric variables</MenuItem> : numericColumns.map(column => <MenuItem key={column.id} value={column.id}>{column.name}</MenuItem>)}
              </Select>
              <Typography variant="caption" color="text.secondary">Select at least 1 numeric variable (2 for scatter plot)</Typography>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="label-variable-label">Label Variable (Optional)</InputLabel>
              <Select labelId="label-variable-label" value={labelVariable} label="Label Variable (Optional)" onChange={handleLabelVariableChange} disabled={!currentDataset}>
                <MenuItem value="">None</MenuItem>
                {allColumns.map(column => <MenuItem key={column.id} value={column.id}>{column.name}</MenuItem>)}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        
        <Accordion sx={{ mt: 2 }} defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}><Typography>Clustering Configuration</Typography></AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>Clustering Method</Typography>
                <RadioGroup row value={clusteringMethod} onChange={handleMethodChange}>
                  {[ 'kmeans', 'hierarchical', 'dbscan' ].map(method => (
                    <FormControlLabel key={method} value={method} control={<Radio />} 
                      label={<Box display="flex" alignItems="center">{method.toUpperCase()}<Tooltip title={
                        method === 'kmeans' ? "Partitions data into K spherical clusters" :
                        method === 'hierarchical' ? "Builds a hierarchy of clusters" :
                        "Density-based, finds arbitrarily shaped clusters"
                      }><IconButton size="small"><HelpIcon fontSize="small" /></IconButton></Tooltip></Box>} />
                  ))}
                </RadioGroup>
              </Grid>
              {(clusteringMethod === 'kmeans' || clusteringMethod === 'hierarchical') && (
                <Grid item xs={12} md={6}>
                  <Typography gutterBottom>Number of Clusters: {numClusters}</Typography>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Slider value={numClusters} onChange={handleNumClustersChange} min={2} max={Math.max(2, Math.min(15, Math.floor((currentDataset?.data.length || 0) / 3)))} step={1} marks valueLabelDisplay="auto" />
                    {clusteringMethod === 'kmeans' && 
                        <Button size="small" variant="outlined" onClick={findOptimalClusters} disabled={optimalClustersSearch || !clusteringResults?.elbowData}>Find Optimal K</Button>}
                  </Box>
                </Grid>
              )}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="distance-metric-label">Distance Metric</InputLabel>
                  <Select labelId="distance-metric-label" value={distanceMetric} label="Distance Metric" onChange={handleDistanceMetricChange}>
                    {['euclidean', 'manhattan', 'cosine'].map(metric => <MenuItem key={metric} value={metric}>{metric.charAt(0).toUpperCase() + metric.slice(1)}</MenuItem>)}
                  </Select>
                </FormControl>
              </Grid>
              {clusteringMethod === 'hierarchical' && (
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel id="linkage-method-label">Linkage Method</InputLabel>
                    <Select labelId="linkage-method-label" value={linkageMethod} label="Linkage Method" onChange={handleLinkageMethodChange}>
                      {['single', 'complete', 'average', 'ward'].map(link => <MenuItem key={link} value={link}>{link.charAt(0).toUpperCase() + link.slice(1)}</MenuItem>)}
                    </Select>
                     <Typography variant="caption" color="text.secondary">Note: 'Ward' linkage is complex and might be approximated if not fully supported by the simplified algorithm.</Typography>
                  </FormControl>
                </Grid>
              )}
              {clusteringMethod === 'dbscan' && (<>
                  <Grid item xs={12} md={6}><TextField label="Epsilon (ε)" type="number" value={epsilon} onChange={handleEpsilonChange} fullWidth inputProps={{ min: 0.01, step: 0.01 }} helperText="Max distance for neighborhood" /></Grid>
                  <Grid item xs={12} md={6}><TextField label="Min Samples" type="number" value={minSamples} onChange={handleMinSamplesChange} fullWidth inputProps={{ min: 1, step: 1 }} helperText="Min points for core point" /></Grid>
              </>)}
              <Grid item xs={12}><FormControlLabel control={<Checkbox checked={standardizeData} onChange={(e) => { setStandardizeData(e.target.checked); setClusteringResults(null); localStorage.removeItem('cluster_analysis_results'); }} />} label="Standardize variables (recommended)" /></Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
        
        <Box mt={2}>
          <Typography variant="subtitle2" gutterBottom>Display Options</Typography>
          <Grid container spacing={1}>
            {Object.keys(displayOptions).map(key => (
              <Grid item xs={6} sm={4} md={3} key={key}>
                <FormControlLabel control={<Checkbox checked={displayOptions[key as keyof typeof displayOptions]} onChange={() => handleDisplayOptionChange(key as keyof typeof displayOptions)} 
                disabled={(key === 'showDendrogram' && clusteringMethod !== 'hierarchical') || (key === 'showElbowMethod' && clusteringMethod !== 'kmeans')}
                />} label={key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} />
              </Grid>
            ))}
          </Grid>
        </Box>
        
        <Box mt={2} display="flex" gap={2}>
          <Button variant="contained" color="primary" startIcon={<HubIcon />} onClick={runClustering} disabled={loading || selectedVariables.length === 0 || (selectedVariables.length < 2 && displayOptions.showScatterPlot)}>Run Cluster Analysis</Button>
          {clusteringResults && <Button variant="outlined" startIcon={<DownloadIcon />} onClick={exportClusters}>Export Clusters</Button>}
        </Box>
      </Paper>
      
      {loading && <Box display="flex" justifyContent="center" my={4}><CircularProgress /></Box>}
      {error && <Alert severity={error.startsWith("Suggested optimal K") ? "info" : "error"} sx={{ mb: 3 }}>{error}</Alert>}
      
      {clusteringResults && !loading && (
        <Paper elevation={2} sx={{ p: 2, mt:3 }}>
          <Typography variant="h6" gutterBottom>Clustering Results</Typography>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 2 }} indicatorColor="primary" textColor="primary" variant="scrollable" scrollButtons="auto">
            <Tab label="Overview" />
            <Tab label="Visualization" />
            <Tab label="Cluster Profiles" />
            <Tab label="Validation" />
            <Tab label="Interpretation" />
          </Tabs>
          
          {/* OVERVIEW TAB */}
          {activeTab === 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle2" gutterBottom color="text.secondary">Clustering Summary</Typography>
                    <Box mb={2}><Typography variant="h4">{clusteringResults.numClusters} Clusters</Typography><Typography variant="body2" color="text.secondary">Using {clusteringResults.method} clustering</Typography></Box>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="body2"><strong>Method:</strong> {clusteringResults.method.toUpperCase()}</Typography>
                    <Typography variant="body2"><strong>Variables:</strong> {clusteringResults.variables.map(v => v.name).join(', ')}</Typography>
                    <Typography variant="body2"><strong>Distance:</strong> {clusteringResults.parameters.distance}</Typography>
                    {clusteringResults.parameters.linkage && <Typography variant="body2"><strong>Linkage:</strong> {clusteringResults.parameters.linkage}</Typography>}
                    {clusteringResults.parameters.epsilon !== undefined && <Typography variant="body2"><strong>Epsilon:</strong> {clusteringResults.parameters.epsilon}</Typography>}
                    {clusteringResults.parameters.minSamples !== undefined && <Typography variant="body2"><strong>Min Samples:</strong> {clusteringResults.parameters.minSamples}</Typography>}
                  </CardContent>
                </Card>
              </Grid>
              {displayOptions.showValidationMetrics && (
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom color="text.secondary">Validation Metrics</Typography>
                      <Box mb={2}><Typography variant="h4">{clusteringResults.silhouetteScore.toFixed(3)}</Typography><Typography variant="body2" color="text.secondary">Silhouette Score</Typography></Box>
                      <Divider sx={{ my: 1 }} />
                      <Typography variant="body2"><strong>Davies-Bouldin:</strong> {clusteringResults.daviesBouldinScore.toFixed(3)}</Typography>
                      {clusteringResults.inertia !== undefined && <Typography variant="body2"><strong>Inertia:</strong> {clusteringResults.inertia.toFixed(2)}</Typography>}
                      {clusteringResults.silhouetteScore > 0.7 ? <Chip icon={<CheckCircleIcon />} label="Strong Clustering" color="success" size="small" sx={{ mt: 1 }} />
                       : clusteringResults.silhouetteScore > 0.5 ? <Chip label="Reasonable Clustering" color="primary" size="small" sx={{ mt: 1 }} />
                       : <Chip icon={<WarningIcon />} label="Weak Clustering" color="warning" size="small" sx={{ mt: 1 }} />}
                    </CardContent>
                  </Card>
                </Grid>
              )}
              {displayOptions.showClusterSizes && clusteringResults.clusterSizes && Object.keys(clusteringResults.clusterSizes).length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>Cluster Distribution</Typography>
                    <Plot
                        data={[{
                            values: Object.values(clusteringResults.clusterSizes) as number[],
                            labels: Object.keys(clusteringResults.clusterSizes).map((k: string) => `Cluster ${parseInt(k) + 1}`),
                            type: 'pie' as const,
                            marker: { colors: Object.keys(clusteringResults.clusterSizes).map((k: string) => clusterColors[parseInt(k) % clusterColors.length]) },
                            hoverinfo: 'label+percent+name' as const,
                            textinfo: 'label+percent',
                            automargin: true,
                        }]}
                        layout={{ ...plotlyBaseLayout, title: { text: 'Cluster Sizes' }, height: 350, showlegend: false }}
                        style={{ width: '100%', height: '350px' }}
                        useResizeHandler={true}
                    />
                </Grid>
              )}
            </Grid>
          )}

          {/* VISUALIZATION TAB */}
          {activeTab === 1 && (
            <Grid container spacing={3}>
                {displayOptions.showScatterPlot && clusteringResults.scatterData && clusteringResults.variables.length >= 2 && (
                    <Grid item xs={12} md={clusteringResults.method === 'kmeans' && displayOptions.showElbowMethod ? 6 : 12}>
                        <Typography variant="subtitle2" gutterBottom>Cluster Scatter Plot (Normalized Data)</Typography>
                        <Plot
                            data={(() => {
                                const traces: Data[] = [];
                                const uniqueClusters = [...new Set(clusteringResults.scatterData.map(p => p.cluster))].sort((a,b)=>a-b);
                                uniqueClusters.forEach(clusterId => {
                                    const clusterPoints = clusteringResults.scatterData.filter(p => p.cluster === clusterId);
                                    traces.push({
                                        x: clusterPoints.map(p => p.x),
                                        y: clusterPoints.map(p => p.y),
                                        text: clusterPoints.map(p => p.label),
                                        mode: 'markers',
                                        type: 'scatter',
                                        name: clusterId === -1 ? 'Noise' : `Cluster ${clusterId + 1}`,
                                        marker: { color: clusterId === -1 ? noiseColor : clusterColors[clusterId % clusterColors.length], size: 8, opacity: 0.8 }
                                    });
                                });
                                return traces;
                            })()}
                            layout={{ ...plotlyBaseLayout, title: { text: 'Clusters' },
                                xaxis: { title: { text: clusteringResults.variables[0]?.name || 'Variable 1' } },
                                yaxis: { title: { text: clusteringResults.variables[1]?.name || 'Variable 2' } },
                                height: 450, hovermode: 'closest'
                            }}
                            style={{ width: '100%', height: '450px' }}
                            useResizeHandler={true}
                        />
                    </Grid>
                )}
                 {displayOptions.showScatterPlot && clusteringResults.variables.length < 2 && (
                    <Grid item xs={12}><Alert severity="info">Scatter plot requires at least 2 variables for clustering.</Alert></Grid>
                )}

                {clusteringResults.method === 'kmeans' && displayOptions.showElbowMethod && clusteringResults.elbowData && (
                    <Grid item xs={12} md={displayOptions.showScatterPlot && clusteringResults.variables.length >= 2 ? 6 : 12}>
                         <Typography variant="subtitle2" gutterBottom>Elbow Method & Silhouette Score by K</Typography>
                        <Plot
                            data={[
                                { x: clusteringResults.elbowData.map(d => d.k), y: clusteringResults.elbowData.map(d => d.inertia), type: 'scatter', mode: 'lines+markers', name: 'Inertia (SSE)', line:{color: theme.palette.primary.main}},
                                { x: clusteringResults.elbowData.map(d => d.k), y: clusteringResults.elbowData.map(d => d.silhouette), type: 'scatter', mode: 'lines+markers', name: 'Silhouette Score', yaxis: 'y2', line:{color: theme.palette.secondary.main} }
                            ]}
                            layout={{ ...plotlyBaseLayout, title: { text: 'Elbow Method' },
                                xaxis: { title: { text: 'Number of Clusters (K)' } },
                                yaxis: { title: { text: 'Inertia (SSE)', font: {color: theme.palette.primary.main} }, tickfont: {color: theme.palette.primary.main}},
                                yaxis2: { title: { text: 'Silhouette Score', font: {color: theme.palette.secondary.main} }, overlaying: 'y', side: 'right', tickfont: {color: theme.palette.secondary.main} },
                                height: 450, legend: {yanchor:"top", y:0.99, xanchor:"right", x:0.99}
                            }}
                            style={{ width: '100%', height: '450px' }}
                            useResizeHandler={true}
                        />
                    </Grid>
                )}

                {displayOptions.showSilhouetteAnalysis && clusteringResults.silhouetteData && clusteringResults.silhouetteData.length > 0 && (
                     <Grid item xs={12}>
                        <Typography variant="subtitle2" gutterBottom>Silhouette Analysis per Sample</Typography>
                        <Plot
                            data={[{
                                x: clusteringResults.silhouetteData.map((_, i) => i),
                                y: clusteringResults.silhouetteData.map(p => p.silhouette),
                                text: clusteringResults.silhouetteData.map(p => `Original Index: ${p.originalIndex}<br>Cluster: ${p.cluster === -1 ? 'Noise' : p.cluster + 1}<br>Silhouette: ${p.silhouette.toFixed(3)}`),
                                hoverinfo: 'text',
                                type: 'bar',
                                marker: { 
                                    color: clusteringResults.silhouetteData.map(p => p.cluster === -1 ? noiseColor : clusterColors[p.cluster % clusterColors.length]),
                                    line: { width:0 } 
                                },
                                width: 1, // for continuous look if bargap is 0
                            }]}
                            layout={{ ...plotlyBaseLayout, title: { text: 'Silhouette Coefficients' },
                                xaxis: { title: { text: 'Samples (Sorted by Cluster, then Silhouette Score)' }, showticklabels: false, zeroline: false},
                                yaxis: { title: { text: 'Silhouette Coefficient' }, range: [-1,1], zerolinecolor: theme.palette.text.secondary },
                                height: 450, bargap: 0.05,
                                shapes: [
                                    { type: 'line', x0: 0, y0: clusteringResults.silhouetteScore, x1: 1, y1: clusteringResults.silhouetteScore, xref: 'paper', line: { color: theme.palette.error.main, width: 2, dash: 'dash' }},
                                    // Add lines to separate clusters visually
                                    ...(() => {
                                        const shapes: Partial<Plotly.Shape>[] = [];
                                        let currentCluster = -100; // Ensure first cluster starts a new segment
                                        clusteringResults.silhouetteData!.forEach((p, i) => {
                                            if (p.cluster !== currentCluster && i > 0) {
                                                shapes.push({
                                                    type: 'line', x0: i - 0.5, y0: -1, x1: i - 0.5, y1: 1,
                                                    line: { color: theme.palette.divider, width: 1, dash: 'dot' }
                                                });
                                            }
                                            currentCluster = p.cluster;
                                        });
                                        return shapes;
                                    })()
                                ],
                                annotations: [{
                                    x: 0.98, y: clusteringResults.silhouetteScore, xref: 'paper', yref: 'y', text: `Avg: ${clusteringResults.silhouetteScore.toFixed(3)}`, showarrow: false, font: {color: theme.palette.error.main}, bgcolor: theme.palette.background.paper, opacity: 0.8
                                }]
                            }}
                            style={{ width: '100%', height: '450px' }}
                            useResizeHandler={true}
                        />
                    </Grid>
                )}
                {clusteringMethod === 'hierarchical' && displayOptions.showDendrogram && (
                    <Grid item xs={12}><Alert severity="info">Dendrogram visualization is complex. The current simplified hierarchical clustering provides assignments but not full dendrogram data. Consider specialized libraries for detailed dendrograms.</Alert></Grid>
                )}
            </Grid>
          )}

          {/* CLUSTER PROFILES TAB */}
          {activeTab === 2 && displayOptions.showClusterProfiles && (
            <Grid container spacing={3}>
              {Object.entries(clusteringResults.clusterProfiles).sort(([c1], [c2]) => parseInt(c1) - parseInt(c2)).map(([cluster, profile]) => (
                <Grid item xs={12} md={6} lg={4} key={cluster}>
                  <Card>
                    <CardContent>
                      <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                        <Typography variant="h6">Cluster {parseInt(cluster) + 1}</Typography>
                        <Chip label={`${profile.size} points (${profile.percentage.toFixed(1)}%)`} sx={{backgroundColor: clusterColors[parseInt(cluster) % clusterColors.length], color: theme.palette.getContrastText(clusterColors[parseInt(cluster) % clusterColors.length]) }} size="small" />
                      </Box>
                      <TableContainer component={Paper} variant="outlined" sx={{maxHeight: 300}}>
                        <Table size="small" stickyHeader>
                          <TableHead><TableRow><TableCell>Variable</TableCell><TableCell align="right">Mean</TableCell><TableCell align="right">Std Dev</TableCell></TableRow></TableHead>
                          <TableBody>
                            {Object.entries(profile.means).map(([varName, mean]) => (
                              <TableRow key={varName} hover><TableCell>{varName}</TableCell><TableCell align="right">{mean.toFixed(3)}</TableCell><TableCell align="right">{profile.stds[varName].toFixed(3)}</TableCell></TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
              {Object.keys(clusteringResults.clusterProfiles).length > 0 && clusteringResults.variables.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>Cluster Comparison (Mean Normalized Values if Standardized)</Typography>
                   <Plot
                        data={(() => {
                            const traces: Data[] = [];
                            const vars = clusteringResults.variables.map(v => v.name);
                            // Determine overall min/max for radar axis scaling, using normalized data if available
                            let allMeans: number[] = [];
                            Object.values(clusteringResults.clusterProfiles).forEach(profile => {
                                vars.forEach(varName => {
                                    // Use normalized means if data was standardized for clustering, original otherwise
                                    // For simplicity, we use the means from clusterProfiles which are on original scale.
                                    // If a normalized radar is desired, recalculate means on normalized data for profiles.
                                    allMeans.push(profile.means[varName]);
                                });
                            });
                            // const minVal = Math.min(...allMeans);
                            // const maxVal = Math.max(...allMeans);

                            Object.entries(clusteringResults.clusterProfiles).sort(([c1], [c2]) => parseInt(c1) - parseInt(c2)).forEach(([clusterIdStr, profile], index) => {
                                const clusterIdx = parseInt(clusterIdStr);
                                traces.push({
                                    type: 'scatterpolar',
                                    r: vars.map(varName => profile.means[varName]), // Using original scale means
                                    theta: vars,
                                    fill: 'toself',
                                    name: `Cluster ${clusterIdx + 1}`,
                                    marker: { color: clusterColors[clusterIdx % clusterColors.length] },
                                    fillcolor: `${clusterColors[clusterIdx % clusterColors.length]}66` // Add alpha
                                });
                            });
                            return traces;
                        })()}
                        layout={{ ...plotlyBaseLayout, title: { text: 'Mean Variable Values by Cluster' },
                            polar: { 
                                radialaxis: { visible: true, side: "counterclockwise" /*, range: [minVal, maxVal] */ },
                                angularaxis: { tickfont: {size:10} }
                            }, 
                            height: 500, legend: {orientation: 'h', yanchor: 'bottom', y: 1.02, xanchor: 'right', x: 1}
                        }}
                        style={{ width: '100%', height: '500px' }}
                        useResizeHandler={true}
                    />
                </Grid>
              )}
            </Grid>
          )}

          {/* VALIDATION TAB */}
          {activeTab === 3 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card><CardContent>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary">Validation Summary</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}><Box textAlign="center" p={2}><Typography variant="h3" color="primary">{clusteringResults.silhouetteScore.toFixed(3)}</Typography><Typography variant="body2" color="text.secondary">Silhouette Score</Typography><Typography variant="caption">Similarity to own cluster vs others. Range [-1, 1], higher is better.</Typography></Box></Grid>
                    <Grid item xs={12} sm={4}><Box textAlign="center" p={2}><Typography variant="h3" color="secondary">{clusteringResults.daviesBouldinScore.toFixed(3)}</Typography><Typography variant="body2" color="text.secondary">Davies-Bouldin Score</Typography><Typography variant="caption">Ratio of within-cluster scatter to between-cluster separation. Lower is better (min 0).</Typography></Box></Grid>
                    {clusteringResults.inertia !== undefined && <Grid item xs={12} sm={4}><Box textAlign="center" p={2}><Typography variant="h3" color="error">{clusteringResults.inertia.toFixed(0)}</Typography><Typography variant="body2" color="text.secondary">Inertia (SSE)</Typography><Typography variant="caption">Sum of squared distances to closest centroid (K-Means). Lower is better.</Typography></Box></Grid>}
                  </Grid>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="body1" paragraph><strong>Quality Assessment:</strong></Typography>
                  <Box pl={2}>
                    <Typography variant="body2" paragraph>• Silhouette: {clusteringResults.silhouetteScore > 0.7 ? 'Excellent' : clusteringResults.silhouetteScore > 0.5 ? 'Good' : clusteringResults.silhouetteScore > 0.25 ? 'Fair' : 'Poor'}</Typography>
                    <Typography variant="body2" paragraph>• Davies-Bouldin: {clusteringResults.daviesBouldinScore < 0.5 ? 'Excellent' : clusteringResults.daviesBouldinScore < 1.0 ? 'Good' : 'Fair/Poor'}</Typography>
                  </Box>
                </CardContent></Card>
              </Grid>
            </Grid>
          )}

          {/* INTERPRETATION TAB */}
          {activeTab === 4 && (
             <Box mt={2}>
                <Typography variant="h6" gutterBottom>Automated Interpretation</Typography>
                <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: 'background.default', whiteSpace: 'pre-line' }}>
                  <Typography variant="body2">{getInterpretation()}</Typography>
                </Paper>
             </Box>
          )}
        </Paper>
      )}
    </Box>
  );
};

export default ClusterAnalysis;