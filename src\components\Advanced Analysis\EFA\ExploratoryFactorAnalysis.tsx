import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  FormControlLabel,
  Checkbox,
  TextField,
  Divider,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Chip,
  FormLabel,
  RadioGroup,
  Radio,
  FormGroup,
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  Timeline as TimelineIcon,
  Help as HelpIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Info as InfoIcon,
  TableChart as TableChartIcon,
  Score as ScoreIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType, Column, VariableRole } from '../../../types';
import {
  exploratoryFactorAnalysisService,
  FactorAnalysisResults,
  FactorScores,
  FactorAnalysisData
} from '../../../utils/services/exploratoryFactorAnalysisService';
import Plot from 'react-plotly.js';
import { Data, Layout } from 'plotly.js'; // Import Data and Layout types from plotly.js

interface DisplayOptions {
  showScreePlot: boolean;
  showFactorMatrix: boolean;
  showCommunalities: boolean;
  showRotatedMatrix: boolean;
  showCorrelationMatrix: boolean;
  minimumLoading: number;
}

interface ExportOptions {
  summary: boolean;
  loadings: boolean;
  communalities: boolean;
  eigenvalues: boolean;
  correlationMatrix: boolean;
  factorCorrelations: boolean;
}

const ExploratoryFactorAnalysis: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();

  // State for analysis configuration
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [selectedVariables, setSelectedVariables] = useState<string[]>([]);
  const [extractionMethod, setExtractionMethod] = useState<string>('pca');
  const [rotationMethod, setRotationMethod] = useState<string>('varimax');
  const [numberOfFactors, setNumberOfFactors] = useState<string>('');
  const [factorSelectionMethod, setFactorSelectionMethod] = useState<string>('kaiser');

  // State for display options
  const [displayOptions, setDisplayOptions] = useState<DisplayOptions>({
    showScreePlot: true,
    showFactorMatrix: true,
    showCommunalities: true,
    showRotatedMatrix: true,
    showCorrelationMatrix: false,
    minimumLoading: 0.3,
  });

  // State for results and UI
  const [loading, setLoading] = useState<boolean>(false);
  const [initializingPython, setInitializingPython] = useState<boolean>(false);
  const [pythonReady, setPythonReady] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [factorResults, setFactorResults] = useState<FactorAnalysisResults | null>(null);
  const [factorScores, setFactorScores] = useState<FactorScores | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);
  const [showFactorScoresDialog, setShowFactorScoresDialog] = useState<boolean>(false);
  const [showExportDialog, setShowExportDialog] = useState<boolean>(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    summary: true,
    loadings: true,
    communalities: true,
    eigenvalues: true,
    correlationMatrix: false,
    factorCorrelations: false,
  });

  // Initialize Python environment
  const initializePython = useCallback(async () => {
    if (pythonReady || initializingPython) return;

    setInitializingPython(true);
    try {
      await exploratoryFactorAnalysisService.initialize();
      setPythonReady(true);
    } catch (error) {
      console.error('Failed to initialize Python environment:', error);
      setError('Failed to initialize Python environment. Please refresh the page and try again.');
    } finally {
      setInitializingPython(false);
    }
  }, [pythonReady, initializingPython]);

  // Initialize Python on component mount
  useEffect(() => {
    initializePython();
  }, [initializePython]);

  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('factor_analysis_results');
    if (savedResults) {
      try {
        const parsedResults = JSON.parse(savedResults);
        setFactorResults(parsedResults);
      } catch (error) {
        console.error('Error parsing saved factor analysis results:', error);
        localStorage.removeItem('factor_analysis_results');
      }
    }
  }, []);

  // Get numeric columns for analysis
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedVariables([]);
    setFactorResults(null);
    setFactorScores(null);
    localStorage.removeItem('factor_analysis_results');

    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };

  // Handle variable selection change
  const handleVariableChange = (event: SelectChangeEvent<typeof selectedVariables>) => {
    const value = event.target.value;
    const newVars = typeof value === 'string' ? value.split(',') : value;
    setSelectedVariables(newVars);
    setFactorResults(null);
    setFactorScores(null);
    localStorage.removeItem('factor_analysis_results');
  };

  // Handle extraction method change
  const handleExtractionMethodChange = (event: SelectChangeEvent<string>) => {
    setExtractionMethod(event.target.value);
    setFactorResults(null);
    setFactorScores(null);
    localStorage.removeItem('factor_analysis_results');
  };

  // Handle rotation method change
  const handleRotationMethodChange = (event: SelectChangeEvent<string>) => {
    setRotationMethod(event.target.value);
    setFactorResults(null);
    setFactorScores(null);
    localStorage.removeItem('factor_analysis_results');
  };

  // Handle factor selection method change
  const handleFactorSelectionMethodChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFactorSelectionMethod(event.target.value);
    if (event.target.value !== 'manual') {
      setNumberOfFactors('');
    }
    setFactorResults(null);
    setFactorScores(null);
    localStorage.removeItem('factor_analysis_results');
  };

  // Handle number of factors change
  const handleNumberOfFactorsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNumberOfFactors(event.target.value);
    setFactorResults(null);
    setFactorScores(null);
    localStorage.removeItem('factor_analysis_results');
  };

  // Handle display option change
  const handleDisplayOptionChange = (option: keyof DisplayOptions) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (option === 'minimumLoading') {
      const value = parseFloat(event.target.value);
      if (!isNaN(value) && value >= 0 && value < 1) {
        setDisplayOptions(prev => ({
          ...prev,
          minimumLoading: value
        }));
      }
    } else {
      setDisplayOptions(prev => ({
        ...prev,
        [option]: event.target.checked
      }));
    }
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Run factor analysis
  const runFactorAnalysis = async () => {
    if (!currentDataset || selectedVariables.length < 3) {
      setError('Please select at least 3 variables for factor analysis.');
      return;
    }

    if (!pythonReady) {
      setError('Python environment is not ready. Please wait for initialization to complete.');
      return;
    }

    setLoading(true);
    setError(null);
    setFactorResults(null);
    setFactorScores(null);

    try {
      // Prepare data for analysis
      const selectedColumns = selectedVariables
        .map(id => currentDataset.columns.find(col => col.id === id))
        .filter(Boolean) as Column[];

      const variableNames = selectedColumns.map(col => col.name);
      const variables: { [key: string]: number[] } = {};

      // Extract data for each variable
      selectedColumns.forEach((col, index) => {
        const values: number[] = [];
        currentDataset.data.forEach(row => {
          const value = row[col.name];
          if (typeof value === 'number' && !isNaN(value)) {
            values.push(value);
          }
        });
        variables[`var_${index}`] = values;
      });

      // Check if all variables have the same length
      const lengths = Object.values(variables).map(v => v.length);
      if (new Set(lengths).size > 1) {
        throw new Error('Variables have different numbers of valid observations.');
      }

      if (lengths[0] < 10) {
        throw new Error('Insufficient data for factor analysis. Need at least 10 valid observations.');
      }

      // Prepare analysis data
      const analysisData: FactorAnalysisData & {
        n_factors?: number;
        extraction_method?: string;
        rotation_method?: string;
        factor_selection?: string;
        min_loading?: number;
      } = {
        variables,
        variable_names: variableNames,
        extraction_method: extractionMethod,
        rotation_method: rotationMethod,
        factor_selection: factorSelectionMethod,
        min_loading: displayOptions.minimumLoading
      };

      // Add number of factors if manual selection
      if (factorSelectionMethod === 'manual' && numberOfFactors) {
        const nFactors = parseInt(numberOfFactors);
        if (nFactors > 0 && nFactors <= selectedVariables.length) {
          analysisData.n_factors = nFactors;
        } else {
          throw new Error(`Number of factors must be between 1 and ${selectedVariables.length}`);
        }
      }

      // Run factor analysis
      const results = await exploratoryFactorAnalysisService.runFactorAnalysis(analysisData);

      setFactorResults(results);
      localStorage.setItem('factor_analysis_results', JSON.stringify(results));

    } catch (err) {
      console.error('Factor analysis error:', err);
      setError(`Error in factor analysis: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };

  // Calculate factor scores
  const calculateFactorScores = async () => {
    if (!factorResults || !currentDataset || !pythonReady) return;

    try {
      const selectedColumns = selectedVariables
        .map(id => currentDataset.columns.find(col => col.id === id))
        .filter(Boolean) as Column[];

      const variables: { [key: string]: number[] } = {};

      selectedColumns.forEach((col, index) => {
        const values: number[] = [];
        currentDataset.data.forEach(row => {
          const value = row[col.name];
          if (typeof value === 'number' && !isNaN(value)) {
            values.push(value);
          }
        });
        variables[`var_${index}`] = values;
      });

      const scoreData: FactorAnalysisData = {
        variables,
        variable_names: selectedColumns.map(col => col.name)
      };

      const scores = await exploratoryFactorAnalysisService.calculateFactorScores(
        factorResults,
        scoreData
      );

      setFactorScores(scores);
      setShowFactorScoresDialog(true);

    } catch (err) {
      console.error('Factor score calculation error:', err);
      setError(`Error calculating factor scores: ${err instanceof Error ? err.message : String(err)}`);
    }
  };

  // Export factor scores to CSV
  const exportFactorScores = () => {
    if (!factorScores) return;

    let csv = 'Observation';
    for (let i = 0; i < factorScores.scores[0].length; i++) {
      csv += `,Factor_${i + 1}`;
    }
    csv += '\n';

    factorScores.scores.forEach((scores, index) => {
      csv += `${index + 1}`;
      scores.forEach(score => {
        csv += `,${score.toFixed(6)}`;
      });
      csv += '\n';
    });

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'factor_scores.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Export results
  const exportResults = () => {
    if (!factorResults) return;

    let content = 'EXPLORATORY FACTOR ANALYSIS RESULTS\n';
    content += '====================================\n\n';

    if (exportOptions.summary) {
      content += 'SUMMARY\n';
      content += '-------\n';
      content += `Extraction Method: ${factorResults.extraction_method.toUpperCase()}\n`;
      content += `Rotation Method: ${factorResults.rotation_method}\n`;
      content += `Number of Factors: ${factorResults.n_factors}\n`;
      content += `Number of Variables: ${factorResults.rotated_loadings?.length || 0}\n`;
      content += `Number of Observations: ${factorResults.n_observations}\n`;
      content += `KMO Measure: ${factorResults.kmo.toFixed(3)}\n`;
      content += `Bartlett's Test: χ² = ${factorResults.bartlett_chi_square.toFixed(2)}, p = ${factorResults.bartlett_p_value.toFixed(4)}\n\n`;
    }

    if (exportOptions.eigenvalues) {
      content += 'EIGENVALUES AND VARIANCE EXPLAINED\n';
      content += '-----------------------------------\n';
      content += 'Factor\tEigenvalue\tVariance %\tCumulative %\n';
      factorResults.eigenvalues.forEach((eigen, index) => {
        content += `${index + 1}\t${eigen.toFixed(3)}\t`;
        content += `${factorResults.variance_explained[index].toFixed(1)}\t`;
        content += `${factorResults.cumulative_variance[index].toFixed(1)}\n`;
      });
      content += '\n';
    }

    if (exportOptions.loadings && factorResults.rotated_loadings) {
      content += `ROTATED FACTOR LOADINGS (${factorResults.rotation_method})\n`;
      content += '----------------------------------------\n';
      content += 'Variable';
      for (let i = 0; i < factorResults.n_factors; i++) {
        content += `\tFactor ${i + 1}`;
      }
      content += '\n';

      const variableNames = Object.keys(factorResults.communalities);
      variableNames.forEach((varName, varIndex) => {
        content += varName;
        for (let factorIndex = 0; factorIndex < factorResults.n_factors; factorIndex++) {
          const loading = factorResults.rotated_loadings![varIndex][factorIndex];
          content += `\t${loading.toFixed(3)}`;
        }
        content += '\n';
      });
      content += '\n';
    }

    if (exportOptions.communalities) {
      content += 'COMMUNALITIES\n';
      content += '-------------\n';
      Object.entries(factorResults.communalities).forEach(([varName, comm]) => {
        content += `${varName}: ${comm.toFixed(3)}\n`;
      });
      content += '\n';
    }

    if (exportOptions.correlationMatrix && factorResults.correlation_matrix) {
      content += 'CORRELATION MATRIX\n';
      content += '------------------\n';
      const variableNames = Object.keys(factorResults.communalities);
      content += '\t' + variableNames.join('\t') + '\n';
      factorResults.correlation_matrix.forEach((row, i) => {
        content += variableNames[i] + '\t';
        content += row.map(val => val.toFixed(3)).join('\t') + '\n';
      });
      content += '\n';
    }

    if (exportOptions.factorCorrelations && factorResults.factor_correlations) {
      content += 'FACTOR CORRELATIONS\n';
      content += '-------------------\n';
      for (let i = 0; i < factorResults.n_factors; i++) {
        content += `\tFactor ${i + 1}`;
      }
      content += '\n';
      factorResults.factor_correlations.forEach((row, i) => {
        content += `Factor ${i + 1}`;
        row.forEach(val => {
          content += `\t${val.toFixed(3)}`;
        });
        content += '\n';
      });
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'factor_analysis_results.txt';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    setShowExportDialog(false);
  };

  // Generate interpretation
  const getInterpretation = () => {
    if (!factorResults) return '';

    let interpretation = '';

    // KMO and Bartlett's test interpretation
    interpretation += `Kaiser-Meyer-Olkin (KMO) Measure: ${factorResults.kmo.toFixed(3)}\n`;
    if (factorResults.kmo >= 0.9) {
      interpretation += 'The KMO value is marvelous, indicating the data is highly suitable for factor analysis.\n';
    } else if (factorResults.kmo >= 0.8) {
      interpretation += 'The KMO value is meritorious, indicating the data is well-suited for factor analysis.\n';
    } else if (factorResults.kmo >= 0.7) {
      interpretation += 'The KMO value is middling, indicating the data is adequately suited for factor analysis.\n';
    } else if (factorResults.kmo >= 0.6) {
      interpretation += 'The KMO value is mediocre, indicating the data is marginally suitable for factor analysis.\n';
    } else {
      interpretation += 'The KMO value is miserable, suggesting the data may not be suitable for factor analysis.\n';
    }

    interpretation += `\nBartlett's Test of Sphericity: χ² = ${factorResults.bartlett_chi_square.toFixed(2)}, p = ${factorResults.bartlett_p_value.toFixed(4)}\n`;
    if (factorResults.bartlett_p_value < 0.001) {
      interpretation += 'The test is highly significant (p < 0.001), confirming that correlations between variables are sufficient for factor analysis.\n';
    } else if (factorResults.bartlett_p_value < 0.05) {
      interpretation += 'The test is significant (p < 0.05), indicating that correlations between variables are sufficient for factor analysis.\n';
    } else {
      interpretation += 'The test is not significant, suggesting the correlation matrix may be an identity matrix (variables are uncorrelated).\n';
    }

    // Factor extraction interpretation
    interpretation += `\nExtraction Method: ${factorResults.extraction_method === 'pca' ? 'Principal Component Analysis' : 'Maximum Likelihood'}\n`;
    interpretation += `Number of Factors Extracted: ${factorResults.n_factors}\n`;
    interpretation += `Total Variance Explained: ${factorResults.cumulative_variance[factorResults.n_factors - 1]?.toFixed(1)}%\n`;

    if (factorResults.cumulative_variance[factorResults.n_factors - 1] >= 60) {
      interpretation += 'The extracted factors explain a substantial portion of the total variance.\n';
    } else {
      interpretation += 'The extracted factors explain a moderate portion of the total variance. Consider extracting additional factors.\n';
    }

    // Rotation interpretation
    if (factorResults.rotation_method !== 'none') {
      interpretation += `\nRotation Method: ${factorResults.rotation_method}\n`;
      if (factorResults.rotation_method === 'varimax') {
        interpretation += 'Varimax rotation was used to achieve simple structure while maintaining factor independence.\n';
      } else if (factorResults.rotation_method === 'promax') {
        interpretation += 'Promax rotation was used, allowing factors to correlate, which may be more realistic for your data.\n';
      } else if (factorResults.rotation_method === 'oblimin') {
        interpretation += 'Oblimin rotation was used, allowing oblique (correlated) factors.\n';
      }
    }

    // Communalities interpretation
    if (factorResults.communalities) {
      const communalityValues = Object.values(factorResults.communalities);
      const avgCommunality = communalityValues.reduce((a, b) => a + b, 0) / communalityValues.length;
      const lowCommunalities = Object.entries(factorResults.communalities)
        .filter(([_, value]) => value < 0.3)
        .map(([name, _]) => name);

      interpretation += `\nAverage Communality: ${avgCommunality.toFixed(3)}\n`;
      if (lowCommunalities.length > 0) {
        interpretation += `Variables with low communalities (< 0.3): ${lowCommunalities.join(', ')}\n`;
        interpretation += 'These variables may not be well-represented by the extracted factors.\n';
      }
    }

    return interpretation;
  };

  // Generate scree plot data
  const getScreePlotData = () => {
    if (!factorResults) return null;

    return {
      data: [{
        x: factorResults.eigenvalues.map((_, i) => i + 1),
        y: factorResults.eigenvalues,
        type: 'scatter',
        mode: 'lines+markers',
        name: 'Eigenvalues',
        line: { color: theme.palette.primary.main },
        marker: { size: 8 }
      }, {
        x: [0, factorResults.eigenvalues.length + 1],
        y: [1, 1],
        type: 'scatter',
        mode: 'lines',
        name: 'Kaiser Criterion (λ = 1)',
        line: {
          color: theme.palette.error.main,
          dash: 'dash'
        }
      }] as Data[], // Explicitly cast to Plotly.Data[]
      layout: {
        title: 'Scree Plot',
        xaxis: {
          title: 'Factor Number',
          dtick: 1
        },
        yaxis: { title: 'Eigenvalue' },
        hovermode: 'closest',
        showlegend: true,
        height: 400
      } as Partial<Layout> // Explicitly cast to Partial<Layout>
    };
  };

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Exploratory Factor Analysis
      </Typography>

      {initializingPython && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Box>
            <Typography variant="body2" gutterBottom>
              Initializing Python environment for statistical analysis...
            </Typography>
            <LinearProgress sx={{ mt: 1 }} />
          </Box>
        </Alert>
      )}

      {!pythonReady && !initializingPython && (
        <Alert severity="warning" sx={{ mb: 2 }} action={
          <Button color="inherit" size="small" onClick={initializePython} startIcon={<RefreshIcon />}>
            Retry
          </Button>
        }>
          Python environment not ready. Factor analysis requires Python libraries to be loaded.
        </Alert>
      )}

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Variables
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={8}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="variables-label">Variables (Select at least 3)</InputLabel>
              <Select
                labelId="variables-label"
                id="variables"
                multiple
                value={selectedVariables}
                label="Variables (Select at least 3)"
                onChange={handleVariableChange}
                disabled={!currentDataset}
                renderValue={(selected) => {
                  const selectedNames = selected.map(id => {
                    const column = numericColumns.find(col => col.id === id);
                    return column ? column.name : '';
                  }).filter(Boolean);
                  return (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selectedNames.map((name) => (
                        <Chip key={name} label={name} size="small" />
                      ))}
                    </Box>
                  );
                }}
              >
                {numericColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No numeric variables available
                  </MenuItem>
                ) : (
                  numericColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
              <Typography variant="caption" color="text.secondary">
                Factor analysis requires numeric variables. Select the variables you want to analyze for underlying factors.
              </Typography>
            </FormControl>
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        <Typography variant="subtitle1" gutterBottom>
          Analysis Configuration
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="extraction-method-label">Extraction Method</InputLabel>
              <Select
                labelId="extraction-method-label"
                id="extraction-method"
                value={extractionMethod}
                label="Extraction Method"
                onChange={handleExtractionMethodChange}
              >
                <MenuItem value="pca">Principal Component Analysis</MenuItem>
                <MenuItem value="ml">Maximum Likelihood</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="rotation-method-label">Rotation Method</InputLabel>
              <Select
                labelId="rotation-method-label"
                id="rotation-method"
                value={rotationMethod}
                label="Rotation Method"
                onChange={handleRotationMethodChange}
              >
                <MenuItem value="none">None</MenuItem>
                <MenuItem value="varimax">Varimax (Orthogonal)</MenuItem>
                <MenuItem value="promax">Promax (Oblique)</MenuItem>
                <MenuItem value="oblimin">Oblimin (Oblique)</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl component="fieldset" margin="normal">
              <FormLabel component="legend">Factor Selection Method</FormLabel>
              <RadioGroup
                row
                value={factorSelectionMethod}
                onChange={handleFactorSelectionMethodChange}
              >
                <FormControlLabel value="kaiser" control={<Radio />} label="Kaiser Criterion (λ > 1)" />
                <FormControlLabel value="scree" control={<Radio />} label="Scree Test" />
                <FormControlLabel value="manual" control={<Radio />} label="Manual" />
              </RadioGroup>
            </FormControl>

            {factorSelectionMethod === 'manual' && (
              <TextField
                label="Number of Factors"
                type="number"
                value={numberOfFactors}
                onChange={handleNumberOfFactorsChange}
                fullWidth
                margin="normal"
                inputProps={{ min: 1, max: selectedVariables.length }}
                helperText={`Enter a value between 1 and ${selectedVariables.length || 'N'}`}
              />
            )}
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        <Typography variant="subtitle1" gutterBottom>
          Display Options
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showScreePlot}
                    onChange={handleDisplayOptionChange('showScreePlot')}
                  />
                }
                label="Show Scree Plot"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showFactorMatrix}
                    onChange={handleDisplayOptionChange('showFactorMatrix')}
                  />
                }
                label="Show Unrotated Factor Matrix"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showRotatedMatrix}
                    onChange={handleDisplayOptionChange('showRotatedMatrix')}
                  />
                }
                label="Show Rotated Factor Matrix"
              />
            </FormGroup>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showCorrelationMatrix}
                    onChange={handleDisplayOptionChange('showCorrelationMatrix')}
                  />
                }
                label="Show Correlation Matrix"
              />
            </FormGroup>

            <TextField
              label="Minimum Loading to Display"
              type="number"
              value={displayOptions.minimumLoading}
              onChange={handleDisplayOptionChange('minimumLoading')}
              fullWidth
              margin="normal"
              inputProps={{ min: 0, max: 0.99, step: 0.05 }}
              helperText="Hide loadings below this threshold"
            />
          </Grid>
        </Grid>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AnalyticsIcon />}
            onClick={runFactorAnalysis}
            disabled={loading || !pythonReady || selectedVariables.length < 3}
          >
            {loading ? 'Running Analysis...' : 'Run Factor Analysis'}
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {factorResults && !loading && (
        <>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="factor analysis tabs">
                <Tab label="Summary" icon={<AssessmentIcon />} iconPosition="start" />
                <Tab label="Factor Loadings" icon={<TableChartIcon />} iconPosition="start" />
                <Tab label="Variance Explained" icon={<ShowChartIcon />} iconPosition="start" />
                <Tab label="Additional Results" icon={<FunctionsIcon />} iconPosition="start" />
                <Tab label="Interpretation" icon={<InfoIcon />} iconPosition="start" />
              </Tabs>
            </Box>

            {tabValue === 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Analysis Summary
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Data Adequacy Tests
                        </Typography>

                        <Box mb={2}>
                          <Typography variant="body2" gutterBottom>
                            Kaiser-Meyer-Olkin (KMO) Measure
                          </Typography>
                          <Typography variant="h5">
                            {factorResults.kmo.toFixed(3)}
                          </Typography>
                          <Typography variant="body2" color={
                            factorResults.kmo >= 0.8 ? 'success.main' :
                            factorResults.kmo >= 0.6 ? 'warning.main' : 'error.main'
                          }>
                            {factorResults.kmo >= 0.9 ? 'Marvelous' :
                             factorResults.kmo >= 0.8 ? 'Meritorious' :
                             factorResults.kmo >= 0.7 ? 'Middling' :
                             factorResults.kmo >= 0.6 ? 'Mediocre' : 'Miserable'}
                          </Typography>
                        </Box>

                        <Divider sx={{ my: 2 }} />

                        <Box>
                          <Typography variant="body2" gutterBottom>
                            Bartlett's Test of Sphericity
                          </Typography>
                          <Typography variant="body1">
                            χ² = {factorResults.bartlett_chi_square.toFixed(2)}
                          </Typography>
                          <Typography variant="body2" color={
                            factorResults.bartlett_p_value < 0.05 ? 'success.main' : 'error.main'
                          }>
                            p = {factorResults.bartlett_p_value < 0.001 ? '< 0.001' : factorResults.bartlett_p_value.toFixed(4)}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Model Information
                        </Typography>

                        <TableContainer>
                          <Table size="small">
                            <TableBody>
                              <TableRow>
                                <TableCell>Extraction Method</TableCell>
                                <TableCell>
                                  {factorResults.extraction_method === 'pca' ?
                                   'Principal Component Analysis' : 'Maximum Likelihood'}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Rotation Method</TableCell>
                                <TableCell>{factorResults.rotation_method}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Number of Factors</TableCell>
                                <TableCell>{factorResults.n_factors}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Number of Variables</TableCell>
                                <TableCell>{Object.keys(factorResults.communalities).length}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Number of Observations</TableCell>
                                <TableCell>{factorResults.n_observations}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Total Variance Explained</TableCell>
                                <TableCell>
                                  {factorResults.cumulative_variance[factorResults.n_factors - 1]?.toFixed(1)}%
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Box mt={3}>
                  <Button
                    variant="outlined"
                    startIcon={<ScoreIcon />}
                    onClick={calculateFactorScores}
                    sx={{ mr: 2 }}
                  >
                    Calculate Factor Scores
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={() => setShowExportDialog(true)}
                  >
                    Export Results
                  </Button>
                </Box>
              </Box>
            )}

            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Factor Loadings
                </Typography>

                {displayOptions.showRotatedMatrix && factorResults.rotated_loadings && (
                  <Box mb={3}>
                    <Typography variant="subtitle2" gutterBottom>
                      Rotated Factor Matrix ({factorResults.rotation_method})
                    </Typography>

                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Variable</TableCell>
                            {Array.from({ length: factorResults.n_factors }, (_, i) => (
                              <TableCell key={i} align="right">
                                Factor {i + 1}
                              </TableCell>
                            ))}
                            {displayOptions.showCommunalities && (
                              <TableCell align="right">Communality</TableCell>
                            )}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {Object.keys(factorResults.communalities).map((varName, varIndex) => (
                            <TableRow key={varName}>
                              <TableCell>{varName}</TableCell>
                              {Array.from({ length: factorResults.n_factors }, (_, factorIndex) => {
                                const loading = factorResults.loadings![varIndex][factorIndex];
                                const absLoading = Math.abs(loading);
                                const isSignificant = absLoading >= displayOptions.minimumLoading;
                                const isStrong = absLoading >= 0.7;
                                const isModerate = absLoading >= 0.4;

                                return (
                                  <TableCell key={factorIndex} align="right" sx={{
                                    fontWeight: isStrong ? 'bold' : isModerate ? 'medium' : 'normal',
                                    color: !isSignificant ? 'text.disabled' :
                                           isStrong ? 'primary.main' :
                                           isModerate ? 'text.primary' : 'text.secondary'
                                  }}>
                                    {isSignificant ? loading.toFixed(3) : '-'}
                                  </TableCell>
                                );
                              })}
                              {displayOptions.showCommunalities && (
                                <TableCell align="right">
                                  {factorResults.communalities[varName].toFixed(3)}
                                </TableCell>
                              )}
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    <Box mt={1}>
                      <Typography variant="caption" color="text.secondary">
                        Loadings below {displayOptions.minimumLoading} are suppressed.
                        Strong loadings (≥ 0.7) are highlighted.
                      </Typography>
                    </Box>
                  </Box>
                )}

                {displayOptions.showFactorMatrix && factorResults.loadings && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Unrotated Factor Matrix
                    </Typography>

                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Variable</TableCell>
                            {Array.from({ length: factorResults.n_factors }, (_, i) => (
                              <TableCell key={i} align="right">
                                Factor {i + 1}
                              </TableCell>
                            ))}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {Object.keys(factorResults.communalities).map((varName, varIndex) => (
                            <TableRow key={varName}>
                              <TableCell>{varName}</TableCell>
                              {Array.from({ length: factorResults.n_factors }, (_, factorIndex) => {
                                const loading = factorResults.loadings[varIndex][factorIndex];
                                const absLoading = Math.abs(loading);
                                const isSignificant = absLoading >= displayOptions.minimumLoading;

                                return (
                                  <TableCell key={factorIndex} align="right" sx={{
                                    color: !isSignificant ? 'text.disabled' : 'text.primary'
                                  }}>
                                    {isSignificant ? loading.toFixed(3) : '-'}
                                  </TableCell>
                                );
                              })}
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>
                )}

                {factorResults.factor_correlations && rotationMethod !== 'varimax' && (
                  <Box mt={3}>
                    <Typography variant="subtitle2" gutterBottom>
                      Factor Correlation Matrix
                    </Typography>

                    <TableContainer component={Paper} variant="outlined" sx={{ maxWidth: 600 }}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Factor</TableCell>
                            {Array.from({ length: factorResults.n_factors }, (_, i) => (
                              <TableCell key={i} align="right">
                                {i + 1}
                              </TableCell>
                            ))}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {factorResults.factor_correlations.map((row, rowIndex) => (
                            <TableRow key={rowIndex}>
                              <TableCell>{rowIndex + 1}</TableCell>
                              {row.map((corr, colIndex) => (
                                <TableCell key={colIndex} align="right" sx={{
                                  fontWeight: rowIndex === colIndex ? 'bold' : 'normal',
                                  color: Math.abs(corr) > 0.3 && rowIndex !== colIndex ?
                                         'warning.main' : 'text.primary'
                                }}>
                                  {corr.toFixed(3)}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    <Box mt={1}>
                      <Typography variant="caption" color="text.secondary">
                        Correlations above 0.3 are highlighted as they may indicate factor overlap.
                      </Typography>
                    </Box>
                  </Box>
                )}
              </Box>
            )}

            {tabValue === 2 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Variance Explained
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Factor</TableCell>
                            <TableCell align="right">Eigenvalue</TableCell>
                            <TableCell align="right">Variance %</TableCell>
                            <TableCell align="right">Cumulative %</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {factorResults.eigenvalues.map((eigen, index) => (
                            <TableRow key={index} sx={{
                              backgroundColor: index < factorResults.n_factors ?
                                             'action.selected' : 'transparent'
                            }}>
                              <TableCell>{index + 1}</TableCell>
                              <TableCell align="right">{eigen.toFixed(3)}</TableCell>
                              <TableCell align="right">
                                {factorResults.variance_explained[index].toFixed(1)}%
                              </TableCell>
                              <TableCell align="right">
                                {factorResults.cumulative_variance[index].toFixed(1)}%
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>

                  {displayOptions.showScreePlot && (
                    <Grid item xs={12} md={6}>
                      <Plot
                        {...getScreePlotData()!}
                        config={{ displayModeBar: false }}
                        style={{ width: '100%', height: '100%' }}
                      />
                    </Grid>
                  )}
                </Grid>
              </Box>
            )}

            {tabValue === 3 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Additional Results
                </Typography>

                {displayOptions.showCommunalities && (
                  <Box mb={3}>
                    <Typography variant="subtitle2" gutterBottom>
                      Communalities
                    </Typography>

                    <Grid container spacing={2}>
                      {Object.entries(factorResults.communalities).map(([varName, comm]) => (
                        <Grid item xs={12} sm={6} md={4} key={varName}>
                          <Box sx={{
                            p: 2,
                            border: 1,
                            borderColor: comm < 0.3 ? 'error.main' : 'divider',
                            borderRadius: 1,
                            backgroundColor: comm < 0.3 ? 'error.lighter' : 'background.paper'
                          }}>
                            <Typography variant="body2" color="text.secondary">
                              {varName}
                            </Typography>
                            <Typography variant="h6" color={comm < 0.3 ? 'error.main' : 'text.primary'}>
                              {comm.toFixed(3)}
                            </Typography>
                          </Box>
                        </Grid>
                      ))}
                    </Grid>

                    <Box mt={2}>
                      <Typography variant="body2" color="text.secondary">
                        Communalities represent the proportion of variance in each variable explained by the extracted factors.
                        Values below 0.3 (highlighted) suggest the variable may not fit well with the factor structure.
                      </Typography>
                    </Box>
                  </Box>
                )}

                {displayOptions.showCorrelationMatrix && factorResults.correlation_matrix && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Correlation Matrix
                    </Typography>

                    <TableContainer component={Paper} variant="outlined" sx={{ maxWidth: '100%', overflowX: 'auto' }}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Variable</TableCell>
                            {Object.keys(factorResults.communalities).map((varName) => (
                              <TableCell key={varName} align="right">
                                {varName}
                              </TableCell>
                            ))}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {Object.keys(factorResults.communalities).map((rowVar, rowIndex) => (
                            <TableRow key={rowVar}>
                              <TableCell>{rowVar}</TableCell>
                              {factorResults.correlation_matrix[rowIndex].map((corr, colIndex) => (
                                <TableCell key={colIndex} align="right" sx={{
                                  backgroundColor: rowIndex === colIndex ? 'action.hover' :
                                                  Math.abs(corr) > 0.7 ? 'warning.lighter' :
                                                  Math.abs(corr) > 0.5 ? 'info.lighter' : 'transparent',
                                  fontWeight: rowIndex === colIndex ? 'bold' : 'normal'
                                }}>
                                  {corr.toFixed(3)}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    <Box mt={1}>
                      <Typography variant="caption" color="text.secondary">
                          Strong correlations (|r| &gt; 0.7) are highlighted in orange,
                          moderate correlations (|r| &gt; 0.5) in blue.
                      </Typography>
                    </Box>
                  </Box>
                )}
              </Box>
            )}

            {tabValue === 4 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Interpretation Guide
                </Typography>

                <Paper elevation={0} variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'background.paper' }}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                    {getInterpretation()}
                  </Typography>
                </Paper>

                <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
                  Understanding Factor Analysis
                </Typography>

                <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                  <Typography variant="body2" fontWeight="bold">
                    KMO and Bartlett's Test
                  </Typography>
                  <Typography variant="body2">
                  KMO values &gt; 0.8 are considered good, &gt; 0.6 acceptable. Bartlett's test should be significant (p &lt; 0.05) to proceed with factor analysis.
                  </Typography>

                  <Typography variant="body2" fontWeight="bold" sx={{ mt: 2 }}>
                    Factor Extraction
                  </Typography>
                  <Typography variant="body2">
Factors are extracted based on eigenvalues. Common criteria include eigenvalues &gt 1 (Kaiser criterion) or examining the scree plot for the "elbow" point.
                  </Typography>

                  <Typography variant="body2" fontWeight="bold" sx={{ mt: 2 }}>
                    Factor Loadings
                  </Typography>
                  <Typography variant="body2">
                      Loadings represent correlations between variables and factors. Values &gt 0.4 are typically considered meaningful, &gt 0.7 are strong.
                  </Typography>

                  <Typography variant="body2" fontWeight="bold" sx={{ mt: 2 }}>
                    Communalities
                  </Typography>
<Typography variant="body2">
  Communalities show the proportion of variance in each variable explained by the factors. Values &lt 0.3 suggest the variable may not fit well.
</Typography>

                  <Typography variant="body2" fontWeight="bold" sx={{ mt: 2 }}>
                    Rotation Methods
                  </Typography>
                  <Typography variant="body2" paragraph>
                    • Varimax: Orthogonal rotation that maximizes variance of squared loadings (factors remain uncorrelated)
                  </Typography>
                  <Typography variant="body2" paragraph>
                    • Promax: Oblique rotation allowing factors to correlate (more realistic for psychological constructs)
                  </Typography>
                  <Typography variant="body2">
                    • Oblimin: Another oblique rotation method with adjustable delta parameter
                  </Typography>
                </Paper>
              </Box>
            )}
          </Paper>
        </>
      )}

      {/* Factor Scores Dialog */}
      <Dialog
        open={showFactorScoresDialog}
        onClose={() => setShowFactorScoresDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Factor Scores</DialogTitle>
        <DialogContent>
          {factorScores && (
            <>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Factor scores represent each observation's position on the extracted factors.
              </Typography>

              <TableContainer component={Paper} variant="outlined" sx={{ mt: 2, maxHeight: 400 }}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Observation</TableCell>
                      {Array.from({ length: factorScores.scores[0]?.length || 0 }, (_, i) => (
                        <TableCell key={i} align="right">
                          Factor {i + 1}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {factorScores.scores.slice(0, 100).map((scores, index) => (
                      <TableRow key={index}>
                        <TableCell>{index + 1}</TableCell>
                        {scores.map((score, factorIndex) => (
                          <TableCell key={factorIndex} align="right">
                            {score.toFixed(3)}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                    {factorScores.scores.length > 100 && (
                      <TableRow>
                        <TableCell colSpan={factorScores.scores[0]?.length + 1} align="center">
                          ... and {factorScores.scores.length - 100} more rows
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box mt={2}>
                <Button
                  variant="outlined"
                  onClick={exportFactorScores}
                  startIcon={<DownloadIcon />}
                >
                  Export Factor Scores as CSV
                </Button>
              </Box>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowFactorScoresDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Export Results Dialog */}
      <Dialog
        open={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Export Results</DialogTitle>
        <DialogContent>
          <FormControl component="fieldset">
            <FormLabel component="legend">Select items to export:</FormLabel>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.summary}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, summary: e.target.checked }))}
                  />
                }
                label="Summary Statistics"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.loadings}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, loadings: e.target.checked }))}
                  />
                }
                label="Factor Loadings"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.communalities}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, communalities: e.target.checked }))}
                  />
                }
                label="Communalities"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.eigenvalues}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, eigenvalues: e.target.checked }))}
                  />
                }
                label="Eigenvalues"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.correlationMatrix}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, correlationMatrix: e.target.checked }))}
                  />
                }
                label="Show Correlation Matrix"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={exportOptions.factorCorrelations}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, factorCorrelations: e.target.checked }))}
                    disabled={factorResults?.rotation_method === 'varimax'}
                  />
                }
                label="Factor Correlations (Oblique only)"
              />
            </FormGroup>
          </FormControl>

          <Box mt={2}>
            <Typography variant="body2" color="text.secondary">
              Results will be exported as a formatted text file.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowExportDialog(false)}>Cancel</Button>
          <Button onClick={exportResults} variant="contained" startIcon={<DownloadIcon />}>
            Export
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
export default ExploratoryFactorAnalysis;
