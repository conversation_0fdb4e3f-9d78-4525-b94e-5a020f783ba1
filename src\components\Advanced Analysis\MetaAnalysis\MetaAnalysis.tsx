import React, { useState, useEffect, useRef, useCallback } from 'react';
import * as d3 from 'd3';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  FormControlLabel,
  Checkbox,
  Divider,
  RadioGroup,
  Radio,
  Chip,
  Tabs,
  Tab
} from '@mui/material';
import {
  Forest as ForestIcon,
  Help as HelpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext'; // Adjust path as needed
import { DataType } from '../../../types'; // Adjust path as needed

// Types for meta-analysis
interface StudyData {
  id: string;
  name: string;
  originalEffectSize: number;
  originalStandardError: number;
  effectSize: number;
  standardError: number;
  sampleSize: number;
  weight?: number;
  ci_lower?: number;
  ci_upper?: number;
  subgroup?: string;
  problematic?: boolean;
}

interface MetaAnalysisResults {
  model: 'fixed' | 'random';
  effectMeasure: EffectMeasureType;
  pooledEffect: number;
  pooledSE: number;
  pooledCI: [number, number];
  pooledPValue: number;
  heterogeneity: {
    Q: number;
    df: number;
    pValue: number;
    I2: number;
    tau2: number;
  };
  studyWeights: { [studyId: string]: number };
  forestPlotData: ForestPlotStudyData[];
  funnelPlotData: FunnelPlotStudyData[];
  subgroupAnalysis?: {
    [subgroup: string]: {
      pooledEffect: number;
      pooledSE: number;
      pooledCI: [number, number];
      studies: string[];
      numStudies: number;
    };
  };
  publicationBias: {
    eggerTest?: {
      intercept: number;
      pValue: number;
      significant: boolean;
      sufficientStudies: boolean;
    };
  };
  excludedStudiesCount: number;
}

interface ForestPlotStudyData {
  name: string;
  effectSize: number;
  ci_lower: number;
  ci_upper: number;
  weight: number;
  standardError?: number; // <<< CORRECTED: Made optional
  subgroup?: string;
  isSummary?: boolean;
}

interface FunnelPlotStudyData {
  name: string;
  effectSize: number;
  standardError: number;
  precision?: number;
}

type EffectMeasureType = 'MD' | 'SMD' | 'OR' | 'RR' | 'HR';

const EFFECT_MEASURE_INFO: Record<EffectMeasureType, { name: string; logTransform: boolean; nullValue: number }> = {
  MD: { name: 'Mean Difference', logTransform: false, nullValue: 0 },
  SMD: { name: 'Standardized Mean Difference', logTransform: false, nullValue: 0 },
  OR: { name: 'Odds Ratio', logTransform: true, nullValue: 1 },
  RR: { name: 'Risk Ratio', logTransform: true, nullValue: 1 },
  HR: { name: 'Hazard Ratio', logTransform: true, nullValue: 1 },
};

const getZScore = (confLevel: number): number => {
  const alpha = 1 - confLevel;
  const p = 1 - alpha / 2;
  if (confLevel === 0.90) return 1.645;
  if (confLevel === 0.95) return 1.96;
  if (confLevel === 0.99) return 2.576;
  try {
    // This part requires d3-random to be available. If using modular d3, ensure it's included.
    // @ts-ignore d3.randomNormal might not be recognized if d3 types are minimal
    return d3.quantile(d3.range(1000).map(d3.randomNormal()), p) || 1.96;
  } catch (e) {
    console.warn("d3.randomNormal or d3.quantile not available for Z-score calculation, falling back to 1.96.", e);
    return 1.96; // Fallback
  }
};

const normalCDF = (z: number): number => {
  const b1 =  0.319381530;
  const b2 = -0.356563782;
  const b3 =  1.781477937;
  const b4 = -1.821255978;
  const b5 =  1.330274429;
  const p  =  0.2316419;
  const c  =  0.39894228;
  if (z >= 0.0) {
      const t = 1.0 / ( 1.0 + p * z );
      return (1.0 - c * Math.exp( -z * z / 2.0 ) * t * ( t * ( t * ( t * ( t * b5 + b4 ) + b3 ) + b2 ) + b1 ));
  } else {
      const t = 1.0 / ( 1.0 - p * z );
      return ( c * Math.exp( -z * z / 2.0 ) * t * ( t * ( t * ( t * ( t * b5 + b4 ) + b3 ) + b2 ) + b1 ));
  }
};

const chiSquareCDF = (x: number, df: number): number => {
  if (df <= 0 || x < 0) return 0;
  if (df === 1 && x > 0) return 2 * normalCDF(Math.sqrt(x)) -1;
  const z = (Math.pow(x / df, 1/3) - (1 - 2/(9*df))) / Math.sqrt(2/(9*df));
  return normalCDF(z);
};


const MetaAnalysis: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();

  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [studyNameVar, setStudyNameVar] = useState<string>('');
  const [effectSizeVar, setEffectSizeVar] = useState<string>('');
  const [standardErrorVar, setStandardErrorVar] = useState<string>('');
  const [sampleSizeVar, setSampleSizeVar] = useState<string>('');
  const [subgroupVar, setSubgroupVar] = useState<string>('');

  const [analysisModel, setAnalysisModel] = useState<'fixed' | 'random'>('random');
  const [confLevel, setConfLevel] = useState<number>(0.95);
  const [effectMeasure, setEffectMeasure] = useState<EffectMeasureType>('MD');
  const [activeTab, setActiveTab] = useState<number>(0);

  const [displayOptions, setDisplayOptions] = useState({
    showForestPlot: true,
    showFunnelPlot: true,
    showWeights: true,
    showHeterogeneity: true,
    showSubgroupAnalysis: true,
    showPublicationBias: true,
  });

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [warning, setWarning] = useState<string | null>(null);
  const [metaResults, setMetaResults] = useState<MetaAnalysisResults | null>(null);

  useEffect(() => {
    const savedResults = localStorage.getItem(`meta_analysis_results_${selectedDatasetId}`);
    if (savedResults) {
      try {
        const parsedResults = JSON.parse(savedResults) as MetaAnalysisResults;
        if (parsedResults.pooledEffect !== undefined && parsedResults.forestPlotData) {
          setMetaResults(parsedResults);
          setAnalysisModel(parsedResults.model);
          setEffectMeasure(parsedResults.effectMeasure);
        } else {
          localStorage.removeItem(`meta_analysis_results_${selectedDatasetId}`);
        }
      } catch (e) {
        console.error('Error parsing saved meta-analysis results:', e);
        localStorage.removeItem(`meta_analysis_results_${selectedDatasetId}`);
      }
    }
  }, [selectedDatasetId]);
  
  const resetResults = useCallback(() => {
    setMetaResults(null);
    if (selectedDatasetId) {
      localStorage.removeItem(`meta_analysis_results_${selectedDatasetId}`);
    }
  }, [selectedDatasetId]);

  const stringColumns = currentDataset?.columns.filter(
    col => col.type === DataType.TEXT || col.type === DataType.CATEGORICAL
  ) || [];

  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];

  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setStudyNameVar(''); setEffectSizeVar(''); setStandardErrorVar('');
    setSampleSizeVar(''); setSubgroupVar('');
    resetResults();
    const selectedDs = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDs) setCurrentDataset(selectedDs);
  };
  
  const createSelectHandler = (setter: React.Dispatch<React.SetStateAction<string>>) => (event: SelectChangeEvent<string>) => {
    setter(event.target.value);
    resetResults();
  };
  const handleStudyNameChange = createSelectHandler(setStudyNameVar);
  const handleEffectSizeChange = createSelectHandler(setEffectSizeVar);
  const handleStandardErrorChange = createSelectHandler(setStandardErrorVar);
  const handleSampleSizeChange = createSelectHandler(setSampleSizeVar);
  const handleSubgroupChange = createSelectHandler(setSubgroupVar);

  const handleModelChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAnalysisModel(event.target.value as 'fixed' | 'random');
    resetResults();
  };

  const handleConfLevelChange = (event: SelectChangeEvent<unknown>) => {
    setConfLevel(Number(event.target.value));
    resetResults();
  };

  const handleEffectMeasureChange = (event: SelectChangeEvent<EffectMeasureType>) => {
    setEffectMeasure(event.target.value as EffectMeasureType);
    resetResults();
  };

  const handleDisplayOptionChange = (option: keyof typeof displayOptions) => {
    setDisplayOptions(prev => ({ ...prev, [option]: !prev[option] }));
  };

  const performMetaAnalysis = (
    studiesInput: StudyData[],
    currentAnalysisModel: 'fixed' | 'random',
    currentConfLevel: number,
    currentEffectMeasure: EffectMeasureType
  ): MetaAnalysisResults => {
    let studies = [...studiesInput];
    let excludedStudiesCount = 0;
    const { logTransform } = EFFECT_MEASURE_INFO[currentEffectMeasure];

    if (logTransform) {
      studies = studies.map(s => {
        if (s.originalEffectSize <= 0) { excludedStudiesCount++; return { ...s, problematic: true }; }
        if (s.originalStandardError <= 0) { excludedStudiesCount++; return { ...s, problematic: true }; }
        return { ...s, effectSize: Math.log(s.originalEffectSize), standardError: s.originalStandardError / s.originalEffectSize };
      }).filter(s => !s.problematic);
    } else {
        studies = studies.map(s => {
        if (s.originalStandardError <= 0) { excludedStudiesCount++; return { ...s, problematic: true }; }
        return s;
      }).filter(s => !s.problematic);
    }
    
    if (studies.length < 2) {
      throw new Error(`At least 2 valid studies are required after filtering. ${excludedStudiesCount > 0 ? `${excludedStudiesCount} excluded.` : ''}`);
    }

    const n = studies.length;
    const zCrit = getZScore(currentConfLevel);

    let weightsFixed: { [studyId: string]: number } = {};
    studies.forEach(study => { weightsFixed[study.id] = 1 / (study.standardError ** 2); });
    const totalWeightFixed = Object.values(weightsFixed).reduce((sum, w) => sum + w, 0);
    const pooledEffectFixed_transformed = studies.reduce((sum, study) => sum + study.effectSize * weightsFixed[study.id], 0) / totalWeightFixed;

    const Q = studies.reduce((sum, study) => sum + weightsFixed[study.id] * Math.pow(study.effectSize - pooledEffectFixed_transformed, 2), 0);
    const df = Math.max(1, n - 1);
    const pValue_Q = 1 - chiSquareCDF(Q, df);
    const I2 = (Q > 0 && df < Q) ? ((Q - df) / Q) * 100 : 0; // Ensure Q > 0 for I2 calculation
    let tau2 = 0;

    let finalWeights: { [studyId: string]: number } = {};
    if (currentAnalysisModel === 'random' && Q > df) {
      const C = totalWeightFixed - (studies.reduce((sum, study) => sum + weightsFixed[study.id] ** 2, 0) / totalWeightFixed);
      if (C > 0) { tau2 = Math.max(0, (Q - df) / C); }
      studies.forEach(study => { finalWeights[study.id] = 1 / (study.standardError ** 2 + tau2); });
    } else {
      finalWeights = weightsFixed;
    }

    const totalWeightFinal = Object.values(finalWeights).reduce((sum, w) => sum + w, 0);
    const pooledEffect_transformed = studies.reduce((sum, study) => sum + study.effectSize * finalWeights[study.id], 0) / totalWeightFinal;
    const pooledVariance_transformed = 1 / totalWeightFinal;
    const pooledSE_transformed = Math.sqrt(pooledVariance_transformed);

    let finalPooledEffect: number;
    let finalPooledCI: [number, number];

    if (logTransform) {
      finalPooledEffect = Math.exp(pooledEffect_transformed);
      finalPooledCI = [ Math.exp(pooledEffect_transformed - zCrit * pooledSE_transformed), Math.exp(pooledEffect_transformed + zCrit * pooledSE_transformed) ];
    } else {
      finalPooledEffect = pooledEffect_transformed;
      finalPooledCI = [ pooledEffect_transformed - zCrit * pooledSE_transformed, pooledEffect_transformed + zCrit * pooledSE_transformed ];
    }

    const zStat_pooled = pooledEffect_transformed / pooledSE_transformed;
    const pooledPValue = 2 * (1 - normalCDF(Math.abs(zStat_pooled)));

    const forestPlotData: ForestPlotStudyData[] = studies.map(study => {
      const weightPercent = (finalWeights[study.id] / totalWeightFinal) * 100;
      let ci_lower_display, ci_upper_display;
      if (logTransform) {
        ci_lower_display = Math.exp(study.effectSize - zCrit * study.standardError);
        ci_upper_display = Math.exp(study.effectSize + zCrit * study.standardError);
      } else {
        ci_lower_display = study.effectSize - zCrit * study.standardError;
        ci_upper_display = study.effectSize + zCrit * study.standardError;
      }
      return {
        name: study.name,
        effectSize: study.originalEffectSize,
        ci_lower: ci_lower_display,
        ci_upper: ci_upper_display,
        weight: weightPercent,
        standardError: study.originalStandardError,
        subgroup: study.subgroup,
      };
    });

    forestPlotData.push({
      name: `Pooled Effect (${currentAnalysisModel === 'fixed' ? 'Fixed' : 'Random'})`,
      effectSize: finalPooledEffect,
      ci_lower: finalPooledCI[0],
      ci_upper: finalPooledCI[1],
      weight: 100,
      isSummary: true,
      subgroup: 'Overall',
      standardError: undefined, // Valid due to 'standardError?: number'
    });

    const funnelPlotData: FunnelPlotStudyData[] = studies.map(study => ({
      name: study.name,
      effectSize: study.originalEffectSize,
      standardError: study.originalStandardError,
      precision: study.originalStandardError > 0 ? 1 / study.originalStandardError : undefined,
    }));

    let subgroupAnalysisResults: MetaAnalysisResults['subgroupAnalysis'] = undefined;
    if (subgroupVar && studies.some(s => s.subgroup)) {
      subgroupAnalysisResults = {};
      const uniqueSubgroups = [...new Set(studies.map(s => s.subgroup).filter(Boolean) as string[])];
      uniqueSubgroups.forEach(subgroupName => {
        const subgroupStudies = studies.filter(s => s.subgroup === subgroupName);
        if (subgroupStudies.length > 0) {
          let sub_weights: { [id: string]: number } = {};
          if (currentAnalysisModel === 'random' && tau2 > 0) {
            subgroupStudies.forEach(s => sub_weights[s.id] = 1 / (s.standardError ** 2 + tau2));
          } else {
            subgroupStudies.forEach(s => sub_weights[s.id] = 1 / (s.standardError ** 2));
          }
          const sub_totalWeight = Object.values(sub_weights).reduce((sum, w) => sum + w, 0);
          if (sub_totalWeight > 0) {
            const sub_pooledEffect_transformed = subgroupStudies.reduce((sum, s) => sum + s.effectSize * sub_weights[s.id], 0) / sub_totalWeight;
            const sub_pooledSE_transformed = Math.sqrt(1 / sub_totalWeight);
            let sub_finalPooledEffect, sub_finalCI: [number,number];
            if (logTransform) {
              sub_finalPooledEffect = Math.exp(sub_pooledEffect_transformed);
              sub_finalCI = [ Math.exp(sub_pooledEffect_transformed - zCrit * sub_pooledSE_transformed), Math.exp(sub_pooledEffect_transformed + zCrit * sub_pooledSE_transformed)];
            } else {
              sub_finalPooledEffect = sub_pooledEffect_transformed;
              sub_finalCI = [ sub_pooledEffect_transformed - zCrit * sub_pooledSE_transformed, sub_pooledEffect_transformed + zCrit * sub_pooledSE_transformed ];
            }
            subgroupAnalysisResults![subgroupName] = {
              pooledEffect: sub_finalPooledEffect, pooledSE: sub_pooledSE_transformed,
              pooledCI: sub_finalCI, studies: subgroupStudies.map(s => s.id), numStudies: subgroupStudies.length,
            };
          }
        }
      });
    }

    const publicationBias: MetaAnalysisResults['publicationBias'] = {};
    const sufficientStudiesForEgger = n >= 10;
    if (sufficientStudiesForEgger) {
      const x_egger = studies.map(s => s.standardError);
      const y_egger = studies.map(s => s.effectSize);
      const w_egger = studies.map(s => 1 / (s.standardError ** 2));
      let sum_w = 0, sum_wx = 0, sum_wy = 0, sum_wxx = 0, sum_wxy = 0;
      for (let i = 0; i < n; i++) {
        sum_w += w_egger[i]; sum_wx += w_egger[i] * x_egger[i]; sum_wy += w_egger[i] * y_egger[i];
        sum_wxx += w_egger[i] * x_egger[i] * x_egger[i]; sum_wxy += w_egger[i] * x_egger[i] * y_egger[i];
      }
      const denominator = (sum_w * sum_wxx - sum_wx * sum_wx);
      if (Math.abs(denominator) > 1e-9) { // Avoid division by zero/very small numbers
        const slope = (sum_w * sum_wxy - sum_wx * sum_wy) / denominator;
        const intercept = (sum_wy - slope * sum_wx) / sum_w;
        const y_hat = x_egger.map(x_val => intercept + slope * x_val);
        const residuals_sq_sum = studies.reduce((sum, s, i) => sum + w_egger[i] * Math.pow(y_egger[i] - y_hat[i], 2), 0);
        const mse = (n - 2 > 0) ? residuals_sq_sum / (n - 2) : 0;
        const se_intercept = Math.sqrt(mse * (sum_wxx / denominator));
        if (!isNaN(se_intercept) && se_intercept > 0) {
          const tStat_intercept = intercept / se_intercept;
          const pValue_egger = 2 * (1 - normalCDF(Math.abs(tStat_intercept)));
          publicationBias.eggerTest = { intercept, pValue: pValue_egger, significant: pValue_egger < 0.10, sufficientStudies: true };
        } else {
          publicationBias.eggerTest = { intercept: NaN, pValue: NaN, significant: false, sufficientStudies: true };
        }
      } else {
        publicationBias.eggerTest = { intercept: NaN, pValue: NaN, significant: false, sufficientStudies: true, note: "Denominator too small for Egger's test." } as any;
      }
    } else {
      publicationBias.eggerTest = { intercept: NaN, pValue: NaN, significant: false, sufficientStudies: false };
    }

    return {
      model: currentAnalysisModel, effectMeasure: currentEffectMeasure, pooledEffect: finalPooledEffect,
      pooledSE: pooledSE_transformed, pooledCI: finalPooledCI, pooledPValue,
      heterogeneity: { Q, df, pValue: pValue_Q, I2, tau2 },
      studyWeights: finalWeights, forestPlotData, funnelPlotData,
      subgroupAnalysis: subgroupAnalysisResults, publicationBias, excludedStudiesCount,
    };
  };

  const runMetaAnalysis = () => {
    if (!currentDataset || !studyNameVar || !effectSizeVar || !standardErrorVar) {
      setError('Please select: Study Name, Effect Size, and Standard Error.');
      setWarning(null); return;
    }
    setLoading(true); setError(null); setWarning(null); resetResults();
    try {
      const studyNameCol = currentDataset.columns.find(col => col.id === studyNameVar);
      const effectSizeCol = currentDataset.columns.find(col => col.id === effectSizeVar);
      const standardErrorCol = currentDataset.columns.find(col => col.id === standardErrorVar);
      const sampleSizeCol = sampleSizeVar ? currentDataset.columns.find(col => col.id === sampleSizeVar) : null;
      const subgroupCol = subgroupVar ? currentDataset.columns.find(col => col.id === subgroupVar) : null;
      if (!studyNameCol || !effectSizeCol || !standardErrorCol) throw new Error('Selected variables not found.');
      
      const studiesRaw: StudyData[] = [];
      currentDataset.data.forEach((row, index) => {
        const studyName = row[studyNameCol.name]; const effectSizeVal = row[effectSizeCol.name];
        const standardErrorVal = row[standardErrorCol.name]; const sampleSizeVal = sampleSizeCol ? row[sampleSizeCol.name] : null;
        const subgroupVal = subgroupCol ? row[subgroupCol.name] : null;
        if ( studyName != null && studyName !== '' && typeof effectSizeVal === 'number' && !isNaN(effectSizeVal) && typeof standardErrorVal === 'number' && !isNaN(standardErrorVal) ) {
          studiesRaw.push({
            id: `s_${index}_${String(studyName).slice(0,10).replace(/\W/g, '')}`, name: String(studyName),
            originalEffectSize: effectSizeVal, originalStandardError: standardErrorVal,
            effectSize: effectSizeVal, standardError: standardErrorVal,
            sampleSize: (typeof sampleSizeVal === 'number' && !isNaN(sampleSizeVal)) ? sampleSizeVal : 0,
            subgroup: subgroupVal ? String(subgroupVal) : undefined,
          });
        }
      });
      if (studiesRaw.length < 2) throw new Error('At least 2 studies with valid numeric ES and SE required.');
      const results = performMetaAnalysis(studiesRaw, analysisModel, confLevel, effectMeasure);
      setMetaResults(results);
      if (results.excludedStudiesCount > 0) setWarning(`${results.excludedStudiesCount} studies excluded due to invalid data for chosen effect measure.`);
      localStorage.setItem(`meta_analysis_results_${selectedDatasetId}`, JSON.stringify(results));
    } catch (err) {
      setError(`Analysis Error: ${err instanceof Error ? err.message : String(err)}`);
      console.error(err);
    } finally { setLoading(false); }
  };

  const getInterpretation = () => {
    if (!metaResults) return '';
    const { pooledEffect, pooledCI, pooledPValue, heterogeneity, model, effectMeasure: currentEffectMeasure, publicationBias, subgroupAnalysis } = metaResults;
    const { name: emName, logTransform, nullValue } = EFFECT_MEASURE_INFO[currentEffectMeasure];
    const confLevelText = `${(confLevel * 100)}%`;
    let interpretation = `A ${model === 'fixed' ? 'fixed-effect' : 'random-effects'} meta-analysis was conducted on ${metaResults.forestPlotData.length - 1 - metaResults.excludedStudiesCount} studies to estimate the pooled ${emName.toLowerCase()}.`;
    if (metaResults.excludedStudiesCount > 0) interpretation += ` (${metaResults.excludedStudiesCount} studies excluded).`;
    interpretation += `\n\nThe pooled ${emName.toLowerCase()} was ${pooledEffect.toFixed(3)} (${confLevelText} CI: ${pooledCI[0].toFixed(3)} to ${pooledCI[1].toFixed(3)}). `;
    let significanceText = "";
    if (logTransform) {
      if (pooledCI[0] > nullValue && pooledCI[1] > nullValue) significanceText = `Statistically significant increase (p ${pooledPValue < 0.001 ? '<0.001' : `= ${pooledPValue.toFixed(3)}`}). ${emName} is ${pooledEffect.toFixed(2)}x reference.`;
      else if (pooledCI[0] < nullValue && pooledCI[1] < nullValue) significanceText = `Statistically significant decrease (p ${pooledPValue < 0.001 ? '<0.001' : `= ${pooledPValue.toFixed(3)}`}). ${emName} is ${pooledEffect.toFixed(2)}x reference.`;
      else significanceText = `Not statistically significant (p = ${pooledPValue.toFixed(3)}), CI includes null value ${nullValue}.`;
    } else {
      if (pooledPValue < 0.05) significanceText = `Statistically significant ${pooledEffect > nullValue ? 'increase' : 'decrease'} (p ${pooledPValue < 0.001 ? '<0.001' : `= ${pooledPValue.toFixed(3)}`}).`;
      else significanceText = `Not statistically significant (p = ${pooledPValue.toFixed(3)}), CI includes null value ${nullValue}.`;
    }
    interpretation += significanceText;
    interpretation += `\n\n--- Heterogeneity ---\nCochran's Q = ${heterogeneity.Q.toFixed(2)} (df=${heterogeneity.df}, p ${heterogeneity.pValue < 0.001 ? '<0.001' : ('='+heterogeneity.pValue.toFixed(3))}). I² = ${heterogeneity.I2.toFixed(1)}%. `;
    if (heterogeneity.I2 === 0 && heterogeneity.Q <= heterogeneity.df) interpretation += `No observable heterogeneity.`;
    else if (heterogeneity.I2 < 25) interpretation += `Low heterogeneity.`; else if (heterogeneity.I2 < 50) interpretation += `Moderate heterogeneity.`;
    else if (heterogeneity.I2 < 75) interpretation += `Substantial heterogeneity.`; else interpretation += `Considerable heterogeneity.`;
    if (model === 'random' && heterogeneity.tau2 > 0) interpretation += ` Est. between-study variance (τ²) = ${heterogeneity.tau2.toFixed(4)}.`;
    else if (model === 'random' && heterogeneity.tau2 === 0) interpretation += ` τ² estimated as 0, results similar to fixed-effect.`;
    if (publicationBias.eggerTest) {
      const egger = publicationBias.eggerTest; interpretation += `\n\n--- Publication Bias (Egger's Test) ---\n`;
      if (!egger.sufficientStudies) interpretation += `Egger's test not performed (requires ≥10 studies).`;
      else if (isNaN(egger.intercept) || isNaN(egger.pValue)) interpretation += `Egger's test could not be computed.`;
      else { interpretation += `Egger's intercept = ${egger.intercept.toFixed(3)}, p ${egger.pValue < 0.001 ? '<0.001' : `= ${egger.pValue.toFixed(3)}`}. `;
        if (egger.significant) interpretation += `Suggests potential funnel plot asymmetry (p < 0.10).`;
        else interpretation += `No strong evidence of funnel plot asymmetry (p >= 0.10).`;
        interpretation += ` Note: Low power with few studies.`;
      }
    }
    if (subgroupAnalysis && Object.keys(subgroupAnalysis).length > 0) {
      interpretation += `\n\n--- Subgroup Analysis ---\n`;
      Object.entries(subgroupAnalysis).forEach(([subgroupName, data]) => {
        interpretation += `"${subgroupName}" (${data.numStudies} studies): Pooled ${emName.toLowerCase()} = ${data.pooledEffect.toFixed(3)} (${confLevelText} CI: ${data.pooledCI[0].toFixed(3)} to ${data.pooledCI[1].toFixed(3)}).\n`;
      }); interpretation += `Interpret subgroup differences cautiously.`;
    }
    interpretation += `\n\n--- General Considerations ---\nResults depend on study quality. Interpret in context.`;
    return interpretation;
  };

  const ForestPlot = React.memo(({ data, effectMeasureType, confLevelPercent }: { data: ForestPlotStudyData[], effectMeasureType: EffectMeasureType, confLevelPercent: string }) => {
    const svgRef = useRef<SVGSVGElement>(null);
    const theme = useTheme();
    const { nullValue, name: emNameFromEffectInfo } = EFFECT_MEASURE_INFO[effectMeasureType];
    const resultsFromParentScope = metaResults;

    useEffect(() => {
      if (!data || data.length === 0 || !svgRef.current || !svgRef.current.parentElement) return;
      d3.select(svgRef.current).selectAll("*").remove();
      const baseRowHeight = 25, headerHeight = 40, footerHeight = 50;
      const totalHeight = data.length * baseRowHeight + headerHeight + footerHeight;
      const margins = { top: headerHeight, right: 20, bottom: footerHeight, left: 20 };
      const containerWidth = svgRef.current.parentElement.clientWidth || 900;
      const totalWidth = Math.max(containerWidth, 700);
      const textColWidths = { study: Math.floor(totalWidth * 0.30), effectCI: Math.floor(totalWidth * 0.20), weight: Math.floor(totalWidth * 0.10) };
      const plotAreaXStart = margins.left + textColWidths.study + textColWidths.effectCI + textColWidths.weight;
      const plotAreaWidth = totalWidth - plotAreaXStart - margins.right;

      const svg = d3.select(svgRef.current).attr("width", totalWidth).attr("height", totalHeight)
        .style("font-family", Array.isArray(theme.typography.fontFamily) ? theme.typography.fontFamily[0] : theme.typography.fontFamily || 'sans-serif').style("font-size", "12px");
      
      svg.append("text").text("Study").attr("x", margins.left).attr("y", margins.top - 15).style("font-weight", "bold");
      svg.append("text").text(`${emNameFromEffectInfo} [${confLevelPercent} CI]`).attr("x", margins.left + textColWidths.study).attr("y", margins.top - 15).style("font-weight", "bold");
      svg.append("text").text("Weight (%)").attr("x", plotAreaXStart - 5).attr("y", margins.top - 15).style("font-weight", "bold").attr("text-anchor", "end");
      svg.append("text").text(emNameFromEffectInfo).attr("x", plotAreaXStart + plotAreaWidth / 2).attr("y", margins.top - 15).style("font-weight", "bold").attr("text-anchor", "middle");

      const allEffectValues = data.flatMap(d => [d.effectSize, d.ci_lower, d.ci_upper, nullValue].filter(v => typeof v === 'number')) as number[];
      const xMin = d3.min(allEffectValues) ?? 0; const xMax = d3.max(allEffectValues) ?? 1;
      const xPadding = Math.abs(xMax - xMin) * 0.1 || 0.1;
      const xScale = d3.scaleLinear().domain([xMin - xPadding, xMax + xPadding]).range([0, plotAreaWidth]);
      const plotGroup = svg.append("g").attr("transform", `translate(${plotAreaXStart}, ${margins.top})`);
      plotGroup.append("g").attr("transform", `translate(0, ${data.length * baseRowHeight + 5})`).call(d3.axisBottom(xScale).ticks(5).tickSizeOuter(0)).selectAll("text").style("font-size", "10px");
      if (xScale(nullValue) >= 0 && xScale(nullValue) <= plotAreaWidth) {
        plotGroup.append("line").attr("x1", xScale(nullValue)).attr("x2", xScale(nullValue)).attr("y1", -5).attr("y2", data.length * baseRowHeight).attr("stroke", theme.palette.text.secondary).attr("stroke-dasharray", "3,3");
      }
      data.forEach((d, i) => {
        const yPos = i * baseRowHeight + baseRowHeight / 2; const isSummary = d.isSummary;
        svg.append("text").text(d.name).attr("x", margins.left).attr("y", margins.top + yPos).attr("dominant-baseline", "middle").style("font-weight", isSummary ? "bold" : "normal").style("font-size", isSummary ? "13px" : "12px");
        svg.append("text").text(`${d.effectSize.toFixed(3)} [${d.ci_lower.toFixed(3)}; ${d.ci_upper.toFixed(3)}]`).attr("x", margins.left + textColWidths.study).attr("y", margins.top + yPos).attr("dominant-baseline", "middle").style("font-weight", isSummary ? "bold" : "normal").style("font-size", isSummary ? "13px" : "12px");
        svg.append("text").text(isSummary ? "-" : d.weight.toFixed(1)).attr("x", plotAreaXStart - 5).attr("y", margins.top + yPos).attr("dominant-baseline", "middle").attr("text-anchor", "end").style("font-weight", isSummary ? "bold" : "normal").style("font-size", isSummary ? "13px" : "12px");
        const G = plotGroup.append("g").attr("transform", `translate(0, ${yPos})`);
        if (d.ci_lower != null && d.ci_upper != null) G.append("line").attr("x1", xScale(d.ci_lower)).attr("x2", xScale(d.ci_upper)).attr("stroke", theme.palette.text.primary).attr("stroke-width", isSummary ? 2 : 1);
        if (isSummary) {
          const diamondSize = 8; G.append("path").attr("d", d3.symbol(d3.symbolDiamond, diamondSize * diamondSize * (effectMeasureType === 'MD' || effectMeasureType === 'SMD' ? 1.5 : 2.5))).attr("transform", `translate(${xScale(d.effectSize)}, 0)`).attr("fill", theme.palette.secondary.main).attr("stroke", theme.palette.text.primary).attr("stroke-width", 1);
        } else {
          const maxWeightInData = d3.max(data.filter(sd => !sd.isSummary), sd => sd.weight) || 1; const relativeWeight = (d.weight / maxWeightInData); const squareSize = Math.max(3, Math.min(10, 5 * Math.sqrt(relativeWeight) + 2));
          G.append("rect").attr("x", xScale(d.effectSize) - squareSize / 2).attr("y", -squareSize / 2).attr("width", squareSize).attr("height", squareSize).attr("fill", theme.palette.primary.main);
        }
      });
      if (resultsFromParentScope?.heterogeneity) {
        const het = resultsFromParentScope.heterogeneity; const hetText = `Heterogeneity: Q=${het.Q.toFixed(2)} (df=${het.df}, p=${het.pValue < 0.001 ? '<0.001' : het.pValue.toFixed(3)}); I²=${het.I2.toFixed(1)}%`;
        const tauText = resultsFromParentScope.model === 'random' ? `; τ²=${het.tau2.toFixed(4)}` : '';
        svg.append("text").attr("x", margins.left).attr("y", totalHeight - footerHeight / 2 + 10).attr("text-anchor", "start").style("font-size", "11px").text(hetText + tauText);
      }
    }, [data, theme, effectMeasureType, confLevelPercent, resultsFromParentScope, nullValue, emNameFromEffectInfo]);
    return (<Box sx={{ overflowX: 'auto', overflowY: 'auto', maxHeight: '600px', border: `1px solid ${theme.palette.divider}`, p: 1 }}><svg ref={svgRef}></svg></Box>);
  });
  ForestPlot.displayName = 'ForestPlot';

  const FunnelPlot = React.memo(({ data, pooledEffect, effectMeasureType }: { data: FunnelPlotStudyData[], pooledEffect: number, effectMeasureType: EffectMeasureType }) => {
    const svgRef = useRef<SVGSVGElement>(null);
    const theme = useTheme();
    const { name: emName, nullValue } = EFFECT_MEASURE_INFO[effectMeasureType];
    useEffect(() => {
      if (!data || data.length === 0 || !svgRef.current || !svgRef.current.parentElement) return;
      d3.select(svgRef.current).selectAll("*").remove();
      const margin = { top: 50, right: 50, bottom: 60, left: 70 };
      const parentWidth = svgRef.current.parentElement.clientWidth || 800;
      const width = Math.max(parentWidth, 600) - margin.left - margin.right;
      const height = 450 - margin.top - margin.bottom;
      const svg = d3.select(svgRef.current).attr("width", width + margin.left + margin.right).attr("height", height + margin.top + margin.bottom)
        .append("g").attr("transform", `translate(${margin.left},${margin.top})`)
        .style("font-family", Array.isArray(theme.typography.fontFamily) ? theme.typography.fontFamily[0] : theme.typography.fontFamily || 'sans-serif');
      
      const effectSizes = data.map(d => d.effectSize).concat(pooledEffect, nullValue).filter(v => typeof v === 'number') as number[];
      const xDomainExtent = d3.extent(effectSizes) as [number | undefined, number | undefined];
      const xDomain: [number, number] = (xDomainExtent[0] !== undefined && xDomainExtent[1] !== undefined) ? [xDomainExtent[0], xDomainExtent[1]] : [0,1];
      const xPadding = Math.abs(xDomain[1] - xDomain[0]) * 0.15 || 0.1;
      const xScale = d3.scaleLinear().domain([xDomain[0] - xPadding, xDomain[1] + xPadding]).range([0, width]);
      
      const seValues = data.map(d => d.standardError).filter(v => typeof v === 'number' && v > 0) as number[]; // Ensure SE > 0 for log scale and range
      const maxSE = seValues.length > 0 ? d3.max(seValues) || 1 : 1; // Default if no valid SEs
      const minSE = 0;
      const yScale = d3.scaleLinear().domain([maxSE * 1.05, minSE]).range([height, 0]);

      svg.append("g").attr("class", "grid").attr("transform", `translate(0,${height})`).call(d3.axisBottom(xScale).ticks(8).tickSize(-height).tickFormat(() => "")).selectAll("line").attr("stroke", theme.palette.divider);
      svg.append("g").attr("class", "grid").call(d3.axisLeft(yScale).ticks(8).tickSize(-width).tickFormat(() => "")).selectAll("line").attr("stroke", theme.palette.divider);
      svg.append("g").attr("transform", `translate(0,${height})`).call(d3.axisBottom(xScale).ticks(8)).append("text").attr("fill", theme.palette.text.primary).attr("x", width / 2).attr("y", margin.bottom - 15).attr("text-anchor", "middle").style("font-size", "14px").text(emName);
      svg.append("g").call(d3.axisLeft(yScale).ticks(8)).append("text").attr("fill", theme.palette.text.primary).attr("transform", "rotate(-90)").attr("x", -height / 2).attr("y", -margin.left + 20).attr("text-anchor", "middle").style("font-size", "14px").text("Standard Error");
      svg.append("line").attr("x1", xScale(pooledEffect)).attr("x2", xScale(pooledEffect)).attr("y1", yScale(maxSE * 1.05)).attr("y2", yScale(minSE)).attr("stroke", theme.palette.secondary.main).attr("stroke-width", 2).attr("stroke-dasharray", "4,4");
      if (xScale(nullValue) >= 0 && xScale(nullValue) <= width) {
        svg.append("line").attr("x1", xScale(nullValue)).attr("x2", xScale(nullValue)).attr("y1", yScale(maxSE * 1.05)).attr("y2", yScale(minSE)).attr("stroke", theme.palette.text.secondary).attr("stroke-width", 1).attr("stroke-dasharray", "2,2");
      }
      const zCrit = 1.96;
      const funnelLine = d3.line<{se: number, x: number}>().x(d => xScale(d.x)).y(d => yScale(d.se));
      const seStepValue = maxSE > 1e-5 ? maxSE / 50 : 0.01;
      const seSteps = maxSE > 1e-5 ? d3.range(minSE, maxSE * 1.05, seStepValue) : [0, maxSE * 1.05];
      const upperFunnelData = seSteps.map(se_val => ({ se: se_val, x: pooledEffect + zCrit * se_val }));
      const lowerFunnelData = seSteps.map(se_val => ({ se: se_val, x: pooledEffect - zCrit * se_val })).reverse();
      const funnelPathData = [...upperFunnelData, ...lowerFunnelData];
      if (funnelPathData.length > 1 && funnelPathData.every(p => !isNaN(p.x) && !isNaN(p.se))) {
        svg.append("path").datum(funnelPathData).attr("fill", "none").attr("stroke", theme.palette.info.light).attr("stroke-width", 1.5).attr("stroke-dasharray", "3,3").attr("d", funnelLine as any);
      }
      const tooltipElement = d3.select(document.createElement("div")).attr("class", "funnel-tooltip-meta").style("position", "absolute").style("visibility", "hidden").style("background-color", theme.palette.background.paper).style("border", `1px solid ${theme.palette.divider}`).style("border-radius", "4px").style("padding", "8px").style("font-size", "12px").style("pointer-events", "none").style("z-index", String(theme.zIndex.tooltip));
      document.body.appendChild(tooltipElement.node() as HTMLElement);
      svg.selectAll(".study-point").data(data.filter(d => typeof d.standardError === 'number' && d.standardError > 0)).enter().append("circle").attr("class", "study-point")
        .attr("cx", d => xScale(d.effectSize)).attr("cy", d => yScale(d.standardError)).attr("r", 5).attr("fill", theme.palette.primary.main).attr("stroke", theme.palette.background.paper).attr("stroke-width", 0.5).style("opacity", 0.8)
        .on("mouseover", (event, d_event) => { tooltipElement.html(`<b>${d_event.name}</b><br/>${emName}: ${d_event.effectSize.toFixed(3)}<br/>SE: ${d_event.standardError.toFixed(3)}`).style("visibility", "visible"); })
        .on("mousemove", (event) => { tooltipElement.style("top", (event.pageY - 10) + "px").style("left", (event.pageX + 10) + "px"); })
        .on("mouseout", () => { tooltipElement.style("visibility", "hidden"); });
      svg.append("text").attr("x", width / 2).attr("y", 0 - (margin.top / 2)).attr("text-anchor", "middle").style("font-size", "16px").style("font-weight", "bold").text(`Funnel Plot of ${emName} vs. Standard Error`);
      return () => { tooltipElement.remove(); };
    }, [data, pooledEffect, theme, effectMeasureType, emName, nullValue]);
    return (<Box sx={{ overflowX: 'auto', overflowY: 'auto', maxHeight: '600px', border: `1px solid ${theme.palette.divider}`, p: 1 }}><svg ref={svgRef}></svg></Box>);
  });
  FunnelPlot.displayName = 'FunnelPlot';

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>Meta-Analysis</Typography>
      
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom sx={{borderBottom: `1px solid ${theme.palette.divider}`, pb:1, mb:2}}>Configuration</Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6} lg={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select labelId="dataset-select-label" value={selectedDatasetId} label="Dataset" onChange={handleDatasetChange}>
                {datasets.length === 0 ? <MenuItem value="" disabled>No datasets available</MenuItem> : datasets.map(ds => <MenuItem key={ds.id} value={ds.id}>{ds.name} ({ds.data.length} rows)</MenuItem>)}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <FormControl fullWidth margin="normal" disabled={!currentDataset}>
              <InputLabel id="study-name-label">Study Name/Identifier</InputLabel>
              <Select labelId="study-name-label" value={studyNameVar} label="Study Name/Identifier" onChange={handleStudyNameChange}>
                {stringColumns.length === 0 ? <MenuItem value="" disabled>No text/categorical columns</MenuItem> : stringColumns.map(col => <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>)}
              </Select>
            </FormControl>
          </Grid>
           <Grid item xs={12} md={6} lg={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="effect-measure-label">Effect Measure Type</InputLabel>
              <Select labelId="effect-measure-label" value={effectMeasure} label="Effect Measure Type" onChange={handleEffectMeasureChange} renderValue={(selected) => EFFECT_MEASURE_INFO[selected as EffectMeasureType].name}>
                {Object.entries(EFFECT_MEASURE_INFO).map(([key, val]) => (
                  <MenuItem key={key} value={key as EffectMeasureType}>
                     <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <Typography sx={{ flexGrow: 1 }}>{val.name} ({key})</Typography>
                        <Tooltip title={val.logTransform ? "Analyzed on log scale, then back-transformed. Null = 1." : "Analyzed on original scale. Null = 0."} placement="right">
                            <InfoIcon fontSize="small" color="action" />
                        </Tooltip>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <FormControl fullWidth margin="normal" disabled={!currentDataset}>
              <InputLabel id="effect-size-label">Effect Size (Value)</InputLabel>
              <Select labelId="effect-size-label" value={effectSizeVar} label="Effect Size (Value)" onChange={handleEffectSizeChange}>
                {numericColumns.length === 0 ? <MenuItem value="" disabled>No numeric columns</MenuItem> : numericColumns.map(col => <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>)}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <FormControl fullWidth margin="normal" disabled={!currentDataset}>
              <InputLabel id="se-label">Standard Error (of Effect Size)</InputLabel>
              <Select labelId="se-label" value={standardErrorVar} label="Standard Error (of Effect Size)" onChange={handleStandardErrorChange}>
                 {numericColumns.length === 0 ? <MenuItem value="" disabled>No numeric columns</MenuItem> : numericColumns.map(col => <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>)}
              </Select>
               <Typography variant="caption" color="textSecondary" sx={{mt:0.5}}>
                {EFFECT_MEASURE_INFO[effectMeasure].logTransform ? "Provide SE of original ES (e.g. SE of OR, not log(OR))." : "Provide SE of the ES." }
              </Typography>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <FormControl fullWidth margin="normal" disabled={!currentDataset}>
              <InputLabel id="sample-size-label">Sample Size (Optional)</InputLabel>
              <Select labelId="sample-size-label" value={sampleSizeVar} label="Sample Size (Optional)" onChange={handleSampleSizeChange}>
                <MenuItem value=""><em>None</em></MenuItem>
                {numericColumns.map(col => <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>)}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <FormControl fullWidth margin="normal" disabled={!currentDataset}>
              <InputLabel id="subgroup-label">Subgroup Variable (Optional)</InputLabel>
              <Select labelId="subgroup-label" value={subgroupVar} label="Subgroup Variable (Optional)" onChange={handleSubgroupChange}>
                <MenuItem value=""><em>None</em></MenuItem>
                 {stringColumns.map(col => <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>)}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="conf-level-label">Confidence Level</InputLabel>
              <Select labelId="conf-level-label" value={confLevel} label="Confidence Level" onChange={handleConfLevelChange}>
                <MenuItem value={0.90}>90%</MenuItem> <MenuItem value={0.95}>95%</MenuItem> <MenuItem value={0.99}>99%</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6} lg={4}>
            <FormControl component="fieldset" margin="normal" fullWidth>
                <Typography variant="subtitle2" gutterBottom component="legend">Analysis Model</Typography>
                <RadioGroup row value={analysisModel} onChange={handleModelChange}>
                    <FormControlLabel value="fixed" control={<Radio />} label={<Box display="flex" alignItems="center">Fixed-Effect<Tooltip title="Assumes one true effect size. Weights by precision."><IconButton size="small"><HelpIcon fontSize="inherit" /></IconButton></Tooltip></Box>} />
                    <FormControlLabel value="random" control={<Radio />} label={<Box display="flex" alignItems="center">Random-Effects<Tooltip title="Assumes true effects vary. Incorporates between-study variance (τ²)."><IconButton size="small"><HelpIcon fontSize="inherit" /></IconButton></Tooltip></Box>} />
                </RadioGroup>
            </FormControl>
          </Grid>
        </Grid>
        <Divider sx={{ my: 3 }} />
        <Typography variant="subtitle2" gutterBottom>Display Options</Typography>
        <Grid container spacing={1}>
            {Object.keys(displayOptions).map(keyStr => {
                const key = keyStr as keyof typeof displayOptions;
                return (<Grid item xs={12} sm={6} md={4} key={key}><FormControlLabel control={<Checkbox checked={displayOptions[key]} onChange={() => handleDisplayOptionChange(key)} />} label={key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} /></Grid>);
            })}
        </Grid>
        <Box mt={3} display="flex" justifyContent="flex-start">
          <Button variant="contained" color="primary" size="large" startIcon={<ForestIcon />} onClick={runMetaAnalysis} disabled={loading || !studyNameVar || !effectSizeVar || !standardErrorVar || !currentDataset}>Run Meta-Analysis</Button>
        </Box>
      </Paper>

      {loading && <Box display="flex" justifyContent="center" my={5}><CircularProgress size={50} /></Box>}
      {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}
      {warning && !loading && <Alert severity="warning" sx={{ mb: 3 }}>{warning}</Alert>}

      {metaResults && !loading && (
        <Paper elevation={3} sx={{ p: 0, mt: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ borderBottom: 1, borderColor: 'divider', px:2 }} variant="scrollable" scrollButtons="auto">
            <Tab label="Summary Statistics" />
            {displayOptions.showForestPlot && <Tab label="Forest Plot" />}
            {displayOptions.showFunnelPlot && <Tab label="Funnel Plot" />}
            <Tab label="Diagnostics & Subgroups" />
            <Tab label="Full Interpretation" />
          </Tabs>
          <Box p={3}>
            {activeTab === 0 && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined"><CardContent>
                      <Typography variant="h6" gutterBottom color="primary">Pooled Effect Estimate</Typography>
                      <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>{metaResults.pooledEffect.toFixed(3)}<Typography variant="caption" color="text.secondary" sx={{ml:1}}>({EFFECT_MEASURE_INFO[metaResults.effectMeasure].name})</Typography></Typography>
                      <Divider sx={{ my: 1.5 }} />
                      <Typography variant="body1"><strong>{(confLevel * 100)}% CI:</strong> [{metaResults.pooledCI[0].toFixed(3)}, {metaResults.pooledCI[1].toFixed(3)}]</Typography>
                      <Typography variant="body1"><strong>p-value:</strong> {metaResults.pooledPValue < 0.001 ? '< 0.001' : metaResults.pooledPValue.toFixed(3)}</Typography>
                      <Typography variant="body1"><strong>Model:</strong> {metaResults.model === 'fixed' ? 'Fixed-Effect' : 'Random-Effects'}</Typography>
                      <Typography variant="body1"><strong>Std. Error (of {EFFECT_MEASURE_INFO[metaResults.effectMeasure].logTransform ? `log(${metaResults.effectMeasure})` : 'Effect'}):</strong> {metaResults.pooledSE.toFixed(3)}</Typography>
                       {metaResults.pooledPValue < (1-confLevel) && (<Chip icon={<CheckCircleIcon />} label="Statistically Significant" color="success" size="small" sx={{ mt: 1.5 }} />)}
                  </CardContent></Card>
                </Grid>
                {displayOptions.showHeterogeneity && (<Grid item xs={12} md={6}><Card variant="outlined"><CardContent>
                        <Typography variant="h6" gutterBottom color="primary">Heterogeneity</Typography>
                         <Typography variant="h4" component="div">I² = {metaResults.heterogeneity.I2.toFixed(1)}%</Typography>
                         <Typography variant="body2" color="text.secondary" gutterBottom>{metaResults.heterogeneity.I2 < 25 ? 'Low' : metaResults.heterogeneity.I2 < 50 ? 'Moderate' : metaResults.heterogeneity.I2 < 75 ? 'Substantial' : 'Considerable'} heterogeneity</Typography>
                        <Divider sx={{ my: 1.5 }} />
                        <Typography variant="body1"><strong>Cochran's Q:</strong> {metaResults.heterogeneity.Q.toFixed(2)} (df={metaResults.heterogeneity.df})</Typography>
                        <Typography variant="body1"><strong>p-value (for Q):</strong> {metaResults.heterogeneity.pValue < 0.001 ? '< 0.001' : metaResults.heterogeneity.pValue.toFixed(3)}</Typography>
                        {metaResults.model === 'random' && (<Typography variant="body1"><strong>τ²:</strong> {metaResults.heterogeneity.tau2.toFixed(4)}</Typography>)}
                        {metaResults.heterogeneity.pValue < 0.05 && metaResults.heterogeneity.I2 > 0 && (<Chip icon={<WarningIcon />} label="Significant Heterogeneity" color="warning" size="small" sx={{ mt: 1.5 }} />)}
                </CardContent></Card></Grid>)}
                {displayOptions.showWeights && (<Grid item xs={12}><Typography variant="h6" gutterBottom color="primary" sx={{mt:2}}>Study Details & Weights</Typography><TableContainer component={Paper} variant="outlined"><Table size="small">
                        <TableHead><TableRow>
                            <TableCell>Study</TableCell><TableCell align="right">{EFFECT_MEASURE_INFO[metaResults.effectMeasure].name}</TableCell>
                            <TableCell align="right">Std. Error</TableCell><TableCell align="right">Weight (%)</TableCell>
                            {subgroupVar && <TableCell>Subgroup</TableCell>}
                        </TableRow></TableHead>
                        <TableBody>{metaResults.forestPlotData.filter(s => !s.isSummary).map((study, index) => (
                            <TableRow key={study.name + index} hover>
                              <TableCell component="th" scope="row">{study.name}</TableCell>
                              <TableCell align="right">{study.effectSize.toFixed(3)}</TableCell>
                              <TableCell align="right">{study.standardError?.toFixed(3) ?? '-'}</TableCell>
                              <TableCell align="right">{study.weight.toFixed(1)}%</TableCell>
                              {subgroupVar && <TableCell>{study.subgroup || 'N/A'}</TableCell>}
                            </TableRow>))}
                        </TableBody></Table></TableContainer></Grid>)}
              </Grid>
            )}
            {activeTab === 1 && displayOptions.showForestPlot && (<Box>
              <Typography variant="h6" gutterBottom color="primary">Forest Plot</Typography>
              <ForestPlot data={metaResults.forestPlotData} effectMeasureType={metaResults.effectMeasure} confLevelPercent={`${confLevel*100}%`} />
              <Typography variant="caption" color="text.secondary" display="block" mt={1}>Individual study effects (squares by weight) & CIs. Diamond is pooled effect. Dashed line: no effect ({EFFECT_MEASURE_INFO[metaResults.effectMeasure].nullValue}).</Typography>
            </Box>)}
            {activeTab === 2 && displayOptions.showFunnelPlot && (<Box>
              <Typography variant="h6" gutterBottom color="primary">Funnel Plot</Typography>
              <FunnelPlot data={metaResults.funnelPlotData} pooledEffect={metaResults.pooledEffect} effectMeasureType={metaResults.effectMeasure} />
              <Typography variant="caption" color="text.secondary" display="block" mt={1}>ES vs SE. Asymmetry may indicate bias. Solid line: Pooled effect. Dashed: No effect. Dotted: Pseudo 95% CI.</Typography>
            </Box>)}
            {activeTab === 3 && (
              <Grid container spacing={3}>
                {displayOptions.showPublicationBias && metaResults.publicationBias.eggerTest && (<Grid item xs={12} md={6}><Card variant="outlined"><CardContent>
                    <Typography variant="h6" gutterBottom color="primary">Publication Bias</Typography><Typography variant="subtitle1" gutterBottom>Egger's Test</Typography>
                    {!metaResults.publicationBias.eggerTest.sufficientStudies ? (<Alert severity="info">Egger's test requires ≥10 studies. Current: {metaResults.funnelPlotData.length}.</Alert>)
                    : isNaN(metaResults.publicationBias.eggerTest.intercept) || isNaN(metaResults.publicationBias.eggerTest.pValue) ? (<Alert severity="warning">Egger's test N/A (data variance issues?).</Alert>)
                    : (<>
                        <Typography variant="body1"><strong>Intercept ({EFFECT_MEASURE_INFO[metaResults.effectMeasure].logTransform ? `log(${metaResults.effectMeasure})` : 'Effect'}):</strong> {metaResults.publicationBias.eggerTest.intercept.toFixed(3)}</Typography>
                        <Typography variant="body1"><strong>p-value:</strong> {metaResults.publicationBias.eggerTest.pValue.toFixed(3)}</Typography>
                        {metaResults.publicationBias.eggerTest.significant
                          ? <Alert severity="warning" sx={{ mt: 1.5 }}>Potential asymmetry (p &lt; 0.10). May indicate bias.</Alert>
                          : <Alert severity="success" sx={{ mt: 1.5 }}>No significant asymmetry by Egger's (p &gt;= 0.10).</Alert>
                        }
                      </>)}
                    <Typography variant="caption" display="block" sx={{mt:1}}>Egger's test assesses funnel plot asymmetry. Interpret with caution, especially with few studies.</Typography>
                </CardContent></Card></Grid>)}
                {displayOptions.showSubgroupAnalysis && metaResults.subgroupAnalysis && Object.keys(metaResults.subgroupAnalysis).length > 0 && (<Grid item xs={12} md={displayOptions.showPublicationBias && metaResults.publicationBias.eggerTest ? 6 : 12}><Card variant="outlined"><CardContent>
                    <Typography variant="h6" gutterBottom color="primary">Subgroup Analysis</Typography><TableContainer><Table size="small">
                        <TableHead><TableRow><TableCell>Subgroup</TableCell><TableCell align="right">N Studies</TableCell><TableCell align="right">Pooled {EFFECT_MEASURE_INFO[metaResults.effectMeasure].name}</TableCell><TableCell align="right">{(confLevel * 100)}% CI</TableCell></TableRow></TableHead>
                        <TableBody>{Object.entries(metaResults.subgroupAnalysis).map(([subgroupName, data]) => (<TableRow key={subgroupName} hover>
                            <TableCell component="th" scope="row">{subgroupName}</TableCell><TableCell align="right">{data.numStudies}</TableCell>
                            <TableCell align="right">{data.pooledEffect.toFixed(3)}</TableCell><TableCell align="right">[{data.pooledCI[0].toFixed(3)}, {data.pooledCI[1].toFixed(3)}]</TableCell>
                        </TableRow>))}</TableBody></Table></TableContainer>
                    <Typography variant="caption" display="block" sx={{mt:1}}>Subgroup effects use overall τ² if applicable. Formal test for subgroup differences not performed.</Typography>
                </CardContent></Card></Grid>)}
                 {displayOptions.showSubgroupAnalysis && (!metaResults.subgroupAnalysis || Object.keys(metaResults.subgroupAnalysis).length === 0) && (<Grid item xs={12} md={6}><Card variant="outlined"><CardContent>
                    <Typography variant="h6" gutterBottom color="primary">Subgroup Analysis</Typography><Typography>No subgroup variable selected or no subgroups found.</Typography>
                </CardContent></Card></Grid>)}
              </Grid>
            )}
             {activeTab === 4 && (<Box>
                <Typography variant="h6" gutterBottom color="primary">Comprehensive Interpretation</Typography>
                <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: 'background.default', whiteSpace: 'pre-line', lineHeight: 1.6, fontSize:'0.95rem' }}>{getInterpretation()}</Paper>
             </Box>)}
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default MetaAnalysis;