import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Tabs,
  Tab,
  Chip,
  TextField,
  FormControlLabel,
  Checkbox,
  Divider,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  LinearProgress
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  Group as GroupIcon,
  SwapVert as SwapVertIcon,
  Category as CategoryIcon,
  Help as HelpIcon,
  Calculate as CalculateIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType, Column, VariableRole } from '../../../types';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip as <PERSON>chartsTool<PERSON>,
  Legend,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Cell,
  ScatterChart,
  Scatter,
  LineChart,
  Line,
  ComposedChart,
  Area
} from 'recharts';

// Types for reliability analysis
interface ReliabilityResult {
  value: number;
  interpretation: string;
  details?: any;
}

interface CronbachAlphaResult extends ReliabilityResult {
  itemStatistics?: {
    item: string;
    correctedItemTotal: number;
    alphaIfDeleted: number;
  }[];
  confidence_interval?: [number, number];
}

interface KappaResult extends ReliabilityResult {
  confusionMatrix?: number[][];
  categories?: string[];
  observedAgreement?: number;
  expectedAgreement?: number;
  standardError?: number;
}

interface ICC_Result extends ReliabilityResult {
  model: string;
  msb?: number;
  msw?: number;
  mse?: number;
  f_value?: number;
  p_value?: number;
  confidence_interval?: [number, number];
}

// Tab panel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`reliability-tabpanel-${index}`}
      aria-labelledby={`reliability-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

// Utility functions for calculations
const cronbachAlpha = (data: number[][]): number | null => {
  if (!data || data.length === 0) {
    return null;
  }

  const numItems = data[0].length;
  const numSubjects = data.length;

  if (numItems <= 1 || numSubjects <= 1) {
    return null;
  }

  // Calculate item means
  const itemMeans = data[0].map((_, itemIndex) => {
    const itemScores = data.map(subject => subject[itemIndex]);
    return itemScores.reduce((sum, score) => sum + score, 0) / numSubjects;
  });

  // Calculate item variances
  const itemVariances = data[0].map((_, itemIndex) => {
    const itemScores = data.map(subject => subject[itemIndex]);
    const mean = itemMeans[itemIndex];
    return itemScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / (numSubjects - 1);
  });

  // Calculate average variance (v_bar)
  const averageVariance = itemVariances.reduce((sum, variance) => sum + variance, 0) / numItems;

  // Calculate pairwise covariances and sum them
  let sumCovariances = 0;
  let numPairs = 0;
  for (let i = 0; i < numItems; i++) {
    for (let j = i + 1; j < numItems; j++) {
      const itemIScores = data.map(subject => subject[i]);
      const itemJScores = data.map(subject => subject[j]);
      const meanI = itemMeans[i];
      const meanJ = itemMeans[j];

      let covariance = 0;
      for (let k = 0; k < numSubjects; k++) {
        covariance += (itemIScores[k] - meanI) * (itemJScores[k] - meanJ);
      }
      covariance /= (numSubjects - 1);
      sumCovariances += covariance;
      numPairs++;
    }
  }

  // Calculate average inter-item covariance (c_bar)
  const averageInterItemCovariance = numPairs > 0 ? sumCovariances / numPairs : 0;

  // Calculate Cronbach's alpha using the new formula
  // alpha = (N * c_bar) / (v_bar + (N - 1) * c_bar)
  const alpha = (numItems * averageInterItemCovariance) / (averageVariance + (numItems - 1) * averageInterItemCovariance);

  return alpha;
};

const cohensKappa = (rater1: string[], rater2: string[]): KappaResult | null => {
  if (rater1.length !== rater2.length) {
    throw new Error("Raters must have the same number of ratings.");
  }

  const n = rater1.length;
  const categories = [...new Set([...rater1, ...rater2])];
  const numCategories = categories.length;

  // Create a confusion matrix
  const confusionMatrix = Array(numCategories)
    .fill(null)
    .map(() => Array(numCategories).fill(0));

  for (let i = 0; i < n; i++) {
    const r1CatIndex = categories.indexOf(rater1[i]);
    const r2CatIndex = categories.indexOf(rater2[i]);
    confusionMatrix[r1CatIndex][r2CatIndex]++;
  }

  // Calculate observed agreement (Po)
  let observedAgreement = 0;
  for (let i = 0; i < numCategories; i++) {
    observedAgreement += confusionMatrix[i][i];
  }
  const Po = observedAgreement / n;

  // Calculate expected agreement (Pe)
  let expectedAgreement = 0;
  const rowSums = Array(numCategories).fill(0);
  const colSums = Array(numCategories).fill(0);

  for (let i = 0; i < numCategories; i++) {
    for (let j = 0; j < numCategories; j++) {
      rowSums[i] += confusionMatrix[i][j];
      colSums[j] += confusionMatrix[i][j];
    }
  }

  for (let i = 0; i < numCategories; i++) {
    expectedAgreement += (rowSums[i] * colSums[i]) / (n * n);
  }

  const Pe = expectedAgreement;

  // Calculate Cohen's Kappa
  const kappa = (Po - Pe) / (1 - Pe);

  // Calculate standard error
  const standardError = Math.sqrt(Po * (1 - Po) / (n * (1 - Pe) * (1 - Pe)));

  // Interpret the result
  let interpretation = '';
  if (kappa <= 0) interpretation = 'No agreement';
  else if (kappa <= 0.20) interpretation = 'Slight agreement';
  else if (kappa <= 0.40) interpretation = 'Fair agreement';
  else if (kappa <= 0.60) interpretation = 'Moderate agreement';
  else if (kappa <= 0.80) interpretation = 'Substantial agreement';
  else interpretation = 'Almost perfect agreement';

  return {
    value: kappa,
    interpretation,
    confusionMatrix,
    categories,
    observedAgreement: Po,
    expectedAgreement: Pe,
    standardError
  };
};

const fleissKappa = (ratings: any[][]): KappaResult | null => {
  if (!ratings || ratings.length === 0) {
    return null;
  }

  const numSubjects = ratings.length;
  const numRaters = ratings[0].length;

  // Convert ratings to a matrix of counts
  const counts: { [key: number]: { [key: string]: number } } = {};
  const allCategories = new Set<string>();

  ratings.forEach((row, i) => {
    row.forEach((rating) => {
      allCategories.add(rating);
      if (!counts[i]) {
        counts[i] = {};
      }
      counts[i][rating] = (counts[i][rating] || 0) + 1;
    });
  });

  const categories = Array.from(allCategories);

  // Calculate p_i for each subject
  const p = Object.keys(counts).map((i) => {
    let sum = 0;
    for (const rating in counts[Number(i)]) {
      const nij = counts[Number(i)][rating];
      sum += nij * (nij - 1);
    }
    return sum / (numRaters * (numRaters - 1));
  });

  // Calculate average observed agreement
  const pBar = p.reduce((acc, val) => acc + val, 0) / numSubjects;

  // Calculate expected agreement by chance
  const categoryCounts: { [key: string]: number } = {};
  ratings.forEach((row) => {
    row.forEach((rating) => {
      categoryCounts[rating] = (categoryCounts[rating] || 0) + 1;
    });
  });

  let pe = 0;
  for (const category in categoryCounts) {
    const sumNij = categoryCounts[category];
    pe += Math.pow(sumNij / (numSubjects * numRaters), 2);
  }

  // Calculate Fleiss Kappa
  const kappa = (pBar - pe) / (1 - pe);

  // Interpret the result
  let interpretation = '';
  if (kappa <= 0) interpretation = 'Agreement no better than chance';
  else if (kappa <= 0.20) interpretation = 'Slight agreement';
  else if (kappa <= 0.40) interpretation = 'Fair agreement';
  else if (kappa <= 0.60) interpretation = 'Moderate agreement';
  else if (kappa <= 0.80) interpretation = 'Substantial agreement';
  else interpretation = 'Almost perfect agreement';

  return {
    value: kappa,
    interpretation,
    observedAgreement: pBar,
    expectedAgreement: pe,
    categories: categories
  };
};

const kendallsTau = (arr1: number[], arr2: number[]): ReliabilityResult | null => {
  if (arr1.length !== arr2.length) {
    throw new Error("Arrays must have the same length");
  }

  let concordant = 0;
  let discordant = 0;
  const n = arr1.length;

  for (let i = 0; i < n; i++) {
    for (let j = i + 1; j < n; j++) {
      const x1 = arr1[i];
      const y1 = arr2[i];
      const x2 = arr1[j];
      const y2 = arr2[j];

      if ((x1 < x2 && y1 < y2) || (x1 > x2 && y1 > y2)) {
        concordant++;
      } else if ((x1 < x2 && y1 > y2) || (x1 > x2 && y1 < y2)) {
        discordant++;
      }
    }
  }

  const tau = (concordant - discordant) / (concordant + discordant);

  // Interpret the result
  let interpretation = '';
  const absTau = Math.abs(tau);
  if (absTau >= 0.9) interpretation = 'Very strong correlation';
  else if (absTau >= 0.7) interpretation = 'Strong correlation';
  else if (absTau >= 0.5) interpretation = 'Moderate correlation';
  else if (absTau >= 0.3) interpretation = 'Weak correlation';
  else interpretation = 'Very weak correlation';

  if (tau < 0) interpretation = 'Negative ' + interpretation.toLowerCase();

  return {
    value: tau,
    interpretation,
    details: { concordant, discordant, totalPairs: concordant + discordant }
  };
};

const kendallsW = (rankings: number[][]): ReliabilityResult | null => {
  const n = rankings.length; // Number of items
  const m = rankings[0].length; // Number of raters

  // Calculate row sums
  const rowSums = rankings.map(row => row.reduce((sum, rank) => sum + rank, 0));

  // Calculate mean row sum
  const meanRowSum = rowSums.reduce((sum, rowSum) => sum + rowSum, 0) / n;

  // Calculate sum of squared deviations
  const sumOfSquaredDeviations = rowSums.reduce((sum, rowSum) => {
    const deviation = rowSum - meanRowSum;
    return sum + deviation * deviation;
  }, 0);

  // Calculate Kendall's W
  const W = (12 * sumOfSquaredDeviations) / (m * m * (n * n * n - n));

  // Interpret the result
  let interpretation = '';
  if (W >= 0.9) interpretation = 'Very strong agreement';
  else if (W >= 0.7) interpretation = 'Strong agreement';
  else if (W >= 0.5) interpretation = 'Moderate agreement';
  else if (W >= 0.3) interpretation = 'Fair agreement';
  else if (W >= 0.1) interpretation = 'Slight agreement';
  else interpretation = 'No agreement';

  return {
    value: W,
    interpretation,
    details: { rowSums, meanRowSum, sumOfSquaredDeviations }
  };
};

const calculateICC = (data: number[][]): ICC_Result | null => {
  const numGroups = data.length;
  if (numGroups === 0) return null;

  const k = data[0].length;
  if (k === 0) return null;

  // Calculate group means
  const groupMeans = data.map(group => group.reduce((sum, val) => sum + val, 0) / k);

  // Calculate overall mean
  const totalSum = data.reduce((sum, group) => sum + group.reduce((groupSum, val) => groupSum + val, 0), 0);
  const overallMean = totalSum / (numGroups * k);

  // Calculate Sum of Squares Between (SSB)
  const ssb = groupMeans.reduce((sum, mean) => sum + Math.pow(mean - overallMean, 2), 0) * k;

  // Calculate Sum of Squares Within (SSW)
  let ssw = 0;
  for (let i = 0; i < numGroups; i++) {
    for (let j = 0; j < k; j++) {
      ssw += Math.pow(data[i][j] - groupMeans[i], 2);
    }
  }

  // Calculate Mean Square Between (MSB)
  const msb = ssb / (numGroups - 1);

  // Calculate Mean Square Within (MSW)
  const msw = ssw / (numGroups * (k - 1));

  // Calculate ICC
  const icc = (msb - msw) / (msb + (k - 1) * msw);

  // F-value for significance testing
  const f_value = msb / msw;
  
  // Interpret the result
  let interpretation = '';
  if (icc >= 0.9) interpretation = 'Excellent reliability';
  else if (icc >= 0.75) interpretation = 'Good reliability';
  else if (icc >= 0.5) interpretation = 'Moderate reliability';
  else interpretation = 'Poor reliability';

  return {
    value: icc,
    interpretation,
    model: 'ICC(2,1)',
    msb,
    msw,
    f_value,
    details: { ssb, ssw, groupMeans, overallMean }
  };
};

// Main Reliability Analysis Component
const ReliabilityAnalysis: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();

  // Tab state
  const [tabValue, setTabValue] = useState(0);

  // Common state
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Internal Consistency state (Cronbach's Alpha)
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [cronbachResult, setCronbachResult] = useState<CronbachAlphaResult | null>(null);

  // Inter-rater Agreement state
  const [rater1Variable, setRater1Variable] = useState<string>('');
  const [rater2Variable, setRater2Variable] = useState<string>('');
  const [multiRaterVariables, setMultiRaterVariables] = useState<string[]>([]);
  const [cohenResult, setCohenResult] = useState<KappaResult | null>(null);
  const [fleissResult, setFleissResult] = useState<KappaResult | null>(null);

  // Rank Correlation state
  const [variable1, setVariable1] = useState<string>('');
  const [variable2, setVariable2] = useState<string>('');
  const [rankingVariables, setRankingVariables] = useState<string[]>([]);
  const [kendallTauResult, setKendallTauResult] = useState<ReliabilityResult | null>(null);
  const [kendallWResult, setKendallWResult] = useState<ReliabilityResult | null>(null);

  // ICC state
  const [iccGroups, setIccGroups] = useState<string[]>([]);
  const [iccMeasurements, setIccMeasurements] = useState<string[]>([]);
  const [iccResult, setIccResult] = useState<ICC_Result | null>(null);

  // Get numeric and categorical columns
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];

  const categoricalColumns = currentDataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL
  ) || [];

  const allColumns = currentDataset?.columns || [];

  // Handle dataset change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    
    // Reset all selections
    setSelectedItems([]);
    setRater1Variable('');
    setRater2Variable('');
    setMultiRaterVariables([]);
    setVariable1('');
    setVariable2('');
    setRankingVariables([]);
    setIccGroups([]);
    setIccMeasurements([]);
    
    // Reset all results
    setCronbachResult(null);
    setCohenResult(null);
    setFleissResult(null);
    setKendallTauResult(null);
    setKendallWResult(null);
    setIccResult(null);
    
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Calculate Cronbach's Alpha
  const calculateCronbachAlpha = () => {
    if (!currentDataset || selectedItems.length < 2) {
      setError('Please select at least 2 items for Cronbach\'s Alpha');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Extract data for selected items
      const data: number[][] = [];
      const itemColumns = selectedItems.map(id => 
        currentDataset.columns.find(col => col.id === id)
      ).filter(Boolean) as Column[];

      currentDataset.data.forEach(row => {
        const values: number[] = [];
        let validRow = true;

        itemColumns.forEach(col => {
          const value = row[col.name];
          if (typeof value === 'number' && !isNaN(value)) {
            values.push(value);
          } else {
            validRow = false;
          }
        });

        if (validRow && values.length === itemColumns.length) {
          data.push(values);
        }
      });

      if (data.length < 2) {
        throw new Error('Not enough valid data rows for analysis');
      }

      const alpha = cronbachAlpha(data);
      if (alpha === null) {
        throw new Error('Unable to calculate Cronbach\'s Alpha');
      }

      // Calculate item statistics
      const itemStatistics = itemColumns.map((col, index) => {
        // Calculate alpha if item deleted
        const dataWithoutItem = data.map(row => 
          row.filter((_, i) => i !== index)
        );
        const alphaIfDeleted = cronbachAlpha(dataWithoutItem) || 0;

        // Calculate corrected item-total correlation
        const itemScores = data.map(row => row[index]);
        const totalScoresWithoutItem = data.map(row => 
          row.reduce((sum, val, i) => i !== index ? sum + val : sum, 0)
        );
        
        // Simple correlation calculation
        const correlation = calculateCorrelation(itemScores, totalScoresWithoutItem);

        return {
          item: col.name,
          correctedItemTotal: correlation,
          alphaIfDeleted
        };
      });

      // Interpret alpha
      let interpretation = '';
      if (alpha >= 0.9) interpretation = 'Excellent internal consistency';
      else if (alpha >= 0.8) interpretation = 'Good internal consistency';
      else if (alpha >= 0.7) interpretation = 'Acceptable internal consistency';
      else if (alpha >= 0.6) interpretation = 'Questionable internal consistency';
      else if (alpha >= 0.5) interpretation = 'Poor internal consistency';
      else interpretation = 'Unacceptable internal consistency';

      setCronbachResult({
        value: alpha,
        interpretation,
        itemStatistics,
        confidence_interval: [alpha - 0.1, Math.min(alpha + 0.1, 1)] // Simplified CI
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Calculate Cohen's Kappa
  const calculateCohenKappa = () => {
    if (!currentDataset || !rater1Variable || !rater2Variable) {
      setError('Please select two rater variables');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const rater1Col = currentDataset.columns.find(col => col.id === rater1Variable);
      const rater2Col = currentDataset.columns.find(col => col.id === rater2Variable);

      if (!rater1Col || !rater2Col) {
        throw new Error('Selected variables not found');
      }

      const rater1Data: string[] = [];
      const rater2Data: string[] = [];

      currentDataset.data.forEach(row => {
        const val1 = String(row[rater1Col.name]);
        const val2 = String(row[rater2Col.name]);
        
        if (val1 && val2) {
          rater1Data.push(val1);
          rater2Data.push(val2);
        }
      });

      if (rater1Data.length < 10) {
        throw new Error('Not enough valid data for Cohen\'s Kappa calculation');
      }

      const result = cohensKappa(rater1Data, rater2Data);
      if (result) {
        setCohenResult(result);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Calculate Fleiss' Kappa
  const calculateFleissKappa = () => {
    if (!currentDataset || multiRaterVariables.length < 2) {
      setError('Please select at least 2 rater variables');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const raterColumns = multiRaterVariables.map(id => 
        currentDataset.columns.find(col => col.id === id)
      ).filter(Boolean) as Column[];

      const ratings: any[][] = [];

      currentDataset.data.forEach(row => {
        const raterValues: any[] = [];
        let validRow = true;

        raterColumns.forEach(col => {
          const value = row[col.name];
          if (value !== null && value !== undefined && String(value).trim() !== '') {
            raterValues.push(String(value));
          } else {
            validRow = false;
          }
        });

        if (validRow && raterValues.length === raterColumns.length) {
          ratings.push(raterValues);
        }
      });

      if (ratings.length < 10) {
        throw new Error('Not enough valid data for Fleiss\' Kappa calculation');
      }

      const result = fleissKappa(ratings);
      if (result) {
        setFleissResult(result);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Calculate Kendall's Tau
  const calculateKendallTau = () => {
    if (!currentDataset || !variable1 || !variable2) {
      setError('Please select two variables for Kendall\'s Tau');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const col1 = currentDataset.columns.find(col => col.id === variable1);
      const col2 = currentDataset.columns.find(col => col.id === variable2);

      if (!col1 || !col2) {
        throw new Error('Selected variables not found');
      }

      const arr1: number[] = [];
      const arr2: number[] = [];

      currentDataset.data.forEach(row => {
        const val1 = row[col1.name];
        const val2 = row[col2.name];
        
        if (typeof val1 === 'number' && typeof val2 === 'number' && 
            !isNaN(val1) && !isNaN(val2)) {
          arr1.push(val1);
          arr2.push(val2);
        }
      });

      if (arr1.length < 5) {
        throw new Error('Not enough valid data for Kendall\'s Tau calculation');
      }

      const result = kendallsTau(arr1, arr2);
      if (result) {
        setKendallTauResult(result);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Calculate Kendall's W
  const calculateKendallW = () => {
    if (!currentDataset || rankingVariables.length < 2) {
      setError('Please select at least 2 ranking variables');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const rankColumns = rankingVariables.map(id => 
        currentDataset.columns.find(col => col.id === id)
      ).filter(Boolean) as Column[];

      const rankings: number[][] = [];

      // Transpose the data: each row should be an item, each column a rater
      const numItems = currentDataset.data.length;
      for (let i = 0; i < numItems; i++) {
        const itemRanks: number[] = [];
        let validItem = true;

        rankColumns.forEach(col => {
          const value = currentDataset.data[i][col.name];
          if (typeof value === 'number' && !isNaN(value)) {
            itemRanks.push(value);
          } else {
            validItem = false;
          }
        });

        if (validItem && itemRanks.length === rankColumns.length) {
          rankings.push(itemRanks);
        }
      }

      if (rankings.length < 3) {
        throw new Error('Not enough valid data for Kendall\'s W calculation');
      }

      const result = kendallsW(rankings);
      if (result) {
        setKendallWResult(result);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Calculate ICC
  const calculateICCAnalysis = () => {
    if (!currentDataset || iccMeasurements.length < 2) {
      setError('Please select at least 2 measurement variables');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const measurementColumns = iccMeasurements.map(id => 
        currentDataset.columns.find(col => col.id === id)
      ).filter(Boolean) as Column[];

      const data: number[][] = [];

      currentDataset.data.forEach(row => {
        const measurements: number[] = [];
        let validRow = true;

        measurementColumns.forEach(col => {
          const value = row[col.name];
          if (typeof value === 'number' && !isNaN(value)) {
            measurements.push(value);
          } else {
            validRow = false;
          }
        });

        if (validRow && measurements.length === measurementColumns.length) {
          data.push(measurements);
        }
      });

      if (data.length < 2) {
        throw new Error('Not enough valid data for ICC calculation');
      }

      const result = calculateICC(data);
      if (result) {
        setIccResult(result);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Helper function to calculate correlation
  const calculateCorrelation = (x: number[], y: number[]): number => {
    const n = x.length;
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((total, xi, i) => total + xi * y[i], 0);
    const sumX2 = x.reduce((total, xi) => total + xi * xi, 0);
    const sumY2 = y.reduce((total, yi) => total + yi * yi, 0);

    const correlation = (n * sumXY - sumX * sumY) / 
      Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

    return correlation;
  };

  // Generate reliability gauge chart data
  const getReliabilityGauge = (value: number, label: string) => {
    const data = [
      { name: 'Score', value: value * 100, fill: getReliabilityColor(value) },
      { name: 'Remaining', value: (1 - value) * 100, fill: '#e0e0e0' }
    ];
    return data;
  };

  // Get color based on reliability value
  const getReliabilityColor = (value: number): string => {
    if (value >= 0.9) return theme.palette.success.main;
    if (value >= 0.8) return theme.palette.info.main;
    if (value >= 0.7) return theme.palette.warning.main;
    if (value >= 0.6) return theme.palette.warning.light;
    return theme.palette.error.main;
  };

  return (
    <Box sx={{ overflowY: 'auto', height: '100%' }}>
      <Typography variant="h5" gutterBottom>
        Reliability Analysis
      </Typography>

      {/* Dataset Selection */}
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Dataset Selection
        </Typography>
        
        <FormControl fullWidth>
          <InputLabel>Dataset</InputLabel>
          <Select
            value={selectedDatasetId}
            label="Dataset"
            onChange={handleDatasetChange}
          >
            {datasets.map(dataset => (
              <MenuItem key={dataset.id} value={dataset.id}>
                {dataset.name} ({dataset.data.length} rows)
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Paper>

      {/* Tabs */}
      <Paper elevation={2}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="reliability analysis tabs">
          <Tab label="Internal Consistency" icon={<AssessmentIcon />} iconPosition="start" />
          <Tab label="Inter-rater Agreement" icon={<GroupIcon />} iconPosition="start" />
          <Tab label="Rank Correlation" icon={<SwapVertIcon />} iconPosition="start" />
          <Tab label="Intraclass Correlation" icon={<CategoryIcon />} iconPosition="start" />
        </Tabs>

        {/* Internal Consistency Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Cronbach's Alpha Analysis
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Measures internal consistency - how closely related a set of items are as a group.
                  </Typography>

                  <FormControl fullWidth sx={{ mt: 2 }}>
                    <InputLabel>Select Items (Variables)</InputLabel>
                    <Select
                      multiple
                      value={selectedItems}
                      label="Select Items (Variables)"
                      onChange={(e) => setSelectedItems(typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value)}
                      disabled={!currentDataset}
                      renderValue={(selected) => {
                        const selectedNames = selected.map(id => {
                          const column = numericColumns.find(col => col.id === id);
                          return column ? column.name : '';
                        }).filter(Boolean);
                        return selectedNames.join(', ');
                      }}
                    >
                      {numericColumns.map(col => (
                        <MenuItem key={col.id} value={col.id}>
                          {col.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<CalculateIcon />}
                    onClick={calculateCronbachAlpha}
                    disabled={loading || selectedItems.length < 2}
                    sx={{ mt: 2 }}
                  >
                    Calculate Cronbach's Alpha
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            {error && (
              <Grid item xs={12}>
                <Alert severity="error">{error}</Alert>
              </Grid>
            )}

            {loading && (
              <Grid item xs={12}>
                <Box display="flex" justifyContent="center">
                  <CircularProgress />
                </Box>
              </Grid>
            )}

            {cronbachResult && !loading && (
              <>
                <Grid item xs={12} md={6}>
                  <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      Cronbach's Alpha Result
                    </Typography>
                    
                    <Box display="flex" alignItems="center" mb={2}>
                      <Typography variant="h3" color="primary">
                        {cronbachResult.value.toFixed(3)}
                      </Typography>
                      <Box ml={2}>
                        <Chip
                          label={cronbachResult.interpretation}
                          color={cronbachResult.value >= 0.7 ? 'success' : 'warning'}
                        />
                      </Box>
                    </Box>

                    <LinearProgress
                      variant="determinate"
                      value={cronbachResult.value * 100}
                      sx={{ height: 10, borderRadius: 5 }}
                      color={cronbachResult.value >= 0.7 ? 'success' : 'warning'}
                    />

                    <Box mt={2}>
                      <Typography variant="body2" color="text.secondary">
                        Interpretation Guidelines:
                      </Typography>
                      <List dense>
                        <ListItem>
                          <ListItemText primary="≥ 0.9: Excellent" />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="≥ 0.8: Good" />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="≥ 0.7: Acceptable" />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="< 0.7: Questionable/Poor" />
                        </ListItem>
                      </List>
                    </Box>
                  </Paper>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      Item Statistics
                    </Typography>
                    
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Item</TableCell>
                            <TableCell align="right">Item-Total Correlation</TableCell>
                            <TableCell align="right">Alpha if Deleted</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {cronbachResult.itemStatistics?.map((item) => (
                            <TableRow key={item.item}>
                              <TableCell>{item.item}</TableCell>
                              <TableCell align="right">{item.correctedItemTotal.toFixed(3)}</TableCell>
                              <TableCell align="right">
                                <Chip
                                  label={item.alphaIfDeleted.toFixed(3)}
                                  size="small"
                                  color={item.alphaIfDeleted > cronbachResult.value ? 'warning' : 'default'}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                    
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                      Items that increase alpha when deleted may be candidates for removal.
                    </Typography>
                  </Paper>
                </Grid>
              </>
            )}
          </Grid>
        </TabPanel>

        {/* Inter-rater Agreement Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            {/* Cohen's Kappa */}
            <Grid item xs={12} lg={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Cohen's Kappa (Two Raters)
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Measures agreement between two raters for categorical data.
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Rater 1</InputLabel>
                        <Select
                          value={rater1Variable}
                          label="Rater 1"
                          onChange={(e) => setRater1Variable(e.target.value)}
                          disabled={!currentDataset}
                        >
                          {allColumns.map(col => (
                            <MenuItem key={col.id} value={col.id}>
                              {col.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Rater 2</InputLabel>
                        <Select
                          value={rater2Variable}
                          label="Rater 2"
                          onChange={(e) => setRater2Variable(e.target.value)}
                          disabled={!currentDataset}
                        >
                          {allColumns.map(col => (
                            <MenuItem key={col.id} value={col.id}>
                              {col.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>

                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<CalculateIcon />}
                    onClick={calculateCohenKappa}
                    disabled={loading || !rater1Variable || !rater2Variable}
                    sx={{ mt: 2 }}
                    fullWidth
                  >
                    Calculate Cohen's Kappa
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            {/* Fleiss' Kappa */}
            <Grid item xs={12} lg={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Fleiss' Kappa (Multiple Raters)
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Measures agreement among multiple raters for categorical data.
                  </Typography>

                  <FormControl fullWidth>
                    <InputLabel>Select Raters</InputLabel>
                    <Select
                      multiple
                      value={multiRaterVariables}
                      label="Select Raters"
                      onChange={(e) => setMultiRaterVariables(typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value)}
                      disabled={!currentDataset}
                      renderValue={(selected) => {
                        const selectedNames = selected.map(id => {
                          const column = allColumns.find(col => col.id === id);
                          return column ? column.name : '';
                        }).filter(Boolean);
                        return selectedNames.join(', ');
                      }}
                    >
                      {allColumns.map(col => (
                        <MenuItem key={col.id} value={col.id}>
                          {col.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<CalculateIcon />}
                    onClick={calculateFleissKappa}
                    disabled={loading || multiRaterVariables.length < 2}
                    sx={{ mt: 2 }}
                    fullWidth
                  >
                    Calculate Fleiss' Kappa
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            {/* Results */}
            {(cohenResult || fleissResult) && !loading && (
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  {cohenResult && (
                    <Grid item xs={12} md={6}>
                      <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="h6" gutterBottom>
                          Cohen's Kappa Result
                        </Typography>
                        
                        <Box display="flex" alignItems="center" mb={2}>
                          <Typography variant="h3" color="primary">
                            {cohenResult.value.toFixed(3)}
                          </Typography>
                          <Box ml={2}>
                            <Chip
                              label={cohenResult.interpretation}
                              color={cohenResult.value >= 0.6 ? 'success' : 'warning'}
                            />
                          </Box>
                        </Box>

                        <TableContainer>
                          <Table size="small">
                            <TableBody>
                              <TableRow>
                                <TableCell>Observed Agreement</TableCell>
                                <TableCell align="right">{(cohenResult.observedAgreement! * 100).toFixed(1)}%</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Expected Agreement</TableCell>
                                <TableCell align="right">{(cohenResult.expectedAgreement! * 100).toFixed(1)}%</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Standard Error</TableCell>
                                <TableCell align="right">{cohenResult.standardError?.toFixed(4)}</TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>

                        {cohenResult.confusionMatrix && (
                          <Box mt={2}>
                            <Typography variant="subtitle2" gutterBottom>
                              Confusion Matrix
                            </Typography>
                            <TableContainer>
                              <Table size="small">
                                <TableHead>
                                  <TableRow>
                                    <TableCell>Rater 1 \ Rater 2</TableCell>
                                    {cohenResult.categories?.map(cat => (
                                      <TableCell key={cat} align="center">{cat}</TableCell>
                                    ))}
                                  </TableRow>
                                </TableHead>
                                <TableBody>
                                  {cohenResult.confusionMatrix.map((row, i) => (
                                    <TableRow key={i}>
                                      <TableCell>{cohenResult.categories?.[i]}</TableCell>
                                      {row.map((cell, j) => (
                                        <TableCell key={j} align="center">{cell}</TableCell>
                                      ))}
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </Box>
                        )}
                      </Paper>
                    </Grid>
                  )}

                  {fleissResult && (
                    <Grid item xs={12} md={6}>
                      <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="h6" gutterBottom>
                          Fleiss' Kappa Result
                        </Typography>
                        
                        <Box display="flex" alignItems="center" mb={2}>
                          <Typography variant="h3" color="primary">
                            {fleissResult.value.toFixed(3)}
                          </Typography>
                          <Box ml={2}>
                            <Chip
                              label={fleissResult.interpretation}
                              color={fleissResult.value >= 0.6 ? 'success' : 'warning'}
                            />
                          </Box>
                        </Box>

                        <TableContainer>
                          <Table size="small">
                            <TableBody>
                              <TableRow>
                                <TableCell>Average Observed Agreement</TableCell>
                                <TableCell align="right">{(fleissResult.observedAgreement! * 100).toFixed(1)}%</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Expected Agreement by Chance</TableCell>
                                <TableCell align="right">{(fleissResult.expectedAgreement! * 100).toFixed(1)}%</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Number of Raters</TableCell>
                                <TableCell align="right">{multiRaterVariables.length}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Categories</TableCell>
                                <TableCell align="right">{fleissResult.categories?.join(', ')}</TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Paper>
                    </Grid>
                  )}
                </Grid>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        {/* Rank Correlation Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            {/* Kendall's Tau */}
            <Grid item xs={12} lg={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Kendall's Tau
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Measures rank correlation between two variables.
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Variable 1</InputLabel>
                        <Select
                          value={variable1}
                          label="Variable 1"
                          onChange={(e) => setVariable1(e.target.value)}
                          disabled={!currentDataset}
                        >
                          {numericColumns.map(col => (
                            <MenuItem key={col.id} value={col.id}>
                              {col.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Variable 2</InputLabel>
                        <Select
                          value={variable2}
                          label="Variable 2"
                          onChange={(e) => setVariable2(e.target.value)}
                          disabled={!currentDataset}
                        >
                          {numericColumns.map(col => (
                            <MenuItem key={col.id} value={col.id}>
                              {col.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>

                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<CalculateIcon />}
                    onClick={calculateKendallTau}
                    disabled={loading || !variable1 || !variable2}
                    sx={{ mt: 2 }}
                    fullWidth
                  >
                    Calculate Kendall's Tau
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            {/* Kendall's W */}
            <Grid item xs={12} lg={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Kendall's W (Coefficient of Concordance)
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Measures agreement among multiple raters for ranked data.
                  </Typography>

                  <FormControl fullWidth>
                    <InputLabel>Select Ranking Variables</InputLabel>
                    <Select
                      multiple
                      value={rankingVariables}
                      label="Select Ranking Variables"
                      onChange={(e) => setRankingVariables(typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value)}
                      disabled={!currentDataset}
                      renderValue={(selected) => {
                        const selectedNames = selected.map(id => {
                          const column = numericColumns.find(col => col.id === id);
                          return column ? column.name : '';
                        }).filter(Boolean);
                        return selectedNames.join(', ');
                      }}
                    >
                      {numericColumns.map(col => (
                        <MenuItem key={col.id} value={col.id}>
                          {col.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<CalculateIcon />}
                    onClick={calculateKendallW}
                    disabled={loading || rankingVariables.length < 2}
                    sx={{ mt: 2 }}
                    fullWidth
                  >
                    Calculate Kendall's W
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            {/* Results */}
            {(kendallTauResult || kendallWResult) && !loading && (
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  {kendallTauResult && (
                    <Grid item xs={12} md={6}>
                      <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="h6" gutterBottom>
                          Kendall's Tau Result
                        </Typography>
                        
                        <Box display="flex" alignItems="center" mb={2}>
                          <Typography variant="h3" color="primary">
                            {kendallTauResult.value.toFixed(3)}
                          </Typography>
                          <Box ml={2}>
                            <Chip
                              label={kendallTauResult.interpretation}
                              color={Math.abs(kendallTauResult.value) >= 0.5 ? 'success' : 'warning'}
                            />
                          </Box>
                        </Box>

                        <LinearProgress
                          variant="determinate"
                          value={Math.abs(kendallTauResult.value) * 100}
                          sx={{ height: 10, borderRadius: 5 }}
                          color={Math.abs(kendallTauResult.value) >= 0.5 ? 'success' : 'warning'}
                        />

                        {kendallTauResult.details && (
                          <Box mt={2}>
                            <Typography variant="body2">
                              Concordant pairs: {kendallTauResult.details.concordant}
                            </Typography>
                            <Typography variant="body2">
                              Discordant pairs: {kendallTauResult.details.discordant}
                            </Typography>
                            <Typography variant="body2">
                              Total pairs: {kendallTauResult.details.totalPairs}
                            </Typography>
                          </Box>
                        )}
                      </Paper>
                    </Grid>
                  )}

                  {kendallWResult && (
                    <Grid item xs={12} md={6}>
                      <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="h6" gutterBottom>
                          Kendall's W Result
                        </Typography>
                        
                        <Box display="flex" alignItems="center" mb={2}>
                          <Typography variant="h3" color="primary">
                            {kendallWResult.value.toFixed(3)}
                          </Typography>
                          <Box ml={2}>
                            <Chip
                              label={kendallWResult.interpretation}
                              color={kendallWResult.value >= 0.7 ? 'success' : 'warning'}
                            />
                          </Box>
                        </Box>

                        <LinearProgress
                          variant="determinate"
                          value={kendallWResult.value * 100}
                          sx={{ height: 10, borderRadius: 5 }}
                          color={kendallWResult.value >= 0.7 ? 'success' : 'warning'}
                        />

                        <Box mt={2}>
                          <Typography variant="body2" color="text.secondary">
                            W ranges from 0 (no agreement) to 1 (complete agreement)
                          </Typography>
                        </Box>
                      </Paper>
                    </Grid>
                  )}
                </Grid>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        {/* ICC Tab */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Intraclass Correlation Coefficient (ICC)
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Assesses reliability of ratings when multiple observers measure the same subjects.
                  </Typography>

                  <FormControl fullWidth sx={{ mt: 2 }}>
                    <InputLabel>Select Measurement Variables</InputLabel>
                    <Select
                      multiple
                      value={iccMeasurements}
                      label="Select Measurement Variables"
                      onChange={(e) => setIccMeasurements(typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value)}
                      disabled={!currentDataset}
                      renderValue={(selected) => {
                        const selectedNames = selected.map(id => {
                          const column = numericColumns.find(col => col.id === id);
                          return column ? column.name : '';
                        }).filter(Boolean);
                        return selectedNames.join(', ');
                      }}
                    >
                      {numericColumns.map(col => (
                        <MenuItem key={col.id} value={col.id}>
                          {col.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<CalculateIcon />}
                    onClick={calculateICCAnalysis}
                    disabled={loading || iccMeasurements.length < 2}
                    sx={{ mt: 2 }}
                  >
                    Calculate ICC
                  </Button>
                </CardContent>
              </Card>
            </Grid>
                        {iccResult && !loading && (
              <Grid item xs={12}>
                <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    ICC Result
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Box display="flex" alignItems="center" mb={2}>
                        <Typography variant="h3" color="primary">
                          {iccResult.value.toFixed(3)}
                        </Typography>
                        <Box ml={2}>
                          <Chip
                            label={iccResult.interpretation}
                            color={iccResult.value >= 0.75 ? 'success' : 'warning'}
                          />
                        </Box>
                      </Box>

                      <LinearProgress
                        variant="determinate"
                        value={iccResult.value * 100}
                        sx={{ height: 10, borderRadius: 5 }}
                        color={iccResult.value >= 0.75 ? 'success' : 'warning'}
                      />

                      <Box mt={2}>
                        <Typography variant="subtitle2" gutterBottom>
                          Model: {iccResult.model}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TableContainer>
                        <Table size="small">
                          <TableBody>
                            <TableRow>
                              <TableCell>Mean Square Between</TableCell>
                              <TableCell align="right">{iccResult.msb?.toFixed(3)}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Mean Square Within</TableCell>
                              <TableCell align="right">{iccResult.msw?.toFixed(3)}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>F-value</TableCell>
                              <TableCell align="right">{iccResult.f_value?.toFixed(3)}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Number of Subjects</TableCell>
                              <TableCell align="right">{currentDataset?.data.length}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Number of Measurements</TableCell>
                              <TableCell align="right">{iccMeasurements.length}</TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Grid>

                    <Grid item xs={12}>
                      <Paper elevation={0} sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                        <Typography variant="subtitle2" gutterBottom>
                          ICC Interpretation Guidelines:
                        </Typography>
                        <Grid container spacing={1}>
                          <Grid item xs={12} sm={6}>
                            <List dense>
                              <ListItem>
                                <CheckCircleIcon fontSize="small" sx={{ mr: 1, color: theme.palette.success.main }} />
                                <ListItemText primary="≥ 0.90: Excellent reliability" />
                              </ListItem>
                              <ListItem>
                                <CheckCircleIcon fontSize="small" sx={{ mr: 1, color: theme.palette.info.main }} />
                                <ListItemText primary="0.75 - 0.90: Good reliability" />
                              </ListItem>
                            </List>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <List dense>
                              <ListItem>
                                <WarningIcon fontSize="small" sx={{ mr: 1, color: theme.palette.warning.main }} />
                                <ListItemText primary="0.50 - 0.75: Moderate reliability" />
                              </ListItem>
                              <ListItem>
                                <WarningIcon fontSize="small" sx={{ mr: 1, color: theme.palette.error.main }} />
                                <ListItemText primary="< 0.50: Poor reliability" />
                              </ListItem>
                            </List>
                          </Grid>
                        </Grid>
                      </Paper>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
            )}
          </Grid>
        </TabPanel>
      </Paper>

      {/* Error display for all tabs */}
      {error && (
        <Box mt={2}>
          <Alert severity="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </Box>
      )}

      {/* Global Help Section */}
      <Box mt={3}>
        <Paper elevation={0} sx={{ p: 2, backgroundColor: theme.palette.info.light + '20' }}>
          <Box display="flex" alignItems="center" mb={1}>
            <HelpIcon sx={{ mr: 1, color: theme.palette.info.main }} />
            <Typography variant="h6">
              Understanding Reliability Analysis
            </Typography>
          </Box>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom fontWeight="bold">
                When to use each method:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Cronbach's Alpha" 
                    secondary="For scale reliability with multiple items measuring the same construct"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Cohen's Kappa" 
                    secondary="For agreement between two raters on categorical data"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Fleiss' Kappa" 
                    secondary="For agreement among multiple raters on categorical data"
                  />
                </ListItem>
              </List>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom fontWeight="bold">
                Additional methods:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Kendall's Tau" 
                    secondary="For rank correlation between two ordinal variables"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Kendall's W" 
                    secondary="For agreement among multiple rankers"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="ICC" 
                    secondary="For reliability of continuous measurements across raters or time"
                  />
                </ListItem>
              </List>
            </Grid>
          </Grid>

          <Box mt={2}>
            <Typography variant="subtitle2" gutterBottom fontWeight="bold">
              General Guidelines:
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • Higher values indicate better reliability/agreement (usually 0-1 scale)
              <br />
              • Values above 0.7 are generally considered acceptable for research
              <br />
              • For clinical or high-stakes applications, values above 0.9 are preferred
              <br />
              • Always consider the context and purpose of your analysis
            </Typography>
          </Box>
        </Paper>
      </Box>

      {/* Quick Summary Card */}
      {(cronbachResult || cohenResult || fleissResult || kendallTauResult || kendallWResult || iccResult) && (
        <Box mt={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Analysis Summary
              </Typography>
              <Grid container spacing={2}>
                {cronbachResult && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box textAlign="center">
                      <Typography variant="subtitle2" color="text.secondary">
                        Cronbach's Alpha
                      </Typography>
                      <Typography variant="h4" color="primary">
                        {cronbachResult.value.toFixed(3)}
                      </Typography>
                      <Typography variant="caption">
                        {cronbachResult.interpretation}
                      </Typography>
                    </Box>
                  </Grid>
                )}
                {cohenResult && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box textAlign="center">
                      <Typography variant="subtitle2" color="text.secondary">
                        Cohen's Kappa
                      </Typography>
                      <Typography variant="h4" color="primary">
                        {cohenResult.value.toFixed(3)}
                      </Typography>
                      <Typography variant="caption">
                        {cohenResult.interpretation}
                      </Typography>
                    </Box>
                  </Grid>
                )}
                {fleissResult && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box textAlign="center">
                      <Typography variant="subtitle2" color="text.secondary">
                        Fleiss' Kappa
                      </Typography>
                      <Typography variant="h4" color="primary">
                        {fleissResult.value.toFixed(3)}
                      </Typography>
                      <Typography variant="caption">
                        {fleissResult.interpretation}
                      </Typography>
                    </Box>
                  </Grid>
                )}
                {kendallTauResult && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box textAlign="center">
                      <Typography variant="subtitle2" color="text.secondary">
                        Kendall's Tau
                      </Typography>
                      <Typography variant="h4" color="primary">
                        {kendallTauResult.value.toFixed(3)}
                      </Typography>
                      <Typography variant="caption">
                        {kendallTauResult.interpretation}
                      </Typography>
                    </Box>
                  </Grid>
                )}
                {kendallWResult && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box textAlign="center">
                      <Typography variant="subtitle2" color="text.secondary">
                        Kendall's W
                      </Typography>
                      <Typography variant="h4" color="primary">
                        {kendallWResult.value.toFixed(3)}
                      </Typography>
                      <Typography variant="caption">
                        {kendallWResult.interpretation}
                      </Typography>
                    </Box>
                  </Grid>
                )}
                {iccResult && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box textAlign="center">
                      <Typography variant="subtitle2" color="text.secondary">
                        ICC
                      </Typography>
                      <Typography variant="h4" color="primary">
                        {iccResult.value.toFixed(3)}
                      </Typography>
                      <Typography variant="caption">
                        {iccResult.interpretation}
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default ReliabilityAnalysis;
