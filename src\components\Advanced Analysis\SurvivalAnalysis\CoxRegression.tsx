import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  FormControlLabel,
  Checkbox,
  TextField,
  Divider,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  Timeline as TimelineIcon,
  Help as HelpIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType, Column, VariableRole } from '../../../types';
import { getOrderedCategoriesByColumnId } from '../../../utils/dataUtilities';
import { coxRegressionService, CoxRegressionResults, SurvivalPrediction, CoxRegressionData } from '../../../utils/services/coxRegressionService';

// Define the structure for Cox regression results used in the component
interface ExtendedCoxRegressionResults {
  // From service
  coefficients: { [key: string]: number };
  hazard_ratios: { [key: string]: number };
  confidence_intervals: { [key: string]: [number, number] };
  p_values: { [key: string]: number };
  std_errors: { [key: string]: number };
  concordance: number;
  log_likelihood: number;
  aic: number;
  n_observations: number;
  n_events: number;
  
  // UI-specific data
  xColumns: (Column & {
    isDummy?: boolean;
    originalColumnId?: string;
    originalColumnName?: string;
    dummyForCategory?: string;
    baseCategory?: string;
    allCategories?: string[];
  })[];
  timeColumn: Column;
  eventColumn: Column;
  xNames: string[];
  
  // Legacy compatibility for existing UI code
  n: number;
  events: number;
  coefficientsArray: number[];
  stdErrorsArray: number[];
  pValuesArray: number[];
  hazardRatiosArray: number[];
  confidenceIntervalsArray: { lower: number[]; upper: number[] };
}

interface PredictionResult {
  inputs: { name: string; value: string | number }[];
  time: number;
  hazardRatio: number;
  survivalProbability: number;
  riskCategory: string;
  error?: string;
}

const CoxRegression: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();

  // State for regression selection
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [independentVariables, setIndependentVariables] = useState<string[]>([]);
  const [timeVariable, setTimeVariable] = useState<string>('');
  const [eventVariable, setEventVariable] = useState<string>('');
  const [confInterval, setConfInterval] = useState<number>(0.95);
  const [selectedCategoricalBaseCategories, setSelectedCategoricalBaseCategories] = useState<{ [key: string]: string }>({});

  // State for event variable mapping
  const [eventVariableMapping, setEventVariableMapping] = useState<{ event: string; nonEvent: string } | null>(null);
  const [showEventMappingDialog, setShowEventMappingDialog] = useState<boolean>(false);
  const [eventVariableCategories, setEventVariableCategories] = useState<string[]>([]);
  const [selectedEventMapping, setSelectedEventMapping] = useState<{ event: string } | null>(null);

  // State for prediction
  const [predictionInputValues, setPredictionInputValues] = useState<{ [key: string]: string }>({});
  const [predictionTime, setPredictionTime] = useState<string>('');
  const [prediction, setPrediction] = useState<PredictionResult | null>(null);

  // State for display options
  const [displayOptions, setDisplayOptions] = useState({
    showConfidenceIntervals: true,
    showRegressionEquation: true,
  });

  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [initializingPython, setInitializingPython] = useState<boolean>(false);
  const [pythonReady, setPythonReady] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [regressionResults, setRegressionResults] = useState<ExtendedCoxRegressionResults | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);

  // Initialize Python environment
  const initializePython = useCallback(async () => {
    if (pythonReady || initializingPython) return;

    setInitializingPython(true);
    try {
      await coxRegressionService.initialize();
      setPythonReady(true);
    } catch (error) {
      console.error('Failed to initialize Python environment:', error);
      setError('Failed to initialize Python environment. Please refresh the page and try again.');
    } finally {
      setInitializingPython(false);
    }
  }, [pythonReady, initializingPython]);

  // Initialize Python on component mount
  useEffect(() => {
    initializePython();
  }, [initializePython]);

  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('cox_regression_results');
    if (savedResults) {
      try {
        const parsedResults = JSON.parse(savedResults);
        setRegressionResults(parsedResults);
      } catch (error) {
        console.error('Error parsing saved Cox regression results:', error);
        localStorage.removeItem('cox_regression_results');
      }
    }
  }, []);

  // Helper to get unique categories for a column
  const getUniqueCategories = (columnId: string): string[] => {
    if (!currentDataset) return [];
    const column = currentDataset.columns.find(col => col.id === columnId);
    if (!column || column.type !== DataType.CATEGORICAL) return [];

    // Use ordered categories instead of alphabetical sorting
    return getOrderedCategoriesByColumnId(columnId, currentDataset);
  };

  // Get numeric and categorical columns for independent variables
  const availableIndependentColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC || col.type === DataType.CATEGORICAL
  ) || [];

  // Get numeric columns for time variable
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];

  // Get columns for event variable (numeric binary OR categorical binary)
  const availableEventColumns = currentDataset?.columns.filter(col => {
    if (col.type === DataType.NUMERIC) {
      const values = new Set();
      let validCount = 0;
      for (const row of currentDataset?.data || []) {
        const value = row[col.name];
        if (value === 0 || value === 1) {
          values.add(value);
          validCount++;
        }
      }
      return validCount === (currentDataset?.data.length || 0) && values.size <= 2;
    }
    if (col.type === DataType.CATEGORICAL) {
      return getUniqueCategories(col.id).length === 2;
    }
    return false;
  }) || [];

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setIndependentVariables([]);
    setTimeVariable('');
    setEventVariable('');
    setRegressionResults(null);
    setPrediction(null);
    setEventVariableMapping(null);
    localStorage.removeItem('cox_regression_results');

    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };

  // Handle independent variables selection change
  const handleIndependentVariablesChange = (event: SelectChangeEvent<typeof independentVariables>) => {
    const value = event.target.value;
    const newVars = typeof value === 'string' ? value.split(',') : value;
    setIndependentVariables(newVars);

    const updatedBaseCategories = { ...selectedCategoricalBaseCategories };
    Object.keys(selectedCategoricalBaseCategories).forEach(varId => {
      if (!newVars.includes(varId)) {
        delete updatedBaseCategories[varId];
      }
    });
    setSelectedCategoricalBaseCategories(updatedBaseCategories);

    setRegressionResults(null);
    setPrediction(null);
    localStorage.removeItem('cox_regression_results');
  };

  // Handle time variable selection change
  const handleTimeVariableChange = (event: SelectChangeEvent<string>) => {
    setTimeVariable(event.target.value);
    setRegressionResults(null);
    setPrediction(null);
    localStorage.removeItem('cox_regression_results');
  };

  // Handle event variable selection change
  const handleEventVariableChange = (event: SelectChangeEvent<string>) => {
    const newVarId = event.target.value;
    setEventVariable(newVarId);
    setRegressionResults(null);
    setPrediction(null);
    setEventVariableMapping(null);
    localStorage.removeItem('cox_regression_results');

    const selectedColumn = currentDataset?.columns.find(col => col.id === newVarId);
    if (selectedColumn?.type === DataType.CATEGORICAL) {
      const categories = getUniqueCategories(newVarId);
      if (categories.length === 2) {
        setEventVariableCategories(categories);
        setSelectedEventMapping(null);
        setShowEventMappingDialog(true);
      }
    }
  };

  // Handle confidence interval change
  const handleConfIntervalChange = (event: SelectChangeEvent<number>) => {
    setConfInterval(Number(event.target.value));
    setRegressionResults(null);
    setPrediction(null);
    localStorage.removeItem('cox_regression_results');
  };

  // Handle display option change
  const handleDisplayOptionChange = (option: keyof typeof displayOptions) => {
    setDisplayOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  // Handle prediction input change
  const handlePredictionInputChange = (varId: string, value: string) => {
    setPredictionInputValues(prev => ({ ...prev, [varId]: value }));
    setPrediction(null);
  };

  // Handle prediction time change
  const handlePredictionTimeChange = (value: string) => {
    setPredictionTime(value);
    setPrediction(null);
  };

  // Handle base category change for a categorical variable
  const handleBaseCategoryChange = (variableId: string, baseCategory: string) => {
    setSelectedCategoricalBaseCategories(prev => ({
      ...prev,
      [variableId]: baseCategory,
    }));
    setRegressionResults(null);
    setPrediction(null);
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handlers for the event mapping dialog
  const handleEventMappingDialogClose = () => {
    setShowEventMappingDialog(false);
    if (!eventVariableMapping) { // if mapping was not confirmed
      setEventVariable('');
    }
  };

  const handleEventMappingSelect = (category: string) => {
    setSelectedEventMapping({ event: category });
  };

  const handleEventMappingConfirm = () => {
    if (selectedEventMapping) {
      const nonEventCategory = eventVariableCategories.find(cat => cat !== selectedEventMapping.event);
      if (nonEventCategory) {
        setEventVariableMapping({ event: selectedEventMapping.event, nonEvent: nonEventCategory });
      }
      setShowEventMappingDialog(false);
    }
  };

  // Generate prediction
  const generatePrediction = async () => {
    if (!regressionResults || !regressionResults.xColumns || !predictionTime || !pythonReady) return;

    try {
      const processedInputValues: { [key: string]: number } = {};
      let allInputsValid = true;

      const originalCategoricalVarsInfo: {
        [key: string]: {
          name: string,
          selectedValue: string,
          allCategories: string[],
          baseCategory: string
        }
      } = {};

      // Validate and collect info for original categorical variables
      for (const col of regressionResults.xColumns) {
        if (col.isDummy && col.originalColumnId && col.originalColumnName && col.allCategories && col.baseCategory) {
          if (!originalCategoricalVarsInfo[col.originalColumnId]) {
            const selectedValue = predictionInputValues[col.originalColumnId];
            if (selectedValue === undefined || String(selectedValue).trim() === '') {
              setPrediction({ error: `Please select a value for ${col.originalColumnName}` } as any);
              allInputsValid = false;
              break;
            }
            originalCategoricalVarsInfo[col.originalColumnId] = {
              name: col.originalColumnName,
              selectedValue: String(selectedValue),
              allCategories: col.allCategories,
              baseCategory: col.baseCategory
            };
          }
        }
      }
      if (!allInputsValid) return;

      // Populate processedInputValues
      for (const col of regressionResults.xColumns) {
        if (!allInputsValid) break;

        if (col.isDummy && col.originalColumnId && col.dummyForCategory) {
          const originalVarInfo = originalCategoricalVarsInfo[col.originalColumnId];
          if (!originalVarInfo) {
            setPrediction({ error: `Internal error: Missing info for ${col.originalColumnName}` } as any);
            allInputsValid = false;
            break;
          }
          processedInputValues[col.id] = (originalVarInfo.selectedValue === col.dummyForCategory) ? 1 : 0;
        } else if (!col.isDummy) {
          const valStr = predictionInputValues[col.id];
          if (valStr === undefined || String(valStr).trim() === '') {
            setPrediction({ error: `Please enter a value for ${col.name}` } as any);
            allInputsValid = false;
            break;
          }
          const valNum = parseFloat(String(valStr));
          if (isNaN(valNum)) {
            setPrediction({ error: `Invalid number entered for ${col.name}` } as any);
            allInputsValid = false;
            break;
          }
          processedInputValues[col.id] = valNum;
        }
      }

      if (!allInputsValid) return;

      const timeNum = parseFloat(predictionTime);
      if (isNaN(timeNum) || timeNum <= 0) {
        setPrediction({ error: 'Please enter a valid positive time value' } as any);
        return;
      }

      const covariateValues: { [key: string]: number } = {};
      let covariateIndex = 0;

      regressionResults.xColumns.forEach(col => {
        const pythonKey = `covariate_${covariateIndex}`;
        covariateValues[pythonKey] = processedInputValues[col.id] || 0;
        covariateIndex++;
      });

      const pyodidePrediction = await coxRegressionService.predictSurvival(
        regressionResults.coefficients,
        covariateValues,
        [timeNum]
      );

      const displayInputs: { name: string; value: string | number }[] = [];
      Object.values(originalCategoricalVarsInfo).forEach(info => {
        displayInputs.push({ name: info.name, value: info.selectedValue });
      });
      regressionResults.xColumns.forEach(col => {
        if (!col.isDummy && processedInputValues[col.id] !== undefined) {
          displayInputs.push({ name: col.name, value: processedInputValues[col.id] });
        }
      });

      setPrediction({
        inputs: displayInputs,
        time: timeNum,
        hazardRatio: pyodidePrediction.hazard_ratio,
        survivalProbability: pyodidePrediction.survival_probabilities[timeNum.toString()] || 0.5,
        riskCategory: pyodidePrediction.hazard_ratio > 1.5 ? 'High Risk' : 
                    pyodidePrediction.hazard_ratio < 0.67 ? 'Low Risk' : 'Average Risk'
      });
    } catch (e) {
      setPrediction({
        error: e instanceof Error ? e.message : String(e)
      } as any);
    }
  };

  // Run Cox regression analysis
  const runRegression = async () => {
    if (!currentDataset || independentVariables.length === 0 || !timeVariable || !eventVariable) {
      setError('Please select at least one independent variable, time variable, and event variable.');
      return;
    }

    if (!pythonReady) {
      setError('Python environment is not ready. Please wait for initialization to complete.');
      return;
    }

    setLoading(true);
    setError(null);
    setRegressionResults(null);
    setPrediction(null);

    try {
      const originalSelectedXColumns = independentVariables
        .map(id => currentDataset.columns.find(col => col.id === id))
        .filter(Boolean) as Column[];

      const timeColumn = currentDataset.columns.find(col => col.id === timeVariable);
      const eventColumn = currentDataset.columns.find(col => col.id === eventVariable);

      if (originalSelectedXColumns.length === 0 || !timeColumn || !eventColumn) {
        throw new Error('Selected variables not found in dataset.');
      }

      if (eventColumn.type === DataType.CATEGORICAL && !eventVariableMapping) {
        throw new Error(`Please specify the event mapping for "${eventColumn.name}". Re-select it to define the mapping.`);
      }

      for (const col of originalSelectedXColumns) {
        if (col.type === DataType.CATEGORICAL && !selectedCategoricalBaseCategories[col.id]) {
          throw new Error(`Please select a base category for the categorical variable "${col.name}".`);
        }
      }

      const finalPredictorColumns: (Column & {
        isDummy?: boolean;
        originalColumnId?: string;
        originalColumnName?: string;
        dummyForCategory?: string;
        baseCategory?: string;
        allCategories?: string[];
      })[] = [];
      const finalPredictorNames: string[] = [];
      const covariateData: { [key: string]: number[] } = {};

      originalSelectedXColumns.forEach(originalCol => {
        if (originalCol.type === DataType.NUMERIC) {
          finalPredictorColumns.push(originalCol);
          finalPredictorNames.push(originalCol.name);
          covariateData[`covariate_${finalPredictorColumns.length - 1}`] = [];
        } else if (originalCol.type === DataType.CATEGORICAL) {
          const baseCategory = selectedCategoricalBaseCategories[originalCol.id];
          const uniqueCategories = getUniqueCategories(originalCol.id);
          uniqueCategories.forEach(cat => {
            if (cat !== baseCategory) {
              const dummyName = `${originalCol.name} (${cat} vs ${baseCategory})`;
              finalPredictorNames.push(dummyName);
              const dummyColumn = {
                id: `${originalCol.id}_dummy_${cat}`,
                name: dummyName,
                type: DataType.NUMERIC,
                role: VariableRole.INDEPENDENT,
                description: `Dummy for ${originalCol.name}, category ${cat}, base ${baseCategory}`,
                isDummy: true,
                originalColumnId: originalCol.id,
                originalColumnName: originalCol.name,
                dummyForCategory: cat,
                baseCategory: baseCategory,
                allCategories: uniqueCategories,
              } as Column & { isDummy?: boolean; originalColumnId?: string; originalColumnName?: string; dummyForCategory?: string; baseCategory?: string; allCategories?: string[] };

              finalPredictorColumns.push(dummyColumn);
              covariateData[`covariate_${finalPredictorColumns.length - 1}`] = [];
            }
          });
        }
      });

      const timeData: number[] = [];
      const eventData: number[] = [];

      currentDataset.data.forEach((row) => {
        const timeVal = row[timeColumn.name];
        
        const rawEventVal = row[eventColumn.name];
        let eventVal: number | null = null;
        
        if (eventColumn.type === DataType.CATEGORICAL && eventVariableMapping) {
            if (rawEventVal === eventVariableMapping.event) {
                eventVal = 1;
            } else if (rawEventVal === eventVariableMapping.nonEvent) {
                eventVal = 0;
            }
        } else if (eventColumn.type === DataType.NUMERIC) {
            if (rawEventVal === 0 || rawEventVal === 1) {
                eventVal = rawEventVal as number;
            }
        }

        if (typeof timeVal !== 'number' || isNaN(timeVal) || timeVal <= 0) return;
        if (eventVal === null) return;

        let rowIsValid = true;
        const currentRowValues: number[] = [];

        originalSelectedXColumns.forEach(originalCol => {
          if (!rowIsValid) return;

          if (originalCol.type === DataType.NUMERIC) {
            const val = row[originalCol.name];
            if (typeof val !== 'number' || isNaN(val)) {
              rowIsValid = false;
              return;
            }
            currentRowValues.push(val);
          } else if (originalCol.type === DataType.CATEGORICAL) {
            const baseCategory = selectedCategoricalBaseCategories[originalCol.id];
            const actualCategoryValue = row[originalCol.name] as string;
            const uniqueCategories = getUniqueCategories(originalCol.id);

            uniqueCategories.forEach(cat => {
              if (cat !== baseCategory) {
                currentRowValues.push(actualCategoryValue === cat ? 1 : 0);
              }
            });
          }
        });

        if (rowIsValid) {
          timeData.push(timeVal as number);
          eventData.push(eventVal as number);

          currentRowValues.forEach((value, index) => {
            const key = `covariate_${index}`;
            if (!covariateData[key]) covariateData[key] = [];
            covariateData[key].push(value);
          });
        }
      });

      if (timeData.length < 10) {
        throw new Error('Not enough valid data rows for Cox regression analysis. Need at least 10 valid rows.');
      }

      const typedCovariateData: { [key: string]: number[] } = {};
      for (const key in covariateData) {
        if (Object.prototype.hasOwnProperty.call(covariateData, key)) {
          typedCovariateData[key] = covariateData[key];
        }
      }

      const regressionData: CoxRegressionData = {
        time: timeData,
        event: eventData,
        covariates: typedCovariateData
      };

      const pyodideResults = await coxRegressionService.runCoxRegression(regressionData);

      const coefficientsArray = Object.values(pyodideResults.coefficients) as number[];
      const stdErrorsArray = Object.values(pyodideResults.std_errors) as number[];
      const pValuesArray = Object.values(pyodideResults.p_values) as number[];
      const hazardRatiosArray = Object.values(pyodideResults.hazard_ratios) as number[];
      
      const confidenceIntervalsArray = {
        lower: Object.values(pyodideResults.confidence_intervals).map((ci: [number, number]) => ci[0]),
        upper: Object.values(pyodideResults.confidence_intervals).map((ci: [number, number]) => ci[1])
      };

      const resultsToSave: ExtendedCoxRegressionResults = {
        coefficients: pyodideResults.coefficients,
        hazard_ratios: pyodideResults.hazard_ratios,
        confidence_intervals: pyodideResults.confidence_intervals,
        p_values: pyodideResults.p_values,
        std_errors: pyodideResults.std_errors,
        concordance: pyodideResults.concordance,
        log_likelihood: pyodideResults.log_likelihood,
        aic: pyodideResults.aic,
        n_observations: pyodideResults.n_observations,
        n_events: pyodideResults.n_events,
        xColumns: finalPredictorColumns,
        timeColumn,
        eventColumn,
        xNames: finalPredictorNames,
        n: pyodideResults.n_observations,
        events: pyodideResults.n_events,
        coefficientsArray,
        stdErrorsArray,
        pValuesArray,
        hazardRatiosArray,
        confidenceIntervalsArray
      };

      setRegressionResults(resultsToSave);
      localStorage.setItem('cox_regression_results', JSON.stringify(resultsToSave));

    } catch (err) {
      console.error('Cox regression error:', err);
      setError(`Error in Cox regression analysis: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };

  // Generate interpretation
  const getInterpretation = () => {
    if (!regressionResults) return '';

    const { coefficientsArray, pValuesArray, n, concordance, log_likelihood, xNames, events, stdErrorsArray, confidenceIntervalsArray } = regressionResults;

    let interpretation = '';

    const predictorText = xNames && xNames.length > 1
      ? `${xNames.slice(0, -1).join(', ')} and ${xNames[xNames.length - 1]}`
      : xNames?.[0] || '';

    interpretation += `A Cox proportional hazards regression was conducted to analyze survival time based on ${predictorText}. `;
    interpretation += `The model was trained on ${n} observations with ${events} events. `;

    interpretation += `\n\nModel fit: The model achieved a concordance index of ${concordance.toFixed(3)}, `;
    if (concordance < 0.6) {
      interpretation += 'indicating poor predictive ability. ';
    } else if (concordance < 0.7) {
      interpretation += 'indicating acceptable predictive ability. ';
    } else if (concordance < 0.8) {
      interpretation += 'indicating good predictive ability. ';
    } else {
      interpretation += 'indicating excellent predictive ability. ';
    }

    interpretation += `The log-likelihood was ${log_likelihood.toFixed(2)}. `;

    interpretation += `\n\nHazard Ratios:\n`;
    if (xNames && coefficientsArray && pValuesArray && stdErrorsArray && confidenceIntervalsArray) {
      for (let i = 0; i < coefficientsArray.length; i++) {
        const coef = coefficientsArray[i];
        const name = xNames[i] || `X${i+1}`;
        const coefPValue = pValuesArray[i];
        const hazardRatio = Math.exp(coef);

        interpretation += `• ${name}: HR = ${hazardRatio.toFixed(3)} `;
        interpretation += `(95% CI: ${confidenceIntervalsArray.lower[i].toFixed(3)} - `;
        interpretation += `${confidenceIntervalsArray.upper[i].toFixed(3)}), `;
        interpretation += `p ${coefPValue < 0.001 ? '< 0.001' : '= ' + coefPValue.toFixed(3)}`;

        if (coefPValue < 0.05) {
          if (hazardRatio > 1) {
            interpretation += ` - indicates ${((hazardRatio - 1) * 100).toFixed(1)}% increased hazard`;
          } else {
            interpretation += ` - indicates ${((1 - hazardRatio) * 100).toFixed(1)}% decreased hazard`;
          }
        } else {
          interpretation += ` - not statistically significant`;
        }
        interpretation += '\n';
      }
    }

    return interpretation;
  };

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Cox Regression (Survival Analysis)
      </Typography>

      {initializingPython && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Box>
            <Typography variant="body2" gutterBottom>
              Initializing Python environment for statistical analysis...
            </Typography>
            <LinearProgress sx={{ mt: 1 }} />
          </Box>
        </Alert>
      )}

      {!pythonReady && !initializingPython && (
        <Alert severity="warning" sx={{ mb: 2 }} action={
          <Button color="inherit" size="small" onClick={initializePython} startIcon={<RefreshIcon />}>
            Retry
          </Button>
        }>
          Python environment not ready. Cox regression requires Python libraries to be loaded.
        </Alert>
      )}

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Variables
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="independent-variables-label">Covariates (X)</InputLabel>
              <Select
                labelId="independent-variables-label"
                id="independent-variables"
                multiple
                value={independentVariables}
                label="Covariates (X)"
                onChange={handleIndependentVariablesChange}
                disabled={!currentDataset}
                renderValue={(selected) => {
                  const selectedNames = selected.map(id => {
                    const column = availableIndependentColumns.find(col => col.id === id);
                    return column ? column.name : '';
                  }).filter(Boolean);
                  return selectedNames.join(', ');
                }}
              >
                {availableIndependentColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No suitable variables available
                  </MenuItem>
                ) : (
                  availableIndependentColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name} ({column.type === DataType.CATEGORICAL ? 'Categorical' : 'Numeric'})
                    </MenuItem>
                  ))
                )}
              </Select>
              <Typography variant="caption" color="text.secondary">
                Select predictor variables that may affect survival.
              </Typography>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="time-variable-label">Time Variable</InputLabel>
              <Select
                labelId="time-variable-label"
                id="time-variable"
                value={timeVariable}
                label="Time Variable"
                onChange={handleTimeVariableChange}
                disabled={!currentDataset}
              >
                {numericColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No numeric variables available
                  </MenuItem>
                ) : (
                  numericColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
              <Typography variant="caption" color="text.secondary">
                Select the time-to-event variable (must be positive).
              </Typography>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="event-variable-label">Event Variable</InputLabel>
              <Select
                labelId="event-variable-label"
                id="event-variable"
                value={eventVariable}
                label="Event Variable"
                onChange={handleEventVariableChange}
                disabled={!currentDataset}
              >
                {availableEventColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No suitable binary variables
                  </MenuItem>
                ) : (
                  availableEventColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name} ({column.type === DataType.CATEGORICAL ? 'Categorical' : 'Numeric 0/1'})
                    </MenuItem>
                  ))
                )}
              </Select>
              <Typography variant="caption" color="text.secondary">
                1 = event, 0 = censored.
              </Typography>
              {eventVariableMapping && (
                <Typography variant="caption" color="primary.main" sx={{mt: 0.5}}>
                  Mapping: "{eventVariableMapping.event}" = 1, "{eventVariableMapping.nonEvent}" = 0
                </Typography>
              )}
            </FormControl>
          </Grid>
        </Grid>

        {independentVariables.some(varId => availableIndependentColumns.find(col => col.id === varId)?.type === DataType.CATEGORICAL) && (
          <Box mt={2} p={2} border={1} borderColor="divider" borderRadius={1}>
            <Typography variant="subtitle2" gutterBottom>
              Select Base Category for Categorical Variables
            </Typography>
            <Grid container spacing={2}>
              {independentVariables.map(varId => {
                const column = availableIndependentColumns.find(col => col.id === varId);
                if (column && column.type === DataType.CATEGORICAL) {
                  const categories = getUniqueCategories(varId);
                  return (
                    <Grid item xs={12} md={4} key={varId}>
                      <FormControl fullWidth margin="normal">
                        <InputLabel id={`base-category-label-${varId}`}>Base for {column.name}</InputLabel>
                        <Select
                          labelId={`base-category-label-${varId}`}
                          value={selectedCategoricalBaseCategories[varId] || ''}
                          label={`Base for ${column.name}`}
                          onChange={(e) => handleBaseCategoryChange(varId, e.target.value as string)}
                        >
                          {categories.length === 0 ? (
                            <MenuItem value="" disabled>No categories found</MenuItem>
                          ) : (
                            categories.map(cat => (
                              <MenuItem key={cat} value={cat}>{cat}</MenuItem>
                            ))
                          )}
                        </Select>
                      </FormControl>
                    </Grid>
                  );
                }
                return null;
              })}
            </Grid>
          </Box>
        )}

        <Box mt={1}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="conf-interval-label">Confidence Level</InputLabel>
                <Select
                  labelId="conf-interval-label"
                  id="conf-interval"
                  value={confInterval}
                  label="Confidence Level"
                  onChange={handleConfIntervalChange}
                >
                  <MenuItem value={0.90}>90%</MenuItem>
                  <MenuItem value={0.95}>95%</MenuItem>
                  <MenuItem value={0.99}>99%</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        <Box mt={2}>
          <Typography variant="subtitle2" gutterBottom>
            Display Options
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showConfidenceIntervals}
                    onChange={() => handleDisplayOptionChange('showConfidenceIntervals')}
                  />
                }
                label="Show confidence intervals"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showRegressionEquation}
                    onChange={() => handleDisplayOptionChange('showRegressionEquation')}
                  />
                }
                label="Show regression equation"
              />
            </Grid>
          </Grid>
        </Box>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<TimelineIcon />}
            onClick={runRegression}
            disabled={
                loading || !pythonReady || independentVariables.length === 0 || !timeVariable || !eventVariable ||
                (currentDataset?.columns.find(c => c.id === eventVariable)?.type === DataType.CATEGORICAL && !eventVariableMapping)
            }
          >
            {loading ? 'Running Analysis...' : 'Run Cox Regression'}
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {regressionResults && !loading && (
        <>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="cox regression tabs">
                <Tab label="Model Summary" />
                <Tab label="Survival Prediction" />
                <Tab label="Interpretation" />
              </Tabs>
            </Box>

            {tabValue === 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Cox Regression Results
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Model Information
                    </Typography>

                    <TableContainer>
                      <Table size="small">
                        <TableBody>
                          <TableRow>
                            <TableCell>Time Variable</TableCell>
                            <TableCell>{regressionResults.timeColumn.name}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>Event Variable</TableCell>
                            <TableCell>{regressionResults.eventColumn.name}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>Covariates</TableCell>
                            <TableCell>
                              {regressionResults.xNames?.join(', ') || 'N/A'}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>Number of Observations</TableCell>
                            <TableCell>{regressionResults.n}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>Number of Events</TableCell>
                            <TableCell>{regressionResults.events}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>Concordance Index</TableCell>
                            <TableCell>{regressionResults.concordance.toFixed(3)}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>Log Likelihood</TableCell>
                            <TableCell>{regressionResults.log_likelihood.toFixed(2)}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>AIC</TableCell>
                            <TableCell>{regressionResults.aic.toFixed(2)}</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Coefficients and Hazard Ratios
                    </Typography>

                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Variable</TableCell>
                            <TableCell align="right">Coefficient</TableCell>
                            <TableCell align="right">Std. Error</TableCell>
                            <TableCell align="right">HR</TableCell>
                            {displayOptions.showConfidenceIntervals && (
                              <TableCell align="right">95% CI</TableCell>
                            )}
                            <TableCell align="right">p-value</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {regressionResults.coefficientsArray && regressionResults.xNames && regressionResults.coefficientsArray.map((coef: number, index: number) => (
                            <TableRow key={index}>
                              <TableCell>{regressionResults.xNames?.[index] || `X${index+1}`}</TableCell>
                              <TableCell align="right">{coef.toFixed(4)}</TableCell>
                              <TableCell align="right">{regressionResults.stdErrorsArray[index].toFixed(4)}</TableCell>
                              <TableCell align="right">{Math.exp(coef).toFixed(3)}</TableCell>
                              {displayOptions.showConfidenceIntervals && (
                                <TableCell align="right">
                                  ({regressionResults.confidenceIntervalsArray.lower[index].toFixed(3)} - {regressionResults.confidenceIntervalsArray.upper[index].toFixed(3)})
                                </TableCell>
                              )}
                              <TableCell align="right">
                                {regressionResults.pValuesArray[index] < 0.001 ? '< 0.001' : regressionResults.pValuesArray[index].toFixed(4)}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    {displayOptions.showRegressionEquation && (
                      <Box mt={2} p={2} sx={{ backgroundColor: 'background.default', borderRadius: 1 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Cox Regression Equation
                        </Typography>
                        <Typography variant="body1" sx={{ fontFamily: 'monospace' }}>
                          h(t|x) = h₀(t) × exp(
                          {regressionResults.coefficientsArray && regressionResults.xNames && regressionResults.coefficientsArray.map((coef: number, index: number) => {
                            const sign = index === 0 ? '' : coef >= 0 ? ' + ' : ' - ';
                            return `${sign}${index === 0 ? coef.toFixed(4) : Math.abs(coef).toFixed(4)} × ${regressionResults.xNames?.[index] || `X${index+1}`}`;
                          }).join('')})
                        </Typography>
                        <Typography variant="body2" color="text.secondary" mt={1}>
                          where h(t|x) is the hazard at time t given covariates x, and h₀(t) is the baseline hazard
                        </Typography>
                      </Box>
                    )}
                  </Grid>
                </Grid>
              </Box>
            )}

            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Survival Prediction Tool
                </Typography>

                <Grid container spacing={2} alignItems="flex-start">
                  {(() => {
                    if (!regressionResults || !regressionResults.xColumns) return null;
                    const inputFields: JSX.Element[] = [];
                    const processedOriginalCategoricalVars = new Set<string>();

                    const uniquePredictorIds = new Set<string>();
                    regressionResults.xColumns.forEach(col => {
                      if (col.isDummy && col.originalColumnId) {
                        uniquePredictorIds.add(col.originalColumnId);
                      } else if (!col.isDummy) {
                        uniquePredictorIds.add(col.id);
                      }
                    });
                    const numUniquePredictors = uniquePredictorIds.size;
                    const mdSize = Math.max(3, Math.floor(12 / Math.max(1, numUniquePredictors)));

                    regressionResults.xColumns.forEach(col => {
                      if (col.isDummy && col.originalColumnId && col.originalColumnName && col.allCategories && col.baseCategory) {
                        if (!processedOriginalCategoricalVars.has(col.originalColumnId)) {
                          inputFields.push(
                            <Grid item xs={12} sm={6} md={mdSize} key={col.originalColumnId}>
                              <FormControl fullWidth margin="normal">
                                <InputLabel id={`${col.originalColumnId}-predict-label`}>{col.originalColumnName}</InputLabel>
                                <Select
                                  labelId={`${col.originalColumnId}-predict-label`}
                                  id={`${col.originalColumnId}-predict-select`}
                                  value={predictionInputValues[col.originalColumnId] || ''}
                                  label={col.originalColumnName}
                                  onChange={(e) => handlePredictionInputChange(col.originalColumnId!, e.target.value as string)}
                                >
                                  {col.allCategories.map(category => (
                                    <MenuItem key={category} value={category}>
                                      {category} {category === col.baseCategory ? '(Base)' : ''}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                            </Grid>
                          );
                          processedOriginalCategoricalVars.add(col.originalColumnId);
                        }
                      } else if (!col.isDummy) {
                        inputFields.push(
                          <Grid item xs={12} sm={6} md={mdSize} key={col.id}>
                            <TextField
                              label={`Value for ${col.name}`}
                              value={predictionInputValues[col.id] || ''}
                              onChange={(e) => handlePredictionInputChange(col.id, e.target.value)}
                              fullWidth
                              type="number"
                              margin="normal"
                            />
                          </Grid>
                        );
                      }
                    });
                    return inputFields;
                  })()}

                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      label="Time Point"
                      value={predictionTime}
                      onChange={(e) => handlePredictionTimeChange(e.target.value)}
                      fullWidth
                      type="number"
                      margin="normal"
                      helperText="Enter time point for survival prediction"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<SearchIcon />}
                      onClick={generatePrediction}
                      disabled={!regressionResults || !predictionTime || !pythonReady || !(() => {
                        if (!regressionResults?.xColumns) return true;
                        let allSet = true;
                        const requiredOriginalCategoricalIds = new Set<string>();

                        regressionResults.xColumns.forEach(col => {
                          if (col.isDummy && col.originalColumnId) {
                            requiredOriginalCategoricalIds.add(col.originalColumnId);
                          } else if (!col.isDummy) {
                            if (predictionInputValues[col.id] === undefined || String(predictionInputValues[col.id]).trim() === '') {
                              allSet = false;
                            }
                          }
                        });

                        requiredOriginalCategoricalIds.forEach(originalId => {
                          if (predictionInputValues[originalId] === undefined || String(predictionInputValues[originalId]).trim() === '') {
                            allSet = false;
                          }
                        });
                        return allSet;
                      })()}
                      sx={{ mt: 1 }}
                    >
                      Predict Survival
                    </Button>
                  </Grid>

                  {prediction && !prediction.error && (
                    <Grid item xs={12}>
                      <Paper elevation={0} variant="outlined" sx={{ p: 2, mt: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Prediction Result
                        </Typography>
                        <div>
                          <Typography variant="body1" component="div">
                            Input Values:
                            <ul>
                              {prediction.inputs.map((input: {name: string, value: string | number}) => (
                                <li key={input.name}>{`${input.name} = ${input.value}`}</li>
                              ))}
                            </ul>
                          </Typography>
                        </div>
                        <Typography variant="body1" fontWeight="bold">
                          At time {prediction.time}:
                        </Typography>
                        <Typography variant="body1">
                          Hazard Ratio: {prediction.hazardRatio.toFixed(3)}
                        </Typography>
                        <Typography variant="body1">
                          Estimated Survival Probability: {(prediction.survivalProbability * 100).toFixed(1)}%
                        </Typography>
                        <Typography variant="body1" color={
                          prediction.riskCategory === 'High Risk' ? 'error.main' :
                          prediction.riskCategory === 'Low Risk' ? 'success.main' : 'text.primary'
                        }>
                          Risk Category: {prediction.riskCategory}
                        </Typography>
                      </Paper>
                    </Grid>
                  )}

                  {prediction && prediction.error && (
                    <Grid item xs={12}>
                      <Alert severity="error">
                        {prediction.error}
                      </Alert>
                    </Grid>
                  )}
                </Grid>

                {regressionResults && (
                  <Box mt={4}>
                    <Typography variant="subtitle2" gutterBottom>
                      Interpreting Predictions
                    </Typography>
                    <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                      <Typography variant="body2">
                        {'• Hazard Ratio (HR) > 1 indicates increased risk of the event occurring.'}
                      </Typography>
                      <Typography variant="body2">
                        {'• Hazard Ratio (HR) < 1 indicates decreased risk of the event occurring.'}
                      </Typography>
                      <Typography variant="body2">
                        • Survival probability represents the likelihood of surviving beyond the specified time.
                      </Typography>
                      <Typography variant="body2">
                        {'• Risk categories: High Risk (HR > 1.5), Average Risk (0.67 ≤ HR ≤ 1.5), Low Risk (HR < 0.67).'}
                      </Typography>
                    </Paper>

                    {displayOptions.showRegressionEquation && (
                      <Box mt={2} p={2} sx={{ backgroundColor: 'background.default', borderRadius: 1 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Cox Regression Equation
                        </Typography>
                        <Typography variant="body1" sx={{ fontFamily: 'monospace' }}>
                          h(t|X) = h₀(t) × exp(
                          {regressionResults.coefficientsArray && regressionResults.xNames && regressionResults.coefficientsArray.map((coef: number, index: number) => {
                            const sign = index === 0 ? '' : (coef >= 0 ? ' + ' : ' - ');
                            return `${sign}${index === 0 ? coef.toFixed(4) : Math.abs(coef).toFixed(4)} × ${regressionResults.xNames?.[index] || `X${index+1}`}`;
                          }).join('')})
                        </Typography>
                        <Typography variant="body2" color="text.secondary" mt={1}>
                          where h(t|X) is the hazard at time t given covariates X, and h₀(t) is the baseline hazard
                        </Typography>
                      </Box>
                    )}
                  </Box>
                )}
              </Box>
            )}

            {tabValue === 2 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Interpretation
                </Typography>

                <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                    {getInterpretation()}
                  </Typography>
                </Paper>

                <Box mt={4}>
                  <Typography variant="subtitle2" gutterBottom>
                    Key Cox Regression Concepts
                  </Typography>
                  <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                    <Typography variant="body2" fontWeight="bold">
                      Hazard Function
                    </Typography>
                    <Typography variant="body2" paragraph>
                      The hazard function h(t) represents the instantaneous risk of the event occurring at time t, given survival up to time t. Cox regression models this as: h(t|X) = h₀(t) × exp(β₁X₁ + ... + βₙXₙ).
                    </Typography>

                    <Typography variant="body2" fontWeight="bold">
                      Proportional Hazards Assumption
                    </Typography>
                    <Typography variant="body2" paragraph>
                      Cox regression assumes that the hazard ratio between any two individuals is constant over time. This can be tested using Schoenfeld residuals.
                    </Typography>

                    <Typography variant="body2" fontWeight="bold">
                      Hazard Ratio
                    </Typography>
                    <Typography variant="body2" paragraph>
                      {'The exponential of a coefficient (e^β) is the hazard ratio, representing the change in hazard for a one-unit increase in the covariate. HR > 1 indicates increased risk, HR < 1 indicates decreased risk.'}
                    </Typography>

                    <Typography variant="body2" fontWeight="bold">
                      Concordance Index
                    </Typography>
                    <Typography variant="body2">
                      The C-index measures the model's ability to correctly rank survival times. Values range from 0.5 (no discrimination) to 1.0 (perfect discrimination). A value of 0.7-0.8 is considered good.
                    </Typography>
                  </Paper>
                </Box>
              </Box>
            )}
          </Paper>
        </>
      )}

        <Dialog open={showEventMappingDialog} onClose={handleEventMappingDialogClose}>
            <DialogTitle>Map Event Variable to Binary (0/1)</DialogTitle>
            <DialogContent>
                <Typography gutterBottom>
                    The selected event variable "{currentDataset?.columns.find(col => col.id === eventVariable)?.name}" is categorical.
                    Please specify which value represents the <strong>event (1)</strong>. The other value will be treated as <strong>censored (0)</strong>.
                </Typography>
                <FormControl component="fieldset" sx={{ mt: 2 }}>
                    {eventVariableCategories.map(category => (
                        <FormControlLabel
                            key={category}
                            control={
                                <Checkbox
                                    checked={selectedEventMapping?.event === category}
                                    onChange={() => handleEventMappingSelect(category)}
                                />
                            }
                            label={`Map "${category}" to 1 (Event Occurred)`}
                        />
                    ))}
                </FormControl>
            </DialogContent>
            <DialogActions>
                <Button onClick={handleEventMappingDialogClose} color="secondary">
                    Cancel
                </Button>
                <Button onClick={handleEventMappingConfirm} color="primary" disabled={!selectedEventMapping}>
                    Confirm Mapping
                </Button>
            </DialogActions>
        </Dialog>

    </Box>
  );
};

export default CoxRegression;
