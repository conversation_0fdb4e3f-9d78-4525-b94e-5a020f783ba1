import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  CircularProgress,
  useTheme,
  Chip,
  FormControlLabel,
  Checkbox,
  IconButton,
  Tooltip,
  TextField,
  SelectChangeEvent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  RadioGroup,
  Radio,
} from '@mui/material';
import {
  Timeline as TimelineIcon,
  Tune as TuneIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType, Column } from '../../../types'; // Ensure Column includes 'type' if not already
import createPlotlyComponent from 'react-plotly.js/factory';
import * as Plotlyjs from 'plotly.js';
import jStat from 'jstat';

const Plotly = createPlotlyComponent(Plotlyjs);

interface KaplanMeierResult {
  survivalTable: {
    time: number;
    nRisk: number; // Matched to KaplanMeierEstimator output n_risk
    nEvent: number; // Matched to KaplanMeierEstimator output n_events
    nCensor: number; // Matched to KaplanMeierEstimator output n_censored
    survival: number;
    stdError: number; // Matched to KaplanMeierEstimator output std_err
    ciLower: number; // Matched to KaplanMeierEstimator output lower_ci
    ciUpper: number; // Matched to KaplanMeierEstimator output upper_ci
    group?: string;
  }[];
  medianSurvival: {
    group?: string;
    median: number | null;
    ciLower: number | null;
    ciUpper: number | null;
  }[];
  logRankTest?: {
    chiSquare: number;
    df: number;
    pValue: number;
  };
}

class KaplanMeierEstimator {
  static calculateKM(times: number[], events: number[], confLevel: number = 0.95) {
    const data = times.map((time, i) => ({ time, event: events[i] }))
      .sort((a, b) => a.time - b.time);

    const allUniqueTimes = [...new Set(data.map((d: any) => d.time))].sort((a, b) => a - b);

    const estimates: any[] = [];
    let survivalProb = 1.0;
    let variance = 0;
    const z = this.getZScore(confLevel);

    estimates.push({
      time: 0,
      n_risk: data.length,
      n_events: 0,
      n_censored: 0,
      survival: 1,
      std_err: 0,
      lower_ci: 1,
      upper_ci: 1,
      variance: 0
    });

    let lastEstimate = estimates[0];

    allUniqueTimes.forEach(currentTime => {
      if (currentTime === 0) return;

      const atRisk = data.filter(d => d.time >= currentTime).length;
      const eventsAtTime = data.filter(d => d.time === currentTime && d.event === 1).length;
      const censoredAtTime = data.filter(d => d.time === currentTime && d.event === 0).length;

      if (atRisk > 0) {
        let currentSurvivalProb = lastEstimate.survival;
        let currentVariance = lastEstimate.variance || 0;

        if (eventsAtTime > 0) {
          currentSurvivalProb *= (atRisk - eventsAtTime) / atRisk;
          // **CRITICAL FIX HERE for CI Bands**
          // Guard Greenwood's formula for variance against division by zero
          if (atRisk - eventsAtTime > 0) {
            currentVariance += eventsAtTime / (atRisk * (atRisk - eventsAtTime));
          }
          // If atRisk - eventsAtTime is 0, all at risk had an event.
          // Survival likely drops to 0. Variance for S=0 is typically 0.
          // stdError will be 0 if S=0, leading to CI [0,0].
        }

        const stdError = currentSurvivalProb * Math.sqrt(currentVariance);
        let ciLower, ciUpper;
        if (currentSurvivalProb > 0 && currentSurvivalProb < 1 && stdError > 0 && !isNaN(stdError) && isFinite(stdError)) {
          const logSurvival = Math.log(-Math.log(currentSurvivalProb));
          // Ensure denominator for seLogLog is not zero
          const seLogLogDenominator = currentSurvivalProb * Math.abs(Math.log(currentSurvivalProb));
          if (seLogLogDenominator > 0) {
            const seLogLog = stdError / seLogLogDenominator;
            ciLower = Math.exp(-Math.exp(logSurvival + z * seLogLog));
            ciUpper = Math.exp(-Math.exp(logSurvival - z * seLogLog));
          } else { // Should not happen if currentSurvivalProb is >0 and <1
            ciLower = currentSurvivalProb;
            ciUpper = currentSurvivalProb;
          }
        } else {
          ciLower = currentSurvivalProb;
          ciUpper = currentSurvivalProb;
        }

        const currentEstimate = {
          time: currentTime,
          n_risk: atRisk,
          n_events: eventsAtTime,
          n_censored: censoredAtTime,
          survival: currentSurvivalProb,
          std_err: isNaN(stdError) || !isFinite(stdError) ? 0 : stdError, // Handle potential NaN/Infinity stdError
          lower_ci: Math.max(0, Math.min(1, isNaN(ciLower) ? currentSurvivalProb : ciLower)),
          upper_ci: Math.max(0, Math.min(1, isNaN(ciUpper) ? currentSurvivalProb : ciUpper)),
          variance: isNaN(currentVariance) || !isFinite(currentVariance) ? lastEstimate.variance : currentVariance, // Handle potential NaN/Infinity variance
        };
        estimates.push(currentEstimate);
        lastEstimate = currentEstimate;
      }
    });
    return { estimates };
  }

  static logRankTest(times1: number[], events1: number[], times2: number[], events2: number[]) {
    const allTimes = [...new Set([
      ...times1.filter((_, i) => events1[i] === 1),
      ...times2.filter((_, i) => events2[i] === 1)
    ])].sort((a, b) => a - b);

    let observed1 = 0, expected1 = 0, variance = 0;

    allTimes.forEach(time => {
      const n1AtRisk = times1.filter(t => t >= time).length;
      const n2AtRisk = times2.filter(t => t >= time).length;
      const nAtRisk = n1AtRisk + n2AtRisk;

      const d1 = times1.filter((t, i) => t === time && events1[i] === 1).length;
      const d2 = times2.filter((t, i) => t === time && events2[i] === 1).length;
      const dTotal = d1 + d2;

      if (nAtRisk > 0 && dTotal > 0) {
        observed1 += d1;
        const e1 = (n1AtRisk / nAtRisk) * dTotal;
        expected1 += e1;
        if (nAtRisk > 1) { // Mantel-Haenszel variance
          variance += (n1AtRisk * n2AtRisk * dTotal * (nAtRisk - dTotal)) /
                     (nAtRisk * nAtRisk * (nAtRisk - 1));
        }
      }
    });

    const chiSquare = variance > 0 ? Math.pow(observed1 - expected1, 2) / variance : 0;
    const pValue = 1 - jStat.chisquare.cdf(chiSquare, 1); // df is 1 for two-group comparison
    return { test_statistic: chiSquare, p_value: pValue };
  }

  private static getZScore(confLevel: number): number {
    if (confLevel === 0.95) return 1.96;
    if (confLevel === 0.99) return 2.576;
    if (confLevel === 0.90) return 1.645;
    return 1.96; // Default
  }

  // Simplified CDF functions (consider using a library for production)
  private static chiSquareCDF(x: number, df: number): number {
    if (df === 1 && x >= 0) {
        const z = Math.sqrt(x);
        // CDF of chi-squared with 1 df is P(Z^2 <= x) = P(-sqrt(x) <= Z <= sqrt(x))
        // where Z is std normal. This is 2*normalCDF(sqrt(x)) - 1 if normalCDF gives P(X<=z)
        // Or simpler: 1 - 2 * (1 - normalCDF(Math.sqrt(x))) for upper tail if normalCDF is P(X<z)
        // Using pgamma from a library is best. For a simple approximation:
        return x > 7.8 ? 0.995 : x > 3.84 ? 0.95 : x > 2.71 ? 0.90 : x / 10; // Very rough
    }
    // Fallback for df != 1 or use a proper gamma CDF implementation
    return this.gammaCDF(x / 2, df / 2);
  }

  private static normalCDF(z: number): number { // P(X <= z)
    const t = 1 / (1 + 0.2316419 * Math.abs(z));
    const d = 0.3989423 * Math.exp(-z * z / 2);
    let prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
    if (z > 0) prob = 1 - prob;
    return prob;
  }

  private static gammaFunction(z: number): number { // Lanczos approximation
    const p = [676.5203681218851, -1259.1392167224028, 771.32342877765313, -176.61502916214059, 12.507343278686905, -0.13857109526572012, 9.9843695780195716e-6, 1.5056327351493116e-7];
    if (z < 0.5) return Math.PI / (Math.sin(Math.PI * z) * this.gammaFunction(1 - z));
    z -= 1;
    let x = 0.99999999999980993;
    for (let i = 0; i < p.length; i++) x += p[i] / (z + i + 1);
    const t = z + p.length - 0.5;
    return Math.sqrt(2 * Math.PI) * Math.pow(t, z + 0.5) * Math.exp(-t) * x;
  }

  private static incompleteGammaFunction(s:number, x:number): number { // Regularized lower incomplete gamma P(s,x)
    if (x < 0 || s <= 0) return 0;
    if (x < s + 1) {
        let sum = 1 / s;
        let term = 1 / s;
        for (let k = 1; k < 100; k++) {
            term *= x / (s + k);
            sum += term;
            if (Math.abs(term) < Math.abs(sum) * 1e-7) break;
        }
        return Math.pow(x, s) * Math.exp(-x) * sum;
    } else {
        let sum = 1;
        let term = 1;
        for (let k = 1; k < 100; k++) {
            term *= (s - k) / x ; // This is incorrect for upper incomplete.
                                 // Swapping to series for lower.
            // Correct approach is via continued fraction for x > s+1 or use upper incomplete gamma
            // For simplicity, sticking to series approximation which is better for small x
        }
        // This requires a more complex implementation for P(s,x) for x > s+1
        // Using only series for lower incomplete gamma function (good for small x/s)
        let result = 0;
        let an = 1.0/s;
        result = an;
        for(let i=1; i<100; ++i){ // Max 100 iterations
            an *= x / (s+i);
            result += an;
            if(Math.abs(an) < Math.abs(result) * 1e-10) break;
        }
        return result * Math.exp(-x + s * Math.log(x)); // This is I(x,s) not P(x,s)
    }
     // Simplified: this part requires a robust math library.
     // For now, let's assume a basic placeholder gammaCDF behavior
     return 1; // Placeholder
  }

  private static gammaCDF(x: number, alpha: number): number { // P(X <= x) where X ~ Gamma(alpha, beta=1)
    if (x <= 0) return 0;
    // This is P(alpha, x) / Gamma(alpha)
    // Needs robust incompleteGamma(alpha, x) and gamma(alpha)
    // Placeholder - replace with a math library (e.g. jStat)
    if (alpha === 1) return 1 - Math.exp(-x); // Exponential CDF special case
    if (x > 10 + alpha * 2) return 0.999; // very rough
    if (x < alpha / 2) return 0.001;
    return 0.5; // Highly inaccurate placeholder
  }


  static calculateMedianSurvival(estimates: any[], confLevel: number = 0.95) {
    const validEstimates = estimates.filter(e => !isNaN(e.survival) && e.survival !== null);
    if (validEstimates.length === 0) return { median: null, ciLower: null, ciUpper: null };

    const medianEstimate = validEstimates.find((e: any) => e.survival <= 0.5);
    if (!medianEstimate) { // Median not reached
        const lastEstimate = validEstimates[validEstimates.length - 1];
         // Check if CI ever crosses 0.5 even if median point doesn't
        const lowerCIEstimateCross = validEstimates.find((e: any) => e.lower_ci <= 0.5 && !isNaN(e.lower_ci));
        const upperCIEstimateCross = validEstimates.find((e: any) => e.upper_ci <= 0.5 && !isNaN(e.upper_ci));
        return {
            median: null, // Explicitly null if not reached
            ciLower: lowerCIEstimateCross ? lowerCIEstimateCross.time : null,
            ciUpper: upperCIEstimateCross ? upperCIEstimateCross.time : null,
         };
    }

    const medianTime = medianEstimate.time;
    const lowerCIEstimate = validEstimates.find((e: any) => e.lower_ci <= 0.5 && !isNaN(e.lower_ci));
    const ciLower = lowerCIEstimate ? lowerCIEstimate.time : null;
    const upperCIEstimate = validEstimates.find((e: any) => e.upper_ci <= 0.5 && !isNaN(e.upper_ci));
    const ciUpper = upperCIEstimate ? upperCIEstimate.time : null;

    return { median: medianTime, ciLower: ciLower, ciUpper: ciUpper };
  }
}

const formatPValue = (p: number): string => {
  if (isNaN(p) || p === null) return 'N/A';
  if (p < 0.001) return '< 0.001';
  return p.toFixed(4);
};

interface KaplanMeierAnalysisProps {
  timeVariable: string;
  eventVariable: string;
  groupVariable: string;
  confLevel: number;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;
  categoricalColumns: Column[];
  setGroupVariable: (variable: string) => void;
  groupColors: string[];
  selectedDatasetId: string;
  setSelectedDatasetId: (id: string) => void;
  setTimeVariable: (variable: string) => void;
  setEventVariable: (variable: string) => void;
  setConfLevel: (level: number) => void;
  handleDatasetChange: (event: SelectChangeEvent<string>) => void;
  numericColumns: Column[];
}

interface KmChartSettings {
  title: string;
  xAxisLabel: string;
  yAxisLabel: string;
  xAxisMin?: number;
  xAxisMax?: number;
  xAxisTickInterval?: number;
  yAxisMin?: number;
  yAxisMax?: number;
  yAxisTickInterval?: number;
}

interface ConfirmedEventMap {
  valueForOne: string;
  valueForZero: string;
}

// Helper to convert hex to rgba for Plotly fillcolor
const hexToRgba = (hex: string, alpha: number): string => {
  if (!hex || typeof hex !== 'string' || !hex.startsWith('#')) {
    // Return a default color if hex is invalid, or handle error
    return `rgba(128, 128, 128, ${alpha})`; // Default grey
  }
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  if (isNaN(r) || isNaN(g) || isNaN(b)) {
    return `rgba(128, 128, 128, ${alpha})`; // Default grey if parsing fails
  }
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};


const KaplanMeierAnalysis: React.FC<KaplanMeierAnalysisProps> = ({
  timeVariable,
  eventVariable,
  groupVariable,
  confLevel,
  loading,
  setLoading,
  error,
  setError,
  categoricalColumns,
  groupColors,
  setGroupVariable,
  selectedDatasetId,
  setSelectedDatasetId,
  setTimeVariable,
  setEventVariable,
  setConfLevel,
  handleDatasetChange,
  numericColumns,
}) => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const plotlyDivRef = useRef<HTMLDivElement>(null);

  const [kmResults, setKmResults] = useState<KaplanMeierResult | null>(null);
  const [showAtRisk, setShowAtRisk] = useState(true);
  const [showConfidenceBands, setShowConfidenceBands] = useState(true);
  const [showCensorMarks, setShowCensorMarks] = useState(true);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  const [showEventMappingDialog, setShowEventMappingDialog] = useState(false);
  const [eventVariableCategories, setEventVariableCategories] = useState<string[]>([]);
  const [selectedEventValueForOne, setSelectedEventValueForOne] = useState<string | null>(null);
  const [confirmedEventMap, setConfirmedEventMap] = useState<ConfirmedEventMap | null>(null);

  const defaultKmChartSettings: KmChartSettings = {
    title: 'Kaplan-Meier Survival Curve',
    xAxisLabel: 'Time',
    yAxisLabel: 'Survival Probability',
    yAxisMin: 0,
    yAxisMax: 1.05,
    yAxisTickInterval: 0.1,
  };
  const [kmChartSettings, setKmChartSettings] = useState<KmChartSettings>(defaultKmChartSettings);

  useEffect(() => {
    setConfirmedEventMap(null);
    setEventVariableCategories([]);
    setSelectedEventValueForOne(null);
    setShowEventMappingDialog(false);
    // setKmResults(null); // Keep results if only event var changes but might be re-run
    // setError(null); // Keep error state until next action

    if (currentDataset && eventVariable) {
      const eventCol = currentDataset.columns.find(col => col.id === eventVariable);
      if (eventCol && eventCol.type === 'categorical') {
        const uniqueValues = [
          ...new Set(
            currentDataset.data
              .map(row => row[eventCol.name])
              .filter(val => val !== null && val !== undefined && String(val).trim() !== '')
          ),
        ].map(String);

        if (uniqueValues.length === 2) {
          setEventVariableCategories(uniqueValues);
          setShowEventMappingDialog(true);
           setError(null); // Clear previous errors if we proceed to dialog
        } else if (uniqueValues.length > 0) {
          setError(
            `Categorical event variable "${eventCol.name}" must have exactly two unique non-missing values. Found ${uniqueValues.length}: ${uniqueValues.join(', ')}. Please choose a numeric (0/1) or binary categorical column.`
          );
          // setEventVariable(''); // Don't reset, let user see error and change it.
        } else if (uniqueValues.length === 0 && currentDataset.data.length > 0) {
            setError(`Categorical event variable "${eventCol.name}" has no valid data or only missing values.`);
        }
      } else {
        setError(null); // Clear error if switching to a numeric event var or no event var
      }
    }
  }, [eventVariable, currentDataset?.id]); // Removed data dependency for performance, id implies data change

  const handleEventMappingDialogClose = () => {
    setShowEventMappingDialog(false);
    if (!confirmedEventMap) {
        const eventCol = currentDataset?.columns.find(col => col.id === eventVariable);
        if (eventCol?.type === 'categorical') {
            // setError("Event variable mapping was cancelled. Please select an event variable and complete mapping if required, or choose a different variable.");
            // setEventVariable(''); // Let user decide to change or re-trigger
        }
    }
  };

  const handleEventMappingSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedEventValueForOne(event.target.value);
  };

  const handleEventMappingConfirm = () => {
    if (selectedEventValueForOne && eventVariableCategories.length === 2) {
      const valueForOne = selectedEventValueForOne;
      const valueForZero = eventVariableCategories.find(cat => cat !== valueForOne)!;
      setConfirmedEventMap({ valueForOne, valueForZero });
      setShowEventMappingDialog(false);
      setError(null);
    }
  };

  const handleKmSettingsChange = (setting: keyof KmChartSettings, value: any) => {
    setKmChartSettings(prev => ({ ...prev, [setting]: value }));
  };

  const calculateKaplanMeier = () => {
    if (!currentDataset || !timeVariable || !eventVariable) {
      setError('Please select dataset, time, and event variables.');
      return;
    }

    setLoading(true);
    setError(null);
    setKmResults(null);

    try {
      const timeCol = currentDataset.columns.find((col: Column) => col.id === timeVariable);
      const eventCol = currentDataset.columns.find((col: Column) => col.id === eventVariable);
      const groupCol = groupVariable ? currentDataset.columns.find((col: Column) => col.id === groupVariable) : null;

      if (!timeCol || !eventCol) {
        throw new Error('Selected time or event variables not found in the dataset.');
      }

      if (eventCol.type === 'categorical') {
        if (!confirmedEventMap) {
          const uniqueValues = [
            ...new Set(
              currentDataset.data
                .map(row => row[eventCol.name])
                .filter(val => val !== null && val !== undefined && String(val).trim() !== '')
            ),
          ].map(String);
          if (uniqueValues.length === 2) {
            setEventVariableCategories(uniqueValues);
            setSelectedEventValueForOne(null);
            setShowEventMappingDialog(true);
            setError('Event variable mapping is required. Please complete the mapping.');
          } else {
            setError(`Categorical event variable "${eventCol.name}" must have exactly two unique values. Cannot proceed.`);
          }
          setLoading(false);
          return;
        }
      }

      const survivalData: any[] = [];
      const groups = groupCol ? new Set<string>() : null;

      currentDataset.data.forEach((row: any) => {
        const time = row[timeCol.name];
        const rawEventValue = row[eventCol.name];
        let processedEvent: number | undefined = undefined;

        if (eventCol.type === 'categorical' && confirmedEventMap) {
          const stringVal = String(rawEventValue);
          if (stringVal === confirmedEventMap.valueForOne) processedEvent = 1;
          else if (stringVal === confirmedEventMap.valueForZero) processedEvent = 0;
        } else if (eventCol.type === 'numeric') {
          if (rawEventValue === 1 || rawEventValue === true) processedEvent = 1;
          else if (rawEventValue === 0 || rawEventValue === false) processedEvent = 0;
        }

        if (typeof time === 'number' && !isNaN(time) && time >= 0 &&
            processedEvent !== undefined && (processedEvent === 0 || processedEvent === 1)) {
          const dataPoint: any = { time: time, event: processedEvent };
          if (groupCol) {
            const group = row[groupCol.name];
            if (group !== null && group !== undefined) {
              dataPoint.group = String(group);
              groups?.add(String(group));
            } else {
              // Handle missing group values if necessary, e.g., assign to a default group or skip
            }
          }
          survivalData.push(dataPoint);
        }
      });

      if (survivalData.length === 0) {
        throw new Error('No valid survival data found after processing. Check variable selections, data types, and mappings.');
      }

      const survivalTables: any[] = [];
      const medianSurvivals: any[] = [];
      let logRankResult = undefined;

      if (groupCol && groups && groups.size > 0) { // Check groups.size > 0
        const groupArray = Array.from(groups).sort(); // Sort groups for consistent color mapping
        const groupDataMap: { [key: string]: any[] } = {};
        groupArray.forEach(group => groupDataMap[group] = survivalData.filter(d => d.group === group));

        groupArray.forEach(group => {
          if (groupDataMap[group] && groupDataMap[group].length > 0) {
            const times = groupDataMap[group].map((d: any) => d.time);
            const events = groupDataMap[group].map((d: any) => d.event);
            const km = KaplanMeierEstimator.calculateKM(times, events, confLevel);
            km.estimates.forEach((est: any) => survivalTables.push({
              time: est.time, nRisk: est.n_risk, nEvent: est.n_events, nCensor: est.n_censored,
              survival: est.survival, stdError: est.std_err, ciLower: est.lower_ci, ciUpper: est.upper_ci,
              group: group
            }));
            const median = KaplanMeierEstimator.calculateMedianSurvival(km.estimates, confLevel);
            medianSurvivals.push({ ...median, group });
          }
        });

        if (groupArray.length >= 2) {
          // Log-rank for first two groups for simplicity, or implement pairwise/overall
          const group1Data = groupDataMap[groupArray[0]];
          const group2Data = groupDataMap[groupArray[1]];
          if (group1Data?.length > 0 && group2Data?.length > 0) {
            const times1 = group1Data.map((d: any) => d.time);
            const events1 = group1Data.map((d: any) => d.event);
            const times2 = group2Data.map((d: any) => d.time);
            const events2 = group2Data.map((d: any) => d.event);
            const logRank = KaplanMeierEstimator.logRankTest(times1, events1, times2, events2);
            // For >2 groups, df would be num_groups - 1. This is for 2 groups.
            logRankResult = { chiSquare: logRank.test_statistic, df: 1, pValue: logRank.p_value };
          }
        }
      } else { // Single group or no valid group variable
        const times = survivalData.map((d: any) => d.time);
        const events = survivalData.map((d: any) => d.event);
        if (times.length > 0) {
          const km = KaplanMeierEstimator.calculateKM(times, events, confLevel);
          km.estimates.forEach((est: any) => survivalTables.push({
            time: est.time, nRisk: est.n_risk, nEvent: est.n_events, nCensor: est.n_censored,
            survival: est.survival, stdError: est.std_err, ciLower: est.lower_ci, ciUpper: est.upper_ci,
          }));
          const median = KaplanMeierEstimator.calculateMedianSurvival(km.estimates, confLevel);
          medianSurvivals.push(median);
        }
      }

      if (survivalTables.length === 0) {
          throw new Error("No survival estimates could be calculated. Ensure data is available for selected variables/groups.");
      }
      
      setKmResults({ survivalTable: survivalTables, medianSurvival: medianSurvivals, logRankTest: logRankResult });
    } catch (err) {
      console.error('Kaplan-Meier calculation error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred during Kaplan-Meier calculation');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (kmResults && plotlyDivRef.current && !loading) {
      const { data: plotData, layout: plotLayout } = getSurvivalCurveData();
      const config: Partial<Plotlyjs.Config> = { responsive: true, displaylogo: false };
      Plotlyjs.react(plotlyDivRef.current, plotData, plotLayout, config); // Use Plotly.react for updates
    } else if (!kmResults && plotlyDivRef.current) {
        Plotlyjs.purge(plotlyDivRef.current); // Clear plot if no results
    }
    // No direct cleanup function needed if using Plotly.react, as it handles updates.
    // However, explicit purge on component unmount or when results are cleared is good.
    return () => {
      if (plotlyDivRef.current && typeof Plotlyjs.purge === 'function') {
        // Plotlyjs.purge(plotlyDivRef.current); // Avoid purging on every re-render if not necessary
      }
    };
  }, [kmResults, kmChartSettings, showConfidenceBands, showCensorMarks, theme.palette.mode, loading]); // Added theme.palette.mode for dark/light changes, loading to ensure plot renders after loading


  const getSurvivalCurveData = () => {
    if (!kmResults) return { data: [], layout: {} };

    const uniqueGroups = [...new Set(kmResults.survivalTable.map((row: any) => row.group))];
    const traces: Plotlyjs.Data[] = [];
    const censorTraces: Plotlyjs.Data[] = []; // Keep censor marks separate to add them last

    const layout: Partial<Plotlyjs.Layout> = {
      title: { text: kmChartSettings.title, x: 0.05, xanchor: 'left' },
      xaxis: {
        title: { text: kmChartSettings.xAxisLabel },
        zeroline: false,
        range: [kmChartSettings.xAxisMin, kmChartSettings.xAxisMax].filter(v => v !== undefined) as [number, number] | undefined,
        dtick: kmChartSettings.xAxisTickInterval,
        autorange: kmChartSettings.xAxisMin === undefined && kmChartSettings.xAxisMax === undefined,
        showticklabels: true,
        tickmode: 'array',
        tickvals: Array.from({ length: ((kmChartSettings.xAxisMax ?? 100) - (kmChartSettings.xAxisMin ?? 0)) / (kmChartSettings.xAxisTickInterval ?? 10) + 1 }, (_, i) => (kmChartSettings.xAxisMin ?? 0) + i * (kmChartSettings.xAxisTickInterval ?? 10)).filter(tick => tick !== 0),
      },
      yaxis: {
        title: { text: kmChartSettings.yAxisLabel },
        range: [kmChartSettings.yAxisMin ?? 0, kmChartSettings.yAxisMax ?? 1.05],
        zeroline: false,
        dtick: kmChartSettings.yAxisTickInterval,
        autorange: kmChartSettings.yAxisMin === undefined && kmChartSettings.yAxisMax === undefined,
        showticklabels: true,
        tickmode: 'array',
        tickvals: Array.from({ length: ((kmChartSettings.yAxisMax ?? 1.05) - (kmChartSettings.yAxisMin ?? 0)) / (kmChartSettings.yAxisTickInterval ?? 0.1) + 1 }, (_, i) => (kmChartSettings.yAxisMin ?? 0) + i * (kmChartSettings.yAxisTickInterval ?? 0.1)).filter(tick => tick !== 0),
      },
      hovermode: 'closest',
      showlegend: true,
      margin: { t: 50, b: 50, l: 60, r: 20 },
      legend: { y: 0.5, yanchor: 'auto', traceorder: 'normal' },
      plot_bgcolor: theme.palette.background.paper, // Match paper background
      paper_bgcolor: theme.palette.background.paper,
      font: { color: theme.palette.text.primary }
    };
    
    const hasDefinedGroups = uniqueGroups.some(g => g !== undefined);

    if (!hasDefinedGroups || uniqueGroups.length === 0 || (uniqueGroups.length === 1 && uniqueGroups[0] === undefined) ) { // Single group (or no groups defined)
      const plotData = kmResults.survivalTable.filter(d => d.group === undefined);
      if (plotData.length === 0 && kmResults.survivalTable.length > 0) {
        // Fallback if filtering by undefined group yields nothing but there is data (e.g. single group data has no group property)
        // This case should ideally not happen if data processing is consistent.
        // plotData = kmResults.survivalTable;
      }


      if (plotData.length > 0) {
        traces.push({
          x: plotData.map(d => d.time), y: plotData.map(d => d.survival),
          mode: 'lines', type: 'scatter', name: groupVariable ? 'Overall' : 'Survival',
          line: { shape: 'hv', color: theme.palette.primary.main },
        });
        if (showConfidenceBands) {
          traces.push({ // Upper CI line (invisible, for fill target)
            x: plotData.map(d => d.time), y: plotData.map(d => d.ciUpper),
            mode: 'lines', type: 'scatter', name: 'Upper CI',
            line: { width: 0, shape: 'hv' }, hoverinfo: 'skip', showlegend: false,
          });
          traces.push({ // Lower CI line (fills to previous 'tonexty')
            x: plotData.map(d => d.time), y: plotData.map(d => d.ciLower),
            mode: 'lines', type: 'scatter', name: 'Lower CI',
            line: { width: 0, shape: 'hv' }, fill: 'tonexty', fillcolor: hexToRgba(theme.palette.primary.main, 0.2),
            hoverinfo: 'skip', showlegend: false,
          });
        }
        if (showCensorMarks && plotData.some(row => row.nCensor > 0)) {
          const censoredPoints = plotData.filter(row => row.nCensor > 0);
          censorTraces.push({
            x: censoredPoints.map(d => d.time), y: censoredPoints.map(d => d.survival),
            mode: 'markers', type: 'scatter', name: 'Censored',
            marker: { symbol: 'line-ns-open', size: 8, color: theme.palette.primary.main },
            hoverinfo: 'skip', showlegend: true, // Show legend for censor marks if single group
          });
        }
      }
    } else { // Multiple groups
      const groupArray = uniqueGroups.filter(group => group !== undefined).sort(); // Ensure sorted order for consistent colors
      groupArray.forEach((group, idx) => {
        const groupData = kmResults.survivalTable.filter(row => row.group === group);
        if (groupData.length === 0) return;

        const color = groupColors[idx % groupColors.length] || theme.palette.augmentColor({ color: { main: '#000000' } }).main; // Fallback color

        traces.push({
          x: groupData.map(d => d.time), y: groupData.map(d => d.survival),
          mode: 'lines', type: 'scatter', name: String(group),
          line: { shape: 'hv', color: color },
        });
        if (showConfidenceBands) {
          traces.push({ // Upper CI
            x: groupData.map(d => d.time), y: groupData.map(d => d.ciUpper),
            mode: 'lines', type: 'scatter', name: `${group} Upper CI`,
            line: { width: 0, shape: 'hv' }, hoverinfo: 'skip', showlegend: false,
          });
          traces.push({ // Lower CI
            x: groupData.map(d => d.time), y: groupData.map(d => d.ciLower),
            mode: 'lines', type: 'scatter', name: `${group} Lower CI`,
            line: { width: 0, shape: 'hv' }, fill: 'tonexty', fillcolor: hexToRgba(color, 0.2),
            hoverinfo: 'skip', showlegend: false,
          });
        }
        if (showCensorMarks && groupData.some(row => row.nCensor > 0)) {
          const censoredPoints = groupData.filter(row => row.nCensor > 0);
          censorTraces.push({
            x: censoredPoints.map(d => d.time), y: censoredPoints.map(d => d.survival),
            mode: 'markers', type: 'scatter', name: `${group} Censored`, // Individual trace for data points
            marker: { symbol: 'line-ns-open', size: 8, color: color },
            hoverinfo: 'skip', showlegend: false, // Will be covered by a single "Censored" legend item
          });
        }
      });
      if (showCensorMarks && censorTraces.length > 0) { // Add a single legend item for all censor marks
        traces.push({
          x: [null], y: [null], // Dummy data for legend only
          mode: 'markers', type: 'scatter', name: 'Censored',
          marker: { symbol: 'line-ns-open', size: 8, color: theme.palette.text.secondary },
        });
      }
    }
    return { data: traces.concat(censorTraces), layout };
  };

  const downloadChart = () => {
    if (plotlyDivRef.current && kmResults && !loading) {
      Plotlyjs.downloadImage(plotlyDivRef.current, {
        format: 'svg',
        filename: (kmChartSettings.title || 'kaplan_meier_plot').replace(/\s+/g, '_'),
        width: plotlyDivRef.current.offsetWidth || 800,
        height: plotlyDivRef.current.offsetHeight || 600,
      });
    } else {
      setError('Chart data not available for download or analysis is running.');
    }
  };

  const resetChartSettings = () => {
    setKmChartSettings(defaultKmChartSettings);
    // Plot will re-render due to useEffect dependency on kmChartSettings
  };

  const isEventColCategorical = currentDataset?.columns.find(c => c.id === eventVariable)?.type === 'categorical';
  const runAnalysisDisabled = loading || !currentDataset || !timeVariable || !eventVariable || (isEventColCategorical && !confirmedEventMap);
  const isDownloadDisabled = !kmResults || loading;

  const allColumns = currentDataset ? [...numericColumns, ...categoricalColumns] : [];


  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h5" gutterBottom>
          Kaplan-Meier Analysis
        </Typography>

        <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>Variable Selection</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="dataset-select-label">Dataset</InputLabel>
                <Select
                  labelId="dataset-select-label"
                  value={selectedDatasetId}
                  label="Dataset"
                 onChange={(e) => {
                    console.log('Dataset selection event:', e);
                    const newDatasetId = e.target.value;
                    console.log('Setting selectedDatasetId to:', newDatasetId);
                    setSelectedDatasetId(newDatasetId);
                    handleDatasetChange(e);
                  }}
                  renderValue={(selected) => {
                    console.log('Rendering dataset:', selected);
                    if (!selected) return 'Select a dataset';
                    const selectedDataset = datasets.find(dataset => dataset.id === selected);
                    return selectedDataset ? `${selectedDataset.name} (${selectedDataset.data.length} rows)` : selected;
                  }}
                >
                  {datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth margin="normal" disabled={!currentDataset}>
                <InputLabel id="time-var-label">Time Variable</InputLabel>
                <Select
                  labelId="time-var-label"
                  value={timeVariable}
                  label="Time Variable"
                  onChange={(e) => setTimeVariable(e.target.value)}
                >
                  {numericColumns.map(col => (
                    <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth margin="normal" disabled={!currentDataset}>
                <InputLabel id="event-var-label">Event Variable</InputLabel>
                <Select
                  labelId="event-var-label"
                  value={eventVariable}
                  label="Event Variable"
                  onChange={(e) => {
                    setEventVariable(e.target.value);
                    // Reset mapping if event variable changes
                    setConfirmedEventMap(null); 
                    setSelectedEventValueForOne(null);
                    setEventVariableCategories([]);
                  }}
                >
                  {allColumns.map(col => ( // Show all numeric and categorical
                    <MenuItem key={col.id} value={col.id}>
                      {col.name} {col.type === 'categorical' ? '(Cat)' : ''}
                    </MenuItem>
                  ))}
                </Select>
                <Typography variant="caption" color="text.secondary" sx={{fontSize: '0.7rem'}}>
                  Numeric: 1=Event, 0=Censor. Categorical: Will prompt for 0/1 mapping.
                </Typography>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="conf-level-label">Confidence Level</InputLabel>
                <Select
                  labelId="conf-level-label"
                  value={confLevel}
                  label="Confidence Level"
                  onChange={(e) => setConfLevel(Number(e.target.value))}
                >
                  <MenuItem value={0.90}>90%</MenuItem>
                  <MenuItem value={0.95}>95%</MenuItem>
                  <MenuItem value={0.99}>99%</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Paper>

        <Box mb={2}>
          <Grid container spacing={2} alignItems="flex-start">
            <Grid item xs={12} md={4}>
              <FormControl fullWidth disabled={!currentDataset}>
                <InputLabel id="group-var-label">Group Variable (Optional)</InputLabel>
                <Select
                  labelId="group-var-label"
                  value={groupVariable}
                  label="Group Variable (Optional)"
                  onChange={(e) => setGroupVariable(e.target.value)}
                >
                  <MenuItem value="">None</MenuItem>
                  {categoricalColumns.map(col => (
                    <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={8}>
              <Box display="flex" gap={1} flexWrap="wrap" sx={{pt: {xs: 1, md: 0.5}}}>
                <FormControlLabel control={<Checkbox size="small" checked={showAtRisk} onChange={(e) => setShowAtRisk(e.target.checked)}/>} label="Show Life Table" />
                <FormControlLabel control={<Checkbox size="small" checked={showConfidenceBands} onChange={(e) => setShowConfidenceBands(e.target.checked)}/>} label="Show CI Bands" />
                <FormControlLabel control={<Checkbox size="small" checked={showCensorMarks} onChange={(e) => setShowCensorMarks(e.target.checked)}/>} label="Show Censor Marks" />
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Button
          variant="contained"
          color="primary"
          startIcon={<TimelineIcon />}
          onClick={calculateKaplanMeier}
          disabled={runAnalysisDisabled}
          sx={{ mb: 2 }}
        >
          Run Kaplan-Meier Analysis
        </Button>
      </Grid>

      {error && (
        <Grid item xs={12}>
          <Alert severity="error" onClose={() => setError(null)} sx={{mb:2}}>{error}</Alert>
        </Grid>
      )}

      {loading && (
        <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
          <CircularProgress />
        </Grid>
      )}

      <Dialog open={showEventMappingDialog} onClose={handleEventMappingDialogClose}>
        <DialogTitle>Map Event Variable Categories</DialogTitle>
        <DialogContent>
            <Typography gutterBottom>
                The selected event variable "{currentDataset?.columns.find(col => col.id === eventVariable)?.name}"
                has categories: <strong>{eventVariableCategories[0]}</strong> and <strong>{eventVariableCategories[1]}</strong>.
                <br/>
                Please specify which value represents an event (mapped to 1). The other will be mapped to 0 (censored).
            </Typography>
            <FormControl component="fieldset" sx={{ mt: 2 }}>
                <RadioGroup
                    aria-label="event-mapping"
                    name="event-mapping-group"
                    value={selectedEventValueForOne}
                    onChange={handleEventMappingSelect}
                >
                    {eventVariableCategories.map(category => (
                        <FormControlLabel
                            key={category}
                            value={category}
                            control={<Radio />}
                            label={`Map "${category}" to 1 (Event Occurred)`}
                        />
                    ))}
                </RadioGroup>
            </FormControl>
        </DialogContent>
        <DialogActions>
            <Button onClick={handleEventMappingDialogClose}>Cancel</Button>
            <Button onClick={handleEventMappingConfirm} color="primary" disabled={!selectedEventValueForOne}>
                Confirm Mapping
            </Button>
        </DialogActions>
      </Dialog>


      {kmResults && !loading && (
        <>
          <Grid item xs={12}>
            <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="h6">{kmChartSettings.title || "Survival Curve"}</Typography>
                <Box>
                   <Tooltip title="Download SVG">
                     <span>
                       <IconButton onClick={downloadChart} disabled={isDownloadDisabled} size="small">
                         <SaveIcon />
                       </IconButton>
                     </span>
                   </Tooltip>
                  <Tooltip title="Chart Settings">
                    <IconButton onClick={() => setShowAdvancedSettings(s => !s)} color={showAdvancedSettings ? "primary" : "default"} size="small">
                      <TuneIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
              <Box sx={{ height: {xs: 300, sm: 350, md: 400}, mt:1 }}> {/* Responsive height */}
                <div ref={plotlyDivRef} id="plotlyKaplanMeierPlotDiv" style={{ width: '100%', height: '100%' }} />
              </Box>
            </Paper>
          </Grid>

          {showAdvancedSettings && (
            <Grid item xs={12}>
              <Paper elevation={2} sx={{ p: 2, mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="subtitle1">Chart Appearance</Typography>
                  <Button size="small" startIcon={<RefreshIcon />} onClick={resetChartSettings}>Reset Defaults</Button>
                </Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField fullWidth label="Chart Title" value={kmChartSettings.title} onChange={e => handleKmSettingsChange('title', e.target.value)} margin="dense" size="small"/>
                    <TextField fullWidth label="X-Axis Label" value={kmChartSettings.xAxisLabel} onChange={e => handleKmSettingsChange('xAxisLabel', e.target.value)} margin="dense" size="small"/>
                    <TextField fullWidth label="Y-Axis Label" value={kmChartSettings.yAxisLabel} onChange={e => handleKmSettingsChange('yAxisLabel', e.target.value)} margin="dense" size="small"/>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="caption" display="block" sx={{ mt: {xs:1, md:0}, mb: 0.5 }}>X-Axis Range & Ticks</Typography>
                    <Grid container spacing={1}>
                      <Grid item xs={4}><TextField fullWidth label="Min" type="number" InputLabelProps={{ shrink: true }} value={kmChartSettings.xAxisMin ?? ''} onChange={e => handleKmSettingsChange('xAxisMin', e.target.value ? Number(e.target.value) : undefined)} size="small" margin="dense" /></Grid>
                      <Grid item xs={4}><TextField fullWidth label="Max" type="number" InputLabelProps={{ shrink: true }} value={kmChartSettings.xAxisMax ?? ''} onChange={e => handleKmSettingsChange('xAxisMax', e.target.value ? Number(e.target.value) : undefined)} size="small" margin="dense" /></Grid>
                      <Grid item xs={4}><TextField fullWidth label="Tick Step" type="number" InputLabelProps={{ shrink: true }} value={kmChartSettings.xAxisTickInterval ?? ''} onChange={e => handleKmSettingsChange('xAxisTickInterval', e.target.value ? Number(e.target.value) : undefined)} size="small" margin="dense" /></Grid>
                    </Grid>
                    <Typography variant="caption" display="block" sx={{ mt: 1, mb: 0.5 }}>Y-Axis Range & Ticks</Typography>
                    <Grid container spacing={1}>
                      <Grid item xs={4}><TextField fullWidth label="Min" type="number" InputLabelProps={{ shrink: true }} value={kmChartSettings.yAxisMin ?? ''} onChange={e => handleKmSettingsChange('yAxisMin', e.target.value ? Number(e.target.value) : undefined)} size="small" margin="dense" inputProps={{ min: 0, max: 1, step: 0.05 }} /></Grid>
                      <Grid item xs={4}><TextField fullWidth label="Max" type="number" InputLabelProps={{ shrink: true }} value={kmChartSettings.yAxisMax ?? ''} onChange={e => handleKmSettingsChange('yAxisMax', e.target.value ? Number(e.target.value) : undefined)} size="small" margin="dense" inputProps={{ min: 0, max: 1.1, step: 0.05 }} /></Grid>
                      <Grid item xs={4}><TextField fullWidth label="Tick Step" type="number" InputLabelProps={{ shrink: true }} value={kmChartSettings.yAxisTickInterval ?? ''} onChange={e => handleKmSettingsChange('yAxisTickInterval', e.target.value ? Number(e.target.value) : undefined)} size="small" margin="dense" inputProps={{ min: 0.01, step: 0.01 }} /></Grid>
                    </Grid>
                  </Grid>
                </Grid>
                 <Button
                    variant="outlined"
                    onClick={() => { // This button now only re-renders the plot with current settings
                        if (kmResults && plotlyDivRef.current && !loading) {
                            const { data: plotData, layout: plotLayout } = getSurvivalCurveData();
                            Plotlyjs.react(plotlyDivRef.current, plotData, plotLayout, { responsive: true, displaylogo: false });
                        }
                    }}
                    fullWidth
                    sx={{ mt: 2 }}
                    disabled={!kmResults || loading}
                    >
                    Apply Chart Customizations
                </Button>
              </Paper>
            </Grid>
          )}

          <Grid item xs={12} md={kmResults.logRankTest ? 6 : 12}> {/* Full width if no log-rank */}
            <Paper elevation={0} variant="outlined" sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6" gutterBottom>Median Survival Times</Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      {(groupVariable && kmResults.medianSurvival.some(r => r.group)) && <TableCell>Group</TableCell>}
                      <TableCell>Median</TableCell>
                      <TableCell>{confLevel*100}% CI Lower</TableCell>
                      <TableCell>{confLevel*100}% CI Upper</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {kmResults.medianSurvival.map((row, idx) => (
                      <TableRow key={idx} hover>
                        {(groupVariable && kmResults.medianSurvival.some(r => r.group)) && <TableCell>{row.group || 'Overall'}</TableCell>}
                        <TableCell>{row.median?.toFixed(2) ?? 'Not Reached'}</TableCell>
                        <TableCell>{row.ciLower?.toFixed(2) ?? 'N/A'}</TableCell>
                        <TableCell>{row.ciUpper?.toFixed(2) ?? 'N/A'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>

          {kmResults.logRankTest && (
            <Grid item xs={12} md={6}>
              <Paper elevation={0} variant="outlined" sx={{ p: 2, height: '100%' }}>
                <Typography variant="h6" gutterBottom>Log-Rank Test</Typography>
                <TableContainer>
                  <Table size="small">
                    <TableBody>
                      <TableRow hover><TableCell>Chi-square</TableCell><TableCell>{kmResults.logRankTest.chiSquare.toFixed(4)}</TableCell></TableRow>
                      <TableRow hover><TableCell>Degrees of freedom</TableCell><TableCell>{kmResults.logRankTest.df}</TableCell></TableRow>
                      <TableRow hover>
                        <TableCell>P-value</TableCell>
                        <TableCell>
                          <Chip label={formatPValue(kmResults.logRankTest.pValue)} color={!isNaN(kmResults.logRankTest.pValue) && kmResults.logRankTest.pValue < 0.05 ? 'success' : 'default'} size="small" />
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  {!isNaN(kmResults.logRankTest.pValue) && kmResults.logRankTest.pValue < 0.05 ? 'Suggests a significant difference between groups.' : 'Suggests no significant difference between groups.'}
                </Typography>
              </Paper>
            </Grid>
          )}

          {showAtRisk && (
            <Grid item xs={12}>
              <Paper elevation={0} variant="outlined" sx={{ p: 2, mt:2 }}>
                <Typography variant="h6" gutterBottom>Life Table</Typography>
                <TableContainer sx={{ maxHeight: 400 }}>
                  <Table size="small" stickyHeader>
                    <TableHead>
                      <TableRow>
                        {(groupVariable && kmResults.survivalTable.some(r => r.group)) && <TableCell>Group</TableCell>}
                        <TableCell>Time</TableCell>
                        <TableCell align="right">At Risk</TableCell>
                        <TableCell align="right">Events</TableCell>
                        <TableCell align="right">Censored</TableCell>
                        <TableCell align="right">Survival</TableCell>
                        <TableCell align="right">Std. Error</TableCell>
                        <TableCell align="right">{confLevel*100}% CI Lower</TableCell>
                        <TableCell align="right">{confLevel*100}% CI Upper</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {kmResults.survivalTable
                        .filter(row => row.nEvent > 0 || row.nCensor > 0 || row.time === 0) 
                        .map((row, idx) => (
                          <TableRow key={idx} hover>
                            {(groupVariable && kmResults.survivalTable.some(r => r.group)) && <TableCell>{row.group || 'Overall'}</TableCell>}
                            <TableCell>{row.time.toFixed(2)}</TableCell>
                            <TableCell align="right">{row.nRisk}</TableCell>
                            <TableCell align="right">{row.nEvent}</TableCell>
                            <TableCell align="right">{row.nCensor}</TableCell>
                            <TableCell align="right">{row.survival.toFixed(4)}</TableCell>
                            <TableCell align="right">{row.stdError.toFixed(4)}</TableCell>
                            <TableCell align="right">{row.ciLower.toFixed(4)}</TableCell>
                            <TableCell align="right">{row.ciUpper.toFixed(4)}</TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Grid>
          )}
        </>
      )}
    </Grid>
  );
};

export default KaplanMeierAnalysis;
