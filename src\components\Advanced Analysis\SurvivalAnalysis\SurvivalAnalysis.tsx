import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Timeline as TimelineIcon,
  ShowChart as ShowChartIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType, Column } from '../../../types';

import KaplanMeierAnalysis from './KaplanMeierAnalysis';
import CoxRegression from './CoxRegression';
import AdditionalAnalysis from './AdditionalAnalysis';

// Tab panel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`survival-tabpanel-${index}`}
      aria-labelledby={`survival-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

// Main Survival Analysis Component
const SurvivalAnalysis: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();

  // Tab state
  const [tabValue, setTabValue] = useState(0);

  // Common state for all tabs
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  // Common state for Kaplan-Meier and Cox Regression
  const [timeVariable, setTimeVariable] = useState<string>('');
  const [eventVariable, setEventVariable] = useState<string>('');
  const [groupVariable, setGroupVariable] = useState<string>('');
  const [covariates, setCovariates] = useState<string[]>([]);
  const [confLevel, setConfLevel] = useState<number>(0.95);

  // Common state for loading and error
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get numeric columns for time and event variables
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];

  // Get all columns for grouping and covariates
  const allColumns = currentDataset?.columns || [];
  const categoricalColumns = currentDataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL
  ) || [];

  // Handle dataset change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setTimeVariable('');
    setEventVariable('');
    setGroupVariable('');
    setCovariates([]);
    
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Define colors for different groups (needed for KaplanMeierAnalysis)
  const groupColors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.error.main,
    theme.palette.warning.main,
    theme.palette.info.main,
    theme.palette.success.main
  ];


  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Survival Analysis
      </Typography>

      {/* Tabs */}
      <Paper elevation={2}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="survival analysis tabs">
          <Tab label="Kaplan-Meier Analysis" icon={<TimelineIcon />} iconPosition="start" />
          <Tab label="Cox Regression" icon={<ShowChartIcon />} iconPosition="start" />
          <Tab label="Additional Analysis" icon={<AssessmentIcon />} iconPosition="start" />
        </Tabs>

        {/* Kaplan-Meier Tab */}
        <TabPanel value={tabValue} index={0}>
          <KaplanMeierAnalysis
            timeVariable={timeVariable}
            eventVariable={eventVariable}
            groupVariable={groupVariable}
            confLevel={confLevel}
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
            categoricalColumns={categoricalColumns}
            groupColors={groupColors}
            setGroupVariable={setGroupVariable}
            // Passed from SurvivalAnalysis.tsx
            selectedDatasetId={selectedDatasetId}
            setSelectedDatasetId={setSelectedDatasetId}
            setTimeVariable={setTimeVariable}
            setEventVariable={setEventVariable}
            setConfLevel={setConfLevel}
            handleDatasetChange={handleDatasetChange}
            numericColumns={numericColumns}
          />
        </TabPanel>

        {/* Cox Regression Tab */}
        <TabPanel value={tabValue} index={1}>
          <CoxRegression />
        </TabPanel>

        {/* Additional Analysis Tab */}
        <TabPanel value={tabValue} index={2}>
           <AdditionalAnalysis
             kmResults={null} // Need to pass actual results if available
             coxResults={null} // Need to pass actual results if available
             timeVariable={timeVariable}
             eventVariable={eventVariable}
           />
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default SurvivalAnalysis;
