import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Tabs,
  Tab,
  Button,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  FormGroup,
  FormControlLabel,
  Switch,
  Slider,
  Tooltip,
  IconButton,
  Chip,
  Card,
  CardContent,
  Fade,
  useTheme,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Help as HelpIcon,
  AccountTree as TreeIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  ViewList as VariablesIcon,

} from '@mui/icons-material';
import { SelectChangeEvent } from '@mui/material/Select';
import { useData } from '@/context/DataContext';
import { DataType } from '../../../types';
import * as d3 from 'd3';

// Types and interfaces
interface TreeNode {
  id: string;
  name: string;
  variable: string;
  level: number;
  children?: TreeNode[];
  statistics?: {
    count?: number;
    mean?: number;
    median?: number;
    categories?: { [key: string]: number };
  };
  x?: number;
  y?: number;
}

interface HierarchyLevel {
  level: number;
  variable: string;
  variableName: string;
  role: 'root' | 'branch' | 'leaf';
}

interface ChartSettings {
  title: string;
  nodeSize: number;
  spacing: number;
  linkLength: number;
  colorScheme: string;
  orientation: 'vertical' | 'horizontal';
  showStatistics: boolean;
  showLabels: boolean;
  linkStyle: 'curved' | 'straight' | 'stepped';
  nodeShape: 'circle' | 'rectangle';
  rootLabel: string;
}

const defaultChartSettings: ChartSettings = {
  title: 'Variable Tree Analysis',
  nodeSize: 25,
  spacing: 100,
  linkLength: 150,
  colorScheme: 'default',
  orientation: 'horizontal',
  showStatistics: true,
  showLabels: true,
  linkStyle: 'curved',
  nodeShape: 'circle',
  rootLabel: 'Study Size',
};

// Color schemes for tree visualization
const colorSchemes = {
  default: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f'],
  muted: ['#6b7280', '#9ca3af', '#d1d5db', '#374151', '#4b5563', '#6b7280', '#9ca3af', '#d1d5db'],
  categorical: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5'],
  viridis: ['#440154', '#482777', '#3f4a8a', '#31678e', '#26838f', '#1f9d8a', '#6cce5a', '#b6de2b'],
  pastel: ['#fbb4ae', '#b3cde3', '#ccebc5', '#decbe4', '#fed9a6', '#ffffcc', '#e5d8bd', '#fddaec'],
  bright: ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628', '#f781bf'],
  earth: ['#8b4513', '#daa520', '#228b22', '#4682b4', '#cd853f', '#d2691e', '#556b2f', '#8fbc8f'],
  ocean: ['#006994', '#0085c3', '#00a9cc', '#7dd3c0', '#42b883', '#409eff', '#20a0ff', '#13ce66'],
  sunset: ['#ff6b6b', '#ffa726', '#ffcc02', '#66bb6a', '#42a5f5', '#ab47bc', '#ec407a', '#ff7043'],
};

const VariableTree: React.FC = () => {
  // Hooks
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const svgRef = useRef<SVGSVGElement>(null);

  // State management
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>('');
  const [activePanel, setActivePanel] = useState<number>(0); // 0 = variables, 1 = settings
  const [hierarchy, setHierarchy] = useState<HierarchyLevel[]>([]);
  const [chartSettings, setChartSettings] = useState<ChartSettings>(defaultChartSettings);
  const [treeData, setTreeData] = useState<TreeNode | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get active dataset
  const activeDataset = selectedDatasetId 
    ? datasets.find(d => d.id === selectedDatasetId) 
    : currentDataset;

  // Available variables (both numerical and categorical)
  const availableVariables = activeDataset?.columns.filter(col =>
    col.type === DataType.NUMERIC || col.type === DataType.CATEGORICAL
  ) || [];

  // Auto-select current dataset on mount
  useEffect(() => {
    if (currentDataset && !selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
    }
  }, [currentDataset, selectedDatasetId]);

  // Keyboard accessibility for panel switching
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.altKey && !event.ctrlKey && !event.shiftKey) {
        if (event.key === '1') {
          event.preventDefault();
          setActivePanel(0); // variables
        } else if (event.key === '2') {
          event.preventDefault();
          setActivePanel(1); // settings
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Auto-generate tree when hierarchy or settings change (with debounce)
  useEffect(() => {
    if (hierarchy.length >= 2 && activeDataset && !loading) {
      const timeoutId = setTimeout(() => {
        generateTree();
      }, 500); // Debounce to prevent rapid regeneration

      return () => clearTimeout(timeoutId);
    }
  }, [hierarchy, chartSettings, activeDataset]);

  // Handlers
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newId = event.target.value;
    setSelectedDatasetId(newId);
    setHierarchy([]);
    setTreeData(null);
    setError(null);
  };

  const handleSettingsChange = (setting: keyof ChartSettings, value: any) => {
    setChartSettings(prev => ({ ...prev, [setting]: value }));
  };

  const addVariableToHierarchy = (variable: string) => {
    if (hierarchy.length >= 4) {
      setError('Maximum of 4 variables allowed in hierarchy');
      return;
    }

    if (hierarchy.some(h => h.variable === variable)) {
      setError('Variable already in hierarchy');
      return;
    }

    // Find the column to get the display name
    const column = activeDataset?.columns.find(col => col.id === variable);
    if (!column) {
      setError('Variable not found in dataset');
      return;
    }

    const level = hierarchy.length;
    const role = level === 0 ? 'root' : level === hierarchy.length - 1 ? 'leaf' : 'branch';

    setHierarchy(prev => [...prev, {
      level,
      variable,
      variableName: column.name || variable,
      role
    }]);
    setError(null);
  };

  const removeVariableFromHierarchy = (variable: string) => {
    setHierarchy(prev => {
      const filtered = prev.filter(h => h.variable !== variable);
      // Reassign levels and roles
      return filtered.map((h, index) => ({
        ...h,
        level: index,
        role: index === 0 ? 'root' : index === filtered.length - 1 ? 'leaf' : 'branch'
      }));
    });
    // Clear tree data when hierarchy changes
    setTreeData(null);
    setError(null);
  };

  const moveVariableInHierarchy = (fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex) return;

    setHierarchy(prev => {
      const newHierarchy = [...prev];
      const [movedItem] = newHierarchy.splice(fromIndex, 1);
      newHierarchy.splice(toIndex, 0, movedItem);

      // Reassign levels and roles
      return newHierarchy.map((h, index) => ({
        ...h,
        level: index,
        role: index === 0 ? 'root' : index === newHierarchy.length - 1 ? 'leaf' : 'branch'
      }));
    });
    // Clear tree data when hierarchy changes
    setTreeData(null);
    setError(null);
  };

  const clearHierarchy = () => {
    setHierarchy([]);
    setTreeData(null);
    setError(null);
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, variable: string, fromHierarchy: boolean = false) => {
    e.dataTransfer.setData('text/plain', variable);
    e.dataTransfer.setData('application/json', JSON.stringify({ variable, fromHierarchy }));
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDropOnHierarchy = (e: React.DragEvent, targetIndex?: number) => {
    e.preventDefault();

    try {
      const dragData = JSON.parse(e.dataTransfer.getData('application/json'));
      const { variable, fromHierarchy } = dragData;

      if (fromHierarchy) {
        // Moving within hierarchy
        const currentIndex = hierarchy.findIndex(h => h.variable === variable);
        if (currentIndex !== -1 && targetIndex !== undefined) {
          moveVariableInHierarchy(currentIndex, targetIndex);
        }
      } else {
        // Adding new variable to hierarchy
        if (targetIndex !== undefined) {
          // Insert at specific position
          if (hierarchy.length >= 4) {
            setError('Maximum of 4 variables allowed in hierarchy');
            return;
          }

          if (hierarchy.some(h => h.variable === variable)) {
            setError('Variable already in hierarchy');
            return;
          }

          // Find the column to get the display name
          const column = activeDataset?.columns.find(col => col.id === variable);
          if (!column) {
            setError('Variable not found in dataset');
            return;
          }

          setHierarchy(prev => {
            const newHierarchy = [...prev];
            newHierarchy.splice(targetIndex, 0, {
              level: targetIndex,
              variable,
              variableName: column.name || variable,
              role: 'branch'
            });

            // Reassign levels and roles
            return newHierarchy.map((h, index) => ({
              ...h,
              level: index,
              role: index === 0 ? 'root' : index === newHierarchy.length - 1 ? 'leaf' : 'branch'
            }));
          });
          setError(null);
        } else {
          // Add to end
          addVariableToHierarchy(variable);
        }
      }
    } catch (err) {
      // Fallback for simple drag
      const variable = e.dataTransfer.getData('text/plain');
      if (variable) {
        addVariableToHierarchy(variable);
      }
    }
  };

  const generateTree = async () => {
    if (!activeDataset || hierarchy.length < 1) {
      setError('Please select at least 1 variable for the hierarchy');
      return;
    }

    if (hierarchy.length < 2) {
      setError('Please select at least 2 variables to create a meaningful tree hierarchy');
      return;
    }

    // Validate that all variables exist in the dataset
    const missingVariables = hierarchy.filter(h =>
      !activeDataset.columns.some(col => col.id === h.variable)
    );

    if (missingVariables.length > 0) {
      setError(`Variables not found in dataset: ${missingVariables.map(v => v.variable).join(', ')}`);
      return;
    }

    // Check for sufficient data
    if (activeDataset.data.length === 0) {
      setError('Dataset contains no data rows');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Create tree data structure
      const tree = await buildTreeFromHierarchy();

      // Only show error if we have no tree at all, not if children are empty
      if (!tree) {
        setError('Unable to create tree structure. Please check your data and variable selection.');
        setTreeData(null);
        return;
      }

      // For single-level trees or trees with no children, still show the root
      if (hierarchy.length === 1 || !tree.children || tree.children.length === 0) {
        console.log('Tree has no children, but showing root node');
      }

      setTreeData(tree);

      // Small delay to ensure DOM is ready
      setTimeout(() => {
        renderTree(tree);
      }, 100);

    } catch (err) {
      console.error('Tree generation error:', err);
      setError(`Error generating tree: ${err instanceof Error ? err.message : String(err)}`);
      setTreeData(null);
    } finally {
      setLoading(false);
    }
  };

  const buildTreeFromHierarchy = async (): Promise<TreeNode> => {
    if (!activeDataset || hierarchy.length === 0) {
      throw new Error('No dataset or hierarchy available');
    }

    console.log('Building tree with hierarchy:', hierarchy);
    console.log('Dataset:', activeDataset.name, 'Rows:', activeDataset.data.length);

    const data = activeDataset.data;
    const columns = activeDataset.columns;

    // Build tree with shift-based approach:
    // Level 0: Root showing total study size (using first variable name)
    // Level 1+: Actual hierarchy variables creating branches
    const buildNode = (level: number, parentFilters: { [key: string]: any } = {}): TreeNode => {
      console.log(`Building node at level ${level} with filters:`, parentFilters);

      let currentVariable: HierarchyLevel;
      let isRootNode = false;

      if (level === 0) {
        // Root node: use first variable but show as study root
        currentVariable = { ...hierarchy[0], variableName: `${hierarchy[0].variableName || hierarchy[0].variable} (Study)` };
        isRootNode = true;
      } else {
        // Branch nodes: use hierarchy variables (level-1 because root is level 0)
        const hierarchyIndex = level - 1;
        if (hierarchyIndex >= hierarchy.length) {
          throw new Error(`Hierarchy index ${hierarchyIndex} exceeds hierarchy length ${hierarchy.length}`);
        }
        currentVariable = hierarchy[hierarchyIndex];
      }
      const column = columns.find(col => col.id === currentVariable.variable);

      if (!column) {
        console.error(`Column ${currentVariable.variable} not found in columns:`, columns.map(c => c.id));
        throw new Error(`Column ${currentVariable.variable} not found`);
      }

      // Filter data based on parent filters
      let filteredData;

      if (Object.keys(parentFilters).length === 0) {
        // No filters for root level - use all data
        filteredData = data;
      } else {
        // Apply filters for child levels
        filteredData = data.filter(row => {
          const passes = Object.entries(parentFilters).every(([columnId, value]) => {
            // Find the column to get its name
            const filterColumn = columns.find(col => col.id === columnId);
            if (!filterColumn) {
              console.warn(`Filter column ${columnId} not found`);
              return false;
            }

            const rowValue = row[filterColumn.name]; // Use column.name as key

            // Skip null/undefined values
            if (rowValue == null || value == null) {
              return false;
            }

            // Handle different comparison types
            if (typeof value === 'string' && (value.includes('≤') || value.includes(' - ') || value.includes('>'))) {
              // Handle binned numerical data
              const numValue = parseFloat(String(rowValue));
              if (isNaN(numValue)) {
                return false;
              }

              if (value.startsWith('≤')) {
                const threshold = parseFloat(value.substring(2));
                return !isNaN(threshold) && numValue <= threshold;
              } else if (value.includes(' - ')) {
                const [min, max] = value.split(' - ').map(v => parseFloat(v.trim()));
                return !isNaN(min) && !isNaN(max) && numValue >= min && numValue <= max;
              } else if (value.startsWith('>')) {
                const threshold = parseFloat(value.substring(2));
                return !isNaN(threshold) && numValue > threshold;
              }
            }

            // Direct comparison for categorical data (case-insensitive)
            return String(rowValue).toLowerCase().trim() === String(value).toLowerCase().trim();
          });

          return passes;
        });
      }

      if (filteredData.length === 0 && level > 0) {
        console.warn(`No data found for level ${level} with filters:`, parentFilters);
      }

      // Calculate statistics for current node
      const statistics = calculateNodeStatistics(filteredData, column);

      // Create hierarchical label based on level and filters
      const hierarchicalLabel = createHierarchicalLabel(level, parentFilters, hierarchy, columns);

      // Create base node
      const node: TreeNode = {
        id: `${currentVariable.variable}_${level}_${Object.values(parentFilters).join('_')}`,
        name: hierarchicalLabel,
        variable: currentVariable.variable,
        level,
        statistics,
        children: []
      };

      // Create children based on level
      if (level === 0) {
        // Root node: create children for first variable's categories
        const firstVariable = hierarchy[0];
        const firstColumn = columns.find(col => col.id === firstVariable.variable);

        if (firstColumn) {
          const uniqueValues = getUniqueValues(filteredData, firstColumn);

          if (uniqueValues.length > 0) {
            node.children = uniqueValues.map(value => {
              const childFilters = { [firstVariable.variable]: value };
              return buildNode(level + 1, childFilters);
            }).filter(child => child.statistics && child.statistics.count && child.statistics.count > 0);
          }
        }
      } else {
        // Branch nodes: create children for next variable in hierarchy
        const nextHierarchyIndex = level; // level 1 -> hierarchy[1], level 2 -> hierarchy[2], etc.

        if (nextHierarchyIndex < hierarchy.length) {
          const nextVariable = hierarchy[nextHierarchyIndex];
          const nextColumn = columns.find(col => col.id === nextVariable.variable);

          if (nextColumn) {
            const uniqueValues = getUniqueValues(filteredData, nextColumn);

            if (uniqueValues.length > 0) {
              node.children = uniqueValues.map(value => {
                const childFilters = { ...parentFilters, [nextVariable.variable]: value };
                return buildNode(level + 1, childFilters);
              }).filter(child => child.statistics && child.statistics.count && child.statistics.count > 0);
            }
          }
        }
      }

      return node;
    };

    return buildNode(0);
  };

  const calculateNodeStatistics = (data: any[], column: any) => {
    // Use column.name as the key, not column.id
    const values = data.map(row => row[column.name]).filter(val => val != null);

    if (column.type === 'numerical') {
      const numValues = values.map(v => parseFloat(v)).filter(v => !isNaN(v));
      return {
        count: numValues.length,
        mean: numValues.length > 0 ? numValues.reduce((a, b) => a + b, 0) / numValues.length : 0,
        median: numValues.length > 0 ? calculateMedian(numValues) : 0,
      };
    } else {
      // Categorical
      const categories: { [key: string]: number } = {};
      values.forEach(val => {
        const key = String(val);
        categories[key] = (categories[key] || 0) + 1;
      });
      return {
        count: values.length,
        categories
      };
    }
  };

  const getUniqueValues = (data: any[], column: any): any[] => {
    // Use column.name as the key, not column.id
    const values = data.map(row => row[column.name]).filter(val => val != null && val !== '');
    const unique = [...new Set(values)];

    // Limit to reasonable number of unique values to prevent overcrowding
    if (unique.length > 8) {
      // For numerical data, try to create meaningful bins
      if (column.type === 'numerical') {
        const numValues = unique.map(v => parseFloat(v)).filter(v => !isNaN(v)).sort((a, b) => a - b);
        if (numValues.length > 8) {
          // Create quartile-based bins
          const q1 = calculateQuartile(numValues, 0.25);
          const q2 = calculateQuartile(numValues, 0.5);
          const q3 = calculateQuartile(numValues, 0.75);
          return [
            `≤ ${q1.toFixed(2)}`,
            `${q1.toFixed(2)} - ${q2.toFixed(2)}`,
            `${q2.toFixed(2)} - ${q3.toFixed(2)}`,
            `> ${q3.toFixed(2)}`
          ];
        }
      } else {
        // For categorical, take most frequent values
        const valueCounts = values.reduce((acc: { [key: string]: number }, val) => {
          const key = String(val);
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        }, {});

        return Object.entries(valueCounts)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 8)
          .map(([value]) => value);
      }
    }

    return unique.sort();
  };

  const calculateQuartile = (sortedNumbers: number[], percentile: number): number => {
    const index = percentile * (sortedNumbers.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    const weight = index % 1;

    if (upper >= sortedNumbers.length) return sortedNumbers[sortedNumbers.length - 1];
    return sortedNumbers[lower] * (1 - weight) + sortedNumbers[upper] * weight;
  };

  const calculateMedian = (numbers: number[]): number => {
    const sorted = [...numbers].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid];
  };

  // Helper functions for tree dimension calculation
  const getTreeDepth = (node: TreeNode): number => {
    if (!node.children || node.children.length === 0) return 1;
    return 1 + Math.max(...node.children.map(child => getTreeDepth(child)));
  };

  const getTreeWidth = (node: TreeNode): number => {
    if (!node.children || node.children.length === 0) return 1;
    return node.children.reduce((sum, child) => sum + getTreeWidth(child), 0);
  };

  // Create proper hierarchical labels for nodes
  const createHierarchicalLabel = (
    level: number,
    parentFilters: Record<string, any>,
    hierarchy: HierarchyLevel[],
    columns: any[]
  ): string => {
    if (level === 0) {
      // Root node - use custom root label completely, or fall back to variable name with label
      if (chartSettings.rootLabel && chartSettings.rootLabel.trim() !== '') {
        return chartSettings.rootLabel;
      } else {
        // Fallback to variable name if no custom label
        const rootVariable = hierarchy[0];
        return rootVariable.variableName || rootVariable.variable;
      }
    }

    // For child nodes, show the category value from the appropriate variable
    // Level 1: categories of hierarchy[0] (Gender: Male/Female)
    // Level 2: categories of hierarchy[1] (Education: High School/College/etc.)
    // Level 3: categories of hierarchy[2] (Diabetic: Yes/No)
    const variableIndex = level - 1; // Level 1 -> hierarchy[0], Level 2 -> hierarchy[1], etc.

    if (variableIndex >= 0 && variableIndex < hierarchy.length) {
      const currentLevelVariable = hierarchy[variableIndex];
      const currentValue = parentFilters[currentLevelVariable.variable];
      return currentValue !== undefined ? String(currentValue) : 'Unknown';
    }

    return 'Unknown';
  };





  const renderTree = (data: TreeNode) => {
    if (!svgRef.current || !svgRef.current.parentElement) return;

    // Clear previous content
    d3.select(svgRef.current).selectAll("*").remove();

    const svg = d3.select(svgRef.current);
    const containerRect = svgRef.current.parentElement.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    // Set up margins
    const margin = { top: 40, right: 40, bottom: 40, left: 40 };

    // Calculate required dimensions based on tree structure
    const maxDepth = getTreeDepth(data);
    const maxWidth = getTreeWidth(data);

    // Use spacing and link length settings
    const nodeSpacing = chartSettings.spacing * 0.6; // Node separation
    const levelSpacing = chartSettings.linkLength; // Distance between levels (link length)

    // Calculate actual tree dimensions
    const treeWidth = maxWidth * nodeSpacing;
    const treeHeight = maxDepth * levelSpacing;

    // Set SVG to fill container
    svg.attr("width", containerWidth).attr("height", containerHeight);

    // Create zoom behavior for pan/scroll functionality
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 3])
      .on("zoom", (event) => {
        g.attr("transform", event.transform);
      });

    // Apply zoom behavior to SVG
    svg.call(zoom);

    // Create main group for tree content
    const g = svg.append("g")
      .attr("class", "tree-content");

    // Create tree layout with compact spacing
    const treeLayout = d3.tree<TreeNode>()
      .nodeSize(chartSettings.orientation === 'vertical'
        ? [nodeSpacing, levelSpacing]
        : [levelSpacing, nodeSpacing])
      .separation((a, b) => {
        // More compact separation
        return a.parent === b.parent ? 0.8 : 1.0;
      });

    // Create hierarchy
    const root = d3.hierarchy(data);
    const treeData = treeLayout(root);

    // Calculate bounds for centering (since nodeSize positions around 0,0)
    let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
    treeData.descendants().forEach(d => {
      if (d.x < minX) minX = d.x;
      if (d.x > maxX) maxX = d.x;
      if (d.y < minY) minY = d.y;
      if (d.y > maxY) maxY = d.y;
    });

    // Calculate centering offset to center the tree in the container
    const centerX = (containerWidth - (maxX - minX)) / 2 - minX;
    const centerY = (containerHeight - (maxY - minY)) / 2 - minY;

    // Set initial transform to center the tree
    const initialTransform = d3.zoomIdentity.translate(centerX, centerY);
    svg.call(zoom.transform, initialTransform);

    // Get color scheme and create variable-based color mapping
    const colors = colorSchemes[chartSettings.colorScheme as keyof typeof colorSchemes] || colorSchemes.muted;

    // Create consistent color mapping for each variable level (use original hierarchy for colors)
    const variableColorMap = new Map<string, string>();
    hierarchy.forEach((level, index) => {
      variableColorMap.set(level.variable, colors[index % colors.length]);
    });

    // Create links with correct orientation (no manual centering needed with zoom)
    const linkGenerator = chartSettings.linkStyle === 'curved'
      ? (chartSettings.orientation === 'vertical'
          ? d3.linkVertical<any, d3.HierarchyPointNode<TreeNode>>()
              .x(d => d.x)
              .y(d => d.y)
          : d3.linkHorizontal<any, d3.HierarchyPointNode<TreeNode>>()
              .x(d => d.y)
              .y(d => d.x))
      : null;

    // Draw links
    g.selectAll(".link")
      .data(treeData.links())
      .enter().append("path")
      .attr("class", "link")
      .attr("d", d => {
        if (chartSettings.linkStyle === 'curved' && linkGenerator) {
          return linkGenerator(d);
        } else if (chartSettings.linkStyle === 'straight') {
          const source = chartSettings.orientation === 'vertical'
            ? [d.source.x, d.source.y]
            : [d.source.y, d.source.x];
          const target = chartSettings.orientation === 'vertical'
            ? [d.target.x, d.target.y]
            : [d.target.y, d.target.x];
          return `M${source[0]},${source[1]}L${target[0]},${target[1]}`;
        } else {
          // Stepped
          const source = chartSettings.orientation === 'vertical'
            ? [d.source.x, d.source.y]
            : [d.source.y, d.source.x];
          const target = chartSettings.orientation === 'vertical'
            ? [d.target.x, d.target.y]
            : [d.target.y, d.target.x];
          const midY = chartSettings.orientation === 'vertical'
            ? (source[1] + target[1]) / 2
            : (source[0] + target[0]) / 2;

          return chartSettings.orientation === 'vertical'
            ? `M${source[0]},${source[1]}V${midY}H${target[0]}V${target[1]}`
            : `M${source[0]},${source[1]}H${midY}V${target[1]}H${target[0]}`;
        }
      })
      .style("fill", "none")
      .style("stroke", theme.palette.divider)
      .style("stroke-width", 2);

    // Draw nodes (positioning handled by zoom transform)
    const nodes = g.selectAll(".node")
      .data(treeData.descendants())
      .enter().append("g")
      .attr("class", "node")
      .attr("transform", d => {
        // Use calculated position without manual centering
        const x = chartSettings.orientation === 'vertical' ? d.x : d.y;
        const y = chartSettings.orientation === 'vertical' ? d.y : d.x;
        return `translate(${x},${y})`;
      });

    // Add node shapes (circles or rectangles)
    if (chartSettings.nodeShape === 'circle') {
      nodes.append("circle")
        .attr("r", chartSettings.nodeSize / 2)
        .style("fill", (d) => variableColorMap.get(d.data.variable) || colors[d.depth % colors.length])
        .style("stroke", theme.palette.background.paper)
        .style("stroke-width", 3)
        .style("cursor", "pointer")
        .on("mouseover", function() {
          d3.select(this)
            .transition()
            .duration(200)
            .attr("r", chartSettings.nodeSize / 2 + 5)
            .style("stroke-width", 4);
        })
        .on("mouseout", function() {
          d3.select(this)
            .transition()
            .duration(200)
            .attr("r", chartSettings.nodeSize / 2)
            .style("stroke-width", 3);
        });
    } else {
      nodes.append("rect")
        .attr("width", chartSettings.nodeSize)
        .attr("height", chartSettings.nodeSize)
        .attr("x", -chartSettings.nodeSize / 2)
        .attr("y", -chartSettings.nodeSize / 2)
        .attr("rx", 4) // Rounded corners
        .style("fill", (d) => variableColorMap.get(d.data.variable) || colors[d.depth % colors.length])
        .style("stroke", theme.palette.background.paper)
        .style("stroke-width", 3)
        .style("cursor", "pointer")
        .on("mouseover", function() {
          d3.select(this)
            .transition()
            .duration(200)
            .attr("width", chartSettings.nodeSize + 10)
            .attr("height", chartSettings.nodeSize + 10)
            .attr("x", -(chartSettings.nodeSize + 10) / 2)
            .attr("y", -(chartSettings.nodeSize + 10) / 2)
            .style("stroke-width", 4);
        })
        .on("mouseout", function() {
          d3.select(this)
            .transition()
            .duration(200)
            .attr("width", chartSettings.nodeSize)
            .attr("height", chartSettings.nodeSize)
            .attr("x", -chartSettings.nodeSize / 2)
            .attr("y", -chartSettings.nodeSize / 2)
            .style("stroke-width", 3);
        });
    }

    // Add dragging visual feedback styles
    svg.selectAll(".node.dragging circle")
      .style("stroke", theme.palette.primary.main)
      .style("stroke-width", 4)
      .style("filter", "drop-shadow(2px 2px 4px rgba(0,0,0,0.3))");

    // Add node labels with better positioning
    if (chartSettings.showLabels) {
      nodes.append("text")
        .attr("dy", chartSettings.orientation === 'vertical'
          ? chartSettings.nodeSize / 2 + 15
          : 4) // Center vertically for horizontal layout
        .attr("dx", chartSettings.orientation === 'vertical'
          ? 0
          : chartSettings.nodeSize / 2 + 10) // Offset to the right for horizontal layout
        .attr("text-anchor", chartSettings.orientation === 'vertical' ? "middle" : "start")
        .style("font-size", "11px")
        .style("font-weight", "bold")
        .style("fill", theme.palette.text.primary)
        .text(d => d.data.name);
    }

    // Add statistics with better positioning
    if (chartSettings.showStatistics) {
      nodes.append("text")
        .attr("dy", chartSettings.orientation === 'vertical'
          ? chartSettings.nodeSize / 2 + (chartSettings.showLabels ? 30 : 15)
          : (chartSettings.showLabels ? 16 : 4)) // Below label for horizontal layout
        .attr("dx", chartSettings.orientation === 'vertical'
          ? 0
          : chartSettings.nodeSize / 2 + 10) // Align with label for horizontal layout
        .attr("text-anchor", chartSettings.orientation === 'vertical' ? "middle" : "start")
        .style("font-size", "9px")
        .style("fill", theme.palette.text.secondary)
        .text(d => {
          const stats = d.data.statistics;
          if (stats?.mean !== undefined) {
            return `μ=${stats.mean.toFixed(1)}, n=${stats.count}`;
          } else if (stats?.categories) {
            const total = Object.values(stats.categories).reduce((a: number, b: number) => a + b, 0);
            return `n=${total}`;
          }
          return `n=${stats?.count || 0}`;
        });
    }

    // Add title
    svg.append("text")
      .attr("x", containerWidth / 2)
      .attr("y", 25)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .style("fill", theme.palette.text.primary)
      .text(chartSettings.title);

    // Add variable legend
    if (hierarchy.length > 0) {
      const legendGroup = svg.append("g")
        .attr("class", "legend")
        .attr("transform", `translate(20, 50)`);

      hierarchy.forEach((level, index) => {
        const variableName = level.variableName || level.variable;
        const color = variableColorMap.get(level.variable) || colors[index % colors.length];

        const legendItem = legendGroup.append("g")
          .attr("transform", `translate(0, ${index * 25})`);

        // Legend color box
        legendItem.append("rect")
          .attr("width", 15)
          .attr("height", 15)
          .attr("rx", 2)
          .style("fill", color)
          .style("stroke", theme.palette.background.paper)
          .style("stroke-width", 1);

        // Legend text
        legendItem.append("text")
          .attr("x", 20)
          .attr("y", 12)
          .style("font-size", "12px")
          .style("fill", theme.palette.text.primary)
          .text(variableName);
      });
    }
  };

  const downloadChart = () => {
    if (!treeData || !svgRef.current) {
      setError('No tree data available for download');
      return;
    }

    try {
      // Clone the SVG element
      const svgElement = svgRef.current;
      const svgData = new XMLSerializer().serializeToString(svgElement);

      // Create blob and download
      const blob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `${chartSettings.title.replace(/\s+/g, '_')}_tree.svg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);
    } catch (err) {
      setError(`Error downloading chart: ${err instanceof Error ? err.message : String(err)}`);
    }
  };

  const resetSettings = () => {
    setChartSettings(defaultChartSettings);
  };

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Variable Tree Analysis
      </Typography>

      {/* Information Section */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box display="flex" alignItems="center" gap={1}>
            <HelpIcon color="primary" />
            <Typography variant="subtitle1">About Variable Tree Analysis</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" color="text.secondary" paragraph>
            Variable Tree Analysis creates hierarchical visualizations showing relationships between 2-4 variables from your dataset.
            Build custom tree structures to explore how variables relate to each other at different levels.
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            <strong>How to use:</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary" component="div" sx={{ ml: 2 }}>
            • Select 2-4 variables from your dataset<br/>
            • Drag variables to build hierarchy or click to add in order<br/>
            • Root variable forms the base of the tree<br/>
            • Each level splits data based on the variable values<br/>
            • Customize appearance using the Settings panel
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph sx={{ mt: 1 }}>
            <strong>Keyboard Shortcuts:</strong> Alt+1 (Variables Panel), Alt+2 (Settings Panel)
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>Best practices:</strong> Use categorical variables for clear splits, or numerical variables will be automatically binned.
          </Typography>
        </AccordionDetails>
      </Accordion>

      {/* Three-Panel Layout */}
      <Grid container spacing={2}>
        {/* Left Panel Container - Variables or Settings */}
        <Grid item xs={12} sm={12} md={3} lg={3}>
          {/* Panel Toggle Tabs */}
          <Paper
            elevation={1}
            sx={{
              mb: 1,
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)'
            }}
          >
            <Tabs
              value={activePanel}
              onChange={(_, newValue) => setActivePanel(newValue)}
              variant="fullWidth"
              sx={{
                minHeight: 44,
                '& .MuiTab-root': {
                  minHeight: 44,
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  color: theme.palette.text.secondary,
                  textTransform: 'none',
                  transition: 'all 0.2s ease-in-out',
                  '&.Mui-selected': {
                    color: theme.palette.primary.main,
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(25, 118, 210, 0.08)',
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
                  }
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                }
              }}
            >
              <Tooltip title="Variable Selection Panel" placement="top">
                <Tab
                  label="Variables"
                  value={0}
                  icon={<VariablesIcon />}
                  iconPosition="start"
                />
              </Tooltip>
              <Tooltip title="Chart Settings Panel" placement="top">
                <Tab
                  label="Settings"
                  value={1}
                  icon={<SettingsIcon />}
                  iconPosition="start"
                />
              </Tooltip>
            </Tabs>
          </Paper>

          {/* Variables Panel */}
          <Fade in={activePanel === 0} timeout={300}>
            <Box sx={{ display: activePanel === 0 ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 1
                }}>
                  Variable Selection
                </Typography>

                {/* Dataset Selection */}
                <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                  <InputLabel>Dataset</InputLabel>
                  <Select
                    value={selectedDatasetId}
                    onChange={handleDatasetChange}
                    label="Dataset"
                  >
                    {datasets.map((dataset) => (
                      <MenuItem key={dataset.id} value={dataset.id}>
                        {dataset.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Current Hierarchy */}
                {hierarchy.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="subtitle2">
                        Current Hierarchy ({hierarchy.length}/4)
                      </Typography>
                      <Button
                        size="small"
                        onClick={clearHierarchy}
                        color="error"
                        variant="text"
                      >
                        Clear All
                      </Button>
                    </Box>

                    {/* Hierarchy Drop Zone */}
                    <Box
                      sx={{
                        border: `2px dashed ${theme.palette.divider}`,
                        borderRadius: 1,
                        p: 1,
                        minHeight: 120,
                        backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                      }}
                      onDragOver={handleDragOver}
                      onDrop={(e) => handleDropOnHierarchy(e)}
                    >
                      {hierarchy.length === 0 ? (
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: 100,
                            color: 'text.secondary'
                          }}
                        >
                          <TreeIcon sx={{ fontSize: 32, mb: 1, opacity: 0.5 }} />
                          <Typography variant="body2" textAlign="center">
                            Drag variables here to build hierarchy
                          </Typography>
                        </Box>
                      ) : (
                        hierarchy.map((item, index) => (
                          <Box
                            key={item.variable}
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              mb: 1,
                              p: 1,
                              backgroundColor: theme.palette.background.paper,
                              borderRadius: 1,
                              border: `1px solid ${theme.palette.divider}`,
                              cursor: 'grab',
                              '&:hover': {
                                backgroundColor: theme.palette.action.hover,
                              }
                            }}
                            draggable
                            onDragStart={(e) => handleDragStart(e, item.variable, true)}
                            onDragOver={handleDragOver}
                            onDrop={(e) => handleDropOnHierarchy(e, index)}
                          >
                            <Box
                              sx={{
                                width: 24,
                                height: 24,
                                borderRadius: '50%',
                                backgroundColor: item.role === 'root' ? theme.palette.primary.main :
                                                item.role === 'leaf' ? theme.palette.secondary.main :
                                                theme.palette.grey[500],
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '0.75rem',
                                fontWeight: 'bold',
                                mr: 1,
                                flexShrink: 0
                              }}
                            >
                              {index + 1}
                            </Box>
                            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                              <Typography variant="body2" fontWeight="medium" noWrap>
                                {item.variableName}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {item.role} • Level {item.level + 1}
                              </Typography>
                            </Box>
                            <IconButton
                              size="small"
                              onClick={() => removeVariableFromHierarchy(item.variable)}
                              sx={{ ml: 1, flexShrink: 0 }}
                            >
                              <Typography variant="body2">×</Typography>
                            </IconButton>
                          </Box>
                        ))
                      )}
                    </Box>
                  </Box>
                )}

                {/* Available Variables */}
                <Typography variant="subtitle2" gutterBottom>
                  Available Variables
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  Click to add or drag to hierarchy
                </Typography>
                <Box sx={{ maxHeight: 300, overflowY: 'auto' }}>
                  {availableVariables.map((column) => {
                    const isInHierarchy = hierarchy.some(h => h.variable === column.id);
                    return (
                      <Card
                        key={column.id}
                        sx={{
                          mb: 1,
                          cursor: isInHierarchy ? 'default' : 'grab',
                          opacity: isInHierarchy ? 0.5 : 1,
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            backgroundColor: isInHierarchy ? 'inherit' : theme.palette.action.hover,
                            transform: isInHierarchy ? 'none' : 'translateX(4px)',
                          }
                        }}
                        draggable={!isInHierarchy}
                        onDragStart={(e) => !isInHierarchy && handleDragStart(e, column.id)}
                        onClick={() => !isInHierarchy && addVariableToHierarchy(column.id)}
                      >
                        <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Box
                              sx={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                backgroundColor: column.type === DataType.NUMERIC ? theme.palette.primary.main : theme.palette.secondary.main,
                                flexShrink: 0
                              }}
                            />
                            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                              <Typography variant="body2" fontWeight="medium" noWrap>
                                {column.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {column.type} • {column.id}
                              </Typography>
                            </Box>
                            {isInHierarchy && (
                              <Chip
                                label="Added"
                                size="small"
                                color="primary"
                                variant="outlined"
                                sx={{ fontSize: '0.6rem', height: 20 }}
                              />
                            )}
                          </Box>
                        </CardContent>
                      </Card>
                    );
                  })}

                  {availableVariables.length === 0 && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: 100,
                        color: 'text.secondary'
                      }}
                    >
                      <Typography variant="body2" textAlign="center">
                        No variables available
                      </Typography>
                      <Typography variant="caption" textAlign="center">
                        Please select a dataset with numerical or categorical variables
                      </Typography>
                    </Box>
                  )}
                </Box>

                {/* Action Buttons */}
                <Box mt={2} display="flex" gap={1} flexDirection="column">
                  <Button
                    variant="contained"
                    onClick={generateTree}
                    disabled={hierarchy.length < 2 || loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <TreeIcon />}
                    fullWidth
                  >
                    {loading ? 'Generating...' : 'Generate Tree'}
                  </Button>
                  <Box display="flex" gap={1} justifyContent="center">
                    <Tooltip title="Download Chart">
                      <span>
                        <IconButton onClick={downloadChart} disabled={!treeData}>
                          <SaveIcon />
                        </IconButton>
                      </span>
                    </Tooltip>
                    <Tooltip title="Reset Settings">
                      <IconButton onClick={resetSettings}>
                        <RefreshIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>

          {/* Settings Panel */}
          <Fade in={activePanel === 1} timeout={300}>
            <Box sx={{ display: activePanel === 1 ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 1
                }}>
                  Tree Settings
                </Typography>

                <Box display="flex" flexDirection="column" gap={3}>
                  {/* Title Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Title & Labels
                    </Typography>
                    <TextField
                      fullWidth
                      size="small"
                      label="Chart Title"
                      value={chartSettings.title}
                      onChange={(e) => handleSettingsChange('title', e.target.value)}
                      margin="dense"
                    />
                    <TextField
                      fullWidth
                      size="small"
                      label="Root Node Label"
                      value={chartSettings.rootLabel}
                      onChange={(e) => handleSettingsChange('rootLabel', e.target.value)}
                      margin="dense"
                      helperText="Complete override of root node label (e.g., Study Size, Cohort Size, Sample Size, Total Participants)"
                    />
                  </Box>

                  {/* Layout Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Layout & Orientation
                    </Typography>
                    <FormControl fullWidth size="small" margin="dense">
                      <InputLabel>Orientation</InputLabel>
                      <Select
                        value={chartSettings.orientation}
                        onChange={(e) => handleSettingsChange('orientation', e.target.value)}
                        label="Orientation"
                      >
                        <MenuItem value="vertical">Vertical (Top to Bottom)</MenuItem>
                        <MenuItem value="horizontal">Horizontal (Left to Right)</MenuItem>
                      </Select>
                    </FormControl>

                    <FormControl fullWidth size="small" margin="dense">
                      <InputLabel>Link Style</InputLabel>
                      <Select
                        value={chartSettings.linkStyle}
                        onChange={(e) => handleSettingsChange('linkStyle', e.target.value)}
                        label="Link Style"
                      >
                        <MenuItem value="curved">Curved</MenuItem>
                        <MenuItem value="straight">Straight</MenuItem>
                        <MenuItem value="stepped">Stepped</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>

                  {/* Appearance Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Appearance
                    </Typography>
                    <FormControl fullWidth size="small" margin="dense">
                      <InputLabel>Color Scheme</InputLabel>
                      <Select
                        value={chartSettings.colorScheme}
                        onChange={(e) => handleSettingsChange('colorScheme', e.target.value)}
                        label="Color Scheme"
                      >
                        {Object.keys(colorSchemes).map((scheme) => (
                          <MenuItem key={scheme} value={scheme}>
                            {scheme.charAt(0).toUpperCase() + scheme.slice(1)}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>

                  {/* Size & Spacing Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Size & Spacing
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Node Size: {chartSettings.nodeSize}px
                      </Typography>
                      <Slider
                        value={chartSettings.nodeSize}
                        onChange={(_, value) => handleSettingsChange('nodeSize', value)}
                        step={5}
                        marks
                        min={20}
                        max={80}
                        valueLabelDisplay="auto"
                        size="small"
                      />
                    </Box>

                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Node Spacing: {chartSettings.spacing}px
                      </Typography>
                      <Slider
                        value={chartSettings.spacing}
                        onChange={(_, value) => handleSettingsChange('spacing', value)}
                        step={20}
                        marks
                        min={40}
                        max={300}
                        valueLabelDisplay="auto"
                        size="small"
                      />
                    </Box>

                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Link Length: {chartSettings.linkLength}px
                      </Typography>
                      <Slider
                        value={chartSettings.linkLength}
                        onChange={(_, value) => handleSettingsChange('linkLength', value)}
                        step={10}
                        marks
                        min={50}
                        max={400}
                        valueLabelDisplay="auto"
                        size="small"
                      />
                    </Box>

                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Node Shape
                      </Typography>
                      <FormControl fullWidth size="small">
                        <Select
                          value={chartSettings.nodeShape}
                          onChange={(e) => handleSettingsChange('nodeShape', e.target.value)}
                        >
                          <MenuItem value="circle">Circle</MenuItem>
                          <MenuItem value="rectangle">Rectangle</MenuItem>
                        </Select>
                      </FormControl>
                    </Box>
                  </Box>

                  {/* Display Options Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Display Options
                    </Typography>
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Switch
                            size="small"
                            checked={chartSettings.showStatistics}
                            onChange={(e) => handleSettingsChange('showStatistics', e.target.checked)}
                          />
                        }
                        label="Show Statistics"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            size="small"
                            checked={chartSettings.showLabels}
                            onChange={(e) => handleSettingsChange('showLabels', e.target.checked)}
                          />
                        }
                        label="Show Variable Labels"
                      />
                    </FormGroup>
                  </Box>

                  {/* Interactive Controls */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Tree Controls
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Use the Variables panel to add/remove variables from the hierarchy. Adjust settings here to customize the tree appearance.
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>
        </Grid>

        {/* Chart Display Panel */}
        <Grid item xs={12} sm={12} md={9} lg={9}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Tree Visualization
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  Active: {activePanel === 0 ? 'Variables' : 'Settings'}
                </Typography>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: activePanel === 0 ? theme.palette.primary.main : theme.palette.warning.main,
                    boxShadow: `0 0 0 2px ${activePanel === 0 ? theme.palette.primary.main + '20' : theme.palette.warning.main + '20'}`
                  }}
                />
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Box
              sx={{
                minHeight: 500,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.01)' : 'rgba(0, 0, 0, 0.01)'
              }}
            >
              {loading ? (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
                  <CircularProgress />
                  <Typography color="text.secondary">Generating tree visualization...</Typography>
                </Box>
              ) : treeData ? (
                <Box
                  sx={{
                    width: '100%',
                    height: '500px',
                    overflow: 'hidden',
                    position: 'relative'
                  }}
                >
                  <svg
                    ref={svgRef}
                    style={{
                      width: '100%',
                      height: '100%',
                      display: 'block'
                    }}
                  />
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{
                      position: 'absolute',
                      bottom: 8,
                      left: 8,
                      backgroundColor: 'rgba(255, 255, 255, 0.8)',
                      padding: '4px 8px',
                      borderRadius: 1,
                      fontSize: '0.75rem'
                    }}
                  >
                    💡 Use mouse wheel to zoom, click and drag to pan
                  </Typography>
                </Box>
              ) : (
                <Box textAlign="center">
                  <TreeIcon sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    No Tree Generated
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Select at least 2 variables and click "Generate Tree" to create your visualization
                  </Typography>
                </Box>
              )}
            </Box>

            {/* Summary Statistics */}
            {treeData && (
              <Box mt={2}>
                <Typography variant="h6" gutterBottom>
                  Tree Summary
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {hierarchy.length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Variables
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {hierarchy.length - 1}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Levels
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {activeDataset?.data.length || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Data Points
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {chartSettings.orientation === 'vertical' ? 'V' : 'H'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Orientation
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default VariableTree;
