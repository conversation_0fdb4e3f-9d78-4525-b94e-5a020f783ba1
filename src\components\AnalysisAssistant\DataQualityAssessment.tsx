import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  Typo<PERSON>,
  <PERSON>ert,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Grid,
  LinearProgress,
  Divider,
  Button,
  Collapse,
  IconButton,
  Tooltip,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  DataUsage as DataUsageIcon,
  BugReport as BugReportIcon,
  Psychology as PsychologyIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIconCollapse,
  Lightbulb as LightbulbIcon,
  AutoFixHigh as AutoFixHighIcon,
  Key as KeyIcon,
  Fingerprint as FingerprintIcon
} from '@mui/icons-material';
import { useTheme, alpha } from '@mui/material/styles';
import { DataQualityAssessment as DataQualityAssessmentType } from '../../utils/dataAnalysisService';

interface DataQualityAssessmentProps {
  assessment: DataQualityAssessmentType;
  datasetName: string;
}

const DataQualityAssessment: React.FC<DataQualityAssessmentProps> = ({
  assessment,
  datasetName
}) => {
  const theme = useTheme();
  const [expandedSections, setExpandedSections] = useState<string[]>(['overview', 'recommendations']);
  const [showAllRecommendations, setShowAllRecommendations] = useState(false);

  const toggleSection = (section: string) => {
    setExpandedSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const getPriorityColor = (priority: 'critical' | 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'critical': return theme.palette.error.main;
      case 'high': return theme.palette.warning.main;
      case 'medium': return theme.palette.info.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.grey[500];
    }
  };

  const getPriorityIcon = (priority: 'critical' | 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'critical': return <ErrorIcon />;
      case 'high': return <WarningIcon />;
      case 'medium': return <InfoIcon />;
      case 'low': return <CheckCircleIcon />;
      default: return <InfoIcon />;
    }
  };

  const getGradeColor = (grade: 'excellent' | 'good' | 'fair' | 'poor') => {
    switch (grade) {
      case 'excellent': return theme.palette.success.main;
      case 'good': return theme.palette.info.main;
      case 'fair': return theme.palette.warning.main;
      case 'poor': return theme.palette.error.main;
      default: return theme.palette.grey[500];
    }
  };

  const displayedRecommendations = showAllRecommendations 
    ? assessment.prioritizedRecommendations 
    : assessment.prioritizedRecommendations.slice(0, 5);

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <AssessmentIcon color="primary" sx={{ mr: 1, fontSize: 28 }} />
        <Typography variant="h6">Data Quality Assessment</Typography>
        <Box sx={{ flexGrow: 1 }} />
        <Chip
          label={`${assessment.overallGrade.toUpperCase()} (${assessment.overallScore}/100)`}
          color={assessment.overallGrade === 'excellent' ? 'success' : 
                 assessment.overallGrade === 'good' ? 'info' :
                 assessment.overallGrade === 'fair' ? 'warning' : 'error'}
          sx={{ fontWeight: 'bold' }}
        />
      </Box>

      {/* Overall Score Card */}
      <Card sx={{ mb: 3, bgcolor: alpha(getGradeColor(assessment.overallGrade), 0.05) }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <DataUsageIcon sx={{ mr: 1, color: getGradeColor(assessment.overallGrade) }} />
            <Typography variant="h6">
              Overall Data Quality: {assessment.overallGrade.charAt(0).toUpperCase() + assessment.overallGrade.slice(1)}
            </Typography>
          </Box>
          
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Quality Score
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {assessment.overallScore}/100
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={assessment.overallScore}
              sx={{
                height: 8,
                borderRadius: 4,
                bgcolor: alpha(theme.palette.grey[300], 0.3),
                '& .MuiLinearProgress-bar': {
                  bgcolor: getGradeColor(assessment.overallGrade),
                  borderRadius: 4
                }
              }}
            />
          </Box>

          <Typography variant="body2" color="text.secondary">
            Dataset: <strong>{datasetName}</strong> • 
            {assessment.prioritizedRecommendations.length} recommendations • 
            {assessment.prioritizedRecommendations.filter(r => r.priority === 'critical' || r.priority === 'high').length} high priority issues
          </Typography>
        </CardContent>
      </Card>

      {/* Priority Recommendations */}
      <Accordion 
        expanded={expandedSections.includes('recommendations')}
        onChange={() => toggleSection('recommendations')}
        sx={{ mb: 2 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <AutoFixHighIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
            <Typography variant="h6">Priority Recommendations</Typography>
            <Box sx={{ flexGrow: 1 }} />
            <Chip
              label={assessment.prioritizedRecommendations.length}
              size="small"
              color="primary"
              sx={{ mr: 1 }}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          {displayedRecommendations.length === 0 ? (
            <Alert severity="success" icon={<CheckCircleIcon />}>
              <Typography variant="subtitle2">Excellent Data Quality!</Typography>
              <Typography variant="body2">
                No significant data quality issues detected. Your dataset appears to be well-prepared for analysis.
              </Typography>
            </Alert>
          ) : (
            <>
              <List sx={{ width: '100%' }}>
                {displayedRecommendations.map((rec, index) => (
                  <ListItem
                    key={index}
                    sx={{
                      mb: 2,
                      bgcolor: alpha(getPriorityColor(rec.priority), 0.05),
                      borderRadius: 2,
                      border: `1px solid ${alpha(getPriorityColor(rec.priority), 0.2)}`,
                      flexDirection: 'column',
                      alignItems: 'flex-start'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', mb: 1 }}>
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        {React.cloneElement(getPriorityIcon(rec.priority), {
                          sx: { color: getPriorityColor(rec.priority) }
                        })}
                      </ListItemIcon>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="subtitle1" fontWeight="bold">
                          {rec.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {rec.description}
                        </Typography>
                      </Box>
                      <Chip
                        label={rec.priority.toUpperCase()}
                        size="small"
                        sx={{
                          bgcolor: getPriorityColor(rec.priority),
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    </Box>
                    
                    <Box sx={{ width: '100%', pl: 5 }}>
                      <Typography variant="body2" sx={{ mb: 1, fontStyle: 'italic' }}>
                        <strong>Impact:</strong> {rec.impact}
                      </Typography>
                      
                      <Typography variant="body2" sx={{ mb: 1, fontWeight: 'bold' }}>
                        Recommended Actions:
                      </Typography>
                      <List dense sx={{ pl: 2 }}>
                        {rec.actionSteps.map((step, stepIndex) => (
                          <ListItem key={stepIndex} sx={{ py: 0.5, pl: 0 }}>
                            <ListItemText
                              primary={`${stepIndex + 1}. ${step}`}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  </ListItem>
                ))}
              </List>
              
              {assessment.prioritizedRecommendations.length > 5 && (
                <Box sx={{ textAlign: 'center', mt: 2 }}>
                  <Button
                    variant="outlined"
                    onClick={() => setShowAllRecommendations(!showAllRecommendations)}
                    endIcon={showAllRecommendations ? <ExpandLessIcon /> : <ExpandMoreIconCollapse />}
                  >
                    {showAllRecommendations 
                      ? 'Show Less' 
                      : `Show ${assessment.prioritizedRecommendations.length - 5} More Recommendations`
                    }
                  </Button>
                </Box>
              )}
            </>
          )}
        </AccordionDetails>
      </Accordion>

      {/* Identifier Variables Section */}
      {assessment.identifierVariables.length > 0 && (
        <Accordion
          expanded={expandedSections.includes('identifiers')}
          onChange={() => toggleSection('identifiers')}
          sx={{ mb: 2 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <KeyIcon sx={{ mr: 1, color: theme.palette.secondary.main }} />
              <Typography variant="h6">Identifier Variables</Typography>
              <Box sx={{ flexGrow: 1 }} />
              <Chip
                label={`${assessment.identifierVariables.length} identifiers`}
                size="small"
                color="secondary"
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                Identifier variables are automatically excluded from statistical validation (outlier detection,
                normality tests, etc.) as they serve identification purposes rather than analytical ones.
              </Typography>
            </Alert>

            {assessment.identifierVariables.map((identifier, index) => (
              <Box key={index} sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <FingerprintIcon sx={{ mr: 1, color: theme.palette.secondary.main }} />
                  <Typography variant="subtitle1" fontWeight="bold">
                    {identifier.variable}
                  </Typography>
                </Box>

                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={12} md={6}>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Quality Metrics
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Completeness:</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {identifier.analysis.completenessPercentage.toFixed(1)}%
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Uniqueness:</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {identifier.analysis.uniquenessPercentage.toFixed(1)}%
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Duplicates:</Typography>
                        <Typography variant="body2" fontWeight="bold" color={identifier.analysis.duplicateCount > 0 ? 'error' : 'success'}>
                          {identifier.analysis.duplicateCount}
                        </Typography>
                      </Box>
                    </Paper>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Pattern Analysis
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ mr: 1 }}>Sequential Pattern:</Typography>
                        <Chip
                          label={identifier.analysis.hasSequentialPattern ? 'Yes' : 'No'}
                          size="small"
                          color={identifier.analysis.hasSequentialPattern ? 'success' : 'default'}
                        />
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ mr: 1 }}>Alphanumeric Pattern:</Typography>
                        <Chip
                          label={identifier.analysis.hasAlphanumericPattern ? 'Yes' : 'No'}
                          size="small"
                          color={identifier.analysis.hasAlphanumericPattern ? 'success' : 'default'}
                        />
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>

                {/* Identifier-specific recommendations */}
                <Typography variant="subtitle2" gutterBottom>
                  Recommendations
                </Typography>
                <List dense>
                  {identifier.analysis.recommendations.map((rec, recIndex) => (
                    <ListItem key={recIndex} sx={{ py: 0.5, pl: 0 }}>
                      <ListItemIcon sx={{ minWidth: 30 }}>
                        <LightbulbIcon sx={{ fontSize: 16, color: theme.palette.secondary.main }} />
                      </ListItemIcon>
                      <ListItemText
                        primary={rec}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                  ))}
                </List>

                {index < assessment.identifierVariables.length - 1 && (
                  <Divider sx={{ mt: 2 }} />
                )}
              </Box>
            ))}
          </AccordionDetails>
        </Accordion>
      )}

      {/* Detailed Analysis Sections */}
      {/* Missing Data Analysis */}
      <Accordion 
        expanded={expandedSections.includes('missing')}
        onChange={() => toggleSection('missing')}
        sx={{ mb: 2 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <BugReportIcon sx={{ mr: 1 }} />
            <Typography variant="h6">Missing Data Analysis</Typography>
            <Box sx={{ flexGrow: 1 }} />
            <Chip
              label={`${assessment.missingDataAnalysis.missingPercentage.toFixed(1)}% missing`}
              size="small"
              color={assessment.missingDataAnalysis.missingPercentage > 10 ? 'error' : 
                     assessment.missingDataAnalysis.missingPercentage > 5 ? 'warning' : 'success'}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>Overall Pattern</Typography>
              <Alert 
                severity={assessment.missingDataAnalysis.overallPattern.type === 'MCAR' ? 'info' : 'warning'}
                sx={{ mb: 2 }}
              >
                <Typography variant="body2">
                  <strong>{assessment.missingDataAnalysis.overallPattern.type}</strong> - {assessment.missingDataAnalysis.overallPattern.description}
                </Typography>
              </Alert>
              <Typography variant="body2" color="text.secondary">
                {assessment.missingDataAnalysis.overallPattern.recommendation}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>Missing Data Summary</Typography>
              <Box sx={{ bgcolor: alpha(theme.palette.info.main, 0.05), p: 2, borderRadius: 1 }}>
                <Typography variant="body2">
                  <strong>Total Missing:</strong> {assessment.missingDataAnalysis.totalMissing} values
                </Typography>
                <Typography variant="body2">
                  <strong>Percentage:</strong> {assessment.missingDataAnalysis.missingPercentage.toFixed(2)}%
                </Typography>
                <Typography variant="body2">
                  <strong>Variables Affected:</strong> {Object.keys(assessment.missingDataAnalysis.patternsByVariable).length}
                </Typography>
                {assessment.missingDataAnalysis.excludedIdentifiers.length > 0 && (
                  <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                    <strong>Note:</strong> Excluded {assessment.missingDataAnalysis.excludedIdentifiers.length} identifier variable(s) from analysis
                  </Typography>
                )}
              </Box>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Sample Size Assessment */}
      <Accordion
        expanded={expandedSections.includes('sample')}
        onChange={() => toggleSection('sample')}
        sx={{ mb: 2 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <TrendingUpIcon sx={{ mr: 1 }} />
            <Typography variant="h6">Sample Size Assessment</Typography>
            <Box sx={{ flexGrow: 1 }} />
            <Chip
              label={assessment.sampleSizeAssessment.isAdequate ? 'Adequate' : 'Insufficient'}
              size="small"
              color={assessment.sampleSizeAssessment.isAdequate ? 'success' : 'warning'}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Box sx={{ bgcolor: alpha(theme.palette.info.main, 0.05), p: 2, borderRadius: 1, mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>Sample Size Details</Typography>
                <Typography variant="body2">
                  <strong>Current Size:</strong> {assessment.sampleSizeAssessment.currentSize}
                </Typography>
                <Typography variant="body2">
                  <strong>Recommended Minimum:</strong> {assessment.sampleSizeAssessment.recommendedMinimum}
                </Typography>
                <Typography variant="body2">
                  <strong>Status:</strong> {assessment.sampleSizeAssessment.isAdequate ? 'Adequate' : 'Needs more data'}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>Recommendations</Typography>
              <List dense>
                {assessment.sampleSizeAssessment.recommendations.map((rec, index) => (
                  <ListItem key={index} sx={{ py: 0.5, pl: 0 }}>
                    <ListItemIcon sx={{ minWidth: 30 }}>
                      <LightbulbIcon sx={{ fontSize: 16, color: theme.palette.warning.main }} />
                    </ListItemIcon>
                    <ListItemText
                      primary={rec}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Data Type Validation */}
      {assessment.dataTypeValidation.length > 0 && (
        <Accordion
          expanded={expandedSections.includes('types')}
          onChange={() => toggleSection('types')}
          sx={{ mb: 2 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <PsychologyIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Data Type Validation</Typography>
              <Box sx={{ flexGrow: 1 }} />
              <Chip
                label={`${assessment.dataTypeValidation.length} suggestions`}
                size="small"
                color="info"
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell><strong>Variable</strong></TableCell>
                    <TableCell><strong>Current Type</strong></TableCell>
                    <TableCell><strong>Suggested Type</strong></TableCell>
                    <TableCell><strong>Confidence</strong></TableCell>
                    <TableCell><strong>Reason</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {assessment.dataTypeValidation.map((validation, index) => (
                    <TableRow key={index}>
                      <TableCell>{validation.variable}</TableCell>
                      <TableCell>
                        <Chip label={validation.currentType} size="small" variant="outlined" />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={validation.suggestedType}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <LinearProgress
                            variant="determinate"
                            value={validation.confidence * 100}
                            sx={{ width: 60, mr: 1, height: 6 }}
                          />
                          <Typography variant="caption">
                            {(validation.confidence * 100).toFixed(0)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 200 }}>
                          {validation.reason}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Consistency Issues */}
      {assessment.consistencyChecks.length > 0 && (
        <Accordion
          expanded={expandedSections.includes('consistency')}
          onChange={() => toggleSection('consistency')}
          sx={{ mb: 2 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <ErrorIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Consistency Issues</Typography>
              <Box sx={{ flexGrow: 1 }} />
              <Chip
                label={`${assessment.consistencyChecks.reduce((sum, check) => sum + check.issues.length, 0)} issues`}
                size="small"
                color="error"
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {assessment.consistencyChecks.map((check, index) => (
              <Box key={index} sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {check.variable}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {check.recommendation}
                </Typography>

                <TableContainer component={Paper} variant="outlined" sx={{ mb: 2 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell><strong>Row</strong></TableCell>
                        <TableCell><strong>Value</strong></TableCell>
                        <TableCell><strong>Issue</strong></TableCell>
                        <TableCell><strong>Severity</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {check.issues.slice(0, 10).map((issue, issueIndex) => (
                        <TableRow key={issueIndex}>
                          <TableCell>{issue.rowIndex + 1}</TableCell>
                          <TableCell>
                            <code>{String(issue.value)}</code>
                          </TableCell>
                          <TableCell>{issue.description}</TableCell>
                          <TableCell>
                            <Chip
                              label={issue.severity.toUpperCase()}
                              size="small"
                              color={issue.severity === 'high' ? 'error' :
                                     issue.severity === 'medium' ? 'warning' : 'info'}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {check.issues.length > 10 && (
                  <Typography variant="caption" color="text.secondary">
                    Showing first 10 of {check.issues.length} issues
                  </Typography>
                )}
              </Box>
            ))}
          </AccordionDetails>
        </Accordion>
      )}

      {/* Distribution Analysis Details */}
      <Accordion
        expanded={expandedSections.includes('distribution')}
        onChange={() => toggleSection('distribution')}
        sx={{ mb: 2 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <TrendingUpIcon sx={{ mr: 1 }} />
            <Typography variant="h6">Distribution Analysis</Typography>
            <Box sx={{ flexGrow: 1 }} />
            <Chip
              label={`${Object.keys(assessment.distributionAnalysis).length} analytical variables`}
              size="small"
              color="info"
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          {assessment.identifierVariables.length > 0 && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Note:</strong> {assessment.identifierVariables.length} identifier variable(s)
                ({assessment.identifierVariables.map(id => id.variable).join(', ')}) are excluded from
                distribution analysis as statistical measures don't apply to identifier variables.
              </Typography>
            </Alert>
          )}

          {Object.keys(assessment.distributionAnalysis).length === 0 ? (
            <Alert severity="info">
              <Typography variant="body2">
                No analytical variables found for distribution analysis. All variables appear to be identifiers.
              </Typography>
            </Alert>
          ) : (
            Object.entries(assessment.distributionAnalysis).map(([variable, dist]) => (
            <Box key={variable} sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                {variable}
              </Typography>

              {/* Normality Information for Numeric Variables */}
              {dist.normality.interpretation !== 'Categorical variable - normality not applicable' &&
               dist.normality.interpretation !== 'Distribution analysis not applicable for this data type' && (
                <Box sx={{ mb: 2 }}>
                  <Alert
                    severity={dist.normality.isNormal ? 'success' : 'warning'}
                    sx={{ mb: 1 }}
                  >
                    <Typography variant="body2">
                      <strong>Normality:</strong> {dist.normality.interpretation}
                    </Typography>
                    {!isNaN(dist.normality.pValue) && (
                      <Typography variant="body2">
                        p-value: {dist.normality.pValue.toFixed(4)},
                        Skewness: {dist.normality.skewness.toFixed(3)},
                        Kurtosis: {dist.normality.kurtosis.toFixed(3)}
                      </Typography>
                    )}
                  </Alert>
                </Box>
              )}

              {/* Class Balance Information for Categorical Variables */}
              {dist.balance && (
                <Box sx={{ mb: 2 }}>
                  <Alert
                    severity={dist.balance.isBalanced ? 'success' : 'warning'}
                    sx={{ mb: 2 }}
                  >
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Class Balance:</strong> {dist.balance.recommendation}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Imbalance Ratio:</strong> {dist.balance.imbalanceRatio.toFixed(1)}:1
                    </Typography>
                  </Alert>

                  {/* Detailed Category Breakdown */}
                  <Typography variant="subtitle2" gutterBottom>
                    Category Distribution
                  </Typography>
                  <TableContainer component={Paper} variant="outlined" sx={{ mb: 2 }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell><strong>Category</strong></TableCell>
                          <TableCell align="right"><strong>Count</strong></TableCell>
                          <TableCell align="right"><strong>Percentage</strong></TableCell>
                          <TableCell><strong>Status</strong></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {dist.balance.categoryDetails.allCategories.map((cat, index) => (
                          <TableRow key={index}>
                            <TableCell>{cat.category}</TableCell>
                            <TableCell align="right">{cat.count}</TableCell>
                            <TableCell align="right">{cat.percentage.toFixed(1)}%</TableCell>
                            <TableCell>
                              {cat.category === dist.balance!.categoryDetails.mostFrequent.category && (
                                <Chip label="Most Frequent" size="small" color="primary" />
                              )}
                              {cat.category === dist.balance!.categoryDetails.leastFrequent.category && (
                                <Chip label="Least Frequent" size="small" color="warning" />
                              )}
                              {cat.percentage < 5 && cat.category !== dist.balance!.categoryDetails.leastFrequent.category && (
                                <Chip label="Rare (<5%)" size="small" color="error" />
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}

              {/* Transformation Suggestions */}
              {dist.transformationSuggestions.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Recommended Transformations
                  </Typography>
                  <List dense>
                    {dist.transformationSuggestions.map((suggestion, index) => (
                      <ListItem key={index} sx={{ py: 0.5, pl: 0 }}>
                        <ListItemIcon sx={{ minWidth: 30 }}>
                          <LightbulbIcon sx={{ fontSize: 16, color: theme.palette.info.main }} />
                        </ListItemIcon>
                        <ListItemText
                          primary={suggestion}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}

              {Object.keys(assessment.distributionAnalysis).indexOf(variable) < Object.keys(assessment.distributionAnalysis).length - 1 && (
                <Divider sx={{ mt: 2 }} />
              )}
            </Box>
          ))
          )}
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default DataQualityAssessment;
