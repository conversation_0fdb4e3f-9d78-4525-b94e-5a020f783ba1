import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Button,
  TextField,
  Alert,
  List,
  ListItem,
  ListItemText,
  Chip,
  Divider,
  Paper,
  Grid
} from '@mui/material';
import {
  Psychology as PsychologyIcon,
  PlayArrow as PlayIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { trainingDB } from '../../utils/trainingDatabase';
import { initializeCompleteTrainingSystem } from '../../utils/trainingDataInitializer';
import { testTrainingSystem } from '../../utils/testTrainingSystem';

interface TestResult {
  query: string;
  expectedAnalyses: string[];
  actualSuggestions: any[];
  passed: boolean;
  score: number;
}

const TrainingDemo: React.FC = () => {
  const theme = useTheme();
  const [testQuery, setTestQuery] = useState('');
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Available analyses for display
  const analysisNames: { [key: string]: string } = {
    'CORR1': 'Correlation Matrix',
    'VIZ4': 'Scatter Plot',
    'REG1': 'Linear Regression',
    'CAT1': 'Chi-Square Test',
    'DESC2': 'Frequency Tables',
    'DESC3': 'Cross-Tabulation',
    'TTEST2': 'Independent T-Test',
    'NONPAR1': 'Mann-Whitney U Test',
    'DESC1': 'Descriptive Analysis',
    'TTEST3': 'Paired T-Test',
    'NONPAR2': 'Wilcoxon Signed-Rank Test',
    'ANOVA1': 'One-Way ANOVA',
    'NONPAR3': 'Kruskal-Wallis Test',
    'NORM1': 'Normality Tests',
    'VIZ2': 'Histograms and Q-Q plots'
  };

  const problematicQueries = [
    "How do I test if age is correlated with test scores?",
    "What test for categorical data?",
    "Test correlation between variables",
    "Compare means between two groups",
    "Is my data normally distributed?"
  ];

  useEffect(() => {
    // Initialize the training system
    initializeCompleteTrainingSystem();
    setIsInitialized(true);
  }, []);

  const handleTestQuery = () => {
    if (!testQuery.trim()) return;

    const curatedSuggestions = trainingDB.findMatchingSuggestions(testQuery);
    setSuggestions(curatedSuggestions);
  };

  const handleRunAllTests = () => {
    const results = testTrainingSystem();
    setTestResults(results);
  };

  const handleTestProblematicQuery = (query: string) => {
    setTestQuery(query);
    const curatedSuggestions = trainingDB.findMatchingSuggestions(query);
    setSuggestions(curatedSuggestions);
  };

  const getStats = () => {
    return trainingDB.getStats();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <PsychologyIcon sx={{ mr: 2, color: theme.palette.primary.main }} />
        <Typography variant="h4">Training System Demo</Typography>
      </Box>

      {!isInitialized ? (
        <Alert severity="info">Initializing training system...</Alert>
      ) : (
        <>
          {/* Stats Overview */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>Training Database Stats</Typography>
              <Grid container spacing={2}>
                <Grid item xs={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">{getStats().totalQuestions}</Typography>
                    <Typography variant="body2">Questions</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">{getStats().totalAnswers}</Typography>
                    <Typography variant="body2">Answers</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">{getStats().validatedAnswers}</Typography>
                    <Typography variant="body2">Validated</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="info.main">{trainingDB.getCuratedSuggestions().length}</Typography>
                    <Typography variant="body2">Curated</Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Test Individual Query */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>Test Individual Query</Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <TextField
                  label="Enter your statistical question"
                  value={testQuery}
                  onChange={(e) => setTestQuery(e.target.value)}
                  fullWidth
                  placeholder="e.g., How do I test if age is correlated with test scores?"
                />
                <Button
                  variant="contained"
                  onClick={handleTestQuery}
                  disabled={!testQuery.trim()}
                  startIcon={<PlayIcon />}
                >
                  Test
                </Button>
              </Box>

              {/* Quick Test Buttons */}
              <Typography variant="subtitle2" gutterBottom>Quick Tests (Problematic Queries):</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {problematicQueries.map((query, index) => (
                  <Chip
                    key={index}
                    label={query}
                    onClick={() => handleTestProblematicQuery(query)}
                    clickable
                    variant="outlined"
                  />
                ))}
              </Box>

              {/* Results */}
              {suggestions.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Found {suggestions.length} curated suggestions:
                  </Typography>
                  <List>
                    {suggestions.map((suggestion, index) => (
                      <ListItem key={index} divider>
                        <ListItemText
                          primary={
                            <Box>
                              <Typography variant="subtitle1">
                                Pattern: "{suggestion.questionPattern}"
                              </Typography>
                              <Box sx={{ mt: 1 }}>
                                <Chip label={suggestion.category} size="small" sx={{ mr: 1 }} />
                                {suggestion.validated && <Chip label="Validated" size="small" color="success" />}
                              </Box>
                            </Box>
                          }
                          secondary={
                            <Box sx={{ mt: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Keywords: {suggestion.keywords.join(', ')}
                              </Typography>
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                <strong>Suggested Analyses:</strong>
                              </Typography>
                              {suggestion.suggestions.map((sug: any, sugIndex: number) => (
                                <Box key={sugIndex} sx={{ ml: 2, mt: 0.5 }}>
                                  <Typography variant="body2">
                                    • <strong>{analysisNames[sug.analysisId] || sug.analysisId}</strong> 
                                    (Priority: {sug.priority}, Confidence: {(sug.confidence * 100).toFixed(0)}%)
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {sug.reason}
                                  </Typography>
                                </Box>
                              ))}
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </CardContent>
          </Card>

          {/* Run All Tests */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>Comprehensive Test Suite</Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Test all problematic queries identified in the original issue to validate the training system's accuracy.
              </Typography>
              <Button
                variant="contained"
                onClick={handleRunAllTests}
                startIcon={<PlayIcon />}
                sx={{ mb: 2 }}
              >
                Run All Tests
              </Button>

              {testResults.length > 0 && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    Test Results: {testResults.filter(r => r.passed).length}/{testResults.length} passed
                  </Typography>
                  <List>
                    {testResults.map((result, index) => (
                      <ListItem key={index} divider>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {result.passed ? (
                                <CheckIcon color="success" sx={{ mr: 1 }} />
                              ) : (
                                <ErrorIcon color="error" sx={{ mr: 1 }} />
                              )}
                              <Typography variant="subtitle2">
                                {result.query}
                              </Typography>
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2">
                                Expected: {result.expectedAnalyses.join(', ')}
                              </Typography>
                              <Typography variant="body2">
                                Score: {(result.score * 100).toFixed(1)}% 
                                ({result.actualSuggestions.length} suggestions found)
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </CardContent>
          </Card>

          {/* Instructions */}
          <Alert severity="info">
            <Typography variant="subtitle2" gutterBottom>How to Use:</Typography>
            <Typography variant="body2">
              1. <strong>Test Individual Queries:</strong> Enter any statistical question to see what the trained system suggests.
              <br />
              2. <strong>Quick Tests:</strong> Click on the problematic query chips to test the specific issues mentioned.
              <br />
              3. <strong>Comprehensive Test:</strong> Run all tests to see overall system accuracy.
              <br />
              4. The system now prioritizes curated suggestions from the training database over hardcoded scenarios.
            </Typography>
          </Alert>
        </>
      )}
    </Box>
  );
};

export default TrainingDemo;
