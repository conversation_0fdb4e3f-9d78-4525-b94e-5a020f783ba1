import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Checkbox,
  FormControlLabel,
  Grid,
  Paper,
  Divider,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  School as SchoolIcon,
  Psychology as PsychologyIcon,
  CheckCircle as CheckCircleIcon,
  ExpandMore as ExpandMoreIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import {
  trainingDB,
  TrainingQuestion,
  TrainingAnswer,
  TrainingSession,
  CuratedSuggestion
} from '../../utils/trainingDatabase';

interface TrainingInterfaceProps {
  onClose?: () => void;
}

type TabValue = 'questions' | 'sessions' | 'curated' | 'stats';

const TrainingInterface: React.FC<TrainingInterfaceProps> = ({ onClose }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState<TabValue>('questions');
  const [questions, setQuestions] = useState<TrainingQuestion[]>([]);
  const [sessions, setSessions] = useState<TrainingSession[]>([]);
  const [curatedSuggestions, setCuratedSuggestions] = useState<CuratedSuggestion[]>([]);
  const [stats, setStats] = useState(trainingDB.getStats());
  
  // Question form state
  const [questionForm, setQuestionForm] = useState({
    question: '',
    keywords: '',
    patterns: '',
    category: 'other' as TrainingQuestion['category'],
    difficulty: 'basic' as TrainingQuestion['difficulty'],
    context: ''
  });
  
  // Answer form state
  const [answerForm, setAnswerForm] = useState({
    questionId: '',
    analysisId: '',
    priority: 'medium' as TrainingAnswer['priority'],
    reason: '',
    prerequisites: '',
    alternatives: ''
  });
  
  // Session form state
  const [sessionForm, setSessionForm] = useState({
    name: '',
    description: '',
    selectedQuestions: [] as string[]
  });

  const [editingQuestion, setEditingQuestion] = useState<string | null>(null);
  const [showQuestionDialog, setShowQuestionDialog] = useState(false);
  const [showSessionDialog, setShowSessionDialog] = useState(false);
  const [showAnswerDialog, setShowAnswerDialog] = useState(false);
  const [selectedQuestionForAnswer, setSelectedQuestionForAnswer] = useState<string | null>(null);
  const [showQuestionEditor, setShowQuestionEditor] = useState(false);
  const [selectedQuestionForEdit, setSelectedQuestionForEdit] = useState<TrainingQuestion | null>(null);
  const [notification, setNotification] = useState<{ message: string; type: 'success' | 'error' } | null>(null);

  // Available analyses (comprehensive list from AnalysisAssistant)
  const availableAnalyses = [
    // Descriptive Statistics
    { id: 'DESC1', name: 'Descriptive Analysis' },
    { id: 'DESC2', name: 'Frequency Tables' },
    { id: 'DESC3', name: 'Cross-Tabulation' },
    { id: 'DESC4', name: 'Normality Test' },

    // Correlation and Regression
    { id: 'CORR1', name: 'Correlation Matrix' },
    { id: 'REG1', name: 'Linear Regression' },
    { id: 'REG2', name: 'Logistic Regression' },

    // T-Tests
    { id: 'TTEST1', name: 'One-Sample T-Test' },
    { id: 'TTEST2', name: 'Independent T-Test' },
    { id: 'TTEST3', name: 'Paired T-Test' },

    // ANOVA
    { id: 'ANOVA1', name: 'One-Way ANOVA' },
    { id: 'ANOVA2', name: 'Two-Way ANOVA' },
    { id: 'ANOVA3', name: 'Repeated Measures ANOVA' },

    // Categorical Tests
    { id: 'CAT1', name: 'Chi-Square Test' },

    // Non-parametric Tests
    { id: 'NONPAR1', name: 'Mann-Whitney U Test' },
    { id: 'NONPAR2', name: 'Wilcoxon Signed-Rank Test' },
    { id: 'NONPAR3', name: 'Kruskal-Wallis Test' },
    { id: 'NONPAR4', name: 'Friedman Test' },

    // Advanced Methods
    { id: 'ADV1', name: 'Exploratory Factor Analysis' },
    { id: 'ADV2', name: 'Confirmatory Factor Analysis' },
    { id: 'ADV3', name: 'Reliability Analysis' },
    { id: 'ADV4', name: 'Survival Analysis' },
    { id: 'ADV5', name: 'Cluster Analysis' },
    { id: 'ADV6', name: 'Meta Analysis' },

    // Epidemiology
    { id: 'EPI1', name: 'Case-Control Calculator' },
    { id: 'EPI2', name: 'Cohort Calculator' },

    // Sample Size
    { id: 'SS1', name: 'One Sample Calculator' },
    { id: 'SS2', name: 'Two Sample Calculator' },

    // Publication Ready
    { id: 'PUB1', name: 'Table 1 Generator' },
    { id: 'PUB2', name: 'Table 1a Generator' },
    { id: 'PUB3', name: 'SMD Table' },
    { id: 'PUB4', name: 'Regression Table' },

    // Visualizations
    { id: 'VIZ1', name: 'Bar Chart' },
    { id: 'VIZ2', name: 'Histogram' },
    { id: 'VIZ3', name: 'Box Plot' },
    { id: 'VIZ4', name: 'Scatter Plot' },
    { id: 'VIZ5', name: 'Correlation Heatmap' }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setQuestions(trainingDB.getAllQuestions());
    setSessions(trainingDB.getAllSessions());
    setCuratedSuggestions(trainingDB.getCuratedSuggestions());
    setStats(trainingDB.getStats());
  };

  const handleAddQuestion = () => {
    if (!questionForm.question.trim()) {
      setNotification({ message: 'Question text is required', type: 'error' });
      return;
    }

    const keywords = questionForm.keywords.split(',').map(k => k.trim()).filter(k => k);
    const patterns = questionForm.patterns.split('\n').map(p => p.trim()).filter(p => p);

    const questionData = {
      question: questionForm.question,
      keywords,
      patterns,
      category: questionForm.category,
      difficulty: questionForm.difficulty,
      context: questionForm.context || undefined
    };

    if (editingQuestion) {
      trainingDB.updateQuestion(editingQuestion, questionData);
      setEditingQuestion(null);
      setNotification({ message: 'Question updated successfully', type: 'success' });
    } else {
      trainingDB.addQuestion(questionData);
      setNotification({ message: 'Question added successfully', type: 'success' });
    }

    setQuestionForm({
      question: '',
      keywords: '',
      patterns: '',
      category: 'other',
      difficulty: 'basic',
      context: ''
    });
    setShowQuestionDialog(false);
    loadData();
  };

  const handleEditQuestion = (question: TrainingQuestion) => {
    setQuestionForm({
      question: question.question,
      keywords: question.keywords.join(', '),
      patterns: question.patterns?.join('\n') || '',
      category: question.category,
      difficulty: question.difficulty,
      context: question.context || ''
    });
    setEditingQuestion(question.id);
    setShowQuestionDialog(true);
  };

  const handleAddAnswer = () => {
    if (!answerForm.questionId || !answerForm.analysisId || !answerForm.reason.trim()) {
      setNotification({ message: 'Question, analysis, and reason are required', type: 'error' });
      return;
    }

    const prerequisites = answerForm.prerequisites.split(',').map(p => p.trim()).filter(p => p);
    const alternatives = answerForm.alternatives.split(',').map(a => a.trim()).filter(a => a);

    trainingDB.addAnswer({
      questionId: answerForm.questionId,
      analysisId: answerForm.analysisId,
      priority: answerForm.priority,
      reason: answerForm.reason,
      prerequisites: prerequisites.length > 0 ? prerequisites : undefined,
      alternatives: alternatives.length > 0 ? alternatives : undefined,
      validated: true
    });

    setAnswerForm({
      questionId: '',
      analysisId: '',
      priority: 'medium',
      reason: '',
      prerequisites: '',
      alternatives: ''
    });
    setShowAnswerDialog(false);
    setSelectedQuestionForAnswer(null);
    setNotification({ message: 'Answer added successfully', type: 'success' });
    loadData();
  };

  const handleCreateSession = () => {
    if (!sessionForm.name.trim()) {
      setNotification({ message: 'Session name is required', type: 'error' });
      return;
    }

    const sessionId = trainingDB.createSession(sessionForm.name, sessionForm.description);
    
    sessionForm.selectedQuestions.forEach(questionId => {
      trainingDB.addQuestionToSession(sessionId, questionId);
    });

    setSessionForm({
      name: '',
      description: '',
      selectedQuestions: []
    });
    setShowSessionDialog(false);
    setNotification({ message: 'Training session created successfully', type: 'success' });
    loadData();
  };

  const handleGenerateCuratedSuggestions = () => {
    // Generate curated suggestions from validated training data
    const allQuestions = trainingDB.getAllQuestions();
    
    allQuestions.forEach(question => {
      const answers = trainingDB.getAnswersForQuestion(question.id);
      const validatedAnswers = answers.filter(a => a.validated);
      
      if (validatedAnswers.length > 0) {
        const suggestions = validatedAnswers.map(answer => ({
          analysisId: answer.analysisId,
          priority: answer.priority === 'high' ? 8 : answer.priority === 'medium' ? 5 : 3,
          reason: answer.reason,
          confidence: 0.9
        }));

        const regexPatterns = question.patterns?.map(p => {
          try {
            return new RegExp(p, 'i');
          } catch {
            return new RegExp(p.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
          }
        }) || [];

        const curatedSuggestion: Omit<CuratedSuggestion, 'usage_count' | 'success_rate'> = {
          questionPattern: question.question,
          keywords: question.keywords,
          regexPatterns,
          suggestions,
          category: question.category,
          validated: true
        };

        trainingDB.addCuratedSuggestion(curatedSuggestion);
      }
    });

    loadData();
    setNotification({ message: 'Curated suggestions generated successfully', type: 'success' });
  };

  const handleExportData = () => {
    const data = trainingDB.exportTrainingData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analysis-assistant-training-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    setNotification({ message: 'Training data exported successfully', type: 'success' });
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (trainingDB.importTrainingData(content)) {
        loadData();
        setNotification({ message: 'Training data imported successfully', type: 'success' });
      } else {
        setNotification({ message: 'Failed to import training data', type: 'error' });
      }
    };
    reader.readAsText(file);
  };

  const handleClearAllData = () => {
    if (window.confirm('Are you sure you want to clear all training data? This action cannot be undone.')) {
      trainingDB.clearAllData();
      loadData();
      setNotification({ message: 'All training data cleared', type: 'success' });
    }
  };

  // Comprehensive Question Editor Component
  const QuestionEditor = () => {
    const [questionData, setQuestionData] = useState({
      question: selectedQuestionForEdit?.question || '',
      keywords: selectedQuestionForEdit?.keywords.join(', ') || '',
      patterns: selectedQuestionForEdit?.patterns?.join('\n') || '',
      category: selectedQuestionForEdit?.category || 'other' as TrainingQuestion['category'],
      difficulty: selectedQuestionForEdit?.difficulty || 'basic' as TrainingQuestion['difficulty'],
      context: selectedQuestionForEdit?.context || ''
    });

    const [answers, setAnswers] = useState<TrainingAnswer[]>([]);
    const [editingAnswerId, setEditingAnswerId] = useState<string | null>(null);
    const [newAnswer, setNewAnswer] = useState({
      analysisId: '',
      priority: 'medium' as TrainingAnswer['priority'],
      reason: '',
      prerequisites: '',
      alternatives: ''
    });

    useEffect(() => {
      if (selectedQuestionForEdit) {
        const questionAnswers = trainingDB.getAnswersForQuestion(selectedQuestionForEdit.id);
        setAnswers(questionAnswers);
      }
    }, [selectedQuestionForEdit]);

    const handleSaveQuestion = () => {
      if (!questionData.question.trim()) {
        setNotification({ message: 'Question text is required', type: 'error' });
        return;
      }

      const keywords = questionData.keywords.split(',').map(k => k.trim()).filter(k => k);
      const patterns = questionData.patterns.split('\n').map(p => p.trim()).filter(p => p);

      if (selectedQuestionForEdit) {
        // Update existing question
        trainingDB.updateQuestion(selectedQuestionForEdit.id, {
          question: questionData.question,
          keywords,
          patterns,
          category: questionData.category,
          difficulty: questionData.difficulty,
          context: questionData.context || undefined
        });
        setNotification({ message: 'Question updated successfully', type: 'success' });
      }

      loadData();
    };

    const handleDeleteAnswer = (answerId: string) => {
      if (window.confirm('Are you sure you want to delete this answer?')) {
        trainingDB.deleteAnswer(answerId);
        const updatedAnswers = answers.filter(a => a.id !== answerId);
        setAnswers(updatedAnswers);
        setNotification({ message: 'Answer deleted successfully', type: 'success' });
        loadData();
      }
    };

    const handleEditAnswer = (answer: TrainingAnswer) => {
      setEditingAnswerId(answer.id);
      setNewAnswer({
        analysisId: answer.analysisId,
        priority: answer.priority,
        reason: answer.reason,
        prerequisites: answer.prerequisites?.join(', ') || '',
        alternatives: answer.alternatives?.join(', ') || ''
      });
    };

    const handleSaveAnswer = () => {
      if (!newAnswer.analysisId || !newAnswer.reason.trim()) {
        setNotification({ message: 'Analysis and reason are required', type: 'error' });
        return;
      }

      const prerequisites = newAnswer.prerequisites.split(',').map(p => p.trim()).filter(p => p);
      const alternatives = newAnswer.alternatives.split(',').map(a => a.trim()).filter(a => a);

      if (editingAnswerId) {
        // Update existing answer
        trainingDB.updateAnswer(editingAnswerId, {
          analysisId: newAnswer.analysisId,
          priority: newAnswer.priority,
          reason: newAnswer.reason,
          prerequisites: prerequisites.length > 0 ? prerequisites : undefined,
          alternatives: alternatives.length > 0 ? alternatives : undefined
        });
        setNotification({ message: 'Answer updated successfully', type: 'success' });
      } else {
        // Add new answer
        trainingDB.addAnswer({
          questionId: selectedQuestionForEdit!.id,
          analysisId: newAnswer.analysisId,
          priority: newAnswer.priority,
          reason: newAnswer.reason,
          prerequisites: prerequisites.length > 0 ? prerequisites : undefined,
          alternatives: alternatives.length > 0 ? alternatives : undefined,
          validated: true
        });
        setNotification({ message: 'Answer added successfully', type: 'success' });
      }

      // Reset form and refresh answers
      setEditingAnswerId(null);
      setNewAnswer({
        analysisId: '',
        priority: 'medium',
        reason: '',
        prerequisites: '',
        alternatives: ''
      });

      const updatedAnswers = trainingDB.getAnswersForQuestion(selectedQuestionForEdit!.id);
      setAnswers(updatedAnswers);
      loadData();
    };

    const handleCancelEdit = () => {
      setEditingAnswerId(null);
      setNewAnswer({
        analysisId: '',
        priority: 'medium',
        reason: '',
        prerequisites: '',
        alternatives: ''
      });
    };

    const getAnalysisName = (analysisId: string) => {
      const analysis = availableAnalyses.find(a => a.id === analysisId);
      return analysis ? analysis.name : analysisId;
    };

    const getPriorityColor = (priority: string) => {
      switch (priority) {
        case 'high': return 'error';
        case 'medium': return 'warning';
        case 'low': return 'info';
        default: return 'default';
      }
    };

    return (
      <Dialog
        open={showQuestionEditor}
        onClose={() => setShowQuestionEditor(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{ sx: { height: '90vh' } }}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <PsychologyIcon color="primary" />
            <Typography variant="h6">
              {selectedQuestionForEdit ? 'Edit Question & Answers' : 'Question Editor'}
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          <Box sx={{ display: 'flex', height: '100%' }}>
            {/* Left Panel - Question Details */}
            <Box sx={{ width: '40%', p: 3, borderRight: 1, borderColor: 'divider' }}>
              <Typography variant="h6" gutterBottom>Question Details</Typography>

              <TextField
                label="Question"
                multiline
                rows={3}
                value={questionData.question}
                onChange={(e) => setQuestionData({ ...questionData, question: e.target.value })}
                fullWidth
                sx={{ mb: 2 }}
              />

              <TextField
                label="Keywords (comma-separated)"
                value={questionData.keywords}
                onChange={(e) => setQuestionData({ ...questionData, keywords: e.target.value })}
                fullWidth
                sx={{ mb: 2 }}
                helperText="Keywords for matching user queries"
              />

              <TextField
                label="Patterns (one per line)"
                multiline
                rows={3}
                value={questionData.patterns}
                onChange={(e) => setQuestionData({ ...questionData, patterns: e.target.value })}
                fullWidth
                sx={{ mb: 2 }}
                helperText="Optional regex patterns for matching"
              />

              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={questionData.category}
                    onChange={(e) => setQuestionData({ ...questionData, category: e.target.value as TrainingQuestion['category'] })}
                  >
                    <MenuItem value="correlation">Correlation</MenuItem>
                    <MenuItem value="comparison">Comparison</MenuItem>
                    <MenuItem value="categorical">Categorical</MenuItem>
                    <MenuItem value="descriptive">Descriptive</MenuItem>
                    <MenuItem value="prediction">Prediction</MenuItem>
                    <MenuItem value="test">Test</MenuItem>
                    <MenuItem value="visualization">Visualization</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </Select>
                </FormControl>

                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>Difficulty</InputLabel>
                  <Select
                    value={questionData.difficulty}
                    onChange={(e) => setQuestionData({ ...questionData, difficulty: e.target.value as TrainingQuestion['difficulty'] })}
                  >
                    <MenuItem value="basic">Basic</MenuItem>
                    <MenuItem value="intermediate">Intermediate</MenuItem>
                    <MenuItem value="advanced">Advanced</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <TextField
                label="Context (optional)"
                multiline
                rows={2}
                value={questionData.context}
                onChange={(e) => setQuestionData({ ...questionData, context: e.target.value })}
                fullWidth
                sx={{ mb: 2 }}
              />

              <Button
                variant="contained"
                onClick={handleSaveQuestion}
                fullWidth
                sx={{ mt: 2 }}
              >
                Save Question
              </Button>
            </Box>

            {/* Right Panel - Answers Management */}
            <Box sx={{ width: '60%', p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Answers ({answers.length})
              </Typography>

              {/* Existing Answers List */}
              <Box sx={{ mb: 3, maxHeight: '300px', overflow: 'auto' }}>
                {answers.map((answer) => (
                  <Card key={answer.id} variant="outlined" sx={{ mb: 2 }}>
                    <CardContent sx={{ pb: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                        <Box>
                          <Typography variant="subtitle2" color="primary">
                            {getAnalysisName(answer.analysisId)} ({answer.analysisId})
                          </Typography>
                          <Chip
                            label={answer.priority}
                            size="small"
                            color={getPriorityColor(answer.priority) as any}
                            sx={{ mt: 0.5 }}
                          />
                        </Box>
                        <Box>
                          <IconButton size="small" onClick={() => handleEditAnswer(answer)}>
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton size="small" onClick={() => handleDeleteAnswer(answer.id)} color="error">
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </Box>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        {answer.reason}
                      </Typography>
                      {answer.prerequisites && answer.prerequisites.length > 0 && (
                        <Typography variant="caption" color="text.secondary">
                          Prerequisites: {answer.prerequisites.join(', ')}
                        </Typography>
                      )}
                      {answer.alternatives && answer.alternatives.length > 0 && (
                        <Typography variant="caption" color="text.secondary" display="block">
                          Alternatives: {answer.alternatives.join(', ')}
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Add/Edit Answer Form */}
              <Typography variant="subtitle1" gutterBottom>
                {editingAnswerId ? 'Edit Answer' : 'Add New Answer'}
              </Typography>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Analysis</InputLabel>
                <Select
                  value={newAnswer.analysisId}
                  onChange={(e) => setNewAnswer({ ...newAnswer, analysisId: e.target.value })}
                >
                  {availableAnalyses.map((analysis) => (
                    <MenuItem key={analysis.id} value={analysis.id}>
                      {analysis.name} ({analysis.id})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={newAnswer.priority}
                  onChange={(e) => setNewAnswer({ ...newAnswer, priority: e.target.value as TrainingAnswer['priority'] })}
                >
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                </Select>
              </FormControl>

              <TextField
                label="Reason"
                multiline
                rows={2}
                value={newAnswer.reason}
                onChange={(e) => setNewAnswer({ ...newAnswer, reason: e.target.value })}
                fullWidth
                sx={{ mb: 2 }}
                required
              />

              <TextField
                label="Prerequisites (comma-separated)"
                value={newAnswer.prerequisites}
                onChange={(e) => setNewAnswer({ ...newAnswer, prerequisites: e.target.value })}
                fullWidth
                sx={{ mb: 2 }}
                helperText="Optional: conditions that must be met"
              />

              <TextField
                label="Alternatives (comma-separated analysis IDs)"
                value={newAnswer.alternatives}
                onChange={(e) => setNewAnswer({ ...newAnswer, alternatives: e.target.value })}
                fullWidth
                sx={{ mb: 2 }}
                helperText="Optional: alternative analysis suggestions"
              />

              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleSaveAnswer}
                  disabled={!newAnswer.analysisId || !newAnswer.reason.trim()}
                >
                  {editingAnswerId ? 'Update Answer' : 'Add Answer'}
                </Button>
                {editingAnswerId && (
                  <Button onClick={handleCancelEdit}>
                    Cancel Edit
                  </Button>
                )}
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowQuestionEditor(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  };

  const renderQuestionsTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6">Training Questions ({questions.length})</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setShowQuestionDialog(true)}
        >
          Add Question
        </Button>
      </Box>

      <Grid container spacing={2}>
        {questions.map((question) => (
          <Grid item xs={12} md={6} key={question.id}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  {question.question}
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Chip label={question.category} size="small" sx={{ mr: 1 }} />
                  <Chip label={question.difficulty} size="small" variant="outlined" />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Keywords: {question.keywords.join(', ')}
                </Typography>
                {question.context && (
                  <Typography variant="body2" color="text.secondary">
                    Context: {question.context}
                  </Typography>
                )}
                {/* Show existing answers with better visibility */}
                {(() => {
                  const questionAnswers = trainingDB.getAnswersForQuestion(question.id);
                  return questionAnswers.length > 0 && (
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                      <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 500 }}>
                        Answers ({questionAnswers.length}):
                      </Typography>
                      <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {questionAnswers.slice(0, 3).map((answer, index) => {
                          const analysisName = availableAnalyses.find(a => a.id === answer.analysisId)?.name || answer.analysisId;
                          return (
                            <Chip
                              key={index}
                              label={`${analysisName} (${answer.priority})`}
                              size="small"
                              variant="outlined"
                              color={answer.priority === 'high' ? 'error' : answer.priority === 'medium' ? 'warning' : 'info'}
                            />
                          );
                        })}
                        {questionAnswers.length > 3 && (
                          <Chip
                            label={`+${questionAnswers.length - 3} more`}
                            size="small"
                            variant="outlined"
                            color="default"
                          />
                        )}
                      </Box>
                    </Box>
                  );
                })()}

                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<EditIcon />}
                    onClick={() => {
                      setSelectedQuestionForEdit(question);
                      setShowQuestionEditor(true);
                    }}
                    sx={{ minWidth: 120 }}
                  >
                    Edit Question
                  </Button>
                  <Typography variant="caption" color="text.secondary">
                    {question.difficulty} • {question.category}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const renderSessionsTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6">Training Sessions ({sessions.length})</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setShowSessionDialog(true)}
        >
          Create Session
        </Button>
      </Box>

      <Grid container spacing={2}>
        {sessions.map((session) => (
          <Grid item xs={12} md={6} key={session.id}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {session.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {session.description}
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Chip
                    label={session.status}
                    size="small"
                    color={session.status === 'completed' ? 'success' : session.status === 'active' ? 'primary' : 'default'}
                  />
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(session.completedQuestions.length / session.questions.length) * 100}
                  sx={{ mb: 1 }}
                />
                <Typography variant="caption" color="text.secondary">
                  Progress: {session.completedQuestions.length}/{session.questions.length} questions
                </Typography>
                <Typography variant="caption" display="block" color="text.secondary">
                  Created: {new Date(session.createdAt).toLocaleDateString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const renderCuratedTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6">Curated Suggestions ({curatedSuggestions.length})</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<SchoolIcon />}
            onClick={handleGenerateCuratedSuggestions}
          >
            Generate from Training
          </Button>
          <Button
            variant="contained"
            startIcon={<DownloadIcon />}
            onClick={handleExportData}
          >
            Export Data
          </Button>
          <input
            type="file"
            accept=".json"
            style={{ display: 'none' }}
            id="import-file"
            onChange={handleImportData}
          />
          <label htmlFor="import-file">
            <Button
              variant="outlined"
              startIcon={<UploadIcon />}
              component="span"
            >
              Import Data
            </Button>
          </label>
        </Box>
      </Box>

      <Grid container spacing={2}>
        {curatedSuggestions.map((suggestion, index) => (
          <Grid item xs={12} key={index}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                  <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                    {suggestion.questionPattern}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, mr: 2 }}>
                    <Chip label={suggestion.category} size="small" />
                    {suggestion.validated && <CheckCircleIcon color="success" />}
                  </Box>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Typography variant="body2">
                    <strong>Keywords:</strong> {suggestion.keywords.join(', ')}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Usage:</strong> {suggestion.usage_count} times,
                    Success Rate: {(suggestion.success_rate * 100).toFixed(1)}%
                  </Typography>
                  <Typography variant="body2">
                    <strong>Suggestions:</strong>
                  </Typography>
                  <List dense>
                    {suggestion.suggestions.map((sug, idx) => (
                      <ListItem key={idx}>
                        <ListItemText
                          primary={availableAnalyses.find(a => a.id === sug.analysisId)?.name || sug.analysisId}
                          secondary={`Priority: ${sug.priority}/10, Confidence: ${(sug.confidence * 100).toFixed(0)}% - ${sug.reason}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </AccordionDetails>
            </Accordion>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const renderStatsTab = () => (
    <Box>
      <Typography variant="h6" gutterBottom>Training Statistics</Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h4" color="primary" gutterBottom>
              {stats.totalQuestions}
            </Typography>
            <Typography variant="body1">Total Questions</Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h4" color="primary" gutterBottom>
              {stats.totalAnswers}
            </Typography>
            <Typography variant="body1">Total Answers</Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h4" color="success.main" gutterBottom>
              {stats.validatedAnswers}
            </Typography>
            <Typography variant="body1">Validated Answers</Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h4" color="info.main" gutterBottom>
              {stats.accuracyScore.toFixed(1)}%
            </Typography>
            <Typography variant="body1">Accuracy Score</Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h4" color="secondary.main" gutterBottom>
              {stats.sessionsCompleted}
            </Typography>
            <Typography variant="body1">Sessions Completed</Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="body1" gutterBottom>Last Training</Typography>
            <Typography variant="h6">
              {new Date(stats.lastTrainingDate).toLocaleDateString()}
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>Data Management</Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            color="error"
            startIcon={<ClearIcon />}
            onClick={handleClearAllData}
          >
            Clear All Data
          </Button>
        </Box>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <PsychologyIcon sx={{ mr: 2, color: theme.palette.primary.main }} />
        <Typography variant="h4">Analysis Assistant Training System</Typography>
      </Box>

      {notification && (
        <Alert 
          severity={notification.type} 
          sx={{ mb: 3 }}
          onClose={() => setNotification(null)}
        >
          {notification.message}
        </Alert>
      )}

      {/* Tab Navigation */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {[
            { value: 'questions', label: 'Questions' },
            { value: 'sessions', label: 'Sessions' },
            { value: 'curated', label: 'Curated' },
            { value: 'stats', label: 'Statistics' }
          ].map((tab) => (
            <Button
              key={tab.value}
              variant={activeTab === tab.value ? 'contained' : 'text'}
              onClick={() => setActiveTab(tab.value as TabValue)}
            >
              {tab.label}
            </Button>
          ))}
        </Box>
      </Box>

      {/* Tab Content */}
      {activeTab === 'questions' && renderQuestionsTab()}
      {activeTab === 'sessions' && renderSessionsTab()}
      {activeTab === 'curated' && renderCuratedTab()}
      {activeTab === 'stats' && renderStatsTab()}

      {/* Question Dialog */}
      <Dialog open={showQuestionDialog} onClose={() => setShowQuestionDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingQuestion ? 'Edit Question' : 'Add New Question'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="Question"
              multiline
              rows={3}
              value={questionForm.question}
              onChange={(e) => setQuestionForm({ ...questionForm, question: e.target.value })}
              fullWidth
            />
            
            <TextField
              label="Keywords (comma-separated)"
              value={questionForm.keywords}
              onChange={(e) => setQuestionForm({ ...questionForm, keywords: e.target.value })}
              fullWidth
              helperText="e.g., correlation, relationship, association"
            />
            
            <TextField
              label="Regex Patterns (one per line)"
              multiline
              rows={3}
              value={questionForm.patterns}
              onChange={(e) => setQuestionForm({ ...questionForm, patterns: e.target.value })}
              fullWidth
              helperText="Optional regex patterns for matching"
            />
            
            <Box sx={{ display: 'flex', gap: 2 }}>
              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>Category</InputLabel>
                <Select
                  value={questionForm.category}
                  onChange={(e) => setQuestionForm({ ...questionForm, category: e.target.value as TrainingQuestion['category'] })}
                >
                  <MenuItem value="correlation">Correlation</MenuItem>
                  <MenuItem value="comparison">Comparison</MenuItem>
                  <MenuItem value="categorical">Categorical</MenuItem>
                  <MenuItem value="descriptive">Descriptive</MenuItem>
                  <MenuItem value="prediction">Prediction</MenuItem>
                  <MenuItem value="test">Test</MenuItem>
                  <MenuItem value="visualization">Visualization</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
              
              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>Difficulty</InputLabel>
                <Select
                  value={questionForm.difficulty}
                  onChange={(e) => setQuestionForm({ ...questionForm, difficulty: e.target.value as TrainingQuestion['difficulty'] })}
                >
                  <MenuItem value="basic">Basic</MenuItem>
                  <MenuItem value="intermediate">Intermediate</MenuItem>
                  <MenuItem value="advanced">Advanced</MenuItem>
                </Select>
              </FormControl>
            </Box>
            
            <TextField
              label="Context (optional)"
              multiline
              rows={2}
              value={questionForm.context}
              onChange={(e) => setQuestionForm({ ...questionForm, context: e.target.value })}
              fullWidth
              helperText="Additional context about when this question applies"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowQuestionDialog(false)}>Cancel</Button>
          <Button onClick={handleAddQuestion} variant="contained">
            {editingQuestion ? 'Update' : 'Add'} Question
          </Button>
        </DialogActions>
      </Dialog>

      {/* Answer Dialog */}
      <Dialog open={showAnswerDialog} onClose={() => setShowAnswerDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add Answer for Question</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Analysis</InputLabel>
              <Select
                value={answerForm.analysisId}
                onChange={(e) => setAnswerForm({ ...answerForm, analysisId: e.target.value })}
              >
                {availableAnalyses.map((analysis) => (
                  <MenuItem key={analysis.id} value={analysis.id}>
                    {analysis.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            <FormControl>
              <InputLabel>Priority</InputLabel>
              <Select
                value={answerForm.priority}
                onChange={(e) => setAnswerForm({ ...answerForm, priority: e.target.value as TrainingAnswer['priority'] })}
              >
                <MenuItem value="high">High</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="low">Low</MenuItem>
              </Select>
            </FormControl>
            
            <TextField
              label="Reason"
              multiline
              rows={3}
              value={answerForm.reason}
              onChange={(e) => setAnswerForm({ ...answerForm, reason: e.target.value })}
              fullWidth
              required
              helperText="Explain why this analysis is recommended"
            />
            
            <TextField
              label="Prerequisites (comma-separated)"
              value={answerForm.prerequisites}
              onChange={(e) => setAnswerForm({ ...answerForm, prerequisites: e.target.value })}
              fullWidth
              helperText="Optional: conditions that must be met"
            />
            
            <TextField
              label="Alternatives (comma-separated analysis IDs)"
              value={answerForm.alternatives}
              onChange={(e) => setAnswerForm({ ...answerForm, alternatives: e.target.value })}
              fullWidth
              helperText="Optional: alternative analysis suggestions"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAnswerDialog(false)}>Cancel</Button>
          <Button onClick={handleAddAnswer} variant="contained">Add Answer</Button>
        </DialogActions>
      </Dialog>

      {/* Session Dialog */}
      <Dialog open={showSessionDialog} onClose={() => setShowSessionDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Training Session</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="Session Name"
              value={sessionForm.name}
              onChange={(e) => setSessionForm({ ...sessionForm, name: e.target.value })}
              fullWidth
              required
            />

            <TextField
              label="Description"
              multiline
              rows={3}
              value={sessionForm.description}
              onChange={(e) => setSessionForm({ ...sessionForm, description: e.target.value })}
              fullWidth
            />

            <Typography variant="subtitle2">Select Questions for Session:</Typography>
            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
              {questions.map((question) => (
                <FormControlLabel
                  key={question.id}
                  control={
                    <Checkbox
                      checked={sessionForm.selectedQuestions.includes(question.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSessionForm({
                            ...sessionForm,
                            selectedQuestions: [...sessionForm.selectedQuestions, question.id]
                          });
                        } else {
                          setSessionForm({
                            ...sessionForm,
                            selectedQuestions: sessionForm.selectedQuestions.filter(id => id !== question.id)
                          });
                        }
                      }}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body2">{question.question}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {question.category} • {question.difficulty}
                      </Typography>
                    </Box>
                  }
                />
              ))}
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSessionDialog(false)}>Cancel</Button>
          <Button onClick={handleCreateSession} variant="contained">Create Session</Button>
        </DialogActions>
      </Dialog>

      {/* Comprehensive Question Editor */}
      <QuestionEditor />
    </Box>
  );
};

export default TrainingInterface;
