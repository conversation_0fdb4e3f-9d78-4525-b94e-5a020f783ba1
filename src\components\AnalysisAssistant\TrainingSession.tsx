import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  CheckCircle as CheckIcon,
  Edit as EditIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import {
  trainingDB,
  TrainingSession as TrainingSessionType,
  TrainingQuestion,
  TrainingAnswer
} from '../../utils/trainingDatabase';

interface TrainingSessionProps {
  sessionId: string;
  onComplete?: () => void;
  onClose?: () => void;
}

const TrainingSession: React.FC<TrainingSessionProps> = ({ sessionId, onComplete, onClose }) => {
  const theme = useTheme();
  const [session, setSession] = useState<TrainingSessionType | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentQuestion, setCurrentQuestion] = useState<TrainingQuestion | null>(null);
  const [userAnswer, setUserAnswer] = useState('');
  const [selectedAnalysis, setSelectedAnalysis] = useState('');
  const [priority, setPriority] = useState<'high' | 'medium' | 'low'>('medium');
  const [showResults, setShowResults] = useState(false);
  const [sessionComplete, setSessionComplete] = useState(false);

  // Available analyses (simplified list)
  const availableAnalyses = [
    { id: 'CORR1', name: 'Correlation Matrix' },
    { id: 'VIZ4', name: 'Scatter Plot' },
    { id: 'REG1', name: 'Linear Regression' },
    { id: 'CAT1', name: 'Chi-Square Test' },
    { id: 'DESC2', name: 'Frequency Tables' },
    { id: 'DESC3', name: 'Cross-Tabulation' },
    { id: 'TTEST2', name: 'Independent T-Test' },
    { id: 'NONPAR1', name: 'Mann-Whitney U Test' },
    { id: 'DESC1', name: 'Descriptive Analysis' },
    { id: 'TTEST3', name: 'Paired T-Test' },
    { id: 'NONPAR2', name: 'Wilcoxon Signed-Rank Test' },
    { id: 'ANOVA1', name: 'One-Way ANOVA' },
    { id: 'NONPAR3', name: 'Kruskal-Wallis Test' },
    { id: 'NORM1', name: 'Normality Tests' },
    { id: 'VIZ2', name: 'Histograms and Q-Q plots' }
  ];

  useEffect(() => {
    const sessionData = trainingDB.getSession(sessionId);
    if (sessionData) {
      setSession(sessionData);
      loadCurrentQuestion(sessionData, 0);
    }
  }, [sessionId]);

  const loadCurrentQuestion = (sessionData: TrainingSessionType, index: number) => {
    if (index < sessionData.questions.length) {
      const questionId = sessionData.questions[index];
      const question = trainingDB.getQuestion(questionId);
      setCurrentQuestion(question || null);
      setCurrentQuestionIndex(index);
      
      // Reset form
      setUserAnswer('');
      setSelectedAnalysis('');
      setPriority('medium');
      setShowResults(false);
    } else {
      setSessionComplete(true);
    }
  };

  const handleSubmitAnswer = () => {
    if (!currentQuestion || !selectedAnalysis || !userAnswer.trim()) {
      return;
    }

    // Add the answer to the training database
    trainingDB.addAnswer({
      questionId: currentQuestion.id,
      analysisId: selectedAnalysis,
      priority,
      reason: userAnswer,
      validated: true
    });

    // Mark question as completed in session
    trainingDB.markQuestionCompleted(sessionId, currentQuestion.id);

    setShowResults(true);
  };

  const handleNextQuestion = () => {
    if (!session) return;
    
    const nextIndex = currentQuestionIndex + 1;
    if (nextIndex >= session.questions.length) {
      setSessionComplete(true);
      onComplete?.();
    } else {
      loadCurrentQuestion(session, nextIndex);
    }
  };

  const getProgress = () => {
    if (!session) return 0;
    return (session.completedQuestions.length / session.questions.length) * 100;
  };

  const getExistingAnswers = () => {
    if (!currentQuestion) return [];
    return trainingDB.getAnswersForQuestion(currentQuestion.id);
  };

  if (!session || !currentQuestion) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography>Loading training session...</Typography>
      </Box>
    );
  }

  if (sessionComplete) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <CheckIcon sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
        <Typography variant="h5" gutterBottom>
          Training Session Complete!
        </Typography>
        <Typography variant="body1" color="text.secondary" gutterBottom>
          You have successfully completed all questions in this training session.
        </Typography>
        <Button variant="contained" onClick={onClose} sx={{ mt: 2 }}>
          Close Session
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <SchoolIcon sx={{ mr: 2, color: theme.palette.primary.main }} />
        <Typography variant="h6">{session.name}</Typography>
      </Box>

      {/* Progress */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2">
            Question {currentQuestionIndex + 1} of {session.questions.length}
          </Typography>
          <Typography variant="body2">
            {Math.round(getProgress())}% Complete
          </Typography>
        </Box>
        <LinearProgress variant="determinate" value={getProgress()} />
      </Box>

      {/* Current Question */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {currentQuestion.question}
          </Typography>
          <Box sx={{ mb: 2 }}>
            <Chip label={currentQuestion.category} size="small" sx={{ mr: 1 }} />
            <Chip label={currentQuestion.difficulty} size="small" variant="outlined" />
          </Box>
          <Typography variant="body2" color="text.secondary">
            Keywords: {currentQuestion.keywords.join(', ')}
          </Typography>
          {currentQuestion.context && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Context: {currentQuestion.context}
            </Typography>
          )}
        </CardContent>
      </Card>

      {/* Answer Form */}
      {!showResults ? (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Provide Your Answer
            </Typography>
            
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Recommended Analysis</InputLabel>
              <Select
                value={selectedAnalysis}
                onChange={(e) => setSelectedAnalysis(e.target.value)}
                required
              >
                {availableAnalyses.map((analysis) => (
                  <MenuItem key={analysis.id} value={analysis.id}>
                    {analysis.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl sx={{ mb: 2, minWidth: 120 }}>
              <InputLabel>Priority</InputLabel>
              <Select
                value={priority}
                onChange={(e) => setPriority(e.target.value as 'high' | 'medium' | 'low')}
              >
                <MenuItem value="high">High</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="low">Low</MenuItem>
              </Select>
            </FormControl>

            <TextField
              label="Explanation"
              multiline
              rows={4}
              value={userAnswer}
              onChange={(e) => setUserAnswer(e.target.value)}
              fullWidth
              required
              helperText="Explain why this analysis is appropriate for this question"
              sx={{ mb: 2 }}
            />

            <Button
              variant="contained"
              onClick={handleSubmitAnswer}
              disabled={!selectedAnalysis || !userAnswer.trim()}
            >
              Submit Answer
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Alert severity="success" sx={{ mb: 2 }}>
              Answer submitted successfully! Your response has been added to the training database.
            </Alert>
            
            {/* Show existing answers for comparison */}
            {getExistingAnswers().length > 0 && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Existing Answers for Comparison:
                </Typography>
                <List dense>
                  {getExistingAnswers().map((answer, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={availableAnalyses.find(a => a.id === answer.analysisId)?.name || answer.analysisId}
                        secondary={`${answer.priority.toUpperCase()} priority: ${answer.reason}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            <Button
              variant="contained"
              onClick={handleNextQuestion}
              sx={{ mt: 2 }}
            >
              {currentQuestionIndex + 1 >= session.questions.length ? 'Complete Session' : 'Next Question'}
            </Button>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default TrainingSession;
