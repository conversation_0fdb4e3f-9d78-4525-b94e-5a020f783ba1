import React from 'react';
import { useAuth } from '../../context/AuthContext';
import UserProfile from './UserProfile';
import AuthContainer, { AuthView } from './AuthContainer';

interface AuthProps {
  initialView?: AuthView;
  onAuthSuccess?: () => void;
}

const Auth: React.FC<AuthProps> = ({ initialView = AuthView.LOGIN, onAuthSuccess }) => {
  // const { user } = useAuth(); // user check is not needed here anymore

  // If the user is already authenticated, App.tsx should not render the Auth component.
  // This component's responsibility is to show the login/register forms
  // and call onAuthSuccess when authentication is successful.
  // App.tsx will then handle navigation.

  return (
    <AuthContainer 
      initialView={initialView}
      onAuthSuccess={onAuthSuccess} // This will be called by AuthContainer
    />
  );
};


export { AuthView };
export default Auth;
