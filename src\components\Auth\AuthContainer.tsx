import React, { useState, useEffect } from 'react';
import { Box, Container, Paper, Tabs, Tab, Typography, useTheme } from '@mui/material';
import { Person as PersonIcon, Vpn<PERSON>ey as VpnKeyIcon, LockReset as LockResetIcon, PersonOutline as PersonOutlineIcon } from '@mui/icons-material';
import Login from './Login';
import Register from './Register';
import ResetPassword from './ResetPassword';
import GuestAccess from './GuestAccess';
import { useAuth } from '../../context/AuthContext';

export enum AuthView {
  LOGIN = 'login',
  REGISTER = 'register',
  RESET_PASSWORD = 'reset_password',
  GUEST_ACCESS = 'guest_access'
}

interface AuthContainerProps {
  initialView?: AuthView;
  onAuthSuccess?: () => void;
  onGuestLoginSuccess?: () => void; // Add callback for guest login
}

const AuthContainer: React.FC<AuthContainerProps> = ({ 
  initialView = AuthView.LOGIN, 
  onAuthSuccess,
  onGuestLoginSuccess // Destructure the new callback
}) => {
  const theme = useTheme();
  const { user, isGuest, loginAsGuest } = useAuth(); // Get guest state and login function
  const [currentView, setCurrentView] = useState<AuthView>(initialView);

  // Redirect if user is already authenticated or is guest
  // This effect handles redirection *after* state is updated by login/signup/guest actions
  useEffect(() => {
    if (user || isGuest) { // Redirect if logged in OR guest
      if (onAuthSuccess) onAuthSuccess(); // Use the same success callback for simplicity
    }
  }, [user, isGuest, onAuthSuccess]);

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: AuthView) => {
    setCurrentView(newValue);
  };

  // Handle guest login action from Login component
  const handleGuestLogin = () => {
    loginAsGuest(); // Update auth context state
  };

  // If already authenticated or guest, don't show auth forms (useEffect handles redirect)
  // Show a loading state briefly while redirect happens
  if (user || isGuest) {
    return (
      <Container maxWidth="sm" sx={{ mt: 4, mb: 4, textAlign: 'center' }}>
        <Typography variant="h6">Redirecting...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="sm" sx={{ mt: 4, mb: 4 }}>
      <Paper 
        elevation={3} 
        sx={{ 
          borderRadius: 2,
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
        }}
      >
        <Box sx={{ 
          p: 2, 
          bgcolor: theme.palette.primary.main, 
          color: 'white',
          textAlign: 'center'
        }}>
          <Typography variant="h5" component="h1">
            DataStatPro
          </Typography>
          <Typography variant="subtitle2">
            Statistical Analysis Platform
          </Typography>
        </Box>

        <Tabs 
          value={currentView} 
          onChange={handleTabChange}
          variant="fullWidth"
          indicatorColor="primary"
          textColor="primary"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab 
            icon={<VpnKeyIcon />} 
            label="Sign In" 
            value={AuthView.LOGIN} 
            iconPosition="start"
          />
          <Tab 
            icon={<PersonIcon />} 
            label="Register" 
            value={AuthView.REGISTER} 
            iconPosition="start"
          />
          <Tab 
            icon={<PersonOutlineIcon />} 
            label="Guest Access" 
            value={AuthView.GUEST_ACCESS} 
            iconPosition="start"
          />
          <Tab 
            icon={<LockResetIcon />} 
            label="Reset" 
            value={AuthView.RESET_PASSWORD} 
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {currentView === AuthView.LOGIN && (
            <Login
              onRegisterClick={() => setCurrentView(AuthView.REGISTER)}
              onResetPasswordClick={() => setCurrentView(AuthView.RESET_PASSWORD)}
              onGuestLoginClick={() => setCurrentView(AuthView.GUEST_ACCESS)}
            />
          )}

          {currentView === AuthView.REGISTER && (
            <Register
              onLoginClick={() => setCurrentView(AuthView.LOGIN)}
            />
          )}

          {currentView === AuthView.GUEST_ACCESS && (
            <GuestAccess
              onGuestLoginClick={handleGuestLogin}
            />
          )}

          {currentView === AuthView.RESET_PASSWORD && (
            <ResetPassword
              onLoginClick={() => setCurrentView(AuthView.LOGIN)}
            />
          )}
        </Box>
      </Paper>
    </Container>
  );
};

export default AuthContainer;
