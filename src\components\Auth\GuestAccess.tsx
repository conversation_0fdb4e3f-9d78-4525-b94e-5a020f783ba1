import React from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  Paper,
  useTheme
} from '@mui/material';
import { PersonOutline as PersonOutlineIcon } from '@mui/icons-material';

interface GuestAccessProps {
  onGuestLoginClick: () => void;
}

const GuestAccess: React.FC<GuestAccessProps> = ({ onGuestLoginClick }) => {
  const theme = useTheme();

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 400, mx: 'auto', mt: 4 }}>
      <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
        <PersonOutlineIcon fontSize="large" color="secondary" sx={{ mb: 1 }} />
        <Typography variant="h5" component="h1" gutterBottom>
          Guest Access
        </Typography>
      </Box>

      <Alert 
        severity="info" 
        sx={{ 
          mb: 3, 
          border: `1px solid ${theme.palette.info.main}`,
          '& .MuiAlert-message': { width: '100%' }
        }}
      >
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Guest Access Information
        </Typography>
        <Typography variant="body2" paragraph>
          Guest users may explore the application with all features but are limited to Sample Datasets for teaching and learning purposes.
        </Typography>
        <Typography variant="body2" component="ul" sx={{ pl: 2, m: 0 }}>
          <li>Access to all statistical analysis features</li>
          <li>Limited to pre-loaded sample datasets only</li>
          <li>No data saving or custom data importing</li>
          <li>Perfect for learning and evaluation</li>
        </Typography>
      </Alert>

      <Typography variant="body2" paragraph textAlign="center" sx={{ mb: 3 }}>
        No registration required. Start exploring DataStatPro immediately.
      </Typography>

      <Button
        fullWidth
        variant="contained"
        color="secondary"
        size="large"
        startIcon={<PersonOutlineIcon />}
        onClick={onGuestLoginClick}
        sx={{ 
          py: 1.5,
          fontSize: '1.1rem',
          fontWeight: 'medium'
        }}
      >
        Continue as Guest
      </Button>
    </Paper>
  );
};

export default GuestAccess;