import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Link,
  Alert,
  CircularProgress,
  useTheme,
  Snackbar, // Import Snackbar
  IconButton // Import IconButton for Snackbar close action
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close'; // Import Close icon
import { useAuth } from '../../context/AuthContext';
import { Lock as LockIcon } from '@mui/icons-material';
import GoogleIcon from '@mui/icons-material/Google'; // Import Google icon
import { useNavigate } from 'react-router-dom'; // Import useNavigate

interface LoginProps {
  onRegisterClick: () => void;
  onResetPasswordClick: () => void;
  onGuestLoginClick: () => void; // Now used to navigate to Guest Access tab
}

const Login: React.FC<LoginProps> = ({ 
  onRegisterClick, 
  onResetPasswordClick, 
  onGuestLoginClick // Destructure the prop
}) => {
  const theme = useTheme();
  const { signIn, signInWithGoogle } = useAuth(); // Destructure signInWithGoogle
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [openSnackbar, setOpenSnackbar] = useState(false); // State for Snackbar
  const navigate = useNavigate(); // Initialize useNavigate

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      const { error } = await signIn(email, password);
      if (error) throw error;
      setOpenSnackbar(true); // Show success message
      // Navigation will be handled by AuthContext
    } catch (err: any) {
      setError(err.message || 'Failed to sign in');
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpenSnackbar(false);
  };

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 400, mx: 'auto', mt: 4 }}>
      <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
        <LockIcon fontSize="large" color="primary" sx={{ mb: 1 }} />
        <Typography variant="h5" component="h1" gutterBottom>
          Sign in to DataStatPro
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Google Sign-In Button - Moved to top */}
      <Button
        fullWidth
        variant="contained"
        color="secondary"
        size="large"
        sx={{ mt: 1, mb: 2 }}
        onClick={signInWithGoogle}
        disabled={loading}
        startIcon={<GoogleIcon />}
      >
        Sign In with Google
      </Button>

      <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
        <Box sx={{ flexGrow: 1, height: '1px', bgcolor: 'divider' }} />
        <Typography variant="body2" sx={{ mx: 2, color: 'text.secondary' }}>
          OR
        </Typography>
        <Box sx={{ flexGrow: 1, height: '1px', bgcolor: 'divider' }} />
      </Box>

      <Box component="form" onSubmit={handleSubmit}>
        <TextField
          label="Email Address"
          type="email"
          fullWidth
          margin="normal"
          required
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          disabled={loading}
        />
        <TextField
          label="Password"
          type="password"
          fullWidth
          margin="normal"
          required
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          disabled={loading}
        />
        <Button
          type="submit"
          fullWidth
          variant="contained"
          color="primary"
          size="large"
          sx={{ mt: 3, mb: 2 }}
          disabled={loading}
          disableElevation
        >
          {loading ? <CircularProgress size={24} /> : 'Sign In'}
        </Button>

        <Box sx={{ textAlign: 'center', mt: 2, mb: 2 }}>
          <Typography variant="body2">
            Don't want to create an account?
            <Link
              component="button"
              variant="body2"
              onClick={onGuestLoginClick}
              underline="hover"
              sx={{ ml: 1 }}
            >
              Continue as guest
            </Link>
          </Typography>
        </Box>
      </Box>

      <Box display="flex" justifyContent="space-between" mt={1}> 
        <Link
          component="button"
          variant="body2"
          onClick={onResetPasswordClick}
          underline="hover"
        >
          Forgot password?
        </Link>
        <Link
          component="button"
          variant="body2"
          onClick={onRegisterClick}
          underline="hover"
        >
          Don't have an account? Sign up
        </Link>
      </Box>

      <Snackbar
        open={openSnackbar}
        autoHideDuration={4000} // Hide after 4 seconds
        onClose={handleCloseSnackbar}
        message="You are successfully logged in to DataStatPro app"
        action={
          <IconButton size="small" aria-label="close" color="inherit" onClick={handleCloseSnackbar}>
            <CloseIcon fontSize="small" />
          </IconButton>
        }
      />
    </Paper>
  );
};

export default Login;
