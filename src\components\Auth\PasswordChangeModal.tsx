import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Alert,
  CircularProgress,
  Box
} from '@mui/material';
import { Password as PasswordIcon } from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';

interface PasswordChangeModalProps {
  open: boolean;
  onClose: () => void;
}

const PasswordChangeModal: React.FC<PasswordChangeModalProps> = ({ open, onClose }) => {
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handlePasswordChange = async () => {
    setMessage(null);
    if (newPassword !== confirmNewPassword) {
      setMessage({ type: 'error', text: 'Passwords do not match.' });
      return;
    }
    if (newPassword.length < 6) {
      setMessage({ type: 'error', text: 'Password must be at least 6 characters long.' });
      return;
    }

    setLoading(true);
    try {
      // Assumes user is already logged in when this modal is opened
      const { error } = await supabase.auth.updateUser({ password: newPassword });
      if (error) throw error;
      setMessage({ type: 'success', text: 'Password changed successfully!' });
      // Clear fields and close modal after a short delay on success
      setTimeout(() => {
        setNewPassword('');
        setConfirmNewPassword('');
        setMessage(null);
        onClose(); 
      }, 1500); 
    } catch (err: any) {
      setMessage({ type: 'error', text: err.message || 'Failed to change password.' });
      console.error("Password change error:", err);
    } finally {
      setLoading(false);
    }
  };

  // Reset state when modal closes
  const handleClose = () => {
    if (!loading) { // Prevent closing while loading
      setNewPassword('');
      setConfirmNewPassword('');
      setMessage(null);
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center' }}>
        <PasswordIcon sx={{ mr: 1 }} /> Change Password
      </DialogTitle>
      <DialogContent>
        <DialogContentText sx={{ mb: 2 }}>
          Enter your new password below. Ensure it meets the minimum security requirements.
        </DialogContentText>
        {message && (
          <Alert severity={message.type} sx={{ mb: 2 }}>
            {message.text}
          </Alert>
        )}
        <TextField
          autoFocus // Focus on first field when opened
          margin="dense"
          id="newPasswordModal" // Unique ID
          label="New Password"
          type="password"
          fullWidth
          variant="outlined"
          value={newPassword}
          onChange={(e) => setNewPassword(e.target.value)}
          disabled={loading}
          helperText="Minimum 6 characters"
        />
        <TextField
          margin="dense"
          id="confirmNewPasswordModal" // Unique ID
          label="Confirm New Password"
          type="password"
          fullWidth
          variant="outlined"
          value={confirmNewPassword}
          onChange={(e) => setConfirmNewPassword(e.target.value)}
          disabled={loading}
        />
      </DialogContent>
      <DialogActions sx={{ p: '16px 24px' }}>
        <Button onClick={handleClose} disabled={loading}>Cancel</Button>
        <Button 
          onClick={handlePasswordChange} 
          variant="contained" 
          disabled={loading || !newPassword || !confirmNewPassword || message?.type === 'success'} // Disable if success message shown
        >
          {loading ? <CircularProgress size={24} /> : 'Update Password'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PasswordChangeModal;
