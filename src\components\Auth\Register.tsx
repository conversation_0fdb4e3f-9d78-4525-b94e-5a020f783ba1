import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Link,
  Alert,
  CircularProgress,
  useTheme,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  FormHelperText,
  Snackbar, // Import Snackbar
  IconButton // Import IconButton for Snackbar close action
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close'; // Import Close icon
import { useAuth } from '../../context/AuthContext';
import { PersonAdd as PersonAddIcon } from '@mui/icons-material';
import GoogleIcon from '@mui/icons-material/Google'; // Import Google icon
import { countries } from '../../data/countries';
import { useNavigate } from 'react-router-dom'; // Import useNavigate
import FeatureComparisonTable from '../UI/FeatureComparisonTable';

interface RegisterProps {
  onLoginClick: () => void;
}

const Register: React.FC<RegisterProps> = ({ onLoginClick }) => {
  const theme = useTheme();
  const { signUp, signInWithGoogle } = useAuth(); // Destructure signInWithGoogle
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [institution, setInstitution] = useState('');
  const [country, setCountry] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false); // State for Snackbar
  const navigate = useNavigate(); // Initialize useNavigate

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    // Validate password strength
    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }
    
    setLoading(true);

    const metadata = {
      full_name: fullName,
      institution,
      country,
      // username: email // Or some other logic for username if desired
    };

    try {
      const { error, user } = await signUp(email, password, { data: metadata });
      if (error) throw error;
      
      // Check if this is a fake user response (existing email)
      if (user && (!user.identities || user.identities.length === 0)) {
        setError('An account with this email already exists');
        setSuccess(false);
      } else {
        setSuccess(true);
        setOpenSnackbar(true); // Show success message
        setTimeout(() => {
          navigate('/dashboard'); // Navigate to dashboard after a delay
        }, 2000); // 2 second delay
      }
    } catch (err: any) {
      setError(err.message || 'Failed to sign up');
      setSuccess(false);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpenSnackbar(false);
  };

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 400, mx: 'auto', mt: 4 }}>
      <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
        <PersonAddIcon fontSize="large" color="primary" sx={{ mb: 1 }} />
        <Typography variant="h5" component="h1" gutterBottom>
          Create an Account
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Registration successful! Please check your email to confirm your account.
        </Alert>
      )}

      {!success && (
        <>
          {/* Google Sign-Up Button - Moved to top */}
          <Button
            fullWidth
            variant="contained" // Changed to contained
            color="secondary" // Changed to secondary to make it more prominent
            size="large"
            sx={{ mt: 1, mb: 2 }}
            onClick={signInWithGoogle}
            disabled={loading}
            startIcon={<GoogleIcon />}
          >
            Sign Up with Google
          </Button>

          <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
            <Box sx={{ flexGrow: 1, height: '1px', bgcolor: 'divider' }} />
            <Typography variant="body2" sx={{ mx: 2, color: 'text.secondary' }}>
              OR
            </Typography>
            <Box sx={{ flexGrow: 1, height: '1px', bgcolor: 'divider' }} />
          </Box>

          <Box component="form" onSubmit={handleSubmit}>
            <TextField
              label="Email Address"
              type="email"
              fullWidth
              margin="normal"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
            <TextField
              label="Full Name"
              type="text"
              fullWidth
              margin="normal"
              required
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              disabled={loading}
            />
            <TextField
              label="Password"
              type="password"
              fullWidth
              margin="normal"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
              helperText="Password must be at least 6 characters"
            />
            <TextField
              label="Confirm Password"
              type="password"
              fullWidth
              margin="normal"
              required
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              disabled={loading}
            />
            <TextField
              label="Institution"
              type="text"
              fullWidth
              margin="normal"
              value={institution}
              onChange={(e) => setInstitution(e.target.value)}
              disabled={loading}
            />
            <FormControl fullWidth margin="normal">
              <InputLabel id="country-select-label">Country</InputLabel>
              <Select
                labelId="country-select-label"
                id="country-select"
                value={country}
                label="Country"
                onChange={(e) => setCountry(e.target.value)}
                disabled={loading}
              >
                <MenuItem value=""><em>Select a country</em></MenuItem>
                {countries.map((country) => (
                  <MenuItem key={country} value={country}>{country}</MenuItem>
                ))}
              </Select>
              <FormHelperText>Select your country from the list</FormHelperText>
            </FormControl>
            <Button
              type="submit"
              fullWidth
              variant="contained"
              color="primary"
              size="large"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Sign Up'}
            </Button>
          </Box>
        </>
      )}

      {/* Feature Comparison */}
      <Box sx={{ mt: 4, mb: 3 }}>
        <Typography variant="h6" textAlign="center" gutterBottom>
          What You Get With Your Account
        </Typography>
        <FeatureComparisonTable compact={true} showDescriptions={false} />
      </Box>

      <Box display="flex" justifyContent="center" mt={2}>
        <Link
          component="button"
          variant="body2"
          onClick={onLoginClick}
          underline="hover"
        >
          Already have an account? Sign in
        </Link>
      </Box>

      <Snackbar
        open={openSnackbar}
        autoHideDuration={2000} // Hide after 2 seconds
        onClose={handleCloseSnackbar}
        message="Registration successful! Redirecting to dashboard..."
        action={
          <IconButton size="small" aria-label="close" color="inherit" onClick={handleCloseSnackbar}>
            <CloseIcon fontSize="small" />
          </IconButton>
        }
      />
    </Paper>
  );
};

export default Register;
