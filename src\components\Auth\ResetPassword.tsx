import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Link,
  Alert,
  CircularProgress,
  useTheme
} from '@mui/material';
import { useAuth } from '../../context/AuthContext';
import { LockReset as LockResetIcon } from '@mui/icons-material';

interface ResetPasswordProps {
  onLoginClick: () => void;
}

const ResetPassword: React.FC<ResetPasswordProps> = ({ onLoginClick }) => {
  const theme = useTheme();
  const { resetPassword } = useAuth();
  
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      const { error } = await resetPassword(email);
      if (error) throw error;
      setSuccess(true);
    } catch (err: any) {
      setError(err.message || 'Failed to send reset password email');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 400, mx: 'auto', mt: 4 }}>
      <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
        <LockResetIcon fontSize="large" color="primary" sx={{ mb: 1 }} />
        <Typography variant="h5" component="h1" gutterBottom>
          Reset Password
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {success ? (
        <Box>
          <Alert severity="success" sx={{ mb: 3 }}>
            Password reset instructions have been sent to your email.
          </Alert>
          <Button
            fullWidth
            variant="contained"
            color="primary"
            onClick={onLoginClick}
            sx={{ mt: 2 }}
          >
            Back to Login
          </Button>
        </Box>
      ) : (
        <Box component="form" onSubmit={handleSubmit}>
          <Typography variant="body2" color="textSecondary" paragraph>
            Enter your email address and we'll send you instructions to reset your password.
          </Typography>
          <TextField
            label="Email Address"
            type="email"
            fullWidth
            margin="normal"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={loading}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            size="large"
            sx={{ mt: 3, mb: 2 }}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Send Reset Instructions'}
          </Button>
          <Box display="flex" justifyContent="center" mt={2}>
            <Link
              component="button"
              variant="body2"
              onClick={onLoginClick}
              underline="hover"
            >
              Back to Login
            </Link>
          </Box>
        </Box>
      )}
    </Paper>
  );
};

export default ResetPassword;