import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Switch,
  FormControlLabel,
  Divider,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Grid,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useTheme,
  SelectChangeEvent,
  Tabs,
  Tab,
  Radio,
  RadioGroup,
  Tooltip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Badge,
  Dialog, // Import Dialog components
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Chip // Import Chip for the badge
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Palette as PaletteIcon,
  Save as SaveIcon,
  Language as LanguageIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  SettingsBrightness as SettingsBrightnessIcon,
  NotificationsActive as NotificationsActiveIcon,
  NotificationsOff as NotificationsOffIcon,
  Email as EmailIcon,
  PhoneAndroid as PhoneAndroidIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  DeleteForever as DeleteForeverIcon,
  CloudDownload as CloudDownloadIcon,
  Info as InfoIcon,
  Help as HelpIcon,
  AccountCircle as AccountCircleIcon // Import AccountCircleIcon
} from '@mui/icons-material';
import { Avatar } from '@mui/material'; // Import Avatar
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../utils/supabaseClient';
import { useAppTheme } from '../../context/ThemeContext'; 
import PasswordChangeModal from './PasswordChangeModal'; // Import the new modal

interface UserSettings {
  notifications_enabled: boolean;
  email_notifications: boolean;
  mobile_notifications: boolean;
  theme_preference: string;
  language_preference: string;
  data_privacy: boolean;
  data_retention: string;
  two_factor_auth: boolean;
  analytics_consent: boolean;
}

const Settings: React.FC = () => {
  const theme = useTheme(); // MUI theme for styling within this component
  const { user, accountType } = useAuth(); // Destructure accountType
  const { themeMode, setThemeMode, appliedTheme } = useAppTheme(); // Use our theme context
  
  const [settings, setSettings] = useState<UserSettings>({
    notifications_enabled: true,
    email_notifications: true,
    mobile_notifications: false,
    theme_preference: 'system',
    language_preference: 'en',
    data_privacy: true,
    data_retention: 'standard',
    two_factor_auth: false,
    analytics_consent: true
  });
  
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [passwordModalOpen, setPasswordModalOpen] = useState(false); // Keep state to control modal visibility


  // Load user settings
  useEffect(() => {
    if (user) {
      setLoading(true);
      loadUserSettings();
    }
  }, [user]);

  const loadUserSettings = async () => {
    try {
      if (!user) return;

      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is the error code for no rows returned
        throw error;
      }

      if (data) {
        setSettings({
          notifications_enabled: data.notifications_enabled ?? true,
          email_notifications: data.email_notifications ?? true,
          mobile_notifications: data.mobile_notifications ?? false,
          theme_preference: data.theme_preference ?? 'system',
          language_preference: data.language_preference ?? 'en',
          data_privacy: data.data_privacy ?? true,
          data_retention: data.data_retention ?? 'standard',
          two_factor_auth: data.two_factor_auth ?? false,
          analytics_consent: data.analytics_consent ?? true
        });
        // Apply loaded theme preference to the context
        if (data.theme_preference) {
          setThemeMode(data.theme_preference as 'light' | 'dark' | 'system');
        }
      } else {
        // If no settings in DB, apply current context's themeMode to local state for UI consistency
        setSettings(prev => ({ ...prev, theme_preference: themeMode }));
      }
    } catch (error: any) {
      console.error('Error loading user settings:', error.message);
      // Fallback: ensure local settings UI reflects current app theme mode
      setSettings(prev => ({ ...prev, theme_preference: themeMode }));
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setLoading(true);
      setMessage(null);

      if (!user) throw new Error('No user');

      const updates = {
        user_id: user.id,
        ...settings,
        updated_at: new Date().toISOString(),
      };

      console.log('Attempting to save settings:', updates); // Added logging
      const { error } = await supabase
        .from('user_settings')
        .upsert(updates);

      if (error) {
        console.error('Error saving settings:', error); // Added logging
        throw error;
      }
      console.log('Settings saved successfully.'); // Added logging
      setMessage({ type: 'success', text: 'Settings saved successfully!' });
    } catch (error: any) {
      console.error('Caught error saving settings:', error); // Added logging
      setMessage({ type: 'error', text: error.message });
    } finally {
      setLoading(false);
    }
  };

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings({
      ...settings,
      [event.target.name]: event.target.checked
    });
  };

  const handleSelectChange = (event: SelectChangeEvent) => {
    setSettings({
      ...settings,
      [event.target.name]: event.target.value
    });
  };
  
  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newPreference = event.target.value as 'light' | 'dark' | 'system';
    setSettings({
      ...settings,
      [event.target.name]: newPreference
    });
    setThemeMode(newPreference); // Apply theme change immediately
  };
  
  const handleThemeCardClick = (newPreference: 'light' | 'dark' | 'system') => {
    setSettings({...settings, theme_preference: newPreference});
    setThemeMode(newPreference); // Apply theme change immediately
  };
  
  // State for active tab
  const [activeTab, setActiveTab] = useState(0);
  
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // --- Password Change Logic (Moved to Modal) ---
  const handleOpenPasswordModal = () => {
    setPasswordModalOpen(true); // Just open the modal
  };

  const handleClosePasswordModal = () => {
    setPasswordModalOpen(false); // Just close the modal
  };
  // --- End Password Change Logic ---


  if (!user) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography>Please sign in to view your settings.</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, maxWidth: 900, mx: 'auto' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <SettingsIcon sx={{ mr: 1 }} color="primary" />
        <Typography variant="h5">Settings</Typography>
      </Box>
      
      {message && (
        <Alert severity={message.type} sx={{ mb: 3 }}>
          {message.text}
        </Alert>
      )}

      <Paper elevation={3} sx={{ p: 3 }}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange} 
          variant="scrollable"
          scrollButtons="auto"
          sx={{ 
            mb: 3, 
            borderBottom: 1, 
            borderColor: 'divider',
            '& .MuiTab-root': { minWidth: 120 }
          }}
        >
          <Tab 
            icon={<PaletteIcon />} 
            label="Appearance" 
            iconPosition="start"
          />
          {/*
          <Tab
            icon={<LanguageIcon />}
            label="Language"
            iconPosition="start"
          />
          */}
          <Tab 
            icon={<NotificationsIcon />} 
            label="Notifications" 
            iconPosition="start"
          />
          <Tab 
            icon={<SecurityIcon />} 
            label="Privacy & Security" 
            iconPosition="start"
          />
        </Tabs>

        {/* User Profile Section (assuming this is part of Settings or a linked component) */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <AccountCircleIcon sx={{ mr: 1 }} color="primary" />
            Account Information
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
              <Avatar
                src={user.user_metadata?.avatar_url || '/logo.png'} // Use user's avatar or default
                alt={user.user_metadata?.full_name || user.email || 'User'}
                sx={{ width: 120, height: 120, mx: 'auto', mb: 2 }}
              />
              <Button variant="outlined" size="small" sx={{ mb: 1 }}>
                Change Avatar
              </Button>
              <Typography variant="body1" fontWeight="medium">
                {user.email}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Member since: {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
              </Typography>
              {accountType && (
                <Chip
                  label={`Subscription Type: ${accountType.charAt(0).toUpperCase() + accountType.slice(1)}`}
                  color={accountType === 'pro' ? 'primary' : (accountType === 'edu' ? 'secondary' : 'default')}
                  sx={{ mt: 1, fontWeight: 'bold' }}
                />
              )}
            </Grid>
            <Grid item xs={12} md={8}>
              <TextField
                label="Username"
                value={user.user_metadata?.username || ''}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="Full Name"
                value={user.user_metadata?.full_name || ''}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="Email"
                value={user.email || ''}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
                helperText="Email cannot be changed"
              />
              <TextField
                label="Institution"
                value={user.user_metadata?.institution || ''}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
              <TextField
                label="Country"
                value={user.user_metadata?.country || ''}
                fullWidth
                margin="normal"
                InputProps={{ readOnly: true }}
              />
            </Grid>
          </Grid>
        </Box>

        {/* Appearance Settings */}
        {activeTab === 0 && (
          <Box>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <PaletteIcon sx={{ mr: 1 }} color="primary" />
              Theme Preferences
            </Typography>
            
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                  Choose your preferred theme
                </Typography>
                
                <RadioGroup
                  name="theme_preference"
                  value={settings.theme_preference}
                  onChange={handleRadioChange}
                  sx={{ mt: 2 }}
                >
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Card 
                        variant={settings.theme_preference === 'light' ? 'elevation' : 'outlined'} 
                        elevation={settings.theme_preference === 'light' ? 3 : 0}
                        sx={{ 
                          p: 2, 
                          textAlign: 'center',
                          bgcolor: settings.theme_preference === 'light' ? theme.palette.action.hover : 'transparent',
                          border: settings.theme_preference === 'light' ? `2px solid ${theme.palette.primary.main}` : `1px solid ${theme.palette.divider}`,
                          cursor: 'pointer',
                          boxShadow: settings.theme_preference === 'light' ? theme.shadows[3] : theme.shadows[1],
                        }}
                        onClick={() => handleThemeCardClick('light')}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
                          <LightModeIcon fontSize="large" color={settings.theme_preference === 'light' ? 'primary' : 'inherit'} />
                        </Box>
                        <FormControlLabel 
                          value="light" 
                          control={<Radio />} 
                          label="Light Mode" 
                          sx={{ mx: 'auto' }}
                        />
                        <Typography variant="body2" color="text.secondary">
                          Bright interface with light colors
                        </Typography>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={12} sm={4}>
                      <Card 
                        variant={settings.theme_preference === 'dark' ? 'elevation' : 'outlined'} 
                        elevation={settings.theme_preference === 'dark' ? 3 : 0}
                        sx={{ 
                          p: 2, 
                          textAlign: 'center',
                          bgcolor: settings.theme_preference === 'dark' ? theme.palette.action.hover : 'transparent',
                          border: settings.theme_preference === 'dark' ? `2px solid ${theme.palette.primary.main}` : `1px solid ${theme.palette.divider}`,
                          cursor: 'pointer',
                          boxShadow: settings.theme_preference === 'dark' ? theme.shadows[3] : theme.shadows[1],
                        }}
                        onClick={() => handleThemeCardClick('dark')}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
                          <DarkModeIcon fontSize="large" color={settings.theme_preference === 'dark' ? 'primary' : 'inherit'} />
                        </Box>
                        <FormControlLabel 
                          value="dark" 
                          control={<Radio />} 
                          label="Dark Mode" 
                          sx={{ mx: 'auto' }}
                        />
                        <Typography variant="body2" color="text.secondary">
                          Dark interface to reduce eye strain
                        </Typography>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={12} sm={4}>
                      <Card 
                        variant={settings.theme_preference === 'system' ? 'elevation' : 'outlined'} 
                        elevation={settings.theme_preference === 'system' ? 3 : 0}
                        sx={{ 
                          p: 2, 
                          textAlign: 'center',
                          bgcolor: settings.theme_preference === 'system' ? theme.palette.action.hover : 'transparent',
                          border: settings.theme_preference === 'system' ? `2px solid ${theme.palette.primary.main}` : `1px solid ${theme.palette.divider}`,
                          cursor: 'pointer',
                          boxShadow: settings.theme_preference === 'system' ? theme.shadows[3] : theme.shadows[1],
                        }}
                        onClick={() => handleThemeCardClick('system')}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
                          <SettingsBrightnessIcon fontSize="large" color={settings.theme_preference === 'system' ? 'primary' : 'inherit'} />
                        </Box>
                        <FormControlLabel 
                          value="system" 
                          control={<Radio />} 
                          label="System Default" 
                          sx={{ mx: 'auto' }}
                        />
                        <Typography variant="body2" color="text.secondary">
                          Follow your device settings
                        </Typography>
                      </Card>
                    </Grid>
                  </Grid>
                </RadioGroup>
              </CardContent>
            </Card>
          </Box>
        )}
        
        {/* Language Settings */}
        {/* {activeTab === 1 && (
          <Box>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <LanguageIcon sx={{ mr: 1 }} color="primary" />
              Language Settings
            </Typography>

            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                  Select your preferred language
                </Typography>

                <FormControl fullWidth margin="normal">
                  <InputLabel id="language-preference-label">Display Language</InputLabel>
                  <Select
                    labelId="language-preference-label"
                    name="language_preference"
                    value={settings.language_preference}
                    onChange={handleSelectChange}
                    label="Display Language"
                  >
                    <MenuItem value="en">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box component="img" src="/flags/en.svg" alt="English" sx={{ width: 24, mr: 1 }} />
                        English
                      </Box>
                    </MenuItem>
                    <MenuItem value="es">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box component="img" src="/flags/es.svg" alt="Spanish" sx={{ width: 24, mr: 1 }} />
                        Español
                      </Box>
                    </MenuItem>
                    <MenuItem value="fr">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box component="img" src="/flags/fr.svg" alt="French" sx={{ width: 24, mr: 1 }} />
                        Français
                      </Box>
                    </MenuItem>
                    <MenuItem value="de">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box component="img" src="/flags/de.svg" alt="German" sx={{ width: 24, mr: 1 }} />
                        Deutsch
                      </Box>
                    </MenuItem>
                    <MenuItem value="ar">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box component="img" src="/flags/ar.svg" alt="Arabic" sx={{ width: 24, mr: 1 }} />
                        العربية
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>

                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  The application will use this language for all text and labels. Some content may remain in English.
                </Typography>
              </CardContent>
            </Card>
          </Box>
        )} */}
        
        {/* Notifications Settings */}
        {activeTab === 1 && (
          <Box>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <NotificationsIcon sx={{ mr: 1 }} color="primary" />
              Notification Preferences
            </Typography>
            
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="subtitle1" fontWeight="medium">
                    All Notifications
                  </Typography>
                  <Switch
                    checked={settings.notifications_enabled}
                    onChange={handleSwitchChange}
                    name="notifications_enabled"
                    color="primary"
                    icon={<NotificationsOffIcon />}
                    checkedIcon={<NotificationsActiveIcon />}
                  />
                </Box>
                
                <Divider sx={{ my: 2 }} />
                
                <Typography variant="subtitle2" gutterBottom>
                  Notification Channels
                </Typography>
                
                <List disablePadding>
                  <ListItem disablePadding sx={{ py: 1 }}>
                    <ListItemIcon>
                      <EmailIcon color="action" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Email Notifications" 
                      secondary="Receive updates via email"
                      primaryTypographyProps={{ fontWeight: 'medium' }}
                    />
                    <Switch
                      edge="end"
                      checked={settings.email_notifications}
                      onChange={handleSwitchChange}
                      name="email_notifications"
                      color="primary"
                      disabled={!settings.notifications_enabled}
                    />
                  </ListItem>
                  
                  <ListItem disablePadding sx={{ py: 1 }}>
                    <ListItemIcon>
                      <PhoneAndroidIcon color="action" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Mobile Notifications" 
                      secondary="Receive push notifications on mobile devices"
                      primaryTypographyProps={{ fontWeight: 'medium' }}
                    />
                    <Switch
                      edge="end"
                      checked={settings.mobile_notifications}
                      onChange={handleSwitchChange}
                      name="mobile_notifications"
                      color="primary"
                      disabled={!settings.notifications_enabled}
                    />
                  </ListItem>
                </List>
                
                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  You'll receive notifications about new features, updates, and activity related to your account.
                </Typography>
              </CardContent>
            </Card>
          </Box>
        )}
        
        {/* Privacy & Security Settings */}
        {activeTab === 2 && (
          <Box>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <SecurityIcon sx={{ mr: 1 }} color="primary" />
              Privacy & Security Options
            </Typography>
            
            <Typography variant="body1" sx={{ mb: 2 }}>
              Enjoy more peace of mind with data security
            </Typography>
            <Typography variant="body2" color="text.secondary">
              At DataStatPro, data privacy is our top priority.
              By default, all data processing occurs directly on your device, ensuring that no data is sent to the cloud. Calculations are performed within your browser, and all results remain securely on your device.
            </Typography>
            <Typography variant="body2" color="text.secondary">
              
For registered users, we offer the option to save up to two datasets on our server. This allows you to access your saved data from any device after a successful login. Rest assured, your data is handled with the utmost care and security.
            </Typography>
          </Box>
        )}
        
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            onClick={saveSettings}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Save Settings'}
          </Button>
        </Box>
      </Paper>

      {/* Render the imported Password Change Modal */}
      <PasswordChangeModal 
        open={passwordModalOpen} 
        onClose={handleClosePasswordModal} 
      />

    </Box>
  );
};

export default Settings;
