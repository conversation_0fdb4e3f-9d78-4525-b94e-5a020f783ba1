import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Avatar,
  Grid,
  Alert,
  CircularProgress,
  Divider,

  Card,
  CardContent,

  MenuItem,
  Select,
  FormControl,
  InputLabel,
  FormHelperText,
  Chip, // Import Chip for the badge
  Tabs,
  Tab,
  Container
} from '@mui/material';
import { useAuth } from '../../context/AuthContext';
import { Person as PersonIcon, Save as SaveIcon, UploadFile as UploadFileIcon, CreditCard as CreditCardIcon } from '@mui/icons-material';
import { countries } from '../../data/countries';
import BillingTab from '../Settings/BillingTab';

const UserProfile: React.FC = () => {
  const { user, userProfile, signOut, updateProfile: updateProfileFromContext, uploadAvatar, accountType, refreshProfile } = useAuth(); // Destructure userProfile and refreshProfile
  
  const [username, setUsername] = useState('');
  const [fullName, setFullName] = useState('');
  const [institution, setInstitution] = useState('');
  const [country, setCountry] = useState('');
  const [loading, setLoading] = useState(false);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Load user profile data
  // Load profile data from AuthContext when it becomes available
  useEffect(() => {
    if (userProfile) {
      console.log('📋 Loading profile data from AuthContext:', userProfile);
      setUsername(userProfile.username || '');
      setAvatarUrl(userProfile.avatar_url || null);
      setFullName(userProfile.full_name || '');
      setInstitution(userProfile.institution || '');
      setCountry(userProfile.country || '');
      setLoading(false);
    } else if (user && !userProfile) {
      // If user exists but no profile data, trigger a refresh
      console.log('🔄 User exists but no profile data, refreshing...');
      setLoading(true);
      refreshProfile().finally(() => setLoading(false));
    }
  }, [user, userProfile, refreshProfile]);

  const handleUpdateProfile = async () => {
    try {
      setLoading(true);
      setMessage(null);

      const profileData = {
        username,
        full_name: fullName,
        institution,
        country,
        // avatar_url is handled by handleAvatarUpload
      };

      const { error } = await updateProfileFromContext(profileData);

      if (error) throw error;
      setMessage({ type: 'success', text: 'Profile updated successfully!' });
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message });
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
      // Optionally, create a preview URL for the selected image
      setAvatarUrl(URL.createObjectURL(event.target.files[0]));
    }
  };

  const handleAvatarUpload = async () => {
    if (!selectedFile) {
      setMessage({ type: 'error', text: 'Please select an image file first.' });
      return;
    }
    setUploadingAvatar(true);
    setMessage(null);
    try {
      const { error, publicUrl } = await uploadAvatar(selectedFile);
      if (error) throw error;
      if (publicUrl) {
        setAvatarUrl(publicUrl); // Update displayed avatar
        setMessage({ type: 'success', text: 'Avatar uploaded successfully!' });
      }
    } catch (error: any) {
      setMessage({ type: 'error', text: `Avatar upload failed: ${error.message}` });
    } finally {
      setUploadingAvatar(false);
      setSelectedFile(null); // Clear selected file
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error: any) {
      console.error('Error signing out:', error.message);
    }
  };

  if (!user) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography>Please sign in to view your profile.</Typography>
      </Box>
    );
  }

  const renderProfileTab = () => (
    <Box>
      <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
        {message && (
          <Alert severity={message.type} sx={{ mb: 3 }}>
            {message.text}
          </Alert>
        )}

        <Grid container spacing={3}>
          <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <Avatar
              sx={{ width: 100, height: 100, mb: 2 }}
              src={avatarUrl || undefined}
            >
              <PersonIcon fontSize="large" />
            </Avatar>
            <input
              accept="image/*"
              style={{ display: 'none' }}
              id="avatar-upload-input"
              type="file"
              onChange={handleFileSelect}
              disabled={uploadingAvatar}
            />
            <label htmlFor="avatar-upload-input">
              <Button 
                variant="outlined" 
                component="span" 
                size="small" 
                sx={{ mt: 1 }}
                startIcon={<UploadFileIcon />}
                disabled={uploadingAvatar}
              >
                Change Avatar
              </Button>
            </label>
            {selectedFile && (
              <Button 
                variant="contained" 
                size="small" 
                sx={{ mt: 1, ml: 1 }}
                onClick={handleAvatarUpload}
                disabled={uploadingAvatar || loading}
              >
                {uploadingAvatar ? <CircularProgress size={20} /> : 'Upload'}
              </Button>
            )}
            <Typography variant="subtitle1" gutterBottom sx={{mt: 2}}>
              {user.email}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Member since: {new Date(user.created_at || '').toLocaleDateString()}
            </Typography>
            {accountType && (
              <Chip
                label={accountType === 'standard' ? 'Standard' :
                       accountType === 'pro' ? 'Pro' :
                       accountType === 'edu' ? 'Educational' :
                       'Standard'}
                color={accountType === 'pro' ? 'primary' : (accountType === 'edu' ? 'secondary' : 'default')}
                variant="outlined"
                size="small"
                sx={{
                  mt: 1,
                  fontWeight: 'bold',
                  borderRadius: '12px',
                  '& .MuiChip-label': {
                    px: 1.5
                  }
                }}
              />
            )}
          </Grid>
          
          <Grid item xs={12} md={8}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Account Information
                </Typography>
                <TextField
                  label="Username"
                  fullWidth
                  margin="normal"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  disabled={loading || uploadingAvatar}
                />
                <TextField
                  label="Full Name"
                  fullWidth
                  margin="normal"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  disabled={loading || uploadingAvatar}
                />
                <TextField
                  label="Email"
                  fullWidth
                  margin="normal"
                  value={user.email}
                  disabled
                  helperText="Email cannot be changed"
                />

                <TextField
                  label="Institution"
                  fullWidth
                  margin="normal"
                  value={institution}
                  onChange={(e) => setInstitution(e.target.value)}
                  disabled={loading || uploadingAvatar}
                />
                <FormControl fullWidth margin="normal">
                  <InputLabel id="country-select-label">Country</InputLabel>
                  <Select
                    labelId="country-select-label"
                    id="country-select"
                    value={country}
                    label="Country"
                    onChange={(e) => setCountry(e.target.value)}
                    disabled={loading || uploadingAvatar}
                  >
                    <MenuItem value=""><em>Select a country</em></MenuItem>
                    {countries.map((countryName) => (
                      <MenuItem key={countryName} value={countryName}>{countryName}</MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>Select your country from the list</FormHelperText>
                </FormControl>
                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    onClick={handleUpdateProfile}
                    disabled={loading || uploadingAvatar}
                  >
                    {loading ? <CircularProgress size={24} /> : 'Save Profile Changes'}
                  </Button>
                </Box>
              </CardContent>
            </Card>
            
            <Box sx={{ mt: 4 }}>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="subtitle2" color="textSecondary">
                  Need to leave?
                </Typography>
                <Button
                  variant="outlined"
                  color="error"
                  onClick={handleSignOut}
                >
                  Sign Out
                </Button>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      <Typography variant="h4" gutterBottom>
        Account Settings
      </Typography>

      <Paper sx={{ mt: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<PersonIcon />}
            label="Profile"
            iconPosition="start"
          />
          <Tab
            icon={<CreditCardIcon />}
            label="Billing"
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {tabValue === 0 && renderProfileTab()}
          {tabValue === 1 && <BillingTab />}
        </Box>
      </Paper>
    </Container>
  );
};

export default UserProfile;
