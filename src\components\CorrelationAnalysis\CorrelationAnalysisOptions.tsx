import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import {
  CompareArrows as CorrelationIcon,
  TrendingUp as LinearRegressionIcon,
  Functions as LogisticRegressionIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';

interface CorrelationAnalysisOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Correlation' | 'Regression';
  color: string;
}

interface CorrelationAnalysisOptionsProps {
  onNavigate: (path: string) => void;
}

export const correlationAnalysisOptions: CorrelationAnalysisOption[] = [
  {
    name: 'Correlation Matrix',
    shortDescription: 'Calculate and visualize correlations between variables',
    detailedDescription: 'Generate a correlation matrix to explore the relationships between multiple variables. Includes options for <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> correlations, as well as significance testing and visualization.',
    path: 'correlation/pearson', // Updated path
    icon: <CorrelationIcon />,
    category: 'Correlation',
    color: '#FF9800', // Orange
  },
  {
    name: 'Linear Regression',
    shortDescription: 'Model the linear relationship between variables',
    detailedDescription: 'Perform simple and multiple linear regression to model the relationship between a dependent variable and one or more independent variables. Includes assumption checks, model diagnostics, and interpretation of results.',
    path: 'correlation/linear', // Updated path
    icon: <LinearRegressionIcon />,
    category: 'Regression',
    color: '#4CAF50', // Green
  },
  {
    name: 'Logistic Regression',
    shortDescription: 'Model the relationship with a binary outcome',
    detailedDescription: 'Conduct logistic regression analysis to model the probability of a binary outcome based on one or more predictor variables. Includes odds ratios, confidence intervals, and model fit statistics.',
    path: 'correlation/logistic', // Updated path
    icon: <LogisticRegressionIcon />,
    category: 'Regression',
    color: '#2196F3', // Blue
  },
];

const CorrelationAnalysisOptions: React.FC<CorrelationAnalysisOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const categories = ['All', 'Correlation', 'Regression'];

  const filteredOptions = selectedCategory === 'All'
    ? correlationAnalysisOptions
    : correlationAnalysisOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Correlation': return <CorrelationIcon />;
      case 'Regression': return <LinearRegressionIcon />;
      default: return <CorrelationIcon />;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Correlation Analysis Tools
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Explore relationships and build predictive models
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Utilize a range of tools to understand the associations between your variables
          and build linear or logistic regression models.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography variant="h6" fontWeight="bold">
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {option.shortDescription}
                </Typography>

                <Typography variant="body2" paragraph>
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Exploring relationships?</strong> Start with Correlation Matrix
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Predicting a continuous outcome?</strong> Use Linear Regression
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • <strong>Predicting a binary outcome?</strong> Use Logistic Regression
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default CorrelationAnalysisOptions;
