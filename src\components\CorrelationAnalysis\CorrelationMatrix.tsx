import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Tooltip,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Chip,
  Divider,
  alpha
} from '@mui/material';
import {
  GridOn as GridOnIcon,
  ColorLens as ColorLensIcon,
  Info as InfoIcon,
  People as PeopleIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType } from '../../types';
import { calculatePearsonCorrelation } from '@/utils/stats';
import { extractNumericValues } from '../../utils/typeConversions';
import StatsCard from '../UI/StatsCard';

// Enhanced components for heatmap visualization
const HeatmapCell: React.FC<{
  value: number;
  pValue: number;
  significant: boolean;
  colorScale: (value: number) => string;
  sampleSize: number;
  var1Name: string;
  var2Name: string;
}> = ({ value, pValue, significant, colorScale, sampleSize, var1Name, var2Name }) => {
  const theme = useTheme();
  const formattedValue = value.toFixed(3);
  const formattedPValue = pValue < 0.001 ? '< 0.001' : pValue.toFixed(3);

  // Determine text color based on background brightness
  const backgroundColor = colorScale(value);
  const textColor = Math.abs(value) > 0.6 ? 'white' : theme.palette.text.primary;

  // Get correlation strength description
  const getCorrelationStrength = (r: number) => {
    const absR = Math.abs(r);
    if (absR < 0.1) return 'Negligible';
    if (absR < 0.3) return 'Weak';
    if (absR < 0.5) return 'Moderate';
    if (absR < 0.7) return 'Strong';
    return 'Very Strong';
  };

  const tooltipContent = (
    <Box>
      <Typography variant="subtitle2" gutterBottom>
        {var1Name} × {var2Name}
      </Typography>
      <Divider sx={{ my: 1 }} />
      <Typography variant="body2">
        <strong>Correlation:</strong> {formattedValue}
      </Typography>
      <Typography variant="body2">
        <strong>Strength:</strong> {getCorrelationStrength(value)} {value < 0 ? 'Negative' : 'Positive'}
      </Typography>
      <Typography variant="body2">
        <strong>p-value:</strong> {formattedPValue}
      </Typography>
      <Typography variant="body2">
        <strong>Sample Size:</strong> n = {sampleSize}
      </Typography>
      <Typography variant="body2">
        <strong>Significance:</strong> {significant ? 'Significant' : 'Not Significant'}
      </Typography>
    </Box>
  );

  return (
    <Tooltip title={tooltipContent} arrow placement="top">
      <Box
        sx={{
          backgroundColor,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          p: 1,
          height: '100%',
          width: '100%',
          color: textColor,
          position: 'relative',
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          borderRadius: 1,
          '&:hover': {
            transform: 'scale(1.05)',
            boxShadow: theme.shadows[4],
            zIndex: 10
          }
        }}
      >
        <Typography
          variant="body2"
          fontWeight={significant ? 'bold' : 'normal'}
          sx={{ fontSize: '0.875rem' }}
        >
          {formattedValue}
        </Typography>
        {significant && (
          <Typography
            variant="caption"
            sx={{
              fontSize: '0.6rem',
              color: textColor,
              opacity: 0.9
            }}
          >
            {pValue < 0.001 ? '***' : pValue < 0.01 ? '**' : pValue < 0.05 ? '*' : ''}
          </Typography>
        )}
      </Box>
    </Tooltip>
  );
};

type CorrelationType = 'pearson' | 'spearman';

// Component for correlation matrix analysis
const CorrelationMatrix: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();
  
  // State for correlation options
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [selectedVariables, setSelectedVariables] = useState<string[]>([]);
  const [correlationType, setCorrelationType] = useState<CorrelationType>('pearson');
  const [displayOptions, setDisplayOptions] = useState({
    showPValues: false,
    showSignificanceStars: true,
    highlightSignificant: true,
    showHistograms: false
  });
  
  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [correlationResults, setCorrelationResults] = useState<any | null>(null);
  
  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('correlation_matrix_results');
    
    if (savedResults) {
      try {
        setCorrelationResults(JSON.parse(savedResults));
      } catch (e) {
        console.error('Error parsing saved correlation matrix results:', e);
      }
    }
  }, []);
  
  // Get numeric columns from current dataset
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedVariables([]);
    setCorrelationResults(null);
    
    // Clear saved results from localStorage
    localStorage.removeItem('correlation_matrix_results');
    
    // Update the current dataset in the DataContext
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };
  
  // Handle variable selection change
  const handleVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedVariables(typeof value === 'string' ? [value] : value);
    
    // Clear results when variables change
    setCorrelationResults(null);
    localStorage.removeItem('correlation_matrix_results');
  };
  
  // Handle correlation type change
  const handleCorrelationTypeChange = (event: SelectChangeEvent<CorrelationType>) => {
    setCorrelationType(event.target.value as CorrelationType);
    
    // Clear results when correlation type changes
    setCorrelationResults(null);
    localStorage.removeItem('correlation_matrix_results');
  };
  
  // Handle display options change
  const handleDisplayOptionChange = (option: keyof typeof displayOptions) => {
    setDisplayOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };
  
  // Enhanced color scale function with muted colors
  const getColorScale = (value: number) => {
    // Use more muted, professional colors that work well in both light and dark themes
    const isDark = theme.palette.mode === 'dark';

    // Positive correlations (blues/teals - muted)
    if (value === 1) return isDark ? '#1565c0' : '#0d47a1'; // Strong positive
    if (value > 0.7) return isDark ? '#1976d2' : '#1565c0'; // Strong positive
    if (value > 0.5) return isDark ? '#42a5f5' : '#1976d2'; // Moderate positive
    if (value > 0.3) return isDark ? '#64b5f6' : '#42a5f5'; // Weak-moderate positive
    if (value > 0.1) return isDark ? '#90caf9' : '#64b5f6'; // Weak positive

    // Near-zero correlations (neutral grays)
    if (value > -0.1) return isDark ? '#424242' : '#f5f5f5';

    // Negative correlations (muted oranges/reds)
    if (value > -0.3) return isDark ? '#ff8a65' : '#ffab91'; // Weak negative
    if (value > -0.5) return isDark ? '#ff7043' : '#ff8a65'; // Weak-moderate negative
    if (value > -0.7) return isDark ? '#ff5722' : '#ff7043'; // Moderate negative
    if (value > -1) return isDark ? '#e64a19' : '#ff5722'; // Strong negative
    return isDark ? '#d84315' : '#e64a19'; // Perfect negative
  };


  
  // Run correlation analysis
  const runCorrelationAnalysis = () => {
    if (!currentDataset || selectedVariables.length < 2) {
      setError('Please select at least 2 variables for correlation analysis.');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Get selected columns
      const selectedColumns = selectedVariables.map(varId => {
        const column = currentDataset.columns.find(col => col.id === varId);
        if (!column) throw new Error(`Column with ID ${varId} not found.`);
        return column;
      });
      
      // Prepare variables data
      const variablesData: Record<string, number[]> = {};
      
      selectedColumns.forEach(column => {
        const allValues = currentDataset.data.map(row => row[column.name]);
        const values = extractNumericValues(allValues);

        variablesData[column.id] = values;
      });
      
      // Calculate correlation matrix
      const matrix: Record<string, Record<string, any>> = {};
      
      selectedColumns.forEach(col1 => {
        matrix[col1.id] = {};
        
        selectedColumns.forEach(col2 => {
          // For the diagonal (same variable), correlation is 1
          if (col1.id === col2.id) {
            matrix[col1.id][col2.id] = {
              correlation: 1,
              pValue: 1,
              n: variablesData[col1.id].length
            };
            return;
          }
          
          // Get data for both variables with pairwise deletion of missing values
          const pairs: {x: number, y: number}[] = [];
          
          currentDataset.data.forEach(row => {
            const val1 = row[col1.name];
            const val2 = row[col2.name];
            
            if (typeof val1 === 'number' && !isNaN(val1) && 
                typeof val2 === 'number' && !isNaN(val2)) {
              pairs.push({ x: val1, y: val2 });
            }
          });
          
          if (pairs.length < 3) {
            matrix[col1.id][col2.id] = {
              correlation: null,
              pValue: null,
              n: pairs.length,
              error: 'Not enough valid paired data'
            };
            return;
          }
          
          // Calculate correlation based on selected type
          if (correlationType === 'pearson') {
            // Pearson correlation
            try {
              const result = calculatePearsonCorrelation(
                pairs.map(p => p.x),
                pairs.map(p => p.y)
              );
              
              matrix[col1.id][col2.id] = {
                correlation: result.r,
                pValue: result.pValue,
                n: result.n,
                rSquared: result.rSquared
              };
            } catch (e) {
              matrix[col1.id][col2.id] = {
                correlation: null,
                pValue: null,
                n: pairs.length,
                error: e instanceof Error ? e.message : String(e)
              };
            }
          } else if (correlationType === 'spearman') {
            // Spearman rank correlation
            try {
              // Convert values to ranks
              const n = pairs.length;
              
              // Sort x values for ranking
              const xSorted = [...pairs].sort((a, b) => a.x - b.x);
              // Assign ranks to x values
              const xRanks: Record<number, number> = {};
              xSorted.forEach((_, index) => {
                // Handle ties by assigning average rank
                let j = index;
                while (j < n - 1 && xSorted[j].x === xSorted[j + 1].x) {
                  j++;
                }
                const avgRank = (index + j) / 2 + 1;
                for (let k = index; k <= j; k++) {
                  xRanks[xSorted[k].x] = avgRank;
                }
                index = j;
              });
              
              // Sort y values for ranking
              const ySorted = [...pairs].sort((a, b) => a.y - b.y);
              // Assign ranks to y values
              const yRanks: Record<number, number> = {};
              ySorted.forEach((_, index) => {
                // Handle ties by assigning average rank
                let j = index;
                while (j < n - 1 && ySorted[j].y === ySorted[j + 1].y) {
                  j++;
                }
                const avgRank = (index + j) / 2 + 1;
                for (let k = index; k <= j; k++) {
                  yRanks[ySorted[k].y] = avgRank;
                }
                index = j;
              });
              
              // Calculate Spearman's rho using Pearson correlation on ranks
              const xRankValues = pairs.map(p => xRanks[p.x]);
              const yRankValues = pairs.map(p => yRanks[p.y]);
              
              const pearsonResult = calculatePearsonCorrelation(xRankValues, yRankValues);
              
              matrix[col1.id][col2.id] = {
                correlation: pearsonResult.r,
                pValue: pearsonResult.pValue,
                n: pearsonResult.n,
                rSquared: pearsonResult.rSquared
              };
            } catch (e) {
              matrix[col1.id][col2.id] = {
                correlation: null,
                pValue: null,
                n: pairs.length,
                error: e instanceof Error ? e.message : String(e)
              };
            }
          }
        });
      });
      
      // Prepare scatter plot data for visualizations
      const scatterPlots: Record<string, Record<string, any[]>> = {};
      
      selectedColumns.forEach(col1 => {
        scatterPlots[col1.id] = {};
        
        selectedColumns.forEach(col2 => {
          if (col1.id === col2.id) {
            // For diagonal, create histogram data
            const values = variablesData[col1.id];
            const min = Math.min(...values);
            const max = Math.max(...values);
            const range = max - min;
            const binCount = Math.min(20, Math.max(5, Math.ceil(Math.sqrt(values.length))));
            const binWidth = range / binCount;
            
            const histogramData: any[] = [];
            
            for (let i = 0; i < binCount; i++) {
              const binStart = min + i * binWidth;
              const binEnd = min + (i + 1) * binWidth;
              const count = values.filter(v => v >= binStart && (i === binCount - 1 ? v <= binEnd : v < binEnd)).length;
              
              histogramData.push({
                bin: `${binStart.toFixed(1)}-${binEnd.toFixed(1)}`,
                binMiddle: (binStart + binEnd) / 2,
                count,
                frequency: count / values.length
              });
            }
            
            scatterPlots[col1.id][col2.id] = histogramData;
          } else {
            // For non-diagonal, create scatter plot data
            const scatterData = currentDataset.data
              .map(row => ({
                x: row[col1.name],
                y: row[col2.name]
              }))
              .filter(point => 
                typeof point.x === 'number' && !isNaN(point.x) && 
                typeof point.y === 'number' && !isNaN(point.y)
              );
            
            scatterPlots[col1.id][col2.id] = scatterData;
          }
        });
      });
      
      // Set results
      const results = {
        columns: selectedColumns,
        matrix,
        scatterPlots
      };
      
      setCorrelationResults(results);
      
      // Save results to localStorage for persistence
      localStorage.setItem('correlation_matrix_results', JSON.stringify(results));
      
      setLoading(false);
    } catch (err) {
      setError(`Error in correlation analysis: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  };
  
  // Enhanced interpretation with comprehensive statistical guidance
  const getInterpretation = () => {
    if (!correlationResults) return '';

    const significantCorrelations: {
      var1: string;
      var2: string;
      r: number;
      pValue: number;
      n: number;
    }[] = [];

    const allCorrelations: {
      var1: string;
      var2: string;
      r: number;
      pValue: number;
      n: number;
    }[] = [];

    // Extract all correlations (excluding diagonal)
    correlationResults.columns.forEach((col1: any, i: number) => {
      correlationResults.columns.slice(i + 1).forEach((col2: any) => {
        const result = correlationResults.matrix[col1.id][col2.id];
        if (result && result.correlation !== null) {
          const corrData = {
            var1: col1.name,
            var2: col2.name,
            r: result.correlation,
            pValue: result.pValue,
            n: result.n
          };

          allCorrelations.push(corrData);

          if (result.pValue < 0.05) {
            significantCorrelations.push(corrData);
          }
        }
      });
    });

    let interpretation = '';

    // Overview section
    interpretation += `## Correlation Analysis Summary\n\n`;
    interpretation += `This analysis examined ${allCorrelations.length} pairwise correlations between ${correlationResults.columns.length} variables using ${correlationType === 'pearson' ? 'Pearson' : 'Spearman'} correlation coefficients.\n\n`;

    // Statistical significance results
    if (significantCorrelations.length === 0) {
      interpretation += `### Statistical Significance\n\n`;
      interpretation += `No statistically significant correlations were found at the α = 0.05 level. This could indicate:\n`;
      interpretation += `• True absence of linear relationships between variables\n`;
      interpretation += `• Insufficient sample size to detect existing relationships\n`;
      interpretation += `• Non-linear relationships that correlation analysis cannot capture\n`;
      interpretation += `• High measurement error reducing correlation strength\n\n`;
    } else {
      interpretation += `### Statistical Significance\n\n`;
      interpretation += `${significantCorrelations.length} of ${allCorrelations.length} correlations (${((significantCorrelations.length / allCorrelations.length) * 100).toFixed(1)}%) were statistically significant at p < 0.05.\n\n`;

      // Sort by absolute correlation strength
      const topCorrelations = significantCorrelations
        .sort((a, b) => Math.abs(b.r) - Math.abs(a.r))
        .slice(0, 5);

      interpretation += `**Strongest Significant Correlations:**\n\n`;
      topCorrelations.forEach((corr, index) => {
        const absR = Math.abs(corr.r);
        const strength = absR < 0.1 ? 'negligible' : absR < 0.3 ? 'weak' : absR < 0.5 ? 'moderate' : absR < 0.7 ? 'strong' : 'very strong';
        const direction = corr.r < 0 ? 'negative' : 'positive';
        const effectSize = absR < 0.1 ? 'no practical significance' : absR < 0.3 ? 'small effect' : absR < 0.5 ? 'medium effect' : absR < 0.7 ? 'large effect' : 'very large effect';

        interpretation += `${index + 1}. **${corr.var1} ↔ ${corr.var2}**\n`;
        interpretation += `   • Correlation: r = ${corr.r.toFixed(3)} (${direction} ${strength})\n`;
        interpretation += `   • Significance: p = ${corr.pValue < 0.001 ? '< 0.001' : corr.pValue.toFixed(3)}\n`;
        interpretation += `   • Effect Size: ${effectSize}\n`;
        interpretation += `   • Sample Size: n = ${corr.n}\n`;
        interpretation += `   • Shared Variance: ${(corr.r * corr.r * 100).toFixed(1)}% (r²)\n\n`;
      });

      if (significantCorrelations.length > 5) {
        interpretation += `*${significantCorrelations.length - 5} additional significant correlations found. See matrix for complete results.*\n\n`;
      }
    }

    // Correlation strength distribution
    const strengthCounts = {
      negligible: allCorrelations.filter(c => Math.abs(c.r) < 0.1).length,
      weak: allCorrelations.filter(c => Math.abs(c.r) >= 0.1 && Math.abs(c.r) < 0.3).length,
      moderate: allCorrelations.filter(c => Math.abs(c.r) >= 0.3 && Math.abs(c.r) < 0.5).length,
      strong: allCorrelations.filter(c => Math.abs(c.r) >= 0.5 && Math.abs(c.r) < 0.7).length,
      veryStrong: allCorrelations.filter(c => Math.abs(c.r) >= 0.7).length
    };

    interpretation += `### Correlation Strength Distribution\n\n`;
    interpretation += `• **Negligible** (|r| < 0.1): ${strengthCounts.negligible} correlations\n`;
    interpretation += `• **Weak** (|r| = 0.1-0.3): ${strengthCounts.weak} correlations\n`;
    interpretation += `• **Moderate** (|r| = 0.3-0.5): ${strengthCounts.moderate} correlations\n`;
    interpretation += `• **Strong** (|r| = 0.5-0.7): ${strengthCounts.strong} correlations\n`;
    interpretation += `• **Very Strong** (|r| ≥ 0.7): ${strengthCounts.veryStrong} correlations\n\n`;

    // Methodological notes
    interpretation += `### Methodological Notes\n\n`;

    if (correlationType === 'pearson') {
      interpretation += `**Pearson Correlation** was used, which:\n`;
      interpretation += `• Measures linear relationships between continuous variables\n`;
      interpretation += `• Assumes variables are normally distributed\n`;
      interpretation += `• Is sensitive to outliers and extreme values\n`;
      interpretation += `• Ranges from -1 (perfect negative) to +1 (perfect positive)\n\n`;
    } else {
      interpretation += `**Spearman Correlation** was used, which:\n`;
      interpretation += `• Measures monotonic relationships (not necessarily linear)\n`;
      interpretation += `• Does not assume normal distribution\n`;
      interpretation += `• Is robust to outliers and extreme values\n`;
      interpretation += `• Based on rank ordering of values\n\n`;
    }

    // Interpretation guidelines
    interpretation += `### Interpretation Guidelines\n\n`;
    interpretation += `**Statistical vs. Practical Significance:**\n`;
    interpretation += `• Statistical significance (p < 0.05) indicates the correlation is unlikely due to chance\n`;
    interpretation += `• Practical significance depends on effect size (correlation strength)\n`;
    interpretation += `• Large samples can produce statistically significant but practically meaningless correlations\n\n`;

    interpretation += `**Effect Size Interpretation (Cohen's Guidelines):**\n`;
    interpretation += `• Small effect: |r| ≈ 0.1 (1% shared variance)\n`;
    interpretation += `• Medium effect: |r| ≈ 0.3 (9% shared variance)\n`;
    interpretation += `• Large effect: |r| ≈ 0.5 (25% shared variance)\n\n`;

    interpretation += `**Important Considerations:**\n`;
    interpretation += `• Correlation does not imply causation\n`;
    interpretation += `• Missing data may affect correlation strength\n`;
    interpretation += `• Non-linear relationships may not be detected\n`;
    interpretation += `• Multiple comparisons increase Type I error risk\n`;

    return interpretation;
  };
  
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Correlation Analysis
      </Typography>
      
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Variables
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="correlation-type-label">Correlation Type</InputLabel>
              <Select
                labelId="correlation-type-label"
                id="correlation-type"
                value={correlationType}
                label="Correlation Type"
                onChange={handleCorrelationTypeChange}
              >
                <MenuItem value="pearson">
                  Pearson Correlation
                  <Typography variant="caption" display="block" color="text.secondary">
                    Measures linear relationship between variables
                  </Typography>
                </MenuItem>
                <MenuItem value="spearman">
                  Spearman Rank Correlation
                  <Typography variant="caption" display="block" color="text.secondary">
                    Non-parametric alternative, robust to outliers
                  </Typography>
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="variables-label">Variables to Correlate</InputLabel>
              <Select
                labelId="variables-label"
                id="variables"
                multiple
                value={selectedVariables}
                label="Variables to Correlate"
                onChange={handleVariableChange}
                disabled={!currentDataset}
                renderValue={(selected) => {
                  const selectedNames = selected
                    .map(id => numericColumns.find(col => col.id === id)?.name || id)
                    .join(', ');
                  return selectedNames;
                }}
              >
                {numericColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No numeric variables available
                  </MenuItem>
                ) : (
                  numericColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
            <Typography variant="caption" color="text.secondary">
              Select at least 2 numeric variables to calculate correlations.
            </Typography>
          </Grid>
        </Grid>
        
        <Box mt={2}>
          <Typography variant="subtitle2" gutterBottom>
            Display Options
          </Typography>
          
          <FormGroup row>
            <FormControlLabel
              control={
                <Checkbox
                  checked={displayOptions.showPValues}
                  onChange={() => handleDisplayOptionChange('showPValues')}
                />
              }
              label="Show p-values"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={displayOptions.showSignificanceStars}
                  onChange={() => handleDisplayOptionChange('showSignificanceStars')}
                />
              }
              label="Show significance stars"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={displayOptions.highlightSignificant}
                  onChange={() => handleDisplayOptionChange('highlightSignificant')}
                />
              }
              label="Highlight significant correlations"
            />
          </FormGroup>
        </Box>
        
        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<GridOnIcon />}
            onClick={runCorrelationAnalysis}
            disabled={loading || selectedVariables.length < 2}
          >
            Generate Correlation Matrix
          </Button>
        </Box>
      </Paper>
      
      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {correlationResults && !loading && (
        <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              {correlationType === 'pearson' ? 'Pearson' : 'Spearman'} Correlation Matrix
            </Typography>
            <Chip
              icon={<PeopleIcon />}
              label={`Total Variables: ${correlationResults.columns.length}`}
              color="primary"
              variant="outlined"
            />
          </Box>

          {/* Sample Size Information */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <InfoIcon fontSize="small" />
              Sample Size Information
            </Typography>
            <Grid container spacing={2}>
              {(() => {
                // Calculate overall sample size statistics
                const sampleSizes: number[] = [];
                correlationResults.columns.forEach((col1: any) => {
                  correlationResults.columns.forEach((col2: any) => {
                    const result = correlationResults.matrix[col1.id][col2.id];
                    if (result && result.n && col1.id !== col2.id) {
                      sampleSizes.push(result.n);
                    }
                  });
                });

                const totalN = Math.max(...sampleSizes);
                const minN = Math.min(...sampleSizes);
                const avgN = Math.round(sampleSizes.reduce((a, b) => a + b, 0) / sampleSizes.length);
                const hasVariableN = minN !== totalN;

                return (
                  <>
                    <Grid item xs={12} sm={6} md={3}>
                      <StatsCard
                        title="Maximum N"
                        value={totalN.toLocaleString()}
                        description="Largest pairwise sample size"
                        color="primary"
                        variant="outlined"
                        icon={<PeopleIcon />}
                      />
                    </Grid>
                    {hasVariableN && (
                      <>
                        <Grid item xs={12} sm={6} md={3}>
                          <StatsCard
                            title="Minimum N"
                            value={minN.toLocaleString()}
                            description="Smallest pairwise sample size"
                            color="warning"
                            variant="outlined"
                            icon={<PeopleIcon />}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <StatsCard
                            title="Average N"
                            value={avgN.toLocaleString()}
                            description="Mean pairwise sample size"
                            color="info"
                            variant="outlined"
                            icon={<PeopleIcon />}
                          />
                        </Grid>
                      </>
                    )}
                    <Grid item xs={12} sm={6} md={3}>
                      <StatsCard
                        title="Missing Data"
                        value={hasVariableN ? "Variable" : "None"}
                        description={hasVariableN ? "Sample sizes vary by pair" : "Consistent across all pairs"}
                        color={hasVariableN ? "warning" : "success"}
                        variant="outlined"
                        icon={<InfoIcon />}
                      />
                    </Grid>
                  </>
                );
              })()}
            </Grid>
          </Box>
          
          <Box sx={{ overflowX: 'auto', mb: 3 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Hover over cells for detailed information. Darker colors indicate stronger correlations.
            </Typography>
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: `minmax(140px, auto) repeat(${correlationResults.columns.length}, minmax(110px, 1fr))`,
                gap: 1,
                my: 2,
                p: 1,
                backgroundColor: alpha(theme.palette.background.default, 0.3),
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`
              }}
              role="table"
              aria-label="Correlation matrix"
            >
              {/* Header row */}
              <Box
                sx={{
                  fontWeight: 'bold',
                  p: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                role="columnheader"
              >
                <Typography variant="body2" fontWeight="bold" color="text.secondary">
                  Variables
                </Typography>
              </Box>
              {correlationResults.columns.map((column: any) => (
                <Box
                  key={column.id}
                  sx={{
                    fontWeight: 'bold',
                    p: 1.5,
                    textAlign: 'center',
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    borderRadius: 1,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                    minHeight: 48,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                  role="columnheader"
                  aria-label={`Column for ${column.name}`}
                >
                  <Typography variant="body2" fontWeight="bold" noWrap>
                    {column.name}
                  </Typography>
                </Box>
              ))}
              
              {/* Matrix rows */}
              {correlationResults.columns.map((rowColumn: any) => (
                <React.Fragment key={`row-${rowColumn.id}`}>
                  <Box
                    sx={{
                      fontWeight: 'bold',
                      p: 1.5,
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      borderRadius: 1,
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                      minHeight: 48,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'flex-start'
                    }}
                    role="rowheader"
                    aria-label={`Row for ${rowColumn.name}`}
                  >
                    <Typography variant="body2" fontWeight="bold" noWrap>
                      {rowColumn.name}
                    </Typography>
                  </Box>
                  
                  {correlationResults.columns.map((colColumn: any) => {
                    const cellResult = correlationResults.matrix[rowColumn.id][colColumn.id];
                    
                    if (cellResult.error) {
                      return (
                        <Box
                          key={`cell-${rowColumn.id}-${colColumn.id}`}
                          sx={{
                            p: 1.5,
                            backgroundColor: alpha(theme.palette.error.main, 0.1),
                            textAlign: 'center',
                            color: 'error.main',
                            fontSize: '0.8rem',
                            borderRadius: 1,
                            border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,
                            minHeight: 48,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                          role="cell"
                          aria-label={`No data available for ${rowColumn.name} and ${colColumn.name}`}
                        >
                          <Tooltip title={cellResult.error || 'Insufficient data for correlation calculation'} arrow>
                            <Typography variant="body2" fontWeight="medium">
                              N/A
                            </Typography>
                          </Tooltip>
                        </Box>
                      );
                    }
                    
                    const isSignificant = cellResult.pValue < 0.05;
                    
                    return (
                      <Box
                        key={`cell-${rowColumn.id}-${colColumn.id}`}
                        sx={{
                          aspectRatio: '1/1',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          minHeight: 48
                        }}
                        role="cell"
                        aria-label={`Correlation between ${rowColumn.name} and ${colColumn.name}: ${cellResult.correlation.toFixed(3)}, p-value: ${cellResult.pValue < 0.001 ? 'less than 0.001' : cellResult.pValue.toFixed(3)}`}
                      >
                        <HeatmapCell
                          value={cellResult.correlation}
                          pValue={cellResult.pValue}
                          significant={isSignificant}
                          colorScale={getColorScale}
                          sampleSize={cellResult.n}
                          var1Name={rowColumn.name}
                          var2Name={colColumn.name}
                        />
                      </Box>
                    );
                  })}
                </React.Fragment>
              ))}
            </Box>
          </Box>
          
          {/* Enhanced Color Scale Legend */}
          <Box mt={3}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ColorLensIcon fontSize="small" />
              Correlation Strength & Color Scale
            </Typography>

            {/* Color gradient bar */}
            <Box sx={{ mb: 2 }}>
              <Box
                sx={{
                  height: 40,
                  borderRadius: 2,
                  background: `linear-gradient(to right,
                    ${getColorScale(-1)},
                    ${getColorScale(-0.8)},
                    ${getColorScale(-0.6)},
                    ${getColorScale(-0.4)},
                    ${getColorScale(-0.2)},
                    ${getColorScale(0)},
                    ${getColorScale(0.2)},
                    ${getColorScale(0.4)},
                    ${getColorScale(0.6)},
                    ${getColorScale(0.8)},
                    ${getColorScale(1)})`,
                  border: `1px solid ${theme.palette.divider}`,
                  position: 'relative'
                }}
              />

              {/* Scale labels */}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  mt: 1,
                  px: 0.5
                }}
              >
                <Typography variant="caption" fontWeight="medium">-1.0</Typography>
                <Typography variant="caption" fontWeight="medium">-0.5</Typography>
                <Typography variant="caption" fontWeight="medium">0</Typography>
                <Typography variant="caption" fontWeight="medium">+0.5</Typography>
                <Typography variant="caption" fontWeight="medium">+1.0</Typography>
              </Box>

              {/* Strength indicators */}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  mt: 0.5,
                  px: 0.5
                }}
              >
                <Typography variant="caption" color="text.secondary">Perfect Negative</Typography>
                <Typography variant="caption" color="text.secondary">Moderate Negative</Typography>
                <Typography variant="caption" color="text.secondary">No Correlation</Typography>
                <Typography variant="caption" color="text.secondary">Moderate Positive</Typography>
                <Typography variant="caption" color="text.secondary">Perfect Positive</Typography>
              </Box>
            </Box>

            {/* Correlation strength guide */}
            <Grid container spacing={1} sx={{ mb: 2 }}>
              {[
                { range: '0.0 - 0.1', strength: 'Negligible', color: getColorScale(0.05) },
                { range: '0.1 - 0.3', strength: 'Weak', color: getColorScale(0.2) },
                { range: '0.3 - 0.5', strength: 'Moderate', color: getColorScale(0.4) },
                { range: '0.5 - 0.7', strength: 'Strong', color: getColorScale(0.6) },
                { range: '0.7 - 1.0', strength: 'Very Strong', color: getColorScale(0.85) }
              ].map((item, index) => (
                <Grid item xs={12} sm={6} md={2.4} key={index}>
                  <Box
                    sx={{
                      p: 1,
                      borderRadius: 1,
                      backgroundColor: alpha(item.color, 0.1),
                      border: `1px solid ${alpha(item.color, 0.3)}`,
                      textAlign: 'center'
                    }}
                  >
                    <Typography variant="caption" fontWeight="medium" display="block">
                      {item.strength}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      |r| = {item.range}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Box>

          {/* Significance Indicators */}
          <Box mt={2}>
            <Typography variant="subtitle2" gutterBottom>
              Statistical Significance
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Chip label="* p < 0.05" size="small" variant="outlined" />
              <Chip label="** p < 0.01" size="small" variant="outlined" />
              <Chip label="*** p < 0.001" size="small" variant="outlined" />
              <Chip label="Bold = Significant" size="small" color="primary" variant="outlined" />
            </Box>
          </Box>
          
          <Box mt={4}>
            <Typography variant="h6" gutterBottom>
              Correlation Table
            </Typography>
            
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Variable 1</TableCell>
                    <TableCell>Variable 2</TableCell>
                    <TableCell align="right">Correlation</TableCell>
                    <TableCell align="right">p-value</TableCell>
                    <TableCell align="right">N</TableCell>
                    {correlationType === 'pearson' && (
                      <TableCell align="right">R²</TableCell>
                    )}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {correlationResults.columns.map((col1: any, i: number) => 
                    correlationResults.columns.slice(i + 1).map((col2: any) => {
                      const result = correlationResults.matrix[col1.id][col2.id];
                      if (result.error) return null;
                      
                      const isSignificant = result.pValue < 0.05;
                      
                      return (
                        <TableRow 
                          key={`${col1.id}-${col2.id}`}
                          sx={{ 
                            backgroundColor: displayOptions.highlightSignificant && isSignificant 
                              ? `${theme.palette.success.main}20` 
                              : 'inherit'
                          }}
                        >
                          <TableCell>{col1.name}</TableCell>
                          <TableCell>{col2.name}</TableCell>
                          <TableCell 
                            align="right"
                            sx={{ fontWeight: isSignificant ? 'bold' : 'normal' }}
                          >
                            {result.correlation.toFixed(3)}
                            {displayOptions.showSignificanceStars && isSignificant && (
                              <Box component="span" ml={0.5}>
                                {result.pValue < 0.001 ? '***' : result.pValue < 0.01 ? '**' : '*'}
                              </Box>
                            )}
                          </TableCell>
                          <TableCell align="right">
                            {result.pValue < 0.001 ? '< 0.001' : result.pValue.toFixed(3)}
                          </TableCell>
                          <TableCell align="right">{result.n}</TableCell>
                          {correlationType === 'pearson' && (
                            <TableCell align="right">{result.rSquared.toFixed(3)}</TableCell>
                          )}
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
          
          <Box mt={4}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <InfoIcon />
              Statistical Interpretation & Guidance
            </Typography>

            <Paper
              elevation={0}
              variant="outlined"
              sx={{
                p: 3,
                bgcolor: alpha(theme.palette.background.paper, 0.8),
                borderRadius: 2
              }}
            >
              <Box sx={{
                '& h2': {
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  color: theme.palette.primary.main,
                  mt: 2,
                  mb: 1,
                  '&:first-of-type': { mt: 0 }
                },
                '& h3': {
                  fontSize: '1rem',
                  fontWeight: 600,
                  color: theme.palette.text.primary,
                  mt: 2,
                  mb: 1
                },
                '& p': {
                  mb: 1,
                  lineHeight: 1.6
                },
                '& ul': {
                  pl: 2,
                  mb: 1
                },
                '& li': {
                  mb: 0.5
                }
              }}>
                <Typography
                  variant="body2"
                  sx={{
                    whiteSpace: 'pre-line',
                    '& strong': { fontWeight: 600 }
                  }}
                  dangerouslySetInnerHTML={{
                    __html: getInterpretation()
                      .replace(/## (.*)/g, '<h2>$1</h2>')
                      .replace(/### (.*)/g, '<h3>$1</h3>')
                      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                      .replace(/• (.*)/g, '• $1')
                      .replace(/\n\n/g, '</p><p>')
                      .replace(/^\s*/, '<p>')
                      .replace(/\s*$/, '</p>')
                  }}
                />
              </Box>
            </Paper>
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default CorrelationMatrix;