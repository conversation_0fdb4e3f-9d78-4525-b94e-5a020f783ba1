import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  FormControlLabel,
  Checkbox,
  TextField,
  Chip,
  alpha
} from '@mui/material';
import {
  Timeline as TimelineIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  B<PERSON>ble<PERSON>hart as BubbleChartIcon,
  Search as SearchIcon,
  Info as InfoIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Calculate as CalculateIcon
} from '@mui/icons-material';
import StatsCard from '../UI/StatsCard';
import { useData } from '../../context/DataContext';
import { DataType, Column, VariableRole } from '../../types'; // Import Column and VariableRole type
import { multipleLinearRegression } from '@/utils/stats';
import { getOrderedCategoriesByColumnId } from '../../utils/dataUtilities';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Line,
  ReferenceLine,
  Cell
} from 'recharts';

// Component for simple linear regression analysis
const LinearRegression: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData(); // Added setCurrentDataset
  const theme = useTheme();
  
  // State for regression selection
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [independentVariables, setIndependentVariables] = useState<string[]>([]);
  const [dependentVariable, setDependentVariable] = useState<string>('');
  const [confInterval, setConfInterval] = useState<number>(0.95);
  const [selectedCategoricalBaseCategories, setSelectedCategoricalBaseCategories] = useState<{ [key: string]: string }>({});
  
  // State for prediction
  const [predictionInputValues, setPredictionInputValues] = useState<{ [key: string]: string }>({});
  const [prediction, setPrediction] = useState<any | null>(null);
  
  // State for display options
  const [displayOptions, setDisplayOptions] = useState({
    showConfidenceIntervals: true,
    showPredictionIntervals: false,
    showRegressionEquation: true,
    showRegressionLine: true,
    showResidualPlot: true
  });
  
  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [regressionResults, setRegressionResults] = useState<any | null>(null);
  
  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('linear_regression_results');
    if (savedResults) {
      try {
        const parsedResults = JSON.parse(savedResults);
        setRegressionResults(parsedResults);
      } catch (error) {
        console.error('Error parsing saved linear regression results:', error);
        localStorage.removeItem('linear_regression_results');
      }
    }
  }, []);
  
  // Get numeric and string (categorical) columns from current dataset for independent variables
  const availableIndependentColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC || col.type === DataType.CATEGORICAL 
  ) || [];

  // Get numeric columns for dependent variable (dependent variable must be numeric)
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setIndependentVariables([]);
    setDependentVariable('');
    setSelectedCategoricalBaseCategories({});
    setRegressionResults(null);
    setPrediction(null);
    
    // Clear saved results when dataset changes
    localStorage.removeItem('linear_regression_results');
    
    // Update the current dataset in the DataContext
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };
  
  // Handle independent variables selection change
  const handleIndependentVariablesChange = (event: SelectChangeEvent<typeof independentVariables>) => {
    const value = event.target.value;
    const newVars = typeof value === 'string' ? value.split(',') : value;
    setIndependentVariables(newVars);

    // Clear base category for deselected categorical variables
    const updatedBaseCategories = { ...selectedCategoricalBaseCategories };
    Object.keys(selectedCategoricalBaseCategories).forEach(varId => {
      if (!newVars.includes(varId)) {
        delete updatedBaseCategories[varId];
      }
    });
    setSelectedCategoricalBaseCategories(updatedBaseCategories);

    setRegressionResults(null);
    setPrediction(null);
    
    // Clear saved results when variables change
    localStorage.removeItem('linear_regression_results');
  };
  
  // Handle dependent variable selection change
  const handleDependentVariableChange = (event: SelectChangeEvent<string>) => {
    setDependentVariable(event.target.value);
    setRegressionResults(null);
    setPrediction(null);
    
    // Clear saved results when dependent variable changes
    localStorage.removeItem('linear_regression_results');
  };

  // Handle base category change for a categorical variable
  const handleBaseCategoryChange = (variableId: string, baseCategory: string) => {
    setSelectedCategoricalBaseCategories(prev => ({
      ...prev,
      [variableId]: baseCategory,
    }));
    setRegressionResults(null);
    setPrediction(null);
    
    // Clear saved results when base categories change
    localStorage.removeItem('linear_regression_results');
  };

  // Helper to get unique categories for a column (using ordered categories)
  const getUniqueCategories = (columnId: string): string[] => {
    if (!currentDataset) return [];
    const column = currentDataset.columns.find(col => col.id === columnId);
    if (!column || column.type !== DataType.CATEGORICAL) return [];

    // Use ordered categories for consistent ordering
    return getOrderedCategoriesByColumnId(columnId, currentDataset);
  };
  
  // Handle confidence interval change
  const handleConfIntervalChange = (event: SelectChangeEvent<number>) => {
    setConfInterval(Number(event.target.value));
    setRegressionResults(null);
    setPrediction(null);
    
    // Clear saved results when confidence interval changes
    localStorage.removeItem('linear_regression_results');
  };
  
  // Handle display option change
  const handleDisplayOptionChange = (option: keyof typeof displayOptions) => {
    setDisplayOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };
  
  // Handle prediction input change
  const handlePredictionInputChange = (varId: string, value: string) => {
    setPredictionInputValues(prev => ({ ...prev, [varId]: value }));
    setPrediction(null);
  };
  
  // Generate prediction
  const generatePrediction = () => {
    if (!regressionResults || !regressionResults.originalXColumns || !regressionResults.xColumns || !regressionResults.coefficients) return;

    try {
      const processedInputValues: { [key: string]: number } = {}; // This will store values for the final predictor columns (including dummies)
      const displayInputValues: { name: string; value: string | number }[] = []; // For displaying user's original inputs
      let allInputsValid = true;

      // Process inputs from the user (which are based on original columns)
      regressionResults.originalXColumns.forEach((originalCol: Column) => {
        if (!allInputsValid) return; // Stop if an error occurred

        const val = predictionInputValues[originalCol.id];
        if (val === undefined || String(val).trim() === '') {
          setPrediction({ error: `Please enter a value for ${originalCol.name}` });
          allInputsValid = false;
          return;
        }

        if (originalCol.type === DataType.NUMERIC) {
          const valNum = parseFloat(String(val));
          if (isNaN(valNum)) {
            setPrediction({ error: `Invalid number entered for ${originalCol.name}` });
            allInputsValid = false;
            return;
          }
          // Store numeric value using the original column ID (which is also the final column ID for numerics)
          processedInputValues[originalCol.id] = valNum;
          displayInputValues.push({ name: originalCol.name, value: valNum });
        } else if (originalCol.type === DataType.CATEGORICAL) {
          const selectedCategory = String(val);


          // Find all dummy columns associated with this original categorical column
          const dummyCols = regressionResults.xColumns.filter((col: Column & { originalColumnId?: string }) =>
            col.originalColumnId === originalCol.id
          );

          // Set dummy variable values (0 or 1) based on the selected category
          dummyCols.forEach((dummyCol: Column & { dummyForCategory?: string }) => {
            if (dummyCol.dummyForCategory) {
              processedInputValues[dummyCol.id] = (selectedCategory === dummyCol.dummyForCategory) ? 1 : 0;
            }
          });
          // The base category is implicitly handled: all its dummy variables will be 0.

          displayInputValues.push({ name: originalCol.name, value: selectedCategory });
        }
      });

      if (!allInputsValid) return;

      // Calculate predicted value using the processed input values (for final predictor columns)
      let predictedY = regressionResults.intercept;
      regressionResults.coefficients.forEach((coef: number, index: number) => {
        const xCol = regressionResults.xColumns[index]; // This is one of the final predictor columns (numeric or dummy)
        if (processedInputValues[xCol.id] !== undefined) {
            predictedY += coef * processedInputValues[xCol.id];
        } else {
            // This case should ideally not be hit if logic above is correct
            console.error(`Missing processed value for ${xCol.name} (${xCol.id}) in prediction calculation.`);
            // Optionally, set an error and return
            // setPrediction({ error: `Internal error processing ${xCol.name}` });
            // allInputsValid = false;
            // return;
        }
      });

      // TODO: Implement correct prediction and confidence intervals for multiple linear regression.
      // The formulas below are for simple linear regression and are placeholders.
      const placeholderInterval = [predictedY * 0.9, predictedY * 1.1]; // Example placeholder

      setPrediction({
        inputs: displayInputValues, // Display the original inputs
        predicted: predictedY,
        predictionInterval: placeholderInterval, // Placeholder
        confidenceInterval: placeholderInterval, // Placeholder
        intervalNote: 'Note: Prediction and confidence intervals shown are placeholders for multiple regression.',
      });
    } catch (e) {
      setPrediction({
        error: e instanceof Error ? e.message : String(e)
      });
    }
  };
  
  // Run linear regression analysis
  const runRegression = () => {
    if (!currentDataset || independentVariables.length === 0 || !dependentVariable) {
      setError('Please select at least one independent variable and one dependent variable.');
      return;
    }

    setLoading(true);
    setError(null);
    setRegressionResults(null);
    setPrediction(null);

    try {
      const originalSelectedXColumns = independentVariables
        .map(id => currentDataset.columns.find(col => col.id === id))
        .filter(Boolean) as Column[];
      
      const yColumn = currentDataset.columns.find(col => col.id === dependentVariable);

      if (originalSelectedXColumns.length === 0 || !yColumn) {
        throw new Error('Selected variables not found in dataset.');
      }

      // Validate base categories for selected categorical IVs
      for (const col of originalSelectedXColumns) {
        if (col.type === DataType.CATEGORICAL && !selectedCategoricalBaseCategories[col.id]) {
          throw new Error(`Please select a base category for the categorical variable "${col.name}".`);
        }
      }

      const finalPredictorColumns: Column[] = [];
      const finalPredictorNames: string[] = [];

      originalSelectedXColumns.forEach(originalCol => {
        if (originalCol.type === DataType.NUMERIC) {
          finalPredictorColumns.push(originalCol);
          finalPredictorNames.push(originalCol.name);
        } else if (originalCol.type === DataType.CATEGORICAL) {
          const baseCategory = selectedCategoricalBaseCategories[originalCol.id];
          const uniqueCategories = getUniqueCategories(originalCol.id);
          uniqueCategories.forEach(cat => {
            if (cat !== baseCategory) {
              const dummyName = `${originalCol.name} (${cat} vs ${baseCategory})`;
              finalPredictorNames.push(dummyName);
              finalPredictorColumns.push({
                id: `${originalCol.id}_dummy_${cat}`,
                name: dummyName,
                type: DataType.NUMERIC, // Dummies are 0 or 1
                role: VariableRole.INDEPENDENT,
                description: `Dummy for ${originalCol.name}, cat ${cat}, base ${baseCategory}`
              });
            }
          });
        }
      });
      
      const X_for_regression: number[][] = [];
      const Y_for_regression: number[] = [];
      const validDataRowIndices: number[] = []; // To keep track of which original rows are used

      currentDataset.data.forEach((row, rowIndex) => {
        const yValue = row[yColumn.name];
        if (typeof yValue !== 'number' || isNaN(yValue)) {
          return; // Skip row if Y is invalid
        }

        const currentRowXValues: number[] = [];
        let rowIsValid = true;

        originalSelectedXColumns.forEach(originalCol => {
          if (!rowIsValid) return;

          if (originalCol.type === DataType.NUMERIC) {
            const val = row[originalCol.name];
            if (typeof val !== 'number' || isNaN(val)) {
              rowIsValid = false;
              return;
            }
            currentRowXValues.push(val);
          } else if (originalCol.type === DataType.CATEGORICAL) {
            const baseCategory = selectedCategoricalBaseCategories[originalCol.id];
            const actualCategoryValue = row[originalCol.name] as string; // Assuming it's string
            const uniqueCategories = getUniqueCategories(originalCol.id);
            uniqueCategories.forEach(cat => {
              if (cat !== baseCategory) {
                currentRowXValues.push(actualCategoryValue === cat ? 1 : 0);
              }
            });
          }
        });

        if (rowIsValid) {
          X_for_regression.push(currentRowXValues);
          Y_for_regression.push(yValue);
          validDataRowIndices.push(rowIndex);
        }
      });
      
      if (X_for_regression.length < finalPredictorNames.length + 2 || X_for_regression.length < 3) {
        throw new Error(
          `Not enough valid data rows for regression analysis. Need at least ${Math.max(3, finalPredictorNames.length + 2)} valid rows after processing categorical variables.`
        );
      }
      
      const regression = multipleLinearRegression(X_for_regression, Y_for_regression);
      regression.xNames = finalPredictorNames;

      // Scatter plot data (only if first *selected* IV is numeric)
      let scatterDataPrimary: any[] | null = null;
      const firstOriginalXCol = originalSelectedXColumns[0];
      let firstSelectedXColForScatterPlot: Column | null = null;

      if (firstOriginalXCol && firstOriginalXCol.type === DataType.NUMERIC) {
        firstSelectedXColForScatterPlot = firstOriginalXCol;
        scatterDataPrimary = [];
        X_for_regression.forEach((dataRow, index) => {
            // Find the index of firstOriginalXCol in originalSelectedXColumns
            const originalColIndex = originalSelectedXColumns.findIndex(col => col.id === firstOriginalXCol.id);
            if (originalColIndex !== -1) { // Should always be found if it's numeric
                 scatterDataPrimary?.push({
                    x: dataRow[originalColIndex], // Get the value from the processed X matrix
                    y: Y_for_regression[index],
                    residual: regression.residuals[index],
                    predicted: regression.predicted[index],
                    index: validDataRowIndices[index] // original row index
                });
            }
        });
        if (scatterDataPrimary && scatterDataPrimary.length > 0) {
          // Assign to a new const to help TypeScript with type narrowing
          const dataToSort = scatterDataPrimary; 
          dataToSort.sort((a, b) => a.x - b.x);
          // scatterDataPrimary is sorted in place
        } else {
          scatterDataPrimary = null; // No valid data for plot or already null
        }
      }
      
      const residualsMean = regression.residuals.reduce((sum, r) => sum + r, 0) / regression.residuals.length;
      const residualsStd = Math.sqrt(
        regression.residuals.reduce((sum, r) => sum + Math.pow(r - residualsMean, 2), 0) / (regression.residuals.length -1) // Use n-1 for sample std dev
      ) || 1; // Avoid division by zero if all residuals are same

      const residualPlotData = regression.predicted.map((predY, index) => ({
        x: predY,
        residual: regression.residuals[index],
        standardized: residualsStd !== 0 ? regression.residuals[index] / residualsStd : 0,
        index: validDataRowIndices[index] 
      })).sort((a,b) => a.x - b.x);

      const residualsSkewness = regression.residuals.reduce(
        (sum, r) => sum + Math.pow((r - residualsMean) / residualsStd, 3),0) / regression.residuals.length;
      const residualsKurtosis = regression.residuals.reduce(
        (sum, r) => sum + Math.pow((r - residualsMean) / residualsStd, 4),0) / regression.residuals.length - 3;
      const residualsAreNormal = Math.abs(residualsSkewness) < 1 && Math.abs(residualsKurtosis) < 1; // Looser criteria

      const sortedResiduals = [...regression.residuals].sort((a, b) => a - b);
      const qqPlotData = sortedResiduals.map((residual, index) => {
        const p = (index + 0.5) / sortedResiduals.length;
        const c0=2.515517, c1=0.802853, c2=0.010328, d1=1.432788, d2=0.189269, d3=0.001308;
        let q;
        if(p <= 0.5){const t=Math.sqrt(-2.0*Math.log(p)); q = -t + (c0+c1*t+c2*t*t)/(1.0+d1*t+d2*t*t+d3*t*t*t);}
        else{const t=Math.sqrt(-2.0*Math.log(1.0-p)); q = t - (c0+c1*t+c2*t*t)/(1.0+d1*t+d2*t*t+d3*t*t*t);}
        return { theoretical: q * residualsStd + residualsMean, observed: residual };
      });

      const results = {
        ...regression,
        xColumns: finalPredictorColumns, // Actual predictors used (numeric + dummies)
        originalXColumns: originalSelectedXColumns, // Original user selection
        yColumn,
        xColumn: finalPredictorColumns[0],
        scatterData: scatterDataPrimary,
        firstSelectedXColForScatter: firstSelectedXColForScatterPlot,
        residualPlotData,
        qqPlotData,
        residualStats: {
          mean: residualsMean, std: residualsStd,
          skewness: residualsSkewness, kurtosis: residualsKurtosis,
          normal: residualsAreNormal
        }
      };
      
      setRegressionResults(results);
      
      // Save results to localStorage
      try {
        localStorage.setItem('linear_regression_results', JSON.stringify(results));
      } catch (error) {
        console.error('Error saving linear regression results to localStorage:', error);
      }
      
      setLoading(false);
    } catch (err) {
      setError(`Error in regression analysis: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  };
  
  // Enhanced interpretation with comprehensive statistical guidance
  const getInterpretation = () => {
    if (!regressionResults) return '';

    const { coefficients, intercept, rSquared, pValue, n, pValues, interceptPValue, xNames, yColumn } = regressionResults;

    let interpretation = '';
    const numPredictors = xNames?.length || 0;
    const dfModel = numPredictors;
    const dfError = n - numPredictors - 1;



    // Calculate additional statistics
    const adjR2 = dfError > 0 && numPredictors > 0
      ? 1 - (1 - rSquared) * (n - 1) / dfError
      : null;

    const fStat = dfError > 0 && numPredictors > 0 && rSquared < 1
      ? (rSquared / dfModel) / ((1 - rSquared) / dfError)
      : null;

    // Paragraph-style summary (similar to logistic regression format)
    const predictorText = xNames && xNames.length > 1
      ? `${xNames.slice(0, -1).join(', ')} and ${xNames[xNames.length - 1]}`
      : xNames?.[0] || 'the selected predictor(s)';

    interpretation += `A linear regression analysis was conducted to predict ${yColumn.name} based on ${predictorText}. `;
    interpretation += `The model was fitted using ${n.toLocaleString()} observations. `;

    if (fStat !== null) {
      if (pValue < 0.05) {
        interpretation += `The overall regression model was statistically significant (F(${dfModel}, ${dfError}) = ${fStat.toFixed(2)}, p ${pValue < 0.001 ? '< 0.001' : '= ' + pValue.toFixed(3)}), `;
        interpretation += `explaining ${(rSquared * 100).toFixed(1)}% of the variance in ${yColumn.name} (R² = ${rSquared.toFixed(3)}`;
        if (adjR2 !== null) {
          interpretation += `, adjusted R² = ${adjR2.toFixed(3)}`;
        }
        interpretation += `). `;
      } else {
        interpretation += `The overall regression model was not statistically significant (F(${dfModel}, ${dfError}) = ${fStat.toFixed(2)}, p = ${pValue.toFixed(3)}), `;
        interpretation += `explaining only ${(rSquared * 100).toFixed(1)}% of the variance in ${yColumn.name} (R² = ${rSquared.toFixed(3)}). `;
      }
    } else {
      interpretation += `The model explained ${(rSquared * 100).toFixed(1)}% of the variance in ${yColumn.name} (R² = ${rSquared.toFixed(3)}). `;
    }

    // Add coefficient significance summary
    if (xNames && coefficients && pValues) {
      const significantPredictors = coefficients.filter((_: number, index: number) => pValues[index] < 0.05);
      const significantNames = xNames.filter((_: string, index: number) => pValues[index] < 0.05);

      if (significantPredictors.length > 0) {
        if (significantPredictors.length === 1) {
          const index = pValues.findIndex((p: number) => p < 0.05);
          const coef = coefficients[index];
          const name = xNames[index];
          interpretation += `${name} was a statistically significant predictor (β = ${coef.toFixed(3)}, p ${pValues[index] < 0.001 ? '< 0.001' : '= ' + pValues[index].toFixed(3)}), `;
          interpretation += `indicating that for each one-unit increase in ${name}, ${yColumn.name} ${coef > 0 ? 'increases' : 'decreases'} by ${Math.abs(coef).toFixed(3)} units on average, holding other variables constant. `;
        } else {
          interpretation += `${significantPredictors.length} of the ${numPredictors} predictor variables were statistically significant: ${significantNames.join(', ')}. `;
        }
      } else {
        interpretation += `None of the predictor variables were statistically significant at the α = 0.05 level. `;
      }
    }

    // Effect size interpretation
    if (rSquared < 0.01) {
      interpretation += `The effect size was negligible (R² < 0.01), suggesting minimal practical significance. `;
    } else if (rSquared < 0.09) {
      interpretation += `The effect size was small (R² = 0.01-0.09), indicating a weak but detectable relationship. `;
    } else if (rSquared < 0.25) {
      interpretation += `The effect size was medium (R² = 0.09-0.25), indicating a moderate relationship with practical significance. `;
    } else {
      interpretation += `The effect size was large (R² ≥ 0.25), indicating a strong relationship with substantial practical significance. `;
    }

    interpretation += `\n\n`;

    // Overview section
    interpretation += `## Detailed Analysis Summary\n\n`;
    interpretation += `This analysis examined the relationship between ${yColumn.name} (dependent variable) and ${numPredictors} predictor variable${numPredictors > 1 ? 's' : ''} using multiple linear regression.\n\n`;

    // Model Performance
    interpretation += `### Model Performance\n\n`;
    interpretation += `**Sample Size:** n = ${n.toLocaleString()} observations\n\n`;

    if (fStat !== null) {
      if (pValue < 0.05) {
        interpretation += `**Overall Model Significance:** The regression model was statistically significant (F(${dfModel}, ${dfError}) = ${fStat.toFixed(2)}, p ${pValue < 0.001 ? '< 0.001' : '= ' + pValue.toFixed(3)}), indicating that the predictors collectively explain a significant portion of the variance in ${yColumn.name}.\n\n`;
      } else {
        interpretation += `**Overall Model Significance:** The regression model was not statistically significant (F(${dfModel}, ${dfError}) = ${fStat.toFixed(2)}, p = ${pValue.toFixed(3)}), suggesting that the predictors do not collectively explain a significant portion of the variance in ${yColumn.name}.\n\n`;
      }
    }

    interpretation += `**Variance Explained:**\n`;
    interpretation += `• R² = ${rSquared.toFixed(4)} (${(rSquared * 100).toFixed(1)}% of variance explained)\n`;
    if (adjR2 !== null) {
      interpretation += `• Adjusted R² = ${adjR2.toFixed(4)} (adjusted for model complexity)\n`;
    }
    interpretation += `• Standard Error of Regression = ${regressionResults.standardErrorOfRegression ? regressionResults.standardErrorOfRegression.toFixed(4) : 'N/A'}\n\n`;

    // Effect Size Interpretation
    if (rSquared < 0.01) {
      interpretation += `**Effect Size:** Negligible effect (R² < 0.01)\n\n`;
    } else if (rSquared < 0.09) {
      interpretation += `**Effect Size:** Small effect (R² = 0.01-0.09)\n\n`;
    } else if (rSquared < 0.25) {
      interpretation += `**Effect Size:** Medium effect (R² = 0.09-0.25)\n\n`;
    } else {
      interpretation += `**Effect Size:** Large effect (R² ≥ 0.25)\n\n`;
    }

    // Regression Equation
    interpretation += `### Regression Equation\n\n`;
    interpretation += `**${yColumn.name} = ${intercept.toFixed(4)}`;
    if (xNames && coefficients) {
      coefficients.forEach((coef: number, index: number) => {
        const name = xNames[index] || `Predictor ${index + 1}`;
        interpretation += ` ${coef >= 0 ? '+' : '-'} ${Math.abs(coef).toFixed(4)} × (${name})`;
      });
    }
    interpretation += `**\n\n`;

    // Coefficient Interpretation
    interpretation += `### Coefficient Analysis\n\n`;

    // Intercept
    if (interceptPValue < 0.05) {
      interpretation += `**Intercept:** ${intercept.toFixed(4)} (p ${interceptPValue < 0.001 ? '< 0.001' : '= ' + interceptPValue.toFixed(3)}) - Statistically significant. This represents the expected value of ${yColumn.name} when all predictors equal zero.\n\n`;
    } else {
      interpretation += `**Intercept:** ${intercept.toFixed(4)} (p = ${interceptPValue.toFixed(3)}) - Not statistically significant.\n\n`;
    }

    // Predictor coefficients
    if (xNames && coefficients && pValues) {
      interpretation += `**Predictor Variables:**\n\n`;
      coefficients.forEach((coef: number, index: number) => {
        const name = xNames[index] || `Predictor ${index + 1}`;
        const coefPValue = pValues[index];
        const tValue = regressionResults.stdErrors ? coef / regressionResults.stdErrors[index] : null;

        interpretation += `• **${name}:**\n`;
        interpretation += `  - Coefficient: ${coef.toFixed(4)}\n`;
        interpretation += `  - Standard Error: ${regressionResults.stdErrors ? regressionResults.stdErrors[index].toFixed(4) : 'N/A'}\n`;
        if (tValue) interpretation += `  - t-value: ${tValue.toFixed(4)}\n`;
        interpretation += `  - p-value: ${coefPValue < 0.001 ? '< 0.001' : coefPValue.toFixed(4)}\n`;
        interpretation += `  - Significance: ${coefPValue < 0.05 ? '**Significant**' : 'Not significant'}\n`;

        if (coefPValue < 0.05) {
          if (name.includes('vs') || name.includes('dummy')) {
            // Categorical variable interpretation
            interpretation += `  - Interpretation: This category differs significantly from the reference category by ${coef.toFixed(4)} units in ${yColumn.name}, holding other variables constant.\n`;
          } else {
            // Numeric variable interpretation
            interpretation += `  - Interpretation: For each one-unit increase in ${name}, ${yColumn.name} ${coef > 0 ? 'increases' : 'decreases'} by ${Math.abs(coef).toFixed(4)} units, holding other variables constant.\n`;
          }
        } else {
          interpretation += `  - Interpretation: No significant relationship detected with ${yColumn.name}.\n`;
        }
        interpretation += `\n`;
      });
    }

    // Model Assumptions and Diagnostics
    interpretation += `### Model Assumptions and Diagnostics\n\n`;

    interpretation += `**Residual Analysis:**\n`;
    if (regressionResults.residualStats) {
      const { skewness, kurtosis, normal } = regressionResults.residualStats;

      if (normal) {
        interpretation += `• **Normality:** Residuals appear normally distributed (skewness = ${skewness.toFixed(3)}, kurtosis = ${kurtosis.toFixed(3)}). This supports the normality assumption.\n`;
      } else {
        interpretation += `• **Normality:** Residuals may violate normality assumption (skewness = ${skewness.toFixed(3)}, kurtosis = ${kurtosis.toFixed(3)}). Consider data transformation or robust regression methods.\n`;
      }

      interpretation += `• **Homoscedasticity:** Examine the residual plot for constant variance. Points should be randomly scattered around zero with consistent spread.\n`;
      interpretation += `• **Independence:** Ensure observations are independent. Check for autocorrelation if data has temporal or spatial structure.\n`;
      interpretation += `• **Linearity:** The relationship between predictors and outcome should be linear. Consider polynomial terms or transformations if needed.\n\n`;
    }

    // Practical Significance
    interpretation += `### Practical Significance and Limitations\n\n`;

    interpretation += `**Practical Considerations:**\n`;
    interpretation += `• **Clinical/Practical Significance:** Consider whether the magnitude of effects is meaningful in the real-world context, not just statistical significance.\n`;
    interpretation += `• **Confidence Intervals:** Examine confidence intervals for coefficients to understand the range of plausible effect sizes.\n`;
    interpretation += `• **Multicollinearity:** Check for high correlations between predictors that might affect coefficient stability.\n`;
    interpretation += `• **Outliers and Influential Points:** Examine residual plots and leverage statistics for observations that might unduly influence results.\n\n`;

    // Methodological Notes
    interpretation += `### Methodological Notes\n\n`;

    interpretation += `**Linear Regression Assumptions:**\n`;
    interpretation += `• **Linearity:** The relationship between predictors and outcome is linear\n`;
    interpretation += `• **Independence:** Observations are independent of each other\n`;
    interpretation += `• **Homoscedasticity:** Constant variance of residuals across all levels of predictors\n`;
    interpretation += `• **Normality:** Residuals are normally distributed\n`;
    interpretation += `• **No perfect multicollinearity:** Predictors are not perfectly correlated\n\n`;

    interpretation += `**Interpretation Guidelines:**\n`;
    interpretation += `• **R² Interpretation:** Proportion of variance in the dependent variable explained by the model\n`;
    interpretation += `• **Adjusted R²:** R² adjusted for the number of predictors (penalizes model complexity)\n`;
    interpretation += `• **F-test:** Tests whether the overall model is statistically significant\n`;
    interpretation += `• **t-tests:** Test whether individual coefficients are significantly different from zero\n`;
    interpretation += `• **Standard Error:** Measure of precision for coefficient estimates\n\n`;

    interpretation += `**Important Considerations:**\n`;
    interpretation += `• Correlation does not imply causation\n`;
    interpretation += `• Results are specific to the sample and may not generalize to other populations\n`;
    interpretation += `• Missing data patterns may affect results\n`;
    interpretation += `• Model selection and variable inclusion decisions affect interpretation\n`;
    interpretation += `• Consider alternative models (non-linear, robust regression) if assumptions are violated\n`;

    return interpretation;
  };
  
  // Generate color for residuals based on standardized value
  const getResidualColor = (standardized: number) => {
    if (Math.abs(standardized) > 3) {
      return theme.palette.error.main; // Severe outlier
    } else if (Math.abs(standardized) > 2) {
      return theme.palette.warning.main; // Potential outlier
    } else {
      return theme.palette.primary.main; // Normal
    }
  };
  

  
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Linear Regression
      </Typography>
      
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Variables
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="independent-variables-label">Independent Variables (X)</InputLabel>
              <Select
                labelId="independent-variables-label"
                id="independent-variables"
                multiple
                value={independentVariables}
                label="Independent Variables (X)"
                onChange={handleIndependentVariablesChange}
                disabled={!currentDataset}
                renderValue={(selected) => {
                  const selectedNames = selected.map(id => {
                    const column = availableIndependentColumns.find(col => col.id === id);
                    return column ? column.name : '';
                  }).filter(Boolean);
                  return selectedNames.join(', ');
                }}
              >
                {availableIndependentColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No suitable variables available
                  </MenuItem>
                ) : (
                  availableIndependentColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name} ({column.type === DataType.CATEGORICAL ? 'Categorical' : 'Numeric'})
                    </MenuItem>
                  ))
                )}
              </Select>
              <Typography variant="caption" color="text.secondary">
                Select one or more predictor variables (Numeric or Categorical).
              </Typography>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dependent-variable-label">Dependent Variable (Y)</InputLabel>
              <Select
                labelId="dependent-variable-label"
                id="dependent-variable"
                value={dependentVariable}
                label="Dependent Variable (Y)"
                onChange={handleDependentVariableChange}
                disabled={!currentDataset}
              >
                {numericColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No numeric variables available
                  </MenuItem>
                ) : (
                  numericColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Base Category Selection for Categorical Variables */}
        {independentVariables.some(varId => availableIndependentColumns.find(col => col.id === varId)?.type === DataType.CATEGORICAL) && (
          <Box mt={2} p={2} border={1} borderColor="divider" borderRadius={1}>
            <Typography variant="subtitle2" gutterBottom>
              Select Base Category for Categorical Variables
            </Typography>
            <Grid container spacing={2}>
              {independentVariables.map(varId => {
                const column = availableIndependentColumns.find(col => col.id === varId);
                if (column && column.type === DataType.CATEGORICAL) {
                  const categories = getUniqueCategories(varId);
                  return (
                    <Grid item xs={12} md={4} key={varId}>
                      <FormControl fullWidth margin="normal">
                        <InputLabel id={`base-category-label-${varId}`}>Base for {column.name}</InputLabel>
                        <Select
                          labelId={`base-category-label-${varId}`}
                          value={selectedCategoricalBaseCategories[varId] || ''}
                          label={`Base for ${column.name}`}
                          onChange={(e) => handleBaseCategoryChange(varId, e.target.value as string)}
                        >
                          {categories.length === 0 ? (
                            <MenuItem value="" disabled>No categories found</MenuItem>
                          ) : (
                            categories.map(cat => (
                              <MenuItem key={cat} value={cat}>{cat}</MenuItem>
                            ))
                          )}
                        </Select>
                      </FormControl>
                    </Grid>
                  );
                }
                return null;
              })}
            </Grid>
          </Box>
        )}
        
        <Box mt={1}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="conf-interval-label">Confidence Level</InputLabel>
                <Select
                  labelId="conf-interval-label"
                  id="conf-interval"
                  value={confInterval}
                  label="Confidence Level"
                  onChange={handleConfIntervalChange}
                >
                  <MenuItem value={0.90}>90%</MenuItem>
                  <MenuItem value={0.95}>95%</MenuItem>
                  <MenuItem value={0.99}>99%</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>
        
        <Box mt={2}>
          <Typography variant="subtitle2" gutterBottom>
            Display Options
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showRegressionLine}
                    onChange={() => handleDisplayOptionChange('showRegressionLine')}
                  />
                }
                label="Show regression line"
              />
              
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showConfidenceIntervals}
                    onChange={() => handleDisplayOptionChange('showConfidenceIntervals')}
                  />
                }
                label="Show confidence intervals"
              />
              
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showPredictionIntervals}
                    onChange={() => handleDisplayOptionChange('showPredictionIntervals')}
                  />
                }
                label="Show prediction intervals"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showRegressionEquation}
                    onChange={() => handleDisplayOptionChange('showRegressionEquation')}
                  />
                }
                label="Show regression equation"
              />
              
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showResidualPlot}
                    onChange={() => handleDisplayOptionChange('showResidualPlot')}
                  />
                }
                label="Show residual plot"
              />
            </Grid>
          </Grid>
        </Box>
        
        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<TimelineIcon />}
            onClick={runRegression}
            disabled={loading || independentVariables.length === 0 || !dependentVariable || independentVariables.includes(dependentVariable)}
          >
            Run Regression Analysis
          </Button>
        </Box>
      </Paper>
      
      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {regressionResults && !loading && (
        <>
          <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">
                Linear Regression Results
              </Typography>
              <Chip
                icon={<PeopleIcon />}
                label={`n = ${regressionResults.n.toLocaleString()}`}
                color="primary"
                variant="outlined"
              />
            </Box>

            {/* Model Overview */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <InfoIcon fontSize="small" />
                Model Overview
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <StatsCard
                    title="Dependent Variable"
                    value={regressionResults.yColumn.name}
                    description="Outcome variable being predicted"
                    color="primary"
                    variant="outlined"
                    icon={<TrendingUpIcon />}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <StatsCard
                    title="Predictors"
                    value={regressionResults.xNames?.length || 0}
                    description={regressionResults.xNames && regressionResults.xNames.length > 0
                      ? regressionResults.xNames.slice(0, 2).join(', ') + (regressionResults.xNames.length > 2 ? '...' : '')
                      : 'No predictors'}
                    color="info"
                    variant="outlined"
                    icon={<AssessmentIcon />}
                  />
                </Grid>
              </Grid>
            </Box>

            {/* Key Statistics */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CalculateIcon fontSize="small" />
                Model Performance
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    title="R-squared"
                    value={regressionResults.rSquared.toFixed(4)}
                    description={`${(regressionResults.rSquared * 100).toFixed(1)}% variance explained`}
                    color="primary"
                    variant="gradient"
                    icon={<ShowChartIcon />}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    title="Adjusted R²"
                    value={(() => {
                      const numPredictors = regressionResults.xNames?.length || 0;
                      const n = regressionResults.n;
                      if (n - numPredictors - 1 > 0 && numPredictors > 0) {
                        const adjR2 = 1 - (1 - regressionResults.rSquared) * (n - 1) / (n - numPredictors - 1);
                        return adjR2.toFixed(4);
                      }
                      return 'N/A';
                    })()}
                    description="Adjusted for model complexity"
                    color="secondary"
                    variant="outlined"
                    icon={<FunctionsIcon />}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    title="F-statistic"
                    value={(() => {
                      const numPredictors = regressionResults.xNames?.length || 0;
                      const n = regressionResults.n;
                      const rSquared = regressionResults.rSquared;
                      if (n - numPredictors - 1 > 0 && numPredictors > 0 && rSquared < 1) {
                        const fStat = (rSquared / numPredictors) / ((1 - rSquared) / (n - numPredictors - 1));
                        return fStat.toFixed(2);
                      }
                      return 'N/A';
                    })()}
                    description="Overall model significance"
                    color="warning"
                    variant="outlined"
                    icon={<TimelineIcon />}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    title="p-value"
                    value={regressionResults.pValue < 0.001 ? '< 0.001' : regressionResults.pValue.toFixed(4)}
                    description={regressionResults.pValue < 0.05 ? 'Statistically significant' : 'Not significant'}
                    color={regressionResults.pValue < 0.05 ? 'success' : 'error'}
                    variant="outlined"
                    icon={<BubbleChartIcon />}
                  />
                </Grid>
              </Grid>
            </Box>

            {/* Additional Model Statistics */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Additional Statistics
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Sample Size"
                    value={regressionResults.n.toLocaleString()}
                    description="Valid observations used"
                    color="info"
                    variant="outlined"
                    icon={<PeopleIcon />}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Standard Error"
                    value={regressionResults.standardErrorOfRegression ? regressionResults.standardErrorOfRegression.toFixed(4) : 'N/A'}
                    description="Residual standard error"
                    color="warning"
                    variant="outlined"
                    icon={<TimelineIcon />}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Degrees of Freedom"
                    value={(() => {
                      const numPredictors = regressionResults.xNames?.length || 0;
                      const n = regressionResults.n;
                      return `${numPredictors}, ${n - numPredictors - 1}`;
                    })()}
                    description="Model, Error"
                    color="info"
                    variant="outlined"
                    icon={<CalculateIcon />}
                  />
                </Grid>
              </Grid>
            </Box>

            {/* Coefficients Table */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Regression Coefficients
              </Typography>

              <TableContainer
                component={Paper}
                elevation={0}
                variant="outlined"
                sx={{
                  borderRadius: 2,
                  '& .MuiTableCell-head': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    fontWeight: 'bold'
                  }
                }}
              >
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Parameter</TableCell>
                      <TableCell align="right">Estimate</TableCell>
                      <TableCell align="right">Std. Error</TableCell>
                      <TableCell align="right">t value</TableCell>
                      <TableCell align="right">p-value</TableCell>
                      <TableCell align="center">Significance</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 'medium' }}>Intercept</TableCell>
                      <TableCell align="right">{regressionResults.intercept.toFixed(4)}</TableCell>
                      <TableCell align="right">{regressionResults.interceptStdError.toFixed(4)}</TableCell>
                      <TableCell align="right">
                        {(regressionResults.intercept / regressionResults.interceptStdError).toFixed(4)}
                      </TableCell>
                      <TableCell align="right">
                        {regressionResults.interceptPValue < 0.001 ? '< 0.001' : regressionResults.interceptPValue.toFixed(4)}
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={regressionResults.interceptPValue < 0.05 ? 'Significant' : 'Not Significant'}
                          color={regressionResults.interceptPValue < 0.05 ? 'success' : 'default'}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                    </TableRow>
                    {regressionResults.xNames && regressionResults.coefficients && regressionResults.coefficients.map((coef: number, index: number) => (
                      <TableRow key={regressionResults.xNames[index] || `predictor-${index}`}>
                        <TableCell sx={{ fontWeight: 'medium' }}>
                          {regressionResults.xNames[index] || `Predictor ${index + 1}`}
                        </TableCell>
                        <TableCell align="right">{coef.toFixed(4)}</TableCell>
                        <TableCell align="right">{regressionResults.stdErrors[index].toFixed(4)}</TableCell>
                        <TableCell align="right">
                          {(coef / regressionResults.stdErrors[index]).toFixed(4)}
                        </TableCell>
                        <TableCell align="right">
                          {regressionResults.pValues[index] < 0.001 ? '< 0.001' : regressionResults.pValues[index].toFixed(4)}
                        </TableCell>
                        <TableCell align="center">
                          <Chip
                            label={regressionResults.pValues[index] < 0.05 ? 'Significant' : 'Not Significant'}
                            color={regressionResults.pValues[index] < 0.05 ? 'success' : 'default'}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>

            {/* Regression Equation */}
            {displayOptions.showRegressionEquation && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Regression Equation
                </Typography>
                <Paper
                  elevation={0}
                  variant="outlined"
                  sx={{
                    p: 3,
                    backgroundColor: alpha(theme.palette.background.default, 0.5),
                    borderRadius: 2
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      fontFamily: 'monospace',
                      wordBreak: 'break-all',
                      color: theme.palette.primary.main,
                      textAlign: 'center'
                    }}
                  >
                    {regressionResults.yColumn.name} = {regressionResults.intercept.toFixed(4)}
                    {regressionResults.xNames && regressionResults.coefficients && regressionResults.coefficients.map((coef: number, index: number) => {
                      const sign = coef >= 0 ? ' + ' : ' - ';
                      return `${sign}${Math.abs(coef).toFixed(4)} × (${regressionResults.xNames[index] || `Predictor ${index + 1}`})`;
                    })}
                  </Typography>
                </Paper>
              </Box>
            )}

            {/* Charts Section */}
            <Grid container spacing={3}>
              {/* Regression Plot (only if first selected IV is numeric and data exists) */}
              {regressionResults.scatterData && regressionResults.firstSelectedXColForScatter && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Regression Plot (vs {regressionResults.firstSelectedXColForScatter.name})
                  </Typography>
                  <Paper elevation={0} variant="outlined" sx={{ p: 2, borderRadius: 2 }}>
                    <Box height={400}>
                      <ResponsiveContainer width="100%" height="100%">
                        <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                          <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.5)} />
                          <XAxis
                            type="number"
                            dataKey="x"
                            name={regressionResults.firstSelectedXColForScatter.name}
                            label={{ value: regressionResults.firstSelectedXColForScatter.name, position: 'insideBottom', offset: -10 }}
                          />
                          <YAxis
                            type="number"
                            dataKey="y"
                            name={regressionResults.yColumn.name}
                            label={{ value: regressionResults.yColumn.name, angle: -90, position: 'insideLeft' }}
                          />
                          <RechartsTooltip
                            cursor={{ strokeDasharray: '3 3' }}
                            formatter={(value: any, name: string) => [typeof value === 'number' ? value.toFixed(4) : value, name]}
                            contentStyle={{
                              backgroundColor: theme.palette.background.paper,
                              border: `1px solid ${theme.palette.divider}`,
                              borderRadius: 8
                            }}
                          />
                          <Legend />
                          <Scatter
                            name="Data Points"
                            data={regressionResults.scatterData}
                            fill={alpha(theme.palette.primary.main, 0.7)}
                          />
                          {displayOptions.showRegressionLine && (
                            <Line
                              type="monotone"
                              dataKey="predicted"
                              data={regressionResults.scatterData}
                              stroke={theme.palette.secondary.main}
                              strokeWidth={3}
                              dot={false}
                              name="Regression Line"
                            />
                          )}
                        </ScatterChart>
                      </ResponsiveContainer>
                    </Box>
                  </Paper>
                </Grid>
              )}

              {displayOptions.showResidualPlot && regressionResults.residualPlotData && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Residual Plot
                  </Typography>

                  <Paper elevation={0} variant="outlined" sx={{ p: 2, borderRadius: 2 }}>
                    <Box height={400}>
                      <ResponsiveContainer width="100%" height="100%">
                        <ScatterChart
                          data={regressionResults.residualPlotData}
                          margin={{
                            top: 20,
                            right: 30,
                            bottom: 60,
                            left: 60,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.5)} />
                          <XAxis
                            type="number"
                            dataKey="x"
                            name="Fitted Values"
                            domain={['dataMin - 0.1', 'dataMax + 0.1']}
                            label={{
                              value: 'Fitted Values',
                              position: 'insideBottom',
                              offset: -5,
                              style: { textAnchor: 'middle', fontSize: '14px', fontWeight: 'bold' }
                            }}
                            tick={{ fontSize: 12 }}
                            tickFormatter={(value) => typeof value === 'number' ? value.toFixed(2) : value}
                          />
                          <YAxis
                            type="number"
                            dataKey="residual"
                            name="Residuals"
                            domain={(() => {
                              // Calculate appropriate Y-axis domain based on residual range
                              const residuals = regressionResults.residualPlotData.map((d: any) => d.residual);
                              const minRes = Math.min(...residuals);
                              const maxRes = Math.max(...residuals);
                              const range = maxRes - minRes;
                              const padding = range * 0.1; // 10% padding
                              return [minRes - padding, maxRes + padding];
                            })()}
                            label={{
                              value: 'Residuals',
                              angle: -90,
                              position: 'insideLeft',
                              style: { textAnchor: 'middle', fontSize: '14px', fontWeight: 'bold' }
                            }}
                            tick={{ fontSize: 12 }}
                            tickFormatter={(value) => typeof value === 'number' ? value.toFixed(2) : value}
                          />
                          <RechartsTooltip
                            cursor={{ strokeDasharray: '3 3' }}
                            formatter={(value: any, name: string) => {
                              if (name === 'Fitted Values') {
                                return [value.toFixed(3), 'Fitted Value'];
                              } else if (name === 'Residuals') {
                                return [value.toFixed(3), 'Residual'];
                              }
                              return [value.toFixed(3), name];
                            }}
                            labelFormatter={(label) => `Point ${label}`}
                            contentStyle={{
                              backgroundColor: theme.palette.background.paper,
                              border: `1px solid ${theme.palette.divider}`,
                              borderRadius: 8,
                              fontSize: '12px'
                            }}
                          />
                          <Legend
                            wrapperStyle={{ fontSize: '12px' }}
                            iconType="circle"
                          />

                          {/* Enhanced reference line at y=0 */}
                          <ReferenceLine
                            y={0}
                            stroke={theme.palette.mode === 'dark' ? '#666' : '#333'}
                            strokeWidth={2}
                            strokeDasharray="5 5"
                            label={{
                              value: "y = 0",
                              position: "insideTopRight",
                              style: { fontSize: '11px', fill: theme.palette.text.secondary }
                            }}
                          />

                          {/* Residual points with enhanced styling */}
                          <Scatter
                            name="Residuals"
                            data={regressionResults.residualPlotData}
                            fill={theme.palette.primary.main}
                          >
                            {regressionResults.residualPlotData.map((entry: any, index: number) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={getResidualColor(entry.standardized)}
                                stroke={alpha(getResidualColor(entry.standardized), 0.8)}
                                strokeWidth={1}
                              />
                            ))}
                          </Scatter>
                        </ScatterChart>
                      </ResponsiveContainer>
                    </Box>

                    <Box sx={{ mt: 2 }}>
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                        <strong>Residual Plot Interpretation:</strong> Points should be randomly scattered around the zero line with no clear pattern.
                        Patterns may indicate violations of linearity, homoscedasticity, or independence assumptions.
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                        <strong>Color Coding:</strong>
                        <span style={{ color: theme.palette.primary.main, marginLeft: 4 }}>● Normal</span>
                        <span style={{ color: theme.palette.warning.main, marginLeft: 8 }}>● Potential outlier (|z| {'>'} 2)</span>
                        <span style={{ color: theme.palette.error.main, marginLeft: 8 }}>● Severe outlier (|z| {'>'} 3)</span>
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
              )}

              {displayOptions.showResidualPlot && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Q-Q Plot of Residuals
                  </Typography>
                  <Paper elevation={0} variant="outlined" sx={{ p: 2, borderRadius: 2 }}>
                    <Box height={350}>
                      <ResponsiveContainer width="100%" height="100%">
                        <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                          <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.5)} />
                          <XAxis
                            type="number"
                            dataKey="theoretical"
                            name="Theoretical Quantiles"
                            label={{ value: 'Theoretical Quantiles', position: 'insideBottom', offset: -10 }}
                          />
                          <YAxis
                            type="number"
                            dataKey="observed"
                            name="Sample Quantiles"
                            label={{ value: 'Sample Quantiles', angle: -90, position: 'insideLeft' }}
                          />
                          <RechartsTooltip
                            cursor={{ strokeDasharray: '3 3' }}
                            formatter={(value: any) => typeof value === 'number' ? value.toFixed(4) : value}
                            contentStyle={{
                              backgroundColor: theme.palette.background.paper,
                              border: `1px solid ${theme.palette.divider}`,
                              borderRadius: 8
                            }}
                          />
                          <Scatter
                            name="Q-Q Plot"
                            data={regressionResults.qqPlotData}
                            fill={alpha(theme.palette.secondary.main, 0.7)}
                          />
                        </ScatterChart>
                      </ResponsiveContainer>
                    </Box>

                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                      Q-Q plot assesses normality of residuals. Points should follow the diagonal line if residuals are normally distributed.
                    </Typography>
                  </Paper>
                </Grid>
              )}
            </Grid>
            
            {/* Prediction Section */}
            <Box mt={4}>
              <Typography variant="h6" gutterBottom>Prediction Tool</Typography>

              <Grid container spacing={2} alignItems="flex-start">
                {(() => {
                  if (!regressionResults || !regressionResults.originalXColumns) return null;
                  const inputFields: JSX.Element[] = [];
                  const processedOriginalCategoricalVars = new Set<string>();

                  // Calculate number of unique predictors (original categoricals + numerics)
                  // to determine grid sizing
                  const numUniquePredictors = regressionResults.originalXColumns.length;
                  // Ensure mdSize is at least 1 to prevent division by zero or excessively small columns
                  const mdSize = Math.max(3, Math.floor(12 / Math.max(1, numUniquePredictors)));

                  regressionResults.originalXColumns.forEach((originalCol: Column) => {
                    if (originalCol.type === DataType.NUMERIC) {
                      // Numeric variable, render a TextField
                      inputFields.push(
                        <Grid item xs={12} sm={6} md={mdSize} key={originalCol.id}>
                          <TextField
                            label={`Value for ${originalCol.name}`}
                            value={predictionInputValues[originalCol.id] || ''}
                            onChange={(e) => handlePredictionInputChange(originalCol.id, e.target.value)}
                            fullWidth
                            type="number"
                            margin="normal"
                          />
                        </Grid>
                      );
                    } else if (originalCol.type === DataType.CATEGORICAL) {
                       // Categorical variable, render a Select dropdown
                       // We only need one dropdown per original categorical variable
                       if (!processedOriginalCategoricalVars.has(originalCol.id)) {
                          const categories = getUniqueCategories(originalCol.id);
                          const baseCategory = selectedCategoricalBaseCategories[originalCol.id]; // Get base category from state
                          inputFields.push(
                            <Grid item xs={12} sm={6} md={mdSize} key={originalCol.id}>
                              <FormControl fullWidth margin="normal">
                                <InputLabel id={`${originalCol.id}-predict-label`}>{originalCol.name}</InputLabel>
                                <Select
                                  labelId={`${originalCol.id}-predict-label`}
                                  id={`${originalCol.id}-predict-select`}
                                  value={predictionInputValues[originalCol.id] || ''} // Use originalColumnId as key
                                  label={originalCol.name}
                                  onChange={(e) => handlePredictionInputChange(originalCol.id, e.target.value as string)}
                                >
                                  {categories.map(category => (
                                    <MenuItem key={category} value={category}>
                                      {category} {category === baseCategory ? '(Base)' : ''}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                            </Grid>
                          );
                          processedOriginalCategoricalVars.add(originalCol.id);
                       }
                    }
                  });
                  return inputFields;
                })()}

                <Grid item xs={12} sx={{ mt: (regressionResults?.originalXColumns?.length || 0) > 0 ? 0 : 2 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SearchIcon />}
                    onClick={generatePrediction}
                    disabled={
                      !regressionResults || !regressionResults.originalXColumns ||
                      !regressionResults.originalXColumns.every(
                        (col: Column) => predictionInputValues[col.id] !== undefined && String(predictionInputValues[col.id]).trim() !== ''
                      )
                    }
                    sx={{ mt: 1 }}
                  >
                    Predict {regressionResults.yColumn.name}
                  </Button>
                </Grid>

                {prediction && !prediction.error && (
                  <Grid item xs={12}>
                    <Paper elevation={0} variant="outlined" sx={{ p: 2, mt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>Prediction Result</Typography>
                      <Typography variant="body1">Input Values:</Typography>
                      <ul>
                        {prediction.inputs.map((input: {name: string, value: string | number}) => (
                          <li key={input.name}>{`${input.name} = ${input.value}`}</li>
                        ))}
                      </ul>
                      <Typography variant="body1" fontWeight="bold">
                        {`Predicted ${regressionResults.yColumn.name} = ${prediction.predicted.toFixed(4)}`}
                      </Typography>
                      {prediction.intervalNote && (
                        <Typography variant="caption" color="text.secondary" display="block" mt={1}>
                          {prediction.intervalNote}
                        </Typography>
                      )}
                    </Paper>
                  </Grid>
                )}
                {prediction && prediction.error && (
                  <Grid item xs={12}><Alert severity="error">{prediction.error}</Alert></Grid>
                )}
              </Grid>
            </Box>
            
            <Box mt={4}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <InfoIcon />
                Statistical Interpretation & Guidance
              </Typography>

              <Paper
                elevation={0}
                variant="outlined"
                sx={{
                  p: 3,
                  bgcolor: alpha(theme.palette.background.paper, 0.8),
                  borderRadius: 2
                }}
              >
                <Box sx={{
                  '& h2': {
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    color: theme.palette.primary.main,
                    mt: 2,
                    mb: 1,
                    '&:first-of-type': { mt: 0 }
                  },
                  '& h3': {
                    fontSize: '1rem',
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                    mt: 2,
                    mb: 1
                  },
                  '& p': {
                    mb: 1,
                    lineHeight: 1.6
                  },
                  '& ul': {
                    pl: 2,
                    mb: 1
                  },
                  '& li': {
                    mb: 0.5
                  }
                }}>
                  <Typography
                    variant="body2"
                    sx={{
                      whiteSpace: 'pre-line',
                      '& strong': { fontWeight: 600 }
                    }}
                    dangerouslySetInnerHTML={{
                      __html: getInterpretation()
                        .replace(/## (.*)/g, '<h2>$1</h2>')
                        .replace(/### (.*)/g, '<h3>$1</h3>')
                        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                        .replace(/• (.*)/g, '• $1')
                        .replace(/\n\n/g, '</p><p>')
                        .replace(/^\s*/, '<p>')
                        .replace(/\s*$/, '</p>')
                    }}
                  />
                </Box>
              </Paper>
            </Box>
          </Paper>
        </>
      )}
    </Box>
  );
};

export default LinearRegression;
