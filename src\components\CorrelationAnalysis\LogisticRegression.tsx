import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  FormControlLabel,
  Checkbox,
  TextField,
  Divider,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Radio,
  RadioGroup,
  Chip
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  BubbleChart as BubbleChartIcon,
  Help as HelpIcon,
  Search as SearchIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  Info as InfoIcon,
  People as PeopleIcon,
  Calculate as CalculateIcon,
  Speed as SpeedIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import StatsCard from '../UI/StatsCard';
import { useData } from '../../context/DataContext';
import { DataType, Column, VariableRole } from '../../types'; // Import Column and VariableRole types
import { multipleLogisticRegression } from '@/utils/stats';
import { getOrderedCategoriesByColumnId } from '../../utils/dataUtilities';
import {
  ScatterChart,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Line,
  Label,
  ReferenceLine,
  ReferenceArea,
  ComposedChart,
  Area,
  Bar
} from 'recharts';
import { create, all } from 'mathjs'; // Import mathjs

// Initialize math.js
const math = create(all);

// Define the expected structure for regression results within this component
// Combine the return type of the function with component-specific additions
type LogisticRegressionComponentResults = ReturnType<typeof multipleLogisticRegression> & {
  xColumns: (Column & {
    isDummy?: boolean;
    originalColumnId?: string;
    originalColumnName?: string;
    dummyForCategory?: string;
    baseCategory?: string;
    allCategories?: string[];
  })[];
  yColumn: Column;
  xColumn: Column; // For primary visualization
  scatterData: { x: number; y: number; predicted: number; index: number; variable: string }[];
  curvePoints: { x: number; predicted: number }[];
  rocPoints: { threshold: number; tpr: number; fpr: number }[];
  xMin: number;
  xMax: number;
  xNames?: string[]; // Added dynamically
  covMatrix: number[][]; // Added covariance matrix
};


// Component for logistic regression analysis
const LogisticRegression: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();

  // State for regression selection
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [independentVariables, setIndependentVariables] = useState<string[]>([]);
  const [dependentVariable, setDependentVariable] = useState<string>('');
  // Initialize with a valid value to prevent MUI Select warnings
  const [confInterval, setConfInterval] = useState<number>(0.95);
  const [selectedCategoricalBaseCategories, setSelectedCategoricalBaseCategories] = useState<{ [key: string]: string }>({});

  // State for prediction
  const [predictionInputValues, setPredictionInputValues] = useState<{ [key: string]: string }>({});
  const [prediction, setPrediction] = useState<any | null>(null);

  // State for display options
  const [displayOptions, setDisplayOptions] = useState({
    showConfidenceIntervals: true,
    showRegressionEquation: true,
    showRegressionCurve: true,
    showROCCurve: true
  });

  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  // Use the defined interface for the state
  const [regressionResults, setRegressionResults] = useState<LogisticRegressionComponentResults | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);

  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('logistic_regression_results');
    if (savedResults) {
      try {
        const parsedResults = JSON.parse(savedResults);
        setRegressionResults(parsedResults);
      } catch (error) {
        console.error('Error parsing saved logistic regression results:', error);
        localStorage.removeItem('logistic_regression_results');
      }
    }
  }, []);

  // State for categorical binary variable mapping
  const [showMappingDialog, setShowMappingDialog] = useState<boolean>(false);
  const [categoricalValues, setCategoricalValues] = useState<string[]>([]);
  const [valueMapping, setValueMapping] = useState<{value1: string; value2: string; oneValue: string}>({value1: '', value2: '', oneValue: ''});

  // Get numeric and categorical columns from current dataset for independent variables
  const availableIndependentColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC || col.type === DataType.CATEGORICAL
  ) || [];

  // Get numeric columns from current dataset (for dependent variable, though logistic usually uses binary)
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];

  // Get binary columns (0/1) from current dataset
  const binaryColumns = currentDataset?.columns.filter(col => {
    if (col.type !== DataType.NUMERIC && col.type !== DataType.CATEGORICAL) return false;

    // Check if column has only 0/1 or true/false values
    const values = new Set();
    let validCount = 0;

    for (const row of currentDataset.data) {
      const value = row[col.name];
      if (value === 0 || value === 1 || value === '0' || value === '1' ||
          value === false || value === true ||
          value === 'false' || value === 'true' ||
          value === 'False' || value === 'True' ||
          value === 'FALSE' || value === 'TRUE' ||
          value === 'no' || value === 'yes' ||
          value === 'No' || value === 'Yes' ||
          value === 'NO' || value === 'YES') {
        values.add(value);
        validCount++;
      }
    }

    // Check if only has 2 values and they are binary-like
    return validCount === currentDataset.data.length && values.size <= 2;
  }) || [];

  // Get categorical binary columns (with exactly 2 distinct values)
  const categoricalBinaryColumns = currentDataset?.columns.filter(col => {
    if (col.type !== DataType.CATEGORICAL && col.type !== DataType.BOOLEAN) return false;

    // Check if column has exactly 2 distinct values
    const values = new Set();

    for (const row of currentDataset.data) {
      const value = row[col.name];
      if (value !== null && value !== undefined) {
        values.add(String(value));
      }
      // If we already found more than 2 values, no need to continue
      if (values.size > 2) return false;
    }

    // Return true if exactly 2 distinct values
    return values.size === 2;
  }) || [];

  // Combine binary and categorical binary columns, removing duplicates
  const allBinaryColumns = [...binaryColumns];
  categoricalBinaryColumns.forEach(col => {
    if (!allBinaryColumns.some(existingCol => existingCol.id === col.id)) {
      allBinaryColumns.push(col);
    }
  });

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setIndependentVariables([]);
    setDependentVariable('');
    setRegressionResults(null);
    setPrediction(null);

    // Clear saved results when dataset changes
    localStorage.removeItem('logistic_regression_results');

    // Update the current dataset in the DataContext
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };

  // Handle independent variables selection change
  const handleIndependentVariablesChange = (event: SelectChangeEvent<typeof independentVariables>) => {
    const value = event.target.value;
    const newVars = typeof value === 'string' ? value.split(',') : value;
    setIndependentVariables(newVars);

    // Clear base category for deselected categorical variables
    const updatedBaseCategories = { ...selectedCategoricalBaseCategories };
    Object.keys(selectedCategoricalBaseCategories).forEach(varId => {
      if (!newVars.includes(varId)) {
        delete updatedBaseCategories[varId];
      }
    });
    setSelectedCategoricalBaseCategories(updatedBaseCategories);

    setRegressionResults(null);
    setPrediction(null);

    // Clear saved results when variables change
    localStorage.removeItem('logistic_regression_results');
  };

  // Handle dependent variable selection change
  const handleDependentVariableChange = (event: SelectChangeEvent<string>) => {
    const selectedId = event.target.value;
    setDependentVariable(selectedId);
    setRegressionResults(null);
    setPrediction(null);

    // Clear saved results when dependent variable changes
    localStorage.removeItem('logistic_regression_results');

    // Check if selected column is categorical binary
    const selectedColumn = currentDataset?.columns.find(col => col.id === selectedId);
    if (selectedColumn &&
        (selectedColumn.type === DataType.CATEGORICAL || selectedColumn.type === DataType.BOOLEAN) &&
        categoricalBinaryColumns.some(col => col.id === selectedId)) {

      // Get unique values for this column
      const values = new Set<string>();
      // Add null check for currentDataset
      if (currentDataset?.data) {
        currentDataset.data.forEach(row => {
          const value = row[selectedColumn.name];
          if (value !== null && value !== undefined) {
            values.add(String(value));
          }
        });
      }
      // Removed duplicate/misplaced block here

      const uniqueValues = Array.from(values);
      if (uniqueValues.length === 2) {
        setCategoricalValues(uniqueValues);
        setValueMapping({
          value1: uniqueValues[0],
          value2: uniqueValues[1],
          oneValue: uniqueValues[0] // Default mapping first value to 1
        });
        setShowMappingDialog(true);
      }
    }
  };

  // Handle confidence interval change
  const handleConfIntervalChange = (event: SelectChangeEvent<number>) => {
    setConfInterval(Number(event.target.value));
    setRegressionResults(null);
    setPrediction(null);

    // Clear saved results when confidence interval changes
    localStorage.removeItem('logistic_regression_results');
  };

  // Handle display option change
  const handleDisplayOptionChange = (option: keyof typeof displayOptions) => {
    setDisplayOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  // Handle prediction input change
  const handlePredictionInputChange = (varId: string, value: string) => {
    setPredictionInputValues(prev => ({ ...prev, [varId]: value }));
    setPrediction(null);
  };

  // Handle base category change for a categorical variable
  const handleBaseCategoryChange = (variableId: string, baseCategory: string) => {
    setSelectedCategoricalBaseCategories(prev => ({
      ...prev,
      [variableId]: baseCategory,
    }));
    setRegressionResults(null); // Results will need to be recalculated
    setPrediction(null);
  };

  // Helper to get unique categories for a column (using ordered categories)
  const getUniqueCategories = (columnId: string): string[] => {
    if (!currentDataset) return [];
    const column = currentDataset.columns.find(col => col.id === columnId);
    if (!column || column.type !== DataType.CATEGORICAL) return [];

    // Use ordered categories for consistent ordering
    return getOrderedCategoriesByColumnId(columnId, currentDataset);
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Generate prediction
  const generatePrediction = () => {
    if (!regressionResults || !regressionResults.xColumns || !regressionResults.coefficients) return;

    try {
      const processedInputValues: { [key: string]: number } = {};
      let allInputsValid = true;

      const originalCategoricalVarsInfo: {
        [key: string]: {
          name: string,
          selectedValue: string,
          allCategories: string[],
          baseCategory: string
        }
      } = {};

      // First, validate and collect info for original categorical variables from predictionInputValues
      // predictionInputValues stores user input against originalColumnId for categoricals
      for (const col of regressionResults.xColumns) {
        if (col.isDummy && col.originalColumnId && col.originalColumnName && col.allCategories && col.baseCategory) {
          if (!originalCategoricalVarsInfo[col.originalColumnId]) {
            const selectedValue = predictionInputValues[col.originalColumnId];
            if (selectedValue === undefined || String(selectedValue).trim() === '') {
              setPrediction({ error: `Please select a value for ${col.originalColumnName}` });
              allInputsValid = false;
              break;
            }
            originalCategoricalVarsInfo[col.originalColumnId] = {
              name: col.originalColumnName,
              selectedValue: String(selectedValue),
              allCategories: col.allCategories,
              baseCategory: col.baseCategory
            };
          }
        }
      }
      if (!allInputsValid) return;

      // Populate processedInputValues for both numeric and dummy variables
      // This map will hold the 0/1 for dummies and numeric values for others, against the actual xColumn id (e.g., originalColId_dummy_cat)
      for (const col of regressionResults.xColumns) {
        if (!allInputsValid) break;

        if (col.isDummy && col.originalColumnId && col.dummyForCategory) {
          const originalVarInfo = originalCategoricalVarsInfo[col.originalColumnId];
          if (!originalVarInfo) {
            setPrediction({ error: `Internal error: Missing info for ${col.originalColumnName}` });
            allInputsValid = false;
            break;
          }
          // If the selected category for the original variable matches this dummy's category, set to 1, else 0.
          // No dummy is created for the base category, so it's implicitly handled (all dummies are 0).
          processedInputValues[col.id] = (originalVarInfo.selectedValue === col.dummyForCategory) ? 1 : 0;
        } else if (!col.isDummy) { // Numeric variable
          const valStr = predictionInputValues[col.id];
          if (valStr === undefined || String(valStr).trim() === '') {
            setPrediction({ error: `Please enter a value for ${col.name}` });
            allInputsValid = false;
            break;
          }
          const valNum = parseFloat(String(valStr));
          if (isNaN(valNum)) {
            setPrediction({ error: `Invalid number entered for ${col.name}` });
            allInputsValid = false;
            break;
          }
          processedInputValues[col.id] = valNum;
        }
      }

      if (!allInputsValid) return;

      // Calculate logit
      let logit = regressionResults.intercept;
      regressionResults.coefficients.forEach((coef: number, index: number) => {
        const xCol = regressionResults.xColumns[index]; // This xCol is one of the finalPredictorColumns
        // It could be a numeric column or a dummy variable column
        if (processedInputValues[xCol.id] !== undefined) {
            logit += coef * processedInputValues[xCol.id];
        } else {
            // This case should ideally not be hit if logic above is correct
            console.error(`Missing processed value for ${xCol.name} (${xCol.id}) in logit calculation.`);
            // Optionally, set an error and return
            // setPrediction({ error: `Internal error processing ${xCol.name}` });
            // allInputsValid = false;
            // return;
        }
      });

      const probability = 1 / (1 + Math.exp(-logit));

      let lowerCI = NaN;
      let upperCI = NaN;
      let intervalNote = '';

      if (regressionResults.xColumns.length === 1 && regressionResults.covMatrix && regressionResults.covMatrix.length === 2 && regressionResults.covMatrix[0].length === 2) {
        try {
          const firstColId = regressionResults.xColumns[0].id;
          const xVal = processedInputValues[firstColId];
          if (xVal === undefined) {
            throw new Error ('Processed value for single predictor not found for CI calculation.');
          }
          const x_pred_vector = [1, xVal];
          const covMatrix_mathjs = math.matrix(regressionResults.covMatrix);
          const x_pred_matrix_mathjs = math.matrix(x_pred_vector);

          const varianceLogitMatrix = math.multiply(math.multiply(math.transpose(x_pred_matrix_mathjs), covMatrix_mathjs), x_pred_matrix_mathjs);
          const varianceLogitValue = Math.max(0, varianceLogitMatrix.get([0, 0]));
          const seLogit = Math.sqrt(varianceLogitValue);

          const z = 1.96; // For 95% confidence interval
          const lowerLogitCI = logit - z * seLogit;
          const upperLogitCI = logit + z * seLogit;

          lowerCI = 1 / (1 + Math.exp(-lowerLogitCI));
          upperCI = 1 / (1 + Math.exp(-upperLogitCI));
        } catch (matrixError) {
          console.error("Error calculating prediction CI for single predictor:", matrixError);
          intervalNote = 'Error calculating confidence interval.';
        }
      } else if (regressionResults.xColumns.length > 1) {
        intervalNote = 'Confidence interval calculation is not supported for multiple predictors in this tool.';
      }

      const displayInputs: { name: string; value: string | number }[] = [];
      // Populate displayInputs from originalCategoricalVarsInfo and numeric processedInputValues
      Object.values(originalCategoricalVarsInfo).forEach(info => {
        displayInputs.push({ name: info.name, value: info.selectedValue });
      });
      regressionResults.xColumns.forEach(col => {
        if (!col.isDummy) { // Numeric variables
          if (processedInputValues[col.id] !== undefined) {
            displayInputs.push({ name: col.name, value: processedInputValues[col.id] });
          } else {
            console.error(`Missing processed value for numeric display input ${col.name} (${col.id})`);
          }
        }
      });

      setPrediction({
        inputs: displayInputs,
        probability,
        confidenceInterval: [isNaN(lowerCI) ? 0 : lowerCI, isNaN(upperCI) ? 1 : upperCI],
        predictedClass: probability >= 0.5 ? 1 : 0,
        intervalNote: intervalNote || (isNaN(lowerCI) ? 'Confidence interval could not be calculated.' : '')
      });
    } catch (e) {
      setPrediction({
        error: e instanceof Error ? e.message : String(e)
      });
    }
  };

  // Handle mapping dialog close
  const handleMappingDialogClose = () => {
    setShowMappingDialog(false);
  };

  // Handle mapping value change
  const handleMappingValueChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValueMapping(prev => ({
      ...prev,
      oneValue: event.target.value
    }));
  };

  // Run logistic regression analysis
  const runRegression = () => {
    if (!currentDataset || independentVariables.length === 0 || !dependentVariable) {
      setError('Please select at least one independent variable and one dependent variable.');
      return;
    }

    setLoading(true);
    setError(null);
    setRegressionResults(null);
    setPrediction(null);

    try {
      const originalSelectedXColumns = independentVariables
        .map(id => currentDataset.columns.find(col => col.id === id))
        .filter(Boolean) as Column[];

      const yColumn = currentDataset.columns.find(col => col.id === dependentVariable);

      if (originalSelectedXColumns.length === 0 || !yColumn) {
        throw new Error('Selected variables not found in dataset.');
      }

      // Validate base categories for selected categorical IVs
      for (const col of originalSelectedXColumns) {
        if (col.type === DataType.CATEGORICAL && !selectedCategoricalBaseCategories[col.id]) {
          throw new Error(`Please select a base category for the categorical variable "${col.name}".`);
        }
      }

      const finalPredictorColumns: Column[] = [];
      const finalPredictorNames: string[] = [];

      originalSelectedXColumns.forEach(originalCol => {
        if (originalCol.type === DataType.NUMERIC) {
          finalPredictorColumns.push(originalCol);
          finalPredictorNames.push(originalCol.name);
        } else if (originalCol.type === DataType.CATEGORICAL) {
          const baseCategory = selectedCategoricalBaseCategories[originalCol.id];
          const uniqueCategories = getUniqueCategories(originalCol.id);
          uniqueCategories.forEach(cat => {
            if (cat !== baseCategory) {
              const dummyName = `${originalCol.name} (${cat} vs ${baseCategory})`;
              finalPredictorNames.push(dummyName);
              finalPredictorColumns.push({
                id: `${originalCol.id}_dummy_${cat}`,
                name: dummyName,
                type: DataType.NUMERIC, // Dummies are 0 or 1
                role: VariableRole.INDEPENDENT,
                description: `Dummy for ${originalCol.name}, category ${cat}, base ${baseCategory}`,
                isDummy: true,
                originalColumnId: originalCol.id,
                originalColumnName: originalCol.name,
                dummyForCategory: cat,
                baseCategory: baseCategory,
                allCategories: uniqueCategories, // Store all categories for UI dropdown
              } as Column & { isDummy?: boolean; originalColumnId?: string; originalColumnName?: string; dummyForCategory?: string; baseCategory?: string; allCategories?: string[] });
            }
          });
        }
      });

      const X_for_regression: number[][] = [];
      const Y_for_regression: number[] = [];
      const validDataRowIndices: number[] = []; // To keep track of which original rows are used

      currentDataset.data.forEach((row, rowIndex) => {
        let yVal = row[yColumn.name];
        // Convert y value to binary 0/1 based on mapping if it's a categorical/boolean dependent variable
        if (yColumn.type === DataType.CATEGORICAL || yColumn.type === DataType.BOOLEAN) {
          if (valueMapping.value1 && valueMapping.value2 && valueMapping.oneValue) {
            if (String(yVal) === valueMapping.oneValue) yVal = 1;
            else if (String(yVal) === (valueMapping.oneValue === valueMapping.value1 ? valueMapping.value2 : valueMapping.value1)) yVal = 0;
            else return; // Skip row if yVal doesn't match mapping
          } else {
            if (typeof yVal === 'string') {
              if (['true', 'True', 'TRUE', 'yes', 'Yes', 'YES', '1'].includes(yVal)) yVal = 1;
              else if (['false', 'False', 'FALSE', 'no', 'No', 'NO', '0'].includes(yVal)) yVal = 0;
              else return; // Skip invalid string yVal
            } else if (typeof yVal === 'boolean') yVal = yVal ? 1 : 0;
            else if (yVal !== 0 && yVal !== 1) return; // Skip invalid numeric yVal
          }
        } else if (yVal !== 0 && yVal !== 1) {
          return; // Skip invalid yVal for numeric dependent variables not 0 or 1
        }

        const currentRowXValues: number[] = [];
        let rowIsValid = true;

        originalSelectedXColumns.forEach(originalCol => {
          if (!rowIsValid) return;

          if (originalCol.type === DataType.NUMERIC) {
            const val = row[originalCol.name];
            if (typeof val !== 'number' || isNaN(val)) {
              rowIsValid = false;
              return;
            }
            currentRowXValues.push(val);
          } else if (originalCol.type === DataType.CATEGORICAL) {
            const baseCategory = selectedCategoricalBaseCategories[originalCol.id];
            const actualCategoryValue = row[originalCol.name] as string;
            const uniqueCategories = getUniqueCategories(originalCol.id);
            uniqueCategories.forEach(cat => {
              if (cat !== baseCategory) {
                currentRowXValues.push(actualCategoryValue === cat ? 1 : 0);
              }
            });
          }
        });

        if (rowIsValid) {
          X_for_regression.push(currentRowXValues);
          Y_for_regression.push(yVal as number); // yVal should be 0 or 1 at this point
          validDataRowIndices.push(rowIndex);
        }
      });

      // Use X_for_regression.length for checks, similar to validRows.length previously
      if (X_for_regression.length < 10 || X_for_regression.length < finalPredictorNames.length + 2) {
        throw new Error(
          `Not enough valid data rows for regression. Need at least ${Math.max(10, finalPredictorNames.length + 2)} rows after processing categorical variables.`
        );
      }

      if (X_for_regression.length < 10) {
        throw new Error('Not enough valid data rows for logistic regression analysis. Need at least 10 valid rows.');
      }

      // X_for_regression and Y_for_regression are now prepared directly above.

      // Run logistic regression
      try {
        // Check for separation in the data (perfect prediction)
        let hasSeparation = false;
        // Check for separation only if there's one original predictor and it's numeric
        if (originalSelectedXColumns.length === 1 && originalSelectedXColumns[0].type === DataType.NUMERIC) {
          const numericXIndex = finalPredictorColumns.findIndex(col => col.id === originalSelectedXColumns[0].id);
          if (numericXIndex !== -1) { // Should be found
            const xValuesForSeparationCheck = X_for_regression.map((dataRow, i) => ({
              x: dataRow[numericXIndex],
              y: Y_for_regression[i]
            }));
            // Sort by x value
            xValuesForSeparationCheck.sort((a, b) => a.x - b.x);
            // Check if there's a value of x where all points below have y=0 and all above have y=1 (or vice versa)
            for (let i = 0; i < xValuesForSeparationCheck.length - 1; i++) {
              if (xValuesForSeparationCheck[i].y !== xValuesForSeparationCheck[i+1].y) {
                const belowClass = xValuesForSeparationCheck[i].y;
                const aboveClass = xValuesForSeparationCheck[i+1].y;
                const allBelowSame = xValuesForSeparationCheck.slice(0, i+1).every(point => point.y === belowClass);
                const allAboveSame = xValuesForSeparationCheck.slice(i+1).every(point => point.y === aboveClass);
                if (allBelowSame && allAboveSame) {
                  hasSeparation = true;
                  break;
                }
              }
            }
          }
        }
        // For multiple predictors or categorical predictors, separation check is more complex and omitted here for simplicity
        // but can be added using more advanced libraries or checks if needed.

        if (hasSeparation) {
          throw new Error('Perfect separation detected. The predictor perfectly separates classes, causing instability.');
        }



        const regressionBaseResults = multipleLogisticRegression(X_for_regression, Y_for_regression);

        // Prepare scatter plot data for each *original* numeric independent variable
        const scatterDataSets = originalSelectedXColumns
          .filter(col => col.type === DataType.NUMERIC) // Only for original numeric IVs
          .map((originalNumCol) => {
            const numColIndexInFinalPredictors = finalPredictorColumns.findIndex(col => col.id === originalNumCol.id);
            if (numColIndexInFinalPredictors === -1) return null; // Should not happen

            const xValuesForPlot = X_for_regression.map(dataRow => dataRow[numColIndexInFinalPredictors]);
            const xMin = Math.min(...xValuesForPlot);
            const xMax = Math.max(...xValuesForPlot);
            const range = xMax - xMin;
            const padding = range * 0.1;

            const curvePoints = [];
            // Curve points only make sense if this numeric var is the *only* original predictor
            if (originalSelectedXColumns.length === 1 && originalSelectedXColumns[0].id === originalNumCol.id) {
            for (let x = xMin - padding; x <= xMax + padding; x += range / 100) {
              // Use regressionBaseResults here as 'regression' is not defined in this scope
              const logit = regressionBaseResults.intercept + regressionBaseResults.coefficients[0] * x;
              const predictedProb = 1 / (1 + Math.exp(-logit));

              curvePoints.push({
                x,
                predicted: predictedProb
              });
            }
          }

          // Create scatter plot data for this numeric variable
          return {
            variable: originalNumCol.name,
            xMin,
            xMax,
            curvePoints,
            scatterData: X_for_regression.map((dataRow, rowIndex) => ({
              x: dataRow[numColIndexInFinalPredictors],
              y: Y_for_regression[rowIndex],
              predicted: regressionBaseResults.predictions[rowIndex],
              index: validDataRowIndices[rowIndex], // Use original row index
              variable: originalNumCol.name
            }))
          };
        }).filter(Boolean); // Remove nulls if any issue occurred

        // Use the first *original numeric* independent variable for primary visualization if available
        const primaryScatterSet = scatterDataSets.length > 0 ? scatterDataSets[0] : null;
        const primaryScatterData = primaryScatterSet ? primaryScatterSet.scatterData : [];
        const curvePoints = primaryScatterSet ? primaryScatterSet.curvePoints : [];
        const xMin = primaryScatterSet ? primaryScatterSet.xMin : 0;
        const xMax = primaryScatterSet ? primaryScatterSet.xMax : 0;

        // Calculate observed values at different thresholds for ROC curve
        const thresholds = Array.from({ length: 101 }, (_, i) => i / 100);
        const rocPoints = thresholds.map(threshold => {
          const predictionsAtThreshold = regressionBaseResults.predictions.map(p => p >= threshold ? 1 : 0);
          let tp = 0, fp = 0, tn = 0, fn = 0;

          for (let i = 0; i < Y_for_regression.length; i++) {
            if (Y_for_regression[i] === 1 && predictionsAtThreshold[i] === 1) tp++;
            else if (Y_for_regression[i] === 0 && predictionsAtThreshold[i] === 1) fp++;
            else if (Y_for_regression[i] === 0 && predictionsAtThreshold[i] === 0) tn++;
            else if (Y_for_regression[i] === 1 && predictionsAtThreshold[i] === 0) fn++;
          }

          const tpr = (tp + fn) > 0 ? tp / (tp + fn) : 0;
          const fpr = (fp + tn) > 0 ? fp / (fp + tn) : 0;

          return { threshold, tpr, fpr };
        });

        // Sort by FPR for proper ROC curve
          rocPoints.sort((a, b) => a.fpr - b.fpr);

        // Set regression results with added data
        const resultsToSave = {
          ...regressionBaseResults,
          xColumns: finalPredictorColumns,
          yColumn,
          xColumn: primaryScatterSet
            ? originalSelectedXColumns.find(col => col.name === primaryScatterSet.variable && col.type === DataType.NUMERIC) || finalPredictorColumns[0]
            : finalPredictorColumns[0],
          scatterData: primaryScatterData,
          curvePoints,
          rocPoints,
          xMin,
          xMax,
          xNames: finalPredictorNames
        };

        setRegressionResults(resultsToSave);

        // Save results to localStorage
        localStorage.setItem('logistic_regression_results', JSON.stringify(resultsToSave));

        setLoading(false);
      } catch (err) {
        console.error('Logistic regression error:', err);

        // Provide more specific error messages based on the error type
        if (String(err).includes('determinant is zero') || String(err).includes('calculate inverse')) {
          if (finalPredictorColumns.length === 1) {
            // For a single predictor, this is likely due to separation or numerical issues
            setError('Error in regression analysis: Numerical instability detected. This may be due to complete or quasi-complete separation in your data, or multicollinearity if using multiple predictors. Try adding more data points with mixed outcomes, or check for highly correlated predictors.');
          } else {
            setError('Error in regression analysis: Cannot calculate regression. This could be due to multicollinearity or other numerical issues. Please check your variable selection and data.');
          }
        } else if (String(err).includes('Perfect separation')) {
          setError(`Error in regression analysis: ${err instanceof Error ? err.message : String(err)}. Try adding more data points with mixed outcomes.`);
        } else {
          setError(`Error in regression analysis: ${err instanceof Error ? err.message : String(err)}`);
        }
        setLoading(false);
      }
    } catch (err) {
        setError(`Error in regression analysis: ${err instanceof Error ? err.message : String(err)}`);
        setLoading(false);
    }
  };

  // Generate interpretation from regression results
  const getInterpretation = () => {
    if (!regressionResults) return '';

    const { intercept, coefficients, interceptPValue, pValues, n, accuracy, auc, pseudoRSquared, yColumn, xColumns, xNames } = regressionResults;

    let interpretation = '';

    // Overall model interpretation
    const predictorText = xNames && xNames.length > 1
      ? `${xNames.slice(0, -1).join(', ')} and ${xNames[xNames.length - 1]}`
      : xNames?.[0] || '';

    interpretation += `A binary logistic regression was conducted to predict ${yColumn.name} (0/1) based on ${predictorText}. `;
    interpretation += `The model was trained on ${n} observations. `;

    // Model fit statistics
    interpretation += `\n\nModel fit: The model achieved an accuracy of ${(accuracy * 100).toFixed(1)}% and an AUC of ${auc.toFixed(3)}. `;
    interpretation += `McFadden's pseudo R² was ${pseudoRSquared.toFixed(3)}, `;

    if (pseudoRSquared < 0.2) {
      interpretation += 'indicating a relatively weak relationship between the predictor and outcome. ';
    } else if (pseudoRSquared < 0.4) {
      interpretation += 'indicating a moderate relationship between the predictor and outcome. ';
    } else {
      interpretation += 'indicating a strong relationship between the predictor and outcome. ';
    }

    // Coefficient interpretation
    interpretation += `\n\nThe regression equation is: logit(p) = ${intercept.toFixed(4)}`;

    // Add each coefficient to the equation
    if (xNames && coefficients) {
      for (let i = 0; i < coefficients.length; i++) {
        const coef = coefficients[i];
        const name = xNames[i] || `X${i+1}`;
        interpretation += ` ${coef >= 0 ? '+' : ''} ${coef.toFixed(4)} × ${name}`;
      }
    }
    interpretation += '. ';

    // Interpret each coefficient
    if (xNames && coefficients && pValues) {
      for (let i = 0; i < coefficients.length; i++) {
        const coef = coefficients[i];
        const name = xNames[i] || `X${i+1}`;
        const coefPValue = pValues[i];

        if (coefPValue < 0.05) {
          interpretation += `The coefficient for ${name} (${coef.toFixed(4)}) was statistically significant (p ${coefPValue < 0.001 ? '< 0.001' : '= ' + coefPValue.toFixed(3)}), `;

          const oddsRatio = Math.exp(coef);
          if (coef > 0) {
            interpretation += `indicating that for each one-unit increase in ${name}, the odds of ${yColumn.name} = 1 increase by a factor of ${oddsRatio.toFixed(3)} (odds ratio). `;
            interpretation += `This means the probability of ${yColumn.name} = 1 increases as ${name} increases. `;
          } else {
            interpretation += `indicating that for each one-unit increase in ${name}, the odds of ${yColumn.name} = 1 decrease by a factor of ${oddsRatio.toFixed(3)} (odds ratio). `;
            interpretation += `This means the probability of ${yColumn.name} = 1 decreases as ${name} increases. `;
          }
        } else {
          interpretation += `The coefficient for ${name} (${coef.toFixed(4)}) was not statistically significant (p = ${coefPValue.toFixed(3)}), `;
          interpretation += `suggesting that there is not enough evidence to conclude that ${name} is associated with ${yColumn.name}. `;
        }
      }
    }

    // Intercept interpretation
    if (interceptPValue < 0.05) {
      interpretation += `The intercept (${intercept.toFixed(4)}) was statistically significant (p ${interceptPValue < 0.001 ? '< 0.001' : '= ' + interceptPValue.toFixed(3)}). `;

      const baselineProb = 1 / (1 + Math.exp(-intercept));
      interpretation += `It represents the log odds when all predictors are 0, which corresponds to a probability of ${baselineProb.toFixed(3)} for ${yColumn.name} = 1. `;
    } else {
      interpretation += `The intercept (${intercept.toFixed(4)}) was not statistically significant (p = ${interceptPValue.toFixed(3)}). `;
    }

    // Classification performance
    interpretation += `\n\nClassification performance: `;
    interpretation += `At the standard threshold (0.5), the model correctly classified ${(accuracy * 100).toFixed(1)}% of cases. `;
    interpretation += `The area under the ROC curve (AUC) of ${auc.toFixed(3)} `;

    if (auc < 0.7) {
      interpretation += 'suggests poor to fair discriminative ability. ';
    } else if (auc < 0.8) {
      interpretation += 'suggests acceptable discriminative ability. ';
    } else if (auc < 0.9) {
      interpretation += 'suggests good discriminative ability. ';
    } else {
      interpretation += 'suggests excellent discriminative ability. ';
    }

    // Practical interpretation
    interpretation += `\n\nPractical interpretation: `;

    // Only add this part for single-predictor models
    if (xNames && xNames.length === 1 && coefficients && coefficients.length === 1) {
      const name = xNames[0];
      const probAtMin = 1 / (1 + Math.exp(-(intercept + coefficients[0] * regressionResults.xMin)));
      const probAtMax = 1 / (1 + Math.exp(-(intercept + coefficients[0] * regressionResults.xMax)));

      interpretation += `At the minimum observed value of ${name} (${regressionResults.xMin.toFixed(2)}), the predicted probability of ${yColumn.name} = 1 is ${probAtMin.toFixed(3)}. `;
      interpretation += `At the maximum observed value of ${name} (${regressionResults.xMax.toFixed(2)}), the predicted probability of ${yColumn.name} = 1 is ${probAtMax.toFixed(3)}. `;
    } else {
      interpretation += `For multiple predictors, the probability depends on the combination of all predictor values. `;
    }

    return interpretation;
  };

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Logistic Regression
      </Typography>

      {/* Mapping Dialog for Categorical Binary Variables */}
      <Dialog open={showMappingDialog} onClose={handleMappingDialogClose}>
        <DialogTitle>Map Categorical Values to Binary (0/1)</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            The selected variable has two categorical values. Please specify which value should be mapped to 1 (the other will be mapped to 0).
          </Typography>
          <Box sx={{ mt: 2 }}>
            <RadioGroup
              value={valueMapping.oneValue}
              onChange={handleMappingValueChange}
            >
              <FormControlLabel
                value={valueMapping.value1}
                control={<Radio />}
                label={`Map "${valueMapping.value1}" to 1 (and "${valueMapping.value2}" to 0)`}
              />
              <FormControlLabel
                value={valueMapping.value2}
                control={<Radio />}
                label={`Map "${valueMapping.value2}" to 1 (and "${valueMapping.value1}" to 0)`}
              />
            </RadioGroup>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleMappingDialogClose} color="primary">
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Variables
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="independent-variables-label">Independent Variables (X)</InputLabel>
              <Select
                labelId="independent-variables-label"
                id="independent-variables"
                multiple
                value={independentVariables}
                label="Independent Variables (X)"
                onChange={handleIndependentVariablesChange}
                disabled={!currentDataset}
                renderValue={(selected) => {
                  const selectedNames = selected.map(id => {
                    const column = availableIndependentColumns.find(col => col.id === id);
                    return column ? column.name : '';
                  }).filter(Boolean);
                  return selectedNames.join(', ');
                }}
              >
                {availableIndependentColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No suitable variables available
                  </MenuItem>
                ) : (
                  availableIndependentColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name} ({column.type === DataType.CATEGORICAL ? 'Categorical' : 'Numeric'})
                    </MenuItem>
                  ))
                )}
              </Select>
              <Typography variant="caption" color="text.secondary">
                Select one or more predictor variables (Numeric or Categorical).
              </Typography>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dependent-variable-label">Dependent Variable (Y - Binary)</InputLabel>
              <Select
                labelId="dependent-variable-label"
                id="dependent-variable"
                value={dependentVariable}
                label="Dependent Variable (Y - Binary)"
                onChange={handleDependentVariableChange}
                disabled={!currentDataset}
              >
                {allBinaryColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No binary variables available
                  </MenuItem>
                ) : (
                  allBinaryColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name} {column.type === DataType.CATEGORICAL ? "(categorical)" : ""}
                    </MenuItem>
                  ))
                )}
              </Select>
              <Typography variant="caption" color="text.secondary">
                Select a binary (0/1) outcome variable or a categorical variable with exactly two values.
              </Typography>
            </FormControl>
          </Grid>
        </Grid>

        {/* Base Category Selection for Categorical Independent Variables */}
        {independentVariables.some(varId => availableIndependentColumns.find(col => col.id === varId)?.type === DataType.CATEGORICAL) && (
          <Box mt={2} p={2} border={1} borderColor="divider" borderRadius={1}>
            <Typography variant="subtitle2" gutterBottom>
              Select Base Category for Categorical Independent Variables
            </Typography>
            <Grid container spacing={2}>
              {independentVariables.map(varId => {
                const column = availableIndependentColumns.find(col => col.id === varId);
                if (column && column.type === DataType.CATEGORICAL) {
                  const categories = getUniqueCategories(varId);
                  return (
                    <Grid item xs={12} md={4} key={varId}>
                      <FormControl fullWidth margin="normal">
                        <InputLabel id={`base-category-label-${varId}`}>Base for {column.name}</InputLabel>
                        <Select
                          labelId={`base-category-label-${varId}`}
                          value={selectedCategoricalBaseCategories[varId] || ''}
                          label={`Base for {column.name}`}
                          onChange={(e) => handleBaseCategoryChange(varId, e.target.value as string)}
                          MenuProps={{ disableScrollLock: true }}
                        >
                          {categories.length === 0 ? (
                            <MenuItem value="" disabled>No categories found</MenuItem>
                          ) : (
                            categories.map(cat => (
                              <MenuItem key={cat} value={cat}>{cat}</MenuItem>
                            ))
                          )}
                        </Select>
                      </FormControl>
                    </Grid>
                  );
                }
                return null;
              })}
            </Grid>
          </Box>
        )}


        <Box mt={1}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="conf-interval-label">Confidence Level</InputLabel>
                <Select
                  labelId="conf-interval-label"
                  id="conf-interval"
                  value={confInterval}
                  label="Confidence Level"
                  onChange={handleConfIntervalChange}
                  MenuProps={{ disableScrollLock: true }}
                >
                  <MenuItem value={0.90}>90%</MenuItem>
                  <MenuItem value={0.95}>95%</MenuItem>
                  <MenuItem value={0.99}>99%</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        <Box mt={2}>
          <Typography variant="subtitle2" gutterBottom>
            Display Options
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showRegressionCurve}
                    onChange={() => handleDisplayOptionChange('showRegressionCurve')}
                  />
                }
                label="Show regression curve"
              />

              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showConfidenceIntervals}
                    onChange={() => handleDisplayOptionChange('showConfidenceIntervals')}
                  />
                }
                label="Show confidence intervals"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showRegressionEquation}
                    onChange={() => handleDisplayOptionChange('showRegressionEquation')}
                  />
                }
                label="Show regression equation"
              />

              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showROCCurve}
                    onChange={() => handleDisplayOptionChange('showROCCurve')}
                  />
                }
                label="Show ROC curve"
              />
            </Grid>
          </Grid>
        </Box>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AssessmentIcon />}
            onClick={runRegression}
            disabled={loading || independentVariables.length === 0 || !dependentVariable || independentVariables.includes(dependentVariable)}
          >
            Run Logistic Regression
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {regressionResults && !loading && (
        <>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="logistic regression tabs">
                <Tab label="Model Summary" />
                <Tab label="Visualizations" />
                <Tab label="Classification Results" />
                <Tab label="Prediction" />
                <Tab label="Interpretation" />
              </Tabs>
            </Box>

            {/* Model Summary Tab */}
            {tabValue === 0 && (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">
                    Logistic Regression Results
                  </Typography>
                  <Chip
                    icon={<PeopleIcon />}
                    label={`n = ${regressionResults.n.toLocaleString()}`}
                    color="primary"
                    variant="outlined"
                  />
                </Box>

                {/* Model Overview */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <InfoIcon fontSize="small" />
                    Model Overview
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <StatsCard
                        title="Dependent Variable"
                        value={`${regressionResults.yColumn.name} (Binary)`}
                        description="Binary outcome variable being predicted"
                        color="primary"
                        variant="outlined"
                        icon={<TrendingUpIcon />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <StatsCard
                        title="Predictors"
                        value={regressionResults.xNames?.length || 0}
                        description={regressionResults.xNames && regressionResults.xNames.length > 0
                          ? regressionResults.xNames.slice(0, 2).join(', ') + (regressionResults.xNames.length > 2 ? '...' : '')
                          : 'No predictors'}
                        color="secondary"
                        variant="outlined"
                        icon={<AnalyticsIcon />}
                      />
                    </Grid>
                  </Grid>
                </Box>

                {/* Model Performance */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CalculateIcon fontSize="small" />
                    Model Performance
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <StatsCard
                        title="Pseudo R²"
                        value={regressionResults.pseudoRSquared.toFixed(4)}
                        description={`${(regressionResults.pseudoRSquared * 100).toFixed(1)}% explained deviance`}
                        color="primary"
                        variant="gradient"
                        icon={<ShowChartIcon />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <StatsCard
                        title="Log Likelihood"
                        value={regressionResults.logLikelihood.toFixed(2)}
                        description="Model fit statistic"
                        color="info"
                        variant="outlined"
                        icon={<TimelineIcon />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <StatsCard
                        title="AIC"
                        value={regressionResults.aic.toFixed(2)}
                        description="Akaike Information Criterion"
                        color="warning"
                        variant="outlined"
                        icon={<SpeedIcon />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <StatsCard
                        title="Accuracy"
                        value={`${(regressionResults.accuracy * 100).toFixed(1)}%`}
                        description="Classification accuracy"
                        color={regressionResults.accuracy > 0.7 ? 'success' : regressionResults.accuracy > 0.6 ? 'warning' : 'error'}
                        variant="outlined"
                        icon={<AssessmentIcon />}
                      />
                    </Grid>
                  </Grid>
                </Box>

                {/* Additional Model Statistics */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Additional Statistics
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={4}>
                      <StatsCard
                        title="Sample Size"
                        value={regressionResults.n.toLocaleString()}
                        description="Valid observations used"
                        color="info"
                        variant="outlined"
                        icon={<PeopleIcon />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <StatsCard
                        title="AUC"
                        value={regressionResults.auc.toFixed(3)}
                        description="Area Under ROC Curve"
                        color={regressionResults.auc > 0.8 ? 'success' : regressionResults.auc > 0.7 ? 'warning' : 'error'}
                        variant="outlined"
                        icon={<BubbleChartIcon />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <StatsCard
                        title="Deviance"
                        value={(-2 * regressionResults.logLikelihood).toFixed(2)}
                        description="Model deviance (-2 × Log Likelihood)"
                        color="secondary"
                        variant="outlined"
                        icon={<FunctionsIcon />}
                      />
                    </Grid>
                  </Grid>
                </Box>

                {/* Coefficients Table */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <FunctionsIcon fontSize="small" />
                    Model Coefficients
                  </Typography>

                  <Paper elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow sx={{ backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)' }}>
                            <TableCell sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>Parameter</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>Estimate</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>Std. Error</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>z-value</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>p-value</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>Odds Ratio</TableCell>
                            <TableCell align="center" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>Significance</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          <TableRow sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}>
                            <TableCell sx={{ fontWeight: 'medium' }}>Intercept</TableCell>
                            <TableCell align="right">{regressionResults.intercept.toFixed(4)}</TableCell>
                            <TableCell align="right">{regressionResults.interceptStdError.toFixed(4)}</TableCell>
                            <TableCell align="right">
                              {(regressionResults.intercept / regressionResults.interceptStdError).toFixed(4)}
                            </TableCell>
                            <TableCell align="right">
                              {regressionResults.interceptPValue < 0.001 ? '< 0.001' : regressionResults.interceptPValue.toFixed(4)}
                            </TableCell>
                            <TableCell align="right">
                              {Math.exp(regressionResults.intercept).toFixed(4)}
                            </TableCell>
                            <TableCell align="center">
                              <Chip
                                label={regressionResults.interceptPValue < 0.001 ? '***' : regressionResults.interceptPValue < 0.01 ? '**' : regressionResults.interceptPValue < 0.05 ? '*' : 'ns'}
                                size="small"
                                color={regressionResults.interceptPValue < 0.05 ? 'success' : 'default'}
                                variant={regressionResults.interceptPValue < 0.05 ? 'filled' : 'outlined'}
                              />
                            </TableCell>
                          </TableRow>
                          {regressionResults.coefficients && regressionResults.xNames && regressionResults.coefficients.map((coef: number, index: number) => (
                            <TableRow key={index} sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}>
                              <TableCell sx={{ fontWeight: 'medium' }}>{regressionResults.xNames?.[index] || `X${index+1}`}</TableCell>
                              <TableCell align="right">{coef.toFixed(4)}</TableCell>
                              <TableCell align="right">{regressionResults.stdErrors[index].toFixed(4)}</TableCell>
                              <TableCell align="right">
                                {(coef / regressionResults.stdErrors[index]).toFixed(4)}
                              </TableCell>
                              <TableCell align="right">
                                {regressionResults.pValues[index] < 0.001 ? '< 0.001' : regressionResults.pValues[index].toFixed(4)}
                              </TableCell>
                              <TableCell align="right">
                                {Math.exp(coef).toFixed(4)}
                              </TableCell>
                              <TableCell align="center">
                                <Chip
                                  label={regressionResults.pValues[index] < 0.001 ? '***' : regressionResults.pValues[index] < 0.01 ? '**' : regressionResults.pValues[index] < 0.05 ? '*' : 'ns'}
                                  size="small"
                                  color={regressionResults.pValues[index] < 0.05 ? 'success' : 'default'}
                                  variant={regressionResults.pValues[index] < 0.05 ? 'filled' : 'outlined'}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Paper>

                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                    <strong>Significance codes:</strong> *** p {'<'} 0.001, ** p {'<'} 0.01, * p {'<'} 0.05, ns = not significant
                  </Typography>
                </Box>

                {/* Regression Equation */}
                {displayOptions.showRegressionEquation && (
                  <Box sx={{ mb: 3 }}>
                    <Paper elevation={0} variant="outlined" sx={{ p: 3, borderRadius: 2, backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)' }}>
                      <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <FunctionsIcon fontSize="small" />
                        Logistic Regression Equation
                      </Typography>
                      <Typography variant="h6" sx={{ fontFamily: 'monospace', textAlign: 'center', my: 2, color: theme.palette.primary.main }}>
                        logit(p) = {regressionResults.intercept.toFixed(4)}
                        {regressionResults.coefficients && regressionResults.xNames && regressionResults.coefficients.map((coef: number, index: number) => {
                          const sign = coef >= 0 ? ' + ' : ' - ';
                          return `${sign}${Math.abs(coef).toFixed(4)} × ${regressionResults.xNames?.[index] || `X${index+1}`}`;
                        })}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                        where logit(p) = ln(p / (1 - p)) and p is the probability of {regressionResults.yColumn.name} = 1
                      </Typography>
                    </Paper>
                  </Box>
                )}
              </Box>
            )}

            {/* Visualizations Tab */}
            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Logistic Regression Visualizations
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ShowChartIcon fontSize="small" />
                      Probability Curve
                    </Typography>
                    {regressionResults.xColumns.length > 1 && (
                      <Typography variant="caption" display="block" gutterBottom color="text.secondary">
                        Displaying relationship for the first numeric predictor: {regressionResults.xColumn.name}
                      </Typography>
                    )}

                    <Paper elevation={0} variant="outlined" sx={{ p: 2, borderRadius: 2 }}>
                      <Box height={400}>
                      <ResponsiveContainer width="100%" height="100%">
                        <ComposedChart
                          margin={{
                            top: 20,
                            right: 30,
                            bottom: 60,
                            left: 70,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis
                            type="number"
                            dataKey="x"
                            name={regressionResults.xColumn.name}
                            domain={['dataMin', 'dataMax']}
                            tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                            label={{
                              value: regressionResults.xColumn.name,
                              position: 'insideBottom',
                              offset: -25,
                              style: { fill: theme.palette.text.primary, fontWeight: 'bold', fontSize: '14px' },
                            }}
                          />
                          <YAxis
                            type="number"
                            domain={[0, 1]}
                            tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                            label={{
                              value: `Probability of ${regressionResults.yColumn.name} = 1`,
                              angle: -90,
                              position: 'insideLeft',
                              style: { fill: theme.palette.text.primary, fontWeight: 'bold', fontSize: '14px', textAnchor: 'middle' },
                              dx: -20
                            }}
                          />
                          <RechartsTooltip
                            cursor={{ strokeDasharray: '3 3' }}
                            formatter={(value: any, name: string) => {
                              if (name === 'y') {
                                return [value > 0.5 ? 'Yes/True/1' : 'No/False/0', `Actual ${regressionResults.yColumn.name}`];
                              }
                              if (name === 'predicted') {
                                return [value.toFixed(4), 'Predicted Probability'];
                              }
                              return [value.toFixed(4), name];
                            }}
                            contentStyle={{
                              backgroundColor: theme.palette.background.paper,
                              border: `1px solid ${theme.palette.divider}`,
                              borderRadius: 8,
                              fontSize: '12px'
                            }}
                          />
                          <Legend verticalAlign="top" wrapperStyle={{ paddingBottom: '10px' }}/>

                          {/* Data points (jittered for binary outcome) */}
                          <Scatter
                            name="Observations"
                            dataKey="y"
                            data={regressionResults.scatterData.map((point: any) => ({
                              ...point,
                              // Add small jitter to y to better visualize binary outcomes
                              y: point.y === 1 ? 1 - Math.random() * 0.05 : 0 + Math.random() * 0.05
                            }))}
                            fill={theme.palette.primary.main}
                          />

                          {/* Fitted logistic curve */}
                          {displayOptions.showRegressionCurve && (
                            <Line
                              type="monotone"
                              dataKey="predicted"
                              data={regressionResults.curvePoints}
                              stroke={theme.palette.secondary.main}
                              strokeWidth={2}
                              dot={false}
                              name="Logistic Curve"
                              activeDot={false}
                            />
                          )}

                          {/* Classification threshold */}
                          <ReferenceLine
                            y={0.5}
                            stroke={theme.palette.warning.main}
                            strokeDasharray="3 3"
                            label={{
                              value: 'Classification Threshold (0.5)',
                              position: 'right',
                              fill: theme.palette.warning.main
                            }}
                          />
                        </ComposedChart>
                      </ResponsiveContainer>
                      </Box>
                    </Paper>
                  </Grid>

                  {displayOptions.showROCCurve && (
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <BubbleChartIcon fontSize="small" />
                        ROC Curve
                      </Typography>

                      <Paper elevation={0} variant="outlined" sx={{ p: 2, borderRadius: 2 }}>
                        <Box height={350}>
                        <ResponsiveContainer width="100%" height="100%">
                          <ComposedChart
                            data={regressionResults.rocPoints}
                            margin={{
                              top: 20,
                              right: 30,
                              bottom: 60,
                              left: 70,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis
                              type="number"
                              dataKey="fpr"
                              name="False Positive Rate"
                              domain={[0, 1]}
                              tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                              label={{
                                value: 'False Positive Rate (1 - Specificity)',
                                position: 'insideBottom',
                                offset: -25,
                                style: { fill: theme.palette.text.primary, fontWeight: 'bold', fontSize: '14px' },
                              }}
                            />
                            <YAxis
                              type="number"
                              dataKey="tpr"
                              name="True Positive Rate"
                              domain={[0, 1]}
                              tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                              label={{
                                value: 'True Positive Rate (Sensitivity)',
                                angle: -90,
                                position: 'insideLeft',
                                style: { fill: theme.palette.text.primary, fontWeight: 'bold', fontSize: '14px', textAnchor: 'middle' },
                                dx: -20
                              }}
                            />
                            <RechartsTooltip
                              cursor={{ strokeDasharray: '3 3' }}
                              formatter={(value: any, name: string, props: any) => {
                                if (props.payload.threshold !== undefined) {
                                  const formattedName = name.startsWith('ROC') ? 'Sensitivity' : name;
                                  return [
                                    `${value.toFixed(3)} (${formattedName})`,
                                    `Threshold: ${props.payload.threshold.toFixed(2)}`
                                  ];
                                }
                                return [value.toFixed(3), name];
                              }}
                              contentStyle={{
                                backgroundColor: theme.palette.background.paper,
                                border: `1px solid ${theme.palette.divider}`,
                                borderRadius: 8,
                                fontSize: '12px'
                              }}
                            />
                            <Legend verticalAlign="top" wrapperStyle={{ paddingBottom: '10px' }}/>

                            {/* Reference diagonal line */}
                            <Line
                              type="linear"
                              dataKey="fpr" // Plot y=x line
                              stroke={theme.palette.text.disabled}
                              strokeWidth={1}
                              strokeDasharray="5 5"
                              dot={false}
                              name="Random Chance"
                              legendType="none"
                            />

                            {/* ROC curve */}
                            <Line
                              type="monotone"
                              dataKey="tpr"
                              stroke={theme.palette.success.main}
                              strokeWidth={2}
                              dot={{ r: 2 }}
                              name={`ROC Curve (AUC = ${regressionResults.auc.toFixed(3)})`}
                              activeDot={{ r: 6 }}
                            />
                          </ComposedChart>
                        </ResponsiveContainer>
                        </Box>
                      </Paper>
                    </Grid>
                  )}

                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AnalyticsIcon fontSize="small" />
                      Predicted Probabilities
                    </Typography>

                    <Paper elevation={0} variant="outlined" sx={{ p: 2, borderRadius: 2 }}>
                      <Box height={350}>
                      <ResponsiveContainer width="100%" height="100%">
                        <ComposedChart
                          margin={{
                            top: 20,
                            right: 70,
                            bottom: 60,
                            left: 70,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis
                            type="number"
                            dataKey="x" // Use the bin center for the axis
                            domain={[0, 1]}
                            name="Predicted Probability"
                            tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                            label={{
                              value: 'Predicted Probability',
                              position: 'insideBottom',
                              offset: -25,
                              style: { fill: theme.palette.text.primary, fontWeight: 'bold', fontSize: '14px' },
                            }}
                          />
                          <YAxis
                            yAxisId="left"
                            tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                            label={{
                              value: 'Frequency',
                              angle: -90,
                              position: 'insideLeft',
                              style: { fill: theme.palette.text.primary, fontWeight: 'bold', fontSize: '14px', textAnchor: 'middle' },
                              dx: -20
                            }}
                          />
                          <YAxis
                            yAxisId="right"
                            orientation="right"
                            domain={[0, 1]}
                            tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                            label={{
                              value: 'Actual Class Proportion',
                              angle: 90,
                              position: 'insideRight',
                              style: { fill: theme.palette.text.primary, fontWeight: 'bold', fontSize: '14px', textAnchor: 'middle' },
                              dx: 25
                            }}
                          />
                          <RechartsTooltip
                            contentStyle={{
                              backgroundColor: theme.palette.background.paper,
                              border: `1px solid ${theme.palette.divider}`,
                              borderRadius: 8,
                              fontSize: '12px'
                            }}
                          />
                          <Legend />

                          {/* Create a histogram-like view of predictions */}
                          {(() => {
                            // Group predictions into bins
                            const bins = 10;
                            // Add 'x' key for bin center
                            const histogramData = Array(bins).fill(0).map((_, i) => ({
                              bin: i / bins, // Start of the bin
                              x: (i + 0.5) / bins, // Center of the bin for plotting
                              count: 0,
                              actualProportion: 0,
                              trueCount: 0
                            }));

                            regressionResults.predictions.forEach((p: number, i: number) => {
                              const binIndex = Math.min(Math.floor(p * bins), bins - 1);
                              histogramData[binIndex].count++;
                              // Ensure scatterData exists and has the expected structure
                              if (regressionResults.scatterData && regressionResults.scatterData[i]?.y === 1) {
                                histogramData[binIndex].trueCount++;
                              }
                            });

                            // Calculate actual proportions
                            histogramData.forEach(bin => {
                              bin.actualProportion = bin.count > 0 ? bin.trueCount / bin.count : 0;
                            });

                            return (
                              <>
                                <Bar
                                  yAxisId="left"
                                  dataKey="count"
                                  data={histogramData}
                                  fill={theme.palette.primary.light}
                                  name="Count"
                                  barSize={20}
                                />
                                <Line
                                  yAxisId="right"
                                  type="monotone"
                                  dataKey="actualProportion"
                                  data={histogramData}
                                  stroke={theme.palette.success.main}
                                  strokeWidth={2}
                                  name="Actual Proportion"
                                />
                              </>
                            );
                          })()}
                        </ComposedChart>
                      </ResponsiveContainer>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Classification Results Tab */}
            {tabValue === 2 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Classification Performance
                </Typography>

                {/* Classification Metrics */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AssessmentIcon fontSize="small" />
                    Performance Metrics
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <StatsCard
                        title="Accuracy"
                        value={`${(regressionResults.accuracy * 100).toFixed(1)}%`}
                        description="Overall correct classification rate"
                        color={regressionResults.accuracy > 0.8 ? 'success' : regressionResults.accuracy > 0.7 ? 'warning' : 'error'}
                        variant="gradient"
                        icon={<AssessmentIcon />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <StatsCard
                        title="Precision"
                        value={`${(regressionResults.precision * 100).toFixed(1)}%`}
                        description="Positive predictions that are correct"
                        color={regressionResults.precision > 0.8 ? 'success' : regressionResults.precision > 0.7 ? 'warning' : 'error'}
                        variant="outlined"
                        icon={<BubbleChartIcon />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <StatsCard
                        title="Recall"
                        value={`${(regressionResults.recall * 100).toFixed(1)}%`}
                        description="Actual positives correctly identified"
                        color={regressionResults.recall > 0.8 ? 'success' : regressionResults.recall > 0.7 ? 'warning' : 'error'}
                        variant="outlined"
                        icon={<ShowChartIcon />}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <StatsCard
                        title="F1 Score"
                        value={`${(regressionResults.f1Score * 100).toFixed(1)}%`}
                        description="Harmonic mean of precision and recall"
                        color={regressionResults.f1Score > 0.8 ? 'success' : regressionResults.f1Score > 0.7 ? 'warning' : 'error'}
                        variant="outlined"
                        icon={<AnalyticsIcon />}
                      />
                    </Grid>
                  </Grid>
                </Box>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FunctionsIcon fontSize="small" />
                      Confusion Matrix
                    </Typography>

                    <Paper elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow sx={{ backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)' }}>
                              <TableCell sx={{ fontWeight: 'bold' }}></TableCell>
                              <TableCell colSpan={2} align="center" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                                Predicted
                              </TableCell>
                            </TableRow>
                            <TableRow sx={{ backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)' }}>
                              <TableCell sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>Actual</TableCell>
                              <TableCell align="center" sx={{ fontWeight: 'bold' }}>0</TableCell>
                              <TableCell align="center" sx={{ fontWeight: 'bold' }}>1</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}>
                              <TableCell sx={{ fontWeight: 'medium' }}>0</TableCell>
                              <TableCell align="center" sx={{
                                bgcolor: theme.palette.mode === 'dark' ? 'rgba(76, 175, 80, 0.2)' : 'rgba(76, 175, 80, 0.1)',
                                fontWeight: 'bold',
                                border: `2px solid ${theme.palette.success.main}`,
                                borderRadius: 1
                              }}>
                                {regressionResults.confusionMatrix[0][0]} (TN)
                              </TableCell>
                              <TableCell align="center" sx={{
                                bgcolor: theme.palette.mode === 'dark' ? 'rgba(244, 67, 54, 0.2)' : 'rgba(244, 67, 54, 0.1)',
                                border: `1px solid ${theme.palette.error.main}`,
                                borderRadius: 1
                              }}>
                                {regressionResults.confusionMatrix[0][1]} (FP)
                              </TableCell>
                            </TableRow>
                            <TableRow sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}>
                              <TableCell sx={{ fontWeight: 'medium' }}>1</TableCell>
                              <TableCell align="center" sx={{
                                bgcolor: theme.palette.mode === 'dark' ? 'rgba(244, 67, 54, 0.2)' : 'rgba(244, 67, 54, 0.1)',
                                border: `1px solid ${theme.palette.error.main}`,
                                borderRadius: 1
                              }}>
                                {regressionResults.confusionMatrix[1][0]} (FN)
                              </TableCell>
                              <TableCell align="center" sx={{
                                bgcolor: theme.palette.mode === 'dark' ? 'rgba(76, 175, 80, 0.2)' : 'rgba(76, 175, 80, 0.1)',
                                fontWeight: 'bold',
                                border: `2px solid ${theme.palette.success.main}`,
                                borderRadius: 1
                              }}>
                                {regressionResults.confusionMatrix[1][1]} (TP)
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>

                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                      <strong>Legend:</strong> TN = True Negative, FP = False Positive, FN = False Negative, TP = True Positive
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AnalyticsIcon fontSize="small" />
                      Detailed Metrics
                    </Typography>

                    <Paper elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow sx={{ backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)' }}>
                              <TableCell sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>Metric</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>Value</TableCell>
                              <TableCell sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>Description</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}>
                              <TableCell sx={{ fontWeight: 'medium' }}>Accuracy</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                                {(regressionResults.accuracy * 100).toFixed(2)}%
                              </TableCell>
                              <TableCell>
                                Overall correct classification rate
                              </TableCell>
                            </TableRow>
                            <TableRow sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}>
                              <TableCell sx={{ fontWeight: 'medium' }}>Precision</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.secondary.main }}>
                                {(regressionResults.precision * 100).toFixed(2)}%
                              </TableCell>
                              <TableCell>
                                Proportion of positive predictions that are correct
                              </TableCell>
                            </TableRow>
                            <TableRow sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}>
                              <TableCell sx={{ fontWeight: 'medium' }}>Recall (Sensitivity)</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
                                {(regressionResults.recall * 100).toFixed(2)}%
                              </TableCell>
                              <TableCell>
                                Proportion of actual positives correctly identified
                              </TableCell>
                            </TableRow>
                            <TableRow sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}>
                              <TableCell sx={{ fontWeight: 'medium' }}>Specificity</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.warning.main }}>
                                {(regressionResults.confusionMatrix[0][0] / (regressionResults.confusionMatrix[0][0] + regressionResults.confusionMatrix[0][1]) * 100).toFixed(2)}%
                              </TableCell>
                              <TableCell>
                                Proportion of actual negatives correctly identified
                              </TableCell>
                            </TableRow>
                            <TableRow sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}>
                              <TableCell sx={{ fontWeight: 'medium' }}>F1 Score</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                                {(regressionResults.f1Score * 100).toFixed(2)}%
                              </TableCell>
                              <TableCell>
                                Harmonic mean of precision and recall
                              </TableCell>
                            </TableRow>
                            <TableRow sx={{ '&:hover': { backgroundColor: theme.palette.action.hover } }}>
                              <TableCell sx={{ fontWeight: 'medium' }}>AUC</TableCell>
                              <TableCell align="right" sx={{ fontWeight: 'bold', color: theme.palette.error.main }}>
                                {regressionResults.auc.toFixed(3)}
                              </TableCell>
                              <TableCell>
                                Area under the ROC curve, measures discrimination
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Prediction Tab */}
            {tabValue === 3 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Prediction Tool
                </Typography>

                <Grid container spacing={2} alignItems="flex-start">
                  {(() => {
                    if (!regressionResults || !regressionResults.xColumns) return null;
                    const inputFields: JSX.Element[] = [];
                    const processedOriginalCategoricalVars = new Set<string>();

                    // Calculate number of unique predictors (original categoricals + numerics)
                    // to determine grid sizing
                    const uniquePredictorIds = new Set<string>();
                    regressionResults.xColumns.forEach(col => {
                      if (col.isDummy && col.originalColumnId) {
                        uniquePredictorIds.add(col.originalColumnId);
                      } else if (!col.isDummy) {
                        uniquePredictorIds.add(col.id);
                      }
                    });
                    const numUniquePredictors = uniquePredictorIds.size;
                    // Ensure mdSize is at least 1 to prevent division by zero or excessively small columns
                    const mdSize = Math.max(3, Math.floor(12 / Math.max(1, numUniquePredictors)));

                    regressionResults.xColumns.forEach(col => {
                      // If it's a dummy variable, we need to render a dropdown for its original categorical variable
                      if (col.isDummy && col.originalColumnId && col.originalColumnName && col.allCategories && col.baseCategory) {
                        if (!processedOriginalCategoricalVars.has(col.originalColumnId)) {
                          inputFields.push(
                            <Grid item xs={12} sm={6} md={mdSize} key={col.originalColumnId}>
                              <FormControl fullWidth margin="normal">
                                <InputLabel id={`${col.originalColumnId}-predict-label`}>{col.originalColumnName}</InputLabel>
                                <Select
                                  labelId={`${col.originalColumnId}-predict-label`}
                                  id={`${col.originalColumnId}-predict-select`}
                                  value={predictionInputValues[col.originalColumnId] || ''} // Use originalColumnId as key
                                  label={col.originalColumnName}
                                  onChange={(e) => handlePredictionInputChange(col.originalColumnId!, e.target.value as string)}
                                >
                                  {col.allCategories.map(category => (
                                    <MenuItem key={category} value={category}>
                                      {category} {category === col.baseCategory ? '(Base)' : ''}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                            </Grid>
                          );
                          processedOriginalCategoricalVars.add(col.originalColumnId);
                        }
                      } else if (!col.isDummy) { // Numeric variable, render a TextField
                        inputFields.push(
                          <Grid item xs={12} sm={6} md={mdSize} key={col.id}>
                            <TextField
                              label={`Value for ${col.name}`}
                              value={predictionInputValues[col.id] || ''}
                              onChange={(e) => handlePredictionInputChange(col.id, e.target.value)}
                              fullWidth
                              type="number"
                              margin="normal"
                            />
                          </Grid>
                        );
                      }
                    });
                    return inputFields;
                  })()}

                  <Grid item xs={12} sx={{ mt: (regressionResults?.xColumns?.length || 0) > 0 ? 0 : 2 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<SearchIcon />}
                      onClick={generatePrediction}
                      disabled={!regressionResults || !(() => {
                        if (!regressionResults?.xColumns) return true; // Disabled if no columns
                        let allSet = true;
                        const requiredOriginalCategoricalIds = new Set<string>();

                        // Check if all inputs are provided
                        regressionResults.xColumns.forEach(col => {
                          if (col.isDummy && col.originalColumnId) {
                            requiredOriginalCategoricalIds.add(col.originalColumnId);
                          } else if (!col.isDummy) { // Numeric variable
                            if (predictionInputValues[col.id] === undefined || String(predictionInputValues[col.id]).trim() === '') {
                              allSet = false;
                            }
                          }
                        });

                        // Check original categorical inputs
                        requiredOriginalCategoricalIds.forEach(originalId => {
                          if (predictionInputValues[originalId] === undefined || String(predictionInputValues[originalId]).trim() === '') {
                            allSet = false;
                          }
                        });
                        return allSet;
                      })()}
                      sx={{ mt: 1 }}
                    >
                      Predict Probability
                    </Button>
                  </Grid>

                  {prediction && !prediction.error && (
                    <Grid item xs={12}>
                      <Paper elevation={0} variant="outlined" sx={{ p: 2, mt: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Prediction Result
                        </Typography>
                      <div>
                        <Typography variant="body1" component="div">
                          Input Values:
                          <ul>
                            {prediction.inputs.map((input: {name: string, value: number}) => (
                              <li key={input.name}>{`${input.name} = ${input.value}`}</li>
                            ))}
                          </ul>
                        </Typography>
                      </div>
                        <Typography variant="body1" fontWeight="bold" color={prediction.predictedClass === 1 ? 'success.main' : 'inherit'}>
                          {`Predicted Probability = ${prediction.probability.toFixed(4)}`}
                        </Typography>
                        <Typography variant="body1">
                          {`Predicted Class: ${prediction.predictedClass === 1 ? '1 (Yes)' : '0 (No)'}`}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          95% Confidence Interval: [{prediction.confidenceInterval[0].toFixed(4)}, {prediction.confidenceInterval[1].toFixed(4)}]
                        </Typography>
                        {prediction.intervalNote && (
                          <Typography variant="caption" color="text.secondary" display="block" mt={1}>
                            {prediction.intervalNote}
                          </Typography>
                        )}
                      </Paper>
                    </Grid>
                  )}

                  {prediction && prediction.error && (
                    <Grid item xs={12} md={4}>
                      <Alert severity="error">
                        {prediction.error}
                      </Alert>
                    </Grid>
                  )}
                </Grid>

                {regressionResults && (
                  <Box mt={4}>
                    <Typography variant="subtitle2" gutterBottom>
                      Interpreting Predictions
                    </Typography>
                    <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                      <Typography variant="body2">
                        • A predicted probability of 0.5 or higher means the model predicts class "1" (Yes/True).
                      </Typography>
                      <Typography variant="body2">
                        • A predicted probability below 0.5 means the model predicts class "0" (No/False).
                      </Typography>
                      <Typography variant="body2">
                        • The confidence interval represents uncertainty around the predicted probability.
                      </Typography>
                      <Typography variant="body2">
                        • Predictions far from the range of observed values may be less reliable.
                      </Typography>
                    </Paper>
                  </Box>
                )}
              </Box>
            )}

            {/* Interpretation Tab */}
            {tabValue === 4 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Interpretation
                </Typography>

                <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                    {getInterpretation()}
                  </Typography>
                </Paper>

                <Box mt={4}>
                  <Typography variant="subtitle2" gutterBottom>
                    Key Logistic Regression Concepts
                  </Typography>
                  <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                    <Typography variant="body2" fontWeight="bold">
                      Logit Transformation
                    </Typography>
                    <Typography variant="body2" paragraph>
                      Logistic regression models the log odds (logit) of the outcome as a linear function of the predictors: logit(p) = ln(p/(1-p)) = β₀ + β₁X₁ + ... + βₙXₙ
                    </Typography>

                    <Typography variant="body2" fontWeight="bold">
                      Odds Ratio
                    </Typography>
                    <Typography variant="body2" paragraph>
                      The exponential of a coefficient (e^β) is the odds ratio, which represents how the odds change for a one-unit increase in the predictor. An odds ratio more than 1 indicates increased odds of the outcome, while less than 1 indicates decreased odds.
                    </Typography>

                    <Typography variant="body2" fontWeight="bold">
                      Prediction
                    </Typography>
                    <Typography variant="body2" paragraph>
                      To convert from log odds to probability: p = 1 / (1 + e^(-logit))
                    </Typography>

                    <Typography variant="body2" fontWeight="bold">
                      AUC (Area Under Curve)
                    </Typography>
                    <Typography variant="body2">
                      AUC measures the model's ability to discriminate between positive and negative classes. Values range from 0.5 (no discrimination) to 1.0 (perfect discrimination).
                    </Typography>
                  </Paper>
                </Box>
              </Box>
            )}
          </Paper>
        </>
      )}
    </Box>
  );
};

export default LogisticRegression;
