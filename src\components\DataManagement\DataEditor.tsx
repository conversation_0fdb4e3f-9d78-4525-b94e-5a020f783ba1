import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  IconButton,
  Button,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  Divider,
  SelectChangeEvent,
  Popover,
  Grid,
  useTheme,
  Stack,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  ButtonGroup,
  ToggleButton,
  ToggleButtonGroup,
  Badge,
  alpha,
  Fade,
  Container,
  useMediaQuery,
  Menu,
  ListItemIcon,
  ListItemText,
  InputAdornment,
  Tabs,
  Tab,
  LinearProgress,
  Breadcrumbs,
  Link,
  Checkbox
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  FilterList as FilterIcon,
  SortByAlpha as SortIcon,
  Search as SearchIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  SaveAlt as SaveAltIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  SettingsApplications as SettingsApplicationsIcon,
  ContentPaste as ContentPasteIcon,
  MoreVert as MoreVertIcon,
  ViewColumn as ViewColumnIcon,
  TableChart as TableChartIcon,
  FileDownload as FileDownloadIcon,
  FileUpload as FileUploadIcon,
  Clear as ClearIcon,
  Check as CheckIcon,
  Info as InfoIcon,
  CalendarToday as CalendarIcon,
  TextFields as TextFieldsIcon,
  Numbers as NumbersIcon,
  ToggleOn as ToggleOnIcon,
  Category as CategoryIcon,
  Dataset as DatasetIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  SignalCellularAlt as SignalCellularAltIcon // Added for ordered bars icon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, VariableRole, Dataset, Column, DataValue, DataRow } from '../../types';
import { sortData, filterData } from '../../utils/dataUtilities';
import { isMissingValue } from '../../utils/missingDataUtils';
import { generateUUID } from '../../utils/uuid';
import { parsePastedData } from '../../utils/pasteParser';
import { PastePreviewDialog } from '../PastePreviewDialog';

// Define types
interface SortConfig {
  column: string; 
  direction: 'asc' | 'desc';
}

type FilterOperator = 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith' | 'isEmpty' | 'isNotEmpty';

interface FilterCondition {
  id: string; 
  column: string; 
  operator: FilterOperator;
  value: string; 
  columnType?: DataType;
}

interface ColumnMapping {
  pastedColumn: string;
  targetColumn: string | 'new';
  dataType: DataType;
  createNew: boolean;
}

interface DataEditorProps {
  onGoToVariableEditor?: (datasetId: string) => void;
}

interface RowData {
  [key: string]: DataValue;
  originalIndex: number;
}

// Helper functions
const getOperatorsByDataType = (dataType?: DataType): { value: FilterOperator, label: string }[] => {
  const commonStringOperators: { value: FilterOperator, label: string }[] = [
    { value: 'eq', label: 'Equals' },
    { value: 'neq', label: 'Not Equals' },
    { value: 'contains', label: 'Contains' },
    { value: 'startsWith', label: 'Starts With' },
    { value: 'endsWith', label: 'Ends With' },
  ];
  const commonValueOperators: { value: FilterOperator, label: string }[] = [
    { value: 'isEmpty', label: 'Is Empty' },
    { value: 'isNotEmpty', label: 'Is Not Empty' },
  ];

  switch (dataType) {
    case DataType.NUMERIC:
    case DataType.DATE: 
      return [
        { value: 'eq', label: '=' },
        { value: 'neq', label: '≠' },
        { value: 'gt', label: '>' },
        { value: 'gte', label: '≥' },
        { value: 'lt', label: '<' },
        { value: 'lte', label: '≤' },
        ...commonValueOperators,
      ];
    case DataType.BOOLEAN:
      return [
        { value: 'eq', label: 'Is' },
        { value: 'neq', label: 'Is Not' },
        ...commonValueOperators,
      ];
    case DataType.CATEGORICAL:
    case DataType.TEXT:
    default:
      return [...commonStringOperators, ...commonValueOperators];
  }
};

const operatorRequiresValue = (operator: FilterOperator): boolean => {
  return !['isEmpty', 'isNotEmpty'].includes(operator);
};

const DataEditor: React.FC<DataEditorProps> = ({ onGoToVariableEditor }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { 
    datasets, 
    currentDataset, 
    setCurrentDataset, 
    updateDataset,
    addDataset,
    addRow,
    updateRow,
    removeRow,
    addColumn,
    updateColumn,
    removeColumn
  } = useData();
  
  // State
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
  const [filterConditions, setFilterConditions] = useState<FilterCondition[]>([]);
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [currentFilterColumn, setCurrentFilterColumn] = useState<null | { name: string; type: DataType }>(null);
  const [currentFilterOperator, setCurrentFilterOperator] = useState<FilterOperator>('eq');
  const [currentFilterValue, setCurrentFilterValue] = useState<string>('');
  const [editingCell, setEditingCell] = useState<{
    rowIndex: number;
    columnName: string;
    value: any;
  } | null>(null);
  const [isFullView, setIsFullView] = useState<boolean>(false);
  const [rowDialogOpen, setRowDialogOpen] = useState(false);
  const [newRowData, setNewRowData] = useState<Record<string, any>>({});
  const [columnDialogOpen, setColumnDialogOpen] = useState(false);
  const [columnFormData, setColumnFormData] = useState({
    id: '',
    name: '',
    description: '',
    type: DataType.NUMERIC,
    role: VariableRole.NONE
  });
  const [isEditingColumn, setIsEditingColumn] = useState(false);
  const [showVariableEditorPrompt, setShowVariableEditorPrompt] = useState(false);
  const [pasteDialogOpen, setPasteDialogOpen] = useState(false);
  const [pastedData, setPastedData] = useState<{ headers: string[]; rows: string[][] }>({ 
    headers: [], 
    rows: [] 
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());
  const [actionsMenuAnchor, setActionsMenuAnchor] = useState<null | HTMLElement>(null);
  const [viewMode, setViewMode] = useState<'comfortable' | 'compact'>('comfortable');

  // Memoized data
  const displayedData: RowData[] = useMemo(() => {
    if (!currentDataset) return [];
    let dataToDisplay: RowData[] = currentDataset.data.map((row, index) => ({ ...row, originalIndex: index }));

    console.log('DataEditor: Recalculating displayedData');
    console.log('Current searchTerm:', searchTerm);
    console.log('Initial dataToDisplay length:', dataToDisplay.length);

    // Apply search
    if (searchTerm) {
      const initialLength = dataToDisplay.length;
      dataToDisplay = dataToDisplay.filter(row => {
        return Object.values(row).some(value => 
          String(value).toLowerCase().includes(searchTerm.toLowerCase())
        );
      });
      console.log(`After search (term: "${searchTerm}"): ${dataToDisplay.length} rows (filtered from ${initialLength})`);
    }

    // Apply filtering
    if (filterConditions.length > 0) {
      const initialLength = dataToDisplay.length;
      const activeFilters = filterConditions.filter(fc => fc.column && fc.operator && (fc.operator === 'isEmpty' || fc.operator === 'isNotEmpty' || fc.value !== ''));
      if (activeFilters.length > 0) {
        dataToDisplay = filterData(dataToDisplay as DataRow[], activeFilters.map(fc => ({
          column: fc.column,
          operator: fc.operator,
          value: (fc.operator === 'isEmpty' || fc.operator === 'isNotEmpty') ? undefined : fc.value
        }))) as RowData[];
      }
      console.log(`After filtering (${activeFilters.length} active filters): ${dataToDisplay.length} rows (filtered from ${initialLength})`);
    }

    // Apply sorting
    if (sortConfig) {
      dataToDisplay = sortData(dataToDisplay as DataRow[], [sortConfig]) as RowData[];
      console.log(`After sorting (column: ${sortConfig.column}, direction: ${sortConfig.direction}): ${dataToDisplay.length} rows`);
    }
    console.log('Final displayedData length:', dataToDisplay.length);
    return dataToDisplay;
  }, [currentDataset, sortConfig, filterConditions, searchTerm]);

  // Handlers
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const selectedId = event.target.value;
    const selected = datasets.find(d => d.id === selectedId);
    if (selected) {
      setCurrentDataset(selected);
      setPage(0);
      setSortConfig(null); 
      setFilterConditions([]);
      resetEditing();
      setShowVariableEditorPrompt(false);
      setSearchTerm('');
      setSelectedRows(new Set());
    }
  };
  
  const resetEditing = () => {
    setEditingCell(null);
    setRowDialogOpen(false);
    setNewRowData({});
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
    resetEditing();
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    resetEditing();
  };

  const handleSort = (columnToSort: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.column === columnToSort && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ column: columnToSort, direction });
    setPage(0);
  };

  const handleEditCell = (rowIndexInDisplayedData: number, columnName: string, value: any) => {
    setEditingCell({
      rowIndex: rowIndexInDisplayedData,
      columnName,
      value
    });
  };

  const handleCellValueChange = (value: any) => {
    if (editingCell) {
      setEditingCell({
        ...editingCell,
        value
      });
    }
  };

  const handleCancelCellEdit = () => {
    setEditingCell(null);
  };

  const handleSaveCellEdit = (moveNext: 'down' | 'right' | 'none' = 'none') => {
    if (editingCell && currentDataset) {
      const { rowIndex: rowIndexInDisplayedData, columnName, value } = editingCell;
      
      const column = currentDataset.columns.find(col => col.name === columnName);
      let parsedValue = value;
      
      if (column) {
        switch (column.type) {
          case DataType.NUMERIC:
            parsedValue = value === '' || value === null || isNaN(Number(value)) ? null : Number(value);
            break;
          case DataType.BOOLEAN:
            if (typeof value === 'string') {
              parsedValue = value.toLowerCase() === 'true' || value === '1';
            }
            break;
          case DataType.DATE:
            if (value && typeof value === 'string' && value.trim() !== '' && !isNaN(Date.parse(value))) {
              parsedValue = new Date(value);
            } else if (value instanceof Date && !isNaN(value.getTime())) {
              parsedValue = value;
            } else {
              parsedValue = null;
            }
            break;
          default:
            break;
        }
      }
      
      const originalRowIndex = displayedData[rowIndexInDisplayedData]?.originalIndex;
      if (originalRowIndex !== undefined) {
        updateRow(currentDataset.id, originalRowIndex, { [columnName]: parsedValue });
      }
      
      setEditingCell(null);
    }
  };

  const handleAddRow = () => {
    if (currentDataset) {
      const initialRowData: Record<string, any> = {};
      currentDataset.columns.forEach(column => {
        initialRowData[column.name] = null;
      });
      
      setNewRowData(initialRowData);
      setRowDialogOpen(true);
    }
  };

  const handleNewRowDataChange = (columnName: string, value: any) => {
    setNewRowData(prev => ({
      ...prev,
      [columnName]: value
    }));
  };

  const handleSaveNewRow = () => {
    if (currentDataset) {
      const processedData: Record<string, any> = {};
      
      currentDataset.columns.forEach(column => {
        const value = newRowData[column.name];
        let parsedValue = value;
        
        switch (column.type) {
          case DataType.NUMERIC:
            parsedValue = value === '' || value === null || isNaN(Number(value)) 
              ? null 
              : Number(value);
            break;
          case DataType.BOOLEAN:
            if (typeof value === 'string') {
              parsedValue = value.toLowerCase() === 'true' || value === '1';
            }
            break;
          case DataType.DATE:
            if (value && !isNaN(Date.parse(value))) {
              parsedValue = new Date(value);
            } else {
              parsedValue = null;
            }
            break;
          default:
            break;
        }
        
        processedData[column.name] = parsedValue;
      });
      
      addRow(currentDataset.id, processedData);
      setRowDialogOpen(false);
      resetEditing();
    }
  };

  const handleDeleteRow = (rowIndexInDisplayedData: number) => {
    if (currentDataset) {
      const originalRowIndex = displayedData[rowIndexInDisplayedData]?.originalIndex;
      if (originalRowIndex !== undefined) {
        removeRow(currentDataset.id, originalRowIndex);
      }
    }
  };

  const handleDeleteSelectedRows = () => {
    if (currentDataset && selectedRows.size > 0) {
      const confirmDelete = window.confirm(`Delete ${selectedRows.size} selected row(s)?`);
      if (confirmDelete) {
        const sortedIndices = Array.from(selectedRows).sort((a, b) => b - a);
        sortedIndices.forEach(index => {
          removeRow(currentDataset.id, index);
        });
        setSelectedRows(new Set());
      }
    }
  };

  const toggleRowSelection = (originalIndex: number) => {
    const newSelection = new Set(selectedRows);
    if (newSelection.has(originalIndex)) {
      newSelection.delete(originalIndex);
    } else {
      newSelection.add(originalIndex);
    }
    setSelectedRows(newSelection);
  };

  const selectAllRows = () => {
    if (selectedRows.size === displayedData.length) {
      setSelectedRows(new Set());
    } else {
      setSelectedRows(new Set(displayedData.map(row => row.originalIndex)));
    }
  };

  const handleImportFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        if (text) {
          const parsed = parsePastedData(text);
          if (parsed.rows.length > 0) {
            setPastedData(parsed);
            setPasteDialogOpen(true);
          } else {
            alert('No valid data found in the selected file');
          }
        }
      };
      reader.readAsText(file);
    }
    // Reset the input value so the same file can be selected again
    if (event.target) {
      event.target.value = '';
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent, rowIndexInDisplayedData: number, columnName: string) => {
    if (!editingCell || editingCell.rowIndex !== rowIndexInDisplayedData || editingCell.columnName !== columnName) {
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        event.preventDefault();
        let nextRow = rowIndexInDisplayedData;
        let nextColName = columnName;
        const currentColumnIndex = currentDataset?.columns.findIndex(col => col.name === columnName) ?? -1;

        if (event.key === 'ArrowUp') nextRow = Math.max(0, rowIndexInDisplayedData - 1);
        if (event.key === 'ArrowDown') nextRow = Math.min(displayedData.length - 1, rowIndexInDisplayedData + 1);
        if (event.key === 'ArrowLeft') {
          if (currentColumnIndex > 0) {
            nextColName = currentDataset?.columns[currentColumnIndex - 1].name ?? columnName;
          }
        }
        if (event.key === 'ArrowRight') {
          if (currentDataset && currentColumnIndex < currentDataset.columns.length - 1) {
            nextColName = currentDataset.columns[currentColumnIndex + 1].name;
          }
        }
        
        const cellId = `cell-${nextRow}-${nextColName}`;
        document.getElementById(cellId)?.focus();
      } else if (event.key === 'Enter' || event.key === 'F2') {
        event.preventDefault();
        handleEditCell(rowIndexInDisplayedData, columnName, displayedData[rowIndexInDisplayedData][columnName]);
      }
    }
  };

  // Get data type icon
  const getDataTypeIcon = (type: DataType) => {
    switch (type) {
      case DataType.NUMERIC:
        return <NumbersIcon fontSize="small" />;
      case DataType.TEXT:
        return <TextFieldsIcon fontSize="small" />;
      case DataType.BOOLEAN:
        return <ToggleOnIcon fontSize="small" />;
      case DataType.DATE:
        return <CalendarIcon fontSize="small" />;
      case DataType.CATEGORICAL:
        return <CategoryIcon fontSize="small" />;
      case DataType.ORDINAL:
        return <SignalCellularAltIcon fontSize="small" />; // Using SignalCellularAltIcon for Ordinal
      default:
        return <DatasetIcon fontSize="small" />;
    }
  };

  // Get data type color
  const getDataTypeColor = (type: DataType) => {
    switch (type) {
      case DataType.NUMERIC:
        return theme.palette.info.main;
      case DataType.TEXT:
        return theme.palette.warning.main;
      case DataType.BOOLEAN:
        return theme.palette.success.main;
      case DataType.DATE:
        return theme.palette.error.main;
      case DataType.CATEGORICAL:
      case DataType.ORDINAL:
        return theme.palette.secondary.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const renderCell = (row: RowData, columnName: string, rowIndexInDisplayedData: number) => {
    const value = row[columnName];
    const column = currentDataset?.columns.find(c => c.name === columnName);
    const columnType = column?.type || DataType.TEXT;

    const isEditing = editingCell &&
                     editingCell.rowIndex === rowIndexInDisplayedData &&
                     editingCell.columnName === columnName;

    // Check if value is missing based on user-defined missing codes
    const missingInfo = column ? isMissingValue(value, column) : { isMissing: false, originalValue: value };

    let displayValue = value;

    if (missingInfo.isMissing) {
      displayValue = '';
    } else {
      switch (columnType) {
        case DataType.NUMERIC:
          displayValue = value === null || value === undefined || value === ''
            ? ''
            : (typeof value === 'number' ? (Number.isInteger(value) ? value : value.toFixed(2)) : String(value));
          break;
        case DataType.BOOLEAN:
          displayValue = value === null || value === undefined
            ? ''
            : value ? '✓' : '✗';
          break;
        case DataType.DATE:
          displayValue = value instanceof Date
            ? value.toLocaleDateString()
            : value;
          break;
        default:
          displayValue = value === null || value === undefined ? '' : String(value);
      }
    }
    
    if (isEditing) {
      return (
        <TextField
          value={editingCell?.value ?? ''}
          onChange={(e) => handleCellValueChange(e.target.value)}
          size="small"
          fullWidth
          variant="standard"
          autoFocus
          onBlur={() => handleSaveCellEdit('none')}
          sx={{
            '& .MuiInput-root': {
              fontSize: viewMode === 'compact' ? '0.8rem' : '0.875rem',
            }
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleSaveCellEdit('down');
            } else if (e.key === 'Tab') {
              e.preventDefault();
              handleSaveCellEdit('right');
            } else if (e.key === 'Escape') {
              e.preventDefault();
              handleCancelCellEdit();
            }
          }}
        />
      );
    } else {
      return (
        <Tooltip
          title={missingInfo.isMissing
            ? `Missing value${missingInfo.matchedCode ? ` (${missingInfo.matchedCode})` : ''}`
            : ''
          }
          placement="top"
          arrow
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              height: '100%',
              cursor: 'text',
              padding: viewMode === 'compact' ? '4px 8px' : '8px 12px',
              fontSize: viewMode === 'compact' ? '0.8rem' : '0.875rem',
              color: missingInfo.isMissing
                ? theme.palette.text.disabled
                : columnType === DataType.BOOLEAN
                  ? (value ? theme.palette.success.main : theme.palette.error.main)
                  : 'inherit',
              fontWeight: columnType === DataType.BOOLEAN ? 'bold' : 'normal',
              fontStyle: missingInfo.isMissing ? 'italic' : 'normal',
              bgcolor: missingInfo.isMissing
                ? alpha(theme.palette.warning.main, 0.05)
                : 'transparent',
              border: missingInfo.isMissing
                ? `1px dashed ${alpha(theme.palette.warning.main, 0.3)}`
                : 'none',
              borderRadius: missingInfo.isMissing ? 1 : 0,
              '&:hover': {
                bgcolor: missingInfo.isMissing
                  ? alpha(theme.palette.warning.main, 0.1)
                  : alpha(theme.palette.action.hover, 0.04),
              }
            }}
            onClick={() => handleEditCell(rowIndexInDisplayedData, columnName, value)}
          >
            {missingInfo.isMissing ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Typography variant="caption" color="text.disabled">
                  (missing)
                </Typography>
              </Box>
            ) : (
              displayValue
            )}
          </Box>
        </Tooltip>
      );
    }
  };

  const activeFiltersCount = filterConditions.filter(fc => fc.column && (fc.value !== '' || !operatorRequiresValue(fc.operator))).length;

  // Handle paste
  useEffect(() => {
    const handleGlobalPaste = (e: ClipboardEvent) => {
      const activeElement = document.activeElement;
      if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
        return;
      }
      
      const text = e.clipboardData?.getData('text');
      if (text && currentDataset) {
        e.preventDefault();
        const parsed = parsePastedData(text);
        if (parsed.rows.length > 0) {
          setPastedData(parsed);
          setPasteDialogOpen(true);
        }
      }
    };

    document.addEventListener('paste', handleGlobalPaste);
    return () => document.removeEventListener('paste', handleGlobalPaste);
  }, [currentDataset]);

  const convertValue = (value: string, dataType: DataType): DataValue => {
    if (!value || value === '') return null;

    switch (dataType) {
      case DataType.NUMERIC:
        const num = Number(value);
        return isNaN(num) ? null : num;
        
      case DataType.BOOLEAN:
        const lower = value.toLowerCase();
        return lower === 'true' || lower === '1' || lower === 'yes';
        
      case DataType.DATE:
        const date = new Date(value);
        return isNaN(date.getTime()) ? null : date;
        
      default:
        return value;
    }
  };

  return (
    <Container maxWidth={isFullView ? false : 'xl'} sx={{ py: isFullView ? 0 : 3 }}>
      {!isFullView && (
          <Card sx={{ mb: 1, borderRadius: 2 }}>
            <CardContent sx={{ pt: 0.5, pb: 0.5 }}>
              <Grid container spacing={1} alignItems="center">
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel id="dataset-select-label">Select Dataset</InputLabel>
                    <Select
                      labelId="dataset-select-label"
                      value={currentDataset?.id || ''}
                      label="Select Dataset"
                      onChange={handleDatasetChange}
                      startAdornment={
                        currentDataset && (
                          <InputAdornment position="start">
                            <DatasetIcon />
                          </InputAdornment>
                        )
                      }
                    >
                      {datasets.length === 0 ? (
                        <MenuItem value="" disabled>
                          No datasets available
                        </MenuItem>
                        ) : (
                        datasets.map(dataset => (
                          <MenuItem key={dataset.id} value={dataset.id}>
                            <Box>
                              <Typography variant="body1">{dataset.name}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {dataset.data.length} rows × {dataset.columns.length} columns
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))
                      )}
                    </Select>
                  </FormControl>
                </Grid>
                {currentDataset && (
                  <Grid item xs={12} md={6}>
                    <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                      <Chip
                        icon={<TableChartIcon />}
                        label={`${displayedData.length} rows`}
                        size="small"
                        color={searchTerm || filterConditions.length > 0 ? "primary" : "default"}
                      />
                      <Chip
                        icon={<ViewColumnIcon />}
                        label={`${currentDataset.columns.length} columns`}
                        size="small"
                      />
                      {sortConfig && (
                        <Chip
                          icon={<SortIcon />}
                          label={`Sorted by ${sortConfig.column}`}
                          size="small"
                          color="secondary"
                          onDelete={() => setSortConfig(null)}
                        />
                      )}
                      {activeFiltersCount > 0 && (
                        <Chip
                          icon={<FilterIcon />}
                          label={`${activeFiltersCount} filter${activeFiltersCount > 1 ? 's' : ''}`}
                          size="small"
                          color="secondary"
                          onDelete={() => setFilterConditions([])}
                        />
                      )}
                    </Stack>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
      )}

      {currentDataset ? (
        <Paper elevation={isFullView ? 0 : 2} sx={{ borderRadius: isFullView ? 0 : 2, overflow: 'hidden' }}>
          {/* Optimized Toolbar */}
          <Box
            sx={{
              px: 2,
              py: 1.5,
              bgcolor: 'background.paper',
              borderBottom: 1,
              borderColor: 'divider',
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
            }}
          >
            {/* Single Row Layout for Better Space Utilization */}
            <Stack
              direction={{ xs: 'column', md: 'row' }}
              spacing={{ xs: 1.5, md: 2 }}
              alignItems={{ md: 'center' }}
              justifyContent="space-between"
            >
              {/* Left Section: Search and View Controls */}
              <Stack direction="row" spacing={1.5} alignItems="center" sx={{ minWidth: 0, flex: 1 }}>
                <TextField
                  placeholder="Search in data..."
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon fontSize="small" />
                      </InputAdornment>
                    ),
                    endAdornment: searchTerm && (
                      <InputAdornment position="end">
                        <IconButton size="small" onClick={() => setSearchTerm('')}>
                          <ClearIcon fontSize="small" />
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                  sx={{
                    flexGrow: 1,
                    maxWidth: { xs: '100%', md: 280 },
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'background.default'
                    }
                  }}
                />

                <ToggleButtonGroup
                  value={viewMode}
                  exclusive
                  onChange={(_, newMode) => newMode && setViewMode(newMode)}
                  size="small"
                  sx={{ display: { xs: 'none', sm: 'flex' } }}
                >
                  <ToggleButton value="comfortable" sx={{ px: 1.5 }}>
                    Comfortable
                  </ToggleButton>
                  <ToggleButton value="compact" sx={{ px: 1.5 }}>
                    Compact
                  </ToggleButton>
                </ToggleButtonGroup>
              </Stack>

              {/* Right Section: Action Buttons */}
              <Stack direction="row" spacing={1} alignItems="center" sx={{ flexShrink: 0 }}>
                {/* Data Actions */}
                <ButtonGroup variant="outlined" size="small">
                  <Tooltip title="Add new row">
                    <Button startIcon={<AddIcon />} onClick={handleAddRow} sx={{ px: 1.5 }}>
                      Row
                    </Button>
                  </Tooltip>
                  <Tooltip title="Add new column">
                    <Button startIcon={<AddIcon />} onClick={() => {
                      setColumnFormData({
                        id: '',
                        name: '',
                        description: '',
                        type: DataType.NUMERIC,
                        role: VariableRole.NONE
                      });
                      setIsEditingColumn(false);
                      setColumnDialogOpen(true);
                    }} sx={{ px: 1.5 }}>
                      Column
                    </Button>
                  </Tooltip>
                </ButtonGroup>

                {/* Import/Export Actions */}
                <ButtonGroup variant="outlined" size="small">
                  <Tooltip title="Paste data from clipboard">
                    <Button
                      startIcon={<ContentPasteIcon />}
                      onClick={() => {
                        navigator.clipboard.readText().then(text => {
                          if (text) {
                            const parsed = parsePastedData(text);
                            if (parsed.rows.length > 0) {
                              setPastedData(parsed);
                              setPasteDialogOpen(true);
                            }
                          }
                        }).catch(() => {
                          alert('Unable to read clipboard. Try using Ctrl+V/Cmd+V instead.');
                        });
                      }}
                      sx={{ px: 1.5 }}
                    >
                      Paste
                    </Button>
                  </Tooltip>
                  <input
                    accept=".csv,.tsv,.txt"
                    style={{ display: 'none' }}
                    id="import-file-button"
                    type="file"
                    onChange={handleImportFile}
                  />
                  <label htmlFor="import-file-button">
                    <Tooltip title="Import data from file">
                      <Button component="span" startIcon={<FileUploadIcon />} sx={{ px: 1.5 }}>
                        Import
                      </Button>
                    </Tooltip>
                  </label>
                </ButtonGroup>

                {/* Additional Controls */}
                <Stack direction="row" spacing={0.5} alignItems="center">
                  {onGoToVariableEditor && (
                    <Tooltip title="Manage variables">
                      <IconButton
                        size="small"
                        onClick={() => onGoToVariableEditor(currentDataset.id)}
                        sx={{
                          border: 1,
                          borderColor: 'divider',
                          '&:hover': { borderColor: 'primary.main' }
                        }}
                      >
                        <SettingsApplicationsIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}

                  <Tooltip title={isFullView ? "Exit fullscreen" : "Fullscreen"}>
                    <IconButton
                      size="small"
                      onClick={() => setIsFullView(!isFullView)}
                      sx={{
                        border: 1,
                        borderColor: 'divider',
                        '&:hover': { borderColor: 'primary.main' }
                      }}
                    >
                      {isFullView ? <FullscreenExitIcon fontSize="small" /> : <FullscreenIcon fontSize="small" />}
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Stack>
            </Stack>

            {/* Selected rows actions */}
            {selectedRows.size > 0 && (
              <Alert
                severity="info"
                sx={{ mt: 1.5 }}
                action={
                  <Button
                    color="inherit"
                    size="small"
                    startIcon={<DeleteIcon />}
                    onClick={handleDeleteSelectedRows}
                  >
                    Delete Selected
                  </Button>
                }
              >
                {selectedRows.size} row{selectedRows.size > 1 ? 's' : ''} selected
              </Alert>
            )}
          </Box>

          {/* Data Table */}
          <TableContainer sx={{
            maxHeight: isFullView ? 'calc(100vh - 200px)' : 600,
            '& .MuiTableHead-root': {
              '& .MuiTableRow-root': {
                '& .MuiTableCell-root': {
                  position: 'sticky',
                  top: 0,
                  zIndex: 100,
                  backgroundColor: theme.palette.mode === 'dark'
                    ? theme.palette.grey[800]
                    : theme.palette.grey[100],
                  borderBottom: `2px solid ${theme.palette.divider}`,
                  fontWeight: 600,
                  color: theme.palette.text.primary,
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    bottom: 0,
                    height: '1px',
                    backgroundColor: theme.palette.divider,
                  }
                }
              }
            }
          }}>
            <Table stickyHeader size={viewMode === 'compact' ? 'small' : 'medium'}>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      indeterminate={selectedRows.size > 0 && selectedRows.size < displayedData.length}
                      checked={displayedData.length > 0 && selectedRows.size === displayedData.length}
                      onChange={selectAllRows}
                    />
                  </TableCell>
                  <TableCell>
                    #
                  </TableCell>
                  {currentDataset.columns.map(column => (
                    <TableCell
                      key={column.id}
                      sx={{
                        cursor: 'pointer',
                        minWidth: 120,
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.action.hover, 0.2)
                        }
                      }}
                      onClick={() => handleSort(column.name)}
                    >
                      <Stack direction="row" alignItems="center" spacing={0.5}>
                        {getDataTypeIcon(column.type)}
                        <Box flex={1}>
                          <Typography variant="subtitle2" noWrap>
                            {column.name}
                          </Typography>
                          {/* Conditionally render column type for non-numeric/categorical */}
                          {![DataType.NUMERIC, DataType.CATEGORICAL, DataType.ORDINAL].includes(column.type) && (
                            <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                              {column.type}
                            </Typography>
                          )}
                        </Box>
                        {sortConfig?.column === column.name && (
                          sortConfig.direction === 'asc' ?
                          <ArrowUpwardIcon fontSize="small" color="primary" /> :
                          <ArrowDownwardIcon fontSize="small" color="primary" />
                        )}
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            setFilterAnchorEl(e.currentTarget);
                            setCurrentFilterColumn({ name: column.name, type: column.type });
                            const existingFilter = filterConditions.find(fc => fc.column === column.name);
                            if (existingFilter) {
                              setCurrentFilterOperator(existingFilter.operator);
                              setCurrentFilterValue(existingFilter.value);
                            } else {
                              const defaultOps = getOperatorsByDataType(column.type);
                              setCurrentFilterOperator(defaultOps[0]?.value || 'eq');
                              setCurrentFilterValue('');
                            }
                          }}
                        >
                          <Badge
                            variant="dot"
                            color="secondary"
                            invisible={!filterConditions.some(fc => fc.column === column.name)}
                          >
                            <FilterIcon fontSize="small" />
                          </Badge>
                        </IconButton>
                      </Stack>
                    </TableCell>
                  ))}
                  <TableCell sx={{ bgcolor: 'inherit', fontWeight: 'bold' }}>
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {displayedData
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, index) => {
                    const rowIndex = page * rowsPerPage + index;
                    const isSelected = selectedRows.has(row.originalIndex);
                    
                    return (
                      <TableRow
                        key={row.originalIndex}
                        hover
                        selected={isSelected}
                        sx={{
                          '&:hover': {
                            bgcolor: alpha(theme.palette.action.hover, 0.04)
                          }
                        }}
                      >
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={isSelected}
                            onChange={() => toggleRowSelection(row.originalIndex)}
                          />
                        </TableCell>
                        <TableCell sx={{ fontWeight: 500 }}>
                          {rowIndex + 1}
                        </TableCell>
                        {currentDataset.columns.map(column => (
                          <TableCell
                            key={`${row.originalIndex}-${column.id}`}
                            id={`cell-${rowIndex}-${column.name}`}
                            tabIndex={0}
                            onKeyDown={(e) => handleKeyDown(e, rowIndex, column.name)}
                            sx={{
                              padding: 0,
                              '&:hover': {
                                bgcolor: alpha(theme.palette.action.hover, 0.08)
                              },
                              '&:focus': {
                                outline: `2px solid ${theme.palette.primary.main}`,
                                outlineOffset: '-1px',
                              }
                            }}
                          >
                            {renderCell(row, column.name, rowIndex)}
                          </TableCell>
                        ))}
                        <TableCell>
                          <Tooltip title="Delete Row">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteRow(rowIndex)}
                              color="error"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    );
                  })}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              p: 2,
              borderTop: 1,
              borderColor: 'divider'
            }}
          >
            <Typography variant="body2" color="text.secondary">
              Showing {page * rowsPerPage + 1}-{Math.min((page + 1) * rowsPerPage, displayedData.length)} of {displayedData.length} rows
            </Typography>
            <TablePagination
              component="div"
              count={displayedData.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={[10, 25, 50, 100]}
              labelRowsPerPage={isMobile ? "Rows:" : "Rows per page:"}
            />
          </Box>
        </Paper>
      ) : (
        <Card sx={{ textAlign: 'center', py: 8 }}>
          <TableChartIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
          <Typography variant="h5" gutterBottom color="text.secondary">
            No Dataset Selected
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Select a dataset from the dropdown above to start editing
          </Typography>
        </Card>
      )}

      {/* Actions Menu */}
      <Menu
        anchorEl={actionsMenuAnchor}
        open={Boolean(actionsMenuAnchor)}
        onClose={() => setActionsMenuAnchor(null)}
      >
        <MenuItem onClick={() => {
          if (sortConfig || filterConditions.length > 0) {
            const dataToSave = displayedData.map(({originalIndex, ...row}) => row);
            const newDataset: Dataset = {
              ...currentDataset!,
              id: generateUUID(),
              name: `${currentDataset!.name} (Processed)`,
              data: dataToSave,
              dateCreated: new Date(),
              dateModified: new Date(),
            };
            addDataset(newDataset);
            setCurrentDataset(newDataset);
          }
          setActionsMenuAnchor(null);
        }} disabled={!sortConfig && filterConditions.length === 0}>
          <ListItemIcon>
            <SaveAltIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Save as New Dataset</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {
          setSortConfig(null);
          setFilterConditions([]);
          setSearchTerm('');
          setActionsMenuAnchor(null);
        }}>
          <ListItemIcon>
            <RefreshIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Reset Filters & Sort</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          // Export functionality could go here
          setActionsMenuAnchor(null);
        }}>
          <ListItemIcon>
            <FileDownloadIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Export Data</ListItemText>
        </MenuItem>
      </Menu>

      {/* Filter Popover */}
      <Popover
        open={Boolean(filterAnchorEl)}
        anchorEl={filterAnchorEl}
        onClose={() => {
          setFilterAnchorEl(null);
          setCurrentFilterColumn(null);
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box sx={{ p: 3, minWidth: 320 }}>
          <Typography variant="h6" gutterBottom>
            Filter: {currentFilterColumn?.name}
          </Typography>
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel id="filter-operator-label">Operator</InputLabel>
            <Select
              labelId="filter-operator-label"
              value={currentFilterOperator}
              label="Operator"
              onChange={(e) => setCurrentFilterOperator(e.target.value as FilterOperator)}
            >
              {currentFilterColumn && getOperatorsByDataType(currentFilterColumn.type).map(op => (
                <MenuItem key={op.value} value={op.value}>{op.label}</MenuItem>
              ))}
            </Select>
          </FormControl>

          {operatorRequiresValue(currentFilterOperator) && (
            <TextField
              label="Value"
              value={currentFilterValue}
              onChange={(e) => setCurrentFilterValue(e.target.value)}
              fullWidth
              sx={{ mb: 2 }}
              type={currentFilterColumn?.type === DataType.NUMERIC ? 'number' : 'text'}
            />
          )}

          <Stack direction="row" spacing={1} justifyContent="flex-end">
            <Button
              variant="outlined"
              onClick={() => {
                if (currentFilterColumn) {
                  setFilterConditions(prev => prev.filter(f => f.column !== currentFilterColumn.name));
                }
                setFilterAnchorEl(null);
              }}
            >
              Clear
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                if (currentFilterColumn) {
                  const newFilter: FilterCondition = {
                    id: generateUUID(),
                    column: currentFilterColumn.name,
                    operator: currentFilterOperator,
                    value: currentFilterValue,
                    columnType: currentFilterColumn.type
                  };
                  
                  setFilterConditions(prev => {
                    const existing = prev.filter(f => f.column !== currentFilterColumn.name);
                    return [...existing, newFilter];
                  });
                }
                setFilterAnchorEl(null);
              }}
            >
              Apply
            </Button>
          </Stack>
        </Box>
      </Popover>

      {/* Add Row Dialog */}
      <Dialog 
        open={rowDialogOpen} 
        onClose={() => setRowDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Row</DialogTitle>
        <DialogContent dividers>
          <Stack spacing={2} sx={{ mt: 1 }}>
            {currentDataset?.columns.map(column => (
              <TextField
                key={column.id}
                label={column.name}
                value={newRowData[column.name] || ''}
                onChange={(e) => handleNewRowDataChange(column.name, e.target.value)}
                fullWidth
                type={column.type === DataType.NUMERIC ? 'number' : 'text'}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      {getDataTypeIcon(column.type)}
                    </InputAdornment>
                  )
                }}
                helperText={column.description || `Type: ${column.type}`}
              />
            ))}
          </Stack>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setRowDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained"
            onClick={handleSaveNewRow}
          >
            Add Row
          </Button>
        </DialogActions>
      </Dialog>

      {/* Column Dialog */}
      <Dialog 
        open={columnDialogOpen} 
        onClose={() => setColumnDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {isEditingColumn ? 'Edit Column' : 'Add New Column'}
        </DialogTitle>
        <DialogContent dividers>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Column Name"
              value={columnFormData.name}
              onChange={(e) => setColumnFormData({ ...columnFormData, name: e.target.value })}
              fullWidth
              required
              autoFocus
            />
            
            <TextField
              label="Description (optional)"
              value={columnFormData.description}
              onChange={(e) => setColumnFormData({ ...columnFormData, description: e.target.value })}
              fullWidth
              multiline
              rows={2}
            />
            
            <FormControl fullWidth>
              <InputLabel>Data Type</InputLabel>
              <Select
                value={columnFormData.type}
                label="Data Type"
                onChange={(e) => setColumnFormData({ ...columnFormData, type: e.target.value as DataType })}
              >
                <MenuItem value={DataType.NUMERIC}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <NumbersIcon fontSize="small" />
                    <span>Numeric</span>
                  </Stack>
                </MenuItem>
                <MenuItem value={DataType.TEXT}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <TextFieldsIcon fontSize="small" />
                    <span>Text</span>
                  </Stack>
                </MenuItem>
                <MenuItem value={DataType.BOOLEAN}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <ToggleOnIcon fontSize="small" />
                    <span>Boolean</span>
                  </Stack>
                </MenuItem>
                <MenuItem value={DataType.DATE}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <CalendarIcon fontSize="small" />
                    <span>Date</span>
                  </Stack>
                </MenuItem>
                <MenuItem value={DataType.CATEGORICAL}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <CategoryIcon fontSize="small" />
                    <span>Categorical</span>
                  </Stack>
                </MenuItem>
              </Select>
            </FormControl>
            
            <FormControl fullWidth>
              <InputLabel>Variable Role</InputLabel>
              <Select
                value={columnFormData.role}
                label="Variable Role"
                onChange={(e) => setColumnFormData({ ...columnFormData, role: e.target.value as VariableRole })}
              >
                <MenuItem value={VariableRole.NONE}>None</MenuItem>
                <MenuItem value={VariableRole.INDEPENDENT}>Independent Variable</MenuItem>
                <MenuItem value={VariableRole.DEPENDENT}>Dependent Variable</MenuItem>
                <MenuItem value={VariableRole.COVARIATE}>Covariate</MenuItem>
              </Select>
            </FormControl>
          </Stack>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setColumnDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained"
            onClick={() => {
              if (!currentDataset || !columnFormData.name.trim()) {
                return;
              }
              
              if (isEditingColumn) {
                updateColumn(currentDataset.id, {
                  id: columnFormData.id,
                  name: columnFormData.name.trim(),
                  description: columnFormData.description.trim() || undefined,
                  type: columnFormData.type,
                  role: columnFormData.role
                });
              } else {
                if (currentDataset.columns.some(col => col.name.toLowerCase() === columnFormData.name.trim().toLowerCase())) {
                  alert(`A column named "${columnFormData.name.trim()}" already exists.`);
                  return;
                }
                addColumn(currentDataset.id, {
                  name: columnFormData.name.trim(),
                  description: columnFormData.description.trim() || undefined,
                  type: columnFormData.type,
                  role: columnFormData.role
                });
              }
              
              setColumnDialogOpen(false);
            }}
            disabled={!columnFormData.name.trim()}
          >
            {isEditingColumn ? 'Save Changes' : 'Add Column'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Paste Preview Dialog */}
      <PastePreviewDialog
        open={pasteDialogOpen}
        onClose={() => setPasteDialogOpen(false)}
        pastedHeaders={pastedData.headers}
        pastedRows={pastedData.rows}
        existingColumns={currentDataset?.columns || []}
        onConfirm={async (mappings, mode, datasetName) => {
          if (!currentDataset && mode !== 'newDataset') return;

          try {
            if (mode === 'newDataset') {
              const newColumns: Column[] = mappings.map(mapping => ({
                id: generateUUID(),
                name: mapping.pastedColumn,
                type: mapping.dataType,
                role: VariableRole.NONE
              }));

              const newData = pastedData.rows.map(row => {
                const rowData: Record<string, any> = {};
                mappings.forEach((mapping, index) => {
                  const value = row[index];
                  rowData[mapping.pastedColumn] = convertValue(value, mapping.dataType);
                });
                return rowData;
              });

              const newDataset: Dataset = {
                id: generateUUID(),
                name: datasetName || 'Imported Dataset',
                description: `Imported from clipboard on ${new Date().toLocaleDateString()}`,
                columns: newColumns,
                data: newData,
                dateCreated: new Date(),
                dateModified: new Date()
              };

              await addDataset(newDataset);
              setCurrentDataset(newDataset);
              
            } else {
              const targetDataset = { ...currentDataset! };
              const newColumns: Column[] = [];
              
              mappings.forEach(mapping => {
                if (mapping.createNew) {
                  newColumns.push({
                    id: generateUUID(),
                    name: mapping.pastedColumn,
                    type: mapping.dataType,
                    role: VariableRole.NONE
                  });
                }
              });

              if (newColumns.length > 0) {
                targetDataset.columns = [...targetDataset.columns, ...newColumns];
              }

              const processedRows = pastedData.rows.map(row => {
                const rowData: Record<string, any> = {};
                
                targetDataset.columns.forEach(col => {
                  rowData[col.name] = null;
                });

                mappings.forEach((mapping, index) => {
                  const targetColName = mapping.createNew ? mapping.pastedColumn : mapping.targetColumn;
                  if (targetColName && targetColName !== 'new') {
                    const value = row[index];
                    const targetCol = targetDataset.columns.find(col => col.name === targetColName);
                    rowData[targetColName] = convertValue(value, targetCol?.type || mapping.dataType);
                  }
                });

                return rowData;
              });

              if (mode === 'replace') {
                targetDataset.data = processedRows;
              } else {
                targetDataset.data = [...targetDataset.data, ...processedRows];
              }

              targetDataset.dateModified = new Date();
              updateDataset(targetDataset);
            }

            setPasteDialogOpen(false);
            setPastedData({ headers: [], rows: [] });
            
          } catch (error) {
            console.error('Error importing data:', error);
            alert('Error importing data. Please check your data format and try again.');
          }
        }}
      />
    </Container>
  );
};

export default DataEditor;
