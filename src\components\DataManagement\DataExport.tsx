import React, { useState } from 'react';
import { 
  Box, 
  <PERSON>ton, 
  <PERSON>pography, 
  Card, 
  CardContent, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  FormControlLabel, 
  Checkbox, 
  Alert, 
  Divider,
  SelectChangeEvent
} from '@mui/material';
import { 
  FileDownload as FileDownloadIcon,
  Code as CodeIcon,
  TableChart as TableChartIcon,
  GridOn as GridOnIcon // Icon for Excel
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { useAuth } from '../../context/AuthContext'; // Import useAuth
import { exportCSV, exportExcel } from '../../utils/dataUtilities';

const DataExport: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const { isGuest } = useAuth(); // Get guest status
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [exportOptions, setExportOptions] = useState({
    includeHeaders: true,
    selectedColumns: [] as string[],
    exportAllColumns: true,
  });
  const [error, setError] = useState<string | null>(null);
  
  // Find the selected dataset
  const selectedDataset = datasets.find(dataset => dataset.id === selectedDatasetId);
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    setSelectedDatasetId(event.target.value);
    setExportOptions({
      ...exportOptions,
      selectedColumns: [],
    });
  };
  
  // Handle column selection change
  const handleColumnSelection = (columnId: string) => {
    setExportOptions(prev => {
      const selectedColumns = prev.selectedColumns.includes(columnId)
        ? prev.selectedColumns.filter(id => id !== columnId)
        : [...prev.selectedColumns, columnId];
      
      return {
        ...prev,
        selectedColumns,
      };
    });
  };
  
  // Handle export all columns toggle
  const handleExportAllColumnsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setExportOptions({
      ...exportOptions,
      exportAllColumns: event.target.checked,
      selectedColumns: event.target.checked ? [] : exportOptions.selectedColumns,
    });
  };
  
  // Export data as CSV
  const handleExportCSV = () => {
    if (!selectedDataset) {
      setError('No dataset selected for export');
      return;
    }
    
    try {
      // Create a new dataset with only selected columns if needed
      let dataToExport = selectedDataset;
      
      if (!exportOptions.exportAllColumns && exportOptions.selectedColumns.length > 0) {
        const selectedColumnIds = exportOptions.selectedColumns;
        const selectedColumnNames = selectedDataset.columns
          .filter(col => selectedColumnIds.includes(col.id))
          .map(col => col.name);
        
        // Create new dataset with only selected columns
        dataToExport = {
          ...selectedDataset,
          data: selectedDataset.data.map(row => {
            const newRow: Record<string, any> = {};
            selectedColumnNames.forEach(colName => {
              newRow[colName] = row[colName];
            });
            return newRow;
          }),
        };
      }
      
      // Export to CSV
      const csvContent = exportCSV(dataToExport);
      
      // Create a download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${selectedDataset.name}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setError(null);
    } catch (err) {
      setError(`Export failed: ${err instanceof Error ? err.message : String(err)}`);
    }
  };
  
  // Export data as JSON
  const handleExportJSON = () => {
    if (!selectedDataset) {
      setError('No dataset selected for export');
      return;
    }
    
    try {
      // Create a new dataset with only selected columns if needed
      let dataToExport = selectedDataset.data;
      
      if (!exportOptions.exportAllColumns && exportOptions.selectedColumns.length > 0) {
        const selectedColumnIds = exportOptions.selectedColumns;
        const selectedColumnNames = selectedDataset.columns
          .filter(col => selectedColumnIds.includes(col.id))
          .map(col => col.name);
        
        // Create new dataset with only selected columns
        dataToExport = selectedDataset.data.map(row => {
          const newRow: Record<string, any> = {};
          selectedColumnNames.forEach(colName => {
            newRow[colName] = row[colName];
          });
          return newRow;
        });
      }
      
      // Convert to JSON
      const jsonContent = JSON.stringify(dataToExport, null, 2);
      
      // Create a download link
      const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${selectedDataset.name}.json`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setError(null);
    } catch (err) {
      setError(`Export failed: ${err instanceof Error ? err.message : String(err)}`);
    }
  };

  // Export data as Excel
  const handleExportExcel = () => {
    if (!selectedDataset) {
      setError('No dataset selected for export');
      return;
    }

    try {
      let dataToExport = selectedDataset;

      if (!exportOptions.exportAllColumns && exportOptions.selectedColumns.length > 0) {
        const selectedColumnNames = selectedDataset.columns
          .filter(col => exportOptions.selectedColumns.includes(col.id))
          .map(col => col.name);
        
        dataToExport = {
          ...selectedDataset,
          columns: selectedDataset.columns.filter(col => exportOptions.selectedColumns.includes(col.id)), // Keep only selected columns definitions
          data: selectedDataset.data.map(row => {
            const newRow: Record<string, any> = {};
            selectedColumnNames.forEach(colName => {
              newRow[colName] = row[colName];
            });
            return newRow;
          }),
        };
      }
      // Note: The exportExcel utility itself handles creating a sheet from dataset.data.
      // If includeHeaders is false, the utility would need modification, or data pre-processing here.
      // For now, assuming headers are always included based on the data structure passed.
      exportExcel(dataToExport, `${selectedDataset.name}.xlsx`);
      
      setError(null);
    } catch (err) {
      setError(`Export failed: ${err instanceof Error ? err.message : String(err)}`);
    }
  };
  
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Export Data
      </Typography>
      
      <Card elevation={2} sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Select Dataset to Export
          </Typography>
          
          <FormControl fullWidth margin="normal">
            <InputLabel id="dataset-select-label">Dataset</InputLabel>
            <Select
              labelId="dataset-select-label"
              id="dataset-select"
              value={selectedDatasetId}
              label="Dataset"
              onChange={handleDatasetChange}
              disabled={datasets.length === 0}
            >
              {datasets.length === 0 ? (
                <MenuItem value="" disabled>
                  No datasets available
                </MenuItem>
              ) : (
                datasets.map(dataset => (
                  <MenuItem key={dataset.id} value={dataset.id}>
                    {dataset.name} ({dataset.data.length} rows, {dataset.columns.length} columns)
                  </MenuItem>
                ))
              )}
            </Select>
          </FormControl>

          {isGuest && datasets.length > 0 && (
            <Alert severity="warning" sx={{ mt: 2, mb: 2 }}>
              You are exploring as a Guest. Exported data reflects the current session only and is not saved permanently.
            </Alert>
          )}
          
          {selectedDataset && (
            <>
              <Box mt={3}>
                <Typography variant="subtitle1" gutterBottom>
                  Export Options
                </Typography>
                
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={exportOptions.includeHeaders}
                      onChange={e => setExportOptions({...exportOptions, includeHeaders: e.target.checked})}
                    />
                  }
                  label="Include column headers"
                />
                
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={exportOptions.exportAllColumns}
                      onChange={handleExportAllColumnsChange}
                    />
                  }
                  label="Export all columns"
                />
              </Box>
              
              {!exportOptions.exportAllColumns && (
                <Box mt={2}>
                  <Typography variant="subtitle2" gutterBottom>
                    Select Columns to Export
                  </Typography>
                  
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedDataset.columns.map(column => (
                      <FormControlLabel
                        key={column.id}
                        control={
                          <Checkbox
                            checked={exportOptions.selectedColumns.includes(column.id)}
                            onChange={() => handleColumnSelection(column.id)}
                            size="small"
                          />
                        }
                        label={column.name}
                      />
                    ))}
                  </Box>
                </Box>
              )}
              
              <Divider sx={{ my: 3 }} />
              
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<FileDownloadIcon />}
                  onClick={handleExportCSV}
                  disabled={!selectedDataset || (!exportOptions.exportAllColumns && exportOptions.selectedColumns.length === 0)}
                >
                  Export as CSV
                </Button>
                
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<CodeIcon />}
                  onClick={handleExportJSON}
                  disabled={!selectedDataset || (!exportOptions.exportAllColumns && exportOptions.selectedColumns.length === 0)}
                >
                  Export as JSON
                </Button>

                <Button
                  variant="outlined"
                  color="success" // Using success color for Excel
                  startIcon={<GridOnIcon />}
                  onClick={handleExportExcel}
                  disabled={!selectedDataset || (!exportOptions.exportAllColumns && exportOptions.selectedColumns.length === 0)}
                >
                  Export as Excel
                </Button>
              </Box>
            </>
          )}
          
          {datasets.length === 0 && (
            <Alert severity="info" sx={{ mt: 2 }}>
              No datasets available for export. Please import or create a dataset first.
            </Alert>
          )}
          
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default DataExport;
