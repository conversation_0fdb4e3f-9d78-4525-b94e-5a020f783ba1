import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Grid,
  Chip,
  Alert,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormLabel,
  Divider,
  Stack,
  Tooltip
} from '@mui/material';
import {
  FilterList as FilterIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Clear as ClearIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { FilterCondition, DataType } from '../../types';

const DataFilter: React.FC = () => {
  const {
    currentDataset,
    activeFilters,
    filterLogic,
    clearFilters,
    applyFilters,
    isFilteredDataset,
    originalDataset,
    getFilteredRowCount,
    getTotalRowCount
  } = useData();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [tempFilters, setTempFilters] = useState<FilterCondition[]>([]);
  const [tempLogic, setTempLogic] = useState<'AND' | 'OR'>('AND');

  // Initialize temp filters when dialog opens
  useEffect(() => {
    if (isDialogOpen) {
      setTempFilters(activeFilters.length > 0 ? [...activeFilters] : [createEmptyFilter()]);
      setTempLogic(filterLogic);
    }
  }, [isDialogOpen, activeFilters, filterLogic]);

  const createEmptyFilter = (): FilterCondition => ({
    column: '',
    operator: 'eq',
    value: ''
  });

  const getOperatorsForColumn = (columnName: string) => {
    // Use original dataset for column information if available
    const sourceDataset = originalDataset || currentDataset;
    if (!sourceDataset) return [];

    const column = sourceDataset.columns.find(col => col.name === columnName);
    if (!column) return [];

    const commonOps = [
      { value: 'isEmpty', label: 'Is Empty' },
      { value: 'isNotEmpty', label: 'Is Not Empty' }
    ];

    switch (column.type) {
      case DataType.NUMERIC:
        return [
          { value: 'eq', label: 'Equals' },
          { value: 'neq', label: 'Not Equals' },
          { value: 'gt', label: 'Greater Than' },
          { value: 'gte', label: 'Greater Than or Equal' },
          { value: 'lt', label: 'Less Than' },
          { value: 'lte', label: 'Less Than or Equal' },
          { value: 'between', label: 'Between' },
          { value: 'notBetween', label: 'Not Between' },
          ...commonOps
        ];
      case DataType.CATEGORICAL:
      case DataType.TEXT:
        return [
          { value: 'eq', label: 'Equals' },
          { value: 'neq', label: 'Not Equals' },
          { value: 'contains', label: 'Contains' },
          { value: 'startsWith', label: 'Starts With' },
          { value: 'endsWith', label: 'Ends With' },
          { value: 'in', label: 'Is In List' },
          { value: 'notIn', label: 'Is Not In List' },
          ...commonOps
        ];
      default:
        return [
          { value: 'eq', label: 'Equals' },
          { value: 'neq', label: 'Not Equals' },
          ...commonOps
        ];
    }
  };

  const addFilter = () => {
    setTempFilters([...tempFilters, createEmptyFilter()]);
  };

  const removeFilter = (index: number) => {
    if (tempFilters.length > 1) {
      setTempFilters(tempFilters.filter((_, i) => i !== index));
    }
  };

  const updateFilter = (index: number, field: keyof FilterCondition, value: any) => {
    const newFilters = [...tempFilters];
    newFilters[index] = { ...newFilters[index], [field]: value };
    
    // Reset operator when column changes
    if (field === 'column') {
      newFilters[index].operator = 'eq';
      newFilters[index].value = '';
      newFilters[index].value2 = undefined;
      newFilters[index].values = undefined;
    }
    
    setTempFilters(newFilters);
  };

  const handleApplyFilters = () => {
    const validFilters = tempFilters.filter(filter =>
      filter.column && filter.operator &&
      (filter.operator === 'isEmpty' || filter.operator === 'isNotEmpty' ||
       filter.value !== '' || filter.values?.length ||
       (filter.operator === 'between' || filter.operator === 'notBetween') && filter.value2 !== '')
    );

    applyFilters(validFilters, tempLogic);
    setIsDialogOpen(false);
  };

  const handleClearFilters = () => {
    clearFilters();
    setIsDialogOpen(false);
  };

  const renderValueInput = (filter: FilterCondition, index: number) => {
    if (filter.operator === 'isEmpty' || filter.operator === 'isNotEmpty') {
      return null;
    }

    if (filter.operator === 'between' || filter.operator === 'notBetween') {
      return (
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <TextField
              label="From"
              type="number"
              value={filter.value || ''}
              onChange={(e) => updateFilter(index, 'value', e.target.value)}
              size="small"
              fullWidth
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              label="To"
              type="number"
              value={filter.value2 || ''}
              onChange={(e) => updateFilter(index, 'value2', e.target.value)}
              size="small"
              fullWidth
            />
          </Grid>
        </Grid>
      );
    }

    if (filter.operator === 'in' || filter.operator === 'notIn') {
      return (
        <TextField
          label="Values (comma-separated)"
          value={filter.values?.join(', ') || ''}
          onChange={(e) => {
            const values = e.target.value.split(',').map(v => v.trim()).filter(v => v);
            updateFilter(index, 'values', values);
          }}
          size="small"
          fullWidth
          helperText="Enter values separated by commas"
        />
      );
    }

    const sourceDataset = originalDataset || currentDataset;
    const column = sourceDataset?.columns.find(col => col.name === filter.column);
    const inputType = column?.type === DataType.NUMERIC ? 'number' : 'text';

    return (
      <TextField
        label="Value"
        type={inputType}
        value={filter.value || ''}
        onChange={(e) => updateFilter(index, 'value', e.target.value)}
        size="small"
        fullWidth
      />
    );
  };

  const getFilterSummary = () => {
    if (activeFilters.length === 0) return 'No filters applied';

    // Use original dataset for column information if available
    const sourceDataset = originalDataset || currentDataset;

    const summaries = activeFilters.map(filter => {
      const column = sourceDataset?.columns.find(col => col.name === filter.column);
      const operatorLabel = getOperatorsForColumn(filter.column).find(op => op.value === filter.operator)?.label || filter.operator;

      let valueText = '';
      if (filter.operator === 'between' || filter.operator === 'notBetween') {
        valueText = ` ${filter.value} and ${filter.value2}`;
      } else if (filter.operator === 'in' || filter.operator === 'notIn') {
        valueText = ` [${filter.values?.join(', ')}]`;
      } else if (filter.operator !== 'isEmpty' && filter.operator !== 'isNotEmpty') {
        valueText = ` "${filter.value}"`;
      }

      return `${column?.name || filter.column} ${operatorLabel}${valueText}`;
    });

    return summaries.join(` ${filterLogic} `);
  };

  if (!currentDataset) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Data Filter
          </Typography>
          <Alert severity="info">
            Please select a dataset to apply filters.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              Data Filter
            </Typography>
            <Stack direction="row" spacing={1}>
              <Button
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={() => setIsDialogOpen(true)}
              >
                {activeFilters.length > 0 ? 'Edit Filters' : 'Add Filters'}
              </Button>
              {activeFilters.length > 0 && (
                <Button
                  variant="outlined"
                  color="secondary"
                  startIcon={<ClearIcon />}
                  onClick={clearFilters}
                >
                  Clear All
                </Button>
              )}
            </Stack>
          </Box>

          {/* Filter Status */}
          <Box mb={2}>
            <Typography variant="body2" color="textSecondary">
              {isFilteredDataset ? (
                <>
                  <strong>Active Filters:</strong> {getFilterSummary()}
                </>
              ) : (
                'No filters applied - showing all data'
              )}
            </Typography>
          </Box>

          {/* Dataset Status Display */}
          <Box display="flex" alignItems="center" gap={2}>
            <Chip
              icon={isFilteredDataset ? <VisibilityIcon /> : <VisibilityOffIcon />}
              label={`Showing ${getFilteredRowCount()} of ${getTotalRowCount()} rows`}
              color={isFilteredDataset ? 'primary' : 'default'}
              variant={isFilteredDataset ? 'filled' : 'outlined'}
            />
            {isFilteredDataset && (
              <Chip
                label="Filtered Dataset Active"
                color="warning"
                variant="outlined"
                size="small"
              />
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Filter Dialog */}
      <Dialog
        open={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Configure Data Filters</DialogTitle>
        <DialogContent>
          <Box mb={3}>
            <FormControl component="fieldset">
              <FormLabel component="legend">Logic Between Conditions</FormLabel>
              <RadioGroup
                row
                value={tempLogic}
                onChange={(e) => setTempLogic(e.target.value as 'AND' | 'OR')}
              >
                <FormControlLabel value="AND" control={<Radio />} label="AND (all conditions must be true)" />
                <FormControlLabel value="OR" control={<Radio />} label="OR (any condition can be true)" />
              </RadioGroup>
            </FormControl>
          </Box>

          <Divider sx={{ mb: 2 }} />

          <List>
            {tempFilters.map((filter, index) => (
              <ListItem key={index} sx={{ flexDirection: 'column', alignItems: 'stretch', mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Column</InputLabel>
                      <Select
                        value={filter.column}
                        onChange={(e) => updateFilter(index, 'column', e.target.value)}
                      >
                        {(originalDataset || currentDataset)?.columns.map(col => (
                          <MenuItem key={col.id} value={col.name}>
                            {col.name} ({col.type})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Operator</InputLabel>
                      <Select
                        value={filter.operator}
                        onChange={(e) => updateFilter(index, 'operator', e.target.value)}
                        disabled={!filter.column}
                      >
                        {getOperatorsForColumn(filter.column).map(op => (
                          <MenuItem key={op.value} value={op.value}>
                            {op.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={5}>
                    {renderValueInput(filter, index)}
                  </Grid>
                  
                  <Grid item xs={12} sm={1}>
                    <Tooltip title="Remove filter">
                      <IconButton
                        onClick={() => removeFilter(index)}
                        disabled={tempFilters.length <= 1}
                        size="small"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Grid>
                </Grid>
              </ListItem>
            ))}
          </List>

          <Button
            startIcon={<AddIcon />}
            onClick={addFilter}
            variant="outlined"
            sx={{ mt: 1 }}
          >
            Add Filter Condition
          </Button>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleClearFilters} color="secondary">
            Clear All Filters
          </Button>
          <Button onClick={handleApplyFilters} variant="contained" color="primary">
            Apply Filters
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DataFilter;
