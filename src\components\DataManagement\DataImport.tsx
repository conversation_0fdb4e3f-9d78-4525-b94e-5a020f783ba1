import React, { useState, useRef, useCallback } from 'react';
import GoogleSheetsImport from './GoogleSheetsImport';
import {
  Box,
  Button,
  Typography,
  Paper,
  Card,
  CardContent,
  FormControl,
  FormControlLabel,
  Checkbox,
  TextField,
  CircularProgress,
  Alert,
  Divider,
  Stack,
  useTheme,
  Collapse,
  IconButton,
  Link,
  Chip,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Fade,
  Tooltip,
  Grid,
  CardActions,
  CardHeader,
  Avatar,
  LinearProgress,
  Container,
  alpha
} from '@mui/material';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  UploadFile as UploadFileIcon,
  TableChart as TableChartIcon,
  AutoAwesome as AutoAwesomeIcon,
  HealthAndSafety as HealthAndSafetyIcon,
  CreditCard as CreditCardIcon,
  Login as LoginIcon,
  ExpandMore as ExpandMoreIcon,
  PeopleAlt as PeopleAltIcon,
  School as SchoolIcon,
  CloudUpload as CloudUploadIcon,
  GetApp as GetAppIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  Psychology as PsychologyIcon,
  Timeline as TimelineIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Visibility as VisibilityIcon,
  Close as CloseIcon,
  FileUpload as FileUploadIcon,
  Description as DescriptionIcon,
  InsertDriveFile as InsertDriveFileIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { importCSV, importExcel, importJSON } from '../../utils/dataUtilities';
import { generateUUID } from '../../utils/uuid';
import {
  generateHealthDataset,
  generateLogisticRegressionDataset,
  generateEmployeeSatisfactionDataset,
  generateStudentPerformanceDataset,
  generateReliabilityDataset,
  generateFactorAnalysisDataset,
  generateSurvivalAnalysisDataset,
  generateMediationModerationDataset,
  generateMetaAnalysisDataset,
  generateClusterAnalysisDataset
} from '../../utils/sampleDataGenerator';
import { DataType, VariableRole, Column } from '../../types';
import { useAuth } from '../../context/AuthContext';
import { PastePreviewDialog } from '../PastePreviewDialog';
import { parsePastedData, ParsedData } from '../../utils/pasteParser';

interface ColumnMapping {
  pastedColumn: string;
  targetColumn: string | 'new';
  dataType: DataType;
  createNew: boolean;
}

interface DataImportProps {
  onImportSuccess?: () => void;
}

const DataImport: React.FC<DataImportProps> = ({ onImportSuccess }) => {
  const { addDataset } = useData();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const theme = useTheme();
  const navigate = useNavigate();

  const { user, isGuest, canAccessSampleData, canImportData, loginAsGuest } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loadingDataset, setLoadingDataset] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const [pasteDialogOpen, setPasteDialogOpen] = useState(false);
  const [pastedData, setPastedData] = useState<ParsedData>({
    headers: [],
    rows: []
  });

  const [importOptions, setImportOptions] = useState({
    hasHeader: true,
    skipEmptyLines: true,
    dynamicTyping: true,
  });

  // Sample datasets configuration
  const sampleDatasets = [
    {
      id: 'health',
      name: 'Health Dataset',
      description: 'Medical research data with patient outcomes and treatment effectiveness',
      icon: <HealthAndSafetyIcon />,
      color: '#e91e63',
      bgColor: alpha('#e91e63', 0.1),
      handler: handleGenerateHealthDataset,
      tags: ['Medical', 'Research', 'Outcomes'],
      rows: 500,
      variables: 12
    },
    {
      id: 'credit',
      name: 'Credit Risk Dataset',
      description: 'Financial data for credit scoring and risk assessment models',
      icon: <CreditCardIcon />,
      color: '#2196f3',
      bgColor: alpha('#2196f3', 0.1),
      handler: handleGenerateCreditRiskDataset,
      tags: ['Finance', 'Risk', 'Classification'],
      rows: 1000,
      variables: 15
    },
    {
      id: 'employee',
      name: 'Employee Satisfaction',
      description: 'HR survey data with performance metrics and satisfaction scores',
      icon: <PeopleAltIcon />,
      color: '#4caf50',
      bgColor: alpha('#4caf50', 0.1),
      handler: handleGenerateEmployeeSatisfactionDataset,
      tags: ['HR', 'Survey', 'Performance'],
      rows: 300,
      variables: 10
    },
    {
      id: 'student',
      name: 'Student Performance',
      description: 'Academic performance data with demographic and socioeconomic factors',
      icon: <SchoolIcon />,
      color: '#ff9800',
      bgColor: alpha('#ff9800', 0.1),
      handler: handleGenerateStudentPerformanceDataset,
      tags: ['Education', 'Performance', 'Demographics'],
      rows: 400,
      variables: 14
    },
    {
      id: 'reliability',
      name: 'Reliability Analysis',
      description: 'Scale reliability data for internal consistency testing',
      icon: <PsychologyIcon />,
      color: '#9c27b0',
      bgColor: alpha('#9c27b0', 0.1),
      handler: handleGenerateReliabilityDataset,
      tags: ['Psychology', 'Reliability', 'Scale'],
      rows: 250,
      variables: 20
    },
    {
      id: 'factor',
      name: 'Factor Analysis',
      description: 'Multivariate data designed for factor extraction and analysis',
      icon: <AnalyticsIcon />,
      color: '#00bcd4',
      bgColor: alpha('#00bcd4', 0.1),
      handler: handleGenerateFactorAnalysisDataset,
      tags: ['Multivariate', 'Factor', 'Dimension'],
      rows: 300,
      variables: 25
    },
    {
      id: 'survival',
      name: 'Survival Analysis',
      description: 'Time-to-event data with censoring for survival modeling',
      icon: <TimelineIcon />,
      color: '#795548',
      bgColor: alpha('#795548', 0.1),
      handler: handleGenerateSurvivalAnalysisDataset,
      tags: ['Survival', 'Time-to-Event', 'Censoring'],
      rows: 200,
      variables: 8
    },
    {
      id: 'mediation',
      name: 'Mediation/Moderation',
      description: 'Complex relationship modeling data for advanced statistical analysis',
      icon: <AnalyticsIcon />,
      color: '#607d8b',
      bgColor: alpha('#607d8b', 0.1),
      handler: handleGenerateMediationModerationDataset,
      tags: ['Mediation', 'Moderation', 'Relationships'],
      rows: 350,
      variables: 12
    },
    {
      id: 'meta',
      name: 'Meta Analysis',
      description: 'Study-level data with effect sizes and confidence intervals for meta-analysis',
      icon: <AnalyticsIcon />,
      color: '#3f51b5',
      bgColor: alpha('#3f51b5', 0.1),
      handler: handleGenerateMetaAnalysisDataset,
      tags: ['Meta-Analysis', 'Effect Size', 'Studies'],
      rows: 30,
      variables: 12
    },
    {
      id: 'cluster',
      name: 'Cluster Analysis',
      description: 'Customer segmentation data with multiple numeric variables for clustering',
      icon: <PeopleAltIcon />,
      color: '#ff5722',
      bgColor: alpha('#ff5722', 0.1),
      handler: handleGenerateClusterAnalysisDataset,
      tags: ['Clustering', 'Segmentation', 'Multivariate'],
      rows: 400,
      variables: 8
    }
  ];

  // Drag and drop handlers
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (!canImportData) {
      setError("Please login to import data. Guest users can only use sample datasets.");
      return;
    }

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      handleFileChange({ target: { files: [file] } } as any);
    }
  }, [canImportData]);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!canImportData) {
      setError("Please login to import data. Guest users can only use sample datasets.");
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    if (fileExtension === 'csv' || fileExtension === 'txt') {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        if (text) {
          const parsed = parsePastedData(text);
          if (parsed.rows.length > 0) {
            setPastedData(parsed);
            setPasteDialogOpen(true);
          } else {
            setError('No valid data found in the selected file');
          }
        } else {
           setError('Could not read file content.');
        }
        setLoading(false);
      };
      reader.onerror = () => {
        setError('Error reading file.');
        setLoading(false);
      };
      reader.readAsText(file);
    } else if (fileExtension === 'xls' || fileExtension === 'xlsx' || fileExtension === 'json') {
       handleDirectImport(file);
    } else {
      setError(`Unsupported file type: .${fileExtension}. Please use CSV, TXT, XLS, XLSX, or JSON.`);
      setLoading(false);
    }
  };

  const handleDirectImport = async (file: File) => {
     setLoading(true);
     setError(null);
     setSuccess(null);
     try {
       let dataset;
       const fileExtension = file.name.split('.').pop()?.toLowerCase();

       if (fileExtension === 'xls' || fileExtension === 'xlsx') {
         dataset = await importExcel(file);
       } else if (fileExtension === 'json') {
         dataset = await importJSON(file);
       } else {
          throw new Error(`Unsupported file type for direct import: .${fileExtension}.`);
       }

       const status = await addDataset(dataset);
       setLoading(false);

       if (typeof status === 'string' && (status === 'added' || status === 'selected')) {
         setSuccess(`Successfully imported "${dataset.name}" with ${dataset.data.length} rows!`);
         if (fileInputRef.current) {
           fileInputRef.current.value = '';
         }
         if (onImportSuccess) {
           onImportSuccess();
         }
       }
     } catch (err) {
       setError(`Failed to import data: ${err instanceof Error ? err.message : String(err)}`);
       setLoading(false);
     }
  };

  const handleImportConfirm = async (
    mappings: ColumnMapping[],
    mode: 'append' | 'replace' | 'newDataset',
    datasetName?: string
  ) => {
     setPasteDialogOpen(false);
     setLoading(true);
     setError(null);
     setSuccess(null);

     try {
       const importedRows = pastedData.rows.map(row => {
         const rowData: Record<string, any> = {};
         mappings.forEach((mapping, index) => {
           const targetColName = mapping.createNew ? mapping.pastedColumn : mapping.targetColumn;
           if (targetColName && targetColName !== 'new') {
             const value = row[index];
             rowData[targetColName] = convertValue(value, mapping.dataType);
           }
         });
         return rowData;
       });

       const newColumns: Column[] = mappings
         .filter(mapping => mapping.createNew)
         .map(mapping => ({
           id: generateUUID(),
           name: mapping.pastedColumn,
           type: mapping.dataType,
           role: VariableRole.NONE
         }));

       if (mode === 'newDataset') {
         const newDataset = {
           id: generateUUID(),
           name: datasetName || 'Imported File Data',
           description: `Imported from file on ${new Date().toLocaleDateString()}`,
           columns: newColumns,
           data: importedRows,
           dateCreated: new Date(),
           dateModified: new Date()
         };
         await addDataset(newDataset);
         setSuccess(`Successfully imported ${importedRows.length} rows as new dataset!`);
       } else {
         console.error("Dataset update/append logic needs to be integrated with useData context.");
         setError("Dataset update/append logic not fully implemented yet.");
         setLoading(false);
         return;
       }

       setLoading(false);
       if (fileInputRef.current) {
         fileInputRef.current.value = '';
       }
       if (onImportSuccess) {
         onImportSuccess();
       }

     } catch (err) {
       setError(`Failed to import data after preview: ${err instanceof Error ? err.message : String(err)}`);
       setLoading(false);
     }
  };

  const convertValue = (value: string, dataType: DataType): any => {
    if (!value || value === '') return null;

    switch (dataType) {
      case DataType.NUMERIC:
        const num = Number(value);
        return isNaN(num) ? null : num;
      case DataType.BOOLEAN:
        const lower = value.toLowerCase();
        return lower === 'true' || lower === '1' || lower === 'yes';
      case DataType.DATE:
        const date = new Date(value);
        return isNaN(date.getTime()) ? null : date;
      default:
        return value;
    }
  };

  // Sample dataset handlers
  const SAMPLE_DATA_SEED = 12345; // Fixed seed for reproducible sample datasets

  async function handleGenerateHealthDataset() {
    await generateSampleDataset('health', () => generateHealthDataset(SAMPLE_DATA_SEED), 'Health Dataset');
  }

  async function handleGenerateCreditRiskDataset() {
    await generateSampleDataset('credit', () => generateLogisticRegressionDataset(SAMPLE_DATA_SEED), 'Credit Risk Dataset');
  }

  async function handleGenerateEmployeeSatisfactionDataset() {
    await generateSampleDataset('employee', () => generateEmployeeSatisfactionDataset(SAMPLE_DATA_SEED), 'Employee Satisfaction Dataset');
  }

  async function handleGenerateStudentPerformanceDataset() {
    await generateSampleDataset('student', () => generateStudentPerformanceDataset(SAMPLE_DATA_SEED), 'Student Performance Dataset');
  }

  async function handleGenerateReliabilityDataset() {
    await generateSampleDataset('reliability', () => generateReliabilityDataset(SAMPLE_DATA_SEED), 'Reliability Analysis Dataset');
  }

  async function handleGenerateFactorAnalysisDataset() {
    await generateSampleDataset('factor', () => generateFactorAnalysisDataset(SAMPLE_DATA_SEED), 'Factor Analysis Dataset');
  }

  async function handleGenerateSurvivalAnalysisDataset() {
    await generateSampleDataset('survival', () => generateSurvivalAnalysisDataset(SAMPLE_DATA_SEED), 'Survival Analysis Dataset');
  }

  async function handleGenerateMediationModerationDataset() {
    await generateSampleDataset('mediation', () => generateMediationModerationDataset(SAMPLE_DATA_SEED), 'Mediation/Moderation Dataset');
  }

  async function handleGenerateMetaAnalysisDataset() {
    await generateSampleDataset('meta', () => generateMetaAnalysisDataset(SAMPLE_DATA_SEED), 'Meta Analysis Dataset');
  }

  async function handleGenerateClusterAnalysisDataset() {
    await generateSampleDataset('cluster', () => generateClusterAnalysisDataset(SAMPLE_DATA_SEED), 'Cluster Analysis Dataset');
  }

  const generateSampleDataset = async (id: string, generator: () => any, name: string) => {
    if (!canAccessSampleData) {
      setError("Please login or continue as guest to access sample datasets.");
      return;
    }
    setLoadingDataset(id);
    setError(null);
    setSuccess(null);
    try {
      const dataset = generator();
      const datasetToAdd = { ...dataset, dateCreated: new Date(), dateModified: new Date() };
      const status = await addDataset(datasetToAdd);
      setLoadingDataset(null);
      if (typeof status === 'string' && (status === 'added' || status === 'selected')) {
        setSuccess(`Successfully loaded "${name}" with ${dataset.data.length} rows!`);
        if (onImportSuccess) onImportSuccess();
      }
    } catch (err) {
      setError(`Failed to generate ${name}: ${err instanceof Error ? err.message : String(err)}`);
      setLoadingDataset(null);
    }
  };

  const handleGuestContinue = () => {
    loginAsGuest();
    navigate('/app');
  };

  const handleClickUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box mb={4} textAlign="center">
        <Typography variant="h4" gutterBottom color="primary" fontWeight="bold">
          Import Your Data
        </Typography>
        <Typography variant="subtitle1" color="textSecondary" maxWidth="600px" mx="auto">
          Get started by importing your own data or exploring with our sample datasets
        </Typography>
      </Box>

      {/* Status Messages */}
      <Stack spacing={2} mb={3}>
        {loading && (
          <Fade in={loading}>
            <Box>
              <LinearProgress />
            </Box>
          </Fade>
        )}

        {error && (
          <Fade in={!!error}>
            <Alert 
              severity="error" 
              onClose={() => setError(null)}
              action={
                <IconButton size="small" color="inherit" onClick={() => setError(null)}>
                  <CloseIcon fontSize="small" />
                </IconButton>
              }
            >
              {error}
            </Alert>
          </Fade>
        )}

        {success && (
          <Fade in={!!success}>
            <Alert 
              severity="success"
              onClose={() => setSuccess(null)}
              action={
                <IconButton size="small" color="inherit" onClick={() => setSuccess(null)}>
                  <CloseIcon fontSize="small" />
                </IconButton>
              }
            >
              {success}
            </Alert>
          </Fade>
        )}
      </Stack>

      {/* User Status Card */}
      {!user && (
        <Card sx={{ mb: 4, borderRadius: 2, overflow: 'hidden' }}>
          <Box
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              color: 'white',
              p: 3,
            }}
          >
            <Stack direction="row" alignItems="center" spacing={2}>
              <InfoIcon fontSize="large" />
              <Box flex={1}>
                <Typography variant="h6" gutterBottom>
                  Get Full Access to All Features
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  {isGuest 
                    ? "You're currently exploring as a guest. Login to import your own data and save your work."
                    : "Login to analyze your own data (from files or Google Sheets), or continue as a guest to explore the app with sample datasets only."}
                </Typography>
              </Box>
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1}>
                <Button
                  variant="contained"
                  color="inherit"
                  component={RouterLink}
                  to="/auth/login"
                  startIcon={<LoginIcon />}
                  sx={{ 
                    bgcolor: 'white', 
                    color: theme.palette.primary.main,
                    '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
                  }}
                >
                  Login
                </Button>
                {!isGuest && (
                  <Button
                    variant="outlined"
                    color="inherit"
                    onClick={handleGuestContinue}
                    sx={{ 
                      borderColor: 'white',
                      color: 'white',
                      '&:hover': { 
                        borderColor: 'white',
                        bgcolor: 'rgba(255,255,255,0.1)' 
                      }
                    }}
                  >
                    Continue as Guest
                  </Button>
                )}
              </Stack>
            </Stack>
          </Box>
        </Card>
      )}

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Left Column - Import Options */}
        <Grid item xs={12} md={5}>
          <Stack spacing={3}>
            {/* File Upload Card */}
            <Card sx={{ borderRadius: 2, overflow: 'hidden' }}>
              <CardHeader
                avatar={<Avatar sx={{ bgcolor: theme.palette.primary.main }}><UploadFileIcon /></Avatar>}
                title="Import from File"
                subheader="Upload CSV, Excel, or JSON files"
              />
              <CardContent>
                <Box
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                  sx={{
                    border: `2px dashed ${dragActive ? theme.palette.primary.main : theme.palette.divider}`,
                    borderRadius: 2,
                    p: 4,
                    textAlign: 'center',
                    bgcolor: dragActive ? alpha(theme.palette.primary.main, 0.05) : 'background.default',
                    cursor: canImportData ? 'pointer' : 'not-allowed',
                    transition: 'all 0.3s ease',
                    opacity: canImportData ? 1 : 0.5,
                  }}
                  onClick={canImportData ? handleClickUpload : undefined}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".csv,.txt,.xls,.xlsx,.json"
                    style={{ display: 'none' }}
                    onChange={handleFileChange}
                  />
                  <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    {dragActive ? 'Drop your file here' : 'Drag & Drop or Click to Upload'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Supported formats: CSV, TXT, XLS, XLSX, JSON
                  </Typography>
                  {!canImportData && (
                    <Typography variant="caption" color="error" display="block" mt={1}>
                      Login required for file upload
                    </Typography>
                  )}
                </Box>

                {/* Import Options */}
                <Collapse in={canImportData}>
                  <Box mt={3}>
                    <Typography variant="subtitle2" gutterBottom>
                      Import Options
                    </Typography>
                    <Stack spacing={1}>
                      <FormControlLabel
                        control={
                          <Checkbox 
                            checked={importOptions.hasHeader} 
                            onChange={e => setImportOptions({...importOptions, hasHeader: e.target.checked})}
                            size="small"
                          />
                        }
                        label={<Typography variant="body2">First row contains headers</Typography>}
                      />
                      <FormControlLabel
                        control={
                          <Checkbox 
                            checked={importOptions.skipEmptyLines} 
                            onChange={e => setImportOptions({...importOptions, skipEmptyLines: e.target.checked})}
                            size="small"
                          />
                        }
                        label={<Typography variant="body2">Skip empty lines</Typography>}
                      />
                      <FormControlLabel
                        control={
                          <Checkbox 
                            checked={importOptions.dynamicTyping} 
                            onChange={e => setImportOptions({...importOptions, dynamicTyping: e.target.checked})}
                            size="small"
                          />
                        }
                        label={<Typography variant="body2">Auto-detect data types</Typography>}
                      />
                    </Stack>
                  </Box>
                </Collapse>
              </CardContent>
            </Card>

            {/* Google Sheets Card */}
            <Card sx={{ borderRadius: 2, overflow: 'hidden', opacity: canImportData ? 1 : 0.6 }}>
              <CardHeader
                avatar={<Avatar sx={{ bgcolor: theme.palette.success.main }}><TableChartIcon /></Avatar>}
                title="Import from Google Sheets"
                subheader="Connect to your Google Sheets data"
              />
              <CardContent>
                <GoogleSheetsImport onImportSuccess={onImportSuccess} disabled={!canImportData} />
              </CardContent>
            </Card>
          </Stack>
        </Grid>

        {/* Right Column - Sample Datasets */}
        <Grid item xs={12} md={7}>
          <Card sx={{ borderRadius: 2, overflow: 'hidden' }}>
            <CardHeader
              avatar={<Avatar sx={{ bgcolor: theme.palette.secondary.main }}><AutoAwesomeIcon /></Avatar>}
              title="Sample Datasets"
              subheader="Pre-configured datasets for exploration and testing"
            />
            <CardContent>
              <Grid container spacing={2}>
                {sampleDatasets.map((dataset) => (
                  <Grid item xs={12} sm={6} key={dataset.id}>
                    <Card
                      variant="outlined"
                      sx={{
                        height: '100%',
                        transition: 'all 0.3s ease',
                        cursor: 'pointer',
                        bgcolor: loadingDataset === dataset.id ? dataset.bgColor : 'background.paper',
                        borderColor: loadingDataset === dataset.id ? dataset.color : 'divider',
                        '&:hover': {
                          bgcolor: dataset.bgColor,
                          borderColor: dataset.color,
                          transform: 'translateY(-2px)',
                          boxShadow: 2,
                        }
                      }}
                      onClick={() => !loading && !loadingDataset && dataset.handler()}
                    >
                      <CardContent>
                        <Stack spacing={2}>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Avatar 
                              sx={{ 
                                bgcolor: dataset.bgColor,
                                color: dataset.color,
                                width: 48,
                                height: 48
                              }}
                            >
                              {dataset.icon}
                            </Avatar>
                            <Box flex={1}>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {dataset.name}
                              </Typography>
                              <Stack direction="row" spacing={1} alignItems="center">
                                <Typography variant="caption" color="textSecondary">
                                  {dataset.rows} rows
                                </Typography>
                                <Typography variant="caption" color="textSecondary">•</Typography>
                                <Typography variant="caption" color="textSecondary">
                                  {dataset.variables} variables
                                </Typography>
                              </Stack>
                            </Box>
                          </Box>
                          
                          <Typography variant="body2" color="textSecondary">
                            {dataset.description}
                          </Typography>
                          
                          <Box display="flex" flexWrap="wrap" gap={0.5}>
                            {dataset.tags.map(tag => (
                              <Chip
                                key={tag}
                                label={tag}
                                size="small"
                                sx={{
                                  bgcolor: alpha(dataset.color, 0.1),
                                  color: dataset.color,
                                  fontSize: '0.7rem',
                                  height: 20
                                }}
                              />
                            ))}
                          </Box>

                          {loadingDataset === dataset.id && (
                            <LinearProgress 
                              sx={{ 
                                position: 'absolute',
                                bottom: 0,
                                left: 0,
                                right: 0,
                                bgcolor: 'transparent',
                                '& .MuiLinearProgress-bar': {
                                  bgcolor: dataset.color
                                }
                              }}
                            />
                          )}
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {!canAccessSampleData && (
                <Box mt={3} p={2} bgcolor={alpha(theme.palette.warning.main, 0.1)} borderRadius={1}>
                  <Typography variant="body2" color="warning.main" align="center">
                    Login or continue as guest to access sample datasets
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Import Preview Dialog */}
      <PastePreviewDialog
        open={pasteDialogOpen}
        onClose={() => setPasteDialogOpen(false)}
        pastedHeaders={pastedData.headers}
        pastedRows={pastedData.rows}
        existingColumns={[]}
        onConfirm={handleImportConfirm}
      />
    </Container>
  );
};

export default DataImport;
