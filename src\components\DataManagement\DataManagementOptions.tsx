import React, { useState } from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import {
  CloudUpload as ImportIcon,
  CloudDownload as ExportIcon,
  Edit as EditIcon,
  Transform as TransformIcon,
  ListAlt as ListIcon,
  EditNote as VariableEditorIcon, // Import icon for Variable Editor
  Info as InfoIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';

interface DataManagementOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Import' | 'Export' | 'Edit' | 'Transform' | 'Manage';
  color: string;
}

interface DataManagementOptionsProps {
  onNavigate: (path: string) => void;
}

export const dataManagementOptions: DataManagementOption[] = [
  {
    name: 'Data Import',
    shortDescription: 'Import data from various sources',
    detailedDescription: 'Import your datasets from local files (CSV, Excel, etc.) or connect to external sources like Google Sheets to begin your analysis.',
    path: 'data-management/import',
    icon: <ImportIcon />,
    category: 'Import',
    color: '#4CAF50', // Green
  },
  {
    name: 'Data Export',
    shortDescription: 'Export your processed data',
    detailedDescription: 'Save your cleaned and transformed datasets to various file formats for use in other applications or for sharing.',
    path: 'data-management/export',
    icon: <ExportIcon />,
    category: 'Export',
    color: '#2196F3', // Blue
  },
  {
    name: 'Data Editor',
    shortDescription: 'View and edit your datasets',
    detailedDescription: 'Inspect and modify your data directly within the application. Make corrections, add new entries, or adjust variable properties.',
    path: 'data-management/editor',
    icon: <EditIcon />,
    category: 'Edit',
    color: '#FF9800', // Orange
  },
  {
    name: 'Data Transform',
    shortDescription: 'Apply transformations to your data',
    detailedDescription: 'Perform common data transformations such as creating new variables, recoding existing ones, or handling missing values.',
    path: 'data-management/transform',
    icon: <TransformIcon />,
    category: 'Transform',
    color: '#9C27B0', // Purple
  },
  {
    name: 'Dataset Manager',
    shortDescription: 'Manage your imported datasets',
    detailedDescription: 'View a list of all your imported datasets, rename them, or remove them from your workspace.',
    path: 'data-management/datasets',
    icon: <ListIcon />,
    category: 'Manage',
    color: '#795548', // Brown
  },
  {
    name: 'Variable Editor',
    shortDescription: 'Edit variable properties',
    detailedDescription: 'Modify variable names, types, labels, and other properties.',
    path: 'data-management/variable-editor',
    icon: <VariableEditorIcon />,
    category: 'Edit',
    color: '#00BCD4', // Cyan
  },
];

const DataManagementOptions: React.FC<DataManagementOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const categories = ['All', 'Import', 'Export', 'Edit', 'Transform', 'Manage'];

  const filteredOptions = selectedCategory === 'All'
    ? dataManagementOptions
    : dataManagementOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Import': return <ImportIcon />;
      case 'Export': return <ExportIcon />;
      case 'Edit': return <EditIcon />;
      case 'Transform': return <TransformIcon />;
      case 'Manage': return <ListIcon />;
      default: return <ImportIcon />;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Data Management Tools
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Tools for importing, cleaning, transforming, and managing your data
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Prepare your data for analysis with a comprehensive suite of data management tools.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography variant="h6" fontWeight="bold">
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {option.shortDescription}
                </Typography>

                <Typography variant="body2" paragraph>
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Bringing data in?</strong> Use Data Import
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Saving your work?</strong> Use Data Export
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Correcting values?</strong> Use the Data Editor
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Modifying variables?</strong> Use Data Transform
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Modifying variable properties?</strong> Use the Variable Editor
            </Typography>
             <Typography variant="body2" color="text.secondary">
              • <strong>Viewing/deleting datasets?</strong> Use the Dataset Manager
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default DataManagementOptions;
