import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Alert,
  IconButton,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  SelectChangeEvent,
  Stepper,
  Step,
  StepLabel,
  Tooltip,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormLabel,
  Stack,
  Collapse,
  Fade
} from '@mui/material';
import {
  Transform as TransformIcon,
  Delete as DeleteIcon,
  InfoOutlined as InfoIcon,
  AutoGraph as AutoGraphIcon,
  FunctionsOutlined as FunctionsIcon,
  Add as AddIcon,
  ArrowForward as ArrowForwardIcon,
  Check as CheckIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import {
  recodeValues,
  standardizeValues,
  logTransform,
  binValues,
  createDummyVariables,
  computeVariable
} from '../../utils/dataUtilities';
import { DataType, Column, TransformationType, Transformation, VariableRole } from '../../types';
import { generateUUID } from '../../utils/uuid';

const DataTransform: React.FC = () => {
  const { datasets, currentDataset, updateDataset } = useData();
  
  // State management
  const [selectedDatasetId, setSelectedDatasetId] = useState<string | null>(null);
  const [selectedColumnId, setSelectedColumnId] = useState<string | null>(null);
  const [transformationType, setTransformationType] = useState<TransformationType>(TransformationType.STANDARDIZE);
  const [transformationResult, setTransformationResult] = useState<string | null>(null);
  const [outputTarget, setOutputTarget] = useState<'current' | 'new'>('new');
  const [newVariableName, setNewVariableName] = useState<string>('');

  // Dialog states
  const [isRecodeOpen, setIsRecodeOpen] = useState(false);
  const [recodeMappings, setRecodeMappings] = useState<{ from: string; to: string }[]>([
    { from: '', to: '' }
  ]);
  const [isLogOpen, setIsLogOpen] = useState(false);
  const [logBase, setLogBase] = useState(10);
  const [isBinningOpen, setIsBinningOpen] = useState(false);
  const [numBins, setNumBins] = useState(5);
  const [isComputeOpen, setIsComputeOpen] = useState(false);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [computeMethod, setComputeMethod] = useState<'sum' | 'mean' | 'count' | 'std' | 'min' | 'max'>('sum');

  // Derived states
  const selectedDataset = datasets.find(d => d.id === selectedDatasetId);
  const selectedColumn = selectedDataset?.columns.find(col => col.id === selectedColumnId);
  const selectedColumnType = selectedColumn?.type;
  const selectedColumnName = selectedColumn?.name;

  // Effect to synchronize selectedDatasetId with currentDataset from DataContext
  useEffect(() => {
    if (currentDataset) {
      setSelectedDatasetId(currentDataset.id);
    } else {
      setSelectedDatasetId(null);
      setSelectedColumnId(null); // Clear column selection if no dataset
    }
  }, [currentDataset]);

  // Effect to synchronize selectedColumnId with the first column of the selected dataset
  useEffect(() => {
    if (selectedDataset && selectedDataset.columns.length > 0) {
      // Only update if the currently selected column is not in the new dataset
      // or if no column is selected
      if (!selectedColumn || !selectedDataset.columns.some(col => col.id === selectedColumn.id)) {
        setSelectedColumnId(selectedDataset.columns[0].id);
      }
    } else {
      setSelectedColumnId(null);
    }
  }, [selectedDataset, selectedColumn]); // Depend on selectedDataset and selectedColumn to avoid unnecessary resets

  // Effect to update newVariableName suggestion when selectedColumnName or transformationType changes
  useEffect(() => {
    setNewVariableName(getNewVariableNameSuggestion());
  }, [selectedColumnName, transformationType]);

  // Helper functions
  const getNewVariableNameSuggestion = () => {
    if (!selectedColumnName) return '';
    return `${selectedColumnName}_${transformationType.toLowerCase()}`;
  };

  // Determine applicable transformation types based on selected column type
  const getApplicableTransformationTypes = (columnType: DataType | undefined): TransformationType[] => {
    if (!columnType) return [];

    const numericTransformations = [
      TransformationType.STANDARDIZE,
      TransformationType.LOG,
      TransformationType.SQUARE_ROOT,
      TransformationType.BINNING,
      TransformationType.COMPUTE_VARIABLE,
    ];

    const categoricalTransformations = [
      TransformationType.RECODE,
      TransformationType.DUMMY_CODING,
    ];

    switch (columnType) {
      case DataType.NUMERIC:
        return [...numericTransformations, ...categoricalTransformations]; // Numeric can also be recoded/dummy coded
      case DataType.CATEGORICAL:
      case DataType.TEXT:
      case DataType.BOOLEAN:
        return categoricalTransformations;
      default:
        return [];
    }
  };

  const applicableTransformationTypes = getApplicableTransformationTypes(selectedColumnType);

  // Effect to reset transformationType if the current one is no longer applicable
  useEffect(() => {
    if (selectedColumnType && !applicableTransformationTypes.includes(transformationType)) {
      // Set to a default applicable type, e.g., RECODE if available, otherwise the first one
      if (applicableTransformationTypes.includes(TransformationType.RECODE)) {
        setTransformationType(TransformationType.RECODE);
      } else if (applicableTransformationTypes.length > 0) {
        setTransformationType(applicableTransformationTypes[0]);
      } else {
        setTransformationType(TransformationType.STANDARDIZE); // Fallback or disable button
      }
    }
  }, [selectedColumnType, applicableTransformationTypes, transformationType]);


  // Dataset selection
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    setSelectedDatasetId(event.target.value);
    setSelectedColumnId(null);
    setTransformationResult(null);
  };

  // Column selection
  const handleColumnChange = (event: SelectChangeEvent<string>) => {
    setSelectedColumnId(event.target.value);
    setNewVariableName(getNewVariableNameSuggestion());
    setTransformationResult(null);
  };

  // Transformation type selection
  const handleTransformationTypeChange = (event: SelectChangeEvent<TransformationType>) => {
    setTransformationType(event.target.value as TransformationType);
    setNewVariableName(getNewVariableNameSuggestion());
  };

  // Output target selection
  const handleOutputTargetChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setOutputTarget(event.target.value as 'current' | 'new');
  };

  // Core transformation logic
  const performTransformationLogic = async (
    type: TransformationType,
    currentLogBase: number,
    currentNumBins: number,
    currentRecodeMappings: { from: string; to: string }[]
  ) => {
    if (!selectedDataset || !selectedColumn) return;

    try {
      const column = selectedColumn;
      const columnName = column.name;

      // Validate new variable name
      if (outputTarget === 'new' && !newVariableName) {
        throw new Error('New variable name is required');
      }

      // Validate existing name conflict
      if (outputTarget === 'new' && selectedDataset.columns.some(col => 
        col.name.toLowerCase() === newVariableName.toLowerCase()
      )) {
        throw new Error('Variable name already exists');
      }

      let finalTransformedData: any[] = selectedDataset.data;
      let transformationDescription = '';
      let parameters: Record<string, any> = {};
      let newColumnType: DataType = DataType.NUMERIC;

      switch (type) {
        case TransformationType.STANDARDIZE: {
          const tempTransformedData = standardizeValues(selectedDataset.data, columnName);
          transformationDescription = 'Standardized (Z-Score)';
          parameters = {};
          if (outputTarget === 'new') {
            finalTransformedData = selectedDataset.data.map((originalRow, index) => ({
              ...originalRow,
              [newVariableName]: tempTransformedData[index][columnName]
            }));
          } else {
            finalTransformedData = tempTransformedData;
          }
          break;
        }
        case TransformationType.LOG: {
          const tempTransformedData = logTransform(selectedDataset.data, columnName, currentLogBase);
          transformationDescription = `Log (base ${currentLogBase})`;
          parameters = { base: currentLogBase };
          if (outputTarget === 'new') {
            finalTransformedData = selectedDataset.data.map((originalRow, index) => ({
              ...originalRow,
              [newVariableName]: tempTransformedData[index][columnName]
            }));
          } else {
            finalTransformedData = tempTransformedData;
          }
          break;
        }
        case TransformationType.SQUARE_ROOT: {
          const tempTransformedData = selectedDataset.data.map(row => ({
            ...row,
            [columnName]: Math.sqrt(row[columnName] as number)
          }));
          transformationDescription = 'Square Root';
          parameters = {};
          if (outputTarget === 'new') {
            finalTransformedData = selectedDataset.data.map((originalRow, index) => ({
              ...originalRow,
              [newVariableName]: tempTransformedData[index][columnName]
            }));
          } else {
            finalTransformedData = tempTransformedData;
          }
          break;
        }
        case TransformationType.RECODE: {
          const mapping = currentRecodeMappings.reduce((acc, { from, to }) => {
            if (from) acc[from] = to;
            return acc;
          }, {} as Record<string, string>);
          const tempTransformedData = recodeValues(selectedDataset.data, columnName, mapping);
          transformationDescription = 'Recode';
          parameters = { mapping };
          if (outputTarget === 'new') {
            finalTransformedData = selectedDataset.data.map((originalRow, index) => ({
              ...originalRow,
              [newVariableName]: tempTransformedData[index][columnName]
            }));
          } else {
            finalTransformedData = tempTransformedData;
          }
          break;
        }
        case TransformationType.BINNING: {
          const tempTransformedData = binValues(selectedDataset.data, columnName, currentNumBins);
          transformationDescription = `Binned into ${currentNumBins} categories`;
          parameters = { numBins: currentNumBins };
          newColumnType = DataType.CATEGORICAL;
          if (outputTarget === 'new') {
            finalTransformedData = selectedDataset.data.map((originalRow, index) => ({
              ...originalRow,
              [newVariableName]: tempTransformedData[index][columnName]
            }));
          } else {
            finalTransformedData = tempTransformedData;
          }
          break;
        }
        case TransformationType.DUMMY_CODING:
          const { newData, newColumns } = createDummyVariables(selectedDataset.data, columnName);
          finalTransformedData = newData;
          transformationDescription = 'Dummy Coded';
          parameters = { originalColumn: columnName };
          const updatedColumnsForDummy = [
            ...selectedDataset.columns,
            ...newColumns.map((col) => ({
              id: generateUUID(),
              name: col.name,
              type: DataType.NUMERIC,
              role: column.role,
              description: `Dummy variable for ${columnName}`,
              transformations: [{
                id: generateUUID(),
                name: transformationDescription,
                type: TransformationType.DUMMY_CODING,
                parameters,
                original: column
              }]
            }))
          ];
          updateDataset({
            ...selectedDataset!,
            data: finalTransformedData,
            columns: updatedColumnsForDummy,
            dateModified: new Date()
          });
          setTransformationResult('Successfully created dummy variables');
          return;
        case TransformationType.COMPUTE_VARIABLE:
          // This case will be handled by the dialog, so we shouldn't reach here
          // But if we do, just return without doing anything
          return;
      }

      const newColumn = {
        id: generateUUID(),
        name: outputTarget === 'new' ? newVariableName : columnName,
        type: newColumnType,
        role: column.role,
        description: transformationDescription,
        transformations: [{
          id: generateUUID(),
          name: transformationDescription,
          type: type, // Use the passed type
          parameters,
          original: column
        }]
      };

      const updatedColumns = outputTarget === 'new'
        ? [...selectedDataset.columns, newColumn]
        : selectedDataset.columns.map(col => 
          col.id === column.id 
            ? {
              ...col,
              ...newColumn,
              transformations: [...(col.transformations || []), newColumn.transformations[0]]
            }
            : col
        );

      updateDataset({
        ...selectedDataset!,
        data: finalTransformedData,
        columns: updatedColumns,
        dateModified: new Date()
      });

      setTransformationResult('Transformation applied successfully');
    } catch (error) {
      setTransformationResult(`Error: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // Main handler for "Apply Transformation" button
  const applyTransformation = () => {
    if (!selectedDataset || !selectedColumn) {
      setTransformationResult('Error: Please select a dataset and column.');
      return;
    }

    switch (transformationType) {
      case TransformationType.RECODE:
        setIsRecodeOpen(true);
        break;
      case TransformationType.LOG:
        setIsLogOpen(true);
        break;
      case TransformationType.BINNING:
        setIsBinningOpen(true);
        break;
      case TransformationType.COMPUTE_VARIABLE:
        setIsComputeOpen(true);
        break;
      default:
        // For transformations that don't require a dialog
        performTransformationLogic(transformationType, logBase, numBins, recodeMappings);
        break;
    }
  };

  // Dialog handlers
  const handleRecode = () => {
    performTransformationLogic(TransformationType.RECODE, logBase, numBins, recodeMappings);
    setIsRecodeOpen(false);
  };

  const handleLogApply = () => {
    performTransformationLogic(TransformationType.LOG, logBase, numBins, recodeMappings);
    setIsLogOpen(false);
  };

  const handleBinningApply = () => {
    performTransformationLogic(TransformationType.BINNING, logBase, numBins, recodeMappings);
    setIsBinningOpen(false);
  };

  const handleComputeApply = () => {
    if (!selectedDataset || selectedColumns.length === 0) {
      setTransformationResult('Error: Please select at least one column for computation.');
      return;
    }

    if (!newVariableName) {
      setTransformationResult('Error: Please provide a name for the computed variable.');
      return;
    }

    // Check if variable name already exists
    if (selectedDataset.columns.some(col =>
      col.name.toLowerCase() === newVariableName.toLowerCase()
    )) {
      setTransformationResult('Error: Variable name already exists.');
      return;
    }

    try {
      const transformedData = computeVariable(
        selectedDataset.data,
        selectedColumns,
        computeMethod,
        newVariableName
      );

      const newColumn = {
        id: generateUUID(),
        name: newVariableName,
        type: DataType.NUMERIC,
        role: selectedColumn?.role || VariableRole.NONE,
        description: `Computed variable using ${computeMethod} of: ${selectedColumns.join(', ')}`,
        transformations: [{
          id: generateUUID(),
          name: `Compute Variable (${computeMethod})`,
          type: TransformationType.COMPUTE_VARIABLE,
          parameters: { columns: selectedColumns, method: computeMethod },
          original: selectedColumn!
        }]
      };

      const updatedColumns = [...selectedDataset.columns, newColumn];

      updateDataset({
        ...selectedDataset,
        data: transformedData,
        columns: updatedColumns,
        dateModified: new Date()
      });

      setTransformationResult(`Successfully created computed variable: ${newVariableName}`);
      setIsComputeOpen(false);
      setSelectedColumns([]);
    } catch (error) {
      setTransformationResult(`Error: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // UI components
  return (
    <Box p={4}>
      <Typography variant="h5" gutterBottom>
        Data Transformations
      </Typography>

      {/* Dataset & Column Selection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="dataset-select-label">Dataset</InputLabel>
                <Select
                  labelId="dataset-select-label"
                  value={selectedDatasetId || ''}
                  onChange={handleDatasetChange}
                  disabled={!datasets.length}
                >
                  {datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.columns.length} columns)
                    </MenuItem>
                  ))}
                  {!datasets.length && (
                    <MenuItem disabled>No datasets available</MenuItem>
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="column-select-label">Column</InputLabel>
                <Select
                  labelId="column-select-label"
                  value={selectedColumnId || ''}
                  onChange={handleColumnChange}
                  disabled={!selectedDataset}
                >
                  {selectedDataset?.columns.map(col => (
                    <MenuItem key={col.id} value={col.id}>
                      {col.name} ({col.type})
                    </MenuItem>
                  ))}
                  {!selectedDataset && <MenuItem disabled>Select dataset first</MenuItem>}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Column Details */}
      {selectedColumn && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Column Details
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography>Name: {selectedColumnName}</Typography>
                <Typography>Type: {selectedColumnType}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography>Role: {selectedColumn.role}</Typography>
                <Typography>Description: {selectedColumn.description}</Typography>
              </Grid>
            </Grid>

            {selectedColumn.transformations && (
              <Box mt={2}>
                <Typography>Applied Transformations:</Typography>
                <List dense>
                  {selectedColumn.transformations.map((t, i) => (
                    <ListItem key={i}>
                      <ListItemText primary={t.name} secondary={t.type} />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </CardContent>
        </Card>
      )}

      {/* Transformation Settings */}
      <Card>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="trans-type-label">Transformation Type</InputLabel>
                <Select
                  labelId="trans-type-label"
                  value={transformationType}
                  onChange={handleTransformationTypeChange}
                  disabled={!selectedColumn || applicableTransformationTypes.length === 0}
                >
                  {applicableTransformationTypes.map(type => (
                    <MenuItem key={type} value={type}>
                      {type.replace(/_/g, ' ')}
                    </MenuItem>
                  ))}
                  {applicableTransformationTypes.length === 0 && (
                    <MenuItem disabled>No applicable transformations</MenuItem>
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Output Target</FormLabel>
                <RadioGroup
                  row
                  value={outputTarget}
                  onChange={handleOutputTargetChange}
                >
                  <FormControlLabel 
                    value="current" 
                    control={<Radio />} 
                    label="Replace Current Column"
                  />
                  <FormControlLabel 
                    value="new" 
                    control={<Radio />} 
                    label="Create New Variable"
                  />
                </RadioGroup>
              </FormControl>
            </Grid>

            {outputTarget === 'new' && (
              <Grid item xs={12}>
                <TextField
                  label="New Variable Name"
                  value={newVariableName}
                  onChange={(e) => setNewVariableName(e.target.value)}
                  helperText="Suggested name: " 
                  InputProps={{
                    endAdornment: (
                      <IconButton 
                        onClick={() => setNewVariableName(getNewVariableNameSuggestion())}
                        title="Generate suggestion"
                      >
                        <FunctionsIcon fontSize="small" />
                      </IconButton>
                    )
                  }}
                  fullWidth
                />
              </Grid>
            )}
          </Grid>

          <Box mt={3}>
            <Button
              variant="contained"
              startIcon={<TransformIcon />}
              onClick={applyTransformation}
              disabled={!selectedColumn}
            >
              Apply Transformation
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Transformation Result */}
      {transformationResult && (
        <Alert
          severity={transformationResult.startsWith('Error') ? 'error' : 'success'}
          sx={{ mt: 3 }}
        >
          {transformationResult}
        </Alert>
      )}

      {/* Recode Dialog */}
      <Dialog
        open={isRecodeOpen}
        onClose={() => setIsRecodeOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Recode Values</DialogTitle>
        <DialogContent>
          <List dense>
            {recodeMappings.map((mapping, index) => (
              <ListItem key={index}>
                <TextField
                  label="From"
                  value={mapping.from}
                  onChange={(e) => {
                    const newMappings = [...recodeMappings];
                    newMappings[index].from = e.target.value;
                    setRecodeMappings(newMappings);
                  }}
                  sx={{ mr: 2, width: 150 }}
                />
                <TextField
                  label="To"
                  value={mapping.to}
                  onChange={(e) => {
                    const newMappings = [...recodeMappings];
                    newMappings[index].to = e.target.value;
                    setRecodeMappings(newMappings);
                  }}
                  sx={{ width: 150 }}
                />
                <IconButton
                  onClick={() => {
                    if (recodeMappings.length > 1) {
                      const newMappings = recodeMappings.filter((_, i) => i !== index);
                      setRecodeMappings(newMappings);
                    }
                  }}
                  disabled={recodeMappings.length <= 1}
                >
                  <DeleteIcon />
                </IconButton>
              </ListItem>
            ))}
          </List>
          <Button
            variant="text"
            startIcon={<AddIcon />}
            onClick={() => setRecodeMappings([...recodeMappings, { from: '', to: '' }])}
          >
            Add Mapping
          </Button>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsRecodeOpen(false)}>Cancel</Button>
          <Button onClick={handleRecode} variant="contained" color="primary">
            Apply Recode
          </Button>
        </DialogActions>
      </Dialog>

      {/* Log Transformation Dialog */}
      <Dialog
        open={isLogOpen}
        onClose={() => setIsLogOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Log Transformation Settings</DialogTitle>
        <DialogContent>
          <TextField
            label="Logarithm Base"
            type="number"
            value={logBase}
            onChange={(e) => setLogBase(Number(e.target.value))}
            InputProps={{ inputProps: { min: 1 } }}
            fullWidth
          />
          <Typography variant="body2" color="textSecondary">
            This transformation requires positive values. Non-positive values will be set to null.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsLogOpen(false)}>Cancel</Button>
          <Button onClick={handleLogApply} variant="contained" color="primary">
            Apply Log Transform
          </Button>
        </DialogActions>
      </Dialog>

      {/* Binning Dialog */}
      <Dialog
        open={isBinningOpen}
        onClose={() => setIsBinningOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Binning Configuration</DialogTitle>
        <DialogContent>
          <TextField
            label="Number of Bins"
            type="number"
            value={numBins}
            onChange={(e) => setNumBins(Number(e.target.value))}
            InputProps={{ inputProps: { min: 2, max: 20 } }}
            fullWidth
          />
          <Typography variant="body2" color="textSecondary">
            This will convert numeric values into categorical bins of equal width.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsBinningOpen(false)}>Cancel</Button>
          <Button onClick={handleBinningApply} variant="contained" color="primary">
            Apply Binning
          </Button>
        </DialogActions>
      </Dialog>

      {/* Compute Variable Dialog */}
      <Dialog
        open={isComputeOpen}
        onClose={() => setIsComputeOpen(false)}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>Compute Variable</DialogTitle>
        <DialogContent>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Create a new variable by computing values from existing numeric variables.
                Perfect for creating Likert scale totals, averages, and other aggregate measures.
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel id="columns-select-label">Select Variables</InputLabel>
                <Select
                  labelId="columns-select-label"
                  multiple
                  value={selectedColumns}
                  onChange={(e) => setSelectedColumns(e.target.value as string[])}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {selectedDataset?.columns
                    .filter(col => col.type === DataType.NUMERIC)
                    .map(col => (
                      <MenuItem key={col.id} value={col.name}>
                        {col.name}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="method-select-label">Computation Method</InputLabel>
                <Select
                  labelId="method-select-label"
                  value={computeMethod}
                  onChange={(e) => setComputeMethod(e.target.value as typeof computeMethod)}
                >
                  <MenuItem value="sum">Sum</MenuItem>
                  <MenuItem value="mean">Mean (Average)</MenuItem>
                  <MenuItem value="count">Count (Non-null values)</MenuItem>
                  <MenuItem value="std">Standard Deviation</MenuItem>
                  <MenuItem value="min">Minimum</MenuItem>
                  <MenuItem value="max">Maximum</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="New Variable Name"
                value={newVariableName}
                onChange={(e) => setNewVariableName(e.target.value)}
                fullWidth
                helperText="Enter a name for the computed variable"
              />
            </Grid>

            {selectedColumns.length > 0 && (
              <Grid item xs={12}>
                <Typography variant="body2" color="textSecondary">
                  <strong>Preview:</strong> This will compute the {computeMethod} of: {selectedColumns.join(', ')}
                </Typography>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsComputeOpen(false)}>Cancel</Button>
          <Button
            onClick={handleComputeApply}
            variant="contained"
            color="primary"
            disabled={selectedColumns.length === 0 || !newVariableName}
          >
            Create Variable
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DataTransform;
