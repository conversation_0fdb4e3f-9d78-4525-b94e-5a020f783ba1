import React, { useState, useEffect, useMemo } from 'react';
import { useData } from '../../context/DataContext';
import { useAuth } from '../../context/AuthContext';
import {
  Box,
  Typography,
  Button,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Tooltip,
  Snackbar,
  Alert,
  Chip,
  Tabs,
  Tab,
  Card,
  CardContent,
  IconButton,
  LinearProgress,
  useTheme,
  Container,
  Grid,
  Stack,
  Fade,
  Grow,
  Badge,
  alpha,
  CardActions,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  CircularProgress,
  Avatar
} from '@mui/material';
import {
  Delete as DeleteIcon,
  AddCircleOutline as AddIcon,
  CheckCircle as CheckCircleIcon,
  CreateNewFolder as CreateIcon,
  CloudUpload as CloudUploadIcon,
  CloudDone as CloudDoneIcon,
  CloudOff as CloudOffIcon,
  Info as InfoIcon,
  Storage as StorageIcon,
  ArrowUpward as ArrowUpwardIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Download as DownloadIcon,
  FileCopy as FileCopyIcon,
  Visibility as VisibilityIcon,
  CalendarToday as CalendarIcon,
  TableChart as TableChartIcon,
  Warning as WarningIcon,
  CloudQueue as CloudQueueIcon,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon
} from '@mui/icons-material';
import PageTitle from '../UI/PageTitle';
import { Dataset, DataType, VariableRole } from '../../types';
import { generateUUID } from '../../utils/uuid';

interface DatasetListProps {
  onNavigateToImport: () => void;
  onNavigateToEditor: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Fade in={value === index} timeout={300}>
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`dataset-tabpanel-${index}`}
        aria-labelledby={`dataset-tab-${index}`}
        {...other}
        style={{ width: '100%' }}
      >
        {value === index && (
          <Box sx={{ pt: 3 }}>
            {children}
          </Box>
        )}
      </div>
    </Fade>
  );
}

const DatasetList: React.FC<DatasetListProps> = ({ onNavigateToImport, onNavigateToEditor }) => {
  const theme = useTheme();
  const {
    datasets,
    currentDataset,
    setCurrentDataset,
    removeDataset,
    addDataset,
    renameDataset,
    saveCurrentDatasetToAccount,
    deleteDatasetFromAccount
  } = useData();
  const { user, canImportData, canAccessCloudStorage } = useAuth();
  
  // State for "Create New Dataset" dialog
  const [isCreateDatasetDialogOpen, setIsCreateDatasetDialogOpen] = useState(false);
  const [newDatasetName, setNewDatasetName] = useState<string>('');
  const [newDatasetDescription, setNewDatasetDescription] = useState<string>('');

  // State for "Rename Dataset" dialog
  const [isRenameDatasetDialogOpen, setIsRenameDatasetDialogOpen] = useState(false);
  const [renameDatasetId, setRenameDatasetId] = useState<string>('');
  const [renameDatasetName, setRenameDatasetName] = useState<string>('');
  
  // State for notifications
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  
  // State for loading
  const [isSaving, setIsSaving] = useState(false);
  const [loadingDatasetId, setLoadingDatasetId] = useState<string | null>(null);
  
  // State for tabs
  const [tabValue, setTabValue] = useState(0);
  
  // State for menu
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedDatasetId, setSelectedDatasetId] = useState<string | null>(null);
  
  // State for storage usage
  const [storageUsage, setStorageUsage] = useState({
    used: 0,
    total: 2 * 1024 * 1024,
    percentage: 0
  });

  // Separate datasets into cloud and local
  const cloudDatasets = useMemo(() => {
    return datasets.filter(dataset => dataset.userId === user?.id);
  }, [datasets, user?.id]);

  const localDatasets = useMemo(() => {
    return datasets.filter(dataset => !dataset.userId);
  }, [datasets, user?.id]);

  // Calculate storage usage
  useEffect(() => {
    if (cloudDatasets.length > 0) {
      let totalSize = 0;
      cloudDatasets.forEach(dataset => {
        const datasetString = JSON.stringify({
          data: dataset.data,
          variableInfo: dataset.columns
        });
        totalSize += new TextEncoder().encode(datasetString).length;
      });
      
      setStorageUsage({
        used: totalSize,
        total: 2 * 1024 * 1024,
        percentage: Math.min(100, (totalSize / (2 * 1024 * 1024)) * 100)
      });
    } else {
      setStorageUsage({
        used: 0,
        total: 2 * 1024 * 1024,
        percentage: 0
      });
    }
  }, [cloudDatasets]);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSetCurrent = (datasetId: string) => {
    const datasetToSet = datasets.find(d => d.id === datasetId);
    if (datasetToSet) {
      setCurrentDataset(datasetToSet);
      setSnackbarMessage(`"${datasetToSet.name}" is now the current dataset`);
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    }
  };

  const handleRemoveDataset = (datasetId: string) => {
    const dataset = datasets.find(d => d.id === datasetId);
    if (window.confirm(`Are you sure you want to delete "${dataset?.name}"? This action cannot be undone.`)) {
      removeDataset(datasetId);
      setSnackbarMessage('Dataset deleted successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    }
  };

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, datasetId: string) => {
    setMenuAnchor(event.currentTarget);
    setSelectedDatasetId(datasetId);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedDatasetId(null);
  };

  // Create dataset handlers
  const handleOpenCreateDatasetDialog = () => {
    setNewDatasetName('');
    setNewDatasetDescription('');
    setIsCreateDatasetDialogOpen(true);
  };

  const handleCloseCreateDatasetDialog = () => {
    setIsCreateDatasetDialogOpen(false);
  };

  // Rename dataset handlers
  const handleOpenRenameDatasetDialog = (datasetId: string) => {
    const dataset = datasets.find(d => d.id === datasetId);
    if (dataset) {
      setRenameDatasetId(datasetId);
      setRenameDatasetName(dataset.name);
      setIsRenameDatasetDialogOpen(true);
    }
  };

  const handleCloseRenameDatasetDialog = () => {
    setIsRenameDatasetDialogOpen(false);
    setRenameDatasetId('');
    setRenameDatasetName('');
  };

  const handleRenameDataset = async () => {
    if (!renameDatasetName.trim()) {
      setSnackbarMessage('Please enter a dataset name');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    if (!renameDatasetId) {
      setSnackbarMessage('No dataset selected for renaming');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    const dataset = datasets.find(d => d.id === renameDatasetId);
    if (!dataset) {
      setSnackbarMessage('Dataset not found');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    // Check access control - Guest users cannot rename sample datasets
    if (!canImportData && !dataset.userId) {
      setSnackbarMessage('Guest users cannot rename datasets. Please login to rename datasets.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    setLoadingDatasetId(renameDatasetId);
    try {
      const result = await renameDataset(renameDatasetId, renameDatasetName.trim());

      if (result.success) {
        handleCloseRenameDatasetDialog();
        setSnackbarMessage(result.message);
        setSnackbarSeverity('success');
      } else {
        setSnackbarMessage(result.message);
        setSnackbarSeverity('error');
      }
      setSnackbarOpen(true);
    } catch (error: any) {
      setSnackbarMessage(error.message || 'Failed to rename dataset');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setLoadingDatasetId(null);
    }
  };

  const handleCreateNewDataset = async () => {
    if (!canImportData) {
      setSnackbarMessage('Please login to create datasets. Guest users can only use sample datasets.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    if (!newDatasetName.trim()) {
      setSnackbarMessage('Please enter a dataset name');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    const newDataset: Dataset = {
      id: generateUUID(),
      name: newDatasetName.trim(),
      description: newDatasetDescription.trim() || `Created on ${new Date().toLocaleDateString()}`,
      data: [],
      columns: [],
      dateCreated: new Date(),
      dateModified: new Date(),
    };

    try {
      await addDataset(newDataset);
      setCurrentDataset(newDataset);
      handleCloseCreateDatasetDialog();
      setSnackbarMessage(`Dataset "${newDataset.name}" created successfully`);
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      onNavigateToEditor();
    } catch (error) {
      console.error("Error creating new dataset:", error);
      setSnackbarMessage('Failed to create dataset');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };
  
  // Handle moving dataset to cloud
  const handleMoveToCloud = async (dataset: Dataset) => {
    if (!user) {
      setSnackbarMessage('Please login to save datasets to cloud');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    // CRITICAL SECURITY CHECK: Verify user has cloud storage permissions
    if (!canAccessCloudStorage) {
      setSnackbarMessage('Cloud storage access requires a Pro account. Please upgrade to save datasets to cloud storage.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }
    
    if (!dataset) return;
    
    setLoadingDatasetId(dataset.id);
    
    if (currentDataset?.id !== dataset.id) {
      setCurrentDataset(dataset);
    }
    
    setIsSaving(true);
    try {
      const result = await saveCurrentDatasetToAccount();
      
      if (result.success) {
        setSnackbarMessage(`"${dataset.name}" moved to cloud successfully`);
        setTabValue(1); // Switch to cloud tab
      } else {
        setSnackbarMessage(result.message);
      }
      
      setSnackbarSeverity(result.success ? 'success' : 'error');
      setSnackbarOpen(true);
    } catch (error: any) {
      setSnackbarMessage(error.message || 'Failed to move dataset to cloud');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setIsSaving(false);
      setLoadingDatasetId(null);
    }
  };
  
  // Handle deleting dataset from account
  const handleDeleteFromAccount = async (datasetId: string) => {
    if (!user) {
      setSnackbarMessage('Please login to manage cloud datasets');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }
    
    const dataset = datasets.find(d => d.id === datasetId);
    if (window.confirm(`Are you sure you want to delete "${dataset?.name}" from your cloud account? This action cannot be undone.`)) {
      setLoadingDatasetId(datasetId);
      try {
        const result = await deleteDatasetFromAccount(datasetId);
        setSnackbarMessage(result.message);
        setSnackbarSeverity(result.success ? 'success' : 'error');
        setSnackbarOpen(true);
      } catch (error: any) {
        setSnackbarMessage(error.message || 'Failed to delete dataset');
        setSnackbarSeverity('error');
        setSnackbarOpen(true);
      } finally {
        setLoadingDatasetId(null);
      }
    }
  };
  
  // Format bytes
  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Render dataset card
  const renderDatasetCard = (dataset: Dataset, isCloudDataset: boolean) => {
    const isCurrentDataset = currentDataset?.id === dataset.id;
    const isLoading = loadingDatasetId === dataset.id;
    
    return (
      <Grow in={true} key={dataset.id} timeout={300}>
        <Grid item xs={12} sm={6} md={4}>
          <Card
            elevation={isCurrentDataset ? 8 : 2}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              bgcolor: isCurrentDataset ? alpha(theme.palette.primary.main, 0.04) : 'background.paper',
              borderColor: isCurrentDataset ? 'primary.main' : 'divider',
              borderWidth: isCurrentDataset ? 2 : 1,
              borderStyle: 'solid',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: theme.shadows[8],
              }
            }}
          >
            {isLoading && (
              <LinearProgress 
                sx={{ 
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  zIndex: 1
                }}
              />
            )}
            
            <CardContent sx={{ flexGrow: 1, pb: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Box sx={{ flex: 1, mr: 1 }}>
                  <Typography 
                    variant="h6" 
                    gutterBottom 
                    sx={{ 
                      fontWeight: 600,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {dataset.name}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 0.5, mb: 1 }}>
                    <Chip
                      size="small"
                      label={isCloudDataset ? "Cloud" : "Local"}
                      icon={isCloudDataset ? <CloudDoneIcon /> : <StorageIcon />}
                      color={isCloudDataset ? "primary" : "default"}
                      sx={{ height: 24 }}
                    />
                    {isCurrentDataset && (
                      <Chip
                        size="small"
                        label="Current"
                        icon={<CheckCircleIcon />}
                        color="success"
                        sx={{ height: 24 }}
                      />
                    )}
                  </Box>
                </Box>
                <IconButton
                  size="small"
                  onClick={(e) => handleMenuOpen(e, dataset.id)}
                  disabled={isLoading}
                >
                  <MoreVertIcon />
                </IconButton>
              </Box>

              <Stack spacing={1.5}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TableChartIcon sx={{ fontSize: 18, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    {dataset.data.length} rows × {dataset.columns.length} columns
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CalendarIcon sx={{ fontSize: 18, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    Modified {formatDate(dataset.dateModified)}
                  </Typography>
                </Box>

                {dataset.description && (
                  <Typography 
                    variant="body2" 
                    color="text.secondary"
                    sx={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      lineHeight: 1.5,
                      minHeight: '3em'
                    }}
                  >
                    {dataset.description}
                  </Typography>
                )}
              </Stack>
            </CardContent>

            <CardActions sx={{ p: 2, pt: 0 }}>
              <Button
                size="small"
                variant={isCurrentDataset ? "contained" : "outlined"}
                onClick={() => isCurrentDataset ? onNavigateToEditor() : handleSetCurrent(dataset.id)}
                disabled={isLoading}
                fullWidth
                startIcon={isCurrentDataset ? <EditIcon /> : null}
              >
                {isCurrentDataset ? 'Edit' : 'Set as Current'}
              </Button>
            </CardActions>
          </Card>
        </Grid>
      </Grow>
    );
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      <PageTitle title="My Datasets" />
      
      {/* Header Actions */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4, flexWrap: 'wrap', gap: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Manage your local and cloud datasets
        </Typography>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<CreateIcon />}
            onClick={canImportData ? handleOpenCreateDatasetDialog : undefined}
            disabled={!canImportData}
            sx={{
              opacity: canImportData ? 1 : 0.5,
              cursor: canImportData ? 'pointer' : 'not-allowed'
            }}
          >
            Create New
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={onNavigateToImport}
          >
            Import Dataset
          </Button>
        </Stack>
      </Box>

      {/* Storage Info for logged-in users */}
      {user && (
        <Card sx={{ mb: 3, borderRadius: 2, overflow: 'hidden' }}>
          <Box
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              color: 'white',
              p: 3,
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                    <CloudQueueIcon fontSize="large" />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Cloud Storage
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      {cloudDatasets.length} of 2 datasets saved
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Storage Used</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {formatBytes(storageUsage.used)} / {formatBytes(storageUsage.total)}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={storageUsage.percentage}
                    sx={{
                      height: 8,
                      borderRadius: 1,
                      bgcolor: 'rgba(255,255,255,0.2)',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: storageUsage.percentage > 80 ? theme.palette.error.main : 'white',
                      }
                    }}
                  />
                  {storageUsage.percentage > 80 && (
                    <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                      <WarningIcon sx={{ fontSize: 14, mr: 0.5 }} />
                      Storage almost full
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Card>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="dataset tabs"
          sx={{
            '& .MuiTab-root': {
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500,
            }
          }}
        >
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FolderIcon />
                <span>Local Datasets</span>
                <Badge badgeContent={localDatasets.length} color="default" />
              </Box>
            }
          />
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CloudDoneIcon />
                <span>Cloud Datasets</span>
                <Badge badgeContent={cloudDatasets.length} color="primary" />
              </Box>
            }
            disabled={!user}
          />
        </Tabs>
      </Box>

      {/* Tab Panels */}
      <TabPanel value={tabValue} index={0}>
        {localDatasets.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <FolderOpenIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
            <Typography variant="h5" gutterBottom color="text.secondary">
              No Local Datasets
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Create a new dataset or import existing data to get started
            </Typography>
            <Stack direction="row" spacing={2} justifyContent="center">
              <Button
                variant="outlined"
                startIcon={<CreateIcon />}
                onClick={canImportData ? handleOpenCreateDatasetDialog : undefined}
                disabled={!canImportData}
                sx={{
                  opacity: canImportData ? 1 : 0.5,
                  cursor: canImportData ? 'pointer' : 'not-allowed'
                }}
              >
                Create Dataset
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={onNavigateToImport}
              >
                Import Dataset
              </Button>
            </Stack>
          </Box>
        ) : (
          <Grid container spacing={3}>
            {localDatasets.map(dataset => renderDatasetCard(dataset, false))}
          </Grid>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {!user ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <CloudOffIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
            <Typography variant="h5" gutterBottom color="text.secondary">
              Login Required
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Please login to access cloud storage features
            </Typography>
          </Box>
        ) : cloudDatasets.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <CloudQueueIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
            <Typography variant="h5" gutterBottom color="text.secondary">
              No Cloud Datasets
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Save your local datasets to cloud for access across devices
            </Typography>
            {localDatasets.length > 0 && (
              <Button
                variant="outlined"
                startIcon={<ArrowUpwardIcon />}
                onClick={() => setTabValue(0)}
              >
                View Local Datasets
              </Button>
            )}
          </Box>
        ) : (
          <Grid container spacing={3}>
            {cloudDatasets.map(dataset => renderDatasetCard(dataset, true))}
          </Grid>
        )}
      </TabPanel>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        {selectedDatasetId && (() => {
          const dataset = datasets.find(d => d.id === selectedDatasetId);
          const isCloud = dataset?.userId === user?.id;
          
          return (
            <>
              <MenuItem onClick={() => {
                handleSetCurrent(selectedDatasetId);
                handleMenuClose();
              }}>
                <ListItemIcon>
                  <CheckCircleIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>Set as Current</ListItemText>
              </MenuItem>
              
              <MenuItem onClick={() => {
                onNavigateToEditor();
                handleMenuClose();
              }}>
                <ListItemIcon>
                  <VisibilityIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>View/Edit</ListItemText>
              </MenuItem>

              {/* Rename option - only for datasets that can be renamed */}
              {((canImportData && !dataset?.userId) || (dataset?.userId === user?.id)) && (
                <MenuItem onClick={() => {
                  handleOpenRenameDatasetDialog(selectedDatasetId);
                  handleMenuClose();
                }}>
                  <ListItemIcon>
                    <EditIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Rename</ListItemText>
                </MenuItem>
              )}

              {!isCloud && user && canAccessCloudStorage && cloudDatasets.length < 2 && (
                <MenuItem onClick={() => {
                  if (dataset) handleMoveToCloud(dataset);
                  handleMenuClose();
                }}>
                  <ListItemIcon>
                    <CloudUploadIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Move to Cloud</ListItemText>
                </MenuItem>
              )}
              
              <Divider />
              
              <MenuItem 
                onClick={() => {
                  if (isCloud) {
                    handleDeleteFromAccount(selectedDatasetId);
                  } else {
                    handleRemoveDataset(selectedDatasetId);
                  }
                  handleMenuClose();
                }}
              >
                <ListItemIcon>
                  <DeleteIcon fontSize="small" color="error" />
                </ListItemIcon>
                <ListItemText>Delete</ListItemText>
              </MenuItem>
            </>
          );
        })()}
      </Menu>

      {/* Create Dataset Dialog */}
      <Dialog 
        open={isCreateDatasetDialogOpen} 
        onClose={handleCloseCreateDatasetDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Create New Dataset</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              autoFocus
              label="Dataset Name"
              fullWidth
              variant="outlined"
              value={newDatasetName}
              onChange={(e) => setNewDatasetName(e.target.value)}
              placeholder="e.g., Survey Results 2024"
            />
            <TextField
              label="Description (optional)"
              fullWidth
              variant="outlined"
              multiline
              rows={2}
              value={newDatasetDescription}
              onChange={(e) => setNewDatasetDescription(e.target.value)}
              placeholder="Brief description of your dataset..."
            />
          </Stack>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button onClick={handleCloseCreateDatasetDialog}>
            Cancel
          </Button>
          <Button 
            onClick={handleCreateNewDataset} 
            variant="contained"
            disabled={!newDatasetName.trim()}
          >
            Create Dataset
          </Button>
        </DialogActions>
      </Dialog>

      {/* Rename Dataset Dialog */}
      <Dialog
        open={isRenameDatasetDialogOpen}
        onClose={handleCloseRenameDatasetDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Rename Dataset</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              autoFocus
              label="Dataset Name"
              fullWidth
              variant="outlined"
              value={renameDatasetName}
              onChange={(e) => setRenameDatasetName(e.target.value)}
              placeholder="Enter new dataset name"
              disabled={loadingDatasetId === renameDatasetId}
            />
          </Stack>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button onClick={handleCloseRenameDatasetDialog} disabled={loadingDatasetId === renameDatasetId}>
            Cancel
          </Button>
          <Button
            onClick={handleRenameDataset}
            variant="contained"
            disabled={!renameDatasetName.trim() || loadingDatasetId === renameDatasetId}
          >
            {loadingDatasetId === renameDatasetId ? 'Renaming...' : 'Rename Dataset'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setSnackbarOpen(false)} 
          severity={snackbarSeverity}
          variant="filled"
          elevation={6}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default DatasetList;
