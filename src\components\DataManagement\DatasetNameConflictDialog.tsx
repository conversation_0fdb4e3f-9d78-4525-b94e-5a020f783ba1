import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  TextField,
  Box,
} from '@mui/material';
import { useData } from '../../context/DataContext';
import { Dataset } from '../../types';

const DatasetNameConflictDialog: React.FC = () => {
  const {
    datasetNameConflictInfo,
    resolveConflictByReplacing,
    resolveConflictByRenaming,
    cancelConflictResolution,
    datasets // Used to check if the new proposed name also conflicts
  } = useData();

  const [newDatasetName, setNewDatasetName] = useState('');
  const [renameError, setRenameError] = useState<string | null>(null);
  const [showRenameField, setShowRenameField] = useState(false);

  useEffect(() => {
    if (datasetNameConflictInfo) {
      setNewDatasetName(datasetNameConflictInfo.newDataset.name + '_copy'); // Suggest a default new name
      setShowRenameField(false); // Reset rename field visibility
      setRenameError(null); // Reset error
    }
  }, [datasetNameConflictInfo]);

  if (!datasetNameConflictInfo) {
    return null;
  }

  const { newDataset, existingDatasetId } = datasetNameConflictInfo;
  const existingDataset = datasets.find(d => d.id === existingDatasetId);

  const handleReplace = () => {
    resolveConflictByReplacing(existingDatasetId, newDataset);
  };

  const handleRename = () => {
    if (!newDatasetName.trim()) {
      setRenameError('New dataset name cannot be empty.');
      return;
    }
    if (datasets.some(d => d.name === newDatasetName.trim() && d.id !== existingDatasetId)) {
      setRenameError(`A dataset named '${newDatasetName.trim()}' already exists. Please choose a different name.`);
      return;
    }
    setRenameError(null);
    resolveConflictByRenaming(newDataset, newDatasetName.trim());
  };

  const handleCancel = () => {
    cancelConflictResolution();
  };
  
  const handleShowRenameField = () => {
    setShowRenameField(true);
  };

  return (
    <Dialog open={true} onClose={handleCancel} maxWidth="sm" fullWidth>
      <DialogTitle>Dataset Name Conflict</DialogTitle>
      <DialogContent>
        <DialogContentText gutterBottom>
          A dataset named "<strong>{existingDataset?.name || newDataset.name}</strong>" already exists.
        </DialogContentText>
        <DialogContentText>
          What would you like to do?
        </DialogContentText>
        
        {showRenameField && (
          <Box mt={2}>
            <TextField
              autoFocus
              margin="dense"
              label="New Dataset Name"
              type="text"
              fullWidth
              variant="outlined"
              value={newDatasetName}
              onChange={(e) => {
                setNewDatasetName(e.target.value);
                if (renameError) setRenameError(null);
              }}
              error={!!renameError}
              helperText={renameError}
              sx={{ mt: 1 }}
            />
          </Box>
        )}
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        <Button onClick={handleCancel} color="inherit">
          Cancel Import
        </Button>
        <Button onClick={handleReplace} color="error" variant="outlined">
          Replace Existing
        </Button>
        {!showRenameField ? (
            <Button onClick={handleShowRenameField} color="primary" variant="contained">
                Import with New Name
            </Button>
        ) : (
            <Button onClick={handleRename} color="primary" variant="contained" disabled={!newDatasetName.trim()}>
                Save with New Name
            </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default DatasetNameConflictDialog;
