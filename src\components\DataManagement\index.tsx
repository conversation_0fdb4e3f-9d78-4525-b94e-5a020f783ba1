import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  useTheme,
  Tooltip, // Added Tooltip
  IconButton // Added IconButton
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Edit as EditIcon,
  Transform as TransformIcon,
  CloudDownload as CloudDownloadIcon,
  Storage as StorageIcon,
  ListAlt as ListAltIcon, // Added for Datasets tab
  EditNote as EditNoteIcon, // Added for Variable Editor
  FilterList as FilterListIcon, // Added for Data Filter
  Fullscreen as FullscreenIcon, // Added for Full View
  FullscreenExit as FullscreenExitIcon // Added for Exit Full View
} from '@mui/icons-material';
import DataImport from './DataImport';
import DataExport from './DataExport';
import DataEditor from './DataEditor';
import DataTransform from './DataTransform';
import DataFilter from './DataFilter'; // Import DataFilter
import DatasetList from './DatasetList'; // Import DatasetList
import VariableEditor from './VariableEditor'; // Import VariableEditor
import { Tabs, TabPanel } from '../UI';

interface DataManagementProps {
  initialTab?: string;
  onNavigate: (path: string) => void;
}

// Map tab names to indices
const tabNameToIndex: Record<string, number> = {
  'import': 0,
  'datasets': 1,
  'editor': 2, // Data Editor
  'variables': 3, // New Variable Editor tab
  'filter': 4, // Data Filter tab
  'transform': 5,
  'export': 6
};

const DataManagement: React.FC<DataManagementProps> = ({ initialTab = '', onNavigate }) => {
  const theme = useTheme();
  const [value, setValue] = useState(0);
  const [isFullView, setIsFullView] = useState<boolean>(false); // State for full view

  // Set initial tab based on URL or prop
  useEffect(() => {
    if (initialTab && tabNameToIndex[initialTab] !== undefined) {
      setValue(tabNameToIndex[initialTab]);
    }
  }, [initialTab]);

  const toggleFullView = () => {
    setIsFullView(!isFullView);
  };
  
  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const navigateToImportTab = () => {
    setValue(tabNameToIndex['import']);
    onNavigate('/data-management/import'); // Navigate using the prop
  };

  // Navigation handlers for DataEditor and VariableEditor




  const tabItems = [
    { label: 'Import Data', icon: <CloudUploadIcon /> },
    { label: 'Datasets', icon: <ListAltIcon /> },
    { label: 'Data Editor', icon: <EditIcon /> },
    { label: 'Variable Editor', icon: <EditNoteIcon /> },
    { label: 'Data Filter', icon: <FilterListIcon /> },
    { label: 'Transform', icon: <TransformIcon /> },
    { label: 'Export Data', icon: <CloudDownloadIcon /> }
  ];
  
  return (
    <Box sx={{ 
      width: isFullView ? '100vw' : '100%', 
      height: isFullView ? '100vh' : '100%', 
      position: isFullView ? 'fixed' : 'relative', // Use fixed for full screen
      top: isFullView ? 0 : 'auto',
      left: isFullView ? 0 : 'auto',
      zIndex: isFullView ? 1300 : 'auto', // Ensure it's on top
      bgcolor: 'background.default', // Use default background for full screen
      display: 'flex',
      flexDirection: 'column',
      p: isFullView ? 2 : 0 // Add padding in full view
    }}>
      <Paper 
        elevation={1} 
        sx={{ 
          width: '100%', 
          flex: 1, // Grow to fill available space
           display: 'flex',
           flexDirection: 'column',
           overflow: 'hidden', // Prevent double scrollbars
           // borderRadius: isFullView ? 0 : theme.shape.borderRadius, // Remove border radius in full view
         }}
       >
        <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Tabs
             items={tabItems}
             value={value}
             onChange={handleChange}
             customVariant="card" // Use customVariant instead of variant
             scrollable
           />
          <Tooltip title={isFullView ? "Exit Full View" : "Enter Full View"}>
            <IconButton onClick={toggleFullView} size="large" sx={{ mr: 1 }}>
              {isFullView ? <FullscreenExitIcon /> : <FullscreenIcon />}
            </IconButton>
          </Tooltip>
        </Box>
        
        <Box sx={{ 
          flex: 1, // Grow to fill available space
          overflow: 'hidden', // Necessary for child overflow to work properly
          display: 'flex',
          flexDirection: 'column'
        }}>
          <TabPanel value={value} index={0}>
            <DataImport onImportSuccess={() => setValue(tabNameToIndex['datasets'])} />
          </TabPanel>
          <TabPanel value={value} index={1}>
<DatasetList onNavigateToImport={navigateToImportTab} onNavigateToEditor={() => onNavigate('data-management/editor')} />
          </TabPanel>
          <TabPanel value={value} index={2}>
            <DataEditor onGoToVariableEditor={() => setValue(tabNameToIndex['variables'])} />
          </TabPanel>
          <TabPanel value={value} index={3}>
            <VariableEditor onGoToDataEditor={() => setValue(tabNameToIndex['editor'])} />
          </TabPanel>
          <TabPanel value={value} index={4}>
            <DataFilter />
          </TabPanel>
          <TabPanel value={value} index={5}>
            <DataTransform />
          </TabPanel>
          <TabPanel value={value} index={6}>
            <DataExport />
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};

export default DataManagement;
