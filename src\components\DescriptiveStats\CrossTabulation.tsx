import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControlLabel,
  Checkbox,
  Alert,
  Divider,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Chip,
  IconButton,
  Tooltip,
  ToggleButton,
  ToggleButtonGroup,

  Fade,
  Zoom,
  Switch,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Stack,
  LinearProgress,
  Badge,
  Tabs,
  Tab,
  Skeleton
} from '@mui/material';
import {
  ViewComfy as ViewComfyIcon,
  TableChart as TableChartIcon,
  BarChart as BarChartIcon,
  GridOn as GridOnIcon,
  SwapHoriz as SwapHorizIcon,
  Download as DownloadIcon,
  Info as InfoIcon,
  FilterList as FilterListIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  FolderOpen as FolderOpenIcon,
  HelpOutline as HelpOutlineIcon,

  Assessment as AssessmentIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  Insights as InsightsIcon,
  FileDownload as FileDownloadIcon,
  Print as PrintIcon,
  Share as ShareIcon,

  Warning as WarningIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, ColumnStatistics } from '../../types';
import { createCrossTabulation } from '@/utils/stats';
import { chiSquareTest } from '@/utils/stats';
import jStat from 'jstat';
import { getOrderedCategories } from '../../utils/dataUtilities';
import { extractCategoricalValuesWithMissingCodes, isMissingValue } from '../../utils/missingDataUtils';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  Cell,
  ResponsiveContainer,
  PieChart,
  Pie,
  Sector,
  Rectangle
} from 'recharts';

// Custom active shape for pie chart
const renderActiveShape = (props: any) => {
  const RADIAN = Math.PI / 180;
  const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props;
  const sin = Math.sin(-RADIAN * midAngle);
  const cos = Math.cos(-RADIAN * midAngle);
  const sx = cx + (outerRadius + 10) * cos;
  const sy = cy + (outerRadius + 10) * sin;
  const mx = cx + (outerRadius + 30) * cos;
  const my = cy + (outerRadius + 30) * sin;
  const ex = mx + (cos >= 0 ? 1 : -1) * 22;
  const ey = my;
  const textAnchor = cos >= 0 ? 'start' : 'end';

  return (
    <g>
      <text x={cx} y={cy} dy={8} textAnchor="middle" fill={fill}>
        {payload.name}
      </text>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
      />
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={outerRadius + 6}
        outerRadius={outerRadius + 10}
        fill={fill}
      />
      <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
      <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
      <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={textAnchor} fill="#333">{`${value}`}</text>
      <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={18} textAnchor={textAnchor} fill="#999">
        {`(${(percent * 100).toFixed(2)}%)`}
      </text>
    </g>
  );
};



// Enhanced chi-square test with Yates' correction
const chiSquareTestWithYates = (contingencyTable: number[][]) => {
  const rowTotals = contingencyTable.map(row => row.reduce((a, b) => a + b, 0));
  const colTotals = contingencyTable[0].map((_, colIndex) => 
    contingencyTable.reduce((sum, row) => sum + row[colIndex], 0)
  );
  const grandTotal = rowTotals.reduce((a, b) => a + b, 0);
  
  // Calculate expected frequencies
  const expected: number[][] = [];
  let minExpected = Infinity;
  let needsYatesCorrection = false;
  
  for (let i = 0; i < contingencyTable.length; i++) {
    expected[i] = [];
    for (let j = 0; j < contingencyTable[i].length; j++) {
      const exp = (rowTotals[i] * colTotals[j]) / grandTotal;
      expected[i][j] = exp;
      if (exp < 5) {
        needsYatesCorrection = true;
        minExpected = Math.min(minExpected, exp);
      }
    }
  }
  
  // Calculate chi-square with or without Yates' correction
  let chiSquare = 0;
  for (let i = 0; i < contingencyTable.length; i++) {
    for (let j = 0; j < contingencyTable[i].length; j++) {
      const observed = contingencyTable[i][j];
      const exp = expected[i][j];
      
      if (needsYatesCorrection) {
        // Apply Yates' continuity correction
        const diff = Math.abs(observed - exp) - 0.5;
        if (diff > 0) {
          chiSquare += (diff * diff) / exp;
        }
      } else {
        // Standard chi-square calculation
        const diff = observed - exp;
        chiSquare += (diff * diff) / exp;
      }
    }
  }
  
  // Degrees of freedom
  const df = (contingencyTable.length - 1) * (contingencyTable[0].length - 1);
  
  // Calculate p-value using proper chi-square distribution
  const pValue = df > 0 ? 1 - jStat.chisquare.cdf(chiSquare, df) : NaN;
  
  // Calculate Cramér's V
  const n = grandTotal;
  const k = Math.min(contingencyTable.length, contingencyTable[0].length);
  const cramersV = Math.sqrt(chiSquare / (n * (k - 1)));
  
  return {
    chiSquare,
    df,
    pValue,
    cramersV,
    yatesCorrection: needsYatesCorrection,
    minExpectedFrequency: minExpected,
    expected
  };
};

// Component for cross-tabulation analysis of categorical data
const CrossTabulation: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();
  
  // State for analysis options
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [rowVariable, setRowVariable] = useState<string>('');
  const [columnVariable, setColumnVariable] = useState<string>('');
  const [includePercentages, setIncludePercentages] = useState<boolean>(true);
  const [includeRowPercentages, setIncludeRowPercentages] = useState<boolean>(true);
  const [includeColumnPercentages, setIncludeColumnPercentages] = useState<boolean>(true);
  const [includeChiSquare, setIncludeChiSquare] = useState<boolean>(true);
  const [excludeMissing, setExcludeMissing] = useState<boolean>(false);
  const [showExpectedFrequencies, setShowExpectedFrequencies] = useState<boolean>(false);
  const [showResiduals, setShowResiduals] = useState<boolean>(false);
  
  // UI state
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [visualizationType, setVisualizationType] = useState<string>('stacked');

  const [activeTab, setActiveTab] = useState<number>(0);
  const [speedDialOpen, setSpeedDialOpen] = useState<boolean>(false);
  const [pieActiveIndex, setPieActiveIndex] = useState<number>(0);
  
  // State for analysis results
  const [crossTabResult, setCrossTabResult] = useState<{
    rowCategories: string[];
    columnCategories: string[];
    frequencies: Record<string, Record<string, number>>;
    rowPercentages: Record<string, Record<string, number>>;
    columnPercentages: Record<string, Record<string, number>>;
    totalPercentages: Record<string, Record<string, number>>;
    rowTotals: Record<string, number>;
    columnTotals: Record<string, number>;
    grandTotal: number;
    expectedFrequencies?: Record<string, Record<string, number>>;
    residuals?: Record<string, Record<string, number>>;
    standardizedResiduals?: Record<string, Record<string, number>>;
    chiSquare?: {
      chiSquare: number;
      df: number;
      pValue: number;
      cramersV: number;
      yatesCorrection?: boolean;
      minExpectedFrequency?: number;
    };
    chartData: Array<{
      category: string;
      [key: string]: string | number;
    }>;
    pieData?: Array<{
      name: string;
      value: number;
    }>;

  } | null>(null);
  
  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('cross_tabulation_results');
    const savedConfig = localStorage.getItem('cross_tabulation_config');
    
    if (savedResults) {
      try {
        setCrossTabResult(JSON.parse(savedResults));
      } catch (e) {
        console.error('Error parsing saved cross tabulation results:', e);
      }
    }
    
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig);
        setRowVariable(config.rowVariable || '');
        setColumnVariable(config.columnVariable || '');
        setIncludePercentages(config.includePercentages ?? true);
        setIncludeRowPercentages(config.includeRowPercentages ?? true);
        setIncludeColumnPercentages(config.includeColumnPercentages ?? true);
        setIncludeChiSquare(config.includeChiSquare ?? true);
        setExcludeMissing(config.excludeMissing ?? false);
        setShowExpectedFrequencies(config.showExpectedFrequencies ?? false);
        setShowResiduals(config.showResiduals ?? false);
        setVisualizationType(config.visualizationType || 'stacked');
      } catch (e) {
        console.error('Error parsing saved configuration:', e);
      }
    }
  }, []);
  
  // Save configuration when it changes
  useEffect(() => {
    const config = {
      rowVariable,
      columnVariable,
      includePercentages,
      includeRowPercentages,
      includeColumnPercentages,
      includeChiSquare,
      excludeMissing,
      showExpectedFrequencies,
      showResiduals,
      visualizationType
    };
    localStorage.setItem('cross_tabulation_config', JSON.stringify(config));
  }, [
    rowVariable,
    columnVariable,
    includePercentages,
    includeRowPercentages,
    includeColumnPercentages,
    includeChiSquare,
    excludeMissing,
    showExpectedFrequencies,
    showResiduals,
    visualizationType
  ]);
  
  // Get categorical columns from current dataset
  const categoricalColumns = currentDataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL || col.type === DataType.ORDINAL || col.type === DataType.BOOLEAN
  ) || [];
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setRowVariable('');
    setColumnVariable('');
    setCrossTabResult(null);
    
    // Clear saved results from localStorage
    localStorage.removeItem('cross_tabulation_results');
    
    // Update the current dataset in the DataContext
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };
  
  // Handle row variable selection change
  const handleRowVariableChange = (event: SelectChangeEvent<string>) => {
    setRowVariable(event.target.value);
    setCrossTabResult(null);
    localStorage.removeItem('cross_tabulation_results');
  };
  
  // Handle column variable selection change
  const handleColumnVariableChange = (event: SelectChangeEvent<string>) => {
    setColumnVariable(event.target.value);
    setCrossTabResult(null);
    localStorage.removeItem('cross_tabulation_results');
  };
  
  // Swap row and column variables
  const handleSwapVariables = () => {
    const temp = rowVariable;
    setRowVariable(columnVariable);
    setColumnVariable(temp);
    setCrossTabResult(null);
    localStorage.removeItem('cross_tabulation_results');
  };
  
  // Calculate expected frequencies
  const calculateExpectedFrequencies = (
    frequencies: Record<string, Record<string, number>>,
    rowTotals: Record<string, number>,
    columnTotals: Record<string, number>,
    grandTotal: number
  ) => {
    const expected: Record<string, Record<string, number>> = {};
    const residuals: Record<string, Record<string, number>> = {};
    const standardizedResiduals: Record<string, Record<string, number>> = {};
    
    Object.keys(frequencies).forEach(row => {
      expected[row] = {};
      residuals[row] = {};
      standardizedResiduals[row] = {};
      
      Object.keys(frequencies[row]).forEach(col => {
        const expectedValue = (rowTotals[row] * columnTotals[col]) / grandTotal;
        expected[row][col] = expectedValue;
        
        const observed = frequencies[row][col];
        const residual = observed - expectedValue;
        residuals[row][col] = residual;
        
        // Standardized residual
        if (expectedValue > 0) {
          standardizedResiduals[row][col] = residual / Math.sqrt(expectedValue);
        } else {
          standardizedResiduals[row][col] = 0;
        }
      });
    });
    
    return { expected, residuals, standardizedResiduals };
  };
  
  // Run cross-tabulation analysis
  const runCrossTabulation = () => {
    if (!currentDataset || !rowVariable || !columnVariable) {
      setError('Please select a dataset and both row and column variables.');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    // Simulate async operation
    setTimeout(() => {
      try {
        // Find the selected columns
        const rowCol = currentDataset.columns.find(col => col.id === rowVariable);
        const colCol = currentDataset.columns.find(col => col.id === columnVariable);
        
        if (!rowCol || !colCol) {
          throw new Error('Selected columns not found in dataset.');
        }
        
        // Get column data using enhanced missing data handling
        const allRowValues: string[] = [];
        const allColValues: string[] = [];

        // Extract values respecting user-defined missing value codes
        currentDataset.data.forEach(row => {
          const rowValue = row[rowCol.name];
          const colValue = row[colCol.name];

          const rowMissingInfo = isMissingValue(rowValue, rowCol);
          const colMissingInfo = isMissingValue(colValue, colCol);

          // Only include rows where both values are not missing (or include missing if not excluding)
          if (!excludeMissing || (!rowMissingInfo.isMissing && !colMissingInfo.isMissing)) {
            // For missing values, use 'Missing' label only if not excluding missing
            const rowStr = rowMissingInfo.isMissing && !excludeMissing ? 'Missing' : String(rowValue ?? 'Missing');
            const colStr = colMissingInfo.isMissing && !excludeMissing ? 'Missing' : String(colValue ?? 'Missing');

            // If excluding missing, only add if both are not missing
            if (!excludeMissing || (!rowMissingInfo.isMissing && !colMissingInfo.isMissing)) {
              allRowValues.push(rowStr);
              allColValues.push(colStr);
            }
          }
        });

        const rowValues = allRowValues;
        const colValues = allColValues;
        
        // Create cross-tabulation
        const crossTab = createCrossTabulation(rowValues, colValues);

        // Get unique categories using custom ordering if defined, respecting missing value codes
        const rowColumn = currentDataset.columns.find(col => col.id === rowVariable);
        const columnColumn = currentDataset.columns.find(col => col.id === columnVariable);

        // Extract valid categories (excluding missing values) for ordering
        const validRowValues = rowColumn ? extractCategoricalValuesWithMissingCodes(currentDataset.data, rowColumn) : [];
        const validColValues = columnColumn ? extractCategoricalValuesWithMissingCodes(currentDataset.data, columnColumn) : [];

        // Use ordered categories if defined, otherwise use unique values from the actual cross-tab data
        const rowCategories = rowColumn && rowColumn.categoryOrder && rowColumn.categoryOrder.length > 0
          ? rowColumn.categoryOrder.filter(cat => Object.keys(crossTab).includes(cat))
          : Object.keys(crossTab);
        const columnCategories = columnColumn && columnColumn.categoryOrder && columnColumn.categoryOrder.length > 0
          ? columnColumn.categoryOrder.filter(cat => rowCategories.length > 0 && Object.keys(crossTab[rowCategories[0]] || {}).includes(cat))
          : Object.keys(rowCategories.length > 0 ? crossTab[rowCategories[0]] || {} : {});
        
        // Calculate row and column totals
        const rowTotals: Record<string, number> = {};
        const columnTotals: Record<string, number> = {};
        let grandTotal = 0;
        
        rowCategories.forEach(row => {
          rowTotals[row] = 0;
          columnCategories.forEach(col => {
            const count = crossTab[row][col] || 0;
            rowTotals[row] += count;
            columnTotals[col] = (columnTotals[col] || 0) + count;
            grandTotal += count;
          });
        });
        
        // Calculate percentages
        const rowPercentages: Record<string, Record<string, number>> = {};
        const columnPercentages: Record<string, Record<string, number>> = {};
        const totalPercentages: Record<string, Record<string, number>> = {};
        
        rowCategories.forEach(row => {
          rowPercentages[row] = {};
          columnPercentages[row] = {};
          totalPercentages[row] = {};
          
          columnCategories.forEach(col => {
            const count = crossTab[row][col] || 0;
            rowPercentages[row][col] = rowTotals[row] > 0 ? count / rowTotals[row] : 0;
            columnPercentages[row][col] = columnTotals[col] > 0 ? count / columnTotals[col] : 0;
            totalPercentages[row][col] = grandTotal > 0 ? count / grandTotal : 0;
          });
        });
        
        // Calculate expected frequencies and residuals
        let expectedFrequencies, residuals, standardizedResiduals;
        if (showExpectedFrequencies || showResiduals || includeChiSquare) {
          const calculations = calculateExpectedFrequencies(
            crossTab,
            rowTotals,
            columnTotals,
            grandTotal
          );
          expectedFrequencies = calculations.expected;
          residuals = calculations.residuals;
          standardizedResiduals = calculations.standardizedResiduals;
        }
        
        // Run chi-square test if requested
        let chiSquareResult = undefined;
        if (includeChiSquare) {
          // Convert cross-tab to format needed for chi-square
          const contingencyTable = rowCategories.map(row => 
            columnCategories.map(col => crossTab[row][col] || 0)
          );
          
          try {
            // Use enhanced chi-square test with Yates' correction when needed
            chiSquareResult = chiSquareTestWithYates(contingencyTable);
          } catch (e) {
            console.error('Chi-square calculation failed:', e);
          }
        }
        
        // Prepare chart data
        const chartData: Array<{ category: string; [key: string]: string | number }> = rowCategories.map(rowName => {
          const dataPoint: { category: string; [key: string]: string | number } = { category: rowName };
          columnCategories.forEach(colName => {
            dataPoint[colName] = crossTab[rowName]?.[colName] || 0;
          });
          return dataPoint;
        });
        
        // Prepare pie chart data (for total distribution)
        const pieData = rowCategories.map(row => ({
          name: row,
          value: rowTotals[row]
        }));
        



        
        const results = {
          rowCategories,
          columnCategories,
          frequencies: crossTab,
          rowPercentages,
          columnPercentages,
          totalPercentages,
          rowTotals,
          columnTotals,
          grandTotal,
          expectedFrequencies,
          residuals,
          standardizedResiduals,
          chiSquare: chiSquareResult,
          chartData,
          pieData
        };
        
        setCrossTabResult(results);
        
        // Save results to localStorage for persistence
        localStorage.setItem('cross_tabulation_results', JSON.stringify(results));
        
        setLoading(false);
      } catch (err) {
        setError(`Error analyzing data: ${err instanceof Error ? err.message : String(err)}`);
        setLoading(false);
      }
    }, 1000); // Simulate loading delay
  };
  
  // Export results to CSV
  const exportToCSV = () => {
    if (!crossTabResult) return;
    
    let csv = `${currentDataset?.columns.find(col => col.id === rowVariable)?.name || 'Row'} / ${currentDataset?.columns.find(col => col.id === columnVariable)?.name || 'Column'}`;
    
    // Header row
    crossTabResult.columnCategories.forEach(col => {
      csv += `,${col}`;
    });
    csv += ',Total\n';
    
    // Data rows
    crossTabResult.rowCategories.forEach(row => {
      csv += row;
      crossTabResult.columnCategories.forEach(col => {
        csv += `,${crossTabResult.frequencies[row][col] || 0}`;
      });
      csv += `,${crossTabResult.rowTotals[row]}\n`;
    });
    
    // Total row
    csv += 'Total';
    crossTabResult.columnCategories.forEach(col => {
      csv += `,${crossTabResult.columnTotals[col]}`;
    });
    csv += `,${crossTabResult.grandTotal}\n`;
    
    // Add chi-square results if available
    if (crossTabResult.chiSquare) {
      csv += '\n\nChi-Square Test Results\n';
      csv += `Chi-Square Value,${crossTabResult.chiSquare.chiSquare.toFixed(3)}\n`;
      csv += `Degrees of Freedom,${crossTabResult.chiSquare.df}\n`;
      csv += `P-Value,${crossTabResult.chiSquare.pValue.toFixed(3)}\n`;
      csv += `Cramer's V,${crossTabResult.chiSquare.cramersV.toFixed(3)}\n`;
      if (crossTabResult.chiSquare.yatesCorrection) {
        csv += `Yates Correction Applied,Yes\n`;
        csv += `Minimum Expected Frequency,${crossTabResult.chiSquare.minExpectedFrequency?.toFixed(2)}\n`;
      }
    }
    
    // Download
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'cross_tabulation_results.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };
  
  // Generate muted, professional colors for chart bars
  const generateColors = (count: number) => {
    // Use muted, professional color palette that works well in both light and dark themes
    const colors = [
      theme.palette.mode === 'dark' ? 'rgba(33, 150, 243, 0.8)' : 'rgba(25, 118, 210, 0.8)',   // Muted blue
      theme.palette.mode === 'dark' ? 'rgba(156, 39, 176, 0.8)' : 'rgba(156, 39, 176, 0.8)',   // Muted purple
      theme.palette.mode === 'dark' ? 'rgba(76, 175, 80, 0.8)' : 'rgba(46, 125, 50, 0.8)',     // Muted green
      theme.palette.mode === 'dark' ? 'rgba(255, 152, 0, 0.8)' : 'rgba(237, 108, 2, 0.8)',     // Muted orange
      theme.palette.mode === 'dark' ? 'rgba(244, 67, 54, 0.8)' : 'rgba(198, 40, 40, 0.8)',     // Muted red
      theme.palette.mode === 'dark' ? 'rgba(0, 188, 212, 0.8)' : 'rgba(0, 151, 167, 0.8)',     // Muted cyan
      theme.palette.mode === 'dark' ? 'rgba(121, 85, 72, 0.8)' : 'rgba(93, 64, 55, 0.8)',      // Muted brown
      theme.palette.mode === 'dark' ? 'rgba(158, 158, 158, 0.8)' : 'rgba(97, 97, 97, 0.8)',    // Muted grey
      theme.palette.mode === 'dark' ? 'rgba(255, 193, 7, 0.8)' : 'rgba(255, 143, 0, 0.8)',     // Muted amber
      theme.palette.mode === 'dark' ? 'rgba(103, 58, 183, 0.8)' : 'rgba(81, 45, 168, 0.8)',    // Muted deep purple
      theme.palette.mode === 'dark' ? 'rgba(63, 81, 181, 0.8)' : 'rgba(48, 63, 159, 0.8)',     // Muted indigo
      theme.palette.mode === 'dark' ? 'rgba(139, 195, 74, 0.8)' : 'rgba(104, 159, 56, 0.8)',   // Muted light green
    ];

    const result = [];
    for (let i = 0; i < count; i++) {
      result.push(colors[i % colors.length]);
    }

    return result;
  };
  
  // Get interpretation of effect size
  const getEffectSizeInterpretation = (cramersV: number) => {
    if (cramersV < 0.1) return { label: 'Negligible', color: 'text.secondary' };
    if (cramersV < 0.3) return { label: 'Small', color: 'info.main' };
    if (cramersV < 0.5) return { label: 'Medium', color: 'warning.main' };
    return { label: 'Large', color: 'success.main' };
  };
  
  return (
    <Box p={3}>
      <Box display="flex" alignItems="center" mb={3}>
        <AssessmentIcon sx={{ fontSize: 32, mr: 2, color: theme.palette.primary.main }} />
        <Typography variant="h4" component="h1">
          Cross Tabulation Analysis
        </Typography>
        <Box ml="auto">
          <Tooltip title="Help">
            <IconButton onClick={() => window.location.href = '/app#video-tutorials'}>
              <HelpOutlineIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      
      <Fade in timeout={500}>
        <Paper elevation={3} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <FilterListIcon sx={{ mr: 1 }} />
            Variable Selection
          </Typography>
          
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={5}>
              <FormControl fullWidth margin="normal" variant="outlined">
                <InputLabel id="dataset-select-label">Dataset</InputLabel>
                <Select
                  labelId="dataset-select-label"
                  id="dataset-select"
                  value={selectedDatasetId}
                  label="Dataset"
                  onChange={handleDatasetChange}
                  disabled={datasets.length === 0}
                >
                  {datasets.length === 0 ? (
                    <MenuItem value="" disabled>
                      No datasets available
                    </MenuItem>
                  ) : (
                    datasets.map(dataset => (
                      <MenuItem key={dataset.id} value={dataset.id}>
                        <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                          <span>{dataset.name}</span>
                          <Chip label={`${dataset.data.length} rows`} size="small" />
                        </Box>
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={3}>
              <FormControl fullWidth margin="normal" variant="outlined">
                <InputLabel id="row-variable-label">Row Variable</InputLabel>
                <Select
                  labelId="row-variable-label"
                  id="row-variable"
                  value={rowVariable}
                  label="Row Variable"
                  onChange={handleRowVariableChange}
                  disabled={categoricalColumns.length === 0}
                >
                  {categoricalColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No categorical variables
                    </MenuItem>
                  ) : (
                    categoricalColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={1} sx={{ display: 'flex', justifyContent: 'center' }}>
              <Tooltip title="Swap Variables">
                <IconButton 
                  onClick={handleSwapVariables}
                  disabled={!rowVariable || !columnVariable}
                  color="primary"
                  size="large"
                >
                  <SwapHorizIcon />
                </IconButton>
              </Tooltip>
            </Grid>
            
            <Grid item xs={12} md={3}>
              <FormControl fullWidth margin="normal" variant="outlined">
                <InputLabel id="column-variable-label">Column Variable</InputLabel>
                <Select
                  labelId="column-variable-label"
                  id="column-variable"
                  value={columnVariable}
                  label="Column Variable"
                  onChange={handleColumnVariableChange}
                  disabled={categoricalColumns.length === 0}
                >
                  {categoricalColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No categorical variables
                    </MenuItem>
                  ) : (
                    categoricalColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          
          <Box mt={3}>
            <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <SettingsIcon sx={{ mr: 1 }} />
              Analysis Options
            </Typography>

            {/* Primary Analysis Options */}
            <Box sx={{
              p: 2,
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              bgcolor: 'background.paper',
              mb: 2
            }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontWeight: 'medium' }}>
                Core Analysis Settings
              </Typography>

              {!excludeMissing && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Missing Data Notice:</strong> User-defined missing value codes will be included as separate categories.
                    Enable "Exclude Missing" to properly handle missing data in your analysis.
                  </Typography>
                </Alert>
              )}

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={excludeMissing}
                        onChange={(e) => setExcludeMissing(e.target.checked)}
                        color="warning"
                      />
                    }
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="body2" fontWeight="medium" color="warning.main">
                          Exclude Missing
                        </Typography>
                        <Tooltip title="Exclude user-defined missing value codes from analysis" arrow>
                          <HelpOutlineIcon sx={{ ml: 0.5, fontSize: 16, color: 'text.secondary' }} />
                        </Tooltip>
                      </Box>
                    }
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includeChiSquare}
                        onChange={(e) => setIncludeChiSquare(e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Chi-Square Test"
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={showExpectedFrequencies}
                        onChange={(e) => setShowExpectedFrequencies(e.target.checked)}
                        color="secondary"
                      />
                    }
                    label="Expected Frequencies"
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={showResiduals}
                        onChange={(e) => setShowResiduals(e.target.checked)}
                        color="secondary"
                      />
                    }
                    label="Residuals"
                  />
                </Grid>
              </Grid>
            </Box>

            {/* Display Options */}
            <Box sx={{
              p: 2,
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              bgcolor: 'background.paper'
            }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontWeight: 'medium' }}>
                Display Options
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includePercentages}
                        onChange={(e) => setIncludePercentages(e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Total Percentages"
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includeRowPercentages}
                        onChange={(e) => setIncludeRowPercentages(e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Row Percentages"
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includeColumnPercentages}
                        onChange={(e) => setIncludeColumnPercentages(e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Column Percentages"
                  />
                </Grid>
              </Grid>
            </Box>
          </Box>
          
          <Box mt={3} display="flex" gap={2} flexWrap="wrap">
            <Button
              variant="contained"
              color="primary"
              size="large"
              startIcon={<ViewComfyIcon />}
              onClick={runCrossTabulation}
              disabled={loading || !rowVariable || !columnVariable}
              sx={{ minWidth: 200 }}
            >
              {loading ? <CircularProgress size={24} color="inherit" /> : 'Generate Analysis'}
            </Button>
            
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<RefreshIcon />}
              onClick={() => {
                setCrossTabResult(null);
                localStorage.removeItem('cross_tabulation_results');
              }}
              disabled={!crossTabResult}
            >
              Clear Results
            </Button>
          </Box>
        </Paper>
      </Fade>
      
      {error && (
        <Zoom in>
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        </Zoom>
      )}
      
      {crossTabResult && !loading && (
        <Fade in timeout={700}>
          <Box>
            <Paper elevation={3} sx={{ borderRadius: 2, overflow: 'hidden' }}>
              <Tabs
                value={activeTab}
                onChange={(_, newValue) => setActiveTab(newValue)}
                indicatorColor="primary"
                textColor="primary"
                variant="fullWidth"
              >
                <Tab label="Table View" icon={<TableChartIcon />} iconPosition="start" />
                <Tab label="Visualizations" icon={<BarChartIcon />} iconPosition="start" />
                <Tab label="Statistical Analysis" icon={<InsightsIcon />} iconPosition="start" />
              </Tabs>
              
              <Box p={3}>
                {activeTab === 0 && (
                  <Box>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Typography variant="h6">
                        Cross Tabulation Table
                      </Typography>
                      <Button
                        startIcon={<DownloadIcon />}
                        variant="outlined"
                        size="small"
                        onClick={exportToCSV}
                      >
                        Export CSV
                      </Button>
                    </Box>
                    
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell 
                              sx={{ 
                                fontWeight: 'bold', 
                                borderRight: `2px solid ${theme.palette.divider}`,
                                borderBottom: `2px solid ${theme.palette.divider}`,
                                backgroundColor: theme.palette.grey[100]
                              }}
                            >
                              {`${currentDataset?.columns.find(col => col.id === rowVariable)?.name || 'Row'} / ${currentDataset?.columns.find(col => col.id === columnVariable)?.name || 'Column'}`}
                            </TableCell>
                            
                            {crossTabResult.columnCategories.map(category => (
                              <TableCell 
                                key={category} 
                                align="center"
                                sx={{ 
                                  fontWeight: 'bold',
                                  borderBottom: `2px solid ${theme.palette.divider}`,
                                  backgroundColor: theme.palette.grey[50]
                                }}
                              >
                                {category}
                              </TableCell>
                            ))}
                            
                            <TableCell 
                              align="center" 
                              sx={{ 
                                fontWeight: 'bold',
                                borderLeft: `2px solid ${theme.palette.divider}`,
                                borderBottom: `2px solid ${theme.palette.divider}`,
                                backgroundColor: theme.palette.primary.light,
                                color: theme.palette.primary.contrastText
                              }}
                            >
                              Total
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {crossTabResult.rowCategories.map((row, rowIndex) => (
                            <TableRow 
                              key={row}
                              sx={{ 
                                '&:hover': { 
                                  backgroundColor: theme.palette.action.hover 
                                }
                              }}
                            >
                              <TableCell 
                                sx={{ 
                                  fontWeight: 'bold',
                                  borderRight: `2px solid ${theme.palette.divider}`,
                                  backgroundColor: theme.palette.grey[50]
                                }}
                              >
                                {row}
                              </TableCell>
                              
                              {crossTabResult.columnCategories.map(col => (
                                <TableCell key={col} align="center">
                                  <Box>
                                    <Typography variant="body2" fontWeight="bold">
                                      {crossTabResult.frequencies[row][col] || 0}
                                    </Typography>
                                    
                                    {showExpectedFrequencies && crossTabResult.expectedFrequencies && (
                                      <Typography variant="caption" display="block" color="info.main">
                                        Expected: {(crossTabResult.expectedFrequencies[row][col] || 0).toFixed(1)}
                                      </Typography>
                                    )}
                                    
                                    {showResiduals && crossTabResult.standardizedResiduals && (
                                      <Typography 
                                        variant="caption" 
                                        display="block" 
                                        color={
                                          Math.abs(crossTabResult.standardizedResiduals[row][col] || 0) > 2 
                                            ? 'error.main' 
                                            : 'text.secondary'
                                        }
                                      >
                                        Std. Res: {(crossTabResult.standardizedResiduals[row][col] || 0).toFixed(2)}
                                      </Typography>
                                    )}
                                    
                                    {includePercentages && (
                                      <Typography variant="caption" display="block" color="text.secondary">
                                        {((crossTabResult.totalPercentages[row][col] || 0) * 100).toFixed(1)}%
                                      </Typography>
                                    )}
                                    
                                    {includeRowPercentages && (
                                      <Typography variant="caption" display="block" color="primary">
                                        →{((crossTabResult.rowPercentages[row][col] || 0) * 100).toFixed(1)}%
                                      </Typography>
                                    )}
                                    
                                    {includeColumnPercentages && (
                                      <Typography variant="caption" display="block" color="secondary">
                                        ↓{((crossTabResult.columnPercentages[row][col] || 0) * 100).toFixed(1)}%
                                      </Typography>
                                    )}
                                  </Box>
                                </TableCell>
                              ))}
                              
                              <TableCell 
                                align="center" 
                                sx={{ 
                                  fontWeight: 'bold',
                                  borderLeft: `2px solid ${theme.palette.divider}`,
                                  backgroundColor: theme.palette.grey[50]
                                }}
                              >
                                {crossTabResult.rowTotals[row] || 0}
                                {includePercentages && (
                                  <Typography variant="caption" display="block" color="text.secondary">
                                    {((crossTabResult.rowTotals[row] / crossTabResult.grandTotal || 0) * 100).toFixed(1)}%
                                  </Typography>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                          
                          <TableRow sx={{ borderTop: `2px solid ${theme.palette.divider}` }}>
                            <TableCell 
                              sx={{ 
                                fontWeight: 'bold',
                                borderRight: `2px solid ${theme.palette.divider}`,
                                backgroundColor: theme.palette.primary.light,
                                color: theme.palette.primary.contrastText
                              }}
                            >
                              Total
                            </TableCell>
                            
                            {crossTabResult.columnCategories.map(col => (
                              <TableCell 
                                key={col} 
                                align="center" 
                                sx={{ 
                                  fontWeight: 'bold',
                                  backgroundColor: theme.palette.grey[50]
                                }}
                              >
                                {crossTabResult.columnTotals[col] || 0}
                                {includePercentages && (
                                  <Typography variant="caption" display="block" color="text.secondary">
                                    {((crossTabResult.columnTotals[col] / crossTabResult.grandTotal || 0) * 100).toFixed(1)}%
                                  </Typography>
                                )}
                              </TableCell>
                            ))}
                            
                            <TableCell 
                              align="center" 
                              sx={{ 
                                fontWeight: 'bold',
                                borderLeft: `2px solid ${theme.palette.divider}`,
                                backgroundColor: theme.palette.primary.main,
                                color: theme.palette.primary.contrastText
                              }}
                            >
                              {crossTabResult.grandTotal}
                              {includePercentages && (
                                <Typography variant="caption" display="block">
                                  100.0%
                                </Typography>
                              )}
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>
                )}
                
                {activeTab === 1 && (
                  <Box>
                    <Box mb={3}>
                      <ToggleButtonGroup
                        value={visualizationType}
                        exclusive
                        onChange={(_, newType) => newType && setVisualizationType(newType)}
                        size="small"
                      >
                        <ToggleButton value="stacked">
                          <Tooltip title="Stacked Bar Chart">
                            <BarChartIcon />
                          </Tooltip>
                        </ToggleButton>
                        <ToggleButton value="grouped">
                          <Tooltip title="Grouped Bar Chart">
                            <ViewComfyIcon />
                          </Tooltip>
                        </ToggleButton>
                        <ToggleButton value="pie">
                          <Tooltip title="Pie Chart">
                            <PieChartIcon />
                          </Tooltip>
                        </ToggleButton>

                      </ToggleButtonGroup>
                    </Box>
                    
                    {visualizationType === 'stacked' && (
                      <Box height={400}>
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={crossTabResult.chartData}
                            margin={{
                              top: 20,
                              right: 30,
                              left: 20,
                              bottom: 60,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis 
                              dataKey="category" 
                              angle={-45} 
                              textAnchor="end"
                              height={70}
                              interval={0}
                            />
                            <YAxis />
                            <RechartsTooltip />
                            <Legend />
                            {crossTabResult.columnCategories.map((category, index) => (
                              <Bar
                                key={category}
                                dataKey={category}
                                name={category}
                                stackId="a"
                                fill={generateColors(crossTabResult.columnCategories.length)[index]}
                              />
                            ))}
                          </BarChart>
                        </ResponsiveContainer>
                      </Box>
                    )}
                    
                    {visualizationType === 'grouped' && (
                      <Box height={400}>
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={crossTabResult.chartData}
                            margin={{
                              top: 20,
                              right: 30,
                              left: 20,
                              bottom: 60,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis 
                              dataKey="category" 
                              angle={-45} 
                              textAnchor="end"
                              height={70}
                              interval={0}
                            />
                            <YAxis />
                            <RechartsTooltip />
                            <Legend />
                            {crossTabResult.columnCategories.map((category, index) => (
                              <Bar
                                key={category}
                                dataKey={category}
                                name={category}
                                fill={generateColors(crossTabResult.columnCategories.length)[index]}
                              />
                            ))}
                          </BarChart>
                        </ResponsiveContainer>
                      </Box>
                    )}
                    
                    {visualizationType === 'pie' && crossTabResult.pieData && (
                      <Box>
                        <Typography variant="h6" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
                          Cross-Tabulation Distribution: {currentDataset?.columns.find(col => col.id === rowVariable)?.name} by {currentDataset?.columns.find(col => col.id === columnVariable)?.name}
                        </Typography>

                        <Grid container spacing={3}>
                          {/* Main distribution pie chart */}
                          <Grid item xs={12} md={6}>
                            <Paper elevation={2} sx={{ p: 2, borderRadius: 2 }}>
                              <Typography variant="subtitle1" gutterBottom sx={{ textAlign: 'center' }}>
                                {currentDataset?.columns.find(col => col.id === rowVariable)?.name} Distribution
                              </Typography>
                              <Box height={300}>
                                <ResponsiveContainer width="100%" height="100%">
                                  <PieChart>
                                    <Pie
                                      activeIndex={pieActiveIndex}
                                      activeShape={renderActiveShape}
                                      data={crossTabResult.pieData}
                                      cx="50%"
                                      cy="50%"
                                      innerRadius={40}
                                      outerRadius={80}
                                      fill="#8884d8"
                                      dataKey="value"
                                      onMouseEnter={(_, index) => setPieActiveIndex(index)}
                                    >
                                      {crossTabResult.pieData.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={generateColors(crossTabResult.pieData!.length)[index]} />
                                      ))}
                                    </Pie>
                                    <RechartsTooltip
                                      formatter={(value: any) => [value, 'Count']}
                                      labelFormatter={(label) => `${currentDataset?.columns.find(col => col.id === rowVariable)?.name}: ${label}`}
                                    />
                                  </PieChart>
                                </ResponsiveContainer>
                              </Box>
                            </Paper>
                          </Grid>

                          {/* Nested donut charts for each row category */}
                          <Grid item xs={12} md={6}>
                            <Paper elevation={2} sx={{ p: 2, borderRadius: 2 }}>
                              <Typography variant="subtitle1" gutterBottom sx={{ textAlign: 'center' }}>
                                {currentDataset?.columns.find(col => col.id === columnVariable)?.name} within {currentDataset?.columns.find(col => col.id === rowVariable)?.name}
                              </Typography>
                              <Box height={300}>
                                <ResponsiveContainer width="100%" height="100%">
                                  <PieChart>
                                    {/* Outer ring - Row categories */}
                                    <Pie
                                      data={crossTabResult.pieData}
                                      cx="50%"
                                      cy="50%"
                                      innerRadius={80}
                                      outerRadius={100}
                                      fill="#8884d8"
                                      dataKey="value"
                                      stroke="white"
                                      strokeWidth={2}
                                    >
                                      {crossTabResult.pieData.map((entry, index) => (
                                        <Cell key={`outer-${index}`} fill={generateColors(crossTabResult.pieData!.length)[index]} />
                                      ))}
                                    </Pie>

                                    {/* Inner ring - Column categories breakdown */}
                                    <Pie
                                      data={(() => {
                                        const innerData: Array<{name: string, value: number, parentCategory: string}> = [];
                                        crossTabResult.rowCategories.forEach(rowCat => {
                                          crossTabResult.columnCategories.forEach(colCat => {
                                            const value = crossTabResult.frequencies[rowCat][colCat] || 0;
                                            if (value > 0) {
                                              innerData.push({
                                                name: `${rowCat} - ${colCat}`,
                                                value: value,
                                                parentCategory: rowCat
                                              });
                                            }
                                          });
                                        });
                                        return innerData;
                                      })()}
                                      cx="50%"
                                      cy="50%"
                                      innerRadius={40}
                                      outerRadius={75}
                                      fill="#82ca9d"
                                      dataKey="value"
                                      stroke="white"
                                      strokeWidth={1}
                                    >
                                      {(() => {
                                        const innerData: Array<{name: string, value: number, parentCategory: string}> = [];
                                        crossTabResult.rowCategories.forEach(rowCat => {
                                          crossTabResult.columnCategories.forEach(colCat => {
                                            const value = crossTabResult.frequencies[rowCat][colCat] || 0;
                                            if (value > 0) {
                                              innerData.push({
                                                name: `${rowCat} - ${colCat}`,
                                                value: value,
                                                parentCategory: rowCat
                                              });
                                            }
                                          });
                                        });
                                        return innerData.map((entry, index) => {
                                          const parentIndex = crossTabResult.rowCategories.indexOf(entry.parentCategory);
                                          const baseColor = generateColors(crossTabResult.rowCategories.length)[parentIndex];
                                          // Create variations of the parent color for column categories
                                          const opacity = 0.6 + (index % crossTabResult.columnCategories.length) * 0.1;
                                          return (
                                            <Cell
                                              key={`inner-${index}`}
                                              fill={baseColor}
                                              fillOpacity={opacity}
                                            />
                                          );
                                        });
                                      })()}
                                    </Pie>

                                    <RechartsTooltip
                                      formatter={(value: any, name: string, props: any) => {
                                        if (props.payload.parentCategory) {
                                          return [value, `${props.payload.name.split(' - ')[1]} in ${props.payload.parentCategory}`];
                                        }
                                        return [value, 'Count'];
                                      }}
                                    />
                                  </PieChart>
                                </ResponsiveContainer>
                              </Box>
                            </Paper>
                          </Grid>

                          {/* Legend and summary */}
                          <Grid item xs={12}>
                            <Paper elevation={1} sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Chart Interpretation:
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                • <strong>Left chart:</strong> Shows the overall distribution of {currentDataset?.columns.find(col => col.id === rowVariable)?.name} categories
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                • <strong>Right chart:</strong> Nested view showing how {currentDataset?.columns.find(col => col.id === columnVariable)?.name} is distributed within each {currentDataset?.columns.find(col => col.id === rowVariable)?.name} category
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                • <strong>Outer ring:</strong> {currentDataset?.columns.find(col => col.id === rowVariable)?.name} categories
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                • <strong>Inner ring:</strong> {currentDataset?.columns.find(col => col.id === columnVariable)?.name} breakdown within each {currentDataset?.columns.find(col => col.id === rowVariable)?.name}
                              </Typography>
                            </Paper>
                          </Grid>
                        </Grid>
                      </Box>
                    )}
                    

                  </Box>
                )}
                
                {activeTab === 2 && (
                  <Box>
                    {crossTabResult.chiSquare ? (
                      <Grid container spacing={3}>
                        {crossTabResult.chiSquare.yatesCorrection && (
                          <Grid item xs={12}>
                            <Alert 
                              severity="warning" 
                              icon={<WarningIcon />}
                            >
                              <Typography variant="body2">
                                <strong>Yates' Continuity Correction Applied:</strong> At least one expected frequency was less than 5 
                                (minimum: {crossTabResult.chiSquare.minExpectedFrequency?.toFixed(2)}). 
                                The chi-square test has been adjusted for small sample sizes.
                              </Typography>
                            </Alert>
                          </Grid>
                        )}
                        
                        <Grid item xs={12}>
                          <Alert
                            severity={crossTabResult.chiSquare.pValue < 0.05 ? "success" : "info"}
                            icon={<InsightsIcon />}
                          >
                            <Typography variant="body1" fontWeight="bold">
                              {crossTabResult.chiSquare.pValue < 0.05
                                ? `Significant Association Found`
                                : `No Significant Association`}
                            </Typography>
                            <Typography variant="body2" sx={{ mt: 1 }}>
                              {crossTabResult.chiSquare.pValue < 0.05
                                ? `There is a statistically significant relationship between ${currentDataset?.columns.find(col => col.id === rowVariable)?.name} and ${currentDataset?.columns.find(col => col.id === columnVariable)?.name} (p ${crossTabResult.chiSquare.pValue < 0.001 ? '< 0.001' : `= ${crossTabResult.chiSquare.pValue.toFixed(3)}`}). The distribution of ${currentDataset?.columns.find(col => col.id === columnVariable)?.name} varies significantly across different ${currentDataset?.columns.find(col => col.id === rowVariable)?.name} categories.`
                                : `The relationship between ${currentDataset?.columns.find(col => col.id === rowVariable)?.name} and ${currentDataset?.columns.find(col => col.id === columnVariable)?.name} is not statistically significant (p = ${crossTabResult.chiSquare.pValue.toFixed(3)}). The distribution of ${currentDataset?.columns.find(col => col.id === columnVariable)?.name} appears to be independent of ${currentDataset?.columns.find(col => col.id === rowVariable)?.name} categories.`}
                            </Typography>
                          </Alert>
                        </Grid>
                        
                        <Grid item xs={12} sm={6} md={3}>
                          <Card>
                            <CardContent>
                              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                Chi-Square Value
                                {crossTabResult.chiSquare.yatesCorrection && (
                                  <Chip 
                                    label="Yates" 
                                    size="small" 
                                    color="warning" 
                                    sx={{ ml: 1 }}
                                  />
                                )}
                              </Typography>
                              <Typography variant="h4">
                                {crossTabResult.chiSquare.chiSquare.toFixed(3)}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        
                        <Grid item xs={12} sm={6} md={3}>
                          <Card>
                            <CardContent>
                              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                Degrees of Freedom
                              </Typography>
                              <Typography variant="h4">
                                {crossTabResult.chiSquare.df}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        
                        <Grid item xs={12} sm={6} md={3}>
                          <Card>
                            <CardContent>
                              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                P-Value
                              </Typography>
                              <Typography 
                                variant="h4"
                                color={crossTabResult.chiSquare.pValue < 0.05 ? 'error.main' : 'text.primary'}
                              >
                                {crossTabResult.chiSquare.pValue < 0.001 
                                  ? '< 0.001' 
                                  : crossTabResult.chiSquare.pValue.toFixed(3)}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        
                        <Grid item xs={12} sm={6} md={3}>
                          <Card>
                            <CardContent>
                              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                Effect Size (Cramér's V)
                              </Typography>
                              <Typography 
                                variant="h4"
                                color={getEffectSizeInterpretation(crossTabResult.chiSquare.cramersV).color}
                              >
                                {crossTabResult.chiSquare.cramersV.toFixed(3)}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {getEffectSizeInterpretation(crossTabResult.chiSquare.cramersV).label} effect
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        
                        <Grid item xs={12}>
                          <Paper sx={{ p: 3, backgroundColor: theme.palette.grey[50], borderRadius: 2 }}>
                            <Typography variant="subtitle1" gutterBottom>
                              <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                              Analysis Interpretation for {currentDataset?.columns.find(col => col.id === rowVariable)?.name} × {currentDataset?.columns.find(col => col.id === columnVariable)?.name}
                            </Typography>

                            <Typography variant="body2" paragraph>
                              This chi-square test examines whether the distribution of <strong>{currentDataset?.columns.find(col => col.id === columnVariable)?.name}</strong> varies
                              significantly across different categories of <strong>{currentDataset?.columns.find(col => col.id === rowVariable)?.name}</strong>.
                            </Typography>

                            <Grid container spacing={2}>
                              <Grid item xs={12} md={6}>
                                <Typography variant="subtitle2" gutterBottom color="primary">
                                  Statistical Significance:
                                </Typography>
                                <Typography variant="body2" component="div">
                                  <ul style={{ margin: 0, paddingLeft: '1.2em' }}>
                                    <li><strong>P-value {crossTabResult.chiSquare.pValue < 0.05 ? '< 0.05' : '≥ 0.05'}:</strong> {crossTabResult.chiSquare.pValue < 0.05 ? 'Significant relationship detected' : 'No significant relationship'}</li>
                                    <li><strong>Current p-value:</strong> {crossTabResult.chiSquare.pValue < 0.001 ? '< 0.001' : crossTabResult.chiSquare.pValue.toFixed(3)}</li>
                                    <li><strong>Interpretation:</strong> {crossTabResult.chiSquare.pValue < 0.05
                                      ? `Knowing someone's ${currentDataset?.columns.find(col => col.id === rowVariable)?.name} helps predict their ${currentDataset?.columns.find(col => col.id === columnVariable)?.name}`
                                      : `${currentDataset?.columns.find(col => col.id === rowVariable)?.name} and ${currentDataset?.columns.find(col => col.id === columnVariable)?.name} appear to be independent`}</li>
                                  </ul>
                                </Typography>
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <Typography variant="subtitle2" gutterBottom color="secondary">
                                  Effect Size (Cramér's V = {crossTabResult.chiSquare.cramersV.toFixed(3)}):
                                </Typography>
                                <Typography variant="body2" component="div">
                                  <ul style={{ margin: 0, paddingLeft: '1.2em' }}>
                                    <li><strong>Strength:</strong> {getEffectSizeInterpretation(crossTabResult.chiSquare.cramersV).label} association</li>
                                    <li><strong>Practical meaning:</strong> {
                                      crossTabResult.chiSquare.cramersV < 0.1 ? `Very weak relationship - ${currentDataset?.columns.find(col => col.id === rowVariable)?.name} has minimal influence on ${currentDataset?.columns.find(col => col.id === columnVariable)?.name}` :
                                      crossTabResult.chiSquare.cramersV < 0.3 ? `Weak to moderate relationship - ${currentDataset?.columns.find(col => col.id === rowVariable)?.name} has some influence on ${currentDataset?.columns.find(col => col.id === columnVariable)?.name}` :
                                      crossTabResult.chiSquare.cramersV < 0.5 ? `Moderate to strong relationship - ${currentDataset?.columns.find(col => col.id === rowVariable)?.name} has considerable influence on ${currentDataset?.columns.find(col => col.id === columnVariable)?.name}` :
                                      `Strong relationship - ${currentDataset?.columns.find(col => col.id === rowVariable)?.name} strongly determines ${currentDataset?.columns.find(col => col.id === columnVariable)?.name}`
                                    }</li>
                                    <li><strong>Scale:</strong> 0 (no association) to 1 (perfect association)</li>
                                  </ul>
                                </Typography>
                              </Grid>

                              {crossTabResult.chiSquare.yatesCorrection && (
                                <Grid item xs={12}>
                                  <Typography variant="subtitle2" gutterBottom color="warning.main">
                                    Yates' Continuity Correction Applied:
                                  </Typography>
                                  <Typography variant="body2">
                                    This correction was applied because some expected frequencies were below 5 (minimum: {crossTabResult.chiSquare.minExpectedFrequency?.toFixed(2)}).
                                    This makes the test more conservative and reduces the chance of false positive results with small sample sizes.
                                  </Typography>
                                </Grid>
                              )}

                              <Grid item xs={12}>
                                <Typography variant="subtitle2" gutterBottom color="info.main">
                                  Data-Specific Insights:
                                </Typography>
                                <Typography variant="body2" component="div">
                                  {(() => {
                                    // Find the most common row and column categories
                                    const maxRowCategory = Object.entries(crossTabResult.rowTotals).reduce((a, b) =>
                                      crossTabResult.rowTotals[a[0]] > crossTabResult.rowTotals[b[0]] ? a : b
                                    );
                                    const maxColCategory = Object.entries(crossTabResult.columnTotals).reduce((a, b) =>
                                      crossTabResult.columnTotals[a[0]] > crossTabResult.columnTotals[b[0]] ? a : b
                                    );

                                    // Find the most common combination
                                    let maxCombination = { row: '', col: '', count: 0 };
                                    crossTabResult.rowCategories.forEach(row => {
                                      crossTabResult.columnCategories.forEach(col => {
                                        const count = crossTabResult.frequencies[row][col] || 0;
                                        if (count > maxCombination.count) {
                                          maxCombination = { row, col, count };
                                        }
                                      });
                                    });

                                    return (
                                      <ul style={{ margin: 0, paddingLeft: '1.2em' }}>
                                        <li>
                                          <strong>Most common {currentDataset?.columns.find(col => col.id === rowVariable)?.name}:</strong> {maxRowCategory[0]}
                                          ({maxRowCategory[1]} cases, {((maxRowCategory[1] / crossTabResult.grandTotal) * 100).toFixed(1)}% of total)
                                        </li>
                                        <li>
                                          <strong>Most common {currentDataset?.columns.find(col => col.id === columnVariable)?.name}:</strong> {maxColCategory[0]}
                                          ({maxColCategory[1]} cases, {((maxColCategory[1] / crossTabResult.grandTotal) * 100).toFixed(1)}% of total)
                                        </li>
                                        <li>
                                          <strong>Most frequent combination:</strong> {maxCombination.row} + {maxCombination.col}
                                          ({maxCombination.count} cases, {((maxCombination.count / crossTabResult.grandTotal) * 100).toFixed(1)}% of total)
                                        </li>
                                        <li>
                                          <strong>Sample size:</strong> {crossTabResult.grandTotal} total cases across {crossTabResult.rowCategories.length} × {crossTabResult.columnCategories.length} = {crossTabResult.rowCategories.length * crossTabResult.columnCategories.length} possible combinations
                                        </li>
                                      </ul>
                                    );
                                  })()}
                                </Typography>
                              </Grid>

                              <Grid item xs={12}>
                                <Typography variant="subtitle2" gutterBottom color="success.main">
                                  Actionable Recommendations:
                                </Typography>
                                <Typography variant="body2">
                                  {crossTabResult.chiSquare.pValue < 0.05 ? (
                                    <>
                                      • <strong>Strategic focus:</strong> Consider <strong>{currentDataset?.columns.find(col => col.id === rowVariable)?.name}</strong> when making decisions about <strong>{currentDataset?.columns.find(col => col.id === columnVariable)?.name}</strong><br/>
                                      • <strong>Pattern analysis:</strong> Examine the cross-tabulation table to identify which specific combinations are over/under-represented<br/>
                                      • <strong>Practical importance:</strong> The relationship strength ({getEffectSizeInterpretation(crossTabResult.chiSquare.cramersV).label.toLowerCase()}) suggests {
                                        crossTabResult.chiSquare.cramersV > 0.3 ? 'this finding has strong practical implications for decision-making' : 'this may have limited practical importance despite being statistically significant'
                                      }<br/>
                                      • <strong>Next steps:</strong> Investigate why certain {currentDataset?.columns.find(col => col.id === rowVariable)?.name} categories are associated with specific {currentDataset?.columns.find(col => col.id === columnVariable)?.name} outcomes
                                    </>
                                  ) : (
                                    <>
                                      • <strong>Independence confirmed:</strong> <strong>{currentDataset?.columns.find(col => col.id === rowVariable)?.name}</strong> appears to be independent of <strong>{currentDataset?.columns.find(col => col.id === columnVariable)?.name}</strong><br/>
                                      • <strong>Alternative analysis:</strong> Consider other variables that might be related to <strong>{currentDataset?.columns.find(col => col.id === columnVariable)?.name}</strong><br/>
                                      • <strong>Uniform distribution:</strong> The distribution of <strong>{currentDataset?.columns.find(col => col.id === columnVariable)?.name}</strong> appears consistent across <strong>{currentDataset?.columns.find(col => col.id === rowVariable)?.name}</strong> categories<br/>
                                      • <strong>Sample considerations:</strong> Ensure adequate sample sizes in each cell before concluding independence
                                    </>
                                  )}
                                </Typography>
                              </Grid>
                            </Grid>
                          </Paper>
                        </Grid>
                      </Grid>
                    ) : (
                      <Alert severity="info">
                        Chi-square test was not performed. Enable it in the analysis options to see statistical results.
                      </Alert>
                    )}
                  </Box>
                )}
              </Box>
            </Paper>
            
            <SpeedDial
              ariaLabel="Quick actions"
              sx={{ position: 'fixed', bottom: 16, right: 16 }}
              icon={<SpeedDialIcon />}
              open={speedDialOpen}
              onOpen={() => setSpeedDialOpen(true)}
              onClose={() => setSpeedDialOpen(false)}
            >
              <SpeedDialAction
                icon={<FileDownloadIcon />}
                tooltipTitle="Export CSV"
                onClick={() => {
                  exportToCSV();
                  setSpeedDialOpen(false);
                }}
              />
              <SpeedDialAction
                icon={<PrintIcon />}
                tooltipTitle="Print"
                onClick={() => {
                  window.print();
                  setSpeedDialOpen(false);
                }}
              />
              <SpeedDialAction
                icon={<RefreshIcon />}
                tooltipTitle="Refresh Analysis"
                onClick={() => {
                  runCrossTabulation();
                  setSpeedDialOpen(false);
                }}
              />
              <SpeedDialAction
                icon={<ShareIcon />}
                tooltipTitle="Share"
                onClick={() => {
                  // Implement sharing functionality
                  setSpeedDialOpen(false);
                }}
              />
            </SpeedDial>
          </Box>
        </Fade>
      )}
      
      {/* Loading skeleton */}
      {loading && (
        <Box>
          <Skeleton variant="rectangular" height={400} sx={{ mb: 2 }} />
          <Skeleton variant="text" height={60} />
          <Skeleton variant="text" height={60} />
        </Box>
      )}
    </Box>
  );
};

export default CrossTabulation;
