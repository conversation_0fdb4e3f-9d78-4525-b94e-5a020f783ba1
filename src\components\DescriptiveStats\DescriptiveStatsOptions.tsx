import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  Card<PERSON>ontent,
  Typo<PERSON>,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import {
  Show<PERSON>hart as DescriptivesIcon,
  BarChart as FrequenciesIcon,
  GridOn as CrossTabsIcon,
  TableChart as NormalityIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';

interface DescriptiveStatsOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Quantitative' | 'Categorical' | 'Distribution';
  color: string;
}

interface DescriptiveStatsOptionsProps {
  onNavigate: (path: string) => void;
}

export const descriptiveStatsOptions: DescriptiveStatsOption[] = [
  {
    name: 'Descriptive Analysis',
    shortDescription: 'Summarize quantitative data',
    detailedDescription: 'Calculate key statistics like mean, median, mode, standard deviation, variance, range, quartiles, skewness, and kurtosis for numeric variables. Includes histograms and box plots.',
    path: 'stats/descriptives',
    icon: <DescriptivesIcon />,
    category: 'Quantitative',
    color: '#4CAF50', // Green
  },
  {
    name: 'Frequency Tables',
    shortDescription: 'Summarize categorical data',
    detailedDescription: 'Generate frequency and percentage tables for categorical, ordinal, or boolean variables. Includes options for cumulative frequencies and sorting. Visualized with bar or pie charts.',
    path: 'stats/frequencies',
    icon: <FrequenciesIcon />,
    category: 'Categorical',
    color: '#2196F3', // Blue
  },
  {
    name: 'Cross Tabulation',
    shortDescription: 'Analyze relationships between categorical variables',
    detailedDescription: 'Create two-way tables showing the joint frequency distribution of two categorical variables. Includes row, column, and total percentages, and optionally performs a Chi-Square test of independence.',
    path: 'stats/crosstabs',
    icon: <CrossTabsIcon />,
    category: 'Categorical',
    color: '#FF9800', // Orange
  },
  {
    name: 'Normality Test',
    shortDescription: 'Assess if data is normally distributed',
    detailedDescription: 'Perform statistical tests (like Kolmogorov-Smirnov) and visualize with Q-Q plots and histograms with normal curve overlays to check if a numeric variable follows a normal distribution.',
    path: 'stats/normality',
    icon: <NormalityIcon />,
    category: 'Distribution',
    color: '#9C27B0', // Purple
  },
];

const DescriptiveStatsOptions: React.FC<DescriptiveStatsOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const categories = ['All', 'Quantitative', 'Categorical', 'Distribution'];

  const filteredOptions = selectedCategory === 'All'
    ? descriptiveStatsOptions
    : descriptiveStatsOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Quantitative': return <DescriptivesIcon />;
      case 'Categorical': return <FrequenciesIcon />; // Using Frequencies icon for general categorical
      case 'Distribution': return <NormalityIcon />;
      default: return <DescriptivesIcon />;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Descriptive Statistics Tools
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Summarize, organize, and visualize your data
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Access fundamental tools to understand the basic features of your dataset,
          including central tendency, dispersion, frequency distributions, and relationships
          between categorical variables.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography variant="h6" fontWeight="bold">
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {option.shortDescription}
                </Typography>

                <Typography variant="body2" paragraph>
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Summarizing numeric data?</strong> Try Descriptive Analysis
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Counting categories?</strong> Use Frequency Tables
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Comparing two categories?</strong> Assess with Cross Tabulation
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • <strong>Checking for normal distribution?</strong> Normality Test is the tool for you
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default DescriptiveStatsOptions;
