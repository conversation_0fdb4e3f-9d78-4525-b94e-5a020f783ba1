import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  FormLabel,
  Alert,
  Divider,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Chip,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  IconButton,
  Tabs,
  Tab,
  ToggleButton,
  ToggleButtonGroup,
  useMediaQuery
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  PieChart as PieChartIcon,
  BarChart as BarChartIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  TableChart as TableChartIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  ViewCarousel as ViewCarouselIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, ColumnStatistics, Column } from '../../types';
import { calculateFrequencies, calculateProportions, getOrderedCategories } from '../../utils/dataUtilities';
import { 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  Legend, 
  Cell,
  ResponsiveContainer
} from 'recharts';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const FrequencyTables: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));
  
  // State for analysis options
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [includePercentages, setIncludePercentages] = useState<boolean>(true);
  const [includeCumulativeFreq, setIncludeCumulativeFreq] = useState<boolean>(true);
  const [sortByFrequency, setSortByFrequency] = useState<boolean>(true);
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');
  
  // State for view mode
  const [viewMode, setViewMode] = useState<'tabs' | 'stacked' | 'side-by-side'>('tabs');
  const [activeTab, setActiveTab] = useState<number>(0);
  
  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [frequencyResults, setFrequencyResults] = useState<{
    [columnId: string]: {
      column: Column;
      frequencies: Record<string, number>;
      percentages: Record<string, number>;
      cumulativeFreq: Record<string, number>;
      cumulativePerc: Record<string, number>;
      total: number;
      sortedKeys: string[];
      chartData: Array<{
        category: string;
        frequency: number;
        percentage: number;
      }>;
    }
  }>({});
  
  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('frequency_tables_results');
    
    if (savedResults) {
      try {
        setFrequencyResults(JSON.parse(savedResults));
      } catch (e) {
        console.error('Error parsing saved frequency tables results:', e);
      }
    }
  }, []);

  // Set default view mode based on screen size
  useEffect(() => {
    if (isMobile) {
      setViewMode('tabs');
    } else if (isTablet) {
      setViewMode('stacked');
    }
  }, [isMobile, isTablet]);
  
  // Get categorical columns from current dataset
  const categoricalColumns = currentDataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL || col.type === DataType.ORDINAL || col.type === DataType.BOOLEAN
  ) || [];
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedColumns([]);
    setFrequencyResults({});
    
    localStorage.removeItem('frequency_tables_results');
    
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };
  
  // Handle column selection change
  const handleColumnChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedColumns(typeof value === 'string' ? [value] : value);
    
    setFrequencyResults({});
    localStorage.removeItem('frequency_tables_results');
  };
  
  // Clear analysis options and results
  const clearAnalysis = () => {
    setFrequencyResults({});
    localStorage.removeItem('frequency_tables_results');
  };
  
  // Handle view mode change
  const handleViewModeChange = (
    event: React.MouseEvent<HTMLElement>,
    newViewMode: 'tabs' | 'stacked' | 'side-by-side' | null,
  ) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
      setActiveTab(0); // Reset to first tab when changing view mode
    }
  };
  
  // Run frequency analysis
  const runFrequencyAnalysis = () => {
    if (!currentDataset || selectedColumns.length === 0) {
      setError('Please select a dataset and at least one column to analyze.');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const selectedColumnsData = currentDataset.columns.filter(
        col => selectedColumns.includes(col.id)
      );
      
      const results: any = {};
      
      selectedColumnsData.forEach(column => {
        const values = currentDataset.data.map(row => String(row[column.name] ?? 'Missing'));

        const frequencies = calculateFrequencies(values);
        const percentages = calculateProportions(values);

        let uniqueValues: string[];

        if (sortByFrequency) {
          // Sort by frequency regardless of custom order
          uniqueValues = Object.keys(frequencies).sort((a, b) => frequencies[b] - frequencies[a]);
        } else {
          // Use custom category order if defined, otherwise alphabetical
          uniqueValues = getOrderedCategories(column, currentDataset.data);
        }
        
        const cumulativeFreq: Record<string, number> = {};
        const cumulativePerc: Record<string, number> = {};
        let runningFreq = 0;
        let runningPerc = 0;
        
        uniqueValues.forEach(value => {
          runningFreq += frequencies[value];
          runningPerc += percentages[value];
          cumulativeFreq[value] = runningFreq;
          cumulativePerc[value] = runningPerc;
        });
        
        const chartData = uniqueValues.map(category => ({
          category,
          name: category,
          frequency: frequencies[category],
          percentage: percentages[category] * 100,
          value: frequencies[category]
        }));
        
        results[column.id] = {
          column,
          frequencies,
          percentages,
          cumulativeFreq,
          cumulativePerc,
          total: values.length,
          sortedKeys: uniqueValues,
          chartData
        };
      });
      
      setFrequencyResults(results);
      localStorage.setItem('frequency_tables_results', JSON.stringify(results));
      setLoading(false);
    } catch (err) {
      setError(`Error analyzing data: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  };
  
  // Generate pie chart colors
  const generateColors = (count: number) => {
    const colors = [
      theme.palette.primary.main,
      theme.palette.secondary.main,
      theme.palette.success.main,
      theme.palette.error.main,
      theme.palette.warning.main,
      theme.palette.info.main,
      '#8884d8',
      '#82ca9d',
      '#ffc658',
      '#8dd1e1',
      '#a4de6c',
      '#d0ed57',
      '#83a6ed',
      '#ff7c7c'
    ];
    
    const result = [];
    for (let i = 0; i < count; i++) {
      result.push(colors[i % colors.length]);
    }
    
    return result;
  };

  // Render frequency table
  const renderFrequencyTable = (result: any) => (
    <TableContainer 
      component={Paper} 
      variant="outlined"
      sx={{ 
        maxHeight: isMobile ? 300 : 400, 
        borderRadius: 2,
        '& .MuiTable-root': {
          minWidth: isMobile ? 300 : 450
        }
      }}
    >
      <Table size={isMobile ? "small" : "medium"} stickyHeader>
        <TableHead>
          <TableRow>
            <TableCell sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
              Category
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
              Frequency
            </TableCell>
            {includePercentages && (
              <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                Percentage
              </TableCell>
            )}
            {includeCumulativeFreq && (
              <>
                <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                  Cumulative Freq
                </TableCell>
                {includePercentages && (
                  <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                    Cumulative %
                  </TableCell>
                )}
              </>
            )}
          </TableRow>
        </TableHead>
        <TableBody>
          {result.sortedKeys.map((category: string, index: number) => (
            <TableRow 
              key={category}
              sx={{ 
                '&:nth-of-type(odd)': { 
                  backgroundColor: theme.palette.action.hover 
                }
              }}
            >
              <TableCell sx={{ fontWeight: 500 }}>
                <Chip 
                  label={category} 
                  size="small" 
                  variant="outlined"
                  sx={{ maxWidth: isMobile ? 100 : 150 }}
                />
              </TableCell>
              <TableCell align="right" sx={{ fontWeight: 500 }}>
                {result.frequencies[category].toLocaleString()}
              </TableCell>
              {includePercentages && (
                <TableCell align="right">
                  {(result.percentages[category] * 100).toFixed(2)}%
                </TableCell>
              )}
              {includeCumulativeFreq && (
                <>
                  <TableCell align="right">
                    {result.cumulativeFreq[category].toLocaleString()}
                  </TableCell>
                  {includePercentages && (
                    <TableCell align="right">
                      {(result.cumulativePerc[category] * 100).toFixed(2)}%
                    </TableCell>
                  )}
                </>
              )}
            </TableRow>
          ))}
          <TableRow sx={{ backgroundColor: theme.palette.action.selected }}>
            <TableCell sx={{ fontWeight: 700 }}>Total</TableCell>
            <TableCell align="right" sx={{ fontWeight: 700 }}>
              {result.total.toLocaleString()}
            </TableCell>
            {includePercentages && (
              <TableCell align="right" sx={{ fontWeight: 700 }}>
                100.00%
              </TableCell>
            )}
            {includeCumulativeFreq && (
              <>
                <TableCell align="right"></TableCell>
                {includePercentages && (
                  <TableCell align="right"></TableCell>
                )}
              </>
            )}
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Render chart
  const renderChart = (result: any) => (
    <Paper 
      variant="outlined" 
      sx={{ 
        p: 2, 
        height: isMobile ? 300 : 400, 
        borderRadius: 2,
        backgroundColor: theme.palette.grey[50]
      }}
    >
      <ResponsiveContainer width="100%" height="100%">
        {chartType === 'bar' ? (
          <BarChart
            data={result.chartData}
            margin={{ 
              top: 20, 
              right: 30, 
              left: 20, 
              bottom: isMobile ? 60 : 80 
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
            <XAxis 
              dataKey="category" 
              angle={isMobile ? -90 : -45} 
              textAnchor="end"
              height={isMobile ? 60 : 80}
              interval={0}
              tick={{ fontSize: isMobile ? 10 : 12 }}
            />
            <YAxis tick={{ fontSize: isMobile ? 10 : 12 }} />
            <RechartsTooltip 
              contentStyle={{
                backgroundColor: theme.palette.background.paper,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 8
              }}
              formatter={(value, name) => [
                value, 
                name === 'frequency' ? 'Frequency' : name
              ]} 
              labelFormatter={(value) => `Category: ${value}`} 
            />
            <Legend />
            <Bar 
              dataKey="frequency" 
              fill={theme.palette.primary.main} 
              name="Frequency"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        ) : (
          <PieChart>
            <Pie
              data={result.chartData}
              cx="50%"
              cy="50%"
              outerRadius={isMobile ? 80 : 120}
              fill="#8884d8"
              dataKey="frequency"
              nameKey="category"
              label={({ name, percent }) => 
                percent > 0.05 ? `${isMobile ? '' : name + ': '}${(percent * 100).toFixed(1)}%` : ''
              }
              labelLine={false}
            >
              {result.chartData.map((entry: any, index: number) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={generateColors(result.chartData.length)[index]} 
                />
              ))}
            </Pie>
            <RechartsTooltip 
              contentStyle={{
                backgroundColor: theme.palette.background.paper,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 8
              }}
              formatter={(value, name, props) => [
                value, 
                `Frequency (${((value as number / result.total) * 100).toFixed(1)}%)`
              ]} 
            />
            <Legend />
          </PieChart>
        )}
      </ResponsiveContainer>
    </Paper>
  );
  
  return (
    <Box sx={{ p: { xs: 2, md: 3 }, maxWidth: 1400, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
          Frequency Analysis
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Analyze categorical data distributions with frequency tables and visualizations
        </Typography>
      </Box>

      {/* Configuration Panel */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardHeader 
          title="Configuration"
          avatar={<SettingsIcon color="primary" />}
          sx={{ pb: 1 }}
        />
        <CardContent>
          {/* Dataset and Column Selection */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Dataset</InputLabel>
                <Select
                  value={selectedDatasetId}
                  label="Dataset"
                  onChange={handleDatasetChange}
                  disabled={datasets.length === 0}
                >
                  {datasets.length === 0 ? (
                    <MenuItem value="" disabled>
                      No datasets available
                    </MenuItem>
                  ) : (
                    datasets.map(dataset => (
                      <MenuItem key={dataset.id} value={dataset.id}>
                        <Box>
                          <Typography variant="body2">{dataset.name}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {dataset.data.length} rows
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Categorical Variables</InputLabel>
                <Select
                  multiple
                  value={selectedColumns}
                  label="Categorical Variables"
                  onChange={handleColumnChange}
                  disabled={categoricalColumns.length === 0}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => {
                        const column = categoricalColumns.find(col => col.id === value);
                        return (
                          <Chip 
                            key={value} 
                            label={column?.name || value} 
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        );
                      })}
                    </Box>
                  )}
                >
                  {categoricalColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No categorical variables available
                    </MenuItem>
                  ) : (
                    categoricalColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        <Typography variant="body2">{column.name}</Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                          ({column.type})
                        </Typography>
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* View Mode Selection */}
          {Object.keys(frequencyResults).length > 0 && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
                Display Mode
              </Typography>
              <ToggleButtonGroup
                value={viewMode}
                exclusive
                onChange={handleViewModeChange}
                size="small"
                sx={{ mb: 2 }}
              >
                <ToggleButton value="tabs" aria-label="tabs view">
                  <ViewCarouselIcon sx={{ mr: 1 }} />
                  {!isMobile && 'Tabs'}
                </ToggleButton>
                <ToggleButton value="stacked" aria-label="stacked view">
                  <ViewListIcon sx={{ mr: 1 }} />
                  {!isMobile && 'Stacked'}
                </ToggleButton>
                {!isMobile && (
                  <ToggleButton value="side-by-side" aria-label="side by side view">
                    <ViewModuleIcon sx={{ mr: 1 }} />
                    Side by Side
                  </ToggleButton>
                )}
              </ToggleButtonGroup>
              <Typography variant="caption" color="text.secondary" display="block">
                Choose how to display the frequency table and chart
              </Typography>
            </Box>
          )}

          {/* Analysis Options */}
          <Accordion defaultExpanded={false}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                Analysis Options
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
                    Table Options
                  </Typography>
                  <Stack spacing={1}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includePercentages}
                          onChange={(e) => {
                            setIncludePercentages(e.target.checked);
                            clearAnalysis();
                          }}
                        />
                      }
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          Include Percentages
                          <Tooltip title="Show percentage distribution for each category">
                            <InfoIcon fontSize="small" color="action" />
                          </Tooltip>
                        </Box>
                      }
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeCumulativeFreq}
                          onChange={(e) => {
                            setIncludeCumulativeFreq(e.target.checked);
                            clearAnalysis();
                          }}
                        />
                      }
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          Cumulative Distributions
                          <Tooltip title="Show running totals and cumulative percentages">
                            <InfoIcon fontSize="small" color="action" />
                          </Tooltip>
                        </Box>
                      }
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={sortByFrequency}
                          onChange={(e) => {
                            setSortByFrequency(e.target.checked);
                            clearAnalysis();
                          }}
                        />
                      }
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          Sort by Frequency
                          <Tooltip title="Sort categories by frequency (descending) instead of alphabetically">
                            <InfoIcon fontSize="small" color="action" />
                          </Tooltip>
                        </Box>
                      }
                    />
                  </Stack>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl component="fieldset">
                    <FormLabel component="legend" sx={{ fontWeight: 500, mb: 1 }}>
                      Visualization Type
                    </FormLabel>
                    <RadioGroup
                      value={chartType}
                      onChange={(e) => {
                        setChartType(e.target.value as 'bar' | 'pie');
                        clearAnalysis();
                      }}
                    >
                      <FormControlLabel 
                        value="bar" 
                        control={<Radio />} 
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <BarChartIcon fontSize="small" />
                            Bar Chart
                          </Box>
                        }
                      />
                      <FormControlLabel 
                        value="pie" 
                        control={<Radio />} 
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <PieChartIcon fontSize="small" />
                            Pie Chart
                          </Box>
                        }
                      />
                    </RadioGroup>
                  </FormControl>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Action Buttons */}
          <Box sx={{ mt: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<AssessmentIcon />}
              onClick={runFrequencyAnalysis}
              disabled={loading || selectedColumns.length === 0}
              sx={{ 
                px: 4,
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 600
              }}
            >
              {loading ? 'Analyzing...' : 'Generate Analysis'}
            </Button>
            
            {Object.keys(frequencyResults).length > 0 && (
              <Button
                variant="outlined"
                size="large"
                onClick={clearAnalysis}
                sx={{ 
                  px: 3,
                  borderRadius: 2,
                  textTransform: 'none'
                }}
              >
                Clear Results
              </Button>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <Card elevation={2}>
          <CardContent>
            <Box display="flex" flexDirection="column" alignItems="center" py={4}>
              <CircularProgress size={60} sx={{ mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                Analyzing Data...
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Processing frequency distributions
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3, borderRadius: 2 }}
          action={
            <Button color="inherit" size="small" onClick={() => setError(null)}>
              Dismiss
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Results */}
      {Object.keys(frequencyResults).length > 0 && !loading && (
        <Stack spacing={3}>
          {Object.entries(frequencyResults).map(([columnId, result]) => (
            <Card elevation={2} key={columnId}>
              <CardHeader
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TableChartIcon color="primary" />
                    <Typography variant="h6">
                      {result.column.name}
                    </Typography>
                  </Box>
                }
                subheader={
                  <Typography variant="body2" color="text.secondary">
                    {result.sortedKeys.length} unique categories • {result.total} total observations
                  </Typography>
                }
              />
              <CardContent>
                {viewMode === 'tabs' ? (
                  <Box>
                    <Tabs 
                      value={activeTab} 
                      onChange={(e, newValue) => setActiveTab(newValue)}
                      sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
                    >
                      <Tab 
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <TableChartIcon fontSize="small" />
                            {!isMobile && 'Frequency Table'}
                          </Box>
                        } 
                      />
                      <Tab 
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {chartType === 'bar' ? <BarChartIcon fontSize="small" /> : <PieChartIcon fontSize="small" />}
                            {!isMobile && `${chartType === 'bar' ? 'Bar' : 'Pie'} Chart`}
                          </Box>
                        } 
                      />
                    </Tabs>
                    <TabPanel value={activeTab} index={0}>
                      {renderFrequencyTable(result)}
                    </TabPanel>
                    <TabPanel value={activeTab} index={1}>
                      {renderChart(result)}
                    </TabPanel>
                  </Box>
                ) : viewMode === 'stacked' ? (
                  <Stack spacing={4}>
                    <Box>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                        Frequency Distribution
                      </Typography>
                      {renderFrequencyTable(result)}
                    </Box>
                    <Box>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                        {chartType === 'bar' ? 'Bar Chart' : 'Pie Chart'}
                      </Typography>
                      {renderChart(result)}
                    </Box>
                  </Stack>
                ) : (
                  <Grid container spacing={4}>
                    <Grid item xs={12} lg={6}>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                        Frequency Distribution
                      </Typography>
                      {renderFrequencyTable(result)}
                    </Grid>
                    <Grid item xs={12} lg={6}>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                        {chartType === 'bar' ? 'Bar Chart' : 'Pie Chart'}
                      </Typography>
                      {renderChart(result)}
                    </Grid>
                  </Grid>
                )}
              </CardContent>
            </Card>
          ))}
        </Stack>
      )}

      {/* Empty State */}
      {!loading && Object.keys(frequencyResults).length === 0 && selectedColumns.length > 0 && (
        <Card elevation={1}>
          <CardContent>
            <Box textAlign="center" py={6}>
              <AssessmentIcon sx={{ fontSize: 80, color: theme.palette.grey[400], mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Ready to Analyze
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Click "Generate Analysis" to create frequency tables and visualizations
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default FrequencyTables;
