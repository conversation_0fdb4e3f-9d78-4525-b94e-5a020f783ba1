import React, { useEffect, useRef } from 'react';
import Plotly from 'plotly.js';
import { useTheme } from '@mui/material';

interface PlotlyBoxPlotProps {
  columnId: string;
  boxPlotData: {
    min: number;
    q1: number;
    median: number;
    q3: number;
    max: number;
    outliers: number[];
  };
  columnName: string;
  includeOutliers: boolean;
}

const PlotlyBoxPlot: React.FC<PlotlyBoxPlotProps> = ({
  columnId,
  boxPlotData,
  columnName,
  includeOutliers,
}) => {
  const theme = useTheme();
  const plotRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (plotRef.current) {
      const trace = {
        x: [boxPlotData.min, boxPlotData.q1, boxPlotData.median,
           boxPlotData.q3, boxPlotData.max, ...boxPlotData.outliers],
        type: 'box',
        name: columnName,
        boxpoints: includeOutliers ? 'outliers' : false,
        jitter: 0.3,
        pointpos: -1.8,
        marker: {
          color: theme.palette.primary.main
        },
        orientation: 'h',
        boxmean: false,
        line: {
          width: 2
        },
        fillcolor: 'rgba(30, 144, 255, 0.5)'
      };

      const layout: Partial<Plotly.Layout> = {
        margin: { t: 10, r: 50, b: 20, l: 50 },
        height: 180,
        width: plotRef.current.clientWidth,
        hovermode: 'closest',
        showlegend: false,
        xaxis: {
          zeroline: false,
          automargin: true,
          fixedrange: true
        },
            yaxis: {
              showticklabels: false,
              fixedrange: true
            },
            autosize: true,
            plot_bgcolor: 'transparent',
            boxmode: 'group',
            // Removed boxgap as it causes a TypeScript error
          };

          const config = {
            displayModeBar: false,
            responsive: true
          };

          // Center the plot in its container
          const plotContainer = plotRef.current;
          if (plotContainer) {
            const containerWidth = plotContainer.clientWidth;
            layout.width = containerWidth;

            const plotAreaWidth = Math.min(600, containerWidth * 0.9);

            const horizontalMargin = Math.max(50, (containerWidth - plotAreaWidth) / 2);
            layout.margin!.l = horizontalMargin;
            layout.margin!.r = horizontalMargin;
          }

          Plotly.newPlot(plotRef.current, [trace as Plotly.Data], layout, config); // Cast trace to Plotly.Data
        }

    return () => {
      if (plotRef.current) {
        Plotly.purge(plotRef.current);
      }
    };
  }, [boxPlotData, columnName, includeOutliers, theme]);

  return <div id={`boxplot-${columnId}`} ref={plotRef} style={{ width: '100%', height: '100%' }} />;
};

export default PlotlyBoxPlot;
