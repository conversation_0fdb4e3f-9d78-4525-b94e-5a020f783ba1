import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  useTheme
} from '@mui/material';
import {
  <PERSON><PERSON>hart as BarChartIcon,
  TableChart as TableChartIcon,
  GridOn as GridOnIcon,
  ShowChart as ShowChartIcon
} from '@mui/icons-material';
import DescriptiveAnalysis from './DescriptiveAnalysis';
import FrequencyTables from './FrequencyTables';
import CrossTabulation from './CrossTabulation';
import NormalityTest from './NormalityTest';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface DescriptiveStatsProps {
  initialTab?: string;
}

// Tab panel component
const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`descriptive-tabpanel-${index}`}
      aria-labelledby={`descriptive-tab-${index}`}
      {...other}
      style={{ width: '100%' }}
    >
      {value === index && (
        <Box sx={{ pt: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Helper function for a11y props
const a11yProps = (index: number) => {
  return {
    id: `descriptive-tab-${index}`,
    'aria-controls': `descriptive-tabpanel-${index}`,
  };
};

// Map tab names to indices
const tabNameToIndex: Record<string, number> = {
  'descriptives': 0,
  'frequencies': 1,
  'crosstabs': 2,
  'normality': 3
};

// Main component for the Descriptive Statistics section
const DescriptiveStats: React.FC<DescriptiveStatsProps> = ({ initialTab = '' }) => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  
  // Set initial tab based on URL or prop
  useEffect(() => {
    if (initialTab && tabNameToIndex[initialTab] !== undefined) {
      setTabValue(tabNameToIndex[initialTab]);
    }
  }, [initialTab]);

  // Handle tab change
  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper elevation={1} sx={{ width: '100%' }}>
        <Tabs
          value={tabValue}
          onChange={handleChange}
          aria-label="Descriptive Statistics Tabs"
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <Tab 
            icon={<ShowChartIcon />} 
            label="Descriptives" 
            {...a11yProps(0)} 
            sx={{ py: 2 }}
          />
          <Tab 
            icon={<BarChartIcon />} 
            label="Frequencies" 
            {...a11yProps(1)} 
            sx={{ py: 2 }}
          />
          <Tab 
            icon={<GridOnIcon />} 
            label="Cross Tabulation" 
            {...a11yProps(2)}
            sx={{ py: 2 }}
          />
          <Tab 
            icon={<TableChartIcon />} 
            label="Normality Test" 
            {...a11yProps(3)}
            sx={{ py: 2 }}
          />
        </Tabs>

        <Box sx={{ p: 0 }}>
          <TabPanel value={tabValue} index={0}>
            <DescriptiveAnalysis />
          </TabPanel>
          <TabPanel value={tabValue} index={1}>
            <FrequencyTables />
          </TabPanel>
          <TabPanel value={tabValue} index={2}>
            <CrossTabulation />
          </TabPanel>
          <TabPanel value={tabValue} index={3}>
            <NormalityTest />
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};

export default DescriptiveStats;
