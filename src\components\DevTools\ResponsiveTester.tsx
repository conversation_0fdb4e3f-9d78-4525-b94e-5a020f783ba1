import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>er,
  IconButton,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  TextField,
  Switch,
  FormControlLabel,
  Divider,
  Paper,
  Tooltip,
  useTheme,
  alpha,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip
} from '@mui/material';
import {
  SettingsOverscan as SettingsOverscanIcon,
  ChevronRight as ChevronRightIcon,
  ViewComfy as ViewComfyIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
  BugReport as BugReportIcon,
  DeviceUnknown as DeviceUnknownIcon,
  Phone as PhoneIcon,
  Tablet as TabletIcon,
  Laptop as LaptopIcon,
  DesktopWindows as DesktopWindowsIcon,
  SaveAlt as SaveAltIcon
} from '@mui/icons-material';
import { getDeviceInfo } from '../../utils/compatibilityChecker';

// Common device presets
interface DevicePreset {
  name: string;
  width: number;
  height: number;
  deviceType: 'mobile' | 'tablet' | 'laptop' | 'desktop';
}

const devicePresets: DevicePreset[] = [
  { name: 'iPhone SE', width: 375, height: 667, deviceType: 'mobile' },
  { name: 'iPhone 13/14', width: 390, height: 844, deviceType: 'mobile' },
  { name: 'iPhone 13/14 Pro Max', width: 428, height: 926, deviceType: 'mobile' },
  { name: 'Google Pixel 6', width: 393, height: 851, deviceType: 'mobile' },
  { name: 'Samsung Galaxy S21', width: 360, height: 800, deviceType: 'mobile' },
  { name: 'iPad', width: 768, height: 1024, deviceType: 'tablet' },
  { name: 'iPad Pro 11"', width: 834, height: 1194, deviceType: 'tablet' },
  { name: 'iPad Pro 12.9"', width: 1024, height: 1366, deviceType: 'tablet' },
  { name: 'Surface Pro', width: 912, height: 1368, deviceType: 'tablet' },
  { name: 'Laptop (720p)', width: 1280, height: 720, deviceType: 'laptop' },
  { name: 'Laptop (1080p)', width: 1920, height: 1080, deviceType: 'laptop' },
  { name: 'Desktop (4K)', width: 3840, height: 2160, deviceType: 'desktop' }
];

// Breakpoint definitions for reference
const breakpoints = [
  { name: 'xs', range: '0px - 599px', color: '#f44336' },
  { name: 'sm', range: '600px - 899px', color: '#ff9800' },
  { name: 'md', range: '900px - 1199px', color: '#2196f3' },
  { name: 'lg', range: '1200px - 1535px', color: '#4caf50' },
  { name: 'xl', range: '1536px+', color: '#9c27b0' }
];

// Tool config interface
interface ResponsiveTesterConfig {
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
  showRulers: boolean;
  showBreakpoints: boolean;
  showDeviceFrame: boolean;
  zoomLevel: number;
  devicePixelRatio: number;
}

// Issue report interface
interface IssueReport {
  description: string;
  deviceInfo: any;
  timestamp: string;
  screenshot?: string; // base64 encoded image
}

const ResponsiveTester: React.FC = () => {
  const theme = useTheme();
  const [open, setOpen] = useState(false);
  const [config, setConfig] = useState<ResponsiveTesterConfig>({
    width: 390,
    height: 844,
    orientation: 'portrait',
    showRulers: true,
    showBreakpoints: true,
    showDeviceFrame: true,
    zoomLevel: 1,
    devicePixelRatio: 1
  });
  const [selectedPreset, setSelectedPreset] = useState<string>('iPhone 13/14');
  const [issueDescription, setIssueDescription] = useState('');
  const [issues, setIssues] = useState<IssueReport[]>([]);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  
  // Initialize device info
  useEffect(() => {
    setDeviceInfo(getDeviceInfo());
    
    // Poll for size changes
    const handleResize = () => {
      setDeviceInfo((prev: any) => ({
        ...prev,
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight
      }));
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Toggle the drawer
  const toggleDrawer = () => {
    setOpen(!open);
  };
  
  // Update config when preset changes
  const handlePresetChange = (event: SelectChangeEvent<string>) => {
    const presetName = event.target.value as string;
    const preset = devicePresets.find(p => p.name === presetName);
    
    if (preset) {
      setSelectedPreset(presetName);
      setConfig(prev => ({
        ...prev,
        width: preset.width,
        height: preset.height,
        orientation: preset.width < preset.height ? 'portrait' : 'landscape'
      }));
    }
  };
  
  // Handle orientation toggle
  const handleOrientationChange = () => {
    setConfig(prev => ({
      ...prev,
      orientation: prev.orientation === 'portrait' ? 'landscape' : 'portrait',
      width: prev.height,
      height: prev.width
    }));
  };
  
  // Update a single config property
  const updateConfig = (key: keyof ResponsiveTesterConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  // Get the current breakpoint based on width
  const getCurrentBreakpoint = (width: number) => {
    if (width < 600) return 'xs';
    if (width < 900) return 'sm';
    if (width < 1200) return 'md';
    if (width < 1536) return 'lg';
    return 'xl';
  };
  
  // Get color for current breakpoint
  const getBreakpointColor = (width: number) => {
    const bp = getCurrentBreakpoint(width);
    return breakpoints.find(b => b.name === bp)?.color || theme.palette.primary.main;
  };
  
  // Add an issue report
  const addIssueReport = async () => {
    if (!issueDescription.trim()) return;
    
    try {
      // Try to capture a screenshot
      const newIssue: IssueReport = {
        description: issueDescription,
        deviceInfo: {
          ...deviceInfo,
          simulatedWidth: config.width,
          simulatedHeight: config.height,
          simulatedOrientation: config.orientation,
          simulatedDevice: selectedPreset,
          currentBreakpoint: getCurrentBreakpoint(config.width)
        },
        timestamp: new Date().toISOString()
      };
      
      setIssues(prev => [newIssue, ...prev]);
      setIssueDescription('');
    } catch (err) {
      console.error('Error creating issue report:', err);
    }
  };
  
  // Export issues to JSON
  const exportIssues = () => {
    const data = JSON.stringify(issues, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `responsive-issues-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };
  
  // Get device icon based on type
  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <PhoneIcon />;
      case 'tablet':
        return <TabletIcon />;
      case 'laptop':
        return <LaptopIcon />;
      case 'desktop':
        return <DesktopWindowsIcon />;
      default:
        return <DeviceUnknownIcon />;
    }
  };
  
  // Reset to current actual device size
  const resetToActualSize = () => {
    if (deviceInfo) {
      setConfig(prev => ({
        ...prev,
        width: deviceInfo.screenWidth,
        height: deviceInfo.screenHeight,
        orientation: deviceInfo.screenWidth < deviceInfo.screenHeight ? 'portrait' : 'landscape'
      }));
      setSelectedPreset('');
    }
  };
  
  return (
    <>
      {/* Trigger Button */}
      <Tooltip title="Responsive Design Tester" placement="left">
        <IconButton
          onClick={toggleDrawer}
          sx={{
            position: 'fixed',
            right: 16,
            bottom: 16,
            zIndex: 1300,
            backgroundColor: alpha(theme.palette.primary.main, 0.9),
            color: 'white',
            '&:hover': {
              backgroundColor: theme.palette.primary.main
            },
            boxShadow: theme.shadows[3]
          }}
        >
          <ViewComfyIcon />
        </IconButton>
      </Tooltip>
      
      {/* Drawer */}
      <Drawer
        anchor="right"
        open={open}
        onClose={toggleDrawer}
        sx={{
          '& .MuiDrawer-paper': {
            width: { xs: '90%', sm: 400 },
            boxSizing: 'border-box',
            overflow: 'auto'
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" display="flex" alignItems="center">
              <SettingsOverscanIcon sx={{ mr: 1 }} />
              Responsive Tester
            </Typography>
            <IconButton onClick={toggleDrawer}>
              <CloseIcon />
            </IconButton>
          </Box>
          
          <Divider sx={{ mb: 2 }} />
          
          {/* Device Info */}
          <Paper sx={{ p: 2, mb: 2, bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
            <Typography variant="subtitle2" gutterBottom>
              Current Device
            </Typography>
            <Typography variant="body2">
              <strong>Browser:</strong> {deviceInfo?.browser} {deviceInfo?.browserVersion}
            </Typography>
            <Typography variant="body2">
              <strong>OS:</strong> {deviceInfo?.os}
            </Typography>
            <Typography variant="body2">
              <strong>Screen:</strong> {deviceInfo?.screenWidth}×{deviceInfo?.screenHeight} (x{deviceInfo?.pixelRatio})
            </Typography>
            <Typography variant="body2">
              <strong>Breakpoint:</strong> {getCurrentBreakpoint(deviceInfo?.screenWidth || 0)}
            </Typography>
            
            <Button 
              size="small" 
              startIcon={<RefreshIcon />} 
              onClick={resetToActualSize}
              sx={{ mt: 1 }}
            >
              Reset to Actual Size
            </Button>
          </Paper>
          
          {/* Device Presets */}
          <FormControl fullWidth margin="normal">
            <InputLabel id="device-preset-label">Device Preset</InputLabel>
            <Select
              labelId="device-preset-label"
              value={selectedPreset}
              onChange={handlePresetChange}
              label="Device Preset"
              fullWidth
            >
              <MenuItem value="">
                <em>Custom Size</em>
              </MenuItem>
              {devicePresets.map((preset) => (
                <MenuItem key={preset.name} value={preset.name}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {getDeviceIcon(preset.deviceType)}
                    <Typography sx={{ ml: 1 }}>{preset.name}</Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                      ({preset.width}×{preset.height})
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          {/* Custom Size */}
          <Box sx={{ display: 'flex', mb: 2, mt: 2, gap: 2 }}>
            <TextField
              label="Width"
              type="number"
              value={config.width}
              onChange={(e) => updateConfig('width', parseInt(e.target.value) || 320)}
              InputProps={{ inputProps: { min: 320, max: 3840 } }}
              fullWidth
            />
            <IconButton onClick={handleOrientationChange} sx={{ mx: 1 }}>
              <ChevronRightIcon 
                sx={{ 
                  transform: config.orientation === 'landscape' ? 'rotate(90deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s'
                }} 
              />
            </IconButton>
            <TextField
              label="Height"
              type="number"
              value={config.height}
              onChange={(e) => updateConfig('height', parseInt(e.target.value) || 320)}
              InputProps={{ inputProps: { min: 320, max: 3840 } }}
              fullWidth
            />
          </Box>
          
          {/* Breakpoint Reference */}
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Material UI Breakpoints
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Range</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {breakpoints.map((bp) => (
                    <TableRow key={bp.name}>
                      <TableCell>
                        <Chip 
                          label={bp.name} 
                          size="small"
                          sx={{ 
                            bgcolor: getCurrentBreakpoint(config.width) === bp.name ? 
                              bp.color : alpha(bp.color, 0.1),
                            color: getCurrentBreakpoint(config.width) === bp.name ? 
                              'white' : bp.color,
                            fontWeight: getCurrentBreakpoint(config.width) === bp.name ? 
                              'bold' : 'normal',
                          }}
                        />
                      </TableCell>
                      <TableCell>{bp.range}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
          
          {/* Display Options */}
          <Typography variant="subtitle2" gutterBottom>
            Display Options
          </Typography>
          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={
                <Switch 
                  checked={config.showRulers} 
                  onChange={(e) => updateConfig('showRulers', e.target.checked)} 
                />
              }
              label="Show Rulers"
            />
            <FormControlLabel
              control={
                <Switch 
                  checked={config.showBreakpoints} 
                  onChange={(e) => updateConfig('showBreakpoints', e.target.checked)} 
                />
              }
              label="Show Breakpoint Indicator"
            />
            <FormControlLabel
              control={
                <Switch 
                  checked={config.showDeviceFrame} 
                  onChange={(e) => updateConfig('showDeviceFrame', e.target.checked)} 
                />
              }
              label="Show Device Frame"
            />
          </Box>
          
          <Divider sx={{ my: 2 }} />
          
          {/* Issue Reporting */}
          <Typography variant="subtitle2" gutterBottom>
            Report Responsive Issue
          </Typography>
          <TextField
            label="Issue Description"
            multiline
            rows={4}
            fullWidth
            value={issueDescription}
            onChange={(e) => setIssueDescription(e.target.value)}
            variant="outlined"
            placeholder="Describe the responsive design issue..."
            sx={{ mb: 2 }}
          />
          <Button 
            variant="contained" 
            fullWidth
            startIcon={<BugReportIcon />}
            onClick={addIssueReport}
            disabled={!issueDescription.trim()}
          >
            Report Issue
          </Button>
          
          {/* Reported Issues */}
          {issues.length > 0 && (
            <>
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="subtitle2">
                  Reported Issues ({issues.length})
                </Typography>
                <Button 
                  size="small" 
                  startIcon={<SaveAltIcon />}
                  onClick={exportIssues}
                >
                  Export
                </Button>
              </Box>
              <Box sx={{ mt: 1, maxHeight: 200, overflow: 'auto' }}>
                {issues.map((issue, index) => (
                  <Paper key={index} sx={{ p: 1.5, mb: 1, bgcolor: alpha(theme.palette.error.main, 0.05) }}>
                    <Typography variant="body2" fontWeight="medium">
                      {issue.description}
                    </Typography>
                    <Typography variant="caption" display="block" color="text.secondary">
                      {new Date(issue.timestamp).toLocaleString()} • 
                      {issue.deviceInfo.simulatedDevice || 'Custom'} • 
                      {issue.deviceInfo.simulatedWidth}×{issue.deviceInfo.simulatedHeight}
                    </Typography>
                  </Paper>
                ))}
              </Box>
            </>
          )}
          
        </Box>
      </Drawer>
      
      {/* Display Metrics on Page */}
      {config.showBreakpoints && (
        <Box
          sx={{
            position: 'fixed',
            bottom: 16,
            left: 16,
            zIndex: 1200,
            backgroundColor: getBreakpointColor(config.width),
            color: '#fff',
            px: 1.5,
            py: 0.5,
            borderRadius: 2,
            fontSize: '0.75rem',
            fontWeight: 'bold',
            boxShadow: theme.shadows[2],
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <Typography variant="caption" fontWeight="bold" sx={{ mr: 1 }}>
            {getCurrentBreakpoint(deviceInfo?.screenWidth || 0).toUpperCase()}
          </Typography>
          <Typography variant="caption">
            {deviceInfo?.screenWidth}×{deviceInfo?.screenHeight}
          </Typography>
        </Box>
      )}
    </>
  );
};

export default ResponsiveTester;