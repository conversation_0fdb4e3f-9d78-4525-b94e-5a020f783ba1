import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON> as BarChartIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  CompareArrows as CompareArrowsIcon,
  CalculateOutlined as CalculateIcon, // New import
} from '@mui/icons-material';
import { TabPanel } from '../UI';
import CrossSectionalCalculator from './CrossSectionalCalculator';
import CaseControlCalculator from './CaseControlCalculator';
import CohortCalculator from './CohortCalculator';
import MatchedCaseControlCalculator from './MatchedCaseControlCalculator';
import SampleSizePowerCalculator from './SampleSizePowerCalculator'; // New import

interface EpiCalcProps {
  initialTab?: string;
}

const EpiCalc: React.FC<EpiCalcProps> = ({ initialTab }) => {
  const tabNameToIndex: { [key: string]: number } = {
    'cross_sectional': 0,
    'case_control': 1,
    'cohort': 2,
    'matched_case_control': 3,
    'sample_size_power': 4,
    'main': 0
  };

  const [activeTab, setActiveTab] = useState<number>(initialTab ? tabNameToIndex[initialTab] : 0);
  
  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  return (
    <Box sx={{ p: 3 }}>
      {/* Tabs Navigation */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange} 
          aria-label="epidemiological calculator tabs"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          textColor="primary"
          indicatorColor="primary"
        >
          <Tab 
            label="Cross-Sectional" 
            icon={<BarChartIcon />} 
            iconPosition="start"
          />
          <Tab 
            label="Case-Control" 
            icon={<CompareArrowsIcon />} 
            iconPosition="start"
          />
          <Tab 
            label="Cohort" 
            icon={<TimelineIcon />} 
            iconPosition="start"
          />
          <Tab 
            label="Matched Case-Control" 
            icon={<TrendingUpIcon />} 
            iconPosition="start"
          />
          <Tab // New Tab for Sample Size & Power
            label="Sample Size & Power" 
            icon={<CalculateIcon />} 
            iconPosition="start"
          />
        </Tabs>
      </Box>

      {/* Cross-Sectional Tab */}
      <TabPanel value={activeTab} index={0}>
        <CrossSectionalCalculator />
      </TabPanel>

      {/* Case-Control Tab */}
      <TabPanel value={activeTab} index={1}>
        <CaseControlCalculator />
      </TabPanel>

      {/* Cohort Tab */}
      <TabPanel value={activeTab} index={2}>
        <CohortCalculator />
      </TabPanel>

      {/* Matched Case-Control Tab */}
      <TabPanel value={activeTab} index={3}>
        <MatchedCaseControlCalculator />
      </TabPanel>

      {/* Sample Size & Power Tab */}
      <TabPanel value={activeTab} index={4}>
        <SampleSizePowerCalculator />
      </TabPanel>
    </Box>
  );
};

export default EpiCalc;
