import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  Card<PERSON>ontent,
  Typo<PERSON>,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import {
  Calculate as CalculatorIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
  Group as CohortIcon,
  Assignment as CaseControlIcon,
  ScatterPlot as CrossSectionalIcon,
  PeopleAlt as MatchedCaseControlIcon,
  PowerSettingsNew as PowerIcon,
} from '@mui/icons-material';

interface EpiCalcOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Study Design' | 'Sample Size';
  color: string;
}

interface EpiCalcOptionsProps {
  onNavigate: (path: string) => void;
}

export const epiCalcOptions: EpiCalcOption[] = [
  {
    name: 'Case-Control Calculator',
    shortDescription: 'Analyze data from case-control studies',
    detailedDescription: 'Calculate odds ratios, confidence intervals, and other measures of association for case-control study data.',
    path: 'epicalc/case-control',
    icon: <CaseControlIcon />,
    category: 'Study Design',
    color: '#FF9800', // Orange
  },
  {
    name: 'Cohort Calculator',
    shortDescription: 'Analyze data from cohort studies',
    detailedDescription: 'Calculate risk ratios, rate ratios, confidence intervals, and other measures of association for cohort study data.',
    path: 'epicalc/cohort',
    icon: <CohortIcon />,
    category: 'Study Design',
    color: '#4CAF50', // Green
  },
  {
    name: 'Cross-Sectional Calculator',
    shortDescription: 'Analyze data from cross-sectional studies',
    detailedDescription: 'Calculate prevalence ratios, odds ratios, confidence intervals, and other measures of association for cross-sectional study data.',
    path: 'epicalc/cross-sectional',
    icon: <CrossSectionalIcon />,
    category: 'Study Design',
    color: '#2196F3', // Blue
  },
  {
    name: 'Matched Case-Control Calculator',
    shortDescription: 'Analyze data from matched case-control studies',
    detailedDescription: 'Calculate matched odds ratios and confidence intervals for data from matched case-control studies.',
    path: 'epicalc/matched-case-control',
    icon: <MatchedCaseControlIcon />,
    category: 'Study Design',
    color: '#9C27B0', // Purple
  },
  {
    name: 'Sample Size / Power Calculator',
    shortDescription: 'Calculate sample size or power for various study designs',
    detailedDescription: 'Determine the required sample size for a study or the power of a study given a specific sample size, for different epidemiological study designs.',
    path: 'epicalc/sample-size-power',
    icon: <PowerIcon />,
    category: 'Sample Size',
    color: '#F44336', // Red
  },
];

const EpiCalcOptions: React.FC<EpiCalcOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const categories = ['All', 'Study Design', 'Sample Size'];

  const filteredOptions = selectedCategory === 'All'
    ? epiCalcOptions
    : epiCalcOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Study Design': return <CaseControlIcon />; // Using one icon for the category chip
      case 'Sample Size': return <PowerIcon />;
      default: return <CalculatorIcon />;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Epidemiological Calculators
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Tools for analyzing data from various epidemiological study designs
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Access a collection of calculators to assist with the analysis of case-control, cohort, cross-sectional, and matched case-control studies, as well as sample size and power calculations.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography variant="h6" fontWeight="bold">
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {option.shortDescription}
                </Typography>

                <Typography variant="body2" paragraph>
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Case-Control:</strong> For studies comparing exposure in people with and without a disease.
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Cohort:</strong> For studies following groups with and without an exposure to see who develops a disease.
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Cross-Sectional:</strong> For studies assessing exposure and disease status at a single point in time.
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Matched Case-Control:</strong> For case-control studies where cases are matched to controls on specific characteristics.
            </Typography>
             <Typography variant="body2" color="text.secondary">
              • <strong>Sample Size / Power:</strong> To determine study size or evaluate the likelihood of detecting an effect.
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default EpiCalcOptions;
