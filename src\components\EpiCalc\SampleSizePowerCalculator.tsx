import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Divider,
  Tooltip,
  IconButton,
} from '@mui/material';
import {
  CalculateOutlined as <PERSON>culateIcon,
  Refresh as RefreshIcon,
  InfoOutlined as InfoIcon,
} from '@mui/icons-material';
import { StatsCard } from '../UI';

// Helper functions for statistical calculations (simplified for initial implementation)
class EpiSampleSizeUtils {
  // Z-score for a given alpha level (two-tailed)
  static zAlpha(alpha: number) {
    // Common Z-scores: 1.96 for 0.05, 2.576 for 0.01
    if (alpha === 0.05) return 1.96;
    if (alpha === 0.01) return 2.576;
    if (alpha === 0.10) return 1.645;
    
    // A more robust implementation using approximation
    // This is an approximation of the inverse normal CDF for two-tailed alpha
    const p = 1 - alpha/2;
    if (p <= 0 || p >= 1) return 1.96; // Default to 0.05 if invalid
    
    // Approximation of inverse normal CDF
    const a1 = -39.6968302866538;
    const a2 = 220.946098424521;
    const a3 = -275.928510446969;
    const a4 = 138.357751867269;
    const a5 = -30.6647980661472;
    const a6 = 2.50662827745924;
    
    const b1 = -54.*************;
    const b2 = 161.585836858041;
    const b3 = -155.698979859887;
    const b4 = 66.8013118877197;
    const b5 = -13.2806815528857;
    
    const c1 = -7.78489400243029E-03;
    const c2 = -0.322396458041136;
    const c3 = -2.40075827716184;
    const c4 = -2.54973253934373;
    const c5 = 4.37466414146497;
    const c6 = 2.93816398269878;
    
    const d1 = 7.78469570904146E-03;
    const d2 = 0.32246712907004;
    const d3 = 2.445134137143;
    const d4 = 3.75440866190742;
    
    let z_value = 0;
    if (p < 0.02425) {
      // Left tail
      const q = Math.sqrt(-2 * Math.log(p));
      z_value = (((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) /
                ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
    } else if (p <= 0.97575) {
      // Central region
      const q = p - 0.5;
      const r = q * q;
      z_value = (((((a1 * r + a2) * r + a3) * r + a4) * r + a5) * r + a6) * q /
                (((((b1 * r + b2) * r + b3) * r + b4) * r + b5) * r + 1);
    } else {
      // Right tail
      const q = Math.sqrt(-2 * Math.log(1 - p));
      z_value = -(((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) /
                 ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
    }
    
    return Math.abs(z_value);
  }

  // Z-score for a given power (one-tailed)
  static zBeta(power: number) {
    // Common Z-scores: 0.84 for 80% power, 1.28 for 90% power
    if (power === 0.80) return 0.84;
    if (power === 0.90) return 1.28;
    if (power === 0.95) return 1.645;
    if (power === 0.75) return 0.67;
    if (power === 0.85) return 1.04;
    
    // A more robust implementation using approximation
    if (power <= 0 || power >= 1) return 0.84; // Default to 80% if invalid
    
    // Approximation of inverse normal CDF for one-tailed power
    const a1 = -39.6968302866538;
    const a2 = 220.946098424521;
    const a3 = -275.928510446969;
    const a4 = 138.357751867269;
    const a5 = -30.6647980661472;
    const a6 = 2.50662827745924;
    
    const b1 = -54.*************;
    const b2 = 161.585836858041;
    const b3 = -155.698979859887;
    const b4 = 66.8013118877197;
    const b5 = -13.2806815528857;
    
    const c1 = -7.78489400243029E-03;
    const c2 = -0.322396458041136;
    const c3 = -2.40075827716184;
    const c4 = -2.54973253934373;
    const c5 = 4.37466414146497;
    const c6 = 2.93816398269878;
    
    const d1 = 7.78469570904146E-03;
    const d2 = 0.32246712907004;
    const d3 = 2.445134137143;
    const d4 = 3.75440866190742;
    
    let z_value = 0;
    if (power < 0.02425) {
      // Left tail
      const q = Math.sqrt(-2 * Math.log(power));
      z_value = (((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) /
                ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
    } else if (power <= 0.97575) {
      // Central region
      const q = power - 0.5;
      const r = q * q;
      z_value = (((((a1 * r + a2) * r + a3) * r + a4) * r + a5) * r + a6) * q /
                (((((b1 * r + b2) * r + b3) * r + b4) * r + b5) * r + 1);
    } else {
      // Right tail
      const q = Math.sqrt(-2 * Math.log(1 - power));
      z_value = -(((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) /
                 ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
    }
    
    return z_value;
  }

  /**
   * Calculates sample size for comparing two proportions (cohort/cross-sectional)
   * Formula: N = [Z_alpha * sqrt(2P(1-P)) + Z_beta * sqrt(P1(1-P1) + P2(1-P2))]^2 / (P1 - P2)^2
   * where P = (P1 + P2) / 2
   */
  static sampleSizeTwoProportions(
    p1: number, // Proportion in group 1
    p2: number, // Proportion in group 2
    alpha: number,
    power: number,
    ratio: number = 1 // Ratio of group 2 to group 1 (n2/n1)
  ) {
    if (p1 === p2) return undefined; // Cannot calculate if proportions are equal
    if (alpha <= 0 || alpha >= 1 || power <= 0 || power >= 1) return undefined;

    const zAlpha = this.zAlpha(alpha);
    const zBeta = this.zBeta(power);

    const pAvg = (p1 + p2) / 2;
    const qAvg = 1 - pAvg;

    const term1 = zAlpha * Math.sqrt(pAvg * qAvg * (1 + 1/ratio));
    const term2 = zBeta * Math.sqrt(p1 * (1 - p1) + (p2 * (1 - p2)) / ratio);
    
    const numerator = Math.pow(term1 + term2, 2);
    const denominator = Math.pow(p1 - p2, 2);

    const n1 = numerator / denominator;
    const n2 = n1 * ratio;

    return { n1: Math.ceil(n1), n2: Math.ceil(n2), total: Math.ceil(n1 + n2) };
  }

  /**
   * Calculates sample size for case-control studies (based on Odds Ratio)
   * Formula: N = [Z_alpha * sqrt(P0(1-P0)(1+1/R)) + Z_beta * sqrt(P1(1-P1) + P0(1-P0)/R)]^2 / (P1 - P0)^2
   * where P0 = proportion of exposed among controls, P1 = proportion of exposed among cases
   * P1 = (OR * P0) / (1 + P0 * (OR - 1))
   */
  static sampleSizeCaseControl(
    p0: number, // Proportion of exposed among controls
    oddsRatio: number,
    alpha: number,
    power: number,
    ratio: number = 1 // Ratio of controls to cases (n_controls/n_cases)
  ) {
    if (p0 <= 0 || p0 >= 1 || oddsRatio <= 0) return undefined;
    if (alpha <= 0 || alpha >= 1 || power <= 0 || power >= 1) return undefined;

    const zAlpha = this.zAlpha(alpha);
    const zBeta = this.zBeta(power);

    // Proportion of exposed among cases
    const p1 = (oddsRatio * p0) / (1 + p0 * (oddsRatio - 1));
    if (p1 <= 0 || p1 >= 1) return undefined; // Invalid p1

    const term1 = zAlpha * Math.sqrt(p0 * (1 - p0) * (1 + 1/ratio));
    const term2 = zBeta * Math.sqrt(p1 * (1 - p1) + (p0 * (1 - p0)) / ratio);

    const numerator = Math.pow(term1 + term2, 2);
    const denominator = Math.pow(p1 - p0, 2);

    const nCases = numerator / denominator;
    const nControls = nCases * ratio;

    return { nCases: Math.ceil(nCases), nControls: Math.ceil(nControls), total: Math.ceil(nCases + nControls) };
  }

  /**
   * Calculates power for comparing two proportions
   */
  static calculatePowerTwoProportions(
    n1: number,
    n2: number,
    p1: number,
    p2: number,
    alpha: number
  ) {
    if (n1 <= 0 || n2 <= 0 || p1 === p2) return undefined;
    if (alpha <= 0 || alpha >= 1) return undefined;

    const zAlpha = this.zAlpha(alpha);
    const pAvg = (p1 * n1 + p2 * n2) / (n1 + n2);
    const qAvg = 1 - pAvg;

    const se = Math.sqrt(p1 * (1 - p1) / n1 + p2 * (1 - p2) / n2);
    const z = (Math.abs(p1 - p2) - zAlpha * Math.sqrt(pAvg * qAvg * (1/n1 + 1/n2))) / se;
    
    return this.normalCDF(z); // Corrected power calculation
  }

  /**
   * Calculates power for case-control studies
   */
  static calculatePowerCaseControl(
    nCases: number,
    nControls: number,
    p0: number, // Proportion of exposed among controls
    oddsRatio: number,
    alpha: number
  ) {
    if (nCases <= 0 || nControls <= 0 || p0 <= 0 || p0 >= 1 || oddsRatio <= 0) return undefined;
    if (alpha <= 0 || alpha >= 1) return undefined;

    const zAlpha = this.zAlpha(alpha);

    // Proportion of exposed among cases
    const p1 = (oddsRatio * p0) / (1 + p0 * (oddsRatio - 1));
    if (p1 <= 0 || p1 >= 1) return undefined;

    const nTotal = nCases + nControls;
    const pAvg = (nCases * p1 + nControls * p0) / nTotal;
    const qAvg = 1 - pAvg;

    const se0 = Math.sqrt(pAvg * qAvg * (1/nCases + 1/nControls)); // Standard error under null
    const se1 = Math.sqrt(p1 * (1 - p1) / nCases + p0 * (1 - p0) / nControls); // Standard error under alternative

    // Z-score for power calculation
    const zBeta = (Math.abs(p1 - p0) - zAlpha * se0) / se1;

    return this.normalCDF(zBeta); // Power is P(Z < zBeta)
  }

  // Normal CDF (copied from MatchedCaseControlUtils for self-containment)
  static normalCDF(x: number) {
    const t = 1 / (1 + 0.2316419 * Math.abs(x));
    const d = 0.3989423 * Math.exp(-x * x / 2);
    const p = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
    return x > 0 ? 1 - p : p;
  }
}

const SampleSizePowerCalculator: React.FC = () => {
  const [studyType, setStudyType] = useState('twoProportions'); // 'twoProportions', 'caseControl'
  const [solveFor, setSolveFor] = useState('sampleSize'); // 'sampleSize', 'power'
  const [alpha, setAlpha] = useState(0.05);
  
  // Inputs for Two Proportions
  const [p1, setP1] = useState(0.1); // Proportion in group 1
  const [p2, setP2] = useState(0.2); // Proportion in group 2
  const [n1, setN1] = useState(50); // Sample size for group 1
  const [n2, setN2] = useState(50); // Sample size for group 2
  const [ratioTwoProportions, setRatioTwoProportions] = useState(1.0); // Dedicated ratio variable

  // Inputs for Case-Control
  const [p0, setP0] = useState(0.1); // Proportion of exposed among controls
  const [oddsRatio, setOddsRatio] = useState(2.0);
  const [nCases, setNCases] = useState(50); // Number of cases
  const [nControls, setNControls] = useState(50); // Number of controls
  const [ratioCaseControl, setRatioCaseControl] = useState(1.0); // Dedicated ratio variable

  // Input for Power (when solving for sample size)
  const [powerInput, setPowerInput] = useState(0.80);

  // Input for Sample Size (when solving for power) - not directly used as input fields for N are now always present
  // const [sampleSizeInput, setSampleSizeInput] = useState(100); 

  const [result, setResult] = useState<{
    type: 'sampleSize' | 'power';
    value?: number | { n1?: number; n2?: number; total?: number; nCases?: number; nControls?: number; };
    params: {
      alpha?: number;
      p1?: number;
      p2?: number;
      p0?: number;
      oddsRatio?: number;
      n1?: number;
      n2?: number;
      nCases?: number;
      nControls?: number;
      power?: number;
      ratio?: number;
    };
  } | undefined>(undefined);

  const handleCalculate = () => {
    let calculatedValue: any;
    let params: any = { alpha: alpha };

    if (solveFor === 'sampleSize') {
      params.power = powerInput;
      if (studyType === 'twoProportions') {
        params.p1 = p1;
        params.p2 = p2;
        params.ratio = ratioTwoProportions; // Use dedicated ratio variable
        calculatedValue = EpiSampleSizeUtils.sampleSizeTwoProportions(p1, p2, alpha, powerInput, ratioTwoProportions);
      } else if (studyType === 'caseControl') {
        params.p0 = p0;
        params.oddsRatio = oddsRatio;
        params.ratio = ratioCaseControl; // Use dedicated ratio variable
        calculatedValue = EpiSampleSizeUtils.sampleSizeCaseControl(p0, oddsRatio, alpha, powerInput, ratioCaseControl);
      }
    } else { // solveFor === 'power'
      if (studyType === 'twoProportions') {
        params.n1 = n1;
        params.n2 = n2;
        params.p1 = p1;
        params.p2 = p2;
        calculatedValue = EpiSampleSizeUtils.calculatePowerTwoProportions(n1, n2, p1, p2, alpha);
      } else if (studyType === 'caseControl') {
        params.nCases = nCases;
        params.nControls = nControls;
        params.p0 = p0;
        params.oddsRatio = oddsRatio;
        calculatedValue = EpiSampleSizeUtils.calculatePowerCaseControl(nCases, nControls, p0, oddsRatio, alpha);
      }
    }
    setResult({ type: solveFor as "power" | "sampleSize", value: calculatedValue, params: params });
  };

  const resetForm = () => {
    setStudyType('twoProportions');
    setSolveFor('sampleSize');
    setAlpha(0.05);
    setP1(0.1);
    setP2(0.2);
    setN1(50);
    setN2(50);
    setP0(0.1);
    setOddsRatio(2.0);
    setNCases(50);
    setNControls(50);
    setPowerInput(0.80);
    setRatioTwoProportions(1.0);
    setRatioCaseControl(1.0);
    setResult(undefined);
  };

  const formatNumber = (num: number | undefined, decimals: number = 0) => {
    if (num === undefined || isNaN(num)) return 'N/A';
    return num.toFixed(decimals);
  };

  const formatPercent = (num: number | undefined, decimals: number = 1) => {
    if (num === undefined || isNaN(num)) return 'N/A';
    return `${(num * 100).toFixed(decimals)}%`;
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>Sample Size & Power Calculator for Epi Studies</Typography>
      <Typography variant="body1" paragraph>
        Determine the required sample size for your epidemiological study or calculate the power of an existing study.
      </Typography>

      <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="study-type-label">Study Type</InputLabel>
              <Select
                labelId="study-type-label"
                value={studyType}
                label="Study Type"
                onChange={(e) => setStudyType(e.target.value as string)}
              >
                <MenuItem value="twoProportions">Comparing Two Proportions (Cohort/Cross-sectional)</MenuItem>
                <MenuItem value="caseControl">Case-Control Study (Odds Ratio)</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth margin="normal">
              <InputLabel id="solve-for-label">Solve For</InputLabel>
              <Select
                labelId="solve-for-label"
                value={solveFor}
                label="Solve For"
                onChange={(e) => setSolveFor(e.target.value as string)}
              >
                <MenuItem value="sampleSize">Sample Size</MenuItem>
                <MenuItem value="power">Power</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="Alpha (α) - Significance Level"
              type="number"
              value={alpha}
              onChange={(e) => setAlpha(parseFloat(e.target.value))}
              inputProps={{ step: 0.01, min: 0.01, max: 0.99 }}
              fullWidth
              margin="normal"
              helperText="e.g., 0.05 for 95% confidence"
            />
            {solveFor === 'sampleSize' && (
              <TextField
                label="Desired Power (1-β)"
                type="number"
                value={powerInput}
                onChange={(e) => setPowerInput(parseFloat(e.target.value))}
                inputProps={{ step: 0.01, min: 0.01, max: 0.99 }}
                fullWidth
                margin="normal"
                helperText="e.g., 0.80 for 80% power"
              />
            )}
          </Grid>

          <Grid item xs={12} md={6}>
            {studyType === 'twoProportions' && (
              <Box>
                <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>Parameters for Comparing Two Proportions</Typography>
                <TextField
                  label="Proportion in Group 1 (P1)"
                  type="number"
                  value={p1}
                  onChange={(e) => setP1(parseFloat(e.target.value))}
                  inputProps={{ step: 0.01, min: 0, max: 1 }}
                  fullWidth
                  margin="normal"
                  helperText="Expected proportion in the first group (e.g., exposed)"
                />
                <TextField
                  label="Proportion in Group 2 (P2)"
                  type="number"
                  value={p2}
                  onChange={(e) => setP2(parseFloat(e.target.value))}
                  inputProps={{ step: 0.01, min: 0, max: 1 }}
                  fullWidth
                  margin="normal"
                  helperText="Expected proportion in the second group (e.g., unexposed)"
                />
                {solveFor === 'power' && (
                  <>
                    <TextField
                      label="Sample Size Group 1 (n1)"
                      type="number"
                      value={n1}
                      onChange={(e) => setN1(parseInt(e.target.value, 10))}
                      inputProps={{ min: 1 }}
                      fullWidth
                      margin="normal"
                      helperText="Actual sample size in the first group"
                    />
                    <TextField
                      label="Sample Size Group 2 (n2)"
                      type="number"
                      value={n2}
                      onChange={(e) => setN2(parseInt(e.target.value, 10))}
                      inputProps={{ min: 1 }}
                      fullWidth
                      margin="normal"
                      helperText="Actual sample size in the second group"
                    />
                  </>
                )}
                {solveFor === 'sampleSize' && (
                  <TextField
                    label="Ratio of Group 2 to Group 1 (n2/n1)"
                    type="number"
                    value={ratioTwoProportions} // Use dedicated ratio variable
                    onChange={(e) => {
                      const newRatio = parseFloat(e.target.value);
                      if (!isNaN(newRatio) && newRatio > 0) {
                        setRatioTwoProportions(newRatio);
                      }
                    }}
                    inputProps={{ step: 0.1, min: 0.1 }}
                    fullWidth
                    margin="normal"
                    helperText="e.g., 1 for equal groups, 2 for 2 controls per case"
                  />
                )}
              </Box>
            )}

            {studyType === 'caseControl' && (
              <Box>
                <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>Parameters for Case-Control Study</Typography>
                <TextField
                  label="Proportion Exposed Among Controls (P0)"
                  type="number"
                  value={p0}
                  onChange={(e) => setP0(parseFloat(e.target.value))}
                  inputProps={{ step: 0.01, min: 0, max: 1 }}
                  fullWidth
                  margin="normal"
                  helperText="Expected proportion of exposure in the control group"
                />
                <TextField
                  label="Odds Ratio (OR)"
                  type="number"
                  value={oddsRatio}
                  onChange={(e) => setOddsRatio(parseFloat(e.target.value))}
                  inputProps={{ step: 0.1, min: 0.1 }}
                  fullWidth
                  margin="normal"
                  helperText="Expected Odds Ratio to detect"
                />
                {solveFor === 'power' && (
                  <>
                    <TextField
                      label="Number of Cases (nCases)"
                      type="number"
                      value={nCases}
                      onChange={(e) => setNCases(parseInt(e.target.value, 10))}
                      inputProps={{ min: 1 }}
                      fullWidth
                      margin="normal"
                      helperText="Actual number of cases"
                    />
                    <TextField
                      label="Number of Controls (nControls)"
                      type="number"
                      value={nControls}
                      onChange={(e) => setNControls(parseInt(e.target.value, 10))}
                      inputProps={{ min: 1 }}
                      fullWidth
                      margin="normal"
                      helperText="Actual number of controls"
                    />
                  </>
                )}
                {solveFor === 'sampleSize' && (
                  <TextField
                    label="Ratio of Controls to Cases"
                    type="number"
                    value={ratioCaseControl} // Use dedicated ratio variable
                    onChange={(e) => {
                      const newRatio = parseFloat(e.target.value);
                      if (!isNaN(newRatio) && newRatio > 0) {
                        setRatioCaseControl(newRatio);
                      }
                    }}
                    inputProps={{ step: 0.1, min: 0.1 }}
                    fullWidth
                    margin="normal"
                    helperText="e.g., 1 for equal groups, 2 for 2 controls per case"
                  />
                )}
              </Box>
            )}
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<CalculateIcon />}
            onClick={handleCalculate}
            disabled={
              (solveFor === 'sampleSize' && (
                (studyType === 'twoProportions' && (isNaN(p1) || isNaN(p2) || p1 === p2 || isNaN(powerInput) || isNaN(ratioTwoProportions) || ratioTwoProportions <= 0)) ||
                (studyType === 'caseControl' && (isNaN(p0) || isNaN(oddsRatio) || oddsRatio <= 0 || isNaN(powerInput) || isNaN(ratioCaseControl) || ratioCaseControl <= 0))
              )) ||
              (solveFor === 'power' && (
                (studyType === 'twoProportions' && (isNaN(p1) || isNaN(p2) || p1 === p2 || isNaN(n1) || isNaN(n2) || n1 <= 0 || n2 <= 0)) ||
                (studyType === 'caseControl' && (isNaN(p0) || isNaN(oddsRatio) || oddsRatio <= 0 || isNaN(nCases) || isNaN(nControls) || nCases <= 0 || nControls <= 0))
              )) ||
              isNaN(alpha)
            }
          >
            Calculate
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={resetForm}
          >
            Reset
          </Button>
        </Box>
      </Paper>

      {/* Results Section */}
      {result && (
        <Paper elevation={1} sx={{ p: 3, mt: 3 }}>
          <Typography variant="h6" gutterBottom>Results</Typography>
          <Divider sx={{ mb: 2 }} />
          
          {result.type === 'sampleSize' && result.value && (
            <Box>
              <Typography variant="subtitle1" gutterBottom>Required Sample Size:</Typography>
              {studyType === 'twoProportions' && (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <StatsCard
                      title="Group 1 (n1)"
                      value={formatNumber((result.value as { n1: number }).n1)}
                      color="primary"
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <StatsCard
                      title="Group 2 (n2)"
                      value={formatNumber((result.value as { n2: number }).n2)}
                      color="primary"
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <StatsCard
                      title="Total Sample Size"
                      value={formatNumber((result.value as { total: number }).total)}
                      color="primary"
                      variant="gradient"
                    />
                  </Grid>
                </Grid>
              )}
              {studyType === 'caseControl' && (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <StatsCard
                      title="Cases (n)"
                      value={formatNumber((result.value as { nCases: number }).nCases)}
                      color="primary"
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <StatsCard
                      title="Controls (n)"
                      value={formatNumber((result.value as { nControls: number }).nControls)}
                      color="primary"
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <StatsCard
                      title="Total Sample Size"
                      value={formatNumber((result.value as { total: number }).total)}
                      color="primary"
                      variant="gradient"
                    />
                  </Grid>
                </Grid>
              )}
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                These are the minimum required sample sizes per group to achieve the specified power and significance level.
                <br />
                • Alpha (α): {formatNumber(result.params.alpha, 2)}
                <br />• Desired Power (1-β): {formatNumber(result.params.power, 2)}
                {result.params.p1 !== undefined && result.params.p2 !== undefined && (
                  <>
                    <br />• Proportion in Group 1 (P1): {formatNumber(result.params.p1, 2)}
                    <br />• Proportion in Group 2 (P2): {formatNumber(result.params.p2, 2)}
                    <br />• Ratio (n2/n1): {formatNumber(result.params.ratio, 1)}
                  </>
                )}
                {result.params.p0 !== undefined && result.params.oddsRatio !== undefined && (
                  <>
                    <br />• Proportion Exposed Among Controls (P0): {formatNumber(result.params.p0, 2)}
                    <br />• Odds Ratio (OR): {formatNumber(result.params.oddsRatio, 2)}
                    <br />• Ratio (Controls/Cases): {formatNumber(result.params.ratio, 1)}
                  </>
                )}
              </Typography>
            </Box>
          )}

          {result.type === 'power' && result.value && (
            <Box sx={{ mt: 0 }}>
              <Typography variant="subtitle1" gutterBottom>Calculated Power:</Typography>
              <StatsCard
                title="Power"
                value={formatPercent(result.value as number)}
                color="secondary"
                variant="gradient"
                tooltip="The probability of correctly rejecting the null hypothesis when it is false."
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                This is the statistical power of a study with the following parameters:
                <br />
                • Alpha (α): {formatNumber(result.params.alpha, 2)}
                {result.params.n1 !== undefined && result.params.n2 !== undefined && (
                  <>
                    <br />• Group 1 Sample Size (n1): {formatNumber(result.params.n1, 0)}
                    <br />• Group 2 Sample Size (n2): {formatNumber(result.params.n2, 0)}
                    <br />• Proportion in Group 1 (P1): {formatNumber(result.params.p1, 2)}
                    <br />• Proportion in Group 2 (P2): {formatNumber(result.params.p2, 2)}
                  </>
                )}
                {result.params.nCases !== undefined && result.params.nControls !== undefined && (
                  <>
                    <br />• Number of Cases (nCases): {formatNumber(result.params.nCases, 0)}
                    <br />• Number of Controls (nControls): {formatNumber(result.params.nControls, 0)}
                    <br />• Proportion Exposed Among Controls (P0): {formatNumber(result.params.p0, 2)}
                    <br />• Odds Ratio (OR): {formatNumber(result.params.oddsRatio, 2)}
                  </>
                )}
              </Typography>
            </Box>
          )}
        </Paper>
      )}
    </Box>
  );
};

export default SampleSizePowerCalculator;