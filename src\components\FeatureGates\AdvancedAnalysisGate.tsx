import React from 'react';
import { useAuth } from '../../context/AuthContext';
import FeatureUpgradePrompt from './FeatureUpgradePrompt';

interface AdvancedAnalysisGateProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showPrompt?: boolean;
  promptTitle?: string;
  promptDescription?: string;
}

const AdvancedAnalysisGate: React.FC<AdvancedAnalysisGateProps> = ({ 
  children, 
  fallback,
  showPrompt = true,
  promptTitle,
  promptDescription
}) => {
  const { canAccessAdvancedAnalysis, isEducationalUser, accountType, user } = useAuth();

  // Allow access if user can access Advanced Analysis
  if (canAccessAdvancedAnalysis) {
    return <>{children}</>;
  }

  // Return custom fallback if provided
  if (fallback) {
    return <>{fallback}</>;
  }

  // Don't show prompt if disabled
  if (!showPrompt) {
    return null;
  }

  // Determine appropriate messaging based on user type
  const getUpgradeMessage = () => {
    if (!user) {
      return "Sign in to access Advanced Analysis features. Educational users (.edu emails) get free access!";
    }

    if (isEducationalUser) {
      return "Advanced Analysis is included free with your educational account!";
    }

    return "Upgrade to Pro to access Advanced Analysis features including advanced statistical tests, data visualization, and analytical tools.";
  };

  const getFeatureList = () => {
    return [
      "Advanced statistical tests (ANOVA, Regression, etc.)",
      "Interactive data visualizations",
      "Correlation and factor analysis",
      "Non-parametric tests",
      "Custom analysis workflows",
      "Export analysis results"
    ];
  };

  return (
    <FeatureUpgradePrompt 
      featureName={promptTitle || "Advanced Analysis"}
      isEducationalUser={isEducationalUser}
      upgradeMessage={getUpgradeMessage()}
      description={promptDescription || "Unlock powerful statistical analysis tools and advanced visualizations for deeper data insights."}
      features={getFeatureList()}
      showUpgradeButton={!isEducationalUser || accountType !== 'edu'}
    />
  );
};

export default AdvancedAnalysisGate;
