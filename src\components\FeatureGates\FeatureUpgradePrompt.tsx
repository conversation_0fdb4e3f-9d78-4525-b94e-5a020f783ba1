import React from 'react';
import {
  <PERSON>,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  Box,
  Alert,
  Chip
} from '@mui/material';
import {
  Upgrade as UpgradeIcon,
  Lock as LockIcon,
  School as SchoolIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';

interface FeatureUpgradePromptProps {
  featureName: string;
  isEducationalUser?: boolean;
  upgradeMessage?: string;
  showUpgradeButton?: boolean;
  description?: string;
  features?: string[];
}

const FeatureUpgradePrompt: React.FC<FeatureUpgradePromptProps> = ({
  featureName,
  isEducationalUser = false,
  upgradeMessage,
  showUpgradeButton = true,
  description,
  features = []
}) => {
  const { user, accountType } = useAuth();
  const navigate = useNavigate();

  const handleUpgradeClick = () => {
    if (!user) {
      // Store intended action and redirect to login
      sessionStorage.setItem('subscriptionIntent', JSON.stringify({
        action: 'upgrade',
        feature: featureName,
        timestamp: Date.now()
      }));
      navigate('/app#/auth/login');
      return;
    }

    // Navigate to billing/upgrade page
    navigate('/app#/profile?tab=billing');
  };

  const handleSignInClick = () => {
    navigate('/app#/auth/login');
  };

  const getUpgradeContent = () => {
    if (!user) {
      return {
        title: `Sign In to Access ${featureName}`,
        message: 'Create a free account to unlock this feature',
        buttonText: 'Sign In',
        buttonAction: handleSignInClick,
        icon: <LockIcon />,
        color: 'info' as const
      };
    }

    if (isEducationalUser && accountType === 'edu') {
      return {
        title: `Upgrade to Access ${featureName}`,
        message: upgradeMessage || `Upgrade to Pro to unlock ${featureName} features`,
        buttonText: 'Upgrade to Pro ($10/month)',
        buttonAction: handleUpgradeClick,
        icon: <SchoolIcon />,
        color: 'secondary' as const
      };
    }

    return {
      title: `Upgrade to Pro for ${featureName}`,
      message: upgradeMessage || `Unlock ${featureName} with a Pro subscription`,
      buttonText: 'Upgrade to Pro',
      buttonAction: handleUpgradeClick,
      icon: <UpgradeIcon />,
      color: 'primary' as const
    };
  };

  const content = getUpgradeContent();

  return (
    <Card 
      sx={{ 
        maxWidth: 600, 
        mx: 'auto', 
        mt: 4,
        border: '2px dashed',
        borderColor: 'divider',
        backgroundColor: 'background.paper'
      }}
    >
      <CardContent sx={{ textAlign: 'center', py: 4 }}>
        <Box sx={{ mb: 2 }}>
          {content.icon}
        </Box>

        <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
          {content.title}
        </Typography>

        {isEducationalUser && accountType === 'edu' && (
          <Box sx={{ mb: 2 }}>
            <Chip 
              icon={<SchoolIcon />}
              label="Educational Account" 
              color="secondary" 
              size="small"
            />
          </Box>
        )}

        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          {content.message}
        </Typography>

        {description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3, fontStyle: 'italic' }}>
            {description}
          </Typography>
        )}

        {features.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              What you'll get:
            </Typography>
            <Box sx={{ textAlign: 'left', maxWidth: 400, mx: 'auto' }}>
              {features.map((feature, index) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <StarIcon sx={{ fontSize: 16, mr: 1, color: 'primary.main' }} />
                  <Typography variant="body2">{feature}</Typography>
                </Box>
              ))}
            </Box>
          </Box>
        )}

        {showUpgradeButton && (
          <Button
            variant="contained"
            color={content.color}
            size="large"
            startIcon={content.icon}
            onClick={content.buttonAction}
            sx={{ 
              minWidth: 200,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600
            }}
          >
            {content.buttonText}
          </Button>
        )}

        {/* Educational user specific messaging */}
        {isEducationalUser && accountType === 'edu' && (
          <Alert severity="info" sx={{ mt: 3, textAlign: 'left' }}>
            <Typography variant="body2">
              <strong>Educational Account Benefits:</strong><br />
              • You already have free access to Advanced Analysis features<br />
              • Upgrade to Pro for Publication Ready tools and Cloud Storage<br />
              • Same Pro pricing - no additional educational discount
            </Typography>
          </Alert>
        )}

        {/* Guest user messaging */}
        {!user && (
          <Alert severity="success" sx={{ mt: 3, textAlign: 'left' }}>
            <Typography variant="body2">
              <strong>Free Account Benefits:</strong><br />
              • Import your own data<br />
              • Local data storage<br />
              • Basic statistical analysis<br />
              • Educational users (.edu) get Advanced Analysis free!
            </Typography>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default FeatureUpgradePrompt;
