import React from 'react';
import { useAuth } from '../../context/AuthContext';
import FeatureUpgradePrompt from './FeatureUpgradePrompt';

interface PublicationReadyGateProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showPrompt?: boolean;
  promptTitle?: string;
  promptDescription?: string;
}

const PublicationReadyGate: React.FC<PublicationReadyGateProps> = ({ 
  children, 
  fallback,
  showPrompt = true,
  promptTitle,
  promptDescription
}) => {
  const { canAccessPublicationReady, isEducationalUser, accountType, user } = useAuth();

  // Allow access if user can access Publication Ready features
  if (canAccessPublicationReady) {
    return <>{children}</>;
  }

  // Return custom fallback if provided
  if (fallback) {
    return <>{fallback}</>;
  }

  // Don't show prompt if disabled
  if (!showPrompt) {
    return null;
  }

  // Determine appropriate messaging based on user type
  const getUpgradeMessage = () => {
    if (!user) {
      return "Sign in and upgrade to Pro to access Publication Ready features for academic and professional reporting.";
    }

    if (isEducationalUser && accountType === 'edu') {
      return "Upgrade to Pro to access Publication Ready features. Perfect for academic papers, theses, and research publications.";
    }

    return "Upgrade to Pro to access Publication Ready features including professional tables, statistical methods generation, and publication-quality outputs.";
  };

  const getFeatureList = () => {
    const baseFeatures = [
      "APA-formatted statistical tables",
      "Automated statistical methods text",
      "Publication-quality figures",
      "Results summary reports",
      "Citation-ready outputs",
      "Professional formatting templates"
    ];

    if (isEducationalUser && accountType === 'edu') {
      return [
        ...baseFeatures,
        "Perfect for academic papers and theses",
        "Research publication support",
        "Institutional reporting tools"
      ];
    }

    return baseFeatures;
  };

  const getButtonText = () => {
    if (!user) {
      return "Sign In & Upgrade";
    }

    if (isEducationalUser && accountType === 'edu') {
      return "Upgrade to Pro ($10/month)";
    }

    return "Upgrade to Pro";
  };

  return (
    <FeatureUpgradePrompt 
      featureName={promptTitle || "Publication Ready"}
      isEducationalUser={isEducationalUser}
      upgradeMessage={getUpgradeMessage()}
      description={promptDescription || "Generate publication-quality tables, figures, and statistical reports ready for academic papers, presentations, and professional documents."}
      features={getFeatureList()}
      showUpgradeButton={true}
    />
  );
};

export default PublicationReadyGate;
