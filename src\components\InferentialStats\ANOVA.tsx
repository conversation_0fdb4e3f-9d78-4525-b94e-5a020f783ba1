import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControlLabel,
  Checkbox,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Divider,
  TextField,
  Tooltip,
  IconButton,
  Tabs, // Added Tabs import
  Tab   // Added Tab import
} from '@mui/material';
import {
  Science as ScienceIcon,
  Help as HelpIcon,
  Bar<PERSON>hart as BarChartIcon,
  Show<PERSON>hart as ShowChartIcon,
  CompareArrows as CompareArrowsIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, VariableRole } from '../../types';
import {
  oneWayANOVA,
  kruskalWallisTest,
  comprehensiveNormalityTest,
  calculateMean,
  calculateStandardDeviation
} from '@/utils/stats';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON>ltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  ErrorBar,
  ReferenceLine,
  LineChart,
  Line,
  ScatterChart,
  Scatter,
  ComposedChart,
  Cell
} from 'recharts';
import OneWayANOVA from './ANOVA/OneWayANOVA'; // Assuming OneWayANOVA is the default export from its file
import TwoWayANOVA from './ANOVA/TwoWayANOVA'; // Import TwoWayANOVA
import { RepeatedMeasuresANOVA } from './ANOVA/RepeatedMeasuresANOVA'; // Import RepeatedMeasuresANOVA as named export

// Component for ANOVA analysis (now a tab container)
const ANOVATest: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        ANOVA Tests
      </Typography>

      <Paper elevation={0} sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="ANOVA type tabs">
            <Tab label="One-Way ANOVA" />
            <Tab label="Two-Way ANOVA" />
            <Tab label="Repeated Measures ANOVA" /> 
          </Tabs>
        </Box>
        {activeTab === 0 && (
          <Box sx={{pt: 2}}>
            <OneWayANOVA />
          </Box>
        )}
        {activeTab === 1 && (
          <Box sx={{pt: 2}}>
            <TwoWayANOVA />
          </Box>
        )}
        {activeTab === 2 && (
          <Box sx={{pt: 2}}>
            <RepeatedMeasuresANOVA />
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default ANOVATest;
