import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControlLabel,
  Checkbox,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Divider,
  TextField,
  Tooltip,
  IconButton,
  Stack,
  Chip,
  Card,
  CardContent
} from '@mui/material';
import {
  Help as HelpIcon,
  Bar<PERSON>hart as BarChartIcon,
  ShowChart as ShowChartIcon,
  CompareArrows as CompareArrowsIcon,
  Science as ScienceIcon,
  Info as InfoIcon,
  Calculate as CalculateIcon,
  TrendingUp as TrendingUpIcon,
  Functions as FunctionsIcon,
  Assessment as AssessmentIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  People as PeopleIcon,
  Timeline as TimelineIcon,
  SettingsBackupRestore as SettingsBackupRestoreIcon
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType } from '../../../types';
import StatsCard from '../../UI/StatsCard';
import {
  oneWayANOVA,
  kruskalWallisTest,
  isNormallyDistributed,
  calculateMean,
  calculateStandardDeviation,
  performLeveneTest
} from '@/utils/stats';
import { extractNumericValues } from '../../../utils/typeConversions';
import { getOrderedCategoriesByColumnId } from '../../../utils/dataUtilities';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  ErrorBar,
  ReferenceLine
  // BoxPlot removed due to import error
} from 'recharts';

const OneWayANOVA: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();
  
  // State for test options
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const activeDataset = datasets.find(ds => ds.id === selectedDatasetId) || currentDataset;
  const [selectedVariable, setSelectedVariable] = useState<string>('');
  const [selectedFactor, setSelectedFactor] = useState<string>('');
  const [useNonParametric, setUseNonParametric] = useState<boolean>(false);
  const [confidenceLevel, setConfidenceLevel] = useState<number>(0.95);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  
  // State for assumption checks
  const [checkAssumptions, setCheckAssumptions] = useState<boolean>(true);
  
  // State for results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [anovaResults, setAnovaResults] = useState<any | null>(null);
  const [assumptionResults, setAssumptionResults] = useState<any | null>(null);
  const [groupData, setGroupData] = useState<any[]>([]);
  
  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedAnovaResults = localStorage.getItem('oneway_anova_results');
    const savedAssumptionResults = localStorage.getItem('oneway_anova_assumptions');
    const savedGroupData = localStorage.getItem('oneway_anova_group_data');
    
    if (savedAnovaResults) {
      try {
        setAnovaResults(JSON.parse(savedAnovaResults));
      } catch (e) {
        console.error('Error parsing saved ANOVA results:', e);
      }
    }
    
    if (savedAssumptionResults) {
      try {
        setAssumptionResults(JSON.parse(savedAssumptionResults));
      } catch (e) {
        console.error('Error parsing saved assumption results:', e);
      }
    }
    
    if (savedGroupData) {
      try {
        setGroupData(JSON.parse(savedGroupData));
      } catch (e) {
        console.error('Error parsing saved group data:', e);
      }
    }
  }, []);
  
  // Get numeric columns from active dataset
  const numericColumns = activeDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];
  
  // Get categorical columns for factors
  const categoricalColumns = activeDataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL || col.type === DataType.ORDINAL || col.type === DataType.BOOLEAN
  ) || [];
  
  // Get unique values for the selected factor
  const factorValues = (() => {
    if (!activeDataset || !selectedFactor) return [];

    const column = activeDataset.columns.find(col => col.id === selectedFactor);
    if (!column) return [];

    // Use ordered categories for consistent ordering
    return getOrderedCategoriesByColumnId(selectedFactor, activeDataset);
  })();
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    const selectedDs = datasets.find(ds => ds.id === newDatasetId);
    if (selectedDs) {
        setCurrentDataset(selectedDs); // Update context
    }
    resetSelections();
  };
  
  // Reset selections
  const resetSelections = () => {
    setSelectedVariable('');
    setSelectedFactor('');
    setSelectedGroups([]);
    setError(null);
    
    // Clear saved results
    localStorage.removeItem('oneway_anova_results');
    localStorage.removeItem('oneway_anova_assumptions');
    localStorage.removeItem('oneway_anova_group_data');
    
    setAnovaResults(null);
    setAssumptionResults(null);
    setGroupData([]);
  };
  
  // Handle variable selection change
  const handleVariableChange = (event: SelectChangeEvent<string>) => {
    setSelectedVariable(event.target.value);
    setAnovaResults(null);
    setAssumptionResults(null);
  };
  
  // Handle factor selection change
  const handleFactorChange = (event: SelectChangeEvent<string>) => {
    setSelectedFactor(event.target.value);
    setSelectedGroups([]);
    setAnovaResults(null);
    setAssumptionResults(null);
  };
  
  // Handle group selection change
  const handleGroupsChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedGroups(typeof value === 'string' ? value.split(',') : value);
    setAnovaResults(null);
    setAssumptionResults(null);
  };
  
  // Handle confidence level change
  const handleConfidenceLevelChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value);
    if (!isNaN(value) && value > 0 && value < 1) {
      setConfidenceLevel(value);
    }
  };
  
  // Check if form is valid
  const isFormValid = () => {
    if (!activeDataset || !selectedVariable || !selectedFactor) return false;
    return selectedGroups.length >= 3 || (factorValues.length >= 3 && selectedGroups.length === 0);
  };
  
  // Function to interpret effect size
  const interpretEffectSize = (etaSquared: number) => {
    if (etaSquared < 0.01) return 'Very Small';
    if (etaSquared < 0.06) return 'Small';
    if (etaSquared < 0.14) return 'Medium';
    return 'Large';
  };
  
  // Run ANOVA test
  const runTest = () => {
    if (!activeDataset || !selectedVariable || !selectedFactor) {
      setError('Please select variable and grouping factor.');
      return;
    }
    
    setLoading(true);
    setError(null);
    setAnovaResults(null);
    setAssumptionResults(null);
    
    try {
      // Get column objects
      const variableColumn = activeDataset.columns.find(col => col.id === selectedVariable);
      const factorColumn = activeDataset.columns.find(col => col.id === selectedFactor);
      
      if (!variableColumn || !factorColumn) {
        throw new Error('Selected variables not found.');
      }
      
      // Get unique factor values
      const factorValuesToUse = selectedGroups.length > 0 ? selectedGroups : factorValues;
      
      if (factorValuesToUse.length < 3) {
        throw new Error('ANOVA requires at least 3 groups. For 2 groups, use t-test instead.');
      }
      
      // Separate data into groups
      const groups: number[][] = [];
      const groupNames: string[] = [];
      const groupStats: any[] = [];
      
      factorValuesToUse.forEach(value => {
        const allGroupValues = activeDataset.data
          .filter(row => String(row[factorColumn.name]) === value)
          .map(row => row[variableColumn.name]);
        const groupData = extractNumericValues(allGroupValues);
        
        if (groupData.length > 0) {
          groups.push(groupData);
          groupNames.push(value);
          
          // Calculate group statistics
          const mean = calculateMean(groupData);
          const sd = calculateStandardDeviation(groupData);
          const n = groupData.length;
          const se = sd / Math.sqrt(n);
          const ci95 = [mean - 1.96 * se, mean + 1.96 * se];
          
          groupStats.push({
            name: value,
            n,
            mean,
            sd,
            se,
            ci95Low: ci95[0],
            ci95High: ci95[1],
            data: groupData
          });
        }
      });
      
      setGroupData(groupStats);
      
      if (groups.length < 3) {
        throw new Error('At least 3 groups with valid data are required for ANOVA.');
      }
      
      // Check assumptions if enabled
      if (checkAssumptions) {
        const normalityResults = groups.map((group, i) => {
          return {
            group: groupNames[i],
            isNormal: isNormallyDistributed(group), // This likely returns {isNormal, pValue}
            n: group.length
          };
        });
        
        const violatesNormality = normalityResults.some(result => !result.isNormal.isNormal); // Access the boolean
        
        let leveneAnovaResult = null;
        let homogeneityViolated = false;
        try {
          if (groups.every(g => g.length > 1)) { // Levene's test needs >1 obs per group
            leveneAnovaResult = performLeveneTest(...groups);
            homogeneityViolated = leveneAnovaResult.rejected;
          } else {
            leveneAnovaResult = { error: "Not all groups have sufficient data (>1 obs) for Levene's test." };
            homogeneityViolated = true; // Assume violation if test can't run, or handle as warning
          }
        } catch (e) {
          const errorMessage = e instanceof Error ? e.message : "Error during Levene's test.";
          leveneAnovaResult = { error: errorMessage };
          homogeneityViolated = true; // Treat error as violation for safety
        }

        const assumptions = {
          normalityResults,
          violatesNormality,
          leveneTest: leveneAnovaResult,
          homogeneityViolated
        };
        
        setAssumptionResults(assumptions);
        
        // Save assumption results to localStorage
        localStorage.setItem('oneway_anova_assumptions', JSON.stringify(assumptions));
        
        // If normality or homogeneity is violated, suggest non-parametric test
        if ((violatesNormality || homogeneityViolated) && !useNonParametric) {
          // setError("Assumptions violated. Consider using Kruskal-Wallis or ensure 'Use non-parametric test' is checked.");
          // Automatically switch or strongly recommend:
           setUseNonParametric(true); // Or provide a stronger UI prompt
        }
      }
      
      // Determine which test to run based on user choice or assumption checks
      const runKruskalWallis = useNonParametric || (checkAssumptions && assumptionResults?.violatesNormality) || (checkAssumptions && assumptionResults?.homogeneityViolated);

      if (runKruskalWallis) {
        // Run Kruskal-Wallis test
        const kwResults = kruskalWallisTest(groups);
        const results = {
          ...kwResults,
          testType: 'kruskal-wallis',
          groupNames,
          variableName: variableColumn.name,
          factorName: factorColumn.name,
          groups: groupStats
        };
        
        setAnovaResults(results);
        
        // Save results to localStorage
        localStorage.setItem('oneway_anova_results', JSON.stringify(results));
        localStorage.setItem('oneway_anova_group_data', JSON.stringify(groupStats));
      } else {
        // Run one-way ANOVA
        const anovaResult = oneWayANOVA(groups);
        const results = {
          ...anovaResult,
          testType: 'anova',
          groupNames,
          variableName: variableColumn.name,
          factorName: factorColumn.name,
          groups: groupStats
        };
        
        setAnovaResults(results);
        
        // Save results to localStorage
        localStorage.setItem('oneway_anova_results', JSON.stringify(results));
        localStorage.setItem('oneway_anova_group_data', JSON.stringify(groupStats));
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while running the test.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  
  // Prepare data for bar chart
  const barChartData = groupData.map(group => ({
    name: group.name,
    mean: group.mean,
    errorHigh: group.ci95High - group.mean,
    errorLow: group.mean - group.ci95Low
  }));
  
  // Prepare data for box plot
  const boxPlotData = groupData.map(group => {
    const sortedData = [...group.data].sort((a, b) => a - b);
    const q1 = sortedData[Math.floor(sortedData.length * 0.25)];
    const median = sortedData[Math.floor(sortedData.length * 0.5)];
    const q3 = sortedData[Math.floor(sortedData.length * 0.75)];
    const min = sortedData[0];
    const max = sortedData[sortedData.length - 1];
    
    return {
      name: group.name,
      min,
      q1,
      median,
      q3,
      max
    };
  });
  
  return (
    <Box>
      <Paper elevation={0} sx={{ p: 3, mb: 3, borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
        <Stack direction="row" alignItems="center" spacing={2} mb={3}>
          <ScienceIcon sx={{ color: theme.palette.primary.main, fontSize: 28 }} />
          <Typography variant="h6" fontWeight="bold">One-Way ANOVA Configuration</Typography>
        </Stack>

        {/* Info notice */}
        <Alert severity="info" icon={<InfoIcon />} sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>One-Way ANOVA:</strong> This component performs a complete one-way analysis of variance
            to compare means across three or more independent groups, calculating F-statistics, p-values, and effect sizes.
          </Typography>
        </Alert>

        <Typography variant="h6" gutterBottom>Test Options</Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
              >
                {datasets.map(ds => (
                  <MenuItem key={ds.id} value={ds.id}>
                    {ds.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel id="variable-select-label">Dependent Variable (Numeric)</InputLabel>
              <Select
                labelId="variable-select-label"
                value={selectedVariable}
                label="Dependent Variable (Numeric)"
                onChange={handleVariableChange}
              >
                {numericColumns.map(column => (
                  <MenuItem key={column.id} value={column.id}>
                    {column.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel id="factor-select-label">Grouping Factor (Categorical)</InputLabel>
              <Select
                labelId="factor-select-label"
                value={selectedFactor}
                label="Grouping Factor (Categorical)"
                onChange={handleFactorChange}
              >
                {categoricalColumns.map(column => (
                  <MenuItem key={column.id} value={column.id}>
                    {column.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            {selectedFactor && factorValues.length > 0 && (
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="groups-select-label">Groups to Include (min 3)</InputLabel>
                <Select
                  labelId="groups-select-label"
                  multiple
                  value={selectedGroups}
                  label="Groups to Include (min 3)"
                  onChange={handleGroupsChange}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map((value) => (
                        <Typography key={value} variant="body2" component="span">
                          {value}{(selected as string[]).indexOf(value) < (selected as string[]).length - 1 ? ', ' : ''}
                        </Typography>
                      ))}
                    </Box>
                  )}
                >
                  {factorValues.map(value => (
                    <MenuItem key={value} value={value}>
                      {value}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
            
            <FormControl fullWidth sx={{ mb: 2 }}>
              <TextField
                label="Confidence Level"
                type="number"
                value={confidenceLevel}
                onChange={handleConfidenceLevelChange}
                inputProps={{
                  step: 0.01,
                  min: 0.8,
                  max: 0.99
                }}
                helperText={`${confidenceLevel * 100}% confidence interval`}
              />
            </FormControl>
            
            <FormControlLabel
              control={
                <Checkbox
                  checked={checkAssumptions}
                  onChange={(e) => setCheckAssumptions(e.target.checked)}
                />
              }
              label="Check test assumptions"
            />
            
            <FormControlLabel
              control={
                <Checkbox
                  checked={useNonParametric}
                  onChange={(e) => setUseNonParametric(e.target.checked)}
                />
              }
              label="Use non-parametric test (Kruskal-Wallis)"
            />
          </Grid>
        </Grid>
        
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            onClick={runTest}
            disabled={!isFormValid() || loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Running...' : 'Run Test'}
          </Button>
        </Box>
      </Paper>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {assumptionResults && (
        <Paper elevation={2} sx={{ p: 3, mb: 3, bgcolor: 'background.paper' }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SettingsBackupRestoreIcon color="primary" />
              Assumption Checks
            </Typography>
            <Tooltip title="ANOVA assumes normality of data within each group and homogeneity of variances across groups.">
              <IconButton size="small">
                <HelpIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>

          {/* Normality Check */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <ShowChartIcon fontSize="small" />
              Normality Check (Shapiro-Wilk Test)
            </Typography>
            <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
              <TableContainer>
                <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                  <TableHead>
                    <TableRow>
                      <TableCell>Group</TableCell>
                      <TableCell align="right">N</TableCell>
                      <TableCell align="right">p-value</TableCell>
                      <TableCell align="right">Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {assumptionResults.normalityResults.map((result: any, index: number) => (
                      <TableRow key={result.group} sx={{ '&:nth-of-type(odd)': { bgcolor: 'grey.25' } }}>
                        <TableCell sx={{ fontWeight: 500 }}>{result.group}</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>{result.n}</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                          {result.isNormal.pValue.toFixed(4)}
                        </TableCell>
                        <TableCell align="right">
                          <Chip
                            label={result.isNormal.isNormal ? 'Normal' : 'Non-normal'}
                            color={result.isNormal.isNormal ? 'success' : 'error'}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <Alert
                severity={assumptionResults.violatesNormality ? 'warning' : 'success'}
                sx={{ mt: 2 }}
              >
                <Typography variant="body2">
                  {assumptionResults.violatesNormality
                    ? 'Normality assumption is violated for one or more groups. Consider using Kruskal-Wallis test.'
                    : 'Normality assumption is met for all groups.'}
                </Typography>
              </Alert>
            </Paper>
          </Box>

          {/* Homogeneity of Variances */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <TimelineIcon fontSize="small" />
              Homogeneity of Variances (Levene's Test)
            </Typography>
            <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
              {assumptionResults.leveneTest && !assumptionResults.leveneTest.error ? (
                <>
                  <TableContainer>
                    <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                      <TableHead>
                        <TableRow>
                          <TableCell>Test</TableCell>
                          <TableCell align="right">F-Statistic</TableCell>
                          <TableCell align="right">df1</TableCell>
                          <TableCell align="right">df2</TableCell>
                          <TableCell align="right">p-value</TableCell>
                          <TableCell align="right">Status</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 500 }}>Levene's Test</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            {assumptionResults.leveneTest.statistic.toFixed(3)}
                          </TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            {assumptionResults.leveneTest.df[0]}
                          </TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            {assumptionResults.leveneTest.df[1]}
                          </TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            {assumptionResults.leveneTest.pValue.toFixed(3)}
                          </TableCell>
                          <TableCell align="right">
                            <Chip
                              label={!assumptionResults.leveneTest.rejected ? 'Equal Variances' : 'Unequal Variances'}
                              color={!assumptionResults.leveneTest.rejected ? 'success' : 'error'}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>

                  <Alert
                    severity={!assumptionResults.leveneTest.rejected ? 'success' : 'warning'}
                    sx={{ mt: 2 }}
                  >
                    <Typography variant="body2">
                      {!assumptionResults.leveneTest.rejected
                        ? 'Homogeneity of variances assumption is met.'
                        : 'Homogeneity of variances assumption is violated. Consider using Welch\'s ANOVA or Kruskal-Wallis test.'}
                    </Typography>
                  </Alert>
                </>
              ) : (
                <Alert severity="warning">
                  <Typography variant="body2">
                    {assumptionResults.leveneTest?.error || 'Homogeneity of variances assumption may be violated.'}
                  </Typography>
                </Alert>
              )}
            </Paper>
          </Box>

          {/* Overall Assessment */}
          {(assumptionResults.violatesNormality || assumptionResults.homogeneityViolated) && (
            <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: 'warning.50' }}>
              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <InfoIcon fontSize="small" />
                Overall Assessment
              </Typography>
              <Alert severity="info">
                <Typography variant="body2">
                  Due to assumption violations, the Kruskal-Wallis test is recommended or was automatically selected as a non-parametric alternative.
                </Typography>
              </Alert>
            </Paper>
          )}
        </Paper>
      )}
      
      {anovaResults && (
        <Paper elevation={2} sx={{ p: 3, mb: 3, bgcolor: 'background.paper' }}>
          <Box display="flex" justifyContent="flex-start" alignItems="center" mb={3}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ScienceIcon color="primary" />
              {anovaResults.testType === 'anova' ? 'One-Way ANOVA Results' : 'Kruskal-Wallis Test Results'}
            </Typography>
          </Box>

          {/* Key Statistics Overview */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <CalculateIcon fontSize="small" />
              Key Statistics
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title={anovaResults.testType === 'anova' ? 'F-statistic' : 'H-statistic'}
                  value={anovaResults.testType === 'anova' ? anovaResults.F.toFixed(4) : anovaResults.H.toFixed(4)}
                  description="Test statistic value"
                  color="primary"
                  variant="outlined"
                  icon={<CalculateIcon />}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="p-value"
                  value={anovaResults.pValue < 0.001 ? '< 0.001' : anovaResults.pValue.toFixed(4)}
                  description={anovaResults.pValue < 0.05 ? 'Statistically significant' : 'Not significant'}
                  color={anovaResults.pValue < 0.05 ? 'success' : 'warning'}
                  variant="outlined"
                  icon={<ShowChartIcon />}
                />
              </Grid>
              {anovaResults.testType === 'anova' && (
                <>
                  <Grid item xs={12} sm={6} md={3}>
                    <StatsCard
                      title="Degrees of Freedom"
                      value={`${anovaResults.dfBetween}, ${anovaResults.dfWithin}`}
                      description="Between, Within groups"
                      color="info"
                      variant="outlined"
                      icon={<FunctionsIcon />}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <StatsCard
                      title="Effect Size (η²)"
                      value={anovaResults.etaSquared.toFixed(4)}
                      description={interpretEffectSize(anovaResults.etaSquared)}
                      color="secondary"
                      variant="outlined"
                      icon={<TrendingUpIcon />}
                    />
                  </Grid>
                </>
              )}
              {anovaResults.testType === 'kruskal-wallis' && (
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    title="Degrees of Freedom"
                    value={anovaResults.df.toString()}
                    description="Test degrees of freedom"
                    color="info"
                    variant="outlined"
                    icon={<FunctionsIcon />}
                  />
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="Number of Groups"
                  value={anovaResults.groupNames.length.toString()}
                  description="Groups compared"
                  color="info"
                  variant="outlined"
                  icon={<PeopleIcon />}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Test Information */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <InfoIcon fontSize="small" />
              Test Information
            </Typography>
            <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">Dependent Variable</Typography>
                  <Typography variant="body1" fontWeight={500}>{anovaResults.variableName}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">Grouping Factor</Typography>
                  <Typography variant="body1" fontWeight={500}>{anovaResults.factorName}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">Test Type</Typography>
                  <Typography variant="body1" fontWeight={500}>
                    {anovaResults.testType === 'anova' ? 'One-Way ANOVA' : 'Kruskal-Wallis Test'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">Groups</Typography>
                  <Typography variant="body1" fontWeight={500}>
                    {anovaResults.groupNames.join(', ')}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          </Box>

          {/* Test Summary Table */}
          {anovaResults.testType === 'anova' && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <FunctionsIcon fontSize="small" />
                ANOVA Summary
              </Typography>
              <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                <TableContainer>
                  <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                    <TableHead>
                      <TableRow>
                        <TableCell>Statistic</TableCell>
                        <TableCell align="right">Value</TableCell>
                        <TableCell align="right">Interpretation</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 500 }}>F-statistic</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                          {anovaResults.F.toFixed(4)}
                        </TableCell>
                        <TableCell align="right">
                          <Chip
                            label={anovaResults.pValue < 0.05 ? 'Significant' : 'Not Significant'}
                            color={anovaResults.pValue < 0.05 ? 'success' : 'default'}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 500 }}>Degrees of Freedom</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                          {anovaResults.dfBetween}, {anovaResults.dfWithin}
                        </TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                          Between, Within
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 500 }}>p-value</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace', color: anovaResults.pValue < 0.05 ? 'success.main' : 'text.primary' }}>
                          {anovaResults.pValue < 0.001 ? '< 0.001' : anovaResults.pValue.toFixed(4)}
                        </TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                          α = 0.05
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 500 }}>Effect Size (η²)</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                          {anovaResults.etaSquared.toFixed(4)}
                        </TableCell>
                        <TableCell align="right">
                          <Chip
                            label={interpretEffectSize(anovaResults.etaSquared)}
                            color="secondary"
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Box>
          )}

          {/* Descriptive Statistics */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <AssessmentIcon fontSize="small" />
              Group Descriptive Statistics
            </Typography>
            <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
              <TableContainer>
                <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                  <TableHead>
                    <TableRow>
                      <TableCell>Group</TableCell>
                      <TableCell align="right">N</TableCell>
                      <TableCell align="right">Mean</TableCell>
                      <TableCell align="right">SD</TableCell>
                      <TableCell align="right">SE</TableCell>
                      <TableCell align="right">95% CI</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {anovaResults.groups.map((group: any, index: number) => (
                      <TableRow key={group.name} sx={{ '&:nth-of-type(odd)': { bgcolor: 'grey.25' } }}>
                        <TableCell sx={{ fontWeight: 500 }}>{group.name}</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>{group.n}</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>{group.mean.toFixed(3)}</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>{group.sd.toFixed(3)}</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>{group.se.toFixed(3)}</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                          [{group.ci95Low.toFixed(3)}, {group.ci95High.toFixed(3)}]
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Box>

          {/* Enhanced Interpretation Section */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <InfoIcon fontSize="small" />
              Statistical Interpretation
            </Typography>
            <Paper elevation={0} variant="outlined" sx={{ p: 3, bgcolor: 'grey.50' }}>
              {/* Summary */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom color="primary">
                  Summary
                </Typography>
                <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                  {anovaResults.pValue < 0.05 ? (
                    <>
                      The {anovaResults.testType === 'anova' ? 'one-way ANOVA' : 'Kruskal-Wallis test'} revealed a statistically significant difference between groups
                      ({anovaResults.testType === 'anova' ? `F(${anovaResults.dfBetween}, ${anovaResults.dfWithin}) = ${anovaResults.F.toFixed(2)}` : `H(${anovaResults.df}) = ${anovaResults.H.toFixed(2)}`},
                      p {anovaResults.pValue < 0.001 ? '< 0.001' : `= ${anovaResults.pValue.toFixed(3)}`}).
                      {anovaResults.testType === 'anova' && ` The effect size (η² = ${anovaResults.etaSquared.toFixed(3)}) indicates a ${interpretEffectSize(anovaResults.etaSquared).toLowerCase()} effect.`}
                    </>
                  ) : (
                    <>
                      The {anovaResults.testType === 'anova' ? 'one-way ANOVA' : 'Kruskal-Wallis test'} did not reveal a statistically significant difference between groups
                      ({anovaResults.testType === 'anova' ? `F(${anovaResults.dfBetween}, ${anovaResults.dfWithin}) = ${anovaResults.F.toFixed(2)}` : `H(${anovaResults.df}) = ${anovaResults.H.toFixed(2)}`},
                      p = {anovaResults.pValue.toFixed(3)}).
                    </>
                  )}
                </Typography>
              </Box>

              {/* Statistical Significance */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom color="primary">
                  Statistical Significance
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Chip
                    label={anovaResults.pValue < 0.05 ? 'Significant' : 'Not Significant'}
                    color={anovaResults.pValue < 0.05 ? 'success' : 'default'}
                    size="small"
                  />
                  <Typography variant="body2" color="text.secondary">
                    (α = 0.05)
                  </Typography>
                </Box>
                <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                  {anovaResults.pValue < 0.05
                    ? `The p-value (${anovaResults.pValue < 0.001 ? '< 0.001' : anovaResults.pValue.toFixed(4)}) is less than the significance level of 0.05, indicating a statistically significant result.`
                    : `The p-value (${anovaResults.pValue.toFixed(4)}) is greater than the significance level of 0.05, indicating no statistically significant difference between groups.`
                  }
                </Typography>
              </Box>

              {/* Effect Size Interpretation */}
              {anovaResults.testType === 'anova' && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom color="primary">
                    Effect Size
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Chip
                      label={interpretEffectSize(anovaResults.etaSquared)}
                      color="secondary"
                      size="small"
                    />
                    <Typography variant="body2" color="text.secondary">
                      (η² = {anovaResults.etaSquared.toFixed(4)})
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                    The effect size indicates a {interpretEffectSize(anovaResults.etaSquared).toLowerCase()} practical significance of the observed differences between groups.
                  </Typography>
                </Box>
              )}

              {/* Next Steps */}
              <Box>
                <Typography variant="subtitle2" gutterBottom color="primary">
                  Next Steps
                </Typography>
                <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                  {anovaResults.pValue < 0.05
                    ? 'Post-hoc tests (e.g., Tukey HSD, Bonferroni) would be needed to determine which specific groups differ from each other.'
                    : 'No further analysis is needed as there are no significant differences between groups to explore.'
                  }
                </Typography>
              </Box>
            </Paper>
          </Box>

          {/* Visualizations */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <BarChartIcon fontSize="small" />
              Visualizations
            </Typography>

            <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Group Means Comparison</Typography>
              <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
                Error bars represent 95% confidence intervals
              </Typography>
              <Box sx={{ height: 400, mt: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={barChartData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 60,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.grey[300]} />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <RechartsTooltip
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.grey[300]}`,
                        borderRadius: 4
                      }}
                    />
                    <Legend />
                    <Bar
                      dataKey="mean"
                      fill={theme.palette.primary.main}
                      name="Mean"
                      radius={[4, 4, 0, 0]}
                    >
                      <ErrorBar
                        dataKey="errorHigh"
                        direction="y"
                        stroke={theme.palette.error.main}
                        strokeWidth={2}
                        width={4}
                      />
                      <ErrorBar
                        dataKey="errorLow"
                        direction="y"
                        stroke={theme.palette.error.main}
                        strokeWidth={2}
                        width={4}
                      />
                    </Bar>
                    {anovaResults.testType === 'anova' && anovaResults.grandMean && (
                      <ReferenceLine
                        y={anovaResults.grandMean}
                        stroke={theme.palette.secondary.main}
                        strokeDasharray="5 5"
                        label={{ value: "Grand Mean", position: "topRight" }}
                      />
                    )}
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </Paper>
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default OneWayANOVA;
