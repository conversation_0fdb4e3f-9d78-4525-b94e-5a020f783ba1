import React, { useState } from 'react';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Divider,
  alpha,
  useTheme,
  Chip
} from '@mui/material';
import {
  ViewStream as ViewStreamIcon,
  ViewModule as ViewModuleIcon,
  Info as InfoIcon,
  Repeat as RepeatIcon
} from '@mui/icons-material';
import { TabPanel } from '../../UI';
import OneWayANOVA from './OneWayANOVA';
import TwoWayANOVA from './TwoWayANOVA';
import RepeatedMeasuresANOVA from './RepeatedMeasuresANOVA';

interface ANOVAProps {
  initialTab?: string;
}

const ANOVA: React.FC<ANOVAProps> = ({ initialTab = 'oneway' }) => {
  const theme = useTheme();
  
  // Map tab names to indices
  const tabNameToIndex: Record<string, number> = {
    'oneway': 0,
    'twoway': 1,
    'repeated': 2
  };
  
  const [activeTab, setActiveTab] = useState<number>(
    initialTab && tabNameToIndex[initialTab] !== undefined 
      ? tabNameToIndex[initialTab] 
      : 0
  );

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ width: '100%', minHeight: '800px' }}>
      <Tabs
        value={activeTab}
        onChange={handleChange}
        aria-label="anova-types"
        variant="fullWidth"
        sx={{ 
          mb: 3,
          borderBottom: 1,
          borderColor: 'divider',
          '& .MuiTab-root': {
            minHeight: 64,
            textTransform: 'none',
          }
        }}
      >
        <Tab 
          label={
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="subtitle2" fontWeight="medium">One-Way ANOVA</Typography>
              <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                Compare three or more groups
              </Typography>
            </Box>
          } 
          icon={<ViewStreamIcon />}
          iconPosition="start"
        />
        <Tab
          label={
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="subtitle2" fontWeight="medium">Two-Way ANOVA</Typography>
              <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                Analyze effects of two factors
              </Typography>
            </Box>
          }
          icon={<ViewModuleIcon />}
          iconPosition="start"
        />
        <Tab
          label={
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="subtitle2" fontWeight="medium">Repeated Measures ANOVA</Typography>
              <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                Compare repeated measurements
              </Typography>
            </Box>
          }
          icon={<RepeatIcon />}
          iconPosition="start"
        />
      </Tabs>
      
      <Box 
        sx={{ 
          p: 2, 
          mb: 3, 
          borderRadius: 2,
          backgroundColor: alpha(theme.palette.primary.main, 0.04),
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <InfoIcon color="primary" sx={{ mr: 2 }} />
        <Box>
          <Typography variant="subtitle2" color="primary">
            About ANOVA
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Analysis of Variance (ANOVA) is used to determine whether there are statistically significant differences between the means of three or more independent groups.
            Select the appropriate test based on your study design and data structure.
          </Typography>
        </Box>
      </Box>
      
      <TabPanel value={activeTab} index={0}>
        <OneWayANOVA />
      </TabPanel>

      <TabPanel value={activeTab} index={1}>
        <TwoWayANOVA />
      </TabPanel>

      <TabPanel value={activeTab} index={2}>
        <RepeatedMeasuresANOVA />
      </TabPanel>
    </Box>
  );
};

export default ANOVA;