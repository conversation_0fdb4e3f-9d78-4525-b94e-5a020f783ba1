import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  CircularProgress,
  SelectChangeEvent,
  useTheme,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Checkbox
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  Functions as FunctionsIcon,
  InfoOutlined as InfoIcon 
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, Column, Dataset } from '../../types';
import { calculateFriedmanTest, FriedmanTestResult } from '@/utils/stats/friedman';

const FriedmanTest: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();

  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [selectedVariables, setSelectedVariables] = useState<string[]>([]); // For repeated measures
  
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<FriedmanTestResult | null>(null);

  const activeDataset = datasets.find(ds => ds.id === selectedDatasetId);

  const numericColumns = activeDataset?.columns.filter(
    col => col.type === DataType.NUMERIC || col.type === DataType.ORDINAL
  ) || [];

  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
      setSelectedVariables([]);
      setResults(null);
      setError(null);
    }
  }, [currentDataset, selectedDatasetId]);

  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedVariables([]);
    setResults(null);
    setError(null);
    const newDs = datasets.find(ds => ds.id === newDatasetId);
    if (newDs) setCurrentDataset(newDs);
  };

  const handleVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedVariables(typeof value === 'string' ? value.split(',') : value);
  };

  const runFriedmanTest = async () => {
    if (!activeDataset || selectedVariables.length < 3) {
      setError('Please select a dataset and at least three numeric/ordinal variables for the repeated measures.');
      return;
    }

    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const dataForTest: number[][] = [];
      const n = activeDataset.data.length;

      // Prepare data in column-major format for calculateFriedmanTest (subjects as rows, variables as columns)
      // However, calculateFriedmanTest expects rows as subjects and columns as conditions.
      // So, we need to transpose if selectedVariables represent different measures for the same subjects.

      // Assuming each row in activeDataset.data is a subject
      // And selectedVariables are the different measures (columns) for these subjects
      for (let i = 0; i < n; i++) {
        const subjectData: number[] = [];
        for (const varId of selectedVariables) {
          const column = activeDataset.columns.find(col => col.id === varId);
          if (!column) throw new Error(`Column with id ${varId} not found.`);
          const value = activeDataset.data[i][column.name];
          if (typeof value !== 'number' || isNaN(value)) {
            throw new Error(`Invalid data for subject ${i + 1}, variable ${column.name}. Expected a number.`);
          }
          subjectData.push(value);
        }
        dataForTest.push(subjectData);
      }
      
      const testResult = calculateFriedmanTest(dataForTest);
      setResults(testResult);
      console.log('Friedman test results:', testResult); // Log the results object

    } catch (err) {
      setError(`Error performing Friedman test: ${err instanceof Error ? err.message : String(err)}`);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (results) {
      // console.log('Results state updated:', results);
      // console.log('results.statistic:', results.statistic, 'typeof:', typeof results.statistic);
    }
  }, [results]);


  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Friedman Test (Non-parametric Repeated Measures)
      </Typography>

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Data Selection & Configuration
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>No datasets available</MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal" disabled={!activeDataset || numericColumns.length === 0}>
              <InputLabel id="variables-select-label">Repeated Measures Variables (≥3)</InputLabel>
              <Select
                labelId="variables-select-label"
                multiple
                value={selectedVariables}
                onChange={handleVariableChange}
                label="Repeated Measures Variables (≥3)"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {(selected as string[]).map((value) => {
                      const columnName = numericColumns.find(col => col.id === value)?.name || value;
                      return <Chip key={value} label={columnName} />;
                    })}
                  </Box>
                )}
              >
                {numericColumns.map(col => (
                  <MenuItem key={col.id} value={col.id}>
                    <Checkbox checked={selectedVariables.indexOf(col.id) > -1} />
                    <ListItemText primary={col.name} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AssessmentIcon />}
            onClick={runFriedmanTest}
            disabled={loading || !activeDataset || selectedVariables.length < 3}
          >
            Run Friedman Test
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {results && !loading && (
        <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Friedman Test Results
          </Typography>
          <TableContainer>
            <Table size="small">
              <TableBody>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Number of Observations (N)</TableCell>
                  <TableCell align="right">{results.n}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Number of Groups (k)</TableCell>
                  <TableCell align="right">{results.k}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Friedman Chi-Squared (χ²)</TableCell>
                  <TableCell align="right">
                    {results.statistic.toFixed(3)}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Degrees of Freedom (df)</TableCell>
                  <TableCell align="right">{results.df}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>P-value</TableCell>
                  <TableCell 
                    align="right" 
                    sx={{
                      color: (typeof results.pValue === 'number' && !isNaN(results.pValue) && results.pValue < 0.05) 
                              ? theme.palette.success.main 
                              : (typeof results.pValue === 'number' && !isNaN(results.pValue)) 
                                ? theme.palette.error.main 
                                : 'text.primary'
                    }}
                  >
                    {(typeof results.pValue === 'number' && !isNaN(results.pValue))
                      ? (results.pValue < 0.001 ? '< 0.001' : results.pValue.toFixed(3))
                      : 'undefined'}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
          <Box mt={2} p={2} sx={{ backgroundColor: theme.palette.action.hover, borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <InfoIcon sx={{ mr: 1, color: theme.palette.info.main }} /> Interpretation
            </Typography>
            <Typography variant="body2">
              {results.interpretation}
            </Typography>
            {(typeof results.pValue === 'number' && !isNaN(results.pValue) && results.pValue < 0.05) && (
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                Since the p-value is less than 0.05, we reject the null hypothesis. Consider performing post-hoc tests (e.g., Nemenyi test, Conover test) to identify which specific groups differ.
              </Typography>
            )}
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default FriedmanTest;
