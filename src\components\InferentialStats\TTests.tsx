import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControlLabel,
  Checkbox,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  TextField,
  Chip
} from '@mui/material';
import {
  Science as ScienceIcon,
  ShowChart as ShowChartIcon,
  SettingsBackupRestore as SettingsBackupRestoreIcon,
  Functions as FunctionsIcon,
  Info as InfoIcon,

  Calculate as CalculateIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType } from '../../types';
import StatsCard from '../UI/StatsCard';
import { extractNumericValues, extractNumericValuesEnhanced } from '../../utils/typeConversions';
import { getColumnMissingSummary, isMissingValue, extractCategoricalValuesWithMissingCodes } from '../../utils/missingDataUtils';
import {
  oneSampleTTest,
  independentSamplesTTest,
  pairedSamplesTTest,
  mannWhitneyUTest,
  wilcoxonSignedRankTest,
  comprehensiveNormalityTest,
  calculateMean,
  performLeveneTest,
  calculateMedian,
  calculateStandardDeviation
} from '@/utils/stats';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  ErrorBar,
  Cell
} from 'recharts';

// Types for t-test options
type TTestType = 'oneSample' | 'independentSamples' | 'pairedSamples';

// Props interface for TTests component
interface TTestsProps {
  initialTab?: string;
}

// Component for t-tests analysis
const TTests: React.FC<TTestsProps> = ({ initialTab }) => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();
  
  // State for test options
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [testType, setTestType] = useState<TTestType>(initialTab === 'independent' ? 'independentSamples' : 'oneSample');
  
  // Set initial test type based on initialTab prop
  React.useEffect(() => {
    if (initialTab === 'independent') {
      setTestType('independentSamples');
    }
  }, [initialTab]);
  const [selectedVariable1, setSelectedVariable1] = useState<string>('');
  const [selectedVariable2, setSelectedVariable2] = useState<string>('');
  const [selectedGroupingVariable, setSelectedGroupingVariable] = useState<string>('');
  const [testValue, setTestValue] = useState<number>(0);
  const [alternativeHypothesis, setAlternativeHypothesis] = useState<'twoSided' | 'less' | 'greater'>('twoSided');
  const [confidenceLevel, setConfidenceLevel] = useState<number>(0.95);
  const [useNonParametric, setUseNonParametric] = useState<boolean>(false);
  const [group1Value, setGroup1Value] = useState<string>('');
  const [group2Value, setGroup2Value] = useState<string>('');
  
  // State for assumption checks
  const [checkAssumptions, setCheckAssumptions] = useState<boolean>(true);
  
  // State for results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<any | null>(null);
  const [assumptionResults, setAssumptionResults] = useState<any | null>(null);
  

  
  // Load saved results from localStorage on component mount and when test type changes
  useEffect(() => {
    const savedTestResults = localStorage.getItem(`ttest_results_${testType}`);
    const savedAssumptionResults = localStorage.getItem(`ttest_assumptions_${testType}`);
    
    if (savedTestResults) {
      try {
        setTestResults(JSON.parse(savedTestResults));
      } catch (e) {
        console.error('Error parsing saved test results:', e);
      }
    }
    
    if (savedAssumptionResults) {
      try {
        setAssumptionResults(JSON.parse(savedAssumptionResults));
      } catch (e) {
        console.error('Error parsing saved assumption results:', e);
      }
    }
  }, [testType]);
  
  // Get numeric columns from current dataset
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];
  
  // Get categorical columns for grouping
  const categoricalColumns = currentDataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL || col.type === DataType.ORDINAL || col.type === DataType.BOOLEAN
  ) || [];
  
  // Get unique values for the selected grouping variable
  const groupingValues = (() => {
    if (!currentDataset || !selectedGroupingVariable) return [];

    const column = currentDataset.columns.find(col => col.id === selectedGroupingVariable);
    if (!column) return [];

    // Use enhanced categorical extraction to respect missing value codes
    const validValues = extractCategoricalValuesWithMissingCodes(currentDataset.data, column);
    const uniqueValues = [...new Set(validValues)];

    return uniqueValues;
  })();
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    resetSelections();
    
    // Update the current dataset in the DataContext
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };
  
  // Reset selections
  const resetSelections = () => {
    setSelectedVariable1('');
    setSelectedVariable2('');
    setSelectedGroupingVariable('');
    setTestValue(0);
    setGroup1Value('');
    setGroup2Value('');
    
    // Clear saved results for the current test type
    localStorage.removeItem(`ttest_results_${testType}`);
    localStorage.removeItem(`ttest_assumptions_${testType}`);
    
    setTestResults(null);
    setAssumptionResults(null);
  };
  
  // Handle test type change
  const handleTestTypeChange = (event: SelectChangeEvent<TTestType>) => {
    setTestType(event.target.value as TTestType);
    resetSelections();
  };
  
  // Handle variable selection changes
  const handleVariable1Change = (event: SelectChangeEvent<string>) => {
    setSelectedVariable1(event.target.value);
    setTestResults(null);
    setAssumptionResults(null);
  };
  
  const handleVariable2Change = (event: SelectChangeEvent<string>) => {
    setSelectedVariable2(event.target.value);
    setTestResults(null);
    setAssumptionResults(null);
  };
  
  const handleGroupingVariableChange = (event: SelectChangeEvent<string>) => {
    setSelectedGroupingVariable(event.target.value);
    setGroup1Value('');
    setGroup2Value('');
    setTestResults(null);
    setAssumptionResults(null);
  };
  
  // Handle group value changes
  const handleGroup1ValueChange = (event: SelectChangeEvent<string>) => {
    setGroup1Value(event.target.value);
    setTestResults(null);
    setAssumptionResults(null);
  };
  
  const handleGroup2ValueChange = (event: SelectChangeEvent<string>) => {
    setGroup2Value(event.target.value);
    setTestResults(null);
    setAssumptionResults(null);
  };
  
  // Check if form is valid
  const isFormValid = () => {
    if (!currentDataset) return false;
    
    switch (testType) {
      case 'oneSample':
        return !!selectedVariable1;
      case 'independentSamples':
        if (selectedGroupingVariable) {
          return !!selectedVariable1 && !!group1Value && !!group2Value && group1Value !== group2Value;
        } else {
          return !!selectedVariable1 && !!selectedVariable2;
        }
      case 'pairedSamples':
        return !!selectedVariable1 && !!selectedVariable2;
      default:
        return false;
    }
  };
  
  // Run tests
  const runTest = () => {
    if (!currentDataset || !isFormValid()) {
      setError('Please select all required variables for the selected test.');
      return;
    }
    
    setLoading(true);
    setError(null);
    setTestResults(null);
    setAssumptionResults(null);
    
    try {
      // Get column objects
      const variable1Column = currentDataset.columns.find(col => col.id === selectedVariable1);
      const variable2Column = selectedVariable2 
        ? currentDataset.columns.find(col => col.id === selectedVariable2)
        : null;
      const groupingColumn = selectedGroupingVariable
        ? currentDataset.columns.find(col => col.id === selectedGroupingVariable)
        : null;
      
      if (!variable1Column) {
        throw new Error('Selected variable not found in dataset.');
      }
      
      // Get data based on test type
      let group1Data: number[] = [];
      let group2Data: number[] = [];
      let groupNames: string[] = [];
      
      switch (testType) {
        case 'oneSample':
          // Get all numeric values for the variable using enhanced missing data handling
          group1Data = extractNumericValuesEnhanced(currentDataset.data, variable1Column);

          groupNames = [variable1Column.name];
          break;
        
        case 'independentSamples':
          if (selectedGroupingVariable && groupingColumn) {
            // Using a grouping variable to split the data
            if (!group1Value || !group2Value) {
              throw new Error('Please select both group values.');
            }
            
            // Filter data for each group using enhanced missing data handling
            const group1Rows = currentDataset.data
              .filter(row => String(row[groupingColumn.name]) === group1Value);
            group1Data = extractNumericValuesEnhanced(group1Rows.map(row => ({ [variable1Column.name]: row[variable1Column.name] })), variable1Column);

            const group2Rows = currentDataset.data
              .filter(row => String(row[groupingColumn.name]) === group2Value);
            group2Data = extractNumericValuesEnhanced(group2Rows.map(row => ({ [variable1Column.name]: row[variable1Column.name] })), variable1Column);
            
            groupNames = [`${variable1Column.name} (${group1Value})`, `${variable1Column.name} (${group2Value})`];
          } else if (variable2Column) {
            // Using two separate variables with enhanced missing data handling
            group1Data = extractNumericValuesEnhanced(currentDataset.data, variable1Column);
            group2Data = extractNumericValuesEnhanced(currentDataset.data, variable2Column);
            
            groupNames = [variable1Column.name, variable2Column.name];
          } else {
            throw new Error('Invalid variable selection for independent samples t-test.');
          }
          break;
        
        case 'pairedSamples':
          if (!variable2Column) {
            throw new Error('Please select both variables for paired samples t-test.');
          }
          
          // Get paired data using enhanced missing data handling
          const pairedData = currentDataset.data
            .map(row => ({
              value1: row[variable1Column.name],
              value2: row[variable2Column.name],
              row: row
            }))
            .filter(pair => {
              // Use enhanced missing value detection
              const value1Info = isMissingValue(pair.value1, variable1Column);
              const value2Info = isMissingValue(pair.value2, variable2Column);

              return !value1Info.isMissing && !value2Info.isMissing &&
                     typeof pair.value1 === 'number' && !isNaN(pair.value1) &&
                     typeof pair.value2 === 'number' && !isNaN(pair.value2);
            });

          group1Data = pairedData.map(pair => pair.value1) as number[];
          group2Data = pairedData.map(pair => pair.value2) as number[];
          
          groupNames = [variable1Column.name, variable2Column.name];
          break;
      }
      
      // Check if we have enough data
      if (group1Data.length === 0) {
        throw new Error('No valid numeric data found for the selected variable(s).');
      }
      
      if ((testType === 'independentSamples' || testType === 'pairedSamples') && group2Data.length === 0) {
        throw new Error('No valid numeric data found for the second variable/group.');
      }
      
      // Check assumptions if requested
      if (checkAssumptions) {
        const assumptions: any = { valid: true, details: {} };
        
        // Check normality
        const normalityTest1 = comprehensiveNormalityTest(group1Data, 0.05, ['auto']);
        assumptions.details.normality = [{
          group: groupNames[0],
          isNormal: normalityTest1.overallAssessment.isNormal,
          pValue: normalityTest1.tests.shapiroWilk?.pValue ||
                  normalityTest1.tests.kolmogorovSmirnov?.pValue ||
                  normalityTest1.tests.jarqueBera?.pValue || NaN
        }];

        if (testType !== 'oneSample') {
          const normalityTest2 = comprehensiveNormalityTest(group2Data, 0.05, ['auto']);
          assumptions.details.normality.push({
            group: groupNames[1],
            isNormal: normalityTest2.overallAssessment.isNormal,
            pValue: normalityTest2.tests.shapiroWilk?.pValue ||
                    normalityTest2.tests.kolmogorovSmirnov?.pValue ||
                    normalityTest2.tests.jarqueBera?.pValue || NaN
          });
          
          // Overall normality assumption
          assumptions.details.normalityValid = normalityTest1.overallAssessment.isNormal && normalityTest2.overallAssessment.isNormal;
          assumptions.valid = assumptions.valid && assumptions.details.normalityValid;
        } else {
          // For one-sample test
          assumptions.details.normalityValid = normalityTest1.overallAssessment.isNormal;
          assumptions.valid = assumptions.valid && assumptions.details.normalityValid;
        }
        
        // Check sample size
        assumptions.details.sampleSize = [{
          group: groupNames[0],
          size: group1Data.length,
          sufficient: group1Data.length >= 30
        }];
        
        if (testType !== 'oneSample') {
          assumptions.details.sampleSize.push({
            group: groupNames[1],
            size: group2Data.length,
            sufficient: group2Data.length >= 30
          });
        }
        
        // Sample size sufficiency
        const sampleSizeSufficient = assumptions.details.sampleSize.every((s: any) => s.sufficient);
        
        // For independent samples t-test, check for equal variances using Levene's test
        if (testType === 'independentSamples') {
          try {
            // Use Levene's test (default: median-based Brown-Forsythe variant)
            const leveneResult = performLeveneTest(group1Data, group2Data);

            assumptions.details.equalVariances = {
              levene: {
                statistic: leveneResult.statistic,
                pValue: leveneResult.pValue,
                df1: leveneResult.df ? leveneResult.df[0] : null,
                df2: leveneResult.df ? leveneResult.df[1] : null,
                rejected: leveneResult.rejected,
                alpha: leveneResult.alpha,
                method: leveneResult.method,
                variant: leveneResult.variant
              },
              isEqual: !leveneResult.rejected // Equal variances if null hypothesis is not rejected
            };
            
            assumptions.valid = assumptions.valid && assumptions.details.equalVariances.isEqual;
          } catch (error) {
            // Fallback to variance ratio if Levene's test fails
            const var1 = calculateStandardDeviation(group1Data) ** 2;
            const var2 = calculateStandardDeviation(group2Data) ** 2;
            const varianceRatio = Math.max(var1, var2) / Math.min(var1, var2);
            
            assumptions.details.equalVariances = {
              var1,
              var2,
              ratio: varianceRatio,
              isEqual: varianceRatio < 4, // Rule of thumb: variance ratio < 4 is acceptable
              error: error instanceof Error ? error.message : 'Error performing Levene\'s test'
            };
            
            assumptions.valid = assumptions.valid && assumptions.details.equalVariances.isEqual;
          }
        }
        
        // Overall assumption validity
        assumptions.valid = assumptions.valid || sampleSizeSufficient;
        
        // Set assumption results
        setAssumptionResults(assumptions);
        
        // Save assumption results to localStorage
        localStorage.setItem(`ttest_assumptions_${testType}`, JSON.stringify(assumptions));
      }
      
      // Run the appropriate test
      let results: any = {};
      
      if (testType === 'oneSample') {
        if (useNonParametric) {
          // One-sample Wilcoxon Signed Rank Test
          // For non-parametric, we compare to median rather than mean
          const wilcoxonResults = wilcoxonSignedRankTest(
            group1Data, 
            group1Data.map(() => testValue)
          );
          
          results = {
            test: 'Wilcoxon Signed Rank Test (One Sample)',
            statistic: wilcoxonResults.W,
            statName: 'W',
            pValue: wilcoxonResults.pValue,
            n: wilcoxonResults.n,
            effectSize: null, // No standard effect size for this test
            median: calculateMedian(group1Data),
            testValue,
            groups: [
              {
                name: groupNames[0],
                n: group1Data.length,
                mean: calculateMean(group1Data),
                median: calculateMedian(group1Data),
                sd: calculateStandardDeviation(group1Data)
              }
            ],
            confidenceInterval: null, // No CI for non-parametric
            alternative: alternativeHypothesis
          };
        } else {
          // One-sample t-test
          const tTestResults = oneSampleTTest(group1Data, testValue);
          
          // Calculate effect size (Cohen's d for one-sample)
          const effectSize = Math.abs(tTestResults.mean - testValue) / tTestResults.se;
          
          results = {
            test: 'One-Sample t-Test',
            statistic: tTestResults.t,
            statName: 't',
            df: tTestResults.df,
            pValue: tTestResults.pValue,
            effectSize,
            mean: tTestResults.mean,
            standardError: tTestResults.se,
            testValue,
            groups: [
              {
                name: groupNames[0],
                n: group1Data.length,
                mean: tTestResults.mean,
                median: calculateMedian(group1Data),
                sd: calculateStandardDeviation(group1Data)
              }
            ],
            confidenceInterval: tTestResults.ci95,
            alternative: alternativeHypothesis
          };
        }
      } else if (testType === 'independentSamples') {
        if (useNonParametric) {
          // Mann-Whitney U Test
          const mannWhitneyResults = mannWhitneyUTest(group1Data, group2Data);
          
          // Calculate effect size (r) for Mann-Whitney U
          const n = mannWhitneyResults.n1 + mannWhitneyResults.n2;
          const effectSize = Math.abs(mannWhitneyResults.z) / Math.sqrt(n);
          
          results = {
            test: 'Mann-Whitney U Test',
            statistic: mannWhitneyResults.U,
            statName: 'U',
            pValue: mannWhitneyResults.pValue,
            effectSize,
            groups: [
              {
                name: groupNames[0],
                n: mannWhitneyResults.n1,
                mean: calculateMean(group1Data),
                median: calculateMedian(group1Data),
                sd: calculateStandardDeviation(group1Data)
              },
              {
                name: groupNames[1],
                n: mannWhitneyResults.n2,
                mean: calculateMean(group2Data),
                median: calculateMedian(group2Data),
                sd: calculateStandardDeviation(group2Data)
              }
            ],
            confidenceInterval: null, // No CI for non-parametric
            alternative: alternativeHypothesis
          };
        } else {
          // Independent samples t-test
          const tTestResults = independentSamplesTTest(group1Data, group2Data);
          
          // Create homogeneity of variances test table if we have the Levene's test results
          let homogeneityTestsTable = null;
          if (assumptionResults?.details?.equalVariances?.levene && !assumptionResults.details.equalVariances.error) {
            const leveneResult = assumptionResults.details.equalVariances.levene;
            homogeneityTestsTable = {
              columns: ['Test', 'F', 'df1', 'df2', 'p-value', 'Equal Variances'],
              rows: [
                [
                  "Levene's Test", 
                  leveneResult.statistic, 
                  leveneResult.df1, 
                  leveneResult.df2, 
                  leveneResult.pValue,
                  !leveneResult.rejected ? 'Yes' : 'No'
                ]
              ]
            };
          }
          
          results = {
            test: 'Independent Samples t-Test',
            statistic: tTestResults.t,
            statName: 't',
            df: tTestResults.df,
            pValue: tTestResults.pValue,
            effectSize: tTestResults.cohensD,
            meanDifference: tTestResults.meanDifference,
            standardError: tTestResults.se,
            groups: [
              {
                name: groupNames[0],
                n: group1Data.length,
                mean: calculateMean(group1Data),
                median: calculateMedian(group1Data),
                sd: calculateStandardDeviation(group1Data)
              },
              {
                name: groupNames[1],
                n: group2Data.length,
                mean: calculateMean(group2Data),
                median: calculateMedian(group2Data),
                sd: calculateStandardDeviation(group2Data)
              }
            ],
            confidenceInterval: tTestResults.ci,
            alternative: alternativeHypothesis,
            homogeneityTestsTable: homogeneityTestsTable
          };
        }
      } else if (testType === 'pairedSamples') {
        if (useNonParametric) {
          // Wilcoxon Signed Rank Test for paired samples
          const wilcoxonResults = wilcoxonSignedRankTest(group1Data, group2Data);
          
          // Calculate effect size (r) for Wilcoxon test
          const effectSize = Math.abs(wilcoxonResults.z) / Math.sqrt(wilcoxonResults.n);
          
          results = {
            test: 'Wilcoxon Signed Rank Test (Paired)',
            statistic: wilcoxonResults.W,
            statName: 'W',
            pValue: wilcoxonResults.pValue,
            effectSize,
            groups: [
              {
                name: groupNames[0],
                n: group1Data.length,
                mean: calculateMean(group1Data),
                median: calculateMedian(group1Data),
                sd: calculateStandardDeviation(group1Data)
              },
              {
                name: groupNames[1],
                n: group2Data.length,
                mean: calculateMean(group2Data),
                median: calculateMedian(group2Data),
                sd: calculateStandardDeviation(group2Data)
              }
            ],
            pairs: group1Data.length,
            confidenceInterval: null, // No CI for non-parametric
            alternative: alternativeHypothesis
          };
        } else {
          // Paired samples t-test
          const tTestResults = pairedSamplesTTest(group1Data, group2Data);
          
          results = {
            test: 'Paired Samples t-Test',
            statistic: tTestResults.t,
            statName: 't',
            df: tTestResults.df,
            pValue: tTestResults.pValue,
            effectSize: tTestResults.cohensD,
            meanDifference: tTestResults.meanDifference,
            standardError: tTestResults.se,
            groups: [
              {
                name: groupNames[0],
                n: group1Data.length,
                mean: calculateMean(group1Data),
                median: calculateMedian(group1Data),
                sd: calculateStandardDeviation(group1Data)
              },
              {
                name: groupNames[1],
                n: group2Data.length,
                mean: calculateMean(group2Data),
                median: calculateMedian(group2Data),
                sd: calculateStandardDeviation(group2Data)
              }
            ],
            pairs: group1Data.length,
            confidenceInterval: tTestResults.ci95,
            alternative: alternativeHypothesis
          };
        }
      }
      
      // Prepare chart data
      if (testType === 'oneSample') {
        // For one-sample, we create a bar chart comparing the sample to the test value
        results.chartData = [
          {
            name: 'Sample',
            value: results.groups[0].mean,
            error: results.standardError ? results.standardError * 1.96 : null
          },
          {
            name: 'Test Value',
            value: testValue
          }
        ];
      } else {
        // For two-sample tests, we create a bar chart comparing the two groups
        results.chartData = results.groups.map((group: any) => ({
          name: group.name,
          value: group.mean,
          error: group.sd / Math.sqrt(group.n) * 1.96
        }));
      }
      
      // Get interpretation
      results.interpretation = getInterpretation(results);
      
      setTestResults(results);
      
      // Save test results to localStorage
      localStorage.setItem(`ttest_results_${testType}`, JSON.stringify(results));
      
      setLoading(false);
    } catch (err) {
      setError(`Error running test: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  };
  
  // Get test interpretation
  const getInterpretation = (results: any) => {
    if (!results) return '';
    
    const { test, pValue, statistic, df, effectSize, alternative } = results;
    
    let interpretation = '';
    
    // Determine significance based on p-value
    const isSignificant = pValue < 0.05;
    
    // Interpret based on test type and significance
    if (test.includes('One-Sample')) {
      const { mean, testValue } = results;
      const relation = mean > testValue ? 'greater than' : mean < testValue ? 'less than' : 'equal to';
      
      interpretation = `The ${test} was conducted to determine if the sample mean (${mean.toFixed(2)}) is significantly different from the test value (${testValue}).`;
      
      if (isSignificant) {
        interpretation += ` The results show a statistically significant difference (${results.statName} = ${statistic.toFixed(2)}, ${df ? `df = ${df}, ` : ''}p = ${pValue < 0.001 ? '< 0.001' : pValue.toFixed(3)}). The sample mean is ${relation} the test value.`;
      } else {
        interpretation += ` The results show no statistically significant difference (${results.statName} = ${statistic.toFixed(2)}, ${df ? `df = ${df}, ` : ''}p = ${pValue.toFixed(3)}). We cannot reject the null hypothesis that the population mean is equal to ${testValue}.`;
      }
    } else if (test.includes('Independent Samples')) {
      const { groups, meanDifference } = results;
      const absoluteDiff = Math.abs(meanDifference || (groups[0].mean - groups[1].mean));
      
      interpretation = `The ${test} was conducted to compare ${groups[0].name} (M = ${groups[0].mean.toFixed(2)}, SD = ${groups[0].sd.toFixed(2)}) and ${groups[1].name} (M = ${groups[1].mean.toFixed(2)}, SD = ${groups[1].sd.toFixed(2)}).`;
      
      if (isSignificant) {
        interpretation += ` The results show a statistically significant difference (${results.statName} = ${statistic.toFixed(2)}, ${df ? `df = ${df}, ` : ''}p = ${pValue < 0.001 ? '< 0.001' : pValue.toFixed(3)}).`;
      } else {
        interpretation += ` The results show no statistically significant difference (${results.statName} = ${statistic.toFixed(2)}, ${df ? `df = ${df}, ` : ''}p = ${pValue.toFixed(3)}).`;
      }
    } else if (test.includes('Paired')) {
      const { groups, meanDifference } = results;
      
      interpretation = `The ${test} was conducted to compare ${groups[0].name} (M = ${groups[0].mean.toFixed(2)}, SD = ${groups[0].sd.toFixed(2)}) and ${groups[1].name} (M = ${groups[1].mean.toFixed(2)}, SD = ${groups[1].sd.toFixed(2)}) in a within-subjects design with ${results.pairs} paired observations.`;
      
      if (isSignificant) {
        interpretation += ` The results show a statistically significant difference (${results.statName} = ${statistic.toFixed(2)}, ${df ? `df = ${df}, ` : ''}p = ${pValue < 0.001 ? '< 0.001' : pValue.toFixed(3)}).`;
      } else {
        interpretation += ` The results show no statistically significant difference (${results.statName} = ${statistic.toFixed(2)}, ${df ? `df = ${df}, ` : ''}p = ${pValue.toFixed(3)}).`;
      }
    }
    
    // Add effect size interpretation if available
    if (effectSize !== null && effectSize !== undefined) {
      let effectSizeMagnitude;
      
      if (test.includes('t-Test')) {
        // Cohen's d interpretation
        if (effectSize < 0.2) effectSizeMagnitude = 'very small';
        else if (effectSize < 0.5) effectSizeMagnitude = 'small';
        else if (effectSize < 0.8) effectSizeMagnitude = 'medium';
        else effectSizeMagnitude = 'large';
        
        interpretation += ` The effect size (Cohen's d = ${effectSize.toFixed(2)}) indicates a ${effectSizeMagnitude} effect.`;
      } else if (test.includes('Mann-Whitney') || test.includes('Wilcoxon')) {
        // r interpretation for non-parametric tests
        if (effectSize < 0.1) effectSizeMagnitude = 'very small';
        else if (effectSize < 0.3) effectSizeMagnitude = 'small';
        else if (effectSize < 0.5) effectSizeMagnitude = 'medium';
        else effectSizeMagnitude = 'large';
        
        interpretation += ` The effect size (r = ${effectSize.toFixed(2)}) indicates a ${effectSizeMagnitude} effect.`;
      }
    }
    
    // Add confidence interval interpretation if available
    const ci = results.confidenceInterval;
    if (Array.isArray(ci) && ci.length >= 2) {
      const lowerCI = ci[0];
      const upperCI = ci[1];

      if (typeof lowerCI === 'number' && !isNaN(lowerCI) && 
          typeof upperCI === 'number' && !isNaN(upperCI)) {
        interpretation += ` The 95% confidence interval of the difference is [${lowerCI.toFixed(2)}, ${upperCI.toFixed(2)}].`;
      
        const includesZero = lowerCI <= 0 && upperCI >= 0;
        if (includesZero) {
          interpretation += ` Since the confidence interval includes zero, this supports the non-significant result.`;
        } else {
          interpretation += ` Since the confidence interval does not include zero, this supports the significant result.`;
        }
      }
    }
    
    return interpretation;
  };
  
  // Interpret effect size
  const getEffectSizeInterpretation = (value: number, type: string) => {
    if (type.includes('Cohen')) {
      // Cohen's d
      if (value < 0.2) return 'Very Small';
      if (value < 0.5) return 'Small';
      if (value < 0.8) return 'Medium';
      return 'Large';
    } else {
      // r for non-parametric
      if (value < 0.1) return 'Very Small';
      if (value < 0.3) return 'Small';
      if (value < 0.5) return 'Medium';
      return 'Large';
    }
  };
  
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        T-Tests and Alternatives
      </Typography>
      
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Test Selection
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="test-type-label">Test Type</InputLabel>
              <Select
                labelId="test-type-label"
                id="test-type"
                value={testType}
                label="Test Type"
                onChange={handleTestTypeChange}
              >
                <MenuItem value="oneSample">
                  One-Sample T-Test
                  <Typography variant="caption" display="block" color="text.secondary">
                    Compare one variable to a hypothesized value
                  </Typography>
                </MenuItem>
                <MenuItem value="independentSamples">
                  Independent Samples T-Test
                  <Typography variant="caption" display="block" color="text.secondary">
                    Compare two unrelated groups
                  </Typography>
                </MenuItem>
                <MenuItem value="pairedSamples">
                  Paired Samples T-Test
                  <Typography variant="caption" display="block" color="text.secondary">
                    Compare two related measurements
                  </Typography>
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        
        <Box mt={2}>
          <FormControlLabel
            control={
              <Checkbox
                checked={useNonParametric}
                onChange={(e) => setUseNonParametric(e.target.checked)}
              />
            }
            label={
              <Box>
                Use non-parametric alternative
                <Typography variant="caption" display="block" color="text.secondary">
                  {testType === 'oneSample' && 'Wilcoxon Signed Rank Test (One Sample)'}
                  {testType === 'independentSamples' && 'Mann-Whitney U Test'}
                  {testType === 'pairedSamples' && 'Wilcoxon Signed Rank Test (Paired)'}
                </Typography>
              </Box>
            }
          />
        </Box>
      </Paper>
      
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Variable Selection
        </Typography>
        
        <Grid container spacing={2}>
          {/* One Sample Test */}
          {testType === 'oneSample' && (
            <>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="variable1-label">Variable</InputLabel>
                  <Select
                    labelId="variable1-label"
                    id="variable1"
                    value={selectedVariable1}
                    label="Variable"
                    onChange={handleVariable1Change}
                    disabled={numericColumns.length === 0}
                  >
                    {numericColumns.length === 0 ? (
                      <MenuItem value="" disabled>
                        No numeric variables available
                      </MenuItem>
                    ) : (
                      numericColumns.map(column => (
                        <MenuItem key={column.id} value={column.id}>
                          {column.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  label="Test Value (Hypothesized Mean)"
                  type="number"
                  value={testValue}
                  onChange={(e) => setTestValue(Number(e.target.value))}
                  fullWidth
                  margin="normal"
                />
              </Grid>
            </>
          )}
          
          {/* Independent Samples Test */}
          {testType === 'independentSamples' && (
            <>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="variable1-label">Variable</InputLabel>
                  <Select
                    labelId="variable1-label"
                    id="variable1"
                    value={selectedVariable1}
                    label="Variable"
                    onChange={handleVariable1Change}
                    disabled={numericColumns.length === 0}
                  >
                    {numericColumns.length === 0 ? (
                      <MenuItem value="" disabled>
                        No numeric variables available
                      </MenuItem>
                    ) : (
                      numericColumns.map(column => (
                        <MenuItem key={column.id} value={column.id}>
                          {column.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="grouping-label">Grouping Variable</InputLabel>
                  <Select
                    labelId="grouping-label"
                    id="grouping"
                    value={selectedGroupingVariable}
                    label="Grouping Variable"
                    onChange={handleGroupingVariableChange}
                    disabled={categoricalColumns.length === 0}
                  >
                    <MenuItem value="">
                      <em>None (use two separate variables)</em>
                    </MenuItem>
                    {categoricalColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              {selectedGroupingVariable ? (
                // If using a grouping variable, show group value selectors
                <>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel id="group1-label">Group 1</InputLabel>
                      <Select
                        labelId="group1-label"
                        id="group1"
                        value={group1Value}
                        label="Group 1"
                        onChange={handleGroup1ValueChange}
                        disabled={groupingValues.length === 0}
                      >
                        {groupingValues.map(value => (
                          <MenuItem key={value} value={value}>
                            {value}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel id="group2-label">Group 2</InputLabel>
                      <Select
                        labelId="group2-label"
                        id="group2"
                        value={group2Value}
                        label="Group 2"
                        onChange={handleGroup2ValueChange}
                        disabled={groupingValues.length === 0}
                      >
                        {groupingValues.map(value => (
                          <MenuItem key={value} value={value}>
                            {value}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                </>
              ) : (
                // If not using a grouping variable, show a second variable selector
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel id="variable2-label">Second Variable</InputLabel>
                    <Select
                      labelId="variable2-label"
                      id="variable2"
                      value={selectedVariable2}
                      label="Second Variable"
                      onChange={handleVariable2Change}
                      disabled={numericColumns.length === 0}
                    >
                      {numericColumns.length === 0 ? (
                        <MenuItem value="" disabled>
                          No numeric variables available
                        </MenuItem>
                      ) : (
                        numericColumns.map(column => (
                          <MenuItem key={column.id} value={column.id}>
                            {column.name}
                          </MenuItem>
                        ))
                      )}
                    </Select>
                  </FormControl>
                </Grid>
              )}
            </>
          )}
          
          {/* Paired Samples Test */}
          {testType === 'pairedSamples' && (
            <>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="variable1-label">First Variable</InputLabel>
                  <Select
                    labelId="variable1-label"
                    id="variable1"
                    value={selectedVariable1}
                    label="First Variable"
                    onChange={handleVariable1Change}
                    disabled={numericColumns.length === 0}
                  >
                    {numericColumns.length === 0 ? (
                      <MenuItem value="" disabled>
                        No numeric variables available
                      </MenuItem>
                    ) : (
                      numericColumns.map(column => (
                        <MenuItem key={column.id} value={column.id}>
                          {column.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="variable2-label">Second Variable</InputLabel>
                  <Select
                    labelId="variable2-label"
                    id="variable2"
                    value={selectedVariable2}
                    label="Second Variable"
                    onChange={handleVariable2Change}
                    disabled={numericColumns.length === 0}
                  >
                    {numericColumns.length === 0 ? (
                      <MenuItem value="" disabled>
                        No numeric variables available
                      </MenuItem>
                    ) : (
                      numericColumns.map(column => (
                        <MenuItem key={column.id} value={column.id}>
                          {column.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Grid>
            </>
          )}
        </Grid>
        
        <Box mt={2}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="alternative-label">Alternative Hypothesis</InputLabel>
                <Select
                  labelId="alternative-label"
                  id="alternative"
                  value={alternativeHypothesis}
                  label="Alternative Hypothesis"
                  onChange={(e) => setAlternativeHypothesis(e.target.value as 'twoSided' | 'less' | 'greater')}
                >
                  <MenuItem value="twoSided">Two-Sided (≠)</MenuItem>
                  <MenuItem value="less">One-Sided (&lt;)</MenuItem>
                  <MenuItem value="greater">One-Sided (&gt;)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="confidence-label">Confidence Level</InputLabel>
                <Select
                  labelId="confidence-label"
                  id="confidence"
                  value={confidenceLevel}
                  label="Confidence Level"
                  onChange={(e) => setConfidenceLevel(Number(e.target.value))}
                >
                  <MenuItem value={0.90}>90%</MenuItem>
                  <MenuItem value={0.95}>95%</MenuItem>
                  <MenuItem value={0.99}>99%</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>
        
        <Box mt={3}>
          <Typography variant="subtitle2" gutterBottom>
            Options
          </Typography>
          
          <FormControlLabel
            control={
              <Checkbox
                checked={checkAssumptions}
                onChange={(e) => setCheckAssumptions(e.target.checked)}
              />
            }
            label="Check test assumptions"
          />
        </Box>
        
        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<ScienceIcon />}
            onClick={runTest}
            disabled={loading || !isFormValid()}
          >
            Run {useNonParametric ? 'Non-parametric Test' : 'T-Test'}
          </Button>
        </Box>
      </Paper>
      
      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {assumptionResults && !loading && (
        <Paper elevation={2} sx={{ p: 3, mb: 3, bgcolor: 'background.paper' }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
            <SettingsBackupRestoreIcon color="primary" />
            Assumption Checks
          </Typography>

          <Grid container spacing={3}>
            {/* Normality Assumption */}
            <Grid item xs={12} lg={6}>
              <Paper elevation={0} variant="outlined" sx={{ p: 2, height: 'fit-content' }}>
                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <ShowChartIcon fontSize="small" />
                  Normality Assumption
                </Typography>

                <TableContainer>
                  <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                    <TableHead>
                      <TableRow>
                        <TableCell>Group</TableCell>
                        <TableCell align="right">p-value</TableCell>
                        <TableCell align="right">Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {assumptionResults.details.normality.map((result: any, index: number) => (
                        <TableRow key={index} sx={{ '&:nth-of-type(odd)': { bgcolor: 'grey.25' } }}>
                          <TableCell sx={{ fontWeight: 500 }}>{result.group}</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            {result.pValue.toFixed(4)}
                          </TableCell>
                          <TableCell align="right">
                            <Chip
                              label={result.isNormal ? 'Normal' : 'Non-normal'}
                              color={result.isNormal ? 'success' : 'error'}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                <Alert
                  severity={assumptionResults.details.normalityValid ? 'success' : 'warning'}
                  sx={{ mt: 2 }}
                >
                  <Typography variant="body2">
                    {assumptionResults.details.normalityValid
                      ? 'Normality assumption is met for all groups.'
                      : `Normality assumption is violated. Consider using the non-parametric alternative: ${
                          testType === 'oneSample'
                            ? 'Wilcoxon Signed Rank Test'
                            : testType === 'independentSamples'
                              ? 'Mann-Whitney U Test'
                              : 'Wilcoxon Signed Rank Test for paired samples'
                        }.`}
                  </Typography>
                </Alert>
              </Paper>
            </Grid>

            {/* Sample Size Check */}
            <Grid item xs={12} lg={6}>
              <Paper elevation={0} variant="outlined" sx={{ p: 2, height: 'fit-content' }}>
                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <PeopleIcon fontSize="small" />
                  Sample Size Check
                </Typography>

                <TableContainer>
                  <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                    <TableHead>
                      <TableRow>
                        <TableCell>Group</TableCell>
                        <TableCell align="right">Size</TableCell>
                        <TableCell align="right">Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {assumptionResults.details.sampleSize.map((result: any, index: number) => (
                        <TableRow key={index} sx={{ '&:nth-of-type(odd)': { bgcolor: 'grey.25' } }}>
                          <TableCell sx={{ fontWeight: 500 }}>{result.group}</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            {result.size}
                          </TableCell>
                          <TableCell align="right">
                            <Chip
                              label={result.sufficient ? 'Sufficient' : 'Small'}
                              color={result.sufficient ? 'success' : 'warning'}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                <Alert
                  severity={assumptionResults.details.sampleSize.every((s: any) => s.sufficient) ? 'success' : 'info'}
                  sx={{ mt: 2 }}
                >
                  <Typography variant="body2">
                    {assumptionResults.details.sampleSize.every((s: any) => s.sufficient)
                      ? 'Sample sizes are sufficient (n ≥ 30) for the t-test to be robust against normality violations.'
                      : 'Small sample size detected (n < 30). The t-test requires normality assumption to be met with small samples.'}
                  </Typography>
                </Alert>
              </Paper>
            </Grid>
            
            {/* Equal Variances Assumption for Independent Samples */}
            {testType === 'independentSamples' && assumptionResults.details.equalVariances && (
              <Grid item xs={12}>
                <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <TimelineIcon fontSize="small" />
                    Homogeneity of Variances (Levene's Test)
                  </Typography>
                  <Typography variant="caption" display="block" gutterBottom sx={{ mb: 2, color: 'text.secondary' }}>
                    For Dependent Variable: {numericColumns.find(col => col.id === selectedVariable1)?.name || 'N/A'} (across groups of {categoricalColumns.find(col => col.id === selectedGroupingVariable)?.name || 'N/A'})
                  </Typography>

                  {assumptionResults.details.equalVariances.levene ? (
                    <>
                      <TableContainer>
                        <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                          <TableHead>
                            <TableRow>
                              <TableCell>Test</TableCell>
                              <TableCell align="right">F</TableCell>
                              <TableCell align="right">df1</TableCell>
                              <TableCell align="right">df2</TableCell>
                              <TableCell align="right">p-value</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell sx={{ fontWeight: 500 }}>Levene's Test</TableCell>
                              <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                {assumptionResults.details.equalVariances.levene.statistic.toFixed(3)}
                              </TableCell>
                              <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                {assumptionResults.details.equalVariances.levene.df1}
                              </TableCell>
                              <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                {assumptionResults.details.equalVariances.levene.df2}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  color: assumptionResults.details.equalVariances.levene.pValue < (1 - confidenceLevel) ? 'error.main' : 'success.main',
                                  fontFamily: 'monospace',
                                  fontWeight: 500
                                }}
                              >
                                {assumptionResults.details.equalVariances.levene.pValue < 0.001
                                  ? '< 0.001'
                                  : assumptionResults.details.equalVariances.levene.pValue.toFixed(3)}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                      <Alert
                        severity={assumptionResults.details.equalVariances.levene.pValue >= (1 - confidenceLevel) ? "success" : "warning"}
                        sx={{ mt: 2 }}
                      >
                        <Typography variant="body2">
                          Levene's test for homogeneity of variances (α = {(1 - confidenceLevel).toFixed(2)}): F({assumptionResults.details.equalVariances.levene.df1}, {assumptionResults.details.equalVariances.levene.df2}) = {assumptionResults.details.equalVariances.levene.statistic.toFixed(3)}, p = {assumptionResults.details.equalVariances.levene.pValue < 0.001 ? '< 0.001' : assumptionResults.details.equalVariances.levene.pValue.toFixed(3)}.
                          Assumption of equal variances is <strong>{assumptionResults.details.equalVariances.levene.pValue >= (1 - confidenceLevel) ? 'met' : 'not met'}</strong>.
                        </Typography>
                      </Alert>
                    </>
                  ) : (
                    // Fallback for variance ratio
                    <>
                      <TableContainer>
                        <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                          <TableHead>
                            <TableRow>
                              <TableCell>Measure</TableCell>
                              <TableCell align="right">Value</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell sx={{ fontWeight: 500 }}>Variance Ratio</TableCell>
                              <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                {typeof assumptionResults.details.equalVariances.ratio === 'number'
                                  ? assumptionResults.details.equalVariances.ratio.toFixed(4)
                                  : 'N/A'}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell sx={{ fontWeight: 500 }}>Status</TableCell>
                              <TableCell align="right">
                                <Chip
                                  label={assumptionResults.details.equalVariances.isEqual ? 'Approximately Equal' : 'Unequal'}
                                  color={assumptionResults.details.equalVariances.isEqual ? 'success' : 'error'}
                                  size="small"
                                  variant="outlined"
                                />
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                      <Alert severity={assumptionResults.details.equalVariances.isEqual ? 'success' : 'warning'} sx={{ mt: 2 }}>
                        <Typography variant="body2">
                          Variance ratio test: The variances are {assumptionResults.details.equalVariances.isEqual ? 'approximately equal' : 'unequal'} (ratio &lt; 4 rule of thumb).
                        </Typography>
                      </Alert>
                    </>
                  )}
                </Paper>
              </Grid>
            )}

            {/* Overall Assumption Summary */}
            <Grid item xs={12}>
              <Paper elevation={0} variant="outlined" sx={{ p: 2, bgcolor: assumptionResults.valid ? 'success.50' : 'warning.50' }}>
                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <InfoIcon fontSize="small" />
                  Overall Assessment
                </Typography>
                <Alert severity={assumptionResults.valid ? 'success' : 'warning'} sx={{ mt: 1 }}>
                  <Typography variant="body2">
                    {assumptionResults.valid
                      ? 'All assumptions required for the selected test are adequately met.'
                      : `Some assumptions are violated. The ${useNonParametric ? 'non-parametric test' : 't-test'} may not be appropriate. ${useNonParametric ? '' : 'Consider using a non-parametric alternative.'}`}
                  </Typography>
                </Alert>
              </Paper>
            </Grid>
          </Grid>
        </Paper>
      )}
      
      {testResults && !loading && (
        <Paper elevation={2} sx={{ p: 3, mb: 3, bgcolor: 'background.paper' }}>
          <Box display="flex" justifyContent="flex-start" alignItems="center" mb={3}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ScienceIcon color="primary" />
              {testResults.test} Results
            </Typography>
          </Box>

          {/* Key Statistics Overview */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <CalculateIcon fontSize="small" />
              Key Statistics
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title={`${testResults.statName} Statistic`}
                  value={testResults.statistic.toFixed(4)}
                  description={`Test statistic value`}
                  color="primary"
                  variant="outlined"
                  icon={<CalculateIcon />}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                  title="p-value"
                  value={testResults.pValue < 0.001 ? '< 0.001' : testResults.pValue.toFixed(4)}
                  description={testResults.pValue < 0.05 ? 'Statistically significant' : 'Not significant'}
                  color={testResults.pValue < 0.05 ? 'success' : 'warning'}
                  variant="outlined"
                  icon={<ShowChartIcon />}
                />
              </Grid>
              {testResults.df !== undefined && (
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    title="Degrees of Freedom"
                    value={testResults.df}
                    description="Statistical degrees of freedom"
                    color="info"
                    variant="outlined"
                    icon={<FunctionsIcon />}
                  />
                </Grid>
              )}
              {testResults.effectSize !== null && testResults.effectSize !== undefined && (
                <Grid item xs={12} sm={6} md={3}>
                  <StatsCard
                    title={`Effect Size ${testResults.test.includes('t-Test') ? '(Cohen\'s d)' : '(r)'}`}
                    value={testResults.effectSize.toFixed(4)}
                    description={getEffectSizeInterpretation(testResults.effectSize, testResults.test.includes('t-Test') ? 'Cohen\'s d' : 'r')}
                    color="secondary"
                    variant="outlined"
                    icon={<TrendingUpIcon />}
                  />
                </Grid>
              )}
            </Grid>
          </Box>

          {/* Detailed Results Tables */}
          <Grid container spacing={3}>
            {/* Test Statistics Table */}
            <Grid item xs={12} lg={6}>
              <Paper elevation={0} variant="outlined" sx={{ p: 2, height: 'fit-content' }}>
                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <InfoIcon fontSize="small" />
                  Test Statistics
                </Typography>
                <TableContainer>
                  <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                    <TableHead>
                      <TableRow>
                        <TableCell>Statistic</TableCell>
                        <TableCell align="right">Value</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 500 }}>{testResults.statName} Value</TableCell>
                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                          {testResults.statistic.toFixed(4)}
                        </TableCell>
                      </TableRow>
                      {testResults.df !== undefined && (
                        <TableRow>
                          <TableCell sx={{ fontWeight: 500 }}>Degrees of Freedom</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            {testResults.df}
                          </TableCell>
                        </TableRow>
                      )}
                      <TableRow>
                        <TableCell sx={{ fontWeight: 500 }}>p-value</TableCell>
                        <TableCell
                          align="right"
                          sx={{
                            color: testResults.pValue < 0.05 ? 'success.main' : 'text.primary',
                            fontWeight: testResults.pValue < 0.05 ? 600 : 400,
                            fontFamily: 'monospace'
                          }}
                        >
                          {testResults.pValue < 0.001 ? '< 0.001' : testResults.pValue.toFixed(4)}
                        </TableCell>
                      </TableRow>
                      {testResults.meanDifference !== undefined && (
                        <TableRow>
                          <TableCell sx={{ fontWeight: 500 }}>Mean Difference</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            {testResults.meanDifference.toFixed(4)}
                          </TableCell>
                        </TableRow>
                      )}
                      {testResults.standardError !== undefined && (
                        <TableRow>
                          <TableCell sx={{ fontWeight: 500 }}>Standard Error</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            {testResults.standardError.toFixed(4)}
                          </TableCell>
                        </TableRow>
                      )}
                      {Array.isArray(testResults.confidenceInterval) &&
                       testResults.confidenceInterval.length >= 2 &&
                       typeof testResults.confidenceInterval[0] === 'number' && !isNaN(testResults.confidenceInterval[0]) &&
                       typeof testResults.confidenceInterval[1] === 'number' && !isNaN(testResults.confidenceInterval[1]) &&
                       (
                        <TableRow>
                          <TableCell sx={{ fontWeight: 500 }}>95% Confidence Interval</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            [{testResults.confidenceInterval[0].toFixed(4)}, {testResults.confidenceInterval[1].toFixed(4)}]
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Grid>

            {/* Descriptive Statistics and Visualization */}
            <Grid item xs={12} lg={6}>
              <Paper elevation={0} variant="outlined" sx={{ p: 2, height: 'fit-content' }}>
                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <PeopleIcon fontSize="small" />
                  Descriptive Statistics
                </Typography>

                <TableContainer sx={{ mb: 2 }}>
                  <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                    <TableHead>
                      <TableRow>
                        <TableCell>Group</TableCell>
                        <TableCell align="right">N</TableCell>
                        <TableCell align="right">Mean</TableCell>
                        <TableCell align="right">{useNonParametric ? 'Median' : 'SD'}</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {testResults.groups.map((group: any, index: number) => (
                        <TableRow key={index} sx={{ '&:nth-of-type(odd)': { bgcolor: 'grey.25' } }}>
                          <TableCell sx={{ fontWeight: 500 }}>{group.name}</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>{group.n}</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>{group.mean.toFixed(4)}</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                            {useNonParametric ? group.median.toFixed(4) : group.sd.toFixed(4)}
                          </TableCell>
                        </TableRow>
                      ))}
                      {testType === 'oneSample' && (
                        <TableRow sx={{ bgcolor: 'primary.50', '& .MuiTableCell-root': { fontWeight: 500 } }}>
                          <TableCell>Test Value</TableCell>
                          <TableCell align="right">-</TableCell>
                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>{testResults.testValue}</TableCell>
                          <TableCell align="right">-</TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Visualization */}
                <Box sx={{ height: 220, mt: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                    Group Comparison Chart
                  </Typography>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={testResults.chartData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 40,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.grey[300]} />
                      <XAxis
                        dataKey="name"
                        tick={{ fontSize: 12 }}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <YAxis tick={{ fontSize: 12 }} />
                      <RechartsTooltip
                        contentStyle={{
                          backgroundColor: theme.palette.background.paper,
                          border: `1px solid ${theme.palette.grey[300]}`,
                          borderRadius: 4
                        }}
                      />
                      <Bar
                        dataKey="value"
                        name={useNonParametric ? "Median" : "Mean"}
                        radius={[4, 4, 0, 0]}
                      >
                        {testResults.chartData.map((_: any, index: number) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={index % 2 === 0
                              ? theme.palette.primary.main
                              : theme.palette.secondary.main
                            }
                          />
                        ))}
                        {!useNonParametric && (
                          <ErrorBar
                            dataKey="error"
                            width={4}
                            strokeWidth={2}
                            stroke={theme.palette.error.main}
                          />
                        )}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </Paper>
            </Grid>
          </Grid>

          {/* Additional Statistics for Independent Samples */}
          {testType === 'independentSamples' && testResults.homogeneityTestsTable && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <TimelineIcon fontSize="small" />
                Homogeneity of Variances Test
              </Typography>
              <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                <TableContainer>
                  <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                    <TableHead>
                      <TableRow>
                        {testResults.homogeneityTestsTable.columns.map((col: string, index: number) => (
                          <TableCell key={index} align={index === 0 ? 'left' : 'right'}>
                            {col}
                          </TableCell>
                        ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {testResults.homogeneityTestsTable.rows.map((row: any[], rowIndex: number) => (
                        <TableRow key={rowIndex}>
                          {row.map((cell: any, cellIndex: number) => (
                            <TableCell
                              key={cellIndex}
                              align={cellIndex === 0 ? 'left' : 'right'}
                              sx={{
                                fontFamily: cellIndex > 0 ? 'monospace' : 'inherit',
                                fontWeight: cellIndex === 0 ? 500 : 400
                              }}
                            >
                              {typeof cell === 'number' ? cell.toFixed(4) : cell}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Box>
          )}

          {/* Enhanced Interpretation Section */}
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <InfoIcon fontSize="small" />
              Statistical Interpretation
            </Typography>
            <Paper elevation={0} variant="outlined" sx={{ p: 3, bgcolor: 'grey.50' }}>
              {/* Summary */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom color="primary">
                  Summary
                </Typography>
                <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                  {testResults.interpretation}
                </Typography>
              </Box>

              {/* Statistical Significance */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom color="primary">
                  Statistical Significance
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Chip
                    label={testResults.pValue < 0.05 ? 'Significant' : 'Not Significant'}
                    color={testResults.pValue < 0.05 ? 'success' : 'default'}
                    size="small"
                  />
                  <Typography variant="body2" color="text.secondary">
                    (α = 0.05)
                  </Typography>
                </Box>
                <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                  {testResults.pValue < 0.05
                    ? `The p-value (${testResults.pValue < 0.001 ? '< 0.001' : testResults.pValue.toFixed(4)}) is less than the significance level of 0.05, indicating a statistically significant result.`
                    : `The p-value (${testResults.pValue.toFixed(4)}) is greater than the significance level of 0.05, indicating no statistically significant difference.`
                  }
                </Typography>
              </Box>

              {/* Effect Size Interpretation */}
              {testResults.effectSize !== null && testResults.effectSize !== undefined && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom color="primary">
                    Effect Size
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Chip
                      label={getEffectSizeInterpretation(testResults.effectSize, testResults.test.includes('t-Test') ? 'Cohen\'s d' : 'r')}
                      color="secondary"
                      size="small"
                    />
                    <Typography variant="body2" color="text.secondary">
                      ({testResults.test.includes('t-Test') ? 'Cohen\'s d' : 'r'} = {testResults.effectSize.toFixed(4)})
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                    The effect size indicates a {getEffectSizeInterpretation(testResults.effectSize, testResults.test.includes('t-Test') ? 'Cohen\'s d' : 'r').toLowerCase()} practical significance of the observed difference.
                  </Typography>
                </Box>
              )}

              {/* Confidence Interval */}
              {Array.isArray(testResults.confidenceInterval) &&
               testResults.confidenceInterval.length >= 2 &&
               typeof testResults.confidenceInterval[0] === 'number' && !isNaN(testResults.confidenceInterval[0]) &&
               typeof testResults.confidenceInterval[1] === 'number' && !isNaN(testResults.confidenceInterval[1]) && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="primary">
                    Confidence Interval
                  </Typography>
                  <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                    The 95% confidence interval [{testResults.confidenceInterval[0].toFixed(4)}, {testResults.confidenceInterval[1].toFixed(4)}]
                    {testResults.confidenceInterval[0] <= 0 && testResults.confidenceInterval[1] >= 0
                      ? ' includes zero, which supports the non-significant result.'
                      : ' does not include zero, which supports the significant result.'
                    }
                  </Typography>
                </Box>
              )}
            </Paper>
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default TTests;
