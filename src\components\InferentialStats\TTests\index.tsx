import React, { useState } from 'react';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Divider,
  alpha,
  useTheme,
  Chip
} from '@mui/material';
import {
  RadioButtonChecked as RadioButtonCheckedIcon,
  Compare as CompareIcon,
  RepeatOne as RepeatOneIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { TabPanel } from '../../UI';
import OneSampleTTest from './OneSampleTTest';
import IndependentTTest from './IndependentTTest';
import PairedTTest from './PairedTTest';

interface TTestsProps {
  initialTab?: string;
}

const TTests: React.FC<TTestsProps> = ({ initialTab = 'independent' }) => {
  const theme = useTheme();
  
  // Map tab names to indices
  const tabNameToIndex: Record<string, number> = {
    'one': 0,
    'independent': 1,
    'paired': 2
  };
  
  const [activeTab, setActiveTab] = useState<number>(
    initialTab && tabNameToIndex[initialTab] !== undefined 
      ? tabNameToIndex[initialTab] 
      : 1
  );

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ width: '100%', minHeight: '800px' }}>
      <Tabs
        value={activeTab}
        onChange={handleChange}
        aria-label="t-test types"
        variant="fullWidth"
        sx={{ 
          mb: 3,
          borderBottom: 1,
          borderColor: 'divider',
          '& .MuiTab-root': {
            minHeight: 64,
            textTransform: 'none',
          }
        }}
      >
        <Tab 
          label={
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="subtitle2" fontWeight="medium">One Sample</Typography>
              <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                Compare to a known value
              </Typography>
            </Box>
          } 
          icon={<RadioButtonCheckedIcon />}
          iconPosition="start"
        />
        <Tab 
          label={
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="subtitle2" fontWeight="medium">Independent Samples</Typography>
              <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                Compare two groups
              </Typography>
            </Box>
          } 
          icon={<CompareIcon />}
          iconPosition="start"
        />
        <Tab 
          label={
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="subtitle2" fontWeight="medium">Paired Samples</Typography>
              <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                Compare paired measurements
              </Typography>
            </Box>
          } 
          icon={<RepeatOneIcon />}
          iconPosition="start"
        />
      </Tabs>
      
      <Box 
        sx={{ 
          p: 2, 
          mb: 3, 
          borderRadius: 2,
          backgroundColor: alpha(theme.palette.primary.main, 0.04),
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <InfoIcon color="primary" sx={{ mr: 2 }} />
        <Box>
          <Typography variant="subtitle2" color="primary">
            About t-Tests
          </Typography>
          <Typography variant="body2" color="text.secondary">
            T-tests are used to determine if there is a significant difference between means of groups.
            Select the appropriate test based on your study design and data structure.
          </Typography>
        </Box>
      </Box>
      
      <TabPanel value={activeTab} index={0}>
        <Box sx={{ position: 'relative', minHeight: '600px' }}>
          <Chip
            label="Coming Soon"
            color="secondary"
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              zIndex: 10
            }}
          />
          <Box sx={{ 
            opacity: 0.6, 
            pointerEvents: 'none' 
          }}>
            {/* Placeholder for One Sample t-test */}
            <Typography variant="h6">One Sample t-Test</Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Compare the mean of a single sample to a known or hypothesized population mean.
            </Typography>
            <Box sx={{ height: 500 }} />
          </Box>
        </Box>
      </TabPanel>
      
      <TabPanel value={activeTab} index={1}>
        <IndependentTTest />
      </TabPanel>
      
      <TabPanel value={activeTab} index={2}>
        <Box sx={{ position: 'relative', minHeight: '600px' }}>
          <Chip
            label="Coming Soon"
            color="secondary"
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              zIndex: 10
            }}
          />
          <Box sx={{ 
            opacity: 0.6, 
            pointerEvents: 'none' 
          }}>
            {/* Placeholder for Paired t-test */}
            <Typography variant="h6">Paired Samples t-Test</Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Compare the means of two related groups or repeated measurements on the same subjects.
            </Typography>
            <Box sx={{ height: 500 }} />
          </Box>
        </Box>
      </TabPanel>
    </Box>
  );
};

export default TTests;