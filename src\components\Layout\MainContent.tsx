import React from 'react';
import { Box, Toolbar, Typography, Paper, useMediaQuery, useTheme } from '@mui/material';

export interface MainContentProps {
  children?: React.ReactNode;
  drawerWidth: number;
  open: boolean;
}

const MainContent: React.FC<MainContentProps> = ({ 
  children, 
  drawerWidth,
  open
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Box
      component="main"
       sx={{
         flexGrow: 1,
         p: { xs: 1, sm: 2, md: 3 }, // Responsive padding
         width: { 
           xs: '100%', 
           sm: open ? `calc(100% - ${drawerWidth}px)` : '100%'
         },
         ml: { 
           xs: 0,
           sm: open ? `${drawerWidth}px` : 0
         },
         transition: theme => theme.transitions.create(['margin', 'width'], { 
           easing: theme.transitions.easing.sharp,
           duration: open ? theme.transitions.duration.enteringScreen : theme.transitions.duration.leavingScreen,
         }),
         display: 'flex', // Add flex display
         flexDirection: 'column', // Stack children vertically
         height: '100vh', // Full viewport height
         overflow: 'hidden', // Prevent content from causing scrollbars
         maxWidth: '100%', // Ensure content doesn't exceed viewport width
         position: 'absolute', // Position absolutely to eliminate any gaps
         left: 0, // Align to the left edge
         top: 0, // Align to the top
      }}
    >
      <Toolbar />
      <Box sx={{ 
        flex: 1, // Grow to fill available space
        overflow: 'auto', // Add scrolling to content
        width: '100%', // Use full width
        maxWidth: '100%', // Ensure content doesn't exceed container
        px: { xs: 0, sm: 1 }, // Add horizontal padding on larger screens
        pb: 2, // Add bottom padding to ensure content isn't cut off
      }}>
        {children ? children : (
          <Paper
            elevation={0}
            sx={{
              p: { xs: 2, sm: 3, md: 4 }, // Responsive padding
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: { 
                xs: 'calc(100vh - 80px)',
                sm: 'calc(100vh - 120px)' 
              },
              textAlign: 'center',
              backgroundColor: 'rgba(25, 118, 210, 0.05)',
              borderRadius: { xs: 2, sm: 4 }, // Responsive border radius
              mx: { xs: 1, sm: 2 }, // Add horizontal margin
            }}
          >
            <Typography 
              variant={isMobile ? "h4" : "h3"} 
              color="primary" 
              gutterBottom
              sx={{ fontSize: { xs: '1.75rem', sm: '2.25rem', md: '3rem' } }} // Responsive font size
            >
              Welcome to DataStatPro
            </Typography>
            <Typography 
              variant={isMobile ? "h6" : "h5"} 
              color="text.secondary" 
              paragraph
              sx={{ fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.5rem' } }} // Responsive font size
            >
              Advanced Statistical Analysis Web Application
            </Typography>
            <Typography 
              variant="body1" 
              color="text.secondary" 
              paragraph 
              sx={{ 
                maxWidth: { xs: '100%', sm: 600, md: 800 },
                px: { xs: 2, sm: 3, md: 0 } // Add padding on smaller screens
              }}
            >
              DataStatPro provides powerful tools for data management, statistical analysis, 
              and visualization. Use the sidebar menu to navigate through different features
              or start by importing your data.
            </Typography>
          </Paper>
        )}
      </Box>
    </Box>
  );
};

export default MainContent;
