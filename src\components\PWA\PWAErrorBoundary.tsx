import React, { Component, ErrorInfo, ReactNode } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Alert, 
  CircularProgress,
  Stack,
  Paper
} from '@mui/material';
import { 
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Build as BuildIcon
} from '@mui/icons-material';
import { performPWARecovery, emergencyPWARecovery } from '../../utils/pwaRecovery';
import { firefoxAuthHandler } from '../../utils/firefoxAuthHandler';
import { isFirefox } from '../../utils/compatibilityChecker';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  isRecovering: boolean;
  recoveryAttempts: number;
  recoveryMessage: string;
  isFirefoxError?: boolean;
}

class PWAErrorBoundary extends Component<Props, State> {
  private maxRecoveryAttempts = 2;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isRecovering: false,
      recoveryAttempts: 0,
      recoveryMessage: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('PWA Error Boundary caught an error:', error, errorInfo);

    // Handle Firefox-specific errors first
    if (isFirefox()) {
      console.log('🦊 Firefox error detected, applying Firefox-specific handling...');

      const firefoxErrorResult = firefoxAuthHandler.handleAuthenticationError(error);
      if (firefoxErrorResult.handled) {
        console.log('🦊 Firefox error handled, attempting clean recovery...');

        // For Firefox, attempt immediate clean recovery
        setTimeout(() => {
          firefoxAuthHandler.forceCleanReload();
        }, 2000);

        this.setState({
          error: new Error(firefoxErrorResult.message || 'Firefox compatibility issue detected'),
          errorInfo,
          isFirefoxError: true
        });
        return;
      }
    }

    this.setState({
      error,
      errorInfo
    });

    // Track the error for cache corruption detection
    const failures = parseInt(localStorage.getItem('datastatpro-loading-failures') || '0') + 1;
    localStorage.setItem('datastatpro-loading-failures', failures.toString());

    if (failures > 2) {
      localStorage.setItem('datastatpro-cache-corruption-detected', 'true');
    }

    // Attempt automatic recovery for certain types of errors
    if (this.shouldAttemptAutoRecovery(error)) {
      setTimeout(() => {
        this.handleAutoRecovery();
      }, 1000);
    }
  }

  private shouldAttemptAutoRecovery(error: Error): boolean {
    const autoRecoveryPatterns = [
      /loading chunk \d+ failed/i,
      /failed to fetch/i,
      /networkerror/i,
      /cache/i,
      /service worker/i
    ];

    return autoRecoveryPatterns.some(pattern => 
      pattern.test(error.message) || pattern.test(error.stack || '')
    );
  }

  private handleAutoRecovery = async () => {
    if (this.state.recoveryAttempts >= this.maxRecoveryAttempts) {
      return;
    }

    this.setState({
      isRecovering: true,
      recoveryAttempts: this.state.recoveryAttempts + 1,
      recoveryMessage: 'Attempting automatic recovery...'
    });

    try {
      const result = await performPWARecovery({
        maxRetries: 1,
        forceReload: this.state.recoveryAttempts === this.maxRecoveryAttempts - 1
      });

      if (result.success) {
        this.setState({
          recoveryMessage: 'Recovery successful, reloading...'
        });
        
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        this.setState({
          isRecovering: false,
          recoveryMessage: `Recovery failed: ${result.message}`
        });
      }
    } catch (error) {
      console.error('Auto recovery failed:', error);
      this.setState({
        isRecovering: false,
        recoveryMessage: 'Automatic recovery failed'
      });
    }
  };

  private handleManualRecovery = async () => {
    this.setState({
      isRecovering: true,
      recoveryMessage: 'Performing manual recovery...'
    });

    try {
      const result = await performPWARecovery({
        forceReload: true,
        clearAllData: false
      });

      this.setState({
        recoveryMessage: result.success ? 'Recovery completed, reloading...' : result.message
      });

      if (result.success || result.requiresReload) {
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        this.setState({ isRecovering: false });
      }
    } catch (error) {
      console.error('Manual recovery failed:', error);
      this.setState({
        isRecovering: false,
        recoveryMessage: 'Manual recovery failed'
      });
    }
  };

  private handleEmergencyRecovery = async () => {
    this.setState({
      isRecovering: true,
      recoveryMessage: 'Performing emergency recovery (clearing all data)...'
    });

    try {
      await emergencyPWARecovery();
    } catch (error) {
      console.error('Emergency recovery failed:', error);
      // Force reload as last resort
      window.location.reload();
    }
  };

  private handleSimpleReload = () => {
    window.location.reload();
  };

  render() {
    if (!this.state.hasError) {
      return this.props.children;
    }

    const { error, isRecovering, recoveryMessage, recoveryAttempts, isFirefoxError } = this.state;

    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          padding: 3,
          backgroundColor: 'background.default'
        }}
      >
        <Paper
          elevation={3}
          sx={{
            padding: 4,
            maxWidth: 600,
            width: '100%',
            textAlign: 'center'
          }}
        >
          <WarningIcon
            sx={{
              fontSize: 64,
              color: isFirefoxError ? 'info.main' : 'warning.main',
              marginBottom: 2
            }}
          />

          <Typography variant="h4" gutterBottom>
            {isFirefoxError ? 'Firefox Compatibility Issue' : 'Application Error'}
          </Typography>

          <Typography variant="body1" color="text.secondary" paragraph>
            {isFirefoxError
              ? 'DataStatPro detected a Firefox-specific compatibility issue. The application will automatically recover in a moment.'
              : 'DataStatPro encountered an error and needs to recover. This might be due to cached data corruption or network issues.'
            }
          </Typography>

          {isFirefoxError && (
            <Alert severity="info" sx={{ mb: 2, textAlign: 'left' }}>
              <Typography variant="body2">
                <strong>Firefox Users:</strong> This is a known compatibility issue with Firefox's security policies.
                The application is automatically clearing problematic caches and will reload shortly.
              </Typography>
            </Alert>
          )}

          {isRecovering ? (
            <Box sx={{ marginY: 3 }}>
              <CircularProgress size={40} sx={{ marginBottom: 2 }} />
              <Typography variant="body2" color="text.secondary">
                {recoveryMessage}
              </Typography>
            </Box>
          ) : (
            <Stack spacing={2} sx={{ marginTop: 3 }}>
              {recoveryAttempts > 0 && (
                <Alert severity="info">
                  Automatic recovery attempted {recoveryAttempts} time(s).
                  {recoveryMessage && ` ${recoveryMessage}`}
                </Alert>
              )}

              <Button
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={this.handleSimpleReload}
                size="large"
              >
                Reload Page
              </Button>

              <Button
                variant="outlined"
                startIcon={<BuildIcon />}
                onClick={this.handleManualRecovery}
                disabled={isRecovering}
              >
                Recover App Data
              </Button>

              <Button
                variant="text"
                color="warning"
                onClick={this.handleEmergencyRecovery}
                disabled={isRecovering}
                size="small"
              >
                Emergency Recovery (Clear All Data)
              </Button>
            </Stack>
          )}

          {process.env.NODE_ENV === 'development' && error && (
            <Box sx={{ marginTop: 3, textAlign: 'left' }}>
              <Typography variant="h6" gutterBottom>
                Error Details (Development):
              </Typography>
              <Alert severity="error" sx={{ textAlign: 'left' }}>
                <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
                  {error.message}
                  {error.stack && `\n\nStack trace:\n${error.stack}`}
                </Typography>
              </Alert>
            </Box>
          )}
        </Paper>
      </Box>
    );
  }
}

export default PWAErrorBoundary;
