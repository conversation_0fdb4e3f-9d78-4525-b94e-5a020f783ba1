import React, { useState } from 'react';
import {
  Button,
  IconButton,
  <PERSON>lt<PERSON>,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Switch,
  FormControlLabel,
  Box,
  Typography,
  CircularProgress,
  Fade
} from '@mui/material';
import {
  Update as UpdateIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { usePWAUpdate } from '../../hooks/usePWAUpdate';

interface UpdateButtonProps {
  variant?: 'button' | 'icon' | 'menu';
  size?: 'small' | 'medium' | 'large';
  showSettings?: boolean;
  showVersionInfo?: boolean;
}

const UpdateButton: React.FC<UpdateButtonProps> = ({
  variant = 'icon',
  size = 'medium',
  showSettings = true,
  showVersionInfo = true
}) => {
  const {
    needRefresh,
    isUpdating,
    updateError,
    currentVersion,
    lastUpdateCheck,
    autoUpdateEnabled,
    checkForUpdates,
    updateServiceWorker,
    setAutoUpdateEnabled,
    forceRefresh
  } = usePWAUpdate();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (variant === 'menu') {
      setAnchorEl(event.currentTarget);
    } else {
      handleCheckForUpdates();
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCheckForUpdates = async () => {
    try {
      await checkForUpdates();
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);
    } catch (error) {
      console.error('Update check failed:', error);
    }
    handleClose();
  };

  const handleUpdateNow = async () => {
    try {
      await updateServiceWorker();
    } catch (error) {
      console.error('Update failed:', error);
    }
    handleClose();
  };

  const handleForceRefresh = () => {
    forceRefresh();
    handleClose();
  };

  const handleAutoUpdateToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAutoUpdateEnabled(event.target.checked);
  };

  const getButtonIcon = () => {
    if (isUpdating) {
      return <CircularProgress size={20} color="inherit" />;
    }
    if (showSuccess) {
      return <CheckCircleIcon />;
    }
    if (updateError) {
      return <ErrorIcon />;
    }
    if (needRefresh) {
      return <UpdateIcon color="warning" />;
    }
    return <UpdateIcon />;
  };

  const getButtonColor = (): 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning' => {
    if (showSuccess) return 'success';
    if (updateError) return 'error';
    if (needRefresh) return 'warning';
    return 'primary';
  };

  const getTooltipText = () => {
    if (isUpdating) return 'Checking for updates...';
    if (showSuccess) return 'Up to date!';
    if (updateError) return `Update error: ${updateError}`;
    if (needRefresh) return 'Update available - click to install';
    return 'Check for updates';
  };

  const formatLastCheck = () => {
    if (!lastUpdateCheck) return 'Never';
    const now = new Date();
    const diffMs = now.getTime() - lastUpdateCheck.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    return lastUpdateCheck.toLocaleTimeString();
  };

  if (variant === 'button') {
    return (
      <Button
        variant="outlined"
        size={size}
        startIcon={getButtonIcon()}
        onClick={handleClick}
        disabled={isUpdating}
        color={getButtonColor()}
        sx={{
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: 'translateY(-1px)',
            boxShadow: 2
          }
        }}
      >
        {isUpdating ? 'Checking...' : needRefresh ? 'Update Available' : 'Check Updates'}
      </Button>
    );
  }

  if (variant === 'icon') {
    return (
      <Tooltip title={getTooltipText()}>
        <span>
          <IconButton
            onClick={handleClick}
            disabled={isUpdating}
            color={getButtonColor()}
            size={size}
            sx={{
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'scale(1.1)'
              }
            }}
          >
            <Fade in={!isUpdating} timeout={200}>
              <Box>{getButtonIcon()}</Box>
            </Fade>
          </IconButton>
        </span>
      </Tooltip>
    );
  }

  // Menu variant
  return (
    <>
      <Tooltip title="Update options">
        <IconButton
          onClick={handleClick}
          color={getButtonColor()}
          size={size}
        >
          {getButtonIcon()}
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: { minWidth: 250 }
        }}
      >
        {showVersionInfo && (
          <>
            <MenuItem disabled>
              <ListItemIcon>
                <InfoIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary={`Version ${currentVersion}`}
                secondary={`Last check: ${formatLastCheck()}`}
              />
            </MenuItem>
            <Divider />
          </>
        )}

        <MenuItem onClick={handleCheckForUpdates} disabled={isUpdating}>
          <ListItemIcon>
            {isUpdating ? (
              <CircularProgress size={20} />
            ) : (
              <RefreshIcon fontSize="small" />
            )}
          </ListItemIcon>
          <ListItemText primary={isUpdating ? 'Checking...' : 'Check for Updates'} />
        </MenuItem>

        {needRefresh && (
          <MenuItem onClick={handleUpdateNow}>
            <ListItemIcon>
              <UpdateIcon fontSize="small" color="warning" />
            </ListItemIcon>
            <ListItemText 
              primary="Install Update" 
              secondary="New version available"
            />
          </MenuItem>
        )}

        <MenuItem onClick={handleForceRefresh}>
          <ListItemIcon>
            <RefreshIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText 
            primary="Force Refresh" 
            secondary="Reload the entire app"
          />
        </MenuItem>

        {showSettings && (
          <>
            <Divider />
            <MenuItem>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="body2">Auto Updates</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Check for updates automatically
                  </Typography>
                </Box>
                <Switch
                  checked={autoUpdateEnabled}
                  onChange={handleAutoUpdateToggle}
                  size="small"
                />
              </Box>
            </MenuItem>
          </>
        )}
      </Menu>
    </>
  );
};

export default UpdateButton;
