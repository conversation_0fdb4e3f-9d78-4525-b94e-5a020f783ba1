import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  LinearProgress,
  Typography,
  IconButton,
  <PERSON><PERSON>se,
  Stack,
  Chip
} from '@mui/material';
import {
  Close as CloseIcon,
  Update as UpdateIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { usePWAUpdate } from '../../hooks/usePWAUpdate';

interface UpdateNotificationProps {
  anchorOrigin?: {
    vertical: 'top' | 'bottom';
    horizontal: 'left' | 'center' | 'right';
  };
}

const UpdateNotification: React.FC<UpdateNotificationProps> = ({
  anchorOrigin = { vertical: 'bottom', horizontal: 'left' }
}) => {
  const {
    needRefresh,
    offlineReady,
    offlineReadyShown,
    isUpdating,
    updateProgress,
    updateError,
    currentVersion,
    availableVersion,
    updateServiceWorker,
    dismissUpdateNotification,
    updateNotificationDismissed,
    forceRefresh,
    checkForUpdates,
    markOfflineReadyAsShown
  } = usePWAUpdate();

  const [expanded, setExpanded] = useState(false);
  const [showOfflineReady, setShowOfflineReady] = useState(false);

  // Show offline ready notification only once per session and if not shown before
  useEffect(() => {
    if (offlineReady && !offlineReadyShown && !showOfflineReady) {
      setShowOfflineReady(true);

      // Auto-hide after 5 seconds
      const timer = setTimeout(() => {
        setShowOfflineReady(false);
        markOfflineReadyAsShown();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [offlineReady, offlineReadyShown, showOfflineReady, markOfflineReadyAsShown]);

  const handleUpdate = async () => {
    try {
      await updateServiceWorker();
    } catch (error) {
      console.error('Update failed:', error);
    }
  };

  const handleDismiss = () => {
    dismissUpdateNotification();
  };

  const handleRetry = () => {
    checkForUpdates();
  };

  // Update available notification
  if (needRefresh && !updateNotificationDismissed) {
    return (
      <Snackbar
        open={true}
        anchorOrigin={anchorOrigin}
        sx={{ maxWidth: 400 }}
      >
        <Alert
          severity={updateError ? 'error' : 'info'}
          variant="filled"
          sx={{ 
            width: '100%',
            '& .MuiAlert-message': {
              width: '100%'
            }
          }}
          action={
            <IconButton
              size="small"
              color="inherit"
              onClick={handleDismiss}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          }
        >
          <Stack spacing={1} sx={{ width: '100%' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                {updateError ? 'Update Failed' : 'New Version Available'}
              </Typography>
              <IconButton
                size="small"
                onClick={() => setExpanded(!expanded)}
                sx={{ color: 'inherit', ml: 1 }}
              >
                {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Box>

            {updateError ? (
              <Typography variant="body2">
                {updateError}
              </Typography>
            ) : (
              <Typography variant="body2">
                A new version of DataStatPro is ready to install.
              </Typography>
            )}

            <Collapse in={expanded}>
              <Stack spacing={1}>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip
                    label={`Current: v${currentVersion}`}
                    size="small"
                    variant="outlined"
                    sx={{ color: 'inherit', borderColor: 'currentColor' }}
                  />
                  {availableVersion && (
                    <Chip
                      label={`Available: ${availableVersion}`}
                      size="small"
                      variant="outlined"
                      sx={{ color: 'inherit', borderColor: 'currentColor' }}
                    />
                  )}
                </Box>

                {isUpdating && (
                  <Box>
                    <Typography variant="caption" sx={{ mb: 0.5, display: 'block' }}>
                      Updating... {updateProgress}%
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={updateProgress}
                      sx={{
                        backgroundColor: 'rgba(255, 255, 255, 0.3)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: 'currentColor'
                        }
                      }}
                    />
                  </Box>
                )}
              </Stack>
            </Collapse>

            <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
              {updateError ? (
                <>
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={handleRetry}
                    disabled={isUpdating}
                    sx={{ 
                      color: 'inherit', 
                      borderColor: 'currentColor',
                      '&:hover': {
                        borderColor: 'currentColor',
                        backgroundColor: 'rgba(255, 255, 255, 0.1)'
                      }
                    }}
                  >
                    Retry
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={forceRefresh}
                    sx={{ 
                      color: 'inherit', 
                      borderColor: 'currentColor',
                      '&:hover': {
                        borderColor: 'currentColor',
                        backgroundColor: 'rgba(255, 255, 255, 0.1)'
                      }
                    }}
                  >
                    Force Refresh
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={isUpdating ? <DownloadIcon /> : <UpdateIcon />}
                    onClick={handleUpdate}
                    disabled={isUpdating}
                    sx={{ 
                      color: 'inherit', 
                      borderColor: 'currentColor',
                      '&:hover': {
                        borderColor: 'currentColor',
                        backgroundColor: 'rgba(255, 255, 255, 0.1)'
                      }
                    }}
                  >
                    {isUpdating ? 'Updating...' : 'Update Now'}
                  </Button>
                  <Button
                    size="small"
                    variant="text"
                    onClick={handleDismiss}
                    sx={{ 
                      color: 'inherit',
                      '&:hover': {
                        backgroundColor: 'rgba(255, 255, 255, 0.1)'
                      }
                    }}
                  >
                    Later
                  </Button>
                </>
              )}
            </Box>
          </Stack>
        </Alert>
      </Snackbar>
    );
  }

  // Offline ready notification
  if (showOfflineReady) {
    const handleOfflineReadyClose = () => {
      setShowOfflineReady(false);
      markOfflineReadyAsShown();
    };

    return (
      <Snackbar
        open={true}
        autoHideDuration={5000}
        onClose={handleOfflineReadyClose}
        anchorOrigin={anchorOrigin}
      >
        <Alert
          severity="success"
          variant="filled"
          icon={<CheckCircleIcon />}
          onClose={handleOfflineReadyClose}
        >
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            App Ready for Offline Use
          </Typography>
          <Typography variant="body2">
            DataStatPro is now cached and ready to work offline.
          </Typography>
        </Alert>
      </Snackbar>
    );
  }

  return null;
};

export default UpdateNotification;
