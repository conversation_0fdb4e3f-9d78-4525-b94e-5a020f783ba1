import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  Card<PERSON><PERSON>er,
  FormControlLabel,
  Switch,
  Typography,
  Box,
  Divider,
  <PERSON>ton,
  <PERSON>ert,
  Stack,
  Chip
} from '@mui/material';
import {
  Update as UpdateIcon,
  Schedule as ScheduleIcon,
  Notifications as NotificationsIcon,
  Storage as StorageIcon
} from '@mui/icons-material';
import { usePWAUpdate } from '../../hooks/usePWAUpdate';
import { VersionInfo } from './';
import { cacheManager, diagnoseCacheIssues, recoverFromCacheIssues } from '../../utils/cacheManager';

interface UpdateSettingsProps {
  showVersionInfo?: boolean;
  showAdvancedOptions?: boolean;
}

const UpdateSettings: React.FC<UpdateSettingsProps> = ({
  showVersionInfo = true,
  showAdvancedOptions = false
}) => {
  const {
    autoUpdateEnabled,
    setAutoUpdateEnabled,
    checkForUpdates,
    isUpdating,
    updateError,
    needRefresh,
    lastUpdateCheck,
    forceRefresh
  } = usePWAUpdate();

  const [cacheIssues, setCacheIssues] = React.useState<{
    hasIssues: boolean;
    issues: string[];
    recommendations: string[];
  } | null>(null);
  const [isCheckingCache, setIsCheckingCache] = React.useState(false);

  const handleAutoUpdateToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAutoUpdateEnabled(event.target.checked);
  };

  const handleClearCache = async () => {
    try {
      await cacheManager.clearAllCaches();
      forceRefresh();
    } catch (error) {
      console.error('Failed to clear caches:', error);
      forceRefresh();
    }
  };

  const handleDiagnoseCaches = async () => {
    setIsCheckingCache(true);
    try {
      const diagnosis = await diagnoseCacheIssues();
      setCacheIssues(diagnosis);
    } catch (error) {
      console.error('Cache diagnosis failed:', error);
      setCacheIssues({
        hasIssues: true,
        issues: ['Cache diagnosis failed'],
        recommendations: ['Try clearing all caches']
      });
    } finally {
      setIsCheckingCache(false);
    }
  };

  const handleRecoverCaches = async () => {
    try {
      await recoverFromCacheIssues();
      setCacheIssues(null);
      // Optionally refresh to apply changes
      // forceRefresh();
    } catch (error) {
      console.error('Cache recovery failed:', error);
    }
  };

  const formatLastCheck = () => {
    if (!lastUpdateCheck) return 'Never';
    return lastUpdateCheck.toLocaleString();
  };

  return (
    <Card>
      <CardHeader
        title="App Updates"
        subheader="Manage how DataStatPro updates are handled"
        avatar={<UpdateIcon color="primary" />}
      />
      <CardContent>
        <Stack spacing={3}>
          {/* Update Status */}
          {needRefresh && (
            <Alert severity="warning" variant="outlined">
              <Typography variant="subtitle2" gutterBottom>
                Update Available
              </Typography>
              <Typography variant="body2">
                A new version of DataStatPro is ready to install.
              </Typography>
            </Alert>
          )}

          {updateError && (
            <Alert severity="error" variant="outlined">
              <Typography variant="subtitle2" gutterBottom>
                Update Error
              </Typography>
              <Typography variant="body2">
                {updateError}
              </Typography>
            </Alert>
          )}

          {/* Version Information */}
          {showVersionInfo && (
            <Box>
              <VersionInfo variant="detailed" showUpdateButton={false} />
            </Box>
          )}

          <Divider />

          {/* Auto-Update Setting */}
          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={autoUpdateEnabled}
                  onChange={handleAutoUpdateToggle}
                  color="primary"
                />
              }
              label={
                <Box>
                  <Typography variant="body1" component="div">
                    Automatic Updates
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Check for updates automatically every 30 minutes
                  </Typography>
                </Box>
              }
            />
          </Box>

          {/* Update Actions */}
          <Box>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ScheduleIcon fontSize="small" />
              Update Actions
            </Typography>
            <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
              <Button
                variant="outlined"
                startIcon={<UpdateIcon />}
                onClick={checkForUpdates}
                disabled={isUpdating}
                size="small"
              >
                {isUpdating ? 'Checking...' : 'Check Now'}
              </Button>
              
              {needRefresh && (
                <Button
                  variant="contained"
                  color="warning"
                  startIcon={<UpdateIcon />}
                  onClick={() => window.location.reload()}
                  size="small"
                >
                  Install Update
                </Button>
              )}
            </Stack>
          </Box>

          {/* Advanced Options */}
          {showAdvancedOptions && (
            <>
              <Divider />
              <Box>
                <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <StorageIcon fontSize="small" />
                  Advanced Options
                </Typography>
                <Stack spacing={2}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Last update check: {formatLastCheck()}
                    </Typography>
                  </Box>
                  
                  <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                    <Button
                      variant="outlined"
                      startIcon={<StorageIcon />}
                      onClick={handleDiagnoseCaches}
                      size="small"
                      disabled={isCheckingCache}
                    >
                      {isCheckingCache ? 'Checking...' : 'Diagnose Cache'}
                    </Button>

                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<StorageIcon />}
                      onClick={handleClearCache}
                      size="small"
                    >
                      Clear Cache & Refresh
                    </Button>
                  </Stack>

                  {cacheIssues && (
                    <Alert
                      severity={cacheIssues.hasIssues ? 'warning' : 'success'}
                      variant="outlined"
                      action={
                        cacheIssues.hasIssues ? (
                          <Button
                            color="inherit"
                            size="small"
                            onClick={handleRecoverCaches}
                          >
                            Fix Issues
                          </Button>
                        ) : null
                      }
                    >
                      <Typography variant="subtitle2" gutterBottom>
                        Cache Diagnosis {cacheIssues.hasIssues ? 'Issues Found' : 'All Good'}
                      </Typography>
                      {cacheIssues.issues.length > 0 && (
                        <Box component="ul" sx={{ mt: 1, mb: 1, pl: 2 }}>
                          {cacheIssues.issues.map((issue, index) => (
                            <li key={index}>
                              <Typography variant="body2">{issue}</Typography>
                            </li>
                          ))}
                        </Box>
                      )}
                      {cacheIssues.recommendations.length > 0 && (
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            Recommendations:
                          </Typography>
                          <Box component="ul" sx={{ mt: 0.5, pl: 2 }}>
                            {cacheIssues.recommendations.map((rec, index) => (
                              <li key={index}>
                                <Typography variant="body2">{rec}</Typography>
                              </li>
                            ))}
                          </Box>
                        </Box>
                      )}
                    </Alert>
                  )}

                  <Alert severity="info" variant="outlined">
                    <Typography variant="body2">
                      Cache diagnostics can help identify and fix loading issues.
                      Clearing cache will remove all stored data and force a fresh download of the app.
                    </Typography>
                  </Alert>
                </Stack>
              </Box>
            </>
          )}

          {/* Update Information */}
          <Box>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <NotificationsIcon fontSize="small" />
              Update Notifications
            </Typography>
            <Typography variant="body2" color="text.secondary">
              You'll be notified when updates are available. Updates include new features, 
              bug fixes, and security improvements.
            </Typography>
            <Box sx={{ mt: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip label="New Features" size="small" variant="outlined" />
              <Chip label="Bug Fixes" size="small" variant="outlined" />
              <Chip label="Security Updates" size="small" variant="outlined" />
            </Box>
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default UpdateSettings;
