import React from 'react';
import {
  <PERSON>,
  Typography,
  Chip,
  Tooltip,
  Icon<PERSON>utt<PERSON>,
  Card,
  <PERSON>Content,
  Stack,
  Divider
} from '@mui/material';
import {
  Info as InfoIcon,
  Update as UpdateIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { usePWAUpdate } from '../../hooks/usePWAUpdate';

interface VersionInfoProps {
  variant?: 'compact' | 'detailed';
  showUpdateButton?: boolean;
}

const VersionInfo: React.FC<VersionInfoProps> = ({ 
  variant = 'compact', 
  showUpdateButton = true 
}) => {
  const {
    currentVersion,
    availableVersion,
    lastUpdateCheck,
    needRefresh,
    offlineReady,
    isUpdating,
    checkForUpdates
  } = usePWAUpdate();

  const formatLastUpdateCheck = (date: Date | null): string => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return date.toLocaleDateString();
  };

  const getStatusColor = (): 'success' | 'warning' | 'info' | 'default' => {
    if (needRefresh) return 'warning';
    if (offlineReady) return 'success';
    return 'info';
  };

  const getStatusText = (): string => {
    if (needRefresh) return 'Update Available';
    if (offlineReady) return 'Ready Offline';
    return 'Up to Date';
  };

  const getStatusIcon = () => {
    if (needRefresh) return <UpdateIcon fontSize="small" />;
    if (offlineReady) return <CheckCircleIcon fontSize="small" />;
    return <InfoIcon fontSize="small" />;
  };

  if (variant === 'compact') {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Tooltip title={`Version ${currentVersion} • Last checked: ${formatLastUpdateCheck(lastUpdateCheck)}`}>
          <Chip
            icon={getStatusIcon()}
            label={`v${currentVersion}`}
            color={getStatusColor()}
            variant="outlined"
            size="small"
          />
        </Tooltip>
        
        {showUpdateButton && (
          <Tooltip title="Check for updates">
            <IconButton
              size="small"
              onClick={checkForUpdates}
              disabled={isUpdating}
              sx={{ 
                opacity: isUpdating ? 0.5 : 1,
                '&:hover': {
                  backgroundColor: 'action.hover'
                }
              }}
            >
              <UpdateIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    );
  }

  return (
    <Card variant="outlined" sx={{ maxWidth: 400 }}>
      <CardContent>
        <Stack spacing={2}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6" component="h3">
              App Version
            </Typography>
            <Chip
              icon={getStatusIcon()}
              label={getStatusText()}
              color={getStatusColor()}
              variant="filled"
              size="small"
            />
          </Box>

          <Divider />

          <Stack spacing={1.5}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Current Version:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {currentVersion}
              </Typography>
            </Box>

            {availableVersion && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  Available Version:
                </Typography>
                <Typography variant="body2" fontWeight="medium" color="warning.main">
                  {availableVersion}
                </Typography>
              </Box>
            )}

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Last Update Check:
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <ScheduleIcon fontSize="small" color="action" />
                <Typography variant="body2">
                  {formatLastUpdateCheck(lastUpdateCheck)}
                </Typography>
              </Box>
            </Box>

            {offlineReady && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  Offline Status:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <CheckCircleIcon fontSize="small" color="success" />
                  <Typography variant="body2" color="success.main">
                    Ready
                  </Typography>
                </Box>
              </Box>
            )}
          </Stack>

          {showUpdateButton && (
            <>
              <Divider />
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <IconButton
                  onClick={checkForUpdates}
                  disabled={isUpdating}
                  color="primary"
                  sx={{
                    opacity: isUpdating ? 0.5 : 1,
                  }}
                >
                  <UpdateIcon />
                </IconButton>
              </Box>
            </>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
};

export default VersionInfo;
