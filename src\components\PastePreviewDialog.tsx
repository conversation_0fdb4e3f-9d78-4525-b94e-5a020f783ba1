import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogA<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  Box,
  Chip,
  Alert,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox
} from '@mui/material';
import { DataType, Column } from '../types';
import { detectDataTypes } from '../utils/pasteParser';

interface ColumnMapping {
  pastedColumn: string;
  targetColumn: string | 'new';
  dataType: DataType;
  createNew: boolean;
}

interface PastePreviewDialogProps {
  open: boolean;
  onClose: () => void;
  pastedHeaders: string[];
  pastedRows: string[][];
  existingColumns: Column[];
  onConfirm: (
    mappings: ColumnMapping[],
    mode: 'append' | 'replace' | 'newDataset',
    datasetName?: string
  ) => void;
}

export const PastePreviewDialog: React.FC<PastePreviewDialogProps> = ({
  open,
  onClose,
  pastedHeaders,
  pastedRows,
  existingColumns,
  onConfirm
}) => {
  const [mappings, setMappings] = useState<ColumnMapping[]>([]);
  const [pasteMode, setPasteMode] = useState<'append' | 'replace' | 'newDataset'>('append');
  const [newDatasetName, setNewDatasetName] = useState('');
  const [autoMap, setAutoMap] = useState(true);

  useEffect(() => {
    if (open && pastedHeaders.length > 0) {
      const detectedTypes = detectDataTypes(pastedRows);
      
      const initialMappings: ColumnMapping[] = pastedHeaders.map((header, index) => {
        // Try to find matching column by name (case-insensitive)
        const matchingColumn = autoMap 
          ? existingColumns.find(col => 
              col.name.toLowerCase() === header.toLowerCase()
            )
          : null;
        
        return {
          pastedColumn: header,
          targetColumn: matchingColumn ? matchingColumn.name : 'new',
          dataType: matchingColumn ? matchingColumn.type : detectedTypes[index],
          createNew: !matchingColumn
        };
      });
      
      setMappings(initialMappings);
    }
  }, [open, pastedHeaders, pastedRows, existingColumns, autoMap]);

  const handleMappingChange = (index: number, field: keyof ColumnMapping, value: any) => {
    const newMappings = [...mappings];
    newMappings[index] = { ...newMappings[index], [field]: value };
    
    if (field === 'targetColumn') {
      newMappings[index].createNew = value === 'new';
      if (value !== 'new') {
        const targetCol = existingColumns.find(col => col.name === value);
        if (targetCol) {
          newMappings[index].dataType = targetCol.type;
        }
      }
    }
    
    setMappings(newMappings);
  };

  const handleConfirm = () => {
    if (pasteMode === 'newDataset' && !newDatasetName.trim()) {
      alert('Please enter a name for the new dataset');
      return;
    }
    onConfirm(mappings, pasteMode, newDatasetName);
  };

  const previewRowCount = Math.min(5, pastedRows.length);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>Import Data Preview</DialogTitle>
      <DialogContent dividers>
        <Alert severity="info" sx={{ mb: 2 }}>
          Found {pastedRows.length} rows and {pastedHeaders.length} columns
        </Alert>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Import Mode
          </Typography>
          <RadioGroup value={pasteMode} onChange={(e) => setPasteMode(e.target.value as any)}>
            <FormControlLabel 
              value="append" 
              control={<Radio />} 
              label="Append to current dataset" 
            />
            <FormControlLabel 
              value="replace" 
              control={<Radio />} 
              label="Replace current dataset" 
            />
            <FormControlLabel 
              value="newDataset" 
              control={<Radio />} 
              label="Create new dataset" 
            />
          </RadioGroup>
          
          {pasteMode === 'newDataset' && (
            <TextField
              label="Dataset Name"
              value={newDatasetName}
              onChange={(e) => setNewDatasetName(e.target.value)}
              fullWidth
              margin="normal"
              required
            />
          )}
        </Box>

        <Box sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Checkbox 
                checked={autoMap} 
                onChange={(e) => setAutoMap(e.target.checked)}
              />
            }
            label="Auto-map columns by name"
          />
        </Box>

        <Typography variant="subtitle1" gutterBottom>
          Column Mapping
        </Typography>
        <TableContainer component={Paper} sx={{ mb: 2 }}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Source Column</TableCell>
                <TableCell>Target Column</TableCell>
                <TableCell>Data Type</TableCell>
                <TableCell>Sample Values</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {mappings.map((mapping, index) => (
                <TableRow key={index}>
                  <TableCell>{mapping.pastedColumn}</TableCell>
                  <TableCell>
                    <FormControl fullWidth size="small">
                      <Select
                        value={mapping.targetColumn}
                        onChange={(e) => handleMappingChange(index, 'targetColumn', e.target.value)}
                      >
                        <MenuItem value="new">
                          <em>Create New Column</em>
                        </MenuItem>
                        {existingColumns.map(col => (
                          <MenuItem key={col.id} value={col.name}>
                            {col.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <FormControl fullWidth size="small">
                      <Select
                        value={mapping.dataType}
                        onChange={(e) => handleMappingChange(index, 'dataType', e.target.value)}
                        disabled={mapping.targetColumn !== 'new'}
                      >
                        <MenuItem value={DataType.TEXT}>Text</MenuItem>
                        <MenuItem value={DataType.NUMERIC}>Numeric</MenuItem>
                        <MenuItem value={DataType.BOOLEAN}>Boolean</MenuItem>
                        <MenuItem value={DataType.DATE}>Date</MenuItem>
                        <MenuItem value={DataType.CATEGORICAL}>Categorical</MenuItem>
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                      {pastedRows.slice(0, 3).map((row, rowIndex) => (
                        <Chip 
                          key={rowIndex} 
                          label={row[index] || '<empty>'} 
                          size="small" 
                          variant="outlined"
                        />
                      ))}
                      {pastedRows.length > 3 && <Chip label="..." size="small" />}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <Typography variant="subtitle1" gutterBottom>
          Data Preview
        </Typography>
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                {pastedHeaders.map((header, index) => (
                  <TableCell key={index}>{header}</TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {pastedRows.slice(0, previewRowCount).map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <TableCell key={cellIndex}>{cell}</TableCell>
                  ))}
                </TableRow>
              ))}
              {pastedRows.length > previewRowCount && (
                <TableRow>
                  <TableCell colSpan={pastedHeaders.length} align="center">
                    ... and {pastedRows.length - previewRowCount} more rows
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleConfirm} variant="contained" color="primary">
          Import Data
        </Button>
      </DialogActions>
    </Dialog>
  );
};
