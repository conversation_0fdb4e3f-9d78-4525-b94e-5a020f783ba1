import React, { useState } from 'react';
import { <PERSON>ton, CircularProgress, Alert } from '@mui/material';
import { useAuth } from '../../context/AuthContext';
import { config, isStripeEnabled } from '../../config/environment';
import { useNavigate } from 'react-router-dom';

interface StripeCheckoutButtonProps {
  priceId: string;
  planName: string;
  billingCycle: 'monthly' | 'annual';
  requiresEducationalEmail?: boolean;
  disabled?: boolean;
  children?: React.ReactNode;
}

// Utility function to check educational email domains
const isEducationalEmail = (email: string): boolean => {
  const eduPatterns = [
    /\.edu$/i,
    /\.ac\.edu$/i, 
    /\.edu\.au$/i,
    /\.ac\.uk$/i,
    /\.edu\.[a-z]{2,}$/i,
    /\.ac\.[a-z]{2,}$/i,
    /\.university\.[a-z]{2,}$/i,
    /\.college\.[a-z]{2,}$/i
  ];
  
  return eduPatterns.some(pattern => pattern.test(email));
};

const StripeCheckoutButton: React.FC<StripeCheckoutButtonProps> = ({
  priceId,
  planName,
  billingCycle,
  requiresEducationalEmail = false,
  disabled = false,
  children
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleCheckout = async () => {
    // Check authentication first
    if (!user) {
      // Store subscription intent for after login
      sessionStorage.setItem('subscriptionIntent', JSON.stringify({
        priceId,
        planName,
        billingCycle,
        requiresEducationalEmail,
        timestamp: Date.now()
      }));

      // Redirect to login with return URL
      navigate('/app#/auth/login?returnTo=subscription&plan=' + planName.toLowerCase().replace(' ', '-'));
      return;
    }

    // Validate educational email if required
    if (requiresEducationalEmail && !isEducationalEmail(user.email || '')) {
      setError('Educational email address (.edu domain) is required for this plan');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // TODO: Replace with actual Stripe integration
      // This is a placeholder for the Stripe Checkout implementation
      
      // For development phase, show email contact
      const subject = `Subscription Request - ${planName} (${billingCycle})`;
      const body = `Hi DataStatPro Team,

I would like to subscribe to the ${planName} plan with ${billingCycle} billing.

Account Details:
- Email: ${user.email}
- Plan: ${planName}
- Billing: ${billingCycle}
- Price ID: ${priceId}

${requiresEducationalEmail ? `Educational Email: ${user.email} (verified)` : ''}

Please process my subscription and provide payment instructions.

Thank you!`;

      const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.open(mailtoUrl);

      /* 
      // Future Stripe implementation:
      const response = await fetch('/api/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          userEmail: user.email,
          billingCycle,
          planName
        }),
      });

      const { url, error: checkoutError } = await response.json();
      
      if (checkoutError) {
        throw new Error(checkoutError);
      }

      // Redirect to Stripe Checkout
      window.location.href = url;
      */
      
    } catch (err: any) {
      setError(err.message || 'Failed to start checkout process');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        fullWidth
        onClick={handleCheckout}
        disabled={disabled || loading}
        sx={{ mt: 2 }}
      >
        {loading ? (
          <CircularProgress size={24} color="inherit" />
        ) : (
          children || `Subscribe to ${planName}`
        )}
      </Button>
      
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}
    </>
  );
};

export default StripeCheckoutButton;
