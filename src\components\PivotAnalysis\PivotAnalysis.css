/* Custom styles for PivotAnalysis component to improve responsiveness */

/* Make the pivot table container responsive */
.pvtUi {
  max-width: 100%;
  overflow-x: auto;
  padding: 10px 0;
}

/* Improve responsiveness of the axis containers */
.pvtAxisContainer, .pvtVals {
  display: flex;
  flex-wrap: wrap;
  max-width: 100%;
  overflow-x: auto;
  min-height: 60px;
  transition: background-color 0.2s ease;
  border: 2px dashed rgba(0, 0, 0, 0.12) !important;
  padding: 15px !important;
  margin: 8px 0 !important;
}

/* Add visual cues for drag targets */
.pvtAxisContainer:hover, .pvtVals:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: rgba(25, 118, 210, 0.5) !important;
}

/* Make the renderer area responsive */
.pvtRendererArea {
  max-width: 100%;
  overflow-x: auto;
  margin-top: 15px;
  padding: 15px !important;
  border: 1px solid rgba(0, 0, 0, 0.12) !important;
  border-radius: 4px;
}

/* Adjust table styles for better mobile viewing */
.pvtTable {
  max-width: 100%;
  overflow-x: auto;
  border-collapse: separate;
  border-spacing: 2px;
}

/* Fix truncating dialog boxes */
.pvtDropdown {
  max-height: 300px;
  overflow-y: auto;
  min-width: 160px;
  z-index: 1000 !important;
  position: absolute !important;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

/* Improve select elements to prevent truncation */
select.pvtRenderer, select.pvtAggregator {
  max-width: 100%;
  min-width: 150px;
  padding: 8px !important;
  border-radius: 4px;
  border: 1px solid #ccc;
  height: 40px !important;
  font-size: 14px !important;
  background-color: white;
  cursor: pointer;
  appearance: menulist !important;
  -webkit-appearance: menulist !important;
  -moz-appearance: menulist !important;
}

/* Improve attribute appearance */
.pvtAttr {
  padding: 6px 10px !important;
  margin: 4px !important;
  border-radius: 4px;
  display: inline-block;
  cursor: move;
  user-select: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.15) !important;
  background-color: #f5f5f5 !important;
  border: 1px solid #e0e0e0 !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.pvtAttr:hover {
  box-shadow: 0 3px 6px rgba(0,0,0,0.2) !important;
  transform: translateY(-1px);
}

/* Responsive styles for small screens */
@media (max-width: 600px) {
  .pvtUi {
    flex-direction: column;
  }
  
  .pvtAxisContainer {
    margin-bottom: 15px !important;
    max-width: 100%;
    padding: 15px 10px !important;
  }
  
  .pvtRendererArea {
    padding-left: 0;
    margin-top: 20px;
    padding: 15px 10px !important;
  }
  
  /* Make drag handles more touch-friendly */
  .pvtAxisContainer li span.pvtAttr {
    padding: 10px !important;
    margin: 6px 3px !important;
    font-size: 16px !important;
  }
  
  /* Improve dropdown menus on mobile */
  select.pvtRenderer, select.pvtAggregator {
    width: 100%;
    margin-bottom: 12px;
    height: 44px !important;
    font-size: 16px !important;
  }
  
  /* Ensure dropdowns don't get cut off */
  .pvtDropdown {
    max-width: 90vw;
    left: 5% !important;
    right: 5% !important;
  }
  
  /* Adjust dropdown menus for touch */
  select.pvtRenderer, select.pvtAggregator {
    height: 36px;
    font-size: 14px;
  }
}

/* Improve plotly chart responsiveness */
.js-plotly-plot, .plot-container {
  max-width: 100%;
}

/* Make sure the pivot controls don't overflow */
.pvtFilterBox {
  max-width: 90vw;
  overflow: auto;
}

/* Improve dropdown positioning on mobile */
.pvtDropdown {
  max-width: 100%;
  position: absolute !important;
  z-index: 1000 !important;
}

/* Fix for drag and drop functionality */
.pvtAxisContainer li, .pvtVals li {
  list-style-type: none !important;
  cursor: move !important;
}

/* Improve the appearance of the dropdown menus */
.pvtDropdownMenu {
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 5px 0;
  z-index: 1001 !important;
}

.pvtDropdownValue {
  padding: 8px 12px;
  cursor: pointer;
}

.pvtDropdownValue:hover {
  background-color: #f5f5f5;
}

/* Improve the appearance of the pivot table */
.pvtTable th, .pvtTable td {
  padding: 8px !important;
  border: 1px solid #e0e0e0 !important;
}

/* Highlight drop zones more clearly */
.pvtUnused, .pvtRows, .pvtCols, .pvtVals {
  position: relative;
  padding-top: 30px !important;
}

/* Improve the visibility of the renderer and aggregator selectors */
.pvtRendererArea select {
  display: inline-block !important;
  margin: 5px !important;
  vertical-align: middle !important;
}