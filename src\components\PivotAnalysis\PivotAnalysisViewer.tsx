import React, { useState, useEffect } from 'react';
import { Typography, Box, Paper, Card, CardContent, useTheme, useMedia<PERSON><PERSON>y, Stack, Divider, Alert, Tooltip, Grid, IconButton, Collapse } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import PageTitle from '../UI/PageTitle';
import DatasetSelector from '../UI/DatasetSelector';
import { useData } from '../../context/DataContext';
import GoogleSheetsImportButton from './GoogleSheetsImportButton';
import PivotTableUI, { PivotTableUIProps } from 'react-pivottable/PivotTableUI';
import 'react-pivottable/pivottable.css';
// import './PivotAnalysis.css'; // Import custom responsive styles
import TableRenderers from 'react-pivottable/TableRenderers';
import { styled } from '@mui/material/styles';

// Lazy load Plotly components to avoid loading 9.7MB bundle unless needed
const loadPlotlyRenderers = async () => {
  const [{ default: PlotlyRenderers }, Plotly, { default: createPlotlyComponent }] = await Promise.all([
    import('react-pivottable/PlotlyRenderers'),
    import('plotly.js'),
    import('react-plotly.js/factory')
  ]);

  const Plot = createPlotlyComponent(Plotly);
  return PlotlyRenderers(Plot);
};

// Initial renderers with just table renderers
let renderers = { ...TableRenderers };

// Styled component for the expand/collapse button
const ExpandButton = styled(IconButton)(({ theme }) => ({
  marginLeft: theme.spacing(1),
  padding: theme.spacing(0.5),
}));

const PivotAnalysisViewer: React.FC = () => {
  const { currentDataset, datasets, setCurrentDataset } = useData();
  const [pivotState, setPivotState] = useState<Partial<PivotTableUIProps>>({});
  const [instructionsExpanded, setInstructionsExpanded] = useState(true);
  const [plotlyRenderersLoaded, setPlotlyRenderersLoaded] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  const dataToUseForPivot = currentDataset ? currentDataset.data : [];

  // When currentDataset changes, reset pivotState
  useEffect(() => {
    setPivotState({ rendererName: "Table" }); // Default to Table renderer on dataset change
  }, [currentDataset?.id]);

  // Load Plotly renderers when a chart renderer is selected
  useEffect(() => {
    const isChartRenderer = pivotState.rendererName &&
      !['Table', 'Table Barchart', 'Heatmap', 'Row Heatmap', 'Col Heatmap'].includes(pivotState.rendererName);

    if (isChartRenderer && !plotlyRenderersLoaded) {
      loadPlotlyRenderers().then((plotlyRenderers) => {
        renderers = { ...renderers, ...plotlyRenderers };
        setPlotlyRenderersLoaded(true);
      }).catch((error) => {
        console.error('Failed to load Plotly renderers:', error);
      });
    }
  }, [pivotState.rendererName, plotlyRenderersLoaded]);
  
  // Adjust pivot state based on screen size
  useEffect(() => {
    if (pivotState.rendererName && pivotState.rendererName !== "Table") {
      // For chart renderers, adjust the plot size based on screen size
      const plotOptions = { 
        width: isMobile ? 300 : isTablet ? 500 : 700,
        height: isMobile ? 300 : isTablet ? 400 : 500,
        responsive: true
      };
      setPivotState(prev => ({ ...prev, plotlyOptions: plotOptions }));
    }
  }, [isMobile, isTablet, pivotState.rendererName]);
  
  // Custom renderer options to ensure dialogs don't get truncated and improve drag-drop functionality
  const customRendererOptions = {
    menuLimit: 500, // Increase menu item limit
    tableColorScaleGenerator: (values: any[]) => {
      // Custom color scale that works well in both light and dark modes
      return (x: number) => {
        const colorScale = theme.palette.mode === 'dark' ? 
          ['#1a237e', '#283593', '#3949ab', '#5c6bc0', '#7986cb'] :
          ['#e3f2fd', '#bbdefb', '#90caf9', '#64b5f6', '#42a5f5'];
        const percentile = values.indexOf(x) / values.length;
        const idx = Math.min(Math.floor(percentile * colorScale.length), colorScale.length - 1);
        return { backgroundColor: colorScale[idx] };
      };
    },
    unusedAttrsVertical: false, // Display unused attributes horizontally
    showUI: true, // Always show the UI elements
    hiddenAttributes: [], // Don't hide any attributes
    hiddenFromAggregators: [], // Don't hide any attributes from aggregators
    hiddenFromDragDrop: [], // Don't hide any attributes from drag and drop
    sorters: {}, // Default sorters
    rowTotal: true, // Show row totals
    colTotal: true, // Show column totals
    autoSortUnusedAttrs: false, // Don't auto-sort unused attributes
    onRefresh: (pivotState: any) => {
      // Force re-render when state changes to ensure UI updates properly
      setPivotState({...pivotState});
    }
  };


  return (
    <Box sx={{ 
      p: { xs: 1, sm: 2, md: 3 }, // Responsive padding
      width: '100%'
    }}>
      <PageTitle title="Pivot Analysis" description="Interactively summarize, analyze, and visualize your data using pivot tables and charts." />

      <Card sx={{ mb: { xs: 2, md: 3 }, mt: { xs: 1, md: 2 } }}>
        <CardContent sx={{ p: { xs: 2, md: 3 } }}>
          <Typography variant="h6" gutterBottom>
            1. Select Dataset
          </Typography>
          <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <Box sx={{ flexGrow: 1 }}>
              <DatasetSelector
                value={currentDataset?.id || ''}
                onChange={(datasetId: string) => {
                  const selected = datasets.find(ds => ds.id === datasetId);
                  setCurrentDataset(selected || null);
                }}
                variant="default"
                showActions={true}
              />
            </Box>
            <GoogleSheetsImportButton size="small" />
          </Stack>
        </CardContent>
      </Card>

      {currentDataset && (
        <Card>
          <CardContent sx={{ p: { xs: 2, md: 3 } }}>
            <Typography variant="h6" gutterBottom>
              2. Configure Pivot Table & Visualization
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="subtitle2">How to use the Pivot Table</Typography>
              <ExpandButton 
                onClick={() => setInstructionsExpanded(!instructionsExpanded)}
                size="small"
                aria-expanded={instructionsExpanded}
                aria-label="toggle instructions"
              >
                {instructionsExpanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
              </ExpandButton>
            </Box>
            
            <Collapse in={instructionsExpanded}>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Grid container spacing={1}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" component="div">
                      <strong>1. Define your analysis structure:</strong>
                      <ul style={{ paddingLeft: '20px', marginTop: '4px', marginBottom: '4px' }}>
                        <li>Drag fields to <strong>Rows</strong> area to group data vertically</li>
                        <li>Drag fields to <strong>Columns</strong> area to group data horizontally</li>
                        <li>Drag fields to <strong>Values</strong> area to calculate metrics</li>
                      </ul>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" component="div">
                      <strong>2. Customize your view:</strong>
                      <ul style={{ paddingLeft: '20px', marginTop: '4px', marginBottom: '4px' }}>
                        <li>Select a <strong>Renderer</strong> to switch between tables and charts</li>
                        <li>Use <strong>Aggregator</strong> to change calculation method (sum, average, etc.)</li>
                        <li>Apply filters by dragging fields to the <strong>Filters</strong> area</li>
                      </ul>
                    </Typography>
                  </Grid>
                </Grid>
              </Alert>
            </Collapse>
            {dataToUseForPivot.length > 0 ? (
              <Box sx={{ 
                overflowX: 'auto', 
                overflowY: 'auto',
                maxWidth: '100%',
                // Removed problematic .pvtUi styles from here
              }}> 
                <Box sx={{ position: 'relative', mb: 2 }}>
                  <Tooltip 
                    title={
                      <Typography variant="body2">
                        <strong>Tips:</strong>
                        <ul style={{ paddingLeft: '15px', margin: '5px 0' }}>
                          <li>Click and drag fields between areas</li>
                          <li>Click on values in the table to filter data</li>
                          <li>Right-click on attributes for more options</li>
                          <li>Double-click on a cell to see underlying data</li>
                        </ul>
                      </Typography>
                    } 
                    placement="top-start"
                    arrow
                  >
                    <Box sx={{ 
                      position: 'absolute', 
                      top: '-5px', 
                      right: '5px', 
                      zIndex: 10,
                      bgcolor: 'background.paper',
                      borderRadius: '50%',
                      p: 0.5,
                      border: '1px solid',
                      borderColor: 'divider'
                    }}>
                      <InfoIcon color="info" fontSize="small" />
                    </Box>
                  </Tooltip>
                  <Box>
                    <PivotTableUI
                      data={dataToUseForPivot}
                      onChange={(s: Partial<PivotTableUIProps>) => setPivotState(s)}
                      renderers={renderers}
                      {...pivotState} 
                      rendererName={pivotState.rendererName || "Table"}
                      {...customRendererOptions}
                    />
                  </Box>
                </Box>
              </Box>
            ) : (
              <Typography sx={{ mt: 2, textAlign: 'center', color: 'text.secondary' }}>
                The selected dataset is empty. Please choose a dataset with data to perform pivot analysis.
              </Typography>
            )}
          </CardContent>
        </Card>
      )}
      {!currentDataset && (
         <Paper sx={{ p: { xs: 2, md: 3 }, mt: 2, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary">
                Please select a dataset to begin pivot analysis.
            </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default PivotAnalysisViewer;
