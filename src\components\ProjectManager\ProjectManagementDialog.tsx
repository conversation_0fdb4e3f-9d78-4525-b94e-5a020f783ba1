import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  TextField,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Typography,
  Box,
  Chip,
  Divider,
  Alert,
  CircularProgress,
  Tooltip
} from '@mui/material';
import {
  Folder as FolderIcon,
  Cloud as CloudIcon,
  Delete as DeleteIcon,
  CloudUpload as CloudUploadIcon,
  CloudDownload as CloudDownloadIcon,
  Add as AddIcon,
  Close as CloseIcon,
  Sync as SyncIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useResults, Project } from '../../context/ResultsContext';
import { useAuth } from '../../context/AuthContext';
import { SavedProject } from '../../utils/services/projectService';

interface ProjectManagementDialogProps {
  open: boolean;
  onClose: () => void;
}

const ProjectManagementDialog: React.FC<ProjectManagementDialogProps> = ({
  open,
  onClose
}) => {
  const {
    projects,
    createProject,
    deleteProject,
    getProjectResults,
    saveProjectToCloud,
    loadProjectFromCloud,
    listCloudProjects,
    syncProjects,
    syncStatus,
    syncErrors,
    manualSyncProject
  } = useResults();
  const { canAccessProFeatures } = useAuth();

  const [newProjectName, setNewProjectName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cloudProjects, setCloudProjects] = useState<SavedProject[]>([]);
  const [showCreateForm, setShowCreateForm] = useState(false);

  useEffect(() => {
    if (open && canAccessProFeatures) {
      loadCloudProjects();
      syncProjects();
    }
  }, [open, canAccessProFeatures]);

  const loadCloudProjects = async () => {
    try {
      setLoading(true);
      const projects = await listCloudProjects();
      setCloudProjects(projects);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load cloud projects');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProject = async () => {
    if (!newProjectName.trim()) {
      setError('Project name is required');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      await createProject(newProjectName.trim());
      setNewProjectName('');
      setShowCreateForm(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create project');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    if (projectId === 'default') {
      setError('Cannot delete the default project');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      await deleteProject(projectId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete project');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveToCloud = async (projectId: string) => {
    try {
      setLoading(true);
      setError(null);
      await saveProjectToCloud(projectId);
      await loadCloudProjects(); // Refresh cloud projects list
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save project to cloud');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadFromCloud = async (cloudProjectId: string) => {
    try {
      setLoading(true);
      setError(null);
      await loadProjectFromCloud(cloudProjectId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load project from cloud');
    } finally {
      setLoading(false);
    }
  };

  const handleManualSync = async (projectId: string) => {
    try {
      setError(null);
      await manualSyncProject(projectId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sync project');
    }
  };

  const getProjectIcon = (project: Project) => {
    return project.isLocal ? <FolderIcon /> : <CloudIcon color="primary" />;
  };

  const getSyncIcon = (projectId: string) => {
    const status = syncStatus[projectId];
    switch (status) {
      case 'syncing':
        return <CircularProgress size={16} />;
      case 'success':
        return <CheckCircleIcon color="success" fontSize="small" />;
      case 'error':
        return <ErrorIcon color="error" fontSize="small" />;
      default:
        return null;
    }
  };

  const localProjects = projects.filter(p => p.isLocal);
  const cloudProjectsLocal = projects.filter(p => !p.isLocal);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Project Management</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {!canAccessProFeatures && (
          <Alert severity="info" sx={{ mb: 2 }}>
            Project organization is available for Pro users. Upgrade to organize your results into projects.
          </Alert>
        )}

        {/* Local Projects Section */}
        <Box mb={3}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">Local Projects</Typography>
            {canAccessProFeatures && (
              <Button
                startIcon={<AddIcon />}
                onClick={() => setShowCreateForm(true)}
                variant="outlined"
                size="small"
              >
                New Project
              </Button>
            )}
          </Box>

          {showCreateForm && (
            <Box mb={2} p={2} border={1} borderColor="divider" borderRadius={1}>
              <TextField
                fullWidth
                label="Project Name"
                value={newProjectName}
                onChange={(e) => setNewProjectName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleCreateProject()}
                disabled={loading}
                size="small"
                sx={{ mb: 2 }}
              />
              <Box display="flex" gap={1}>
                <Button
                  onClick={handleCreateProject}
                  variant="contained"
                  size="small"
                  disabled={loading || !newProjectName.trim()}
                >
                  Create
                </Button>
                <Button
                  onClick={() => {
                    setShowCreateForm(false);
                    setNewProjectName('');
                  }}
                  size="small"
                >
                  Cancel
                </Button>
              </Box>
            </Box>
          )}

          <List dense>
            {localProjects.map((project) => (
              <ListItem key={project.id}>
                <ListItemIcon>
                  {getProjectIcon(project)}
                </ListItemIcon>
                <ListItemText
                  primary={project.name}
                  secondary={`${getProjectResults(project.id).length} results • ${project.lastModified.toLocaleDateString()}`}
                />
                <Box display="flex" alignItems="center" gap={1}>
                  <Chip
                    label={getProjectResults(project.id).length}
                    size="small"
                    variant="outlined"
                  />
                  {canAccessProFeatures && project.id !== 'default' && (
                    <>
                      <Tooltip title="Save to Cloud">
                        <IconButton
                          size="small"
                          onClick={() => handleSaveToCloud(project.id)}
                          disabled={loading}
                        >
                          <CloudUploadIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Project">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteProject(project.id)}
                          disabled={loading}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </>
                  )}
                </Box>
              </ListItem>
            ))}
          </List>
        </Box>

        {canAccessProFeatures && (
          <>
            <Divider sx={{ my: 2 }} />

            {/* Cloud Projects Section */}
            <Box>
              <Typography variant="h6" mb={2}>Cloud Projects</Typography>
              
              {loading && (
                <Box display="flex" justifyContent="center" p={2}>
                  <CircularProgress size={24} />
                </Box>
              )}

              <List dense>
                {cloudProjectsLocal.map((project) => (
                  <ListItem key={project.id}>
                    <ListItemIcon>
                      {getProjectIcon(project)}
                    </ListItemIcon>
                    <ListItemText
                      primary={project.name}
                      secondary={`${getProjectResults(project.id).length} results • Cloud`}
                    />
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip
                        label={getProjectResults(project.id).length}
                        size="small"
                        variant="outlined"
                        color="primary"
                      />
                      {getSyncIcon(project.id)}
                      {syncErrors[project.id] && (
                        <Tooltip title={syncErrors[project.id]}>
                          <ErrorIcon color="error" fontSize="small" />
                        </Tooltip>
                      )}
                      <Tooltip title="Sync Project">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleManualSync(project.id)}
                          disabled={loading || syncStatus[project.id] === 'syncing'}
                        >
                          <SyncIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Project">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteProject(project.id)}
                          disabled={loading}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </ListItem>
                ))}

                {/* Available cloud projects not yet loaded */}
                {cloudProjects
                  .filter(cp => !cloudProjectsLocal.some(lp => lp.cloudProject?.id === cp.id))
                  .map((cloudProject) => (
                    <ListItem key={cloudProject.id}>
                      <ListItemIcon>
                        <CloudIcon color="action" />
                      </ListItemIcon>
                      <ListItemText
                        primary={cloudProject.project_name}
                        secondary={`Available in cloud • ${new Date(cloudProject.created_at).toLocaleDateString()}`}
                      />
                      <Tooltip title="Load from Cloud">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleLoadFromCloud(cloudProject.id)}
                          disabled={loading}
                        >
                          <CloudDownloadIcon />
                        </IconButton>
                      </Tooltip>
                    </ListItem>
                  ))}
              </List>

              {cloudProjects.length === 0 && !loading && (
                <Typography variant="body2" color="text.secondary" textAlign="center" py={2}>
                  No cloud projects found. Save a local project to cloud to get started.
                </Typography>
              )}
            </Box>
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProjectManagementDialog;
