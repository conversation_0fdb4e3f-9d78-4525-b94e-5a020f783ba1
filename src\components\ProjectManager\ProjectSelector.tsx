import React, { useState } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Chip,
  Typography,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  Cloud as CloudIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { useResults, Project } from '../../context/ResultsContext';
import { useAuth } from '../../context/AuthContext';

interface ProjectSelectorProps {
  onCreateProject?: () => void;
  showCreateButton?: boolean;
  size?: 'small' | 'medium';
  fullWidth?: boolean;
}

const ProjectSelector: React.FC<ProjectSelectorProps> = ({
  onCreateProject,
  showCreateButton = true,
  size = 'medium',
  fullWidth = true
}) => {
  const { projects, currentProjectId, setCurrentProject, getProjectResults } = useResults();
  const { canAccessProFeatures } = useAuth();

  const handleProjectChange = (event: SelectChangeEvent<string>) => {
    const projectId = event.target.value;
    setCurrentProject(projectId === 'default' ? null : projectId);
  };

  const getProjectIcon = (project: Project) => {
    if (project.id === currentProjectId) {
      return <FolderOpenIcon fontSize="small" />;
    }
    if (!project.isLocal) {
      return <CloudIcon fontSize="small" />;
    }
    return <FolderIcon fontSize="small" />;
  };

  const getProjectLabel = (project: Project) => {
    const resultCount = getProjectResults(project.id).length;
    return `${project.name} (${resultCount})`;
  };

  // For non-Pro users, don't show project selector if there's only the default project
  if (!canAccessProFeatures && projects.length <= 1) {
    return null;
  }

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <FormControl size={size} fullWidth={fullWidth}>
        <InputLabel id="project-selector-label">Project</InputLabel>
        <Select
          labelId="project-selector-label"
          id="project-selector"
          value={currentProjectId || 'default'}
          label="Project"
          onChange={handleProjectChange}
        >
          {projects.map((project) => (
            <MenuItem key={project.id} value={project.id}>
              <Box display="flex" alignItems="center" gap={1} width="100%">
                {getProjectIcon(project)}
                <Typography variant="body2" sx={{ flexGrow: 1 }}>
                  {project.name}
                </Typography>
                <Chip
                  label={getProjectResults(project.id).length}
                  size="small"
                  variant="outlined"
                  sx={{ minWidth: 'auto', height: 20 }}
                />
                {!project.isLocal && (
                  <Tooltip title="Cloud Project">
                    <CloudIcon fontSize="small" color="primary" />
                  </Tooltip>
                )}
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {showCreateButton && canAccessProFeatures && onCreateProject && (
        <Tooltip title="Create New Project">
          <IconButton
            color="primary"
            onClick={onCreateProject}
            size={size}
          >
            <AddIcon />
          </IconButton>
        </Tooltip>
      )}
    </Box>
  );
};

export default ProjectSelector;
