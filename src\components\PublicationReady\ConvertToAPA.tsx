import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Snackbar,
  IconButton,
  Tooltip,
  Chip,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  ToggleButton,
  ToggleButtonGroup,
  useMediaQuery,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  FormLabel,
  Divider
} from '@mui/material';
import {
  TableChart as TableChartIcon,
  ContentCopy as ContentCopyIcon,
  Download as DownloadIcon,
  CloudUpload as CloudUploadIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  Preview as PreviewIcon,
  Transform as TransformIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  ViewCarousel as ViewCarouselIcon,
  Clear as ClearIcon,
  Psychology as PsychologyIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`apa-tabpanel-${index}`}
      aria-labelledby={`apa-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface ParsedTable {
  headers: string[];
  rows: string[][];
  tableNumber?: string;
  tableTitle?: string;
  tableNote?: string;
}

interface APAFormattingOptions {
  tableNumber: string;
  tableTitle: string;
  tableNote: string;
  includeStatistics: boolean;
  italicizeStatistics: boolean;
  rightAlignNumbers: boolean;
  includeSignificance: boolean;
  significanceThreshold: number;
  decimalPlaces: number;
  includeConfidenceIntervals: boolean;
}

const ConvertToAPA: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));
  
  // State for input and options
  const [rawInput, setRawInput] = useState<string>('');
  const [inputFormat, setInputFormat] = useState<'auto' | 'csv' | 'tsv' | 'spaces'>('auto');
  const [parsedTable, setParsedTable] = useState<ParsedTable | null>(null);
  
  // State for APA formatting options
  const [apaOptions, setApaOptions] = useState<APAFormattingOptions>({
    tableNumber: '1',
    tableTitle: 'Results of Statistical Analysis',
    tableNote: 'Note. *p < .05. **p < .01. ***p < .001.',
    includeStatistics: true,
    italicizeStatistics: true,
    rightAlignNumbers: true,
    includeSignificance: true,
    significanceThreshold: 0.05,
    decimalPlaces: 3,
    includeConfidenceIntervals: false
  });
  
  // State for view mode
  const [viewMode, setViewMode] = useState<'tabs' | 'stacked' | 'side-by-side'>('tabs');
  const [activeTab, setActiveTab] = useState<number>(0);
  
  // State for processing
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Set default view mode based on screen size
  useEffect(() => {
    if (isMobile) {
      setViewMode('tabs');
    } else if (isTablet) {
      setViewMode('stacked');
    }
  }, [isMobile, isTablet]);

  // Sample data for demonstration
  const sampleData = `Variable,Mean,SD,t,df,p,95% CI
Condition A,12.45,2.34,3.42,28,.002,"[8.21, 16.69]"
Condition B,8.76,1.98,-2.11,28,.045,"[4.52, 13.00]"
Condition C,15.23,3.12,4.67,28,.001,"[11.99, 18.47]"`;

  // Handle view mode change
  const handleViewModeChange = (
    event: React.MouseEvent<HTMLElement>,
    newViewMode: 'tabs' | 'stacked' | 'side-by-side' | null,
  ) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
      setActiveTab(0);
    }
  };

  // Clear all data
  const clearAll = () => {
    setRawInput('');
    setParsedTable(null);
    setError(null);
  };

  // Load sample data
  const loadSampleData = () => {
    setRawInput(sampleData);
    parseTable(sampleData);
  };

  // Parse input table data
  const parseTable = (input: string = rawInput) => {
    if (!input.trim()) {
      setError('Please enter some table data to parse.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let delimiter: string | RegExp = ',';
      
      // Auto-detect delimiter
      if (inputFormat === 'auto') {
        const commas = (input.match(/,/g) || []).length;
        const tabs = (input.match(/\t/g) || []).length;
        const spaces = (input.match(/\s+/g) || []).length; // Match one or more spaces
        
        if (tabs > commas && tabs > spaces) {
          delimiter = '\t';
        } else if (spaces > commas && spaces > tabs) {
          delimiter = /\s+/; // Use one or more spaces as delimiter
        } else {
          delimiter = ',';
        }
      } else if (inputFormat === 'tsv') {
        delimiter = '\t';
      } else if (inputFormat === 'spaces') {
        delimiter = /\s+/; // Use one or more spaces as delimiter
      }

      const lines = input.trim().split('\n');
      
      if (lines.length < 2) {
        throw new Error('Table must have at least a header row and one data row.');
      }

      // Helper function to parse a single line, handling quoted fields
      const parseLine = (line: string, currentDelimiter: string | RegExp): string[] => {
        if (typeof currentDelimiter === 'string' && currentDelimiter === ',') {
          // Regex to split CSV by commas that are NOT inside double quotes
          const csvSplitRegex = /,(?=(?:[^"]*"[^"]*")*[^"]*$)/;
          const cells = line.split(csvSplitRegex).map(cell => {
            // Remove leading/trailing whitespace and quotes
            cell = cell.trim();
            if (cell.startsWith('"') && cell.endsWith('"')) {
              cell = cell.substring(1, cell.length - 1).replace(/""/g, '"'); // Unescape double quotes
            }
            return cell;
          });
          return cells.filter(cell => cell !== '');
        } else {
          const cells = line.split(currentDelimiter).map(cell => cell.trim().replace(/['"]/g, ''));
          
          // If the first cell is empty (due to leading delimiter), preserve it for alignment.
          // Filter out other empty cells.
          if (cells.length > 0 && cells[0] === '') {
            return [cells[0], ...cells.slice(1).filter(cell => cell !== '')];
          }
          return cells.filter(cell => cell !== '');
        }
      };

      // Parse headers
      const headers = parseLine(lines[0], delimiter);
      
      // Parse data rows
      const rows: string[][] = [];
      for (let i = 1; i < lines.length; i++) {
        const row = parseLine(lines[i], delimiter);
        if (row.length === headers.length) {
          rows.push(row);
        }
      }

      if (rows.length === 0) {
        throw new Error('No valid data rows found.');
      }

      const parsed: ParsedTable = {
        headers,
        rows,
        tableNumber: apaOptions.tableNumber,
        tableTitle: apaOptions.tableTitle,
        tableNote: apaOptions.tableNote
      };

      setParsedTable(parsed);
      setLoading(false);
      setSnackbarMessage('Table parsed successfully!');
      setSnackbarOpen(true);
    } catch (err) {
      setError(`Error parsing table: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  };

  // Check if a value is numeric
  const isNumeric = (value: string): boolean => {
    const cleaned = value.replace(/[,\[\]()]/g, '');
    return !isNaN(parseFloat(cleaned)) && isFinite(parseFloat(cleaned));
  };

  // Check if a value is a statistical term that should be italicized
  const isStatisticalTerm = (value: string): boolean => {
    const statTerms = ['M', 'SD', 't', 'F', 'r', 'p', 'df', 'CI', 'β', 'χ²', 'd', 'η²'];
    return statTerms.some(term => value.toLowerCase().includes(term.toLowerCase()));
  };

  // Format a numeric value according to APA style
  const formatNumericValue = (value: string): string => {
    // Check if it's a CI string (e.g., "[X, Y]")
    if (value.match(/^\[\s*[-+]?\d*\.?\d+(?:e[-+]?\d+)?\s*,\s*[-+]?\d*\.?\d+(?:e[-+]?\d+)?\s*\]$/)) {
      // If it's a CI, format its components but keep the brackets
      const parts = value.substring(1, value.length - 1).split(',').map(s => {
        const num = parseFloat(s.trim());
        return isNaN(num) ? s.trim() : num.toFixed(apaOptions.decimalPlaces);
      });
      return `[${parts.join(', ')}]`;
    }

    if (!isNumeric(value)) return value; // Only process as a single number if it truly is one
    
    const cleaned = value.replace(/[,\[\]()]/g, '');
    const num = parseFloat(cleaned);
    
    // Handle p-values specially
    if (value.toLowerCase().includes('p')) {
      if (num < 0.001) return '< .001';
      if (num < 1) return num.toFixed(3).replace(/^0/, '');
      return num.toFixed(apaOptions.decimalPlaces);
    }
    
    return num.toFixed(apaOptions.decimalPlaces);
  };

  // Add significance indicators
  const addSignificanceIndicators = (value: string, pValue?: number): string => {
    if (!apaOptions.includeSignificance || !pValue) return value;
    
    if (pValue < 0.001) return value + '***';
    if (pValue < 0.01) return value + '**';
    if (pValue < apaOptions.significanceThreshold) return value + '*';
    
    return value;
  };

  // Copy to clipboard
  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setSnackbarMessage('Table copied to clipboard!');
      setSnackbarOpen(true);
    } catch (err) {
      setSnackbarMessage('Failed to copy to clipboard');
      setSnackbarOpen(true);
    }
  };

  // Generate APA formatted text
  const generateAPAText = (): string => {
    if (!parsedTable) return '';

    let apaText = `Table ${apaOptions.tableNumber}\n\n`;
    apaText += `${apaOptions.tableTitle}\n\n`;
    
    // Create the table
    const headers = parsedTable.headers;
    const maxWidths = headers.map((header, index) => {
      const headerWidth = header.length;
      const dataWidths = parsedTable.rows.map(row => (row[index] || '').length);
      return Math.max(headerWidth, ...dataWidths);
    });

    // Header row
    apaText += headers.map((header, index) => header.padEnd(maxWidths[index])).join('  ') + '\n';
    apaText += headers.map((_, index) => '-'.repeat(maxWidths[index])).join('  ') + '\n';

    // Data rows
    parsedTable.rows.forEach(row => {
      const formattedRow = row.map((cell, index) => {
        let formatted = cell;
        if (apaOptions.rightAlignNumbers && isNumeric(cell)) {
          formatted = formatNumericValue(cell);
          return formatted.padStart(maxWidths[index]);
        }
        return formatted.padEnd(maxWidths[index]);
      });
      apaText += formattedRow.join('  ') + '\n';
    });

    if (apaOptions.tableNote) {
      apaText += `\n${apaOptions.tableNote}`;
    }

    return apaText;
  };

  // Download as text file
  const downloadAsText = () => {
    const content = generateAPAText();
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `apa_table_${apaOptions.tableNumber}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    setSnackbarMessage('Table downloaded successfully!');
    setSnackbarOpen(true);
  };

  // Render raw input section
  const renderInputSection = () => (
    <Box>
      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
        Input Table Data
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth size="small">
            <InputLabel>Input Format</InputLabel>
            <Select
              value={inputFormat}
              label="Input Format"
              onChange={(e) => setInputFormat(e.target.value as any)}
            >
              <MenuItem value="auto">Auto-detect</MenuItem>
              <MenuItem value="csv">Comma-separated (CSV)</MenuItem>
              <MenuItem value="tsv">Tab-separated (TSV)</MenuItem>
              <MenuItem value="spaces">Space-separated</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Stack direction="row" spacing={1}>
            <Button
              variant="outlined"
              size="small"
              onClick={loadSampleData}
              startIcon={<SchoolIcon />}
              sx={{ textTransform: 'none' }}
            >
              Load Sample
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={clearAll}
              startIcon={<ClearIcon />}
              sx={{ textTransform: 'none' }}
            >
              Clear All
            </Button>
          </Stack>
        </Grid>
      </Grid>

      <TextField
        fullWidth
        multiline
        rows={8}
        placeholder="Paste your table data here...

Example formats:
• CSV: Variable,Mean,SD,p
• TSV: Variable	Mean	SD	p
• Spaces: Variable    Mean    SD    p"
        value={rawInput}
        onChange={(e) => setRawInput(e.target.value)}
        variant="outlined"
        sx={{ 
          mb: 2,
          '& .MuiInputBase-root': {
            fontFamily: 'monospace',
            fontSize: '0.875rem'
          }
        }}
      />

      <Button
        variant="contained"
        startIcon={<TransformIcon />}
        onClick={() => parseTable()}
        disabled={!rawInput.trim() || loading}
        sx={{ 
          borderRadius: 2,
          textTransform: 'none',
          fontWeight: 600
        }}
      >
        {loading ? 'Parsing...' : 'Parse Table'}
      </Button>
    </Box>
  );

  // Render APA options section
  const renderAPAOptions = () => (
    <Box>
      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
        APA Formatting Options
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Stack spacing={2}>
            <TextField
              fullWidth
              label="Table Number"
              value={apaOptions.tableNumber}
              onChange={(e) => setApaOptions(prev => ({ ...prev, tableNumber: e.target.value }))}
              size="small"
            />
            
            <TextField
              fullWidth
              label="Table Title"
              value={apaOptions.tableTitle}
              onChange={(e) => setApaOptions(prev => ({ ...prev, tableTitle: e.target.value }))}
              size="small"
            />
            
            <TextField
              fullWidth
              multiline
              rows={2}
              label="Table Note"
              value={apaOptions.tableNote}
              onChange={(e) => setApaOptions(prev => ({ ...prev, tableNote: e.target.value }))}
              size="small"
              placeholder="Note. *p < .05. **p < .01. ***p < .001."
            />
          </Stack>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Grid container spacing={1}> {/* Use a nested Grid for better control */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.includeStatistics}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, includeStatistics: e.target.checked }))}
                  />
                }
                label="Include Statistical Formatting"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.italicizeStatistics}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, italicizeStatistics: e.target.checked }))}
                  />
                }
                label="Italicize Statistical Terms"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.rightAlignNumbers}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, rightAlignNumbers: e.target.checked }))}
                  />
                }
                label="Right-align Numbers"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.includeSignificance}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, includeSignificance: e.target.checked }))}
                  />
                }
                label="Add Significance Indicators"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                type="number"
                label="Decimal Places"
                value={apaOptions.decimalPlaces}
                onChange={(e) => setApaOptions(prev => ({ ...prev, decimalPlaces: parseInt(e.target.value) || 3 }))}
                size="small"
                inputProps={{ min: 0, max: 6 }}
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );

  // Render preview table
  const renderPreviewTable = () => {
    if (!parsedTable) return null;

    return (
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            APA Formatted Table Preview
          </Typography>
          
          <Stack direction="row" spacing={1}>
            <Button
              variant="outlined"
              size="small"
              startIcon={<ContentCopyIcon />}
              onClick={() => copyToClipboard(generateAPAText())}
              sx={{ textTransform: 'none' }}
            >
              Copy Table
            </Button>
            <Button
              variant="outlined"
              size="small"
              startIcon={<DownloadIcon />}
              onClick={downloadAsText}
              sx={{ textTransform: 'none' }}
            >
              Download
            </Button>
          </Stack>
        </Box>

        <Paper variant="outlined" sx={{ p: 3, borderRadius: 2 }}>
          {/* Table Number and Title */}
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
            Table {apaOptions.tableNumber}
          </Typography>
          <Typography variant="subtitle1" sx={{ fontStyle: 'italic', mb: 3 }}>
            {apaOptions.tableTitle}
          </Typography>

          {/* The actual table */}
          <TableContainer>
            <Table size="small" sx={{ '& .MuiTableCell-root': { border: 'none', borderBottom: '1px solid #ddd' } }}>
              <TableHead>
                <TableRow sx={{ borderTop: '2px solid #000', borderBottom: '1px solid #000' }}>
                  {parsedTable.headers.map((header, index) => (
                    <TableCell 
                      key={index}
                      sx={{ 
                        fontWeight: 600,
                        fontStyle: apaOptions.italicizeStatistics && isStatisticalTerm(header) ? 'italic' : 'normal',
                        textAlign: index === 0 ? 'left' : (apaOptions.rightAlignNumbers ? 'right' : 'left')
                      }}
                    >
                      {header}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {parsedTable.rows.map((row, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {row.map((cell, cellIndex) => (
                      <TableCell 
                        key={cellIndex}
                        sx={{ 
                          textAlign: cellIndex === 0 ? 'left' : (apaOptions.rightAlignNumbers && isNumeric(cell) ? 'right' : 'left'),
                          fontStyle: apaOptions.italicizeStatistics && isStatisticalTerm(cell) ? 'italic' : 'normal'
                        }}
                      >
                        {apaOptions.rightAlignNumbers && isNumeric(cell) ? formatNumericValue(cell) : cell}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Table Note */}
          {apaOptions.tableNote && (
            <Typography variant="body2" sx={{ mt: 2, fontStyle: 'italic' }}>
              {apaOptions.tableNote}
            </Typography>
          )}
        </Paper>

        {/* Text preview */}
        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
            Text Format Preview
          </Typography>
          <Paper 
            variant="outlined" 
            sx={{ 
              p: 2, 
              backgroundColor: theme.palette.grey[50],
              borderRadius: 2
            }}
          >
            <Typography 
              component="pre" 
              variant="body2" 
              sx={{ 
                fontFamily: 'monospace',
                whiteSpace: 'pre-wrap',
                fontSize: '0.875rem'
              }}
            >
              {generateAPAText()}
            </Typography>
          </Paper>
        </Box>
      </Box>
    );
  };

  return (
    <PublicationReadyGate>
      <Box sx={{ p: { xs: 2, md: 3 }, maxWidth: 1400, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
          Convert to APA Table Format
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Transform your raw data tables into properly formatted APA-style tables for academic publications
        </Typography>
      </Box>

      {/* Configuration Panel */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardHeader 
          title="Table Converter"
          avatar={<TableChartIcon color="primary" />}
          sx={{ pb: 1 }}
        />
        <CardContent>
          {/* View Mode Selection */}
          {parsedTable && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
                Display Mode
              </Typography>
              <ToggleButtonGroup
                value={viewMode}
                exclusive
                onChange={handleViewModeChange}
                size="small"
                sx={{ mb: 2 }}
              >
                <ToggleButton value="tabs" aria-label="tabs view">
                  <ViewCarouselIcon sx={{ mr: 1 }} />
                  {!isMobile && 'Tabs'}
                </ToggleButton>
                <ToggleButton value="stacked" aria-label="stacked view">
                  <ViewListIcon sx={{ mr: 1 }} />
                  {!isMobile && 'Stacked'}
                </ToggleButton>
                {!isMobile && (
                  <ToggleButton value="side-by-side" aria-label="side by side view">
                    <ViewModuleIcon sx={{ mr: 1 }} />
                    Side by Side
                  </ToggleButton>
                )}
              </ToggleButtonGroup>
            </Box>
          )}

          {/* APA Guidelines Information */}
          <Accordion defaultExpanded={false}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                APA Table Guidelines
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" color="text.secondary" paragraph>
                This tool helps format tables according to APA 7th edition guidelines:
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Table number and title:</strong> Above the table, flush left
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Column headers:</strong> Brief, descriptive labels
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Number alignment:</strong> Right-aligned for consistency
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Statistical symbols:</strong> Italicized (M, SD, p, etc.)
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>P-values:</strong> {'Report as p = .xxx or p < .001'}
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Table notes:</strong> Below table for significance levels
                </Typography>
              </Box>
            </AccordionDetails>
          </Accordion>

          {!parsedTable ? (
            <Box sx={{ mt: 3 }}>
              {renderInputSection()}
            </Box>
          ) : (
            <Box sx={{ mt: 3 }}>
              {viewMode === 'tabs' ? (
                <Box>
                  <Tabs 
                    value={activeTab} 
                    onChange={(e, newValue) => setActiveTab(newValue)}
                    sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
                  >
                    <Tab 
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <SettingsIcon fontSize="small" />
                          {!isMobile && 'Input & Options'}
                        </Box>
                      } 
                    />
                    <Tab 
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <PreviewIcon fontSize="small" />
                          {!isMobile && 'Preview'}
                        </Box>
                      } 
                    />
                  </Tabs>
                  <TabPanel value={activeTab} index={0}>
                    <Grid container spacing={4}>
                      <Grid item xs={12} lg={6}>
                        {renderInputSection()}
                      </Grid>
                      <Grid item xs={12} lg={6}>
                        {renderAPAOptions()}
                      </Grid>
                    </Grid>
                  </TabPanel>
                  <TabPanel value={activeTab} index={1}>
                    {renderPreviewTable()}
                  </TabPanel>
                </Box>
              ) : viewMode === 'stacked' ? (
                <Stack spacing={4}>
                  <Grid container spacing={4}>
                    <Grid item xs={12} lg={6}>
                      {renderInputSection()}
                    </Grid>
                    <Grid item xs={12} lg={6}>
                      {renderAPAOptions()}
                    </Grid>
                  </Grid>
                  <Divider />
                  {renderPreviewTable()}
                </Stack>
              ) : (
                <Grid container spacing={4}>
                  <Grid item xs={12} lg={4}>
                    {renderInputSection()}
                  </Grid>
                  <Grid item xs={12} lg={4}>
                    {renderAPAOptions()}
                  </Grid>
                  <Grid item xs={12} lg={4}>
                    {renderPreviewTable()}
                  </Grid>
                </Grid>
              )}
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <Card elevation={2}>
          <CardContent>
            <Box display="flex" flexDirection="column" alignItems="center" py={4}>
              <CircularProgress size={60} sx={{ mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                Processing Table...
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Parsing and formatting your data
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3, borderRadius: 2 }}
          action={
            <Button color="inherit" size="small" onClick={() => setError(null)}>
              Dismiss
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Empty State */}
      {!loading && !parsedTable && !rawInput && (
        <Card elevation={1}>
          <CardContent>
            <Box textAlign="center" py={6}>
              <TableChartIcon sx={{ fontSize: 80, color: theme.palette.grey[400], mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Ready to Convert
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Paste your raw table data above to get started with APA formatting
              </Typography>
              <Button
                variant="outlined"
                onClick={loadSampleData}
                startIcon={<SchoolIcon />}
                sx={{ textTransform: 'none' }}
              >
                Try with Sample Data
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
    </Box>
    </PublicationReadyGate>
  );
};

export default ConvertToAPA;
