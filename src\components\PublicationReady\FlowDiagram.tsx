import React, { useState, useEffect, useRef } from 'react';
import mermaid from 'mermaid';
import domtoimage from 'dom-to-image';
import './FlowDiagram.css';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';

interface Stage {
  id: string;
  label: string;
  count: number;
  color?: string;
  flowType?: 'straight' | 'branch-right' | 'merge' | 'end';
  connectTo?: string; // ID of stage to connect to (for complex flows)
}

interface DiagramTemplate {
  name: string;
  stages: Omit<Stage, 'id'>[];
}

const FlowDiagram: React.FC = () => {
  const diagramRef = useRef<HTMLDivElement>(null);
  const renderIdRef = useRef(0);

  // States
  const [stages, setStages] = useState<Stage[]>([
    { id: '1', label: 'Assessed for Eligibility', count: 100, color: '#4CAF50', flowType: 'straight' },
    { id: '2', label: 'Excluded', count: 10, color: '#f44336', flowType: 'branch-right' },
    { id: '3', label: 'Randomized', count: 90, color: '#2196F3', flowType: 'straight' },
    { id: '4', label: 'Lost to Follow-up', count: 5, color: '#ff9800', flowType: 'branch-right' },
    { id: '5', label: 'Completed Study', count: 85, color: '#4CAF50', flowType: 'end' },
  ]);
  const [mermaidSyntax, setMermaidSyntax] = useState('');
  const [diagramDirection, setDiagramDirection] = useState('TD');
  const [autoGenerate, setAutoGenerate] = useState(true);
  const [theme, setTheme] = useState<'default' | 'dark' | 'forest' | 'neutral'>('default');



  // Templates
  const templates: DiagramTemplate[] = [
    {
      name: 'Clinical Trial',
      stages: [
        { label: 'Screened for Eligibility', count: 250, color: '#4CAF50', flowType: 'straight' },
        { label: 'Excluded (Not Meeting Criteria)', count: 50, color: '#f44336', flowType: 'branch-right' },
        { label: 'Eligible Participants', count: 200, color: '#2196F3', flowType: 'straight' },
        { label: 'Declined to Participate', count: 20, color: '#ff9800', flowType: 'branch-right' },
        { label: 'Randomized', count: 180, color: '#2196F3', flowType: 'straight' },
        { label: 'Allocated to Intervention', count: 90, color: '#9C27B0', flowType: 'straight' },
        { label: 'Allocated to Control', count: 90, color: '#9C27B0', flowType: 'branch-right', connectTo: '5' },
        { label: 'Lost to Follow-up (Intervention)', count: 5, color: '#ff9800', flowType: 'branch-right' },
        { label: 'Analyzed (Intervention)', count: 85, color: '#4CAF50', flowType: 'straight' },
        { label: 'Lost to Follow-up (Control)', count: 7, color: '#ff9800', flowType: 'branch-right', connectTo: '6' },
        { label: 'Analyzed (Control)', count: 83, color: '#4CAF50', flowType: 'end' },
      ]
    },
    {
      name: 'PRISMA Flow Diagram',
      stages: [
        { label: 'Records identified through database searching', count: 1250, color: '#9C27B0', flowType: 'merge' },
        { label: 'Additional records identified through other sources', count: 75, color: '#9C27B0', flowType: 'merge' },
        { label: 'Records after duplicates removed', count: 980, color: '#2196F3', flowType: 'straight' },
        { label: 'Records screened', count: 980, color: '#2196F3', flowType: 'straight' },
        { label: 'Records excluded', count: 850, color: '#f44336', flowType: 'branch-right' },
        { label: 'Full-text articles assessed for eligibility', count: 130, color: '#2196F3', flowType: 'straight' },
        { label: 'Full-text articles excluded, with reasons', count: 85, color: '#f44336', flowType: 'branch-right' },
        { label: 'Studies included in qualitative synthesis', count: 45, color: '#4CAF50', flowType: 'straight' },
        { label: 'Studies included in quantitative synthesis (meta-analysis)', count: 38, color: '#4CAF50', flowType: 'end' },
      ]
    },
    {
      name: 'Survey Based Studies',
      stages: [
        { label: 'Survey Invitations Sent', count: 5000, color: '#FF5722', flowType: 'straight' },
        { label: 'No Response', count: 3000, color: '#f44336', flowType: 'branch-right' },
        { label: 'Total Responses Received', count: 2000, color: '#FF9800', flowType: 'straight' },
        { label: 'Incomplete Responses', count: 450, color: '#f44336', flowType: 'branch-right' },
        { label: 'Complete Responses', count: 1550, color: '#FFC107', flowType: 'straight' },
        { label: 'Excluded (Failed Quality Checks)', count: 150, color: '#f44336', flowType: 'branch-right' },
        { label: 'Valid Responses', count: 1400, color: '#8BC34A', flowType: 'straight' },
        { label: 'Excluded (Outside Target Population)', count: 200, color: '#f44336', flowType: 'branch-right' },
        { label: 'Final Sample Size', count: 1200, color: '#4CAF50', flowType: 'end' },
      ]
    },
    {
      name: 'Cohort Study',
      stages: [
        { label: 'Initial Population: Total subjects identified', count: 1000, color: '#4CAF50', flowType: 'straight' },
        { label: 'Eligibility Screening: Subjects meeting inclusion criteria', count: 900, color: '#2196F3', flowType: 'straight' },
        { label: 'Subjects excluded (with reasons)', count: 100, color: '#f44336', flowType: 'branch-right' },
        { label: 'Cohort Grouping: Exposed group', count: 450, color: '#9C27B0', flowType: 'straight' },
        { label: 'Non-exposed group', count: 450, color: '#9C27B0', flowType: 'branch-right', connectTo: '3' },
        { label: 'Follow-up: Subjects lost to follow-up (with reasons)', count: 50, color: '#ff9800', flowType: 'branch-right' },
        { label: 'Subjects continuing in the study', count: 850, color: '#FFC107', flowType: 'straight' },
        { label: 'Outcome Assessment: Subjects with the outcome', count: 200, color: '#8BC34A', flowType: 'straight' },
        { label: 'Subjects without the outcome', count: 650, color: '#8BC34A', flowType: 'branch-right', connectTo: '7' },
        { label: 'Analysis: Statistical analysis performed', count: 850, color: '#4CAF50', flowType: 'straight' },
        { label: 'Adjustments for confounders', count: 850, color: '#4CAF50', flowType: 'end' },
      ]
    }
  ];

  // Add new stage
  const addStage = () => {
    const newStage: Stage = {
      id: Date.now().toString(),
      label: '',
      count: 0,
      color: '#2196F3',
      flowType: 'straight'
    };
    setStages([...stages, newStage]);
  };

  // Update stage
  const updateStage = (id: string, field: keyof Stage, value: any) => {
    const newStages = stages.map(stage => 
      stage.id === id ? { ...stage, [field]: value } : stage
    );
    setStages(newStages);
  };

  // Delete stage
  const deleteStage = (id: string) => {
    if (stages.length > 1) {
      setStages(stages.filter(stage => stage.id !== id));
    }
  };

  // Move stage up
  const moveStageUp = (index: number) => {
    if (index > 0) {
      const newStages = [...stages];
      [newStages[index - 1], newStages[index]] = [newStages[index], newStages[index - 1]];
      setStages(newStages);
    }
  };

  // Move stage down
  const moveStageDown = (index: number) => {
    if (index < stages.length - 1) {
      const newStages = [...stages];
      [newStages[index], newStages[index + 1]] = [newStages[index + 1], newStages[index]];
      setStages(newStages);
    }
  };

  // Load template
  const loadTemplate = (template: DiagramTemplate) => {
    const newStages = template.stages.map((stage, index) => ({
      ...stage,
      id: Date.now().toString() + index
    }));
    setStages(newStages);
  };

  // Clear all stages
  const clearStages = () => {
    setStages([{ id: Date.now().toString(), label: '', count: 0, color: '#2196F3', flowType: 'straight' }]);
    setMermaidSyntax('');
  };

  // Helper function to wrap text
  const wrapText = (text: string, maxLength: number = 25) => {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    words.forEach(word => {
      if ((currentLine + ' ' + word).trim().length <= maxLength) {
        currentLine = (currentLine + ' ' + word).trim();
      } else {
        if (currentLine) lines.push(currentLine);
        currentLine = word;
      }
    });
    if (currentLine) lines.push(currentLine);

    return lines.join('<br/>');
  };

  // Generate Mermaid syntax with branching support
  const generateMermaidSyntax = () => {
    let syntax = `graph ${diagramDirection}\n`;
    let mainFlowIndex = 0;
    let branchNodes: { [key: string]: number } = {};
    
    stages.forEach((stage, index) => {
      const wrappedLabel = wrapText(stage.label);
      const nodeStyle = stage.color ? `style id${index} fill:${stage.color},stroke:#333,stroke-width:2px,color:#fff` : '';
      syntax += `    id${index}["${wrappedLabel}<br/><b>n=${stage.count}</b>"]\n`;
      if (nodeStyle) {
        syntax += `    ${nodeStyle}\n`;
      }
      
      // Handle connections based on flow type
      if (index > 0) {
        const prevStage = stages[index - 1];
        
        if (stage.flowType === 'merge' && index === 1) {
          // This is the second merge node, it will connect to the merge point later
          branchNodes[stage.id] = index;
        } else if (prevStage.flowType === 'merge' && stages[index - 2]?.flowType === 'merge') {
          // Connect both merge nodes to this node
          syntax += `    id${index - 2} --> id${index}\n`;
          syntax += `    id${index - 1} --> id${index}\n`;
          mainFlowIndex = index;
        } else if (prevStage.flowType === 'branch-right') {
          // Previous node branches right, so connect from the node before that
          if (mainFlowIndex > 0 && stages[mainFlowIndex]) {
            syntax += `    id${mainFlowIndex} --> id${index}\n`;
          }
          mainFlowIndex = index;
        } else if (stage.flowType === 'branch-right') {
          // This node branches to the right from the previous
          syntax += `    id${index - 1} --> id${index}\n`;
        } else {
          // Normal straight connection
          syntax += `    id${index - 1} --> id${index}\n`;
          if (prevStage.flowType && prevStage.flowType !== 'end') {
            mainFlowIndex = index;
          }
        }
      } else if (stage.flowType !== 'merge') {
        mainFlowIndex = index;
      }
    });
    
    setMermaidSyntax(syntax);
  };

  // Copy syntax to clipboard
  const copySyntax = () => {
    navigator.clipboard.writeText(mermaidSyntax);
    alert('Mermaid syntax copied to clipboard!');
  };

  // Export functions
  const exportDiagram = (format: 'png' | 'svg') => {
    if (!diagramRef.current) return;

    if (format === 'png') {
      domtoimage.toPng(diagramRef.current)
        .then(dataUrl => {
          const link = document.createElement('a');
          link.download = 'flow-diagram.png';
          link.href = dataUrl;
          link.click();
        })
        .catch(error => {
          console.error('Export failed:', error);
          alert('Failed to export diagram');
        });
    } else if (format === 'svg') {
      const svg = diagramRef.current.querySelector('svg');
      if (svg) {
        const svgData = new XMLSerializer().serializeToString(svg);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const svgUrl = URL.createObjectURL(svgBlob);
        const link = document.createElement('a');
        link.download = 'flow-diagram.svg';
        link.href = svgUrl;
        link.click();
      }
    }
  };

  // Initialize mermaid
  useEffect(() => {
    mermaid.initialize({ 
      startOnLoad: false,
      theme: theme,
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis',
        nodeSpacing: 50,
        rankSpacing: 50,
        padding: 20
      }
    });
  }, [theme]);

  // Auto-generate diagram
  useEffect(() => {
    if (autoGenerate && stages.every(s => s.label)) {
      generateMermaidSyntax();
    }
  }, [stages, diagramDirection, autoGenerate]);

  // Render diagram
  useEffect(() => {
    const renderDiagram = async () => {
      if (mermaidSyntax && diagramRef.current) {
        try {
          diagramRef.current.innerHTML = '';
          const id = `mermaid-diagram-${renderIdRef.current++}`;
          const { svg } = await mermaid.render(id, mermaidSyntax);
          
          if (diagramRef.current) {
            diagramRef.current.innerHTML = svg;
            // Scroll to top after rendering
            diagramRef.current.parentElement?.scrollTo(0, 0);
          }
        } catch (error: unknown) {
          console.error('Error rendering diagram:', error);
          if (diagramRef.current) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            diagramRef.current.innerHTML = `<p class="error-message">Error rendering diagram: ${errorMessage}</p>`;
          }
        }
      }
    };

    renderDiagram();
  }, [mermaidSyntax]);



  return (
    <PublicationReadyGate>
      <div className="flow-diagram-container">
      <div className="main-layout">
        {/* Left Panel */}
        <div className="left-panel">
          {/* Templates Section */}
          <div className="templates-section">
            <h3>📋 Research Templates</h3>
            <div className="template-grid">
              <button
                onClick={() => loadTemplate(templates[0])}
                className="template-btn"
              >
                Clinical Trial
              </button>
              <button
                onClick={() => loadTemplate(templates[1])}
                className="template-btn"
              >
                PRISMA Flow Diagram
              </button>
              <button
                onClick={() => loadTemplate(templates[2])}
                className="template-btn"
              >
                Survey Based Studies
              </button>
              <button
                onClick={() => loadTemplate(templates[3])}
                className="template-btn"
              >
                Cohort Study
              </button>
              <button
                onClick={clearStages}
                className="template-btn clear-btn"
              >
                Clear All
              </button>
            </div>
          </div>

          {/* Settings Section */}
          <div className="settings-section">
            <h3>⚙️ Diagram Settings</h3>
            <div className="settings-row">
              <label>
                Direction:
                <select 
                  value={diagramDirection} 
                  onChange={(e) => setDiagramDirection(e.target.value)}
                >
                  <option value="TD">Top to Bottom</option>
                  <option value="LR">Left to Right</option>
                </select>
              </label>
              <label>
                Theme:
                <select 
                  value={theme} 
                  onChange={(e) => setTheme(e.target.value as "dark" | "default" | "neutral" | "forest")}
                >
                  <option value="default">Default</option>
                  <option value="dark">Dark</option>
                  <option value="forest">Forest</option>
                  <option value="neutral">Neutral</option>
                </select>
              </label>
            </div>
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={autoGenerate}
                onChange={(e) => setAutoGenerate(e.target.checked)}
              />
              Auto-generate on change
            </label>
          </div>

          {/* Stages Section */}
          <div className="stages-section">
            <div className="stages-header">
              <h3>📊 Study Stages</h3>
              <button onClick={addStage} className="add-btn">+ Add</button>
            </div>
            
            <div className="stages-list">
              {stages.map((stage, index) => (
                <div key={stage.id} className="stage-item">
                  <span className="stage-number">{index + 1}</span>
                  
                  <div className="stage-controls">
                    <button 
                      onClick={() => moveStageUp(index)}
                      disabled={index === 0}
                      className="arrow-btn"
                      title="Move up"
                    >
                      ↑
                    </button>
                    <button 
                      onClick={() => moveStageDown(index)}
                      disabled={index === stages.length - 1}
                      className="arrow-btn"
                      title="Move down"
                    >
                      ↓
                    </button>
                  </div>
                  
                  <div className="stage-content">
                    <input
                      type="text"
                      placeholder="Stage Label"
                      value={stage.label}
                      onChange={(e) => updateStage(stage.id, 'label', e.target.value)}
                      className="stage-label-input"
                    />
                    <div className="stage-row">
                      <input
                        type="number"
                        placeholder="n"
                        value={stage.count}
                        onChange={(e) => updateStage(stage.id, 'count', parseInt(e.target.value, 10) || 0)}
                        className="stage-count-input"
                      />
                      
                      <select
                        value={stage.flowType || 'straight'}
                        onChange={(e) => updateStage(stage.id, 'flowType', e.target.value)}
                        className="flow-type-select"
                        title="Flow direction"
                      >
                        <option value="straight">↓ Straight</option>
                        <option value="branch-right">→ Branch Right</option>
                        <option value="merge">⤵ Merge</option>
                        <option value="end">⬤ End</option>
                      </select>
                      
                      <input
                        type="color"
                        value={stage.color || '#2196F3'}
                        onChange={(e) => updateStage(stage.id, 'color', e.target.value)}
                        className="color-picker"
                      />
                      
                      <button 
                        onClick={() => deleteStage(stage.id)}
                        className="delete-btn"
                        disabled={stages.length === 1}
                        title="Delete"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="flow-tips">
              <p><strong>Flow Types:</strong></p>
              <ul>
                <li><strong>Straight:</strong> Continue down</li>
                <li><strong>Branch Right:</strong> Side branch (exclusions)</li>
                <li><strong>Merge:</strong> Multiple sources joining</li>
              </ul>
            </div>
          </div>

          {!autoGenerate && (
            <button onClick={generateMermaidSyntax} className="generate-btn">
              Generate Diagram
            </button>
          )}
        </div>

        {/* Right Panel - Diagram Preview */}
        <div className="right-panel">
          <div className="preview-header">
            <h3>📈 Diagram Preview</h3>
            <div className="export-buttons">
              <button
                onClick={() => exportDiagram('png')}
                disabled={!mermaidSyntax}
                className="export-btn"
              >
                📷 PNG
              </button>
              <button
                onClick={() => exportDiagram('svg')}
                disabled={!mermaidSyntax}
                className="export-btn"
              >
                📐 SVG
              </button>
              <button
                onClick={copySyntax}
                disabled={!mermaidSyntax}
                className="export-btn"
              >
                📋 Copy Syntax
              </button>

            </div>
          </div>

          <div className="diagram-area">
            <div ref={diagramRef} className="diagram-container">
              {!mermaidSyntax && (
                <div className="empty-state">
                  <p>Select a template or configure stages to begin</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>


    </div>
    </PublicationReadyGate>
  );
};

export default FlowDiagram;
