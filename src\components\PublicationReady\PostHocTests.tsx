import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Snackbar,

  Chip,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  ToggleButton,
  ToggleButtonGroup,
  useMediaQuery,
  Slider,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  FormLabel
} from '@mui/material';
import {
  Psychology as PsychologyIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  Save as SaveIcon,

  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  Assessment as AssessmentIcon,
  Timeline as TimelineIcon,
  ScatterPlot as ScatterPlotIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  ViewCarousel as ViewCarouselIcon,
  CompareArrows as CompareArrowsIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { useResults } from '../../context/ResultsContext';
import AddToResultsButton from '../UI/AddToResultsButton';
import { DataType } from '../../types';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import { getOrderedCategoriesByColumnId, sortObjectsByCategoricalProperty } from '../../utils/dataUtilities';
import * as jStat from 'jstat'; // Reverting to default import
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  ScatterChart,
  Scatter,
  ErrorBar,
  LineChart,
  Line,
  ReferenceLine
} from 'recharts';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`posthoc-tabpanel-${index}`}
      aria-labelledby={`posthoc-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface PostHocResult {
  dependentVariable: any;
  independentVariable: any;
  testMethod: string;
  alpha: number;
  groupStats: {
    [group: string]: {
      n: number;
      mean: number;
      std: number;
      ci_lower: number;
      ci_upper: number;
    };
  };
  pairwiseComparisons: Array<{
    group1: string;
    group2: string;
    meanDiff: number;
    pValue: number;
    adjustedPValue: number;
    significant: boolean;
    ciLower: number;
    ciUpper: number;
    effectSize: number;
  }>;
  overallTest: {
    fStatistic: number;
    pValue: number;
    significant: boolean;
  };
  chartData: {
    meansChart: Array<{
      group: string;
      mean: number;
      ciLower: number;
      ciUpper: number;
      n: number;
    }>;
    comparisonMatrix: Array<{
      group1: string;
      group2: string;
      significant: boolean;
      pValue: number;
    }>;
  };
}

const PostHocTests: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const { addResult } = useResults();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));
  
  // State for analysis options
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [selectedDependentVar, setSelectedDependentVar] = useState<string>('');
  const [selectedIndependentVar, setSelectedIndependentVar] = useState<string>('');
  const [testMethod, setTestMethod] = useState<string>('tukey');
  const [alpha, setAlpha] = useState<number>(0.05);
  const [includeCI, setIncludeCI] = useState<boolean>(true);
  const [includeEffectSize, setIncludeEffectSize] = useState<boolean>(true);
  
  // State for view mode
  const [viewMode, setViewMode] = useState<'tabs' | 'stacked' | 'side-by-side'>('tabs');
  const [activeTab, setActiveTab] = useState<number>(0);
  
  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [postHocResult, setPostHocResult] = useState<PostHocResult | null>(null);
  
  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('posthoc_test_results');
    
    if (savedResults) {
      try {
        setPostHocResult(JSON.parse(savedResults));
      } catch (e) {
        console.error('Error parsing saved PostHoc test results:', e);
      }
    }
  }, []);

  // Set default view mode based on screen size
  useEffect(() => {
    if (isMobile) {
      setViewMode('tabs');
    } else if (isTablet) {
      setViewMode('stacked');
    }
  }, [isMobile, isTablet]);
  
  // Get numeric columns from current dataset
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];
  
  // Get categorical columns from current dataset
  const categoricalColumns = currentDataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL || col.type === DataType.ORDINAL || col.type === DataType.BOOLEAN
  ) || [];
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedDependentVar('');
    setSelectedIndependentVar('');
    setPostHocResult(null);
    
    localStorage.removeItem('posthoc_test_results');
    
    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };
  
  // Handle column selection changes
  const handleDependentVarChange = (event: SelectChangeEvent<string>) => {
    setSelectedDependentVar(event.target.value);
    setPostHocResult(null);
    localStorage.removeItem('posthoc_test_results');
  };

  const handleIndependentVarChange = (event: SelectChangeEvent<string>) => {
    setSelectedIndependentVar(event.target.value);
    setPostHocResult(null);
    localStorage.removeItem('posthoc_test_results');
  };

  // Handle view mode change
  const handleViewModeChange = (
    event: React.MouseEvent<HTMLElement>,
    newViewMode: 'tabs' | 'stacked' | 'side-by-side' | null,
  ) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
      setActiveTab(0);
    }
  };

  // Clear analysis
  const clearAnalysis = () => {
    setPostHocResult(null);
    localStorage.removeItem('posthoc_test_results');
  };

  // Results context for saving results
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Helper functions for statistical calculations
  const calculateMean = (values: number[]): number => {
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  };

  const calculateStd = (values: number[]): number => {
    const mean = calculateMean(values);
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1);
    return Math.sqrt(variance);
  };

  const calculateCI = (mean: number, std: number, n: number, alpha: number): [number, number] => {
    if (n <= 1) return [NaN, NaN]; // Cannot calculate CI for n <= 1
    const degreesOfFreedom = n - 1;
    const tValue = (jStat as any).studentt.inv(1 - alpha / 2, degreesOfFreedom); // Corrected jStat method call
    const margin = tValue * (std / Math.sqrt(n));
    return [mean - margin, mean + margin];
  };

  const calculateCohenD = (mean1: number, mean2: number, pooledStd: number): number => {
    return Math.abs(mean1 - mean2) / pooledStd;
  };

  const adjustPValue = (
    pValues: number[],
    method: string,
    rawPairwiseComparisons: Array<{
      group1: string;
      group2: string;
      meanDiff: number;
      pValue: number;
      effectSize: number;
      ciLower: number;
      ciUpper: number;
      degreesOfFreedom: number;
      se: number;
    }>,
    k: number, // Number of groups
    dfw: number // Degrees of freedom within (from ANOVA)
  ): number[] => {
    if (pValues.length === 0) return [];

    const sortedPValues = pValues
      .map((value, index) => ({ value, originalIndex: index }))
      .sort((a, b) => a.value - b.value);

    const adjustedPValues = new Array(pValues.length);
    let prevAdjustedP = 0;

    switch (method) {
      case 'bonferroni':
        for (let i = 0; i < sortedPValues.length; i++) {
          adjustedPValues[sortedPValues[i].originalIndex] = Math.min(sortedPValues[i].value * pValues.length, 1);
        }
        break;
      case 'holm':
        for (let i = 0; i < sortedPValues.length; i++) {
          const adjusted = sortedPValues[i].value * (pValues.length - i);
          prevAdjustedP = Math.max(prevAdjustedP, adjusted);
          adjustedPValues[sortedPValues[i].originalIndex] = Math.min(prevAdjustedP, 1);
        }
        // Ensure monotonicity (adjusted p-value should not decrease for higher ranks)
        for (let i = sortedPValues.length - 2; i >= 0; i--) {
          const currentOriginalIndex = sortedPValues[i].originalIndex;
          const nextOriginalIndex = sortedPValues[i + 1].originalIndex;
          adjustedPValues[currentOriginalIndex] = Math.max(
            adjustedPValues[currentOriginalIndex],
            adjustedPValues[nextOriginalIndex]
          );
        }
        break;
      case 'tukey':
        // Tukey HSD implementation
        for (let i = 0; i < rawPairwiseComparisons.length; i++) {
          const comp = rawPairwiseComparisons[i];
          // q-statistic = (mean_diff) / (SE / sqrt(2))
          // The SE here is the standard error of the mean difference, which is 'se' in rawPairwiseComparisons
          const qStatistic = Math.abs(comp.meanDiff) / (comp.se / Math.sqrt(2));
          const tukeyPValue = 1 - (jStat as any).tukey.cdf(qStatistic, k, dfw);
          adjustedPValues[i] = tukeyPValue;
        }
        break;
      case 'scheffe':
        // Scheffe's test implementation for pairwise comparisons
        // F-statistic for a contrast is t^2. For Scheffe, we divide by (k-1)
        for (let i = 0; i < rawPairwiseComparisons.length; i++) {
          const comp = rawPairwiseComparisons[i];
          const fStatistic = Math.pow(comp.meanDiff / comp.se, 2); // t-statistic squared
          const scheffePValue = 1 - (jStat as any).centralF.cdf(fStatistic / (k - 1), k - 1, dfw);
          adjustedPValues[i] = scheffePValue;
        }
        break;
      default:
        for (let i = 0; i < pValues.length; i++) {
          adjustedPValues[i] = pValues[i];
        }
        break;
    }
    return adjustedPValues;
  };

  // Run PostHoc analysis
  const runPostHocAnalysis = () => {
    if (!currentDataset || !selectedDependentVar || !selectedIndependentVar) {
      setError('Please select a dataset, dependent variable, and independent variable.');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const dependentCol = currentDataset.columns.find(col => col.id === selectedDependentVar);
      const independentCol = currentDataset.columns.find(col => col.id === selectedIndependentVar);
      
      if (!dependentCol || !independentCol) {
        throw new Error('Selected columns not found in dataset.');
      }
      
      // Group data by independent variable
      const groupedData: { [key: string]: number[] } = {};
      
      currentDataset.data.forEach(row => {
        const groupValue = String(row[independentCol.name] ?? 'Missing');
        const depValue = row[dependentCol.name];
        
        if (typeof depValue === 'number' && !isNaN(depValue)) {
          if (!groupedData[groupValue]) {
            groupedData[groupValue] = [];
          }
          groupedData[groupValue].push(depValue);
        }
      });
      
      // Use ordered categories instead of Object.keys for consistent ordering
      const groups = getOrderedCategoriesByColumnId(selectedIndependentVar, currentDataset);
      
      if (groups.length < 2) {
        throw new Error('Need at least 2 groups for PostHoc analysis.');
      }
      
      // Calculate group statistics
      const groupStats: { [group: string]: any } = {};
      groups.forEach(group => {
        const values = groupedData[group];
        const mean = calculateMean(values);
        const std = calculateStd(values);
        const [ciLower, ciUpper] = calculateCI(mean, std, values.length, alpha);
        
        groupStats[group] = {
          n: values.length,
          mean,
          std,
          ci_lower: ciLower,
          ci_upper: ciUpper
        };
      });
      
      // Calculate pairwise comparisons
      const rawPairwiseComparisons: Array<{
        group1: string;
        group2: string;
        meanDiff: number;
        pValue: number;
        effectSize: number;
        ciLower: number;
        ciUpper: number;
        degreesOfFreedom: number;
        se: number;
      }> = [];
      
      for (let i = 0; i < groups.length; i++) {
        for (let j = i + 1; j < groups.length; j++) {
          const group1 = groups[i];
          const group2 = groups[j];
          
          const values1 = groupedData[group1];
          const values2 = groupedData[group2];
          
          const mean1 = groupStats[group1].mean;
          const mean2 = groupStats[group2].mean;
          const std1 = groupStats[group1].std;
          const std2 = groupStats[group2].std;
          
          const meanDiff = mean1 - mean2;
          
          const pooledStd = Math.sqrt(((values1.length - 1) * std1 * std1 + (values2.length - 1) * std2 * std2) / 
                                    (values1.length + values2.length - 2));
          const se = pooledStd * Math.sqrt(1/values1.length + 1/values2.length);
          const tStat = meanDiff / se;
          const degreesOfFreedom = values1.length + values2.length - 2;
          
          const pValue = 2 * (1 - (jStat as any).studentt.cdf(Math.abs(tStat), degreesOfFreedom));
          
          const effectSize = calculateCohenD(mean1, mean2, pooledStd);
          const ciMargin = (jStat as any).studentt.inv(1 - alpha / 2, degreesOfFreedom) * se;
          
          rawPairwiseComparisons.push({
            group1,
            group2,
            meanDiff,
            pValue,
            effectSize,
            ciLower: meanDiff - ciMargin,
            ciUpper: meanDiff + ciMargin,
            degreesOfFreedom,
            se
          });
        }
      }

      const rawPValues = rawPairwiseComparisons.map(comp => comp.pValue);
      const k = groups.length; // Number of groups
      // Calculate overall F-test (simplified ANOVA)
      const allValues = Object.values(groupedData).flat();
      const dfw = allValues.length - groups.length; // Degrees of freedom within

      const adjustedPValues = adjustPValue(rawPValues, testMethod, rawPairwiseComparisons, k, dfw);

      const pairwiseComparisons = rawPairwiseComparisons.map((comp, index) => ({
        ...comp,
        adjustedPValue: adjustedPValues[index],
        significant: adjustedPValues[index] < alpha,
      }));
      
      const grandMean = calculateMean(allValues);
      
      let ssb = 0; // Sum of squares between groups
      let ssw = 0; // Sum of squares within groups
      
      groups.forEach(group => {
        const values = groupedData[group];
        const groupMean = groupStats[group].mean;
        ssb += values.length * Math.pow(groupMean - grandMean, 2);
        ssw += values.reduce((sum, val) => sum + Math.pow(val - groupMean, 2), 0);
      });
      
      const dfb = groups.length - 1;
      const msb = ssb / dfb;
      const msw = ssw / dfw;
      const fStatistic = msb / msw;
      const fPValue = 1 - (jStat as any).centralF.cdf(fStatistic, dfb, dfw);
      
      // Prepare chart data
      const meansChart = groups.map(group => ({
        group,
        mean: groupStats[group].mean,
        ciLower: groupStats[group].ci_lower,
        ciUpper: groupStats[group].ci_upper,
        n: groupStats[group].n
      }));
      
      const comparisonMatrix = pairwiseComparisons.map(comp => ({
        group1: comp.group1,
        group2: comp.group2,
        significant: comp.significant,
        pValue: comp.adjustedPValue
      }));
      
      const results: PostHocResult = {
        dependentVariable: dependentCol,
        independentVariable: independentCol,
        testMethod,
        alpha,
        groupStats,
        pairwiseComparisons,
        overallTest: {
          fStatistic,
          pValue: fPValue,
          significant: fPValue < alpha
        },
        chartData: {
          meansChart,
          comparisonMatrix
        }
      };
      
      setPostHocResult(results);
      localStorage.setItem('posthoc_test_results', JSON.stringify(results));
      setLoading(false);
      
      setSnackbarMessage('PostHoc analysis completed successfully!');
      setSnackbarOpen(true);
    } catch (err) {
      setError(`Error analyzing data: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  };
  
  // Save results to the ResultsManager
  const saveToResultsManager = () => {
    if (!postHocResult || !currentDataset) {
      setSnackbarMessage('No results to save. Please run the analysis first.');
      setSnackbarOpen(true);
      return;
    }

    try {
      const dependentColumn = currentDataset.columns.find(col => col.id === selectedDependentVar);
      const independentColumn = currentDataset.columns.find(col => col.id === selectedIndependentVar);

      const resultData = {
        title: `Post-Hoc Tests - ${testMethod.toUpperCase()} (${currentDataset.name})`,
        type: 'other' as const,
        component: 'PostHocTests',
        data: {
          dataset: currentDataset.name,
          dependentVariable: dependentColumn?.name || 'Unknown',
          independentVariable: independentColumn?.name || 'Unknown',
          testMethod: testMethod,
          alpha: alpha,
          includeCI: includeCI,
          includeEffectSize: includeEffectSize,
          results: postHocResult,
          interpretation: getPostHocInterpretation(postHocResult),
          timestamp: new Date().toISOString(),
          totalSampleSize: currentDataset.data.length
        }
      };

      addResult(resultData);
      setSnackbarMessage('Post-hoc test results successfully added to Results Manager!');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Error adding results to Results Manager:', error);
      setSnackbarMessage('Error adding results to Results Manager.');
      setSnackbarOpen(true);
    }
  };

  // Helper function to get interpretation
  const getPostHocInterpretation = (result: PostHocResult) => {
    let interpretation = '';
    
    interpretation += `Overall ANOVA: F = ${result.overallTest.fStatistic.toFixed(3)}, p = ${result.overallTest.pValue.toFixed(3)}\n`;
    interpretation += result.overallTest.significant 
      ? '• Significant differences found between groups (p < 0.05)\n\n' 
      : '• No significant differences found between groups (p ≥ 0.05)\n\n';
    
    interpretation += 'Pairwise Comparisons:\n';
    result.pairwiseComparisons.forEach(comp => {
      interpretation += `• ${comp.group1} vs ${comp.group2}: `;
      interpretation += `Mean difference = ${comp.meanDiff.toFixed(3)}, `;
      interpretation += `p = ${comp.adjustedPValue.toFixed(3)} `;
      interpretation += comp.significant ? '(Significant)' : '(Not significant)';
      if (includeEffectSize) {
        interpretation += `, Effect size (Cohen's d) = ${comp.effectSize.toFixed(3)}`;
      }
      interpretation += '\n';
    });
    
    return interpretation;
  };

  // Get test method name
  const getTestMethodName = (method: string) => {
    const methods: { [key: string]: string } = {
      'tukey': "Tukey's HSD",
      'bonferroni': 'Bonferroni',
      'holm': 'Holm',
      'scheffe': "Scheffé's test",
      'fisher': "Fisher's LSD"
    };
    return methods[method] || method;
  };

  // Render group statistics table
  const renderGroupStats = () => (
    <TableContainer 
      component={Paper} 
      variant="outlined"
      sx={{ borderRadius: 2 }}
    >
      <Table size={isMobile ? "small" : "medium"}>
        <TableHead>
          <TableRow>
            <TableCell sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
              Group
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
              N
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
              Mean
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
              Std Dev
            </TableCell>
            {includeCI && (
              <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                95% CI
              </TableCell>
            )}
          </TableRow>
        </TableHead>
        <TableBody>
          {postHocResult && Object.entries(postHocResult.groupStats).map(([group, stats], index) => (
            <TableRow 
              key={group}
              sx={{ '&:nth-of-type(odd)': { backgroundColor: theme.palette.action.hover } }}
            >
              <TableCell sx={{ fontWeight: 500 }}>
                <Chip 
                  label={group} 
                  size="small" 
                  variant="outlined"
                  color="primary"
                />
              </TableCell>
              <TableCell align="right">{stats.n}</TableCell>
              <TableCell align="right">{stats.mean.toFixed(3)}</TableCell>
              <TableCell align="right">{stats.std.toFixed(3)}</TableCell>
              {includeCI && (
                <TableCell align="right">
                  [{stats.ci_lower.toFixed(3)}, {stats.ci_upper.toFixed(3)}]
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Render pairwise comparisons table
  const renderPairwiseComparisons = () => (
    <TableContainer 
      component={Paper} 
      variant="outlined"
      sx={{ borderRadius: 2, maxHeight: 400 }}
    >
      <Table size={isMobile ? "small" : "medium"} stickyHeader>
        <TableHead>
          <TableRow>
            <TableCell sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
              Comparison
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
              Mean Difference
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
              Adjusted p-value
            </TableCell>
            <TableCell align="center" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
              Significant
            </TableCell>
            {includeEffectSize && (
              <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                Effect Size
              </TableCell>
            )}
            {includeCI && (
              <TableCell align="right" sx={{ fontWeight: 600, backgroundColor: theme.palette.grey[50] }}>
                95% CI
              </TableCell>
            )}
          </TableRow>
        </TableHead>
        <TableBody>
          {postHocResult?.pairwiseComparisons.map((comp, index) => (
            <TableRow 
              key={`${comp.group1}-${comp.group2}`}
              sx={{ 
                '&:nth-of-type(odd)': { backgroundColor: theme.palette.action.hover },
                backgroundColor: comp.significant ? theme.palette.success.light + '20' : undefined
              }}
            >
              <TableCell sx={{ fontWeight: 500 }}>
                {comp.group1} vs {comp.group2}
              </TableCell>
              <TableCell align="right">{comp.meanDiff.toFixed(3)}</TableCell>
              <TableCell align="right">{comp.adjustedPValue.toFixed(3)}</TableCell>
              <TableCell align="center">
                <Chip
                  label={comp.significant ? 'Yes' : 'No'}
                  color={comp.significant ? 'success' : 'default'}
                  size="small"
                />
              </TableCell>
              {includeEffectSize && (
                <TableCell align="right">{comp.effectSize.toFixed(3)}</TableCell>
              )}
              {includeCI && (
                <TableCell align="right">
                  [{comp.ciLower.toFixed(3)}, {comp.ciUpper.toFixed(3)}]
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Render means chart
  const renderMeansChart = () => (
    <Paper 
      variant="outlined" 
      sx={{ 
        p: 2, 
        height: isMobile ? 300 : 400, 
        borderRadius: 2,
        backgroundColor: theme.palette.grey[50]
      }}
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={postHocResult?.chartData.meansChart}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: isMobile ? 60 : 80,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis 
            dataKey="group" 
            angle={isMobile ? -45 : 0} 
            textAnchor={isMobile ? "end" : "middle"}
            height={isMobile ? 60 : 80}
            tick={{ fontSize: isMobile ? 10 : 12 }}
          />
          <YAxis tick={{ fontSize: isMobile ? 10 : 12 }} />
          <RechartsTooltip 
            contentStyle={{
              backgroundColor: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 8
            }}
            formatter={(value, name) => [
              typeof value === 'number' ? value.toFixed(3) : value, 
              name === 'mean' ? 'Mean' : name
            ]}
          />
          <Legend />
          <Bar 
            dataKey="mean" 
            fill={theme.palette.primary.main} 
            name="Group Mean"
            radius={[4, 4, 0, 0]}
          >
            {includeCI && (
              <ErrorBar dataKey="ciLower" width={4} />
            )}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </Paper>
  );

  // Render comparison matrix chart
  const renderComparisonMatrix = () => (
    <Paper 
      variant="outlined" 
      sx={{ 
        p: 2, 
        height: isMobile ? 300 : 400, 
        borderRadius: 2,
        backgroundColor: theme.palette.grey[50]
      }}
    >
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: isMobile ? 60 : 80,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis 
            type="category" 
            dataKey="group1" 
            name="Group 1"
            tick={{ fontSize: isMobile ? 10 : 12 }}
          />
          <YAxis 
            type="category" 
            dataKey="group2" 
            name="Group 2"
            tick={{ fontSize: isMobile ? 10 : 12 }}
          />
          <RechartsTooltip 
            cursor={{ strokeDasharray: '3 3' }}
            contentStyle={{
              backgroundColor: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 8
            }}
            formatter={(value, name, props) => [
              props.payload.significant ? 'Significant' : 'Not Significant',
              `p = ${props.payload.pValue.toFixed(3)}`
            ]}
          />
          <Scatter 
            name="Comparisons" 
            data={postHocResult?.chartData.comparisonMatrix} 
            fill={theme.palette.primary.main} // Changed to a static color to fix TypeScript error
          />
        </ScatterChart>
      </ResponsiveContainer>
    </Paper>
  );
  
  return (
    <PublicationReadyGate>
      <Box sx={{ p: { xs: 2, md: 3 }, maxWidth: 1400, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
          PostHoc Tests
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Perform multiple comparisons between groups after finding significant ANOVA results
        </Typography>
      </Box>

      {/* Configuration Panel */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardHeader 
          title="Configuration"
          avatar={<SettingsIcon color="primary" />}
          sx={{ pb: 1 }}
        />
        <CardContent>
          {/* Dataset and Variable Selection */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Dataset</InputLabel>
                <Select
                  value={selectedDatasetId}
                  label="Dataset"
                  onChange={handleDatasetChange}
                  disabled={datasets.length === 0}
                >
                  {datasets.length === 0 ? (
                    <MenuItem value="" disabled>
                      No datasets available
                    </MenuItem>
                  ) : (
                    datasets.map(dataset => (
                      <MenuItem key={dataset.id} value={dataset.id}>
                        <Box>
                          <Typography variant="body2">{dataset.name}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {dataset.data.length} rows
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Dependent Variable (Numeric)</InputLabel>
                <Select
                  value={selectedDependentVar}
                  label="Dependent Variable (Numeric)"
                  onChange={handleDependentVarChange}
                  disabled={numericColumns.length === 0}
                >
                  {numericColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No numeric variables available
                    </MenuItem>
                  ) : (
                    numericColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        <Typography variant="body2">{column.name}</Typography>
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Independent Variable (Groups)</InputLabel>
                <Select
                  value={selectedIndependentVar}
                  label="Independent Variable (Groups)"
                  onChange={handleIndependentVarChange}
                  disabled={categoricalColumns.length === 0}
                >
                  {categoricalColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No categorical variables available
                    </MenuItem>
                  ) : (
                    categoricalColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        <Typography variant="body2">{column.name}</Typography>
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* View Mode Selection */}
          {postHocResult && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
                Display Mode
              </Typography>
              <ToggleButtonGroup
                value={viewMode}
                exclusive
                onChange={handleViewModeChange}
                size="small"
                sx={{ mb: 2 }}
              >
                <ToggleButton value="tabs" aria-label="tabs view">
                  <ViewCarouselIcon sx={{ mr: 1 }} />
                  {!isMobile && 'Tabs'}
                </ToggleButton>
                <ToggleButton value="stacked" aria-label="stacked view">
                  <ViewListIcon sx={{ mr: 1 }} />
                  {!isMobile && 'Stacked'}
                </ToggleButton>
                {!isMobile && (
                  <ToggleButton value="side-by-side" aria-label="side by side view">
                    <ViewModuleIcon sx={{ mr: 1 }} />
                    Side by Side
                  </ToggleButton>
                )}
              </ToggleButtonGroup>
            </Box>
          )}

          {/* Analysis Options */}
          <Accordion defaultExpanded={false}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                Analysis Options
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl component="fieldset">
                    <FormLabel component="legend" sx={{ fontWeight: 500, mb: 1 }}>
                      PostHoc Test Method
                    </FormLabel>
                    <RadioGroup
                      value={testMethod}
                      onChange={(e) => {
                        setTestMethod(e.target.value);
                        clearAnalysis();
                      }}
                    >
                      <FormControlLabel 
                        value="tukey" 
                        control={<Radio />} 
                        label={
                          <Box>
                            <Typography variant="body2">Tukey's HSD</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Controls family-wise error rate
                            </Typography>
                          </Box>
                        }
                      />
                      <FormControlLabel 
                        value="bonferroni" 
                        control={<Radio />} 
                        label={
                          <Box>
                            <Typography variant="body2">Bonferroni</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Conservative adjustment
                            </Typography>
                          </Box>
                        }
                      />
                      <FormControlLabel 
                        value="holm" 
                        control={<Radio />} 
                        label={
                          <Box>
                            <Typography variant="body2">Holm</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Step-down method
                            </Typography>
                          </Box>
                        }
                      />
                      <FormControlLabel 
                        value="scheffe" 
                        control={<Radio />} 
                        label={
                          <Box>
                            <Typography variant="body2">Scheffé</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Most conservative
                            </Typography>
                          </Box>
                        }
                      />
                    </RadioGroup>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Stack spacing={3}>
                    <Box>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
                        Significance Level (α)
                      </Typography>
                      <Slider
                        value={alpha}
                        onChange={(e, newValue) => {
                          setAlpha(newValue as number);
                          clearAnalysis();
                        }}
                        min={0.01}
                        max={0.10}
                        step={0.01}
                        marks={[
                          { value: 0.01, label: '0.01' },
                          { value: 0.05, label: '0.05' },
                          { value: 0.10, label: '0.10' }
                        ]}
                        valueLabelDisplay="auto"
                        sx={{ mt: 2 }}
                      />
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
                        Additional Options
                      </Typography>
                      <Stack spacing={1}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={includeCI}
                              onChange={(e) => {
                                setIncludeCI(e.target.checked);
                                clearAnalysis();
                              }}
                            />
                          }
                          label="Include Confidence Intervals"
                        />
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={includeEffectSize}
                              onChange={(e) => {
                                setIncludeEffectSize(e.target.checked);
                                clearAnalysis();
                              }}
                            />
                          }
                          label="Include Effect Sizes (Cohen's d)"
                        />
                      </Stack>
                    </Box>
                  </Stack>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Test Information */}
          <Accordion defaultExpanded={false}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                About PostHoc Tests
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" color="text.secondary" paragraph>
                PostHoc tests are used after finding a significant ANOVA result to determine which specific groups differ from each other:
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Tukey's HSD:</strong> Balanced approach controlling family-wise error rate
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Bonferroni:</strong> Conservative method dividing α by number of comparisons
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Holm:</strong> Step-down method more powerful than Bonferroni
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Scheffé:</strong> Most conservative, controls for all possible contrasts
                </Typography>
              </Box>
            </AccordionDetails>
          </Accordion>

          {/* Action Buttons */}
          <Box sx={{ mt: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<PsychologyIcon />}
              onClick={runPostHocAnalysis}
              disabled={loading || !selectedDependentVar || !selectedIndependentVar}
              sx={{ 
                px: 4,
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 600
              }}
            >
              {loading ? 'Analyzing...' : 'Run PostHoc Tests'}
            </Button>
            
            {postHocResult && (
              <Button
                variant="outlined"
                size="large"
                onClick={clearAnalysis}
                sx={{
                  px: 3,
                  borderRadius: 2,
                  textTransform: 'none'
                }}
              >
                Clear Results
              </Button>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <Card elevation={2}>
          <CardContent>
            <Box display="flex" flexDirection="column" alignItems="center" py={4}>
              <CircularProgress size={60} sx={{ mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                Running PostHoc Analysis...
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Performing pairwise comparisons between groups
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3, borderRadius: 2 }}
          action={
            <Button color="inherit" size="small" onClick={() => setError(null)}>
              Dismiss
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Results */}
      {postHocResult && !loading && (
        <Card elevation={2}>
          <CardHeader
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CompareArrowsIcon color="primary" />
                <Typography variant="h6">
                  PostHoc Analysis Results: {postHocResult.dependentVariable.name} by {postHocResult.independentVariable.name}
                </Typography>
              </Box>
            }
            subheader={
              <Typography variant="body2" color="text.secondary">
                {getTestMethodName(postHocResult.testMethod)} • α = {postHocResult.alpha}
              </Typography>
            }

          />
          <CardContent>
            {viewMode === 'tabs' ? (
              <Box>
                <Tabs 
                  value={activeTab} 
                  onChange={(e, newValue) => setActiveTab(newValue)}
                  sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
                >
                  <Tab 
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <FunctionsIcon fontSize="small" />
                        {!isMobile && 'Group Stats'}
                      </Box>
                    } 
                  />
                  <Tab 
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CompareArrowsIcon fontSize="small" />
                        {!isMobile && 'Comparisons'}
                      </Box>
                    } 
                  />
                  <Tab 
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <BarChartIcon fontSize="small" />
                        {!isMobile && 'Means Chart'}
                      </Box>
                    } 
                  />
                  <Tab 
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <InfoIcon fontSize="small" />
                        {!isMobile && 'Summary'}
                      </Box>
                    } 
                  />
                </Tabs>
                <TabPanel value={activeTab} index={0}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Descriptive Statistics by Group
                  </Typography>
                  {renderGroupStats()}
                </TabPanel>
                <TabPanel value={activeTab} index={1}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Pairwise Comparisons
                  </Typography>
                  {renderPairwiseComparisons()}
                </TabPanel>
                <TabPanel value={activeTab} index={2}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Group Means with Confidence Intervals
                  </Typography>
                  {renderMeansChart()}
                </TabPanel>
                <TabPanel value={activeTab} index={3}>
                  <Paper sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 2, border: 1, borderColor: 'divider' }}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500 }}>
                      Analysis Summary
                    </Typography>
                    <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-line' }}>
                      {getPostHocInterpretation(postHocResult)}
                    </Typography>
                  </Paper>
                </TabPanel>
              </Box>
            ) : viewMode === 'stacked' ? (
              <Stack spacing={4}>
                <Box>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Descriptive Statistics by Group
                  </Typography>
                  {renderGroupStats()}
                </Box>
                <Box>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Pairwise Comparisons
                  </Typography>
                  {renderPairwiseComparisons()}
                </Box>
                <Box>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Group Means with Confidence Intervals
                  </Typography>
                  {renderMeansChart()}
                </Box>
                <Box>
                  <Paper sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 2, border: 1, borderColor: 'divider' }}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500 }}>
                      Analysis Summary
                    </Typography>
                    <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-line' }}>
                      {getPostHocInterpretation(postHocResult)}
                    </Typography>
                  </Paper>
                </Box>
              </Stack>
            ) : (
              <Grid container spacing={3}>
                <Grid item xs={12} lg={6}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Group Statistics
                  </Typography>
                  {renderGroupStats()}
                  
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                      Analysis Summary
                    </Typography>
                    <Paper sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 2, border: 1, borderColor: 'divider' }}>
                      <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-line' }}>
                        {getPostHocInterpretation(postHocResult)}
                      </Typography>
                    </Paper>
                  </Box>
                </Grid>
                <Grid item xs={12} lg={6}>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                    Pairwise Comparisons
                  </Typography>
                  {renderPairwiseComparisons()}
                  
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                      Group Means Chart
                    </Typography>
                    {renderMeansChart()}
                  </Box>
                </Grid>
              </Grid>
            )}
          </CardContent>

          {/* Add to Results Manager Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, pt: 0 }}>
            <AddToResultsButton
              resultData={{
                title: `Post-Hoc Tests - ${testMethod.toUpperCase()} (${currentDataset?.name})`,
                type: 'other' as const,
                component: 'PostHocTests',
                data: {
                  dataset: currentDataset?.name,
                  dependentVariable: currentDataset?.columns.find(col => col.id === selectedDependentVar)?.name || 'Unknown',
                  independentVariable: currentDataset?.columns.find(col => col.id === selectedIndependentVar)?.name || 'Unknown',
                  testMethod: testMethod,
                  alpha: alpha,
                  includeCI: includeCI,
                  includeEffectSize: includeEffectSize,
                  results: postHocResult,
                  interpretation: postHocResult ? getPostHocInterpretation(postHocResult) : '',
                  timestamp: new Date().toISOString(),
                  totalSampleSize: currentDataset?.data.length
                }
              }}
              onSuccess={() => {
                setSnackbarMessage('Post-hoc test results successfully added to Results Manager!');
                setSnackbarOpen(true);
              }}
              onError={(error) => {
                setSnackbarMessage(`Error adding results to Results Manager: ${error}`);
                setSnackbarOpen(true);
              }}
              disabled={!postHocResult || !currentDataset}
            />
          </Box>
        </Card>
      )}

      {/* Empty State */}
      {!loading && !postHocResult && selectedDependentVar && selectedIndependentVar && (
        <Card elevation={1}>
          <CardContent>
            <Box textAlign="center" py={6}>
              <PsychologyIcon sx={{ fontSize: 80, color: theme.palette.grey[400], mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Ready to Analyze
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Click "Run PostHoc Tests" to perform pairwise comparisons between groups
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
    </Box>
    </PublicationReadyGate>
  );
};

export default PostHocTests;
