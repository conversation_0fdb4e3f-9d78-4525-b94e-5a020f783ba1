import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
// Remove Link from react-router-dom
// import { Link } from 'react-router-dom';
import {
  TableChart as TableIcon,
  CompareArrows as CompareIcon, // Add back CompareArrowsIcon import
  Functions as SMDIcon,
  TrendingUp as RegressionIcon,
  Psychology as InterpretationIcon,
  AccountTree as FlowIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
  CompareArrows as CompareArrowsIcon, // Ensure CompareArrowsIcon is imported
  Psychology as PsychologyIcon,
  Folder as ResultsIcon, // Icon for Results Manager
  Science as ScienceIcon, // Icon for Statistical Methods Generator

} from '@mui/icons-material';

interface PublicationOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  // route: string; // Route is no longer needed in this component
  path: string; // Use path for onNavigate
  icon: React.ReactNode;
  category: 'Tables' | 'Analysis' | 'Visualization';
  color: string;
}

interface PublicationReadyOptionsProps {
  onNavigate: (path: string) => void;
}

export const publicationReadyOptions: PublicationOption[] = [
  {
    name: 'Table 1',
    shortDescription: 'Descriptive statistics table for baseline characteristics',
    detailedDescription: 'Generate a comprehensive Table 1 showing descriptive statistics for all baseline characteristics of your study participants. This is the standard first table in most research papers that presents demographic and clinical characteristics.',
    // route: '/app/publication-ready/table1', // Remove route
    path: 'publication-ready/table1', // Use path for onNavigate
    icon: <TableIcon />,
    category: 'Tables',
    color: '#2196F3',
  },
  {
    name: 'Table 1a',
    shortDescription: 'Alternative descriptive statistics with advanced formatting',
    detailedDescription: 'Create an enhanced version of Table 1 with additional statistical measures, custom formatting options, and advanced grouping capabilities. Ideal for complex studies requiring detailed baseline comparisons.',
    // route: '/app/publication-ready/table1a', // Remove route
    path: 'publication-ready/table1a', // Use path for onNavigate
    icon: <TableIcon />,
    category: 'Tables',
    color: '#2196F3',
  },
  {
    name: 'Table 1b',
    shortDescription: 'Comprehensive descriptive statistics for numerical variables',
    detailedDescription: 'Generate a comprehensive descriptive statistics table specifically designed for multiple numerical variables. Includes mean, standard deviation, median, quartiles, range, and normality tests with intelligent interpretation of distribution characteristics and variability patterns.',
    path: 'publication-ready/table1b',
    icon: <TableIcon />,
    category: 'Tables',
    color: '#1976D2',
  },
  {
    name: 'Table 2',
    shortDescription: 'Comparative statistics and outcome analysis table',
    detailedDescription: 'Generate Table 2 for presenting primary and secondary outcomes with between-group comparisons. Includes statistical tests, p-values, confidence intervals, and effect sizes appropriate for your study design.',
    // route: '/app/publication-ready/table2', // Remove route
    path: 'publication-ready/table2', // Use path for onNavigate
    icon: <CompareArrowsIcon />,
    category: 'Tables',
    color: '#4CAF50',
  },
  {
    name: 'SMD Table',
    shortDescription: 'Standardized Mean Differences for effect size reporting',
    detailedDescription: 'Calculate and present Standardized Mean Differences (Cohen\'s d, Hedge\'s g) with confidence intervals. Essential for meta-analyses and systematic reviews, or when reporting effect sizes for continuous outcomes.',
    // route: '/app/publication-ready/smd-table', // Remove route
    path: 'publication-ready/smd-table', // Use path for onNavigate
    icon: <SMDIcon />,
    category: 'Analysis',
    color: '#FF9800',
  },
  {
    name: 'Regression Table',
    shortDescription: 'Formatted tables for regression analysis results',
    detailedDescription: 'Create publication-ready tables for linear, logistic, or Cox regression models. Automatically formats coefficients, odds ratios, hazard ratios, confidence intervals, and p-values according to journal standards.',
    // route: '/app/publication-ready/regression-table', // Remove route
    path: 'publication-ready/regression-table', // Use path for onNavigate
    icon: <RegressionIcon />,
    category: 'Analysis',
    color: '#9C27B0',
  },
  {
    name: 'Regression Interpretation',
    shortDescription: 'AI-assisted interpretation of regression results',
    detailedDescription: 'Get intelligent assistance in interpreting your regression analysis results. Provides plain-language explanations of coefficients, statistical significance, clinical significance, and suggests appropriate conclusions.',
    // route: '/app/publication-ready/regression-interpretation', // Remove route
    path: 'publication-ready/regression-interpretation', // Use path for onNavigate
    icon: <InterpretationIcon />,
    category: 'Analysis',
    color: '#607D8B',
  },
  {
    name: 'Flow Diagram',
    shortDescription: 'CONSORT-style participant flow diagrams',
    detailedDescription: 'Create professional participant flow diagrams following CONSORT, STROBE, or PRISMA guidelines. Visualize enrollment, randomization, follow-up, and analysis phases with customizable design options.',
    // route: '/app/publication-ready/flow-diagram', // Remove route
    path: 'publication-ready/flow-diagram', // Use path for onNavigate
    icon: <FlowIcon />,
    category: 'Visualization',
    color: '#00BCD4',
  },
  {
    name: 'Convert to APA',
    shortDescription: 'Convert raw data tables to APA-style format',
    detailedDescription: 'Transform your raw data tables into properly formatted APA-style tables for academic publications. This tool helps ensure your tables meet the strict guidelines of the APA 7th edition.',
    path: 'publication-ready/convert-to-apa',
    icon: <PsychologyIcon />,
    category: 'Tables',
    color: '#8BC34A',
  },
  {
    name: 'PostHoc Tests',
    shortDescription: 'Perform multiple comparisons after ANOVA',
    detailedDescription: 'Conduct various post-hoc tests (e.g., Tukey\'s HSD, Bonferroni, Holm) to identify specific group differences after a significant ANOVA result. Includes detailed tables and visualizations.',
    path: 'publication-ready/posthoc-tests',
    icon: <CompareArrowsIcon />,
    category: 'Analysis',
    color: '#FF5722', // A distinct color for PostHoc Tests
  },
  {
    name: 'Statistical Methods Generator',
    shortDescription: 'Generate publication-ready methods sections',
    detailedDescription: 'Automatically create comprehensive Statistical Methods sections based on your completed analyses. Select multiple analyses, customize the generated text, and export in various formats for publication.',
    path: 'publication-ready/statistical-methods',
    icon: <ScienceIcon />,
    category: 'Analysis',
    color: '#4CAF50', // Green color for Methods Generator
  },

  {
    name: 'Results Manager',
    shortDescription: 'Manage and export analysis results',
    detailedDescription: 'Organize, filter, and export your analysis results in various formats. Collect results from multiple analyses and create comprehensive reports for publication or presentation.',
    path: 'publication-ready/results-manager',
    icon: <ResultsIcon />,
    category: 'Analysis',
    color: '#9C27B0', // Purple color for Results Manager
  },

];

const PublicationReadyOptions: React.FC<PublicationReadyOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const categories = ['All', 'Tables', 'Analysis', 'Visualization', 'Learning'];

  const filteredOptions = selectedCategory === 'All'
    ? publicationReadyOptions
    : publicationReadyOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Tables': return <TableIcon />;
      case 'Analysis': return <RegressionIcon />;
      case 'Visualization': return <FlowIcon />;
      default: return <TableIcon />;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Publication-Ready Tools
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Generate publication-quality tables, analyses, and visualizations
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Choose from our collection of tools designed to help you create professional,
          journal-ready outputs for your research. Each tool follows established reporting
          guidelines and best practices.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography variant="h6" fontWeight="bold">
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {option.shortDescription}
                </Typography>

                <Typography variant="body2" paragraph>
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  // component={Link} // Remove Link component
                  // to={option.route} // Remove to prop
                  onClick={() => onNavigate(option.path)} // Use onClick with onNavigate
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>New to research publishing?</strong> Start with Table 1 or Flow Diagram
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Analyzing interventions?</strong> Use Table 2 for outcomes and SMD Table for effect sizes
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Complex modeling?</strong> Try Regression Table and Regression Interpretation
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • <strong>Systematic reviews?</strong> SMD Table and Flow Diagram are essential tools
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default PublicationReadyOptions;
