import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { RegressionResult } from '../../types';

interface RegressionInterpretationProps {
  regressionType: 'linear' | 'logistic' | 'cox' | '';
  results: RegressionResult | null;
}

const RegressionInterpretation: React.FC<RegressionInterpretationProps> = ({ regressionType, results }) => {
  if (!results || !regressionType) {
    return null; // Don't render if no results or type
  }

  const formatPValue = (pValue: number | undefined): string => {
    if (pValue === undefined) return 'N/A';
    return pValue < 0.001 ? '< .001' : `= ${pValue.toFixed(3)}`;
  };

  const renderInterpretation = () => {
    switch (regressionType) {
      case 'linear':
        if (!results) return null;

        const { coefficients, intercept, p_values, rSquared, fStatistic, dfModel, dfError, pValue } = results;

        let linearInterpretationText = `A linear regression was conducted to examine the relationship between the independent variables and the dependent variable. The model explained ${rSquared?.toFixed(2) || 'N/A'}% of the variance in the dependent variable, F(${dfModel || 'N/A'}, ${dfError || 'N/A'}) = ${fStatistic?.toFixed(2) || 'N/A'}, p ${formatPValue(pValue)}. `;

        Object.keys(coefficients || {}).forEach((variableName: string) => {
          const coef = (coefficients as any)?.[variableName];
          const pValue = p_values?.[variableName];
          const formattedPValue = formatPValue(pValue);
          const significance = pValue !== undefined && pValue < 0.05 ? 'significantly' : 'not significantly';
          const direction = coef > 0 ? 'positively' : 'negatively';

          linearInterpretationText += `The variable ${variableName} was ${significance} ${direction} associated with the dependent variable (b = ${coef?.toFixed(2) || 'N/A'}, p ${formattedPValue}). `;
        });

        // Add intercept interpretation if significant
        if (intercept !== undefined && p_values?.['(Intercept)'] !== undefined) {
           const interceptPValue = p_values['(Intercept)'];
           const formattedInterceptPValue = formatPValue(interceptPValue);
           const interceptSignificance = interceptPValue < 0.05 ? 'significantly' : 'not significantly';
           linearInterpretationText += `The intercept was ${interceptSignificance} different from zero (b = ${intercept?.toFixed(2) || 'N/A'}, p ${formattedInterceptPValue}).`;
        }


        return (
          <Box>
            <Typography variant="h6" gutterBottom>Linear Regression Interpretation</Typography>
            <Typography variant="body1">{linearInterpretationText}</Typography>
          </Box>
        );
      case 'logistic':
        if (!results) return null;

        const extendedResults = results as any;
        const { coefficients: logisticCoefficients, hazard_ratios, p_values: logisticPValues, pseudoRSquared, logLikelihood, aic, positiveCategory } = extendedResults;

        let logisticInterpretationText = `A logistic regression was conducted to predict the likelihood of the outcome variable (coded as 1 for ${positiveCategory || 'N/A'}). The model fit statistics were as follows: Log-Likelihood = ${logLikelihood?.toFixed(2) || 'N/A'}, AIC = ${aic?.toFixed(2) || 'N/A'}, Pseudo R² = ${pseudoRSquared?.toFixed(2) || 'N/A'}. `;

        Object.keys(logisticCoefficients || {}).forEach(variableName => {
          const or = hazard_ratios?.[variableName];
          const pValue = logisticPValues?.[variableName];
          const formattedPValue = formatPValue(pValue);
          const significance = pValue !== undefined && pValue < 0.05 ? 'significantly' : 'not significantly';

          logisticInterpretationText += `The variable ${variableName} was ${significance} associated with the outcome. The odds ratio for ${variableName} was ${or?.toFixed(2) || 'N/A'} (p ${formattedPValue}), indicating that for a one-unit increase in ${variableName}, the odds of the outcome occurring are multiplied by ${or?.toFixed(2) || 'N/A'}, holding other variables constant. `;
        });

        return (
          <Box>
            <Typography variant="h6" gutterBottom>Logistic Regression Interpretation</Typography>
            <Typography variant="body1">{logisticInterpretationText}</Typography>
          </Box>
        );
      case 'cox':
        if (!results) return null;

        const coxExtendedResults = results as any;
        const { coefficients: coxCoefficients, hazard_ratios: coxHazardRatios, p_values: coxPValues, concordance, log_likelihood, n_observations, n_events, eventCategory } = coxExtendedResults;

        let coxInterpretationText = `A Cox proportional hazards regression was conducted to examine the time to event (event defined as ${eventCategory || 'N/A'}). The model fit statistics were as follows: Log-Likelihood = ${log_likelihood?.toFixed(2) || 'N/A'}, Concordance Index = ${concordance?.toFixed(2) || 'N/A'}. The analysis included ${n_observations || 'N/A'} observations and ${n_events || 'N/A'} events. `;

        Object.keys(coxCoefficients || {}).forEach(variableName => {
          const hr = coxHazardRatios?.[variableName];
          const pValue = coxPValues?.[variableName];
          const formattedPValue = formatPValue(pValue);
          const significance = pValue !== undefined && pValue < 0.05 ? 'significantly' : 'not significantly';

          coxInterpretationText += `The variable ${variableName} was ${significance} associated with the hazard of the event. The hazard ratio for ${variableName} was ${hr?.toFixed(2) || 'N/A'} (p ${formattedPValue}), indicating that for a one-unit increase in ${variableName}, the hazard of the event is multiplied by ${hr?.toFixed(2) || 'N/A'}, holding other covariates constant. `;
        });

        return (
          <Box>
            <Typography variant="h6" gutterBottom>Cox Regression Interpretation</Typography>
            <Typography variant="body1">{coxInterpretationText}</Typography>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 2, mt: 3 }}>
      {renderInterpretation()}
    </Paper>
  );
};

export default RegressionInterpretation;
