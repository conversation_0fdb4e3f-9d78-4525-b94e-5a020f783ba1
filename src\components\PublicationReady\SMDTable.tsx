import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  SelectChangeEvent,
  Snackbar,
} from '@mui/material';

import { useData } from '../../context/DataContext';
import AddToResultsButton from '../UI/AddToResultsButton';
import { DataType, Column, Dataset } from '../../types';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import {
  calculateMean,
  calculateStandardDeviation,
} from '../../utils/stats/descriptive';

// Effect size calculation functions
const calculateCohenSD = (mean1: number, sd1: number, n1: number, mean2: number, sd2: number, n2: number): number => {
    // Pooled standard deviation
    const pooledSD = Math.sqrt(((n1 - 1) * Math.pow(sd1, 2) + (n2 - 1) * Math.pow(sd2, 2)) / (n1 + n2 - 2));
    // <PERSON>'s d
    return (mean1 - mean2) / pooledSD;
};

const calculateCohenSDConfidenceInterval = (cohenSD: number, n1: number, n2: number, confidenceLevel: number = 0.95): { lower: number; upper: number } => {
    // This is a simplified calculation and might need a more robust implementation
    // based on non-central t-distribution for more accuracy, especially for small sample sizes.
    // For a basic approximation:
    const pooledN = n1 + n2;
    const variance = (pooledN / (n1 * n2)) + (Math.pow(cohenSD, 2) / (2 * (pooledN - 2)));
    const standardError = Math.sqrt(variance);

    // Using Z-score for 95% CI for simplicity (approximation)
    const z = 1.96; // For 95% CI

    const lower = cohenSD - z * standardError;
    const upper = cohenSD + z * standardError;

    return { lower, upper };
};

interface SMDResult {
  variableName: string;
  smd?: number;
  ciLower?: number;
  ciUpper?: number;
  remarks?: string; // Added for interpretation
  // Potentially add p-value if applicable and easily calculable from t-test
}

// Function to interpret Cohen's d effect size
const interpretCohenSD = (smd: number | undefined): string => {
    if (smd === undefined || isNaN(smd)) {
        return 'N/A';
    }
    const absSmd = Math.abs(smd);
    if (absSmd >= 0.8) {
        return 'Large';
    } else if (absSmd >= 0.5) {
        return 'Medium';
    } else if (absSmd >= 0.2) {
        return 'Small';
    } else {
        return 'Trivial';
    }
};

const SMDTable: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();


  // State for selected dataset, grouping variable, and continuous variables
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [groupingVariableId, setGroupingVariableId] = useState<string | ''>('');
  const [continuousVariableIds, setContinuousVariableIds] = useState<string[]>([]);

  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [smdResults, setSmdResults] = useState<SMDResult[] | null>(null);

  // State for snackbar notifications
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Get the currently selected dataset based on selectedDatasetId
  const selectedDataset = datasets.find(dataset => dataset.id === selectedDatasetId);

  // Get available columns from the selected dataset
  const availableColumns = selectedDataset?.columns || [];

  // Filter binary categorical columns for grouping variable
  const binaryCategoricalColumns = availableColumns.filter(col =>
      col.type === DataType.CATEGORICAL &&
      selectedDataset?.data.map(row => String(row[col.name])).filter((value, index, self) => self.indexOf(value) === index).length === 2
  );

  // Filter continuous columns for analysis
  const continuousColumns = availableColumns.filter(col => col.type === DataType.NUMERIC);

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setGroupingVariableId(''); // Clear grouping variable
    setContinuousVariableIds([]); // Clear continuous variables
    setSmdResults(null); // Clear results
    setError(null);

    // Update the current dataset in the DataContext
    const datasetToSet = datasets.find(dataset => dataset.id === newDatasetId);
    if (datasetToSet) {
      setCurrentDataset(datasetToSet);
    }
  };

  // Handle grouping variable selection change
  const handleGroupingVariableChange = (event: SelectChangeEvent<string>) => {
    const newGroupingVariableId = event.target.value;
    setGroupingVariableId(newGroupingVariableId);
    setContinuousVariableIds([]); // Clear continuous variables when grouping variable changes
    setSmdResults(null); // Clear results
    setError(null);
  };

  // Handle continuous variable selection change
  const handleContinuousVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setContinuousVariableIds(typeof value === 'string' ? value.split(',') : value);
    setSmdResults(null); // Clear results when selection changes
    setError(null);
  };

  // Function to run SMD analysis
  const runSMDAnalysis = () => {
    if (!selectedDataset || !groupingVariableId || continuousVariableIds.length === 0) {
      setError('Please select a dataset, a binary grouping variable, and at least one continuous variable to analyze.');
      setSmdResults(null);
      return;
    }

    setLoading(true);
    setError(null);
    const results: SMDResult[] = [];

    const groupingColumn = availableColumns.find(col => col.id === groupingVariableId);
    const continuousCols = availableColumns.filter(col => continuousVariableIds.includes(col.id));

    if (!groupingColumn || groupingColumn.type !== DataType.CATEGORICAL || binaryCategoricalColumns.findIndex(col => col.id === groupingVariableId) === -1) {
        setError('Selected grouping variable is not a binary categorical variable.');
        setLoading(false);
        return;
    }

    const groupingCategories = Array.from(new Set(selectedDataset.data.map(row => String(row[groupingColumn.name]))));
    const group1Category = groupingCategories[0];
    const group2Category = groupingCategories[1];

    continuousCols.forEach(column => {
        const variableData = selectedDataset.data.map(row => row[column.name]);

        const group1Data = selectedDataset.data
            .filter(row => String(row[groupingColumn.name]) === group1Category)
            .map(row => row[column.name])
            .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

        const group2Data = selectedDataset.data
            .filter(row => String(row[groupingColumn.name]) === group2Category)
            .map(row => row[column.name])
            .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

        if (group1Data.length > 1 && group2Data.length > 1) { // Need at least 2 data points per group for SD
             try {
                 const mean1 = calculateMean(group1Data);
                 const sd1 = calculateStandardDeviation(group1Data);
                 const n1 = group1Data.length;

                 const mean2 = calculateMean(group2Data);
                 const sd2 = calculateStandardDeviation(group2Data);
                 const n2 = group2Data.length;

                 const cohenSD = calculateCohenSD(mean1, sd1, n1, mean2, sd2, n2);
                 const ci = calculateCohenSDConfidenceInterval(cohenSD, n1, n2);

                 results.push({
                     variableName: column.name,
                     smd: cohenSD,
                     ciLower: ci.lower,
                     ciUpper: ci.upper,
                     remarks: interpretCohenSD(cohenSD), // Add interpretation
                 });

             } catch (e) {
                 console.error(`Error calculating SMD for ${column.name}:`, e);
                 results.push({
                     variableName: column.name,
                     smd: NaN,
                     ciLower: NaN,
                     ciUpper: NaN,
                     remarks: interpretCohenSD(NaN), // Add interpretation for error case
                 });
             }
        } else {
             results.push({
                 variableName: column.name,
                 smd: NaN,
                 ciLower: NaN,
                 ciUpper: NaN,
                 remarks: interpretCohenSD(NaN), // Add interpretation for insufficient data
             });
        }
    });

    setSmdResults(results);
    setLoading(false);
  };



  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <PublicationReadyGate>
      <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Publication Ready: Standardized Mean Differences (SMD) Table
      </Typography>

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Data and Variables
        </Typography>

        <Alert severity="info" sx={{ mb: 2 }}>
            This table calculates Standardized Mean Differences (e.g., Cohen's d) for continuous variables, comparing two groups defined by a binary categorical variable.
        </Alert>

        <Grid container spacing={2}>
           {/* Dataset Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          {/* Grouping Variable Selection */}
           <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="grouping-variable-select-label">Grouping Variable (Binary Categorical)</InputLabel>
              <Select
                labelId="grouping-variable-select-label"
                id="grouping-variable-select"
                value={groupingVariableId}
                onChange={handleGroupingVariableChange}
                label="Grouping Variable (Binary Categorical)"
                disabled={binaryCategoricalColumns.length === 0 || !selectedDataset}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {binaryCategoricalColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No binary categorical variables available
                  </MenuItem>
                ) : (
                  binaryCategoricalColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          {/* Continuous Variables Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="continuous-variables-select-label">Continuous Variables</InputLabel>
              <Select
                labelId="continuous-variables-select-label"
                id="continuous-variables-select"
                multiple
                value={continuousVariableIds}
                onChange={handleContinuousVariableChange}
                label="Continuous Variables"
                disabled={continuousColumns.length === 0 || !groupingVariableId}
                renderValue={(selected) => selected.map(id => availableColumns.find(col => col.id === id)?.name || '').join(', ')}
              >
                {continuousColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No continuous variables available
                  </MenuItem>
                ) : (
                  continuousColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={runSMDAnalysis}
            disabled={loading || !selectedDataset || !groupingVariableId || continuousVariableIds.length === 0}
          >
            Generate SMD Table
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {smdResults && !loading && selectedDataset && groupingVariableId && continuousVariableIds.length > 0 && (
        <Box>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Standardized Mean Differences (SMD)
            </Typography>

            {/* Table Rendering */}
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Variable</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">SMD (Cohen's d)</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">95% CI</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Remarks/Interpretation</TableCell> {/* Added Remarks column header */}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {smdResults.map((result, index) => (
                    <TableRow key={index}>
                      <TableCell sx={{ fontWeight: 'bold' }}>{result.variableName}</TableCell>
                      <TableCell align="center">{result.smd !== undefined && !isNaN(result.smd) ? result.smd.toFixed(3) : 'N/A'}</TableCell>
                                            <TableCell align="center">
                          {result.ciLower !== undefined && !isNaN(result.ciLower) && result.ciUpper !== undefined && !isNaN(result.ciUpper) ?
                           `[${result.ciLower.toFixed(3)}, ${result.ciUpper.toFixed(3)}]` : 'N/A'}
                      </TableCell>
                      <TableCell align="center">{result.remarks || 'N/A'}</TableCell> {/* Added Remarks data cell */}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

          </Paper>

          {/* Add to Results Manager Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <AddToResultsButton
              resultData={{
                title: `SMD Table - Standardized Mean Differences (${selectedDataset.name})`,
                type: 'other' as const,
                component: 'SMDTable',
                data: {
                  dataset: selectedDataset.name,
                  groupingVariable: availableColumns.find(col => col.id === groupingVariableId)?.name || 'Unknown',
                  variables: continuousVariableIds.map((id: string) =>
                    availableColumns.find(col => col.id === id)?.name || id
                  ),
                  results: smdResults,
                  timestamp: new Date().toISOString(),
                  totalSampleSize: selectedDataset.data.length
                }
              }}
              onSuccess={() => {
                setSnackbarMessage('Results successfully added to Results Manager!');
                setSnackbarOpen(true);
              }}
              onError={(error) => {
                setSnackbarMessage(`Error adding results to Results Manager: ${error}`);
                setSnackbarOpen(true);
              }}
            />
          </Box>
        </Box>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Box>
    </PublicationReadyGate>
  );
};

export default SMDTable;

