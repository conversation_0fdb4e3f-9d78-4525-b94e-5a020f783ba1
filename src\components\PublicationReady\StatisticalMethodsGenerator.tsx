import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  TextField,
  Divider,
  SelectChangeEvent,
  List,
  ListItem,
  ListItemText,
  IconButton
} from '@mui/material';
import {
  Science as ScienceIcon,
  ContentCopy as CopyIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import AddToResultsButton from '../UI/AddToResultsButton';
import VariableSelector from '../UI/VariableSelector';
import { DataType } from '../../types';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import {
  allMethodsTemplates,
  SOFTWARE_CITATION,
  SIGNIFICANCE_CRITERIA,
  ASSUMPTION_CHECKING,
  EFFECT_SIZE_STATEMENT
} from '../../utils/methodsTemplates';

// Analysis types available in DataStatPro
const ANALYSIS_TYPES = [
  'Descriptive Statistics',
  'Independent samples t-test',
  'Paired samples t-test',
  'One-sample t-test',
  'One-way ANOVA',
  'Two-way ANOVA',
  'Repeated Measures ANOVA',
  'Post-Hoc Tests',
  'Linear Regression',
  'Logistic Regression',
  'Correlation Analysis',
  'Chi-Square Test',
  'Mann-Whitney U Test',
  'Wilcoxon Signed-Rank Test',
  'Kruskal-Wallis Test',
  'Friedman Test',
  'Reliability Analysis',
  'Factor Analysis',
  'Cluster Analysis',
  'Survival Analysis',
  'Log-Rank Test',
  'Cox Regression',
  'Kaplan Meier Curves',
  'Meta-Analysis',
  'Standardized Mean Difference (SMD)'
];

// Analysis purposes
const ANALYSIS_PURPOSES = [
  { value: 'descriptive', label: 'Descriptive' },
  { value: 'comparison', label: 'Comparison' },
  { value: 'relationship', label: 'Relationship' },
  { value: 'prediction', label: 'Prediction' },
  { value: 'other', label: 'Other' }
];

interface SingleAnalysisEntry {
  id: string;
  analysisType: string;
  analysisPurpose: string;
  dependentVariable: string;
  independentVariables: string[];
  covariates: string[];
  demographics: string[];
  generatedText: string;
}

interface AnalysisFormData {
  analysisType: string;
  analysisPurpose: string;
  dependentVariable: string;
  independentVariables: string[];
  covariates: string[];
  demographics: string[];
}

interface StudyParameters {
  selectedDatasetId: string;
  sampleSize: string;
  significanceLevel: string;
  additionalNotes: string;
}

const StatisticalMethodsGenerator: React.FC = () => {
  const { canAccessProFeatures } = useAuth();
  const { datasets, currentDataset, setCurrentDataset } = useData();

  const [analysisFormData, setAnalysisFormData] = useState<AnalysisFormData>({
    analysisType: '',
    analysisPurpose: '',
    dependentVariable: '',
    independentVariables: [],
    covariates: [],
    demographics: []
  });

  const [studyParameters, setStudyParameters] = useState<StudyParameters>({
    selectedDatasetId: currentDataset?.id || '',
    sampleSize: '',
    significanceLevel: '0.05',
    additionalNotes: ''
  });

  const [analysisEntries, setAnalysisEntries] = useState<SingleAnalysisEntry[]>([]);
  const [combinedMethodsText, setCombinedMethodsText] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [customText, setCustomText] = useState<string>('');

  // Get the currently selected dataset
  const selectedDataset = datasets.find(d => d.id === studyParameters.selectedDatasetId) || currentDataset;

  // Determine which variable role sections to show based on selected analysis type
  const getVisibleSections = () => {
    const hasDescriptive = analysisFormData.analysisType.includes('Descriptive');
    const hasInferential = analysisFormData.analysisType.includes('t-test') ||
                          analysisFormData.analysisType.includes('ANOVA') ||
                          analysisFormData.analysisType.includes('Post-Hoc') ||
                          analysisFormData.analysisType.includes('Chi-Square') ||
                          analysisFormData.analysisType.includes('Mann-Whitney') ||
                          analysisFormData.analysisType.includes('Wilcoxon') ||
                          analysisFormData.analysisType.includes('Kruskal-Wallis') ||
                          analysisFormData.analysisType.includes('Friedman');
    const hasRegression = analysisFormData.analysisType.includes('Regression');
    const hasSurvival = analysisFormData.analysisType.includes('Log-Rank') ||
                       analysisFormData.analysisType.includes('Cox Regression') ||
                       analysisFormData.analysisType.includes('Kaplan Meier') ||
                       analysisFormData.analysisType.includes('Survival Analysis');
    const hasMetaAnalysis = analysisFormData.analysisType.includes('Meta-Analysis') ||
                           analysisFormData.analysisType.includes('SMD');
    const hasCorrelation = analysisFormData.analysisType.includes('Correlation');
    const hasReliability = analysisFormData.analysisType.includes('Reliability');
    const hasFactor = analysisFormData.analysisType.includes('Factor');
    const hasCluster = analysisFormData.analysisType.includes('Cluster');

    return {
      showDemographics: hasDescriptive || hasInferential || hasRegression || hasSurvival || hasMetaAnalysis || hasCorrelation,
      showDependent: hasInferential || hasRegression || hasSurvival,
      showIndependent: hasInferential || hasRegression || hasSurvival || hasCorrelation || hasMetaAnalysis,
      showCovariates: hasRegression || hasSurvival
    };
  };

  const visibleSections = getVisibleSections();

  const handleDatasetChange = (event: SelectChangeEvent) => {
    const datasetId = event.target.value;
    setStudyParameters(prev => ({
      ...prev,
      selectedDatasetId: datasetId
    }));

    // Update the current dataset in the data context
    const dataset = datasets.find(d => d.id === datasetId);
    if (dataset) {
      setCurrentDataset(dataset);
    }

    // Clear analysis form when dataset changes
    setAnalysisFormData({
      analysisType: '',
      analysisPurpose: '',
      dependentVariable: '',
      independentVariables: [],
      covariates: [],
      demographics: []
    });
  };

  const handleAnalysisTypeChange = (event: SelectChangeEvent) => {
    setAnalysisFormData(prev => ({
      ...prev,
      analysisType: event.target.value,
      // Clear variables when analysis type changes
      dependentVariable: '',
      independentVariables: [],
      covariates: [],
      demographics: []
    }));
  };

  const handlePurposeChange = (event: SelectChangeEvent) => {
    setAnalysisFormData(prev => ({
      ...prev,
      analysisPurpose: event.target.value
    }));
  };

  const handleAnalysisVariableChange = (field: keyof AnalysisFormData, newValue: string | string[]) => {
    setAnalysisFormData(prev => ({
      ...prev,
      [field]: newValue
    }));
  };

  const handleStudyParameterChange = (field: keyof StudyParameters, value: string) => {
    setStudyParameters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateSingleAnalysisText = (analysisData: AnalysisFormData): string => {
    let methodsText = '';

    // Generate analysis-specific text
    if (analysisData.analysisType.includes('Descriptive')) {
      methodsText = 'Descriptive statistics including measures of central tendency and variability were calculated';
      if (analysisData.demographics.length > 0) {
        methodsText += ` for ${analysisData.demographics.join(', ')}`;
      }
      methodsText += '.';
    } else if (analysisData.analysisType.includes('Independent samples t-test')) {
      methodsText = `Independent samples t-tests were conducted to compare means of ${analysisData.dependentVariable}`;
      if (analysisData.independentVariables.length > 0 || analysisData.demographics.length > 0) {
        const allGroupingVars = [...analysisData.independentVariables, ...analysisData.demographics];
        methodsText += ` between various groups of ${allGroupingVars.join(', ')}`;
      }
      methodsText += '.';
    } else if (analysisData.analysisType.includes('Paired samples t-test')) {
      methodsText = `Paired samples t-tests were performed to compare pre- and post-intervention measurements of ${analysisData.dependentVariable}.`;
    } else if (analysisData.analysisType.includes('One-sample t-test')) {
      methodsText = `One-sample t-tests were used to compare sample means of ${analysisData.dependentVariable} against known population values.`;
    } else if (analysisData.analysisType.includes('One-way ANOVA')) {
      methodsText = `One-way analysis of variance (ANOVA) was conducted to compare means of ${analysisData.dependentVariable}`;
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` across groups of ${analysisData.independentVariables.join(', ')}`;
      }
      methodsText += '.';
    } else if (analysisData.analysisType.includes('Two-way ANOVA')) {
      methodsText = `Two-way ANOVA was performed to examine main effects and interactions between factors on ${analysisData.dependentVariable}.`;
    } else if (analysisData.analysisType.includes('Repeated Measures ANOVA')) {
      methodsText = `Repeated measures ANOVA was used to analyze within-subjects changes in ${analysisData.dependentVariable} over time.`;
    } else if (analysisData.analysisType.includes('Linear Regression')) {
      methodsText = `Multiple linear regression analysis was conducted to examine relationships between variables and predict ${analysisData.dependentVariable}`;
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` using ${analysisData.independentVariables.join(', ')} as predictors`;
      }
      if (analysisData.covariates.length > 0) {
        methodsText += ` while controlling for ${analysisData.covariates.join(', ')}`;
      }
      methodsText += '.';
    } else if (analysisData.analysisType.includes('Logistic Regression')) {
      methodsText = `Logistic regression analysis was performed to model ${analysisData.dependentVariable} and identify predictors`;
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` including ${analysisData.independentVariables.join(', ')}`;
      }
      methodsText += '.';
    } else if (analysisData.analysisType.includes('Correlation')) {
      methodsText = 'Correlation analysis was conducted to examine relationships between continuous variables';
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` including ${analysisData.independentVariables.join(', ')}`;
      }
      methodsText += '.';
    } else if (analysisData.analysisType.includes('Chi-Square')) {
      methodsText = 'Chi-square tests of independence were performed to examine associations between categorical variables';
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` including ${analysisData.independentVariables.join(', ')}`;
      }
      methodsText += '.';
    } else if (analysisData.analysisType.includes('Mann-Whitney')) {
      methodsText = `Mann-Whitney U tests were conducted as non-parametric alternatives to compare ${analysisData.dependentVariable} between groups.`;
    } else if (analysisData.analysisType.includes('Wilcoxon')) {
      methodsText = `Wilcoxon signed-rank tests were performed as non-parametric alternatives to compare ${analysisData.dependentVariable} measurements.`;
    } else if (analysisData.analysisType.includes('Kruskal-Wallis')) {
      methodsText = `Kruskal-Wallis tests were conducted as non-parametric alternatives to compare ${analysisData.dependentVariable} across multiple groups.`;
    } else if (analysisData.analysisType.includes('Friedman')) {
      methodsText = `Friedman tests were performed as non-parametric alternatives to analyze changes in ${analysisData.dependentVariable} over time.`;
    } else if (analysisData.analysisType.includes('Post-Hoc')) {
      methodsText = `Post-hoc tests were conducted to examine pairwise comparisons following significant ANOVA results`;
      if (analysisData.dependentVariable) {
        methodsText += ` for ${analysisData.dependentVariable}`;
      }
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` across groups of ${analysisData.independentVariables.join(', ')}`;
      }
      methodsText += '. Multiple comparison corrections were applied to control for Type I error.';
    } else if (analysisData.analysisType.includes('Log-Rank')) {
      methodsText = `Log-rank tests were performed to compare survival distributions`;
      if (analysisData.dependentVariable) {
        methodsText += ` for ${analysisData.dependentVariable}`;
      }
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` between groups defined by ${analysisData.independentVariables.join(', ')}`;
      }
      methodsText += '.';
    } else if (analysisData.analysisType.includes('Cox Regression')) {
      methodsText = `Cox proportional hazards regression analysis was conducted to examine the relationship between survival time`;
      if (analysisData.dependentVariable) {
        methodsText += ` (${analysisData.dependentVariable})`;
      }
      methodsText += ' and predictor variables';
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` including ${analysisData.independentVariables.join(', ')}`;
      }
      if (analysisData.covariates.length > 0) {
        methodsText += ` while controlling for ${analysisData.covariates.join(', ')}`;
      }
      methodsText += '. The proportional hazards assumption was assessed prior to analysis.';
    } else if (analysisData.analysisType.includes('Kaplan Meier')) {
      methodsText = `Kaplan-Meier survival curves were generated to estimate survival probabilities`;
      if (analysisData.dependentVariable) {
        methodsText += ` for ${analysisData.dependentVariable}`;
      }
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` stratified by ${analysisData.independentVariables.join(', ')}`;
      }
      methodsText += '. Median survival times and confidence intervals were calculated.';
    } else if (analysisData.analysisType.includes('SMD')) {
      methodsText = `Standardized mean differences (SMD) were calculated to assess effect sizes`;
      if (analysisData.dependentVariable) {
        methodsText += ` for ${analysisData.dependentVariable}`;
      }
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` between groups defined by ${analysisData.independentVariables.join(', ')}`;
      }
      methodsText += '. Cohen\'s conventions were used to interpret effect size magnitudes (small: 0.2, medium: 0.5, large: 0.8).';
    } else if (analysisData.analysisType.includes('Reliability')) {
      methodsText = 'Reliability analysis was conducted to assess internal consistency of measurement scales.';
    } else if (analysisData.analysisType.includes('Factor')) {
      methodsText = 'Factor analysis was performed to identify underlying constructs and reduce dimensionality.';
    } else if (analysisData.analysisType.includes('Cluster')) {
      methodsText = 'Cluster analysis was conducted to identify distinct groups within the data.';
    } else if (analysisData.analysisType.includes('Survival')) {
      methodsText = 'Survival analysis methods were employed to analyze time-to-event data.';
    } else if (analysisData.analysisType.includes('Meta-Analysis')) {
      methodsText = 'Meta-analysis was conducted to synthesize effect sizes across multiple studies';
      if (analysisData.independentVariables.length > 0) {
        methodsText += ` examining ${analysisData.independentVariables.join(', ')}`;
      }
      methodsText += '. Random-effects models were used to account for heterogeneity between studies.';
    }

    return methodsText;
  };

  const addAnalysisToMethods = () => {
    if (!analysisFormData.analysisType) {
      console.warn('Please select an analysis type.'); // Changed to console.warn
      return;
    }

    if (!analysisFormData.analysisPurpose) {
      console.warn('Please select an analysis purpose.'); // Changed to console.warn
      return;
    }

    // Validate required variables based on analysis type
    if (visibleSections.showDependent && !analysisFormData.dependentVariable) {
      console.warn('Please select a dependent variable for this analysis type.'); // Changed to console.warn
      return;
    }

    const analysisText = generateSingleAnalysisText(analysisFormData);

    const newEntry: SingleAnalysisEntry = {
      id: Date.now().toString(),
      analysisType: analysisFormData.analysisType,
      analysisPurpose: analysisFormData.analysisPurpose,
      dependentVariable: analysisFormData.dependentVariable,
      independentVariables: [...analysisFormData.independentVariables],
      covariates: [...analysisFormData.covariates],
      demographics: [...analysisFormData.demographics],
      generatedText: analysisText
    };

    setAnalysisEntries(prev => [...prev, newEntry]);

    // Clear only analysis-specific form fields (NOT study parameters)
    setAnalysisFormData({
      analysisType: '',
      analysisPurpose: '',
      dependentVariable: '',
      independentVariables: [],
      covariates: [],
      demographics: []
    });

    console.log('Analysis added to methods section!'); // Changed to console.log
  };

  const removeAnalysisEntry = (id: string) => {
    setAnalysisEntries(prev => prev.filter(entry => entry.id !== id));
    console.log('Analysis removed from methods section.'); // Changed to console.log
  };

  const clearAllEntries = () => {
    setAnalysisEntries([]);
    setCombinedMethodsText('');
    setCustomText('');
    console.log('All analyses cleared.'); // Changed to console.log
  };

  // Update combined methods text when entries change
  useEffect(() => {
    if (analysisEntries.length === 0) {
      setCombinedMethodsText('');
      setCustomText('');
      return;
    }

    let combinedText = '';

    // Add sample size information if provided
    if (studyParameters.sampleSize) {
      combinedText += `The study included ${studyParameters.sampleSize} participants. `;
    }

    // Add analysis descriptions
    const analysisTexts = analysisEntries.map(entry => entry.generatedText);
    combinedText += analysisTexts.join(' ');

    // Add standard methodological statements
    combinedText += ` ${ASSUMPTION_CHECKING}`;
    combinedText += ` Statistical significance was set at p < ${studyParameters.significanceLevel} for all analyses.`;
    combinedText += ` ${SOFTWARE_CITATION}`;

    // Add additional notes if provided
    if (studyParameters.additionalNotes.trim()) {
      combinedText += ` ${studyParameters.additionalNotes.trim()}`;
    }

    setCombinedMethodsText(combinedText);
    setCustomText(combinedText);
    setIsEditing(false);
  }, [analysisEntries, studyParameters.sampleSize, studyParameters.significanceLevel, studyParameters.additionalNotes]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(customText);
      console.log('Text copied to clipboard!'); // Changed to console.log
    } catch (err) {
      console.error('Failed to copy text to clipboard.', err); // Changed to console.error
    }
  };

  const exportAsHTML = () => {
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Statistical Methods Section</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
          .methods-section { max-width: 800px; margin: 0 auto; }
          h1 { color: #333; border-bottom: 2px solid #333; padding-bottom: 10px; }
          p { text-align: justify; margin-bottom: 15px; }
          .analysis-list { margin-bottom: 20px; }
          .analysis-item { margin-bottom: 10px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; }
        </style>
      </head>
      <body>
        <div class="methods-section">
          <h1>Statistical Methods</h1>
          ${analysisEntries.length > 0 ? `
            <div class="analysis-list">
              <h2>Analyses Performed:</h2>
              ${analysisEntries.map(entry => `
                <div class="analysis-item">
                  <strong>${entry.analysisType}</strong> (${entry.analysisPurpose})
                  ${entry.dependentVariable ? `<br>Dependent Variable: ${entry.dependentVariable}` : ''}
                  ${entry.independentVariables.length > 0 ? `<br>Independent Variables: ${entry.independentVariables.join(', ')}` : ''}
                  ${entry.covariates.length > 0 ? `<br>Covariates: ${entry.covariates.join(', ')}` : ''}
                  ${entry.demographics.length > 0 ? `<br>Demographics: ${entry.demographics.join(', ')}` : ''}
                </div>
              `).join('')}
            </div>
          ` : ''}
          <h2>Methods Text:</h2>
          <p>${customText}</p>
        </div>
      </body>
      </html>
    `;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'statistical-methods.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('Methods section exported as HTML!'); // Changed to console.log
  };

  return (
    <PublicationReadyGate>
      <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <ScienceIcon sx={{ mr: 2, color: 'primary.main', fontSize: 32 }} />
          <Typography variant="h4" component="h1">
            Statistical Methods Generator
          </Typography>
        </Box>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Generate publication-ready Statistical Methods sections using an iterative approach.
          Add one analysis at a time to build a comprehensive methods section.
        </Typography>

        {datasets.length === 0 && (
          <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="subtitle2" component="span" sx={{ fontWeight: 600, mb: 1 }}>
            No Datasets Available
          </Typography>
          <Typography variant="body2" component="span">
            Please load a dataset first to access variable selection features. You can still use the tool with manual variable entry.
          </Typography>
          </Alert>
        )}

        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="subtitle2" component="span" sx={{ fontWeight: 600, mb: 1 }}>
            How to use this tool:
          </Typography>
          <Box component="ol" sx={{ m: 0, pl: 2 }}>
            <li>Select ONE analysis type you performed</li>
            <li>Choose the purpose of your analysis</li>
            <li>Select variables from your dataset (or type manually)</li>
            <li>Click "Add to Methods" to add this analysis</li>
            <li>Repeat for each additional analysis</li>
            <li>Review and customize the combined methods text</li>
            <li>Export or save to Results Manager</li>
          </Box>
        </Alert>
      </Box>

      <Grid container spacing={3}>
        {/* Dataset Selection and Study Parameters */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Dataset Selection & Study Parameters
              </Typography>

              <Grid container spacing={2}>
                {/* Dataset Selection */}
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Select Dataset</InputLabel>
                    <Select
                      value={studyParameters.selectedDatasetId}
                      onChange={handleDatasetChange}
                    >
                      <MenuItem value="">
                        <em>No dataset selected</em>
                      </MenuItem>
                      {datasets.map((dataset) => (
                        <MenuItem key={dataset.id} value={dataset.id}>
                          {dataset.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Sample Size */}
                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    label="Sample Size"
                    value={studyParameters.sampleSize}
                    onChange={(e) => handleStudyParameterChange('sampleSize', e.target.value)}
                    placeholder="e.g., N = 150"
                  />
                </Grid>

                {/* Significance Level */}
                <Grid item xs={12} md={2}>
                  <TextField
                    fullWidth
                    label="Significance Level"
                    value={studyParameters.significanceLevel}
                    onChange={(e) => handleStudyParameterChange('significanceLevel', e.target.value)}
                    placeholder="0.05"
                  />
                </Grid>

                {/* Additional Notes */}
                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    label="Additional Notes"
                    value={studyParameters.additionalNotes}
                    onChange={(e) => handleStudyParameterChange('additionalNotes', e.target.value)}
                    placeholder="Study-wide notes..."
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Analysis Form Section */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Add Single Analysis
              </Typography>

              {/* Analysis Type */}
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Type of Analysis *</InputLabel>
                <Select
                  value={analysisFormData.analysisType}
                  onChange={handleAnalysisTypeChange}
                >
                  {ANALYSIS_TYPES.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Analysis Purpose */}
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Analysis Purpose *</InputLabel>
                <Select
                  value={analysisFormData.analysisPurpose}
                  onChange={handlePurposeChange}
                >
                  {ANALYSIS_PURPOSES.map((purpose) => (
                    <MenuItem key={purpose.value} value={purpose.value}>
                      {purpose.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Divider sx={{ my: 2 }} />

              {/* Variable Role Sections - Dynamic based on analysis type */}
              {visibleSections.showDependent && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Dependent Variable *
                  </Typography>
                  {selectedDataset ? (
                    <VariableSelector
                      datasetId={selectedDataset.id}
                      value={analysisFormData.dependentVariable}
                      onChange={(variable) => handleAnalysisVariableChange('dependentVariable', variable)}
                      allowedTypes={[DataType.NUMERIC, DataType.CATEGORICAL]}
                      placeholder="Select dependent variable"
                      multiple={false}
                    />
                  ) : (
                    <TextField
                      fullWidth
                      value={analysisFormData.dependentVariable}
                      onChange={(e) => handleAnalysisVariableChange('dependentVariable', e.target.value)}
                      placeholder="Enter dependent variable name"
                    />
                  )}
                </Box>
              )}

              {visibleSections.showIndependent && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Independent Variables
                  </Typography>
                  {selectedDataset ? (
                    <VariableSelector
                      datasetId={selectedDataset.id}
                      value={analysisFormData.independentVariables}
                      onChange={(variables) => handleAnalysisVariableChange('independentVariables', variables)}
                      allowedTypes={[DataType.NUMERIC, DataType.CATEGORICAL]}
                      placeholder="Select independent variables"
                      multiple={true}
                    />
                  ) : (
                    <TextField
                      fullWidth
                      value={analysisFormData.independentVariables.join(', ')}
                      onChange={(e) => handleAnalysisVariableChange('independentVariables', e.target.value.split(',').map(v => v.trim()).filter(v => v))}
                      placeholder="Enter independent variable names (comma-separated)"
                    />
                  )}
                </Box>
              )}

              {visibleSections.showCovariates && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Covariates
                  </Typography>
                  {selectedDataset ? (
                    <VariableSelector
                      datasetId={selectedDataset.id}
                      value={analysisFormData.covariates}
                      onChange={(variables) => handleAnalysisVariableChange('covariates', variables)}
                      allowedTypes={[DataType.NUMERIC, DataType.CATEGORICAL]}
                      placeholder="Select covariates"
                      multiple={true}
                    />
                  ) : (
                    <TextField
                      fullWidth
                      value={analysisFormData.covariates.join(', ')}
                      onChange={(e) => handleAnalysisVariableChange('covariates', e.target.value.split(',').map(v => v.trim()).filter(v => v))}
                      placeholder="Enter covariate names (comma-separated)"
                    />
                  )}
                </Box>
              )}

              {visibleSections.showDemographics && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Demographics Variables
                  </Typography>
                  {selectedDataset ? (
                    <VariableSelector
                      datasetId={selectedDataset.id}
                      value={analysisFormData.demographics}
                      onChange={(variables) => handleAnalysisVariableChange('demographics', variables)}
                      allowedTypes={[DataType.NUMERIC, DataType.CATEGORICAL]}
                      placeholder="Select demographic variables"
                      multiple={true}
                    />
                  ) : (
                    <TextField
                      fullWidth
                      value={analysisFormData.demographics.join(', ')}
                      onChange={(e) => handleAnalysisVariableChange('demographics', e.target.value.split(',').map(v => v.trim()).filter(v => v))}
                      placeholder="Enter demographic variable names (comma-separated)"
                    />
                  )}
                </Box>
              )}

              <Divider sx={{ my: 2 }} />

              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={addAnalysisToMethods}
                disabled={!analysisFormData.analysisType || !analysisFormData.analysisPurpose}
                fullWidth
                sx={{ mb: 2 }}
              >
                Add to Methods
              </Button>

              {analysisEntries.length > 0 && (
                <Button
                  variant="outlined"
                  startIcon={<ClearIcon />}
                  onClick={clearAllEntries}
                  fullWidth
                  color="warning"
                >
                  Clear All Analyses
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Analysis Entries List */}
          {analysisEntries.length > 0 && (
            <Card sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Added Analyses ({analysisEntries.length})
                </Typography>
                <List>
                  {analysisEntries.map((entry) => (
                    <ListItem
                      key={entry.id}
                      sx={{
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 1,
                        mb: 1,
                        bgcolor: 'background.paper'
                      }}
                      secondaryAction={
                        <IconButton
                          edge="end"
                          onClick={() => removeAnalysisEntry(entry.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      }
                    >
                      <ListItemText
                        primary={entry.analysisType}
                        primaryTypographyProps={{ component: 'div' }}
                        secondary={
                          <Box>
                            <Typography variant="body2" component="span" color="text.secondary" sx={{ display: 'block' }}>
                              Purpose: {entry.analysisPurpose}
                            </Typography>
                            {entry.dependentVariable && (
                              <Typography variant="body2" component="span" color="text.secondary" sx={{ display: 'block' }}>
                                Dependent: {entry.dependentVariable}
                              </Typography>
                            )}
                            {entry.independentVariables.length > 0 && (
                              <Typography variant="body2" component="span" color="text.secondary" sx={{ display: 'block' }}>
                                Independent: {entry.independentVariables.join(', ')}
                              </Typography>
                            )}
                            {entry.covariates.length > 0 && (
                              <Typography variant="body2" component="span" color="text.secondary" sx={{ display: 'block' }}>
                                Covariates: {entry.covariates.join(', ')}
                              </Typography>
                            )}
                            {entry.demographics.length > 0 && (
                              <Typography variant="body2" component="span" color="text.secondary" sx={{ display: 'block' }}>
                                Demographics: {entry.demographics.join(', ')}
                              </Typography>
                            )}
                          </Box>
                        }
                        secondaryTypographyProps={{ component: 'div' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* Generated Text Section */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Combined Methods Text
                </Typography>
                {combinedMethodsText && (
                  <Box>
                    <Button
                      size="small"
                      onClick={() => setIsEditing(!isEditing)}
                      sx={{ mr: 1 }}
                    >
                      {isEditing ? 'Preview' : 'Edit'}
                    </Button>
                    <Button
                      size="small"
                      startIcon={<CopyIcon />}
                      onClick={copyToClipboard}
                      sx={{ mr: 1 }}
                    >
                      Copy
                    </Button>
                    <Button
                      size="small"
                      startIcon={<DownloadIcon />}
                      onClick={exportAsHTML}
                    >
                      Export HTML
                    </Button>
                  </Box>
                )}
              </Box>

              {combinedMethodsText ? (
                <Paper variant="outlined" sx={{ p: 2, minHeight: 300 }}>
                  {isEditing ? (
                    <TextField
                      fullWidth
                      multiline
                      rows={12}
                      value={customText}
                      onChange={(e) => setCustomText(e.target.value)}
                      variant="outlined"
                    />
                  ) : (
                    <Typography variant="body1" sx={{ lineHeight: 1.8, textAlign: 'justify' }}>
                      {customText}
                    </Typography>
                  )}
                </Paper>
              ) : (
                <Paper variant="outlined" sx={{ p: 2, minHeight: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                    Add analyses using the form to build your comprehensive methods section.
                    Each analysis will be combined into a cohesive methods paragraph.
                  </Typography>
                </Paper>
              )}

              {/* Word Count */}
              {combinedMethodsText && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Word count: {customText.trim().split(/\s+/).filter(word => word.length > 0).length}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Add to Results Manager */}
      {combinedMethodsText && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <AddToResultsButton
            resultData={{
              title: `Statistical Methods Section (${analysisEntries.length} analyses)`,
              type: 'other' as const,
              component: 'StatisticalMethodsGenerator',
              data: {
                methodsText: customText,
                analysisEntries: analysisEntries,
                studyParameters: studyParameters,
                selectedDataset: selectedDataset?.name || 'Manual entry',
                wordCount: customText.trim().split(/\s+/).filter(word => word.length > 0).length,
                timestamp: new Date().toISOString()
              }
            }}
            onSuccess={() => {
              console.log('Methods section successfully added to Results Manager!'); // Changed to console.log
            }}
          />
        </Box>
      )}
    </Box>
    </PublicationReadyGate>
  );
};

export default StatisticalMethodsGenerator;
