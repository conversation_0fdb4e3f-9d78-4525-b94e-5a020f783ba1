import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  SelectChangeEvent,
  Snackbar,
} from '@mui/material';
import { useData } from '../../context/DataContext';
import { DataType } from '../../types';
import AddToResultsButton from '../UI/AddToResultsButton';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import {
  calculateMean,
  calculateStandardDeviation,
  calculateRange,
  calculateFrequencies,
  calculateProportions,
} from '../../utils/stats/descriptive'; // Import necessary descriptive stats functions
import { getOrderedCategoriesByColumnId } from '../../utils/dataUtilities';

interface Table1Result {
  variableName: string;
  dataType: DataType;
  results: {
    // Numerical results
    mean?: number;
    standardDeviation?: number;
    range?: number;
    // Categorical results
    frequencies?: Record<string, number>;
    percentages?: Record<string, number>;
  };
}

const Table1: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();


  // State for selected dataset and variables
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [selectedVariables, setSelectedVariables] = useState<string[]>([]);

  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [table1Results, setTable1Results] = useState<Table1Result[] | null>(null);
  const [tableWriteUp, setTableWriteUp] = useState<string | null>(null);

  // State for snackbar notifications
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Get the currently selected dataset based on selectedDatasetId
  const selectedDataset = datasets.find(dataset => dataset.id === selectedDatasetId);

  // Get available columns from the selected dataset
  const availableColumns = selectedDataset?.columns || [];

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedVariables([]); // Clear selected variables when dataset changes
    setTable1Results(null); // Clear results
    setTableWriteUp(null);

    // Update the current dataset in the DataContext
    const datasetToSet = datasets.find(dataset => dataset.id === newDatasetId);
    if (datasetToSet) {
      setCurrentDataset(datasetToSet);
    }
  };

  // Handle variable selection change
  const handleVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedVariables(typeof value === 'string' ? value.split(',') : value);
    setTable1Results(null); // Clear results when selection changes
    setTableWriteUp(null);
  };

  // Run Table 1 analysis
  const runTable1Analysis = () => {
    if (!selectedDataset || selectedVariables.length === 0) {
      setError('Please select a dataset and at least one variable to analyze.');
      setTable1Results(null);
      setTableWriteUp(null);
      return;
    }

    setLoading(true);
    setError(null);
    const results: Table1Result[] = [];

    selectedVariables.forEach(variableId => {
      const column = availableColumns.find(col => col.id === variableId);

      if (!column) {
        console.error(`Column with ID ${variableId} not found in selected dataset.`);
        return; // Skip if column not found
      }

      const columnData = selectedDataset.data.map(row => row[column.name]);

      if (column.type === DataType.NUMERIC) {
        const numericData = columnData.filter(val => typeof val === 'number' && !isNaN(val)) as number[];
        if (numericData.length > 0) {
          const mean = calculateMean(numericData);
          const standardDeviation = calculateStandardDeviation(numericData);
          const range = calculateRange(numericData);
          results.push({
            variableName: column.name,
            dataType: DataType.NUMERIC,
            results: { mean, standardDeviation, range },
          });
        } else {
           results.push({
            variableName: column.name,
            dataType: DataType.NUMERIC,
            results: {}, // No numeric data found
          });
        }
      } else { // Assume categorical for other types for now
        const categoricalData = columnData.map(String); // Convert all values to string for frequency count
        if (categoricalData.length > 0) {
            const frequencies = calculateFrequencies(categoricalData);
            const proportions = calculateProportions(categoricalData);
            const percentages: Record<string, number> = {};
            Object.keys(proportions).forEach(key => {
                percentages[key] = proportions[key] * 100;
            });
             results.push({
                variableName: column.name,
                dataType: column.type, // Use actual column type
                results: { frequencies, percentages },
            });
        } else {
             results.push({
                variableName: column.name,
                dataType: column.type,
                results: {}, // No data found
            });
        }
      }
    });

    setTable1Results(results);
    setTableWriteUp(generateTableWriteUp(results, selectedDataset.data.length)); // Generate write-up
    setLoading(false);
  };

  // Function to generate the table write-up
  const generateTableWriteUp = (results: Table1Result[], totalN: number): string => {
    let writeUp = `Table 1 presents the descriptive statistics for various variables from a dataset of ${totalN} observations. `;

    // Create sentences for each variable
    const sentences: string[] = [];

    results.forEach(result => {
      if (result.dataType === DataType.NUMERIC) {
        if (result.results.mean !== undefined && result.results.standardDeviation !== undefined && result.results.range !== undefined) {
          sentences.push(`The mean ${result.variableName.toLowerCase()} is ${result.results.mean.toFixed(2)}, with a standard deviation of ${result.results.standardDeviation.toFixed(2)} and a range of ${result.results.range.toFixed(2)}`);
        }
      } else {
        if (result.results.frequencies && Object.keys(result.results.frequencies).length > 0) {
          // Use ordered categories instead of Object.keys
          const column = selectedDataset?.columns.find(col => col.name === result.variableName);
          const categories = (column && selectedDataset) ? getOrderedCategoriesByColumnId(column.id, selectedDataset) : Object.keys(result.results.frequencies);

          if (categories.length === 2) {
            // Binary variable - use special formatting
            const sortedCategories = categories.sort((a, b) => {
              const freqA = result.results.frequencies![a];
              const freqB = result.results.frequencies![b];
              return freqB - freqA; // Sort by frequency, highest first
            });

            const firstCategory = sortedCategories[0];
            const secondCategory = sortedCategories[1];
            const firstPercentage = result.results.percentages![firstCategory];
            const secondPercentage = result.results.percentages![secondCategory];

            sentences.push(`${firstPercentage.toFixed(0)}% of the sample is ${firstCategory.toLowerCase()}, while ${secondPercentage.toFixed(0)}% is ${secondCategory.toLowerCase()}`);
          } else {
            // Multi-category variable
            const categoryDescriptions = categories.map(category => {
              const percentage = result.results.percentages![category];
              return `${percentage.toFixed(0)}% have ${category.toLowerCase()}`;
            });

            if (categoryDescriptions.length > 1) {
              const lastCategory = categoryDescriptions.pop();
              sentences.push(`${result.variableName} levels are distributed as follows: ${categoryDescriptions.join(', ')}, and ${lastCategory}`);
            } else {
              sentences.push(`${result.variableName}: ${categoryDescriptions[0]}`);
            }
          }
        }
      }
    });

    // Join sentences with proper punctuation
    if (sentences.length > 0) {
      writeUp += sentences.join('. ') + '. ';
    }

    // Add closing statement
    writeUp += `Overall, the table provides a comprehensive overview of the demographic and characteristic variables of the sample, highlighting the central tendencies and distributions of these variables.`;

    return writeUp;
  };



  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <PublicationReadyGate>
      <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Publication Ready Table 1
      </Typography>

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Data and Variables
        </Typography>

        <Alert severity="info" sx={{ mb: 2 }}>
            This table provides descriptive statistics (mean, standard deviation, range for numerical variables; frequencies and percentages for categorical variables) for selected variables in your dataset.
        </Alert>

        <Grid container spacing={2}>
           {/* Dataset Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          {/* Variable Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="variable-select-label">Variables</InputLabel>
              <Select
                labelId="variable-select-label"
                id="variable-select"
                multiple
                value={selectedVariables}
                onChange={handleVariableChange}
                label="Variables"
                disabled={availableColumns.length === 0}
                renderValue={(selected) => selected.map(id => availableColumns.find(col => col.id === id)?.name || '').join(', ')}
              >
                {availableColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No variables available in selected dataset
                  </MenuItem>
                ) : (
                  availableColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name} ({column.type})
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={runTable1Analysis}
            disabled={loading || selectedVariables.length === 0 || !selectedDataset}
          >
            Generate Table 1
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {table1Results && !loading && selectedDataset && (
        <Box>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Table 1. Descriptive Statistics
            </Typography>

            {/* Table Rendering */}
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Variable</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="right">Overall (n={selectedDataset.data.length || 0})</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {table1Results.map((result, index) => (
                    <React.Fragment key={index}>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 'bold' }}>{result.variableName}</TableCell>
                        <TableCell align="right"></TableCell> {/* Empty cell for variable row */}
                      </TableRow>
                      {result.dataType === DataType.NUMERIC ? (
                        <>
                          {result.results.mean !== undefined && result.results.standardDeviation !== undefined && (
                            <TableRow>
                              <TableCell sx={{ pl: 4 }}>Mean (SD)</TableCell>
                              <TableCell align="right">{result.results.mean.toFixed(2)} ({result.results.standardDeviation.toFixed(2)})</TableCell>
                            </TableRow>
                          )}
                           {result.results.range !== undefined && (
                            <TableRow>
                              <TableCell sx={{ pl: 4 }}>Range</TableCell>
                              <TableCell align="right">{result.results.range.toFixed(2)}</TableCell>
                            </TableRow>
                          )}
                        </>
                      ) : (
                        result.results.frequencies && (() => {
                          // Use ordered categories for display
                          const column = selectedDataset?.columns.find(col => col.name === result.variableName);
                          const orderedCategories = (column && selectedDataset) ? getOrderedCategoriesByColumnId(column.id, selectedDataset) : Object.keys(result.results.frequencies!);
                          return orderedCategories.map((category, catIndex) => (
                            <TableRow key={catIndex}>
                              <TableCell sx={{ pl: 4 }}>{category}</TableCell>
                              <TableCell align="right">{result.results.frequencies![category]} ({result.results.percentages![category].toFixed(1)}%)</TableCell>
                            </TableRow>
                          ));
                        })()
                      )}
                    </React.Fragment>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

          </Paper>

           {/* Table Write-up */}
          {tableWriteUp && (
            <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Table Write-up
              </Typography>
              <Typography variant="body1" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
                {tableWriteUp}
              </Typography>
            </Paper>
          )}

          {/* Add to Results Manager Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <AddToResultsButton
              resultData={{
                title: `Table 1 - Descriptive Statistics (${selectedDataset.name})`,
                type: 'descriptive' as const,
                component: 'Table1',
                data: {
                  dataset: selectedDataset.name,
                  variables: selectedVariables.map(id =>
                    availableColumns.find(col => col.id === id)?.name || id
                  ),
                  results: table1Results,
                  writeUp: tableWriteUp,
                  timestamp: new Date().toISOString(),
                  totalSampleSize: selectedDataset.data.length
                }
              }}
              onSuccess={() => {
                setSnackbarMessage('Results successfully added to Results Manager!');
                setSnackbarOpen(true);
              }}
              onError={(error) => {
                setSnackbarMessage(`Error adding results to Results Manager: ${error}`);
                setSnackbarOpen(true);
              }}
            />
          </Box>
        </Box>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Box>
    </PublicationReadyGate>
  );
};

export default Table1;
