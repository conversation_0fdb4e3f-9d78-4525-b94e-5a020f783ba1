import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  SelectChangeEvent,
  Snackbar,
} from '@mui/material';

import { useData } from '../../context/DataContext';
import AddToResultsButton from '../UI/AddToResultsButton';
import { DataType, Column, Dataset } from '../../types';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import {
  calculateFrequencies,
  calculateProportions,
} from '../../utils/stats/descriptive';
import { getOrderedCategoriesByColumnId } from '../../utils/dataUtilities';

interface Table1aResult {
  variableName: string;
  // Frequencies and percentages per category
  categoryData: {
    [category: string]: {
      n: number;
      percentage: number;
    };
  };
}

const Table1a: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();


  // State for selected dataset and row variables
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [rowVariableIds, setRowVariableIds] = useState<string[]>([]);

  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [table1aResults, setTable1aResults] = useState<Table1aResult[] | null>(null);
  const [commonCategories, setCommonCategories] = useState<string[]>([]);

  // State for snackbar notifications
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');


  // Get the currently selected dataset based on selectedDatasetId
  const selectedDataset = datasets.find(dataset => dataset.id === selectedDatasetId);

  // Get available columns from the selected dataset
  const availableColumns = selectedDataset?.columns || [];

  // Filter categorical columns for row variables
  const categoricalColumns = availableColumns.filter(col => col.type === DataType.CATEGORICAL);

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setRowVariableIds([]); // Clear row variables
    setTable1aResults(null); // Clear results
    setError(null);
    setCommonCategories([]);

    // Update the current dataset in the DataContext
    const datasetToSet = datasets.find(dataset => dataset.id === newDatasetId);
    if (datasetToSet) {
      setCurrentDataset(datasetToSet);
    }
  };

  // Handle row variable selection change
  const handleRowVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    const selectedIds = typeof value === 'string' ? value.split(',') : value;
    setRowVariableIds(selectedIds);
    setTable1aResults(null); // Clear results when selection changes
    setError(null);
    setCommonCategories([]);

    // Check if selected variables have the same categories
    if (selectedDataset && selectedIds.length > 0) {
        const selectedColumns = availableColumns.filter(col => selectedIds.includes(col.id));
        if (selectedColumns.every(col => col.type === DataType.CATEGORICAL)) {
            const firstVariableCategories = Array.from(new Set(selectedDataset.data.map(row => String(row[selectedColumns[0].name]))));
            const allHaveSameCategories = selectedColumns.every(col => {
                const categories = Array.from(new Set(selectedDataset.data.map(row => String(row[col.name]))));
                return categories.length === firstVariableCategories.length &&
                       categories.every(cat => firstVariableCategories.includes(cat));
            });

            if (!allHaveSameCategories) {
                setError('Please select only categorical variables that have the exact same categories.');
                setCommonCategories([]);
            } else {
                setError(null);
                // Use ordered categories from the first variable instead of alphabetical sorting
                const firstColumn = selectedColumns[0];
                const orderedCategories = getOrderedCategoriesByColumnId(firstColumn.id, selectedDataset);
                setCommonCategories(orderedCategories);
            }
        } else {
             setError('Please select only categorical variables.');
             setCommonCategories([]);
        }
    } else {
        setError(null);
        setCommonCategories([]);
    }
  };


  // Function to run Table 1a analysis
  const runTable1aAnalysis = () => {
    if (!selectedDataset || rowVariableIds.length === 0 || error) {
      setError(error || 'Please select a dataset and at least one row variable to analyze.');
      setTable1aResults(null);
      return;
    }

    setLoading(true);
    setError(null);
    const results: Table1aResult[] = [];

    const selectedColumns = availableColumns.filter(col => rowVariableIds.includes(col.id));

    selectedColumns.forEach(column => {
        const variableData = selectedDataset.data.map(row => String(row[column.name]));
        const frequencies = calculateFrequencies(variableData);
        const proportions = calculateProportions(variableData);

        const categoryData: Table1aResult['categoryData'] = {};
        commonCategories.forEach(category => {
            categoryData[category] = {
                n: frequencies[category] || 0,
                percentage: (proportions[category] || 0) * 100,
            };
        });

        results.push({
            variableName: column.name,
            categoryData,
        });
    });

    setTable1aResults(results);
    setLoading(false);
  };



  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <PublicationReadyGate>
      <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Publication Ready Table 1a
      </Typography>

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Data and Variables
        </Typography>

        <Alert severity="info" sx={{ mb: 2 }}>
            This table is applicable for several categorical variables that have the exact same categories (e.g., multiple variables measured on the same Likert scale or multiple Yes/No variables). Please select only such variables.
        </Alert>

        <Grid container spacing={2}>
           {/* Dataset Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          {/* Row Variables Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="row-variables-select-label">Row Variables (Categorical with Same Categories)</InputLabel>
              <Select
                labelId="row-variables-select-label"
                id="row-variables-select"
                multiple
                value={rowVariableIds}
                onChange={handleRowVariableChange}
                label="Row Variables (Categorical with Same Categories)"
                disabled={categoricalColumns.length === 0 || !selectedDataset}
                renderValue={(selected) => selected.map(id => availableColumns.find(col => col.id === id)?.name || '').join(', ')}
              >
                {categoricalColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No categorical variables available
                  </MenuItem>
                ) : (
                  categoricalColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={runTable1aAnalysis}
            disabled={loading || !selectedDataset || rowVariableIds.length === 0 || error !== null}
          >
            Generate Table 1a
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {table1aResults && !loading && selectedDataset && rowVariableIds.length > 0 && commonCategories.length > 0 && (
        <Box>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Table 1a. Descriptive Statistics for Categorical Variables with Same Categories
            </Typography>

            {/* Table Rendering */}
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Variable</TableCell>
                    {/* Categories as Column Headers */}
                    {commonCategories.map((category, index) => (
                        <TableCell key={index} sx={{ fontWeight: 'bold' }} align="center" colSpan={2}>
                            {category}
                        </TableCell>
                    ))}
                  </TableRow>
                   <TableRow>
                     <TableCell></TableCell> {/* Empty cell for Variable column */}
                     {/* Sub-headers for n and % */}
                     {commonCategories.map((category, index) => (
                         <React.Fragment key={index}>
                           <TableCell sx={{ fontWeight: 'bold' }} align="center">n</TableCell>
                           <TableCell sx={{ fontWeight: 'bold' }} align="center">%</TableCell>
                         </React.Fragment>
                     ))}
                   </TableRow>
                </TableHead>
                <TableBody>
                  {table1aResults.map((result, index) => (
                    <TableRow key={index}>
                      <TableCell sx={{ fontWeight: 'bold' }}>{result.variableName}</TableCell>
                       {/* Data cells for each category */}
                       {commonCategories.map((category, catIndex) => (
                           <React.Fragment key={catIndex}>
                             <TableCell align="center">{result.categoryData[category]?.n !== undefined ? result.categoryData[category].n : 'N/A'}</TableCell>
                             <TableCell align="center">{result.categoryData[category]?.percentage !== undefined ? `${result.categoryData[category].percentage.toFixed(1)}%` : 'N/A'}</TableCell>
                           </React.Fragment>
                       ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

          </Paper>

          {/* Add to Results Manager Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <AddToResultsButton
              resultData={{
                title: `Table 1a - Advanced Descriptive Statistics (${selectedDataset.name})`,
                type: 'descriptive' as const,
                component: 'Table1a',
                data: {
                  dataset: selectedDataset.name,
                  variables: rowVariableIds.map(id =>
                    availableColumns.find(col => col.id === id)?.name || id
                  ),
                  results: table1aResults,
                  commonCategories: commonCategories,
                  timestamp: new Date().toISOString(),
                  totalSampleSize: selectedDataset.data.length
                }
              }}
              onSuccess={() => {
                setSnackbarMessage('Results successfully added to Results Manager!');
                setSnackbarOpen(true);
              }}
              onError={(error) => {
                setSnackbarMessage(`Error adding results to Results Manager: ${error}`);
                setSnackbarOpen(true);
              }}
            />
          </Box>
        </Box>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Box>
    </PublicationReadyGate>
  );
};

export default Table1a;
