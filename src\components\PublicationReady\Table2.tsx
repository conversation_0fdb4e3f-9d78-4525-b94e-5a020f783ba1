import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  SelectChangeEvent,
  Snackbar,
} from '@mui/material';

import { useData } from '../../context/DataContext';
import AddToResultsButton from '../UI/AddToResultsButton';
import { DataType, Column, Dataset } from '../../types';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import {
  calculateMean,
  calculateStandardDeviation,
  calculateRange,
  calculateFrequencies,
  calculateProportions,
} from '../../utils/stats/descriptive';
import { comprehensiveNormalityTest } from '../../utils/stats/normality';
import { getOrderedCategoriesByColumnId } from '../../utils/dataUtilities';
import { independentSamplesTTest } from '../../utils/stats/inference/t-tests'; // For binary grouping, numerical row (normal)
import { oneWayANOVA } from '../../utils/stats/inference/anova'; // For multi-category grouping, numerical row (normal)
import {
  mannWhitneyUTest, // For binary grouping, numerical row (non-normal)
  kruskalWallisTest, // For multi-category grouping, numerical row (non-normal)
  chiSquareTest, // For categorical row
} from '../../utils/stats/non-parametric';

interface GroupDescriptives {
  n: number;
  mean?: number;
  standardDeviation?: number;
  range?: number;
  frequencies?: Record<string, number>;
  percentages?: Record<string, number>;
  rowPercentages?: Record<string, number>; // Added for row percentages
  columnPercentages?: Record<string, number>; // Added for column percentages
}

interface Table2Result {
  rowVariableName: string;
  rowDataType: DataType;
  // Descriptive stats per group and overall
  descriptives: {
    [groupCategory: string]: GroupDescriptives;
    Overall: GroupDescriptives;
  };
  // Statistical test results
  testResult?: {
    testName: string;
    pValue: number;
    statistic?: number; // e.g., t, F, U, H, Chi-Square
    df?: number | [number, number]; // Degrees of freedom
    // Add other relevant test outputs as needed
  };
  // For categorical variables, list the categories
  categories?: string[];
  testSuperscript?: string; // Added for superscript
}

type PercentageType = 'row' | 'column';

const Table2: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();


  // State for selected dataset, grouping variable, and row variables
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [groupingVariableId, setGroupingVariableId] = useState<string | ''>('');
  const [rowVariableIds, setRowVariableIds] = useState<string[]>([]);
  const [percentageType, setPercentageType] = useState<PercentageType>('row'); // New state for percentage type

  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [table2Results, setTable2Results] = useState<Table2Result[] | null>(null);
  const [tableWriteUp, setTableWriteUp] = useState<string | null>(null);
  const [groupingCategories, setGroupingCategories] = useState<string[]>([]);
  const [totalNPerGroup, setTotalNPerGroup] = useState<Record<string, number>>({});
  const [testSuperscripts, setTestSuperscripts] = useState<Record<string, string>>({}); // New state for test superscripts

  // State for snackbar notifications
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');


  // Get the currently selected dataset based on selectedDatasetId
  const selectedDataset = datasets.find(dataset => dataset.id === selectedDatasetId);

  // Get available columns from the selected dataset
  const availableColumns = selectedDataset?.columns || [];

  // Filter categorical columns for grouping variable
  const categoricalColumns = availableColumns.filter(col => col.type === DataType.CATEGORICAL);

  // Filter columns that can be row variables (all except the selected grouping variable)
  const rowVariableColumns = availableColumns.filter(col => col.id !== groupingVariableId);

  // Get the grouping column based on the selected grouping variable ID
  const groupingColumn = availableColumns.find(col => col.id === groupingVariableId);

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setGroupingVariableId(''); // Clear grouping variable
    setRowVariableIds([]); // Clear row variables
    setTable2Results(null); // Clear results
    setTableWriteUp(null);
    setGroupingCategories([]);
    setTotalNPerGroup({});
    setPercentageType('row'); // Reset percentage type
    setTestSuperscripts({}); // Clear superscripts

    // Update the current dataset in the DataContext
    const datasetToSet = datasets.find(dataset => dataset.id === newDatasetId);
    if (datasetToSet) {
      setCurrentDataset(datasetToSet);
    }
  };

  // Handle grouping variable selection change
  const handleGroupingVariableChange = (event: SelectChangeEvent<string>) => {
    const newGroupingVariableId = event.target.value;
    setGroupingVariableId(newGroupingVariableId);
    setRowVariableIds([]); // Clear row variables when grouping variable changes
    setTable2Results(null); // Clear results
    setTableWriteUp(null);
    setGroupingCategories([]);
    setTotalNPerGroup({});
    setPercentageType('row'); // Reset percentage type
    setTestSuperscripts({}); // Clear superscripts


    if (selectedDataset && newGroupingVariableId) {
        const groupingColumn = availableColumns.find(col => col.id === newGroupingVariableId);
        if (groupingColumn && groupingColumn.type === DataType.CATEGORICAL) {
            const categories = Array.from(new Set(selectedDataset.data.map(row => String(row[groupingColumn.name]))));
            setGroupingCategories(categories);

            // Calculate total n per group
            const nPerGroup: Record<string, number> = {};
            categories.forEach(category => {
                nPerGroup[category] = selectedDataset.data.filter(row => String(row[groupingColumn.name]) === category).length;
            });
            setTotalNPerGroup(nPerGroup);
        }
    }
  };

  // Handle row variable selection change
  const handleRowVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setRowVariableIds(typeof value === 'string' ? value.split(',') : value);
    setTable2Results(null); // Clear results when selection changes
    setTableWriteUp(null);
    setTestSuperscripts({}); // Clear superscripts
  };

  // Handle percentage type selection change
  const handlePercentageTypeChange = (event: SelectChangeEvent<string>) => {
    setPercentageType(event.target.value as PercentageType);
    // Re-run analysis to update percentages in the table
    // This might be inefficient for large datasets, could optimize later
    if (table2Results) {
        runTable2Analysis();
    }
  };


  // Function to calculate row percentages for a contingency table row
  const calculateRowPercentages = (rowCounts: number[]): number[] => {
      const rowTotal = rowCounts.reduce((sum, count) => sum + count, 0);
      if (rowTotal === 0) return rowCounts.map(() => 0);
      return rowCounts.map(count => (count / rowTotal) * 100);
  };

  // Function to calculate column percentages for a contingency table
  const calculateColumnPercentages = (contingencyTable: number[][]): number[][] => {
      if (contingencyTable.length === 0 || contingencyTable[0].length === 0) return [];

      const numRows = contingencyTable.length;
      const numCols = contingencyTable[0].length;
      const columnTotals = new Array(numCols).fill(0);

      // Calculate column totals
      for (let j = 0; j < numCols; j++) {
          for (let i = 0; i < numRows; i++) {
              columnTotals[j] += contingencyTable[i][j];
          }
      }

      // Calculate column percentages
      const columnPercentages: number[][] = [];
      for (let i = 0; i < numRows; i++) {
          const rowPercentages: number[] = [];
          for (let j = 0; j < numCols; j++) {
              if (columnTotals[j] === 0) {
                  rowPercentages.push(0);
              } else {
                  rowPercentages.push((contingencyTable[i][j] / columnTotals[j]) * 100);
              }
          }
          columnPercentages.push(rowPercentages);
      }
      return columnPercentages;
  };


  // Function to run Table 2 analysis
  const runTable2Analysis = () => {
    if (!selectedDataset || !groupingVariableId || rowVariableIds.length === 0) {
      setError('Please select a dataset, a grouping variable, and at least one row variable to analyze.');
      setTable2Results(null);
      setTableWriteUp(null);
      return;
    }

    setLoading(true);
    setError(null);
    const results: Table2Result[] = [];
    const testSuperscriptMapping: Record<string, string> = {};
    let superscriptIndex = 0;
    const alphabet = 'abcdefghijklmnopqrstuvwxyz';


    const groupingColumn = availableColumns.find(col => col.id === groupingVariableId);
    if (!groupingColumn || groupingColumn.type !== DataType.CATEGORICAL) {
        setError('Selected grouping variable is not categorical.');
        setLoading(false);
        return;
    }

    // Use ordered categories instead of alphabetical sorting
    const categories = getOrderedCategoriesByColumnId(groupingVariableId, selectedDataset);
    setGroupingCategories(categories); // Ensure grouping categories are set

    const isBinaryGrouping = categories.length === 2;
    const isMultiCategoryGrouping = categories.length > 2;

    if (!isBinaryGrouping && !isMultiCategoryGrouping) {
         setError('Grouping variable must have at least two categories.');
         setLoading(false);
         return;
    }

     // Calculate total n per group (re-calculate here to be safe)
    const nPerGroup: Record<string, number> = {};
    categories.forEach(category => {
        nPerGroup[category] = selectedDataset.data.filter(row => String(row[groupingColumn.name]) === category).length;
    });
    setTotalNPerGroup(nPerGroup);


    rowVariableIds.forEach(rowVariableId => {
      const rowColumn = availableColumns.find(col => col.id === rowVariableId);

      if (!rowColumn) {
        console.error(`Row variable with ID ${rowVariableId} not found in selected dataset.`);
        return; // Skip if column not found
      }

      const rowVariableData = selectedDataset.data.map(row => row[rowColumn.name]);

      // 1. Calculate descriptives per group and overall
      const descriptives: Table2Result['descriptives'] = {
          Overall: { n: selectedDataset.data.length }
      };

      // Overall descriptives
      if (rowColumn.type === DataType.NUMERIC) {
          const numericData = rowVariableData.filter(val => typeof val === 'number' && !isNaN(val)) as number[];
          if (numericData.length > 0) {
              descriptives.Overall.mean = calculateMean(numericData);
              descriptives.Overall.standardDeviation = calculateStandardDeviation(numericData);
              descriptives.Overall.range = calculateRange(numericData);
              descriptives.Overall.n = numericData.length; // Update n to actual count of numeric values
          } else {
               descriptives.Overall.n = 0;
          }
      } else {
           const categoricalData = rowVariableData.map(String);
           if (categoricalData.length > 0) {
               descriptives.Overall.frequencies = calculateFrequencies(categoricalData);
               descriptives.Overall.percentages = calculateProportions(categoricalData); // Overall percentages
               Object.keys(descriptives.Overall.percentages).forEach(key => {
                   descriptives.Overall.percentages![key] *= 100;
               });
               descriptives.Overall.n = categoricalData.length;
           } else {
               descriptives.Overall.n = 0;
           }
      }


      // Group-wise descriptives
      categories.forEach(category => {
          const groupData = selectedDataset.data
              .filter(row => String(row[groupingColumn.name]) === category)
              .map(row => row[rowColumn.name]);

          descriptives[category] = { n: groupData.length };

          if (rowColumn.type === DataType.NUMERIC) {
              const numericGroupData = groupData.filter(val => typeof val === 'number' && !isNaN(val)) as number[];
              if (numericGroupData.length > 0) {
                  descriptives[category].mean = calculateMean(numericGroupData);
                  descriptives[category].standardDeviation = calculateStandardDeviation(numericGroupData);
                  descriptives[category].range = calculateRange(numericGroupData);
                  descriptives[category].n = numericGroupData.length;
              } else {
                   descriptives[category].n = 0;
              }
          } else {
              const categoricalGroupData = groupData.map(String);
              if (categoricalGroupData.length > 0) {
                  descriptives[category].frequencies = calculateFrequencies(categoricalGroupData);
                  // Group-wise percentages (based on group n) - this is essentially column percentage within the group
                  descriptives[category].percentages = calculateProportions(categoricalGroupData);
                   Object.keys(descriptives[category].percentages!).forEach(key => {
                       descriptives[category].percentages![key] *= 100;
                   });
                   descriptives[category].n = categoricalGroupData.length;
              } else {
                   descriptives[category].n = 0;
              }
          }
      });

       // Calculate row and column percentages for categorical variables
       if (rowColumn.type === DataType.CATEGORICAL) {
           // Use ordered categories for row variables
           const rowCategories = getOrderedCategoriesByColumnId(rowColumn.id, selectedDataset);
           const contingencyTable: number[][] = [];

           rowCategories.forEach(rowCat => {
               const rowCounts: number[] = [];
               categories.forEach(groupCat => {
                   const count = selectedDataset.data.filter(row =>
                       String(row[groupingColumn.name]) === groupCat && String(row[rowColumn.name]) === rowCat
                   ).length;
                   rowCounts.push(count);
               });
               contingencyTable.push(rowCounts);
           });

           const columnPercentagesTable = calculateColumnPercentages(contingencyTable);

           rowCategories.forEach((rowCat, rowIndex) => {
               categories.forEach((groupCat, groupIndex) => {
                   if (!descriptives[groupCat].columnPercentages) {
                       descriptives[groupCat].columnPercentages = {};
                   }
                   descriptives[groupCat].columnPercentages[rowCat] = columnPercentagesTable[rowIndex][groupIndex];
               });

               // Calculate row percentages for each row category across groups + total
               const rowCountsForCategory = categories.map(groupCat => descriptives[groupCat]?.frequencies?.[rowCat] || 0);
               const totalCountForCategory = descriptives.Overall?.frequencies?.[rowCat] || 0;
               const rowPercentagesForCategory = calculateRowPercentages([...rowCountsForCategory, totalCountForCategory]);

                categories.forEach((groupCat, groupIndex) => {
                   if (!descriptives[groupCat].rowPercentages) {
                       descriptives[groupCat].rowPercentages = {};
                   }
                   descriptives[groupCat].rowPercentages[rowCat] = rowPercentagesForCategory[groupIndex];
               });

               if (!descriptives.Overall.rowPercentages) {
                   descriptives.Overall.rowPercentages = {};
               }
               descriptives.Overall.rowPercentages[rowCat] = rowPercentagesForCategory[rowPercentagesForCategory.length - 1]; // Last element is total row percentage

           });
       }


      // 2. Perform statistical test
      let testResult: Table2Result['testResult'] | undefined = undefined;

      if (rowColumn.type === DataType.CATEGORICAL) {
          // Chi-Square test for categorical row variables
          // Use ordered categories for row variables
          const rowCategories = getOrderedCategoriesByColumnId(rowColumn.id, selectedDataset);
           if (categories.length >= 2 && rowCategories.length >= 2) { // Chi-square requires at least 2x2
              try {
                  // Need to build a contingency table
                  const contingencyTable: number[][] = [];

                  rowCategories.forEach(rowCat => {
                      const rowCounts: number[] = [];
                      categories.forEach(groupCat => {
                          const count = selectedDataset.data.filter(row =>
                              String(row[groupingColumn.name]) === groupCat && String(row[rowColumn.name]) === rowCat
                          ).length;
                          rowCounts.push(count);
                      });
                      contingencyTable.push(rowCounts);
                  });

                  const chiSquareStats = chiSquareTest(contingencyTable);
                  testResult = {
                      testName: 'Chi-Square Test',
                      pValue: chiSquareStats.pValue,
                      statistic: chiSquareStats.chiSquare,
                      df: chiSquareStats.df,
                  };
              } catch (e) {
                  console.error(`Error performing Chi-Square test for ${rowColumn.name}:`, e);
                  testResult = { testName: 'Chi-Square Test', pValue: NaN };
              }
          }

      } else if (rowColumn.type === DataType.NUMERIC) {
          // Tests for numerical row variables depend on grouping variable categories and normality

          // Extract numerical data per group
          const numericGroups: number[][] = categories.map(category => {
              const values = selectedDataset.data
                  .filter(row => String(row[groupingColumn.name]) === category)
                  .map(row => row[rowColumn.name])
                  .filter(val => typeof val === 'number' && !isNaN(val));
              return values as number[];
          });

          // Check normality for each group (simplified check for now, could be more robust)
          // A more rigorous approach might check normality of residuals or use a combined test
          const areGroupsNormallyDistributed = numericGroups.every(group => {
              // Need at least a few data points to test normality
              if (group.length < 3) return false; // Cannot reliably test normality with very small samples
              try {
                 const normality = comprehensiveNormalityTest(group, 0.05, ['auto']);
                 return normality.overallAssessment.isNormal;
              } catch (e) {
                 console.error(`Error checking normality for a group in ${rowColumn.name}:`, e);
                 return false; // Assume non-normal if test fails
              }
          });


          if (isBinaryGrouping) {
              // Binary grouping variable (e.g., Group A vs Group B)
              if (numericGroups.length === 2) {
                  const group1Data = numericGroups[0];
                  const group2Data = numericGroups[1];

                  if (group1Data.length >= 2 && group2Data.length >= 2) { // Need at least 2 per group for t-test
                      if (areGroupsNormallyDistributed) {
                          // Independent samples t-test (assuming equal variances for simplicity)
                          try {
                              const tTestStats = independentSamplesTTest(group1Data, group2Data, { equalVariances: true });
                              testResult = {
                                  testName: 'Independent Samples t-Test',
                                  pValue: tTestStats.pValue,
                                  statistic: tTestStats.t,
                                  df: tTestStats.df,
                              };
                          } catch (e) {
                              console.error(`Error performing t-test for ${rowColumn.name}:`, e);
                              testResult = { testName: 'Independent Samples t-Test', pValue: NaN };
                          }
                      } else {
                          // Mann-Whitney U test (non-parametric alternative)
                          try {
                              const mannWhitneyStats = mannWhitneyUTest(group1Data, group2Data);
                              testResult = {
                                  testName: 'Mann-Whitney U Test',
                                  pValue: mannWhitneyStats.pValue,
                                  statistic: mannWhitneyStats.U, // Or z-score: mannWhitneyStats.z
                                  // df is not applicable for Mann-Whitney U
                              };
                          } catch (e) {
                              console.error(`Error performing Mann-Whitney U test for ${rowColumn.name}:`, e);
                              testResult = { testName: 'Mann-Whitney U Test', pValue: NaN };
                          }
                      }
                  }
              }

          } else if (isMultiCategoryGrouping) {
              // Multi-category grouping variable (e.g., Group A vs Group B vs Group C)
              if (numericGroups.length >= 2 && numericGroups.every(g => g.length > 0)) { // Need at least 2 groups with data
                   if (areGroupsNormallyDistributed) {
                       // One-Way ANOVA
                       try {
                           const anovaStats = oneWayANOVA(numericGroups);
                           testResult = {
                               testName: 'One-Way ANOVA',
                               pValue: anovaStats.pValue,
                               statistic: anovaStats.F,
                               df: [anovaStats.dfBetween, anovaStats.dfWithin],
                           };
                       } catch (e) {
                           console.error(`Error performing One-Way ANOVA for ${rowColumn.name}:`, e);
                           testResult = { testName: 'One-Way ANOVA', pValue: NaN };
                       }
                   } else {
                       // Kruskal-Wallis test (non-parametric alternative)
                       try {
                           const kruskalWallisStats = kruskalWallisTest(numericGroups);
                           testResult = {
                               testName: 'Kruskal-Wallis Test',
                               pValue: kruskalWallisStats.pValue,
                               statistic: kruskalWallisStats.H,
                               df: kruskalWallisStats.df,
                           };
                       } catch (e) {
                           console.error(`Error performing Kruskal-Wallis test for ${rowColumn.name}:`, e);
                           testResult = { testName: 'Kruskal-Wallis Test', pValue: NaN };
                       }
                   }
              }
          }
      }

      const rowResult: Table2Result = {
        rowVariableName: rowColumn.name,
        rowDataType: rowColumn.type,
        descriptives,
        testResult,
      };

      if (rowColumn.type === DataType.CATEGORICAL) {
          // Use ordered categories for consistent display
          rowResult.categories = getOrderedCategoriesByColumnId(rowColumn.id, selectedDataset);
      }

       // Assign superscript if test result exists and test name is not already mapped
       if (testResult?.testName && !testSuperscriptMapping[testResult.testName]) {
           testSuperscriptMapping[testResult.testName] = alphabet[superscriptIndex % alphabet.length];
           superscriptIndex++;
       }
       if (testResult?.testName) {
           rowResult.testSuperscript = testSuperscriptMapping[testResult.testName];
       }


      results.push(rowResult);
    });

    setTable2Results(results);
    setTestSuperscripts(testSuperscriptMapping); // Update the state with the mapping
    setTableWriteUp(generateTableWriteUp(results, selectedDataset, groupingColumn, categories)); // Generate write-up
    setLoading(false);
  };

  // Function to generate the table write-up
  const generateTableWriteUp = (results: Table2Result[], dataset: Dataset, groupingColumn: Column | undefined, groupingCategories: string[]): string => {
    const totalN = dataset.data.length;
    let writeUp = `Table 2 presents the descriptive statistics and group comparisons for various variables across different ${groupingColumn?.name?.toLowerCase() || 'groups'} from a dataset of ${totalN} observations. `;

    // Describe the sample distribution across groups
    if (groupingCategories.length > 0) {
      const groupDescriptions = groupingCategories.map(category => {
        const groupN = totalNPerGroup[category] || 0;
        const percentage = ((groupN / totalN) * 100).toFixed(1);
        return `${category} (${percentage}%)`;
      });

      writeUp += `The sample is divided into ${groupingCategories.length} ${groupingColumn?.name?.toLowerCase() || 'groups'}: ${groupDescriptions.join(', ')}. `;
    }

    // Create sentences for each variable
    const sentences: string[] = [];

    results.forEach(result => {
      if (result.rowDataType === DataType.NUMERIC) {
        // Handle numerical variables
        const groupMeans: string[] = [];
        const groupStats: Array<{category: string, mean: number, sd: number}> = [];

        groupingCategories.forEach(category => {
          const descriptives = result.descriptives[category];
          if (descriptives?.mean !== undefined && descriptives?.standardDeviation !== undefined) {
            groupMeans.push(`${category} having a mean of ${descriptives.mean.toFixed(2)} (SD ${descriptives.standardDeviation.toFixed(2)})`);
            groupStats.push({category, mean: descriptives.mean, sd: descriptives.standardDeviation});
          }
        });

        if (groupStats.length > 0) {
          // Find highest and lowest means
          const sortedByMean = [...groupStats].sort((a, b) => b.mean - a.mean);
          const overallMean = result.descriptives.Overall?.mean?.toFixed(2) || 'N/A';
          const overallSD = result.descriptives.Overall?.standardDeviation?.toFixed(2) || 'N/A';

          let sentence = `The mean ${result.rowVariableName.toLowerCase()} varies`;
          if (result.testResult?.pValue !== undefined && result.testResult.pValue < 0.05) {
            sentence += ` significantly`;
          }
          sentence += ` across ${groupingColumn?.name?.toLowerCase() || 'groups'}, with ${sortedByMean[0].category} having the highest mean of ${sortedByMean[0].mean.toFixed(2)} (SD ${sortedByMean[0].sd.toFixed(2)})`;

          if (sortedByMean.length > 1) {
            const others = sortedByMean.slice(1);
            if (others.length === 1) {
              sentence += `, while ${others[0].category} has a lower mean of ${others[0].mean.toFixed(2)} (SD ${others[0].sd.toFixed(2)})`;
            } else {
              sentence += `, while other groups have means ranging from ${Math.min(...others.map(g => g.mean)).toFixed(2)} to ${Math.max(...others.map(g => g.mean)).toFixed(2)}`;
            }
          }

          sentence += `. The overall mean ${result.rowVariableName.toLowerCase()} is ${overallMean} (SD ${overallSD})`;

          if (result.testResult?.pValue !== undefined) {
            if (result.testResult.pValue < 0.05) {
              sentence += `, and the difference is statistically significant (p=${result.testResult.pValue.toFixed(3)})`;
            } else {
              sentence += `, and the difference is not statistically significant (p=${result.testResult.pValue.toFixed(3)})`;
            }
          }

          sentences.push(sentence);
        }
      } else {
        // Handle categorical variables
        if (result.testResult?.pValue !== undefined) {
          let sentence = `The distribution of ${result.rowVariableName.toLowerCase()} is`;
          if (result.testResult.pValue >= 0.05) {
            sentence += ` relatively consistent across ${groupingColumn?.name?.toLowerCase() || 'groups'}, with no significant differences observed (p=${result.testResult.pValue.toFixed(3)})`;
          } else {
            sentence += ` significantly different across ${groupingColumn?.name?.toLowerCase() || 'groups'} (p=${result.testResult.pValue.toFixed(3)})`;
          }

          // Add information about the most common category if available
          if (result.categories && result.categories.length > 0) {
            // Find the most common category overall
            const overallDescriptives = result.descriptives.Overall;
            if (overallDescriptives?.frequencies) {
              const sortedCategories = Object.entries(overallDescriptives.frequencies)
                .sort(([,a], [,b]) => b - a);
              if (sortedCategories.length > 0) {
                const mostCommon = sortedCategories[0][0];
                sentence += `. ${mostCommon} is the most common category across all groups`;
              }
            }
          }

          sentences.push(sentence);
        }
      }
    });

    // Join sentences with proper punctuation
    if (sentences.length > 0) {
      writeUp += sentences.join('. ') + '. ';
    }

    // Add overall summary
    const significantResults = results.filter(r => r.testResult?.pValue !== undefined && r.testResult.pValue < 0.05);
    const nonSignificantResults = results.filter(r => r.testResult?.pValue !== undefined && r.testResult.pValue >= 0.05);

    writeUp += `Overall, the table highlights`;
    if (significantResults.length > 0) {
      const significantVars = significantResults.map(r => r.rowVariableName.toLowerCase()).join(', ');
      writeUp += ` significant differences in ${significantVars} across ${groupingColumn?.name?.toLowerCase() || 'groups'}`;
      if (nonSignificantResults.length > 0) {
        const nonSignificantVars = nonSignificantResults.map(r => r.rowVariableName.toLowerCase()).join(', ');
        writeUp += `. Other variables, such as ${nonSignificantVars}, show no significant differences across ${groupingColumn?.name?.toLowerCase() || 'groups'}`;
      }
    } else {
      writeUp += ` no significant differences across ${groupingColumn?.name?.toLowerCase() || 'groups'} for the variables examined`;
    }
    writeUp += `.`;

    return writeUp;
  };



  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <PublicationReadyGate>
      <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Publication Ready Table 2
      </Typography>

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Data and Variables
        </Typography>

        <Alert severity="info" sx={{ mb: 2 }}>
            This table presents descriptive statistics for selected variables, grouped by a chosen categorical variable. It also includes statistical tests to compare variables across groups (e.g., t-tests, ANOVA, Chi-Square).
        </Alert>

        <Grid container spacing={2}>
           {/* Dataset Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          {/* Grouping Variable Selection */}
           <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="grouping-variable-select-label">Grouping Variable (Column)</InputLabel>
              <Select
                labelId="grouping-variable-select-label"
                id="grouping-variable-select"
                value={groupingVariableId}
                onChange={handleGroupingVariableChange}
                label="Grouping Variable (Column)"
                disabled={categoricalColumns.length === 0 || !selectedDataset}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {categoricalColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No categorical variables available
                  </MenuItem>
                ) : (
                  categoricalColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          {/* Row Variables Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="row-variables-select-label">Row Variables</InputLabel>
              <Select
                labelId="row-variables-select-label"
                id="row-variables-select"
                multiple
                value={rowVariableIds}
                onChange={handleRowVariableChange}
                label="Row Variables"
                disabled={rowVariableColumns.length === 0 || !groupingVariableId}
                renderValue={(selected) => selected.map(id => availableColumns.find(col => col.id === id)?.name || '').join(', ')}
              >
                {rowVariableColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    Select a grouping variable first or no other variables available
                  </MenuItem>
                ) : (
                  rowVariableColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name} ({column.type})
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

           {/* Percentage Type Selection (for categorical variables) */}
           {rowVariableIds.some(id => availableColumns.find(col => col.id === id)?.type === DataType.CATEGORICAL) && (
               <Grid item xs={12}>
                   <FormControl fullWidth margin="normal">
                       <InputLabel id="percentage-type-select-label">Percentage Type (Categorical)</InputLabel>
                       <Select
                           labelId="percentage-type-select-label"
                           id="percentage-type-select"
                           value={percentageType}
                           onChange={handlePercentageTypeChange}
                           label="Percentage Type (Categorical)"
                       >
                           <MenuItem value="row">Row Percentage</MenuItem>
                           <MenuItem value="column">Column Percentage</MenuItem>
                       </Select>
                   </FormControl>
               </Grid>
           )}

        </Grid>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={runTable2Analysis}
            disabled={loading || !selectedDataset || !groupingVariableId || rowVariableIds.length === 0}
          >
            Generate Table 2
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {table2Results && !loading && selectedDataset && groupingVariableId && groupingCategories.length > 0 && (
        <Box>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Table 2. Descriptive Statistics and Group Comparisons
            </Typography>

            {/* Table Rendering */}
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow><TableCell sx={{ fontWeight: 'bold' }} rowSpan={2}>Variable</TableCell>{/* Grouping Variable Header */}<TableCell sx={{ fontWeight: 'bold' }} align="center" colSpan={groupingCategories.length + 1}>{groupingColumn?.name || 'Grouping Variable'}</TableCell>{/* p-value Column Header */}<TableCell sx={{ fontWeight: 'bold' }} align="center" rowSpan={2}>p</TableCell></TableRow>
                   <TableRow>{/* Sub-headers for each category and Total */}{groupingCategories.map((category, index) => (<TableCell key={index} sx={{ fontWeight: 'bold' }} align="center">{category} (n={totalNPerGroup[category] || 0})</TableCell>))}<TableCell sx={{ fontWeight: 'bold' }} align="center">Total (n={selectedDataset.data.length})</TableCell></TableRow>
                </TableHead>
                <TableBody>
                    {/* Total n (%) Row */}
                    <TableRow><TableCell sx={{ fontWeight: 'bold' }}>Total n (%)</TableCell>{groupingCategories.map((category, index) => (<TableCell key={index} align="center">{totalNPerGroup[category] !== undefined ? `${totalNPerGroup[category]} (${((totalNPerGroup[category] / selectedDataset.data.length) * 100).toFixed(1)}%)` : 'N/A'}</TableCell>))}<TableCell align="center">{selectedDataset.data.length} (100.0%)</TableCell><TableCell></TableCell>{/* Empty cell for p-value column */}</TableRow>

                  {table2Results.map((result, index) => (
                    <React.Fragment key={index}>
                      <TableRow><TableCell sx={{ fontWeight: 'bold' }}>{result.rowVariableName}</TableCell>{/* Data cells for each group and Total */}{result.rowDataType === DataType.NUMERIC ? (<>{groupingCategories.map((category, catIndex) => (<TableCell key={catIndex} align="center">{result.descriptives[category]?.mean !== undefined ? `${result.descriptives[category].mean.toFixed(2)} (${result.descriptives[category].standardDeviation?.toFixed(2) || 'N/A'})` : 'N/A'}</TableCell>))}<TableCell align="center">{result.descriptives.Overall?.mean !== undefined ? `${result.descriptives.Overall.mean.toFixed(2)} (${result.descriptives.Overall.standardDeviation?.toFixed(2) || 'N/A'})` : 'N/A'}</TableCell></>) : (groupingCategories.map((_, catIndex) => <TableCell key={catIndex}></TableCell>).concat(<TableCell key="total-empty"></TableCell>))}{/* p-value cell */}<TableCell align="center">{result.testResult?.pValue !== undefined ? `${result.testResult.pValue.toFixed(3)}${result.testSuperscript || ''}`: 'N/A'}</TableCell></TableRow>
                       {/* For categorical variables, list categories and their counts/percentages */}
                       {result.rowDataType === DataType.CATEGORICAL && result.categories && result.descriptives.Overall.frequencies && result.descriptives.Overall.percentages && result.categories.map((rowCategory, rowCatIndex) => (
                           <TableRow key={`${index}-${rowCatIndex}`}>
                               <TableCell sx={{ pl: 4 }}>{rowCategory}</TableCell>
                                {/* Data cells for each group and Total for this row category */}
                                 {groupingCategories.map((groupCategory, groupCatIndex) => (
                                     <TableCell key={groupCatIndex} align="center">
                                       {result.descriptives[groupCategory]?.frequencies?.[rowCategory] !== undefined ?
                                           `${result.descriptives[groupCategory].frequencies[rowCategory]} (${percentageType === 'row' ?
                                               ((result.descriptives[groupCategory].frequencies[rowCategory] / (result.descriptives.Overall?.frequencies?.[rowCategory] || 1)) * 100).toFixed(1)
                                               : result.descriptives[groupCategory].columnPercentages?.[rowCategory]?.toFixed(1) || 'N/A'}%)`
                                           : 'N/A'}
                                     </TableCell>
                                 ))}
                                  {/* Total Column Data for this row category */}
                                   <TableCell align="center">
                                       {result.descriptives.Overall?.frequencies?.[rowCategory] !== undefined ?
                                           `${result.descriptives.Overall.frequencies[rowCategory]} (${percentageType === 'row' ?
                                                ((result.descriptives.Overall.frequencies[rowCategory] / (result.descriptives.Overall?.frequencies?.[rowCategory] || 1)) * 100).toFixed(1)
                                                : result.descriptives.Overall.percentages?.[rowCategory]?.toFixed(1) || 'N/A'}%)`
                                           : 'N/A'}
                                     </TableCell>
                                {/* Empty cell for p-value column */}
                               <TableCell></TableCell>
                           </TableRow>
                       ))}
                    </React.Fragment>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

          </Paper>

           {/* Test Superscript Explanations */}
           {Object.keys(testSuperscripts).length > 0 && (
               <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
                   <Typography variant="h6" gutterBottom>
                       Statistical Tests Used
                   </Typography>
                   <Typography variant="body2" component="div">
                       {Object.entries(testSuperscripts).map(([testName, superscript]) => (
                           <div key={testName}>
                               <sup>{superscript}</sup>: {testName}
                           </div>
                       ))}
                   </Typography>
               </Paper>
           )}


           {/* Table Write-up */}
          {tableWriteUp && (
            <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Table Write-up
              </Typography>
              <Typography variant="body1" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
                {tableWriteUp}
              </Typography>
            </Paper>
          )}

          {/* Add to Results Manager Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <AddToResultsButton
              resultData={{
                title: `Table 2 - Group Comparisons (${selectedDataset.name})`,
                type: 'other' as const,
                component: 'Table2',
                data: {
                  dataset: selectedDataset.name,
                  groupingVariable: availableColumns.find(col => col.id === groupingVariableId)?.name || 'Unknown',
                  rowVariables: rowVariableIds.map((id: string) =>
                    availableColumns.find(col => col.id === id)?.name || id
                  ),
                  results: table2Results,
                  writeUp: tableWriteUp,
                  timestamp: new Date().toISOString(),
                  totalSampleSize: selectedDataset.data.length
                }
              }}
              onSuccess={() => {
                setSnackbarMessage('Results successfully added to Results Manager!');
                setSnackbarOpen(true);
              }}
              onError={(error) => {
                setSnackbarMessage(`Error adding results to Results Manager: ${error}`);
                setSnackbarOpen(true);
              }}
            />
          </Box>
        </Box>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Box>
    </PublicationReadyGate>
  );
};

export default Table2;
