import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Checkbox,
  Divider,
  Tooltip,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Alert,
  Snackbar,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Menu,
  ListItemButton,
  CircularProgress
} from '@mui/material';
import {
  Delete as DeleteIcon,
  CloudDownload as CloudDownloadIcon,
  FilterList as FilterListIcon,
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  BarChart as BarChartIcon,
  Functions as FunctionsIcon,
  Science as ScienceIcon,
  Timeline as TimelineIcon,
  ShowChart as ShowChartIcon,
  CompareArrows as CompareArrowsIcon,
  Code as CodeIcon,
  Html as HtmlIcon,
  PictureAsPdf as PictureAsPdfIcon,
  ExpandMore as ExpandMoreIcon,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  Cloud as CloudIcon,
  Settings as SettingsIcon,
  MoreVert as MoreVertIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  DragIndicator as DragIndicatorIcon
} from '@mui/icons-material';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  UniqueIdentifier
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable
} from '@dnd-kit/sortable';
import {
  CSS
} from '@dnd-kit/utilities';
import { useResults, ResultItem, Project } from '../../context/ResultsContext';
import { useAuth } from '../../context/AuthContext';
import { ProjectSelector, ProjectManagementDialog } from '../ProjectManager';

// Sortable Result Card Component
interface SortableResultCardProps {
  result: ResultItem;
  position: number;
  onToggleSelection: (id: string) => void;
  onMenuOpen: (event: React.MouseEvent<HTMLElement>, resultId: string) => void;
  onRemove?: (id: string) => void;
  getResultTypeIcon: (type: string) => React.ReactNode;
  canAccessProFeatures: boolean;
  isDragging?: boolean;
}

const SortableResultCard: React.FC<SortableResultCardProps> = ({
  result,
  position,
  onToggleSelection,
  onMenuOpen,
  onRemove,
  getResultTypeIcon,
  canAccessProFeatures,
  isDragging = false
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging
  } = useSortable({ id: result.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: transition || 'transform 200ms ease',
    opacity: isDragging || isSortableDragging ? 0.5 : 1,
    cursor: isDragging ? 'grabbing' : 'grab',
    zIndex: isDragging || isSortableDragging ? 1000 : 'auto'
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      variant="outlined"
      sx={{
        '&:hover': {
          boxShadow: 2,
          borderColor: 'primary.main',
          transform: 'translateY(-2px)'
        },
        transition: 'all 0.2s ease-in-out',
        ...(isDragging || isSortableDragging ? {
          boxShadow: 4,
          borderColor: 'primary.main',
          backgroundColor: 'action.hover'
        } : {})
      }}
    >
      <CardContent sx={{ position: 'relative' }}>
        {/* Position Number Badge */}
        {position > 0 && !isDragging && (
          <Tooltip title={`Position ${position} in current order`} placement="top">
            <Box
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                backgroundColor: 'primary.main',
                color: 'primary.contrastText',
                borderRadius: '50%',
                width: 28,
                height: 28,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                zIndex: 1,
                boxShadow: 2,
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'scale(1.1)',
                  boxShadow: 3
                }
              }}
            >
              {position}
            </Box>
          </Tooltip>
        )}

        <Box display="flex" alignItems="center" mb={1}>
          <Checkbox
            checked={result.selected || false}
            onChange={() => onToggleSelection(result.id)}
          />
          <Box
            display="flex"
            alignItems="center"
            ml={1}
            sx={{ flexGrow: 1 }}
          >
            <Tooltip title="Drag to reorder" placement="top">
              <Box
                {...attributes}
                {...listeners}
                sx={{
                  cursor: 'grab',
                  display: 'flex',
                  alignItems: 'center',
                  '&:active': {
                    cursor: 'grabbing'
                  }
                }}
              >
                <DragIndicatorIcon
                  sx={{
                    color: 'text.secondary',
                    mr: 1,
                    transition: 'color 0.2s ease',
                    '&:hover': {
                      color: 'primary.main',
                      transform: 'scale(1.1)'
                    },
                    '&:active': {
                      color: 'primary.dark'
                    }
                  }}
                />
              </Box>
            </Tooltip>
            {getResultTypeIcon(result.type)}
            <Typography variant="subtitle1" ml={1} sx={{ flexGrow: 1 }}>
              {result.title}
            </Typography>
            {canAccessProFeatures && (
              <IconButton
                size="small"
                onClick={(e) => onMenuOpen(e, result.id)}
              >
                <MoreVertIcon fontSize="small" />
              </IconButton>
            )}
          </Box>
        </Box>

        <Divider sx={{ my: 1 }} />

        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Chip
            label={result.type.charAt(0).toUpperCase() + result.type.slice(1)}
            size="small"
            color="primary"
            variant="outlined"
          />
          <Typography variant="caption" color="text.secondary">
            {formatDate(result.timestamp)}
          </Typography>
        </Box>

        <Typography variant="body2" color="text.secondary" mt={1}>
          From: {result.component}
        </Typography>
      </CardContent>
      <CardActions>
        <Button
          size="small"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={() => onRemove && onRemove(result.id)}
        >
          Remove
        </Button>
      </CardActions>
    </Card>
  );
};

// Component for managing and exporting analysis results
const ResultsManager: React.FC = () => {
  const {
    results,
    projects,
    currentProjectId,
    addResult,
    removeResult,
    clearResults,
    toggleResultSelection,
    selectAllResults,
    deselectAllResults,
    getSelectedResults,
    exportToHTML,
    getProjectResults,
    moveResultToProject,
    setCurrentProject,
    syncStatus,
    syncErrors,
    reorderResults
  } = useResults();
  const { canAccessProFeatures } = useAuth();

  // State for filtering and UI
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState<boolean>(false);
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false);
  const [exportFormat, setExportFormat] = useState<'html'>('html');
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');
  const [syncErrorSnackbarOpen, setSyncErrorSnackbarOpen] = useState<boolean>(false);
  const [syncErrorMessage, setSyncErrorMessage] = useState<string>('');

  // Project management state
  const [projectManagementOpen, setProjectManagementOpen] = useState<boolean>(false);
  const [expandedProjects, setExpandedProjects] = useState<Set<string>>(new Set(['default']));
  const [viewMode, setViewMode] = useState<'flat' | 'projects'>('projects');
  const [resultMenuAnchor, setResultMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedResultForMove, setSelectedResultForMove] = useState<string | null>(null);

  // Drag and drop state
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const [draggedResult, setDraggedResult] = useState<ResultItem | null>(null);
  const [isReordering, setIsReordering] = useState<boolean>(false);

  // Configure drag sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Monitor sync errors and show notifications
  useEffect(() => {
    const errorProjects = Object.entries(syncErrors).filter(([_, error]) => error);
    if (errorProjects.length > 0) {
      const [projectId, error] = errorProjects[0];
      const project = projects.find(p => p.id === projectId);
      const projectName = project?.name || 'Unknown Project';
      setSyncErrorMessage(`Failed to sync "${projectName}": ${error}`);
      setSyncErrorSnackbarOpen(true);
    }
  }, [syncErrors, projects]);

  // Handle date filter change
  const handleDateFilterChange = (event: SelectChangeEvent<string>) => {
    setDateFilter(event.target.value);
  };

  // Filter results based on selected date filter
  const filteredResults = results.filter(result => {
    if (dateFilter === 'all') return true;

    const resultDate = new Date(result.timestamp);
    const now = new Date();
    const daysDiff = Math.floor((now.getTime() - resultDate.getTime()) / (1000 * 60 * 60 * 24));

    switch (dateFilter) {
      case 'today':
        return daysDiff === 0;
      case 'week':
        return daysDiff <= 7;
      case 'month':
        return daysDiff <= 30;
      case 'older':
        return daysDiff > 30;
      default:
        return true;
    }
  });



  // Project management helpers
  const toggleProjectExpansion = (projectId: string) => {
    const newExpanded = new Set(expandedProjects);
    if (newExpanded.has(projectId)) {
      newExpanded.delete(projectId);
    } else {
      newExpanded.add(projectId);
    }
    setExpandedProjects(newExpanded);
  };

  const handleResultMenuOpen = (event: React.MouseEvent<HTMLElement>, resultId: string) => {
    setResultMenuAnchor(event.currentTarget);
    setSelectedResultForMove(resultId);
  };

  const handleResultMenuClose = () => {
    setResultMenuAnchor(null);
    setSelectedResultForMove(null);
  };

  const handleMoveResult = (targetProjectId: string | null) => {
    if (selectedResultForMove) {
      moveResultToProject(selectedResultForMove, targetProjectId);
      setSnackbarMessage('Result moved successfully');
      setSnackbarOpen(true);
    }
    handleResultMenuClose();
  };

  const getProjectIcon = (project: Project, isExpanded: boolean) => {
    if (isExpanded) {
      return <FolderOpenIcon />;
    }
    if (!project.isLocal) {
      return <CloudIcon color="primary" />;
    }
    return <FolderIcon />;
  };

  // Format timestamp
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Get icon for result type
  const getResultTypeIcon = (type: string) => {
    switch (type) {
      case 'descriptive':
        return <BarChartIcon />;
      case 'normality':
        return <ShowChartIcon />;
      case 'ttest':
        return <CompareArrowsIcon />;
      case 'anova':
        return <FunctionsIcon />;
      case 'correlation':
        return <TimelineIcon />;
      case 'regression':
        return <ShowChartIcon />;
      case 'nonparametric':
        return <ScienceIcon />;
      default:
        return <BarChartIcon />;
    }
  };

  // Drag and drop handlers
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id);
    setIsReordering(true);

    // Find the dragged result
    const result = results.find(r => r.id === active.id);
    setDraggedResult(result || null);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      // For project-organized view, we need to handle reordering within projects
      if (canAccessProFeatures && viewMode === 'projects') {
        // Find which project this result belongs to
        const activeResult = results.find(r => r.id === active.id);
        const overResult = results.find(r => r.id === over?.id);

        if (activeResult && overResult && activeResult.projectId === overResult.projectId) {
          // Reorder within the same project
          const projectResults = getProjectResults(activeResult.projectId || 'default');
          const oldIndex = projectResults.findIndex(r => r.id === active.id);
          const newIndex = projectResults.findIndex(r => r.id === over?.id);

          if (oldIndex !== -1 && newIndex !== -1) {
            const reorderedProjectResults = arrayMove(projectResults, oldIndex, newIndex);

            // Update the global results array to maintain the new order
            const otherResults = results.filter(r => r.projectId !== activeResult.projectId);
            const newResults = [...otherResults, ...reorderedProjectResults];

            reorderResults(newResults);
          }
        }
      } else {
        // For flat view, reorder all results
        const oldIndex = results.findIndex(r => r.id === active.id);
        const newIndex = results.findIndex(r => r.id === over?.id);

        if (oldIndex !== -1 && newIndex !== -1) {
          const reorderedResults = arrayMove(results, oldIndex, newIndex);
          reorderResults(reorderedResults);
        }
      }
    }

    setActiveId(null);
    setDraggedResult(null);
    setIsReordering(false);
  };

  // Handle result removal with snackbar feedback
  const handleRemoveResult = (resultId: string) => {
    removeResult(resultId);
    setSnackbarMessage('Result removed');
    setSnackbarOpen(true);
  };

  // Add test results for debugging drag functionality
  const addTestResults = () => {
    const testResults = [
      {
        title: 'Descriptive Statistics Test',
        type: 'descriptive' as const,
        component: 'Descriptive Statistics',
        data: { test: 'data' }
      },
      {
        title: 'T-Test Analysis Test',
        type: 'ttest' as const,
        component: 'T-Test',
        data: { test: 'data' }
      },
      {
        title: 'ANOVA Results Test',
        type: 'anova' as const,
        component: 'ANOVA',
        data: { test: 'data' }
      }
    ];

    testResults.forEach(result => {
      addResult(result);
    });

    setSnackbarMessage('Test results added');
    setSnackbarOpen(true);
  };

  // Get sync status icon for cloud projects
  const getSyncStatusIcon = (projectId: string) => {
    const status = syncStatus[projectId];
    const error = syncErrors[projectId];

    if (error) {
      return (
        <Tooltip title={`Sync Error: ${error}`}>
          <ErrorIcon fontSize="small" color="error" />
        </Tooltip>
      );
    }

    switch (status) {
      case 'syncing':
        return (
          <Tooltip title="Syncing...">
            <CircularProgress size={16} />
          </Tooltip>
        );
      case 'success':
        return (
          <Tooltip title="Synced successfully">
            <CheckCircleIcon fontSize="small" color="success" />
          </Tooltip>
        );
      case 'error':
        return (
          <Tooltip title="Sync failed">
            <ErrorIcon fontSize="small" color="error" />
          </Tooltip>
        );
      default:
        return null;
    }
  };

  // Handle export button click
  const handleExportClick = () => {
    setExportDialogOpen(true);
  };

  // Handle export confirmation
  const handleExportConfirm = async () => {
    const selectedResults = getSelectedResults();
    if (selectedResults.length === 0) {
      setSnackbarMessage('Please select at least one result to export');
      setSnackbarOpen(true);
      return;
    }

    try {
      exportToHTML();
      setSnackbarMessage('Results successfully exported to HTML format');
    } catch (error) {
      console.error('Export error:', error);
      setSnackbarMessage(`Error exporting results: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setSnackbarOpen(true);
      setExportDialogOpen(false);
    }
  };

  // Handle delete selected results
  const handleDeleteSelected = () => {
    const selectedResults = getSelectedResults();
    if (selectedResults.length === 0) {
      setSnackbarMessage('No results selected');
      setSnackbarOpen(true);
      return;
    }
    setConfirmDeleteOpen(true);
  };

  // Handle delete selected confirmation
  const handleDeleteSelectedConfirm = () => {
    const selectedResults = getSelectedResults();
    selectedResults.forEach(result => removeResult(result.id));
    setConfirmDeleteOpen(false);
    setSnackbarMessage(`${selectedResults.length} result(s) deleted`);
    setSnackbarOpen(true);
  };

  // Handle export selected results
  const handleExportSelected = () => {
    const selectedResults = getSelectedResults();
    if (selectedResults.length === 0) {
      setSnackbarMessage('No results selected');
      setSnackbarOpen(true);
      return;
    }
    setExportDialogOpen(true);
  };

  return (
    <Box p={3}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">
          Results Manager
        </Typography>
        <Box display="flex" gap={2}>
          {process.env.NODE_ENV === 'development' && (
            <Button
              onClick={addTestResults}
              variant="outlined"
              color="secondary"
              size="small"
            >
              Add Test Results
            </Button>
          )}
          {canAccessProFeatures && (
            <Button
              startIcon={<SettingsIcon />}
              onClick={() => setProjectManagementOpen(true)}
              variant="outlined"
            >
              Manage Projects
            </Button>
          )}
        </Box>
      </Box>

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          {canAccessProFeatures && (
            <Grid item xs={12} sm={6} md={4}>
              <ProjectSelector
                onCreateProject={() => setProjectManagementOpen(true)}
                showCreateButton={false}
              />
            </Grid>
          )}

          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <InputLabel id="date-filter-select-label">Filter by Date</InputLabel>
              <Select
                labelId="date-filter-select-label"
                id="date-filter-select"
                value={dateFilter}
                label="Filter by Date"
                onChange={handleDateFilterChange}
              >
                <MenuItem value="all">All Results</MenuItem>
                <MenuItem value="today">Today</MenuItem>
                <MenuItem value="week">Past Week</MenuItem>
                <MenuItem value="month">Past Month</MenuItem>
                <MenuItem value="older">Older than Month</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Box
              display="flex"
              justifyContent="flex-end"
              gap={1}
              flexWrap="wrap"
              sx={{
                '& > *': {
                  minWidth: { xs: '120px', sm: 'auto' }
                }
              }}
            >
              <Button
                variant="outlined"
                startIcon={<CheckBoxIcon />}
                onClick={selectAllResults}
                disabled={results.length === 0}
                size="small"
              >
                Select All
              </Button>
              <Button
                variant="outlined"
                startIcon={<CheckBoxOutlineBlankIcon />}
                onClick={deselectAllResults}
                disabled={results.length === 0}
                size="small"
              >
                Deselect All
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<CloudDownloadIcon />}
                onClick={handleExportSelected}
                disabled={getSelectedResults().length === 0}
                size="small"
              >
                Export Selected
              </Button>
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteIcon />}
                onClick={handleDeleteSelected}
                disabled={getSelectedResults().length === 0}
                size="small"
              >
                Delete Selected
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {results.length === 0 ? (
        <Alert severity="info" sx={{ mt: 2 }}>
          No analysis results found. Run some analyses to collect results.
        </Alert>
      ) : filteredResults.length === 0 ? (
        <Alert severity="info" sx={{ mt: 2 }}>
          No results match the selected filter.
        </Alert>
      ) : canAccessProFeatures ? (
        // Project-organized view for Pro users
        <Box>
          {projects.map((project) => {
            const projectResults = getProjectResults(project.id).filter(result => {
              if (dateFilter === 'all') return true;

              const resultDate = new Date(result.timestamp);
              const now = new Date();
              const daysDiff = Math.floor((now.getTime() - resultDate.getTime()) / (1000 * 60 * 60 * 24));

              switch (dateFilter) {
                case 'today':
                  return daysDiff === 0;
                case 'week':
                  return daysDiff <= 7;
                case 'month':
                  return daysDiff <= 30;
                case 'older':
                  return daysDiff > 30;
                default:
                  return true;
              }
            });

            if (projectResults.length === 0) return null;

            const isExpanded = expandedProjects.has(project.id);

            return (
              <Accordion
                key={project.id}
                expanded={isExpanded}
                onChange={() => toggleProjectExpansion(project.id)}
                sx={{ mb: 1 }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1} width="100%">
                    {getProjectIcon(project, isExpanded)}
                    <Typography variant="h6" sx={{ flexGrow: 1 }}>
                      {project.name}
                    </Typography>
                    <Chip
                      label={projectResults.length}
                      size="small"
                      variant="outlined"
                      color={project.isLocal ? 'default' : 'primary'}
                    />
                    {!project.isLocal && (
                      <>
                        <Tooltip title="Cloud Project">
                          <CloudIcon fontSize="small" color="primary" />
                        </Tooltip>
                        {getSyncStatusIcon(project.id)}
                      </>
                    )}
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragStart={handleDragStart}
                    onDragEnd={handleDragEnd}
                  >
                    <SortableContext
                      items={projectResults.map(r => r.id)}
                      strategy={verticalListSortingStrategy}
                    >
                      <Grid container spacing={2}>
                        {projectResults.map((result, index) => (
                          <Grid item xs={12} sm={6} md={4} key={result.id}>
                            <SortableResultCard
                              result={result}
                              position={index + 1}
                              onToggleSelection={toggleResultSelection}
                              onMenuOpen={handleResultMenuOpen}
                              onRemove={handleRemoveResult}
                              getResultTypeIcon={getResultTypeIcon}
                              canAccessProFeatures={canAccessProFeatures}
                              isDragging={activeId === result.id}
                            />
                          </Grid>
                        ))}
                      </Grid>
                    </SortableContext>
                    <DragOverlay>
                      {draggedResult ? (
                        <SortableResultCard
                          result={draggedResult}
                          position={0} // Placeholder for drag preview
                          onToggleSelection={toggleResultSelection}
                          onMenuOpen={handleResultMenuOpen}
                          onRemove={handleRemoveResult}
                          getResultTypeIcon={getResultTypeIcon}
                          canAccessProFeatures={canAccessProFeatures}
                          isDragging={true}
                        />
                      ) : null}
                    </DragOverlay>
                  </DndContext>
                </AccordionDetails>
              </Accordion>
            );
          })}
        </Box>
      ) : (
        // Flat view for non-Pro users (backward compatibility)
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={filteredResults.map(r => r.id)}
            strategy={verticalListSortingStrategy}
          >
            <Grid container spacing={2}>
              {filteredResults.map((result, index) => (
                <Grid item xs={12} sm={6} md={4} key={result.id}>
                  <SortableResultCard
                    result={result}
                    position={index + 1}
                    onToggleSelection={toggleResultSelection}
                    onMenuOpen={() => {}} // No menu for non-Pro users
                    onRemove={handleRemoveResult}
                    getResultTypeIcon={getResultTypeIcon}
                    canAccessProFeatures={false}
                    isDragging={activeId === result.id}
                  />
                </Grid>
              ))}
            </Grid>
          </SortableContext>
          <DragOverlay>
            {draggedResult ? (
              <SortableResultCard
                result={draggedResult}
                position={0} // Placeholder for drag preview
                onToggleSelection={toggleResultSelection}
                onMenuOpen={() => {}}
                onRemove={handleRemoveResult}
                getResultTypeIcon={getResultTypeIcon}
                canAccessProFeatures={false}
                isDragging={true}
              />
            ) : null}
          </DragOverlay>
        </DndContext>
      )}

      {/* Export Dialog */}
      <Dialog open={exportDialogOpen} onClose={() => setExportDialogOpen(false)}>
        <DialogTitle>Export Results</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Select export format:
          </Typography>
          <Box display="flex" justifyContent="center" mt={2}>
            <Button
              variant="contained"
              startIcon={<HtmlIcon />}
              disabled
              fullWidth
            >
              HTML Export
            </Button>
          </Box>
          <Typography variant="body2" color="text.secondary" mt={2}>
            {getSelectedResults().length} result(s) selected for export
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExportDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleExportConfirm} variant="contained" color="primary">
            Export
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirm Delete Selected Dialog */}
      <Dialog open={confirmDeleteOpen} onClose={() => setConfirmDeleteOpen(false)}>
        <DialogTitle>Delete Selected Results</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to delete {getSelectedResults().length} selected result(s)? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDeleteOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteSelectedConfirm} color="error" variant="contained">
            Delete Selected
          </Button>
        </DialogActions>
      </Dialog>

      {/* Project Management Dialog */}
      <ProjectManagementDialog
        open={projectManagementOpen}
        onClose={() => setProjectManagementOpen(false)}
      />

      {/* Result Move Menu */}
      <Menu
        anchorEl={resultMenuAnchor}
        open={Boolean(resultMenuAnchor)}
        onClose={handleResultMenuClose}
      >
        <MenuItem onClick={() => handleMoveResult('default')}>
          <ListItemIcon>
            <FolderIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Default Project" />
        </MenuItem>
        {projects
          .filter(p => p.id !== 'default')
          .map((project) => (
            <MenuItem key={project.id} onClick={() => handleMoveResult(project.id)}>
              <ListItemIcon>
                {project.isLocal ? (
                  <FolderIcon fontSize="small" />
                ) : (
                  <CloudIcon fontSize="small" color="primary" />
                )}
              </ListItemIcon>
              <ListItemText primary={project.name} />
            </MenuItem>
          ))}
      </Menu>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />

      {/* Sync Error Snackbar */}
      <Snackbar
        open={syncErrorSnackbarOpen}
        autoHideDuration={8000}
        onClose={() => setSyncErrorSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSyncErrorSnackbarOpen(false)}
          severity="error"
          sx={{ width: '100%' }}
        >
          {syncErrorMessage}
        </Alert>
      </Snackbar>

      {/* Reordering Indicator */}
      <Snackbar
        open={isReordering}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          severity="info"
          sx={{ width: '100%' }}
          icon={<DragIndicatorIcon />}
        >
          Reordering results... Drop to place in new position
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ResultsManager;