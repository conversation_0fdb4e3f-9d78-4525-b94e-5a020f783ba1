import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Slider,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Button,
  Divider,
  useTheme,
  alpha,
  Card,
  CardContent,
  Tooltip,
  IconButton,
  SelectChangeEvent,
  ToggleButtonGroup,
  ToggleButton,
} from '@mui/material';
import {
  Help as HelpIcon,
  ContentCopy as ContentCopyIcon,
  PictureAsPdf as PictureAsPdfIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import { getZScore } from '../../utils/stats/sampleSize';

const OneSampleCalculator: React.FC = () => {
  const theme = useTheme();

  // State for calculator type within One-Sample Scenarios
  const [calculatorType, setCalculatorType] = useState<string>('proportion');
  
  // State for calculator parameters (One Sample)
  const [confidenceLevel, setConfidenceLevel] = useState<number>(0.95); // 95% confidence level
  const [marginOfError, setMarginOfError] = useState<number>(0.05); 
  const [expectedProportion, setExpectedProportion] = useState<number>(0.5); // 50% expected proportion
  const [standardDeviation, setStandardDeviation] = useState<number>(10); // Default standard deviation for mean calculation
  const [populationSize, setPopulationSize] = useState<number | null>(null); // Optional population size
  
  // State for results
  const [requiredSampleSize, setRequiredSampleSize] = useState<number | null>(null);
  const [powerCurveData, setPowerCurveData] = useState<any[]>([]);
  
  // Calculate sample size when parameters change
  useEffect(() => {
    calculateSampleSize();
    generatePowerCurveData();
  }, [confidenceLevel, marginOfError, expectedProportion, standardDeviation, calculatorType, populationSize]);
  
  // Function to calculate required sample size based on parameters
  const calculateSampleSize = () => {
    const zScore = getZScore(1 - (1 - confidenceLevel) / 2);
    
    let n_infinity: number; // Sample size for infinite population
    
    if (calculatorType === 'proportion') {
      // Formula for proportion (infinite population): n = (z²pq)/e²
      const p = expectedProportion;
      const q = 1 - p;
      n_infinity = (Math.pow(zScore, 2) * p * q) / Math.pow(marginOfError, 2);
    } else {
      // Formula for mean (infinite population): n = (z²σ²)/e²
      n_infinity = (Math.pow(zScore, 2) * Math.pow(standardDeviation, 2)) / Math.pow(marginOfError, 2);
    }

    let sampleSize: number;
    // Apply Finite Population Correction (FPC) if population size is provided and valid
    if (populationSize && populationSize > 0) {
      if (calculatorType === 'proportion') {
        // FPC for proportion: n_adjusted = n_infinity / (1 + (n_infinity - 1) / N)
        sampleSize = Math.ceil(n_infinity / (1 + (n_infinity - 1) / populationSize));
      } else {
        // FPC for mean: n_adjusted = n_infinity / (1 + n_infinity / populationSize)
        sampleSize = Math.ceil(n_infinity / (1 + n_infinity / populationSize));
      }
    } else {
      sampleSize = Math.ceil(n_infinity);
    }
    
    setRequiredSampleSize(sampleSize);
  };
  
  // Generate data for power curve visualization
  const generatePowerCurveData = () => {
    const data = [];
    const minSampleSize = Math.max(10, Math.floor(requiredSampleSize ? requiredSampleSize * 0.5 : 20));
    const maxSampleSize = Math.ceil(requiredSampleSize ? requiredSampleSize * 1.5 : 100);
    // Adjust step to ensure fewer, more readable ticks
    const numberOfTicks = 7; // Aim for around 7 ticks
    const step = Math.max(1, Math.floor((maxSampleSize - minSampleSize) / (numberOfTicks - 1)));
    
    for (let n = minSampleSize; n <= maxSampleSize; n += step) {
      // Calculate margin of error for this sample size
      const zScore = getZScore(1 - (1 - confidenceLevel) / 2);
      let marginOfErrorForN: number;
      
      if (calculatorType === 'proportion') {
        const p = expectedProportion;
        const q = 1 - p;
        // For FPC in power curve, we need n_infinity to calculate adjusted margin of error
        const n_infinity_for_n = (Math.pow(zScore, 2) * p * q) / Math.pow(marginOfError, 2);
        if (populationSize && populationSize > 0) {
          // Recalculate margin of error for adjusted sample size 'n'
          // E = Z * sqrt(p*q/n) * sqrt((N-n)/(N-1))
          marginOfErrorForN = zScore * Math.sqrt((p * q) / n) * Math.sqrt((populationSize - n) / (populationSize - 1));
        } else {
          marginOfErrorForN = zScore * Math.sqrt((p * q) / n);
        }
      } else {
        // E = Z * sigma / sqrt(n) * sqrt((N-n)/(N-1))
        if (populationSize && populationSize > 0) {
          marginOfErrorForN = zScore * (standardDeviation / Math.sqrt(n)) * Math.sqrt((populationSize - n) / (populationSize - 1));
        } else {
          marginOfErrorForN = zScore * (standardDeviation / Math.sqrt(n));
        }
      }
      
      data.push({
        sampleSize: n,
        marginOfError: marginOfErrorForN,
      });
    }
    
    setPowerCurveData(data);
  };
  
  // Handle copying results to clipboard
  const handleCopyResults = () => {
    if (requiredSampleSize) {
      let resultText = '';
      const populationText = populationSize && populationSize > 0 ? ` from a population of ${populationSize}` : '';

      if (calculatorType === 'proportion') {
        resultText = `Required Sample Size: ${requiredSampleSize} for estimating a population proportion of ${Math.round(expectedProportion * 100)}% with a margin of error of ±${Math.round(marginOfError * 100)}% at a ${Math.round(confidenceLevel * 100)}% confidence level${populationText}.`;
      } else {
        resultText = `Required Sample Size: ${requiredSampleSize} for estimating a population mean with a standard deviation of ${standardDeviation} and a margin of error of ±${marginOfError} at a ${Math.round(confidenceLevel * 100)}% confidence level${populationText}.`;
      }
      navigator.clipboard.writeText(resultText);
      // Could add a notification here
    }
  };
  
  // Handle exporting results as PDF
  const handleExportPDF = () => {
    // For now, just a placeholder
    console.log('Export PDF functionality would go here');
  };
  
  // Handle reset
  const handleReset = () => {
    setConfidenceLevel(0.95);
    setMarginOfError(0.05);
    setExpectedProportion(0.5);
    setStandardDeviation(10);
    setPopulationSize(null);
  };
  
  // Handle calculator type change
  const handleCalculatorTypeChange = (_event: React.MouseEvent<HTMLElement>, newValue: string) => {
    if (newValue !== null) {
      setCalculatorType(newValue);
      // Set default values based on calculator type
      if (newValue === 'proportion') {
        setConfidenceLevel(0.95);
        setMarginOfError(0.05); // 5%
        setExpectedProportion(0.5);
      } else if (newValue === 'mean') {
        setConfidenceLevel(0.95);
        setMarginOfError(1.5); // Absolute value
        setStandardDeviation(10);
      }
    }
  };

  return (
    <>
      {/* Calculator Type Selector - Changed from dropdown to toggle button group */}
      <Box sx={{ mb: 3 }}>
        <ToggleButtonGroup
          value={calculatorType}
          exclusive
          onChange={handleCalculatorTypeChange}
          aria-label="calculator type"
          fullWidth
        >
          <ToggleButton
            value="proportion"
            aria-label="based on proportion"
            sx={{
              '&.Mui-selected': {
                bgcolor: theme.palette.primary.main,
                color: theme.palette.primary.contrastText,
                '&:hover': {
                  bgcolor: theme.palette.primary.dark,
                },
              },
            }}
          >
            Based on Proportion
          </ToggleButton>
          <ToggleButton
            value="mean"
            aria-label="based on mean"
            sx={{
              '&.Mui-selected': {
                bgcolor: theme.palette.secondary.main,
                color: theme.palette.secondary.contrastText,
                 '&:hover': {
                  bgcolor: theme.palette.secondary.dark,
                },
              },
            }}
          >
            Based on Mean
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      <Grid container spacing={3}>
        {/* Input Parameters */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Input Parameters</Typography>
            
            {/* Confidence Level */}
            <Box sx={{ mb: 4 }}>
              <Typography gutterBottom>Confidence Level</Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12}>
                  <Slider
                    value={confidenceLevel * 100}
                    onChange={(_, newValue) => setConfidenceLevel((newValue as number) / 100)}
                    step={1}
                    min={80}
                    max={99}
                    marks={[
                      { value: 80, label: '80%' },
                      { value: 90, label: '90%' },
                      { value: 95, label: '95%' },
                      { value: 99, label: '99%' },
                    ]}
                  />
                </Grid>
              </Grid>
            </Box>
            
            {/* Margin of Error for Proportion */}
            {calculatorType === 'proportion' && (
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>Margin of Error</Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12}>
                    <Slider
                      value={marginOfError * 100}
                      onChange={(_, newValue) => setMarginOfError((newValue as number) / 100)}
                      step={0.5}
                      min={1}
                      max={10}
                      marks={[
                        { value: 1, label: '1%' },
                        { value: 5, label: '5%' },
                        { value: 10, label: '10%' },
                      ]}
                    />
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Margin of Error for Mean (Text Field Only) */}
            {calculatorType === 'mean' && (
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>
                  Margin of Error (Absolute)
                  <Tooltip title="The maximum difference between the sample mean and the true population mean that you're willing to accept.">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <HelpIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
                <TextField
                  type="number"
                  value={marginOfError}
                  onChange={(e) => setMarginOfError(Number(e.target.value))}
                  inputProps={{
                    min: 0.1,
                    step: 0.1,
                  }}
                  fullWidth
                />
              </Box>
            )}
            
            
            {calculatorType === 'proportion' ? (
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>
                  Expected Proportion
                  <Tooltip title="Your best guess of the proportion in the population. If unsure, use 50% for the most conservative estimate.">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <HelpIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs>
                    <TextField
                      value={Math.round(expectedProportion * 100)}
                      onChange={(e) => setExpectedProportion(Number(e.target.value) / 100)}
                      type="number"
                      inputProps={{
                        min: 1,
                        max: 99,
                        step: 1,
                      }}
                      fullWidth
                    />
                  </Grid>
                  <Grid item>
                    <Typography>%</Typography>
                  </Grid>
                </Grid>
              </Box>
            ) : (
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>
                  Standard Deviation
                  <Tooltip title="Your best estimate of the population standard deviation. This is a measure of how spread out the values are.">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <HelpIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
                <TextField
                  type="number"
                  value={standardDeviation}
                  onChange={(e) => setStandardDeviation(Number(e.target.value))}
                  inputProps={{
                    min: 0.1,
                    step: 0.1,
                  }}
                  fullWidth
                />
              </Box>
            )}

            {/* Population Size (N) */}
            <Box sx={{ mb: 4 }}>
              <Typography gutterBottom>
                Population Size (N)
                <Tooltip title="The total number of individuals or items in the population. Leave blank for an infinite population assumption.">
                  <IconButton size="small" sx={{ ml: 1 }}>
                    <HelpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
              <TextField
                type="number"
                value={populationSize === null ? '' : populationSize}
                onChange={(e) => setPopulationSize(e.target.value === '' ? null : Number(e.target.value))}
                inputProps={{
                  min: 1,
                  step: 1,
                }}
                fullWidth
                placeholder="Optional"
              />
            </Box>
            

            
            {/* Reset Button */}
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleReset}
              fullWidth
              sx={{ mt: 2 }}
            >
              Reset
            </Button>
          </Paper>
        </Grid>
        
        {/* Results */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Results</Typography>
              <Box>
                <Tooltip title="Copy results">
                  <IconButton onClick={handleCopyResults}>
                    <ContentCopyIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Export as PDF">
                  <IconButton onClick={handleExportPDF}>
                    <PictureAsPdfIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            
            {/* Sample Size Result */}
            <Card sx={{ mb: 4, bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>Required Sample Size</Typography>
                <Typography variant="h2" color="primary">
                  {requiredSampleSize}
                </Typography>
              </CardContent>
            </Card>
            
            {/* Margin of Error Visualization for Proportion */} 
            {calculatorType === 'proportion' && (
              <>
                <Typography variant="h6" gutterBottom>Margin of Error by Sample Size</Typography>
                <Box sx={{ height: 300, mb: 2 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={powerCurveData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="sampleSize" 
                        label={{ value: 'Sample Size', position: 'outerBottom', offset: 25, style: { fontSize: '12px' } }}
                        tickCount={7} 
                        interval="preserveStartEnd"
                        height={70}
                      />
                      <YAxis 
                        label={{ value: 'Margin of Error', angle: -90, position: 'outside', offset: -40, style: { fontSize: '12px' } }}
                        tickFormatter={(value) => `${Math.round(value * 100)}%`}
                        width={100}
                      />
                      <RechartsTooltip 
                        formatter={(value: number) => [
                          `${(value * 100).toFixed(2)}%`,
                          'Margin of Error'
                        ]}
                        labelFormatter={(value) => `Sample Size: ${value}`}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="marginOfError" 
                        stroke={theme.palette.primary.main} 
                        activeDot={{ r: 8 }} 
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </>
            )}

            {/* Margin of Error Visualization for Mean */} 
            {calculatorType === 'mean' && (
              <>
                <Typography variant="h6" gutterBottom>Margin of Error (Absolute) by Sample Size</Typography>
                <Box sx={{ height: 300, mb: 2 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={powerCurveData} // Reuses the same data source
                      margin={{ top: 5, right: 30, left: 20, bottom: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="sampleSize" 
                        label={{ value: 'Sample Size', position: 'outerBottom', offset: 15, style: { fontSize: '12px' } }}
                        tickCount={7} 
                        interval="preserveStartEnd"
                        height={70}
                      />
                      <YAxis 
                        label={{ value: 'Margin of Error (Absolute)', angle: -90, position: 'outside', offset: -60, style: { fontSize: '12px' } }}
                        tickFormatter={(value) => `${value.toFixed(2)}`}
                        width={100}
                      />
                      <RechartsTooltip 
                        formatter={(value: number) => [
                          `${value.toFixed(2)}`,
                          'Margin of Error (Absolute)'
                        ]}
                        labelFormatter={(value) => `Sample Size: ${value}`}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="marginOfError" 
                        stroke={theme.palette.primary.main} 
                        activeDot={{ r: 8 }} 
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </>
            )}
            
            {/* Interpretation */}
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>Interpretation</Typography>
              <Typography variant="body1">
                A sample size of {requiredSampleSize} is needed to estimate a population {calculatorType === 'proportion' ? 'proportion' : 'mean'}
                {calculatorType === 'proportion' ? ` of ${Math.round(expectedProportion * 100)}%` : ` with a standard deviation of ${standardDeviation}`}
                {' with a margin of error of '}
                {calculatorType === 'proportion' ? `±${Math.round(marginOfError * 100)}%` : `±${marginOfError}`}
                {' at a '}
                {Math.round(confidenceLevel * 100)}% confidence level
                {populationSize && populationSize > 0 ? ` from a population of ${populationSize}.` : '.'}
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </>
  );
};

export default OneSampleCalculator;
