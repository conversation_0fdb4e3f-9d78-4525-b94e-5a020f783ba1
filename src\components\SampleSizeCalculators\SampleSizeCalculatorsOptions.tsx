import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  LooksOne as OneSampleIcon,
  LooksTwo as TwoSampleIcon,
  CompareArrows as PairedSampleIcon,
  MoreHoriz as MoreThanTwoGroupsIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';

interface SampleSizeCalculatorOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Means' | 'Proportions' | 'Other'; // Define categories relevant to sample size
  color: string;
}

interface SampleSizeCalculatorsOptionsProps {
  onNavigate: (path: string) => void;
}

export const sampleSizeCalculatorOptions: SampleSizeCalculatorOption[] = [
  {
    name: 'One Sample Calculator',
    shortDescription: 'Calculate sample size for a single group',
    detailedDescription: 'Determine the required sample size for studies involving a single population mean or proportion.',
    path: 'samplesize/one-sample',
    icon: <OneSampleIcon />,
    category: 'Means', // Assuming it can be used for means or proportions
    color: '#2196F3', // Blue
  },
  {
    name: 'Two Sample Calculator',
    shortDescription: 'Calculate sample size for comparing two groups',
    detailedDescription: 'Determine the required sample size for studies comparing the means or proportions of two independent groups.',
    path: 'samplesize/two-sample',
    icon: <TwoSampleIcon />,
    category: 'Means', // Assuming it can be used for means or proportions
    color: '#4CAF50', // Green
  },
  {
    name: 'Paired Sample Calculator',
    shortDescription: 'Calculate sample size for paired or matched data',
    detailedDescription: 'Determine the required sample size for studies involving paired observations or matched subjects, such as pre-post designs.',
    path: 'samplesize/paired-sample',
    icon: <PairedSampleIcon />,
    category: 'Means', // Assuming it can be used for means or proportions
    color: '#FF9800', // Orange
  },
  {
    name: 'More Than Two Groups Calculator',
    shortDescription: 'Calculate sample size for comparing multiple groups (ANOVA)',
    detailedDescription: 'Determine the required sample size for studies comparing the means of more than two independent groups, typically used for ANOVA.',
    path: 'samplesize/more-than-two-groups',
    icon: <MoreThanTwoGroupsIcon />,
    category: 'Means', // Specifically for means (ANOVA)
    color: '#9C27B0', // Purple
  },
];

const SampleSizeCalculatorsOptions: React.FC<SampleSizeCalculatorsOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  // Sample size calculators might not need categories like Advanced Analysis,
  // but keeping the structure for consistency. Can simplify if needed.
  const [selectedCategory, setSelectedCategory] = React.useState<string>('All');

  // Define categories based on the options above or simplify
  const categories = ['All', 'Means', 'Proportions', 'Other']; // Example categories

  const filteredOptions = selectedCategory === 'All'
    ? sampleSizeCalculatorOptions
    : sampleSizeCalculatorOptions.filter(option => option.category === selectedCategory);

  // Placeholder for category icons if needed, or remove category filtering
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Means': return <CalculateIcon />; // Example icon
      case 'Proportions': return <CalculateIcon />; // Example icon
      case 'Other': return <CalculateIcon />; // Example icon
      default: return <CalculateIcon />;
    }
  };


  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Sample Size Calculators
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Determine the necessary sample size for your study
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Use these calculators to estimate the minimum number of participants or observations
          required to detect a statistically significant effect with a given level of power.
        </Typography>
      </Paper>

      {/* Category Filter (Optional - can remove if categories aren't useful here) */}
      {/*
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>
      */}

      {/* Options Grid */}
      <Grid container spacing={3}>
        {sampleSizeCalculatorOptions.map((option) => ( // Using sampleSizeCalculatorOptions directly if no filtering
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography variant="h6" fontWeight="bold">
                    {option.name}
                  </Typography>
                }
                subheader={
                   <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    {/* Optional: Display category chip if categories are used */}
                    {/*
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                    */}
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {option.shortDescription}
                </Typography>

                <Typography variant="body2" paragraph>
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section (Optional - can remove or adapt) */}
      {/*
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Single group?</strong> Use the One Sample Calculator
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Comparing two independent groups?</strong> Use the Two Sample Calculator
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Paired or matched data?</strong> Use the Paired Sample Calculator
            </Typography>
             <Typography variant="body2" color="text.secondary">
              • <strong>Comparing more than two groups?</strong> Use the More Than Two Groups Calculator
            </Typography>
          </Box>
        </Box>
      </Paper>
      */}
    </Container>
  );
};

export default SampleSizeCalculatorsOptions;
