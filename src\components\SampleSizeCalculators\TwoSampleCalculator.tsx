import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  <PERSON>lider,
  TextField,
  Button,
  useTheme,
  alpha,
  Card,
  CardContent,
  Tooltip,
  IconButton,
  ToggleButtonGroup,
  ToggleButton,
} from '@mui/material';
import {
  Help as HelpIcon,
  ContentCopy as ContentCopyIcon,
  PictureAsPdf as PictureAsPdfIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import jStat from 'jstat';
import { getZScore } from '../../utils/stats/sampleSize';

const TwoSampleCalculator: React.FC = () => {
  const theme = useTheme();

  // State for Two-Sample calculator type
  const [twoSampleCalculatorType, setTwoSampleCalculatorType] = useState<string>('proportion');
  
  // State for Two-Sample calculator parameters
  const [twoSampleConfidenceLevel, setTwoSampleConfidenceLevel] = useState<number>(0.95);
  const [twoSamplePower, setTwoSamplePower] = useState<number>(0.80); // 80% power
  
  // Two Independent Proportions parameters
  const [proportion1, setProportion1] = useState<number>(0.5);
  const [proportion2, setProportion2] = useState<number>(0.7);
  
  // Two Independent Means parameters
  const [mean1, setMean1] = useState<number>(50);
  const [mean2, setMean2] = useState<number>(55);
  const [stdDev1, setStdDev1] = useState<number>(5);
  const [stdDev2, setStdDev2] = useState<number>(10);

  // State for results
  const [twoSampleRequiredSize, setTwoSampleRequiredSize] = useState<number | null>(null);
  const [twoSamplePowerCurveData, setTwoSamplePowerCurveData] = useState<any[]>([]);

  // Calculate two-sample size when parameters change
  useEffect(() => {
    calculateTwoSampleSize();
    generateTwoSamplePowerCurveData();
  }, [twoSampleConfidenceLevel, twoSamplePower, proportion1, proportion2, mean1, mean2, stdDev1, stdDev2, twoSampleCalculatorType]);
  
  // Function to calculate required sample size for two samples
  const calculateTwoSampleSize = () => {
    const zAlpha = getZScore(1 - (1 - twoSampleConfidenceLevel) / 2);
    const zBeta = getZScore(twoSamplePower);
    
    let sampleSize: number;
    
    if (twoSampleCalculatorType === 'proportion') {
      // Formula for two independent proportions
      const p1 = proportion1;
      const p2 = proportion2;
      const pBar = (p1 + p2) / 2;
      
      const numerator = Math.pow(
        zAlpha * Math.sqrt(2 * pBar * (1 - pBar)) + 
        zBeta * Math.sqrt(p1 * (1 - p1) + p2 * (1 - p2)), 
        2
      );
      const denominator = Math.pow(p1 - p2, 2);
      
      sampleSize = Math.ceil(numerator / denominator);
    } else {
      // Formula for two independent means
      const numerator = 2 * Math.pow(zAlpha + zBeta, 2) * (Math.pow(stdDev1, 2) + Math.pow(stdDev2, 2));
      const denominator = Math.pow(mean1 - mean2, 2);
      
      sampleSize = Math.ceil(numerator / denominator);
    }
    
    setTwoSampleRequiredSize(sampleSize);
  };

  // Generate data for two-sample power curve visualization
  const generateTwoSamplePowerCurveData = () => {
    const data = [];
    const minSampleSize = Math.max(10, Math.floor(twoSampleRequiredSize ? twoSampleRequiredSize * 0.5 : 20));
    const maxSampleSize = Math.ceil(twoSampleRequiredSize ? twoSampleRequiredSize * 1.5 : 100);
    const step = Math.max(1, Math.floor((maxSampleSize - minSampleSize) / 20));
    
    for (let n = minSampleSize; n <= maxSampleSize; n += step) {
      // Calculate power for this sample size
      let power: number;
      
      if (twoSampleCalculatorType === 'proportion') {
        const p1 = proportion1;
        const p2 = proportion2;
        const pBar = (p1 + p2) / 2;
        const zAlpha = getZScore(1 - (1 - twoSampleConfidenceLevel) / 2);
        
        const se = Math.sqrt(2 * pBar * (1 - pBar) / n);
        const effectSize = Math.abs(p1 - p2);
        const zBeta = (effectSize - zAlpha * se) / Math.sqrt((p1 * (1 - p1) + p2 * (1 - p2)) / n);
        power = jStat.normal.cdf(zBeta, 0, 1); // Use jStat.normal.cdf
      } else {
        const zAlpha = getZScore(1 - (1 - twoSampleConfidenceLevel) / 2);
        const effectSize = Math.abs(mean1 - mean2);
        const pooledSE = Math.sqrt((Math.pow(stdDev1, 2) + Math.pow(stdDev2, 2)) / n);
        const zBeta = (effectSize - zAlpha * pooledSE) / pooledSE;
        power = jStat.normal.cdf(zBeta, 0, 1); // Use jStat.normal.cdf
      }
      
      data.push({
        sampleSize: n,
        power: Math.max(0, Math.min(1, power)),
      });
    }
    
    setTwoSamplePowerCurveData(data);
  };
  
  // Handle two-sample calculator type change
  const handleTwoSampleCalculatorTypeChange = (_event: React.MouseEvent<HTMLElement>, newValue: string) => {
    if (newValue !== null) {
      setTwoSampleCalculatorType(newValue);
      // Set default values based on calculator type
      if (newValue === 'proportion') {
        setTwoSampleConfidenceLevel(0.95);
        setTwoSamplePower(0.80);
        setProportion1(0.5);
        setProportion2(0.7);
      } else if (newValue === 'mean') {
        setTwoSampleConfidenceLevel(0.95);
        setTwoSamplePower(0.80);
        setMean1(50);
        setMean2(55);
        setStdDev1(5);
        setStdDev2(10);
      }
    }
  };
  
  // Handle copying two-sample results to clipboard
  const handleCopyTwoSampleResults = () => {
    if (twoSampleRequiredSize) {
      let resultText = '';
      if (twoSampleCalculatorType === 'proportion') {
        resultText = `Required Sample Size: ${twoSampleRequiredSize} per group for comparing two independent proportions (${Math.round(proportion1 * 100)}% vs ${Math.round(proportion2 * 100)}%) with ${Math.round(twoSamplePower * 100)}% power at a ${Math.round(twoSampleConfidenceLevel * 100)}% confidence level.`;
      } else {
        resultText = `Required Sample Size: ${twoSampleRequiredSize} per group for comparing two independent means (${mean1} vs ${mean2}) with standard deviations of ${stdDev1} and ${stdDev2}, with ${Math.round(twoSamplePower * 100)}% power at a ${Math.round(twoSampleConfidenceLevel * 100)}% confidence level.`;
      }
      navigator.clipboard.writeText(resultText);
    }
  };

  // Handle exporting results as PDF
  const handleExportPDF = () => {
    // For now, just a placeholder
    console.log('Export PDF functionality would go here');
  };

  // Handle reset for two-sample
  const handleTwoSampleReset = () => {
    setTwoSampleConfidenceLevel(0.95);
    setTwoSamplePower(0.80);
    setProportion1(0.5);
    setProportion2(0.7);
    setMean1(50);
    setMean2(55);
    setStdDev1(10);
    setStdDev2(15);
  };

  return (
    <>
      {/* Calculator Type Selector */}
      <Box sx={{ mb: 3 }}>
        <ToggleButtonGroup
          value={twoSampleCalculatorType}
          exclusive
          onChange={handleTwoSampleCalculatorTypeChange}
          aria-label="two sample calculator type"
          fullWidth
        >
          <ToggleButton
            value="proportion"
            aria-label="two independent proportions"
            sx={{
              '&.Mui-selected': {
                bgcolor: theme.palette.primary.main,
                color: theme.palette.primary.contrastText,
                '&:hover': {
                  bgcolor: theme.palette.primary.dark,
                },
              },
            }}
          >
            Two Independent Proportions
          </ToggleButton>
          <ToggleButton
            value="mean"
            aria-label="two independent means"
            sx={{
              '&.Mui-selected': {
                bgcolor: theme.palette.secondary.main,
                color: theme.palette.secondary.contrastText,
                 '&:hover': {
                  bgcolor: theme.palette.secondary.dark,
                },
              },
            }}
          >
            Two Independent Means
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      <Grid container spacing={3}>
        {/* Input Parameters */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Input Parameters</Typography>
            
            {/* Confidence Level */}
            <Box sx={{ mb: 4 }}>
              <Typography gutterBottom>Confidence Level (α)</Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12}>
                  <Slider
                    value={twoSampleConfidenceLevel * 100}
                    onChange={(_, newValue) => setTwoSampleConfidenceLevel((newValue as number) / 100)}
                    step={1}
                    min={80}
                    max={99}
                    marks={[
                      { value: 80, label: '80%' },
                      { value: 90, label: '90%' },
                      { value: 95, label: '95%' },
                      { value: 99, label: '99%' },
                    ]}
                  />
                </Grid>
              </Grid>
            </Box>
            
            {/* Power */}
            <Box sx={{ mb: 4 }}>
              <Typography gutterBottom>
                Statistical Power (1-β)
                <Tooltip title="The probability of correctly detecting a true difference between groups.">
                  <IconButton size="small" sx={{ ml: 1 }}>
                    <HelpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12}>
                  <Slider
                    value={twoSamplePower * 100}
                    onChange={(_, newValue) => setTwoSamplePower((newValue as number) / 100)}
                    step={5}
                    min={70}
                    max={95}
                    marks={[
                      { value: 70, label: '70%' },
                      { value: 80, label: '80%' },
                      { value: 90, label: '90%' },
                      { value: 95, label: '95%' },
                    ]}
                  />
                </Grid>
              </Grid>
            </Box>
            
            {/* Parameters for Two Independent Proportions */}
            {twoSampleCalculatorType === 'proportion' && (
              <>
                <Box sx={{ mb: 4 }}>
                  <Typography gutterBottom>
                    Expected Proportion - Group 1 (p₁)
                    <Tooltip title="The expected proportion in the first group.">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <HelpIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs>
                      <TextField
                        value={Math.round(proportion1 * 100)}
                        onChange={(e) => setProportion1(Number(e.target.value) / 100)}
                        type="number"
                        inputProps={{
                          min: 1,
                          max: 99,
                          step: 1,
                        }}
                        fullWidth
                      />
                    </Grid>
                    <Grid item>
                      <Typography>%</Typography>
                    </Grid>
                  </Grid>
                </Box>
                
                <Box sx={{ mb: 4 }}>
                  <Typography gutterBottom>
                    Expected Proportion - Group 2 (p₂)
                    <Tooltip title="The expected proportion in the second group.">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <HelpIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs>
                      <TextField
                        value={Math.round(proportion2 * 100)}
                        onChange={(e) => setProportion2(Number(e.target.value) / 100)}
                        type="number"
                        inputProps={{
                          min: 1,
                          max: 99,
                          step: 1,
                        }}
                        fullWidth
                      />
                    </Grid>
                    <Grid item>
                      <Typography>%</Typography>
                    </Grid>
                  </Grid>
                </Box>
              </>
            )}
            
            {/* Parameters for Two Independent Means */}
            {twoSampleCalculatorType === 'mean' && (
              <>
                <Box sx={{ mb: 4 }}>
                  <Typography gutterBottom>
                    Expected Mean - Group 1 (μ₁)
                    <Tooltip title="The expected mean in the first group.">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <HelpIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <TextField
                    type="number"
                    value={mean1}
                    onChange={(e) => setMean1(Number(e.target.value))}
                    inputProps={{
                      step: 0.1,
                    }}
                    fullWidth
                  />
                </Box>
                
                <Box sx={{ mb: 4 }}>
                  <Typography gutterBottom>
                    Expected Mean - Group 2 (μ₂)
                    <Tooltip title="The expected mean in the second group.">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <HelpIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <TextField
                    type="number"
                    value={mean2}
                    onChange={(e) => setMean2(Number(e.target.value))}
                    inputProps={{
                      step: 0.1,
                    }}
                    fullWidth
                  />
                </Box>
                
                <Box sx={{ mb: 4 }}>
                  <Typography gutterBottom>
                    Standard Deviation - Group 1 (σ₁)
                    <Tooltip title="The expected standard deviation in the first group.">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <HelpIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <TextField
                    type="number"
                    value={stdDev1}
                    onChange={(e) => setStdDev1(Number(e.target.value))}
                    inputProps={{
                      min: 0.1,
                      step: 0.1,
                    }}
                    fullWidth
                  />
                </Box>
                
                <Box sx={{ mb: 4 }}>
                  <Typography gutterBottom>
                    Standard Deviation - Group 2 (σ₂)
                    <Tooltip title="The expected standard deviation in the second group.">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <HelpIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <TextField
                    type="number"
                    value={stdDev2}
                    onChange={(e) => setStdDev2(Number(e.target.value))}
                    inputProps={{
                      min: 0.1,
                      step: 0.1,
                    }}
                    fullWidth
                  />
                </Box>
              </>
            )}
            

            
            {/* Reset Button */}
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleTwoSampleReset}
              fullWidth
              sx={{ mt: 2 }}
            >
              Reset
            </Button>
          </Paper>
        </Grid>
        
        {/* Results */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Results</Typography>
              <Box>
                <Tooltip title="Copy results">
                  <IconButton onClick={handleCopyTwoSampleResults}>
                    <ContentCopyIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Export as PDF">
                  <IconButton onClick={handleExportPDF}>
                    <PictureAsPdfIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            
            {/* Sample Size Result */}
            <Card sx={{ mb: 4, bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>Required Sample Size</Typography>
                <Typography variant="h2" color="primary">
                  {twoSampleRequiredSize}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  per group
                </Typography>
              </CardContent>
            </Card>
            
            {/* Power Curve Visualization */}
            <Typography variant="h6" gutterBottom>Power by Sample Size</Typography>
            <Box sx={{ height: 300, mb: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={twoSamplePowerCurveData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="sampleSize" 
                    label={{ value: 'Sample Size (per group)', position: 'outerBottom', offset: 15, style: { fontSize: '12px' } }}
                    height={70}
                  />
                  <YAxis 
                    label={{ value: 'Statistical Power', angle: -90, position: 'outside', offset: -60, style: { fontSize: '12px' } }}
                    tickFormatter={(value) => `${Math.round(value * 100)}%`}
                    domain={[0, 1]}
                    width={100}
                  />
                  <RechartsTooltip 
                    formatter={(value: number) => [
                      `${(value * 100).toFixed(1)}%`,
                      'Power'
                    ]}
                    labelFormatter={(value) => `Sample Size: ${value} per group`}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="power" 
                    stroke={theme.palette.primary.main} 
                    activeDot={{ r: 8 }} 
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Box>
            
            {/* Interpretation */}
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>Interpretation</Typography>
              {twoSampleCalculatorType === 'proportion' ? (
                <Typography variant="body1">
                  A sample size of {twoSampleRequiredSize} per group is needed to detect a difference between proportions of {Math.round(proportion1 * 100)}% and {Math.round(proportion2 * 100)}% with {Math.round(twoSamplePower * 100)}% power at a {Math.round(twoSampleConfidenceLevel * 100)}% confidence level.
                </Typography>
              ) : (
                <Typography variant="body1">
                  A sample size of {twoSampleRequiredSize} per group is needed to detect a difference between means of {mean1} and {mean2} (with standard deviations of {stdDev1} and {stdDev2}) with {Math.round(twoSamplePower * 100)}% power at a {Math.round(twoSampleConfidenceLevel * 100)}% confidence level.
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </>
  );
};

export default TwoSampleCalculator;
