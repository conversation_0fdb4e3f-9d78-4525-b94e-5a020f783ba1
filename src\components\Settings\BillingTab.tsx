import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Launch as LaunchIcon,
  CreditCard as CreditCardIcon,
  Receipt as ReceiptIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { config, isStripeEnabled } from '../../config/environment';
import AccountStatusCard from '../Account/AccountStatusCard';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);

const BillingTab: React.FC = () => {
  const {
    user,
    session,
    accountType,
    subscriptionData,
    subscriptionStatus,
    hasActiveSubscription,
    nextPaymentDate,
    billingCycle,
    canUpgradeAccount,
    refreshSubscription,
    isEducationalUser,
    educationalTier,
    canAccessAdvancedAnalysis,
    canAccessPublicationReady
  } = useAuth();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleOpenCustomerPortal = async () => {
    if (!isStripeEnabled()) {
      // Fallback to email contact
      const subject = 'Billing Management Request';
      const body = `Hi DataStatPro Team,

I would like to manage my billing and subscription settings.

Account Details:
- Email: ${user?.email}
- Current Plan: ${accountType?.toUpperCase()}
- Subscription Status: ${subscriptionStatus?.toUpperCase()}

Please provide assistance with billing management.

Thank you!`;

      const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.open(mailtoUrl);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { data, error: functionError } = await supabase.functions.invoke('create-customer-portal', {
        headers: {
          Authorization: `Bearer ${session?.access_token}`,
        },
      });

      if (functionError) {
        throw new Error(functionError.message);
      }

      if (data?.url) {
        window.open(data.url, '_blank');
      } else {
        throw new Error('No portal URL received');
      }
    } catch (err: any) {
      console.error('Error opening customer portal:', err);
      setError(err.message || 'Failed to open billing portal');
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshSubscription = async () => {
    setLoading(true);
    try {
      await refreshSubscription();
    } catch (err) {
      console.error('Error refreshing subscription:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'past_due':
        return 'warning';
      case 'canceled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckIcon />;
      case 'past_due':
        return <WarningIcon />;
      default:
        return <InfoIcon />;
    }
  };

  return (
    <Box>
      {/* Development Notice */}
      {!isStripeEnabled() && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body1" fontWeight="medium">
            🚧 Billing Management in Development
          </Typography>
          <Typography variant="body2" sx={{ mt: 1 }}>
            Full billing management features are currently in development. 
            For billing inquiries, please contact us via email using the buttons below.
          </Typography>
        </Alert>
      )}

      {/* Current Plan Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CreditCardIcon />
          Current Plan
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Chip 
            label={accountType?.toUpperCase() || 'STANDARD'} 
            color={accountType === 'standard' ? 'default' : 'primary'}
            size="large"
          />
          {hasActiveSubscription && subscriptionStatus && (
            <Chip 
              label={subscriptionStatus.toUpperCase()} 
              color={getStatusColor(subscriptionStatus)}
              size="small"
              icon={getStatusIcon(subscriptionStatus)}
            />
          )}
        </Box>

        {subscriptionData && (
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Billing Cycle
              </Typography>
              <Typography variant="body1" sx={{ textTransform: 'capitalize' }}>
                {billingCycle} billing
              </Typography>
            </Grid>
            {nextPaymentDate && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Next Payment Date
                </Typography>
                <Typography variant="body1">
                  {formatDate(nextPaymentDate)}
                </Typography>
              </Grid>
            )}
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Subscription ID
              </Typography>
              <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                {subscriptionData.stripe_subscription_id || 'N/A'}
              </Typography>
            </Grid>
          </Grid>
        )}
      </Paper>

      {/* Subscription Management */}
      {hasActiveSubscription && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ReceiptIcon />
            Manage Subscription
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Update payment method, download invoices, and manage your subscription settings.
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              onClick={handleOpenCustomerPortal}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <LaunchIcon />}
            >
              {isStripeEnabled() ? 'Open Billing Portal' : 'Contact Support'}
            </Button>
            
            <Button
              variant="outlined"
              onClick={handleRefreshSubscription}
              disabled={loading}
            >
              Refresh Status
            </Button>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Paper>
      )}

      {/* Account Upgrade Section */}
      {canUpgradeAccount && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Upgrade Your Account
          </Typography>
          <AccountStatusCard 
            showUpgradeOptions={true}
            compact={false}
          />
        </Paper>
      )}

      {/* Billing Information */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Billing Information
        </Typography>
        
        <List>
          <ListItem>
            <ListItemIcon>
              <InfoIcon color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Secure Payment Processing"
              secondary="All payments are processed securely through Stripe. We never store your payment information."
            />
          </ListItem>
          
          <ListItem>
            <ListItemIcon>
              <InfoIcon color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Billing Cycle"
              secondary="You can choose between monthly and annual billing. Annual billing includes a 20% discount."
            />
          </ListItem>
          
          <ListItem>
            <ListItemIcon>
              <InfoIcon color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Cancellation Policy"
              secondary="You can cancel your subscription at any time. Your access will continue until the end of your current billing period."
            />
          </ListItem>
          
          {accountType === 'edu' && (
            <ListItem>
              <ListItemIcon>
                <InfoIcon color="secondary" />
              </ListItemIcon>
              <ListItemText
                primary="Educational Discount"
                secondary="Your educational discount is automatically applied based on your .edu email address."
              />
            </ListItem>
          )}
        </List>

        <Divider sx={{ my: 2 }} />
        
        <Typography variant="body2" color="text.secondary">
          For billing questions or support, contact us at{' '}
          <Button
            variant="text"
            size="small"
            onClick={() => window.open('mailto:<EMAIL>')}
            sx={{ textTransform: 'none', p: 0, minWidth: 'auto' }}
          >
            <EMAIL>
          </Button>
        </Typography>
      </Paper>
    </Box>
  );
};

export default BillingTab;
