import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Chip,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Button
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  School as SchoolIcon,
  Star as StarIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { AdvancedAnalysisGate, PublicationReadyGate } from '../FeatureGates';

const EducationalTierTest: React.FC = () => {
  const {
    user,
    accountType,
    isEducationalUser,
    educationalTier,
    canAccessAdvancedAnalysis,
    canAccessPublicationReady,
    canAccessCloudStorage,
    canAccessProFeatures,
    refreshProfile
  } = useAuth();

  // Debug information
  console.log('🔍 Educational Tier Debug Info:', {
    user: user?.email,
    accountType,
    isEducationalUser,
    educationalTier,
    canAccessAdvancedAnalysis,
    canAccessPublicationReady,
    canAccessCloudStorage,
    canAccessProFeatures
  });

  const getAccountTypeDisplay = () => {
    switch (accountType) {
      case 'edu':
        return { label: 'Educational Free', color: 'secondary', icon: <SchoolIcon /> };
      case 'edu_pro':
        return { label: 'Educational Pro', color: 'primary', icon: <SchoolIcon /> };
      case 'pro':
        return { label: 'Pro', color: 'primary', icon: <StarIcon /> };
      case 'standard':
        return { label: 'Standard', color: 'default', icon: <InfoIcon /> };
      default:
        return { label: 'Guest', color: 'default', icon: <InfoIcon /> };
    }
  };

  const accountDisplay = getAccountTypeDisplay();

  const handleRefreshProfile = async () => {
    console.log('🔄 Manually refreshing profile...');
    await refreshProfile();
    console.log('✅ Profile refresh completed');
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Educational Tier Implementation Test
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={handleRefreshProfile}
        >
          🔄 Refresh Profile Data
        </Button>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Testing Instructions:</strong><br />
          1. Manually change accounttype and edu_subscription_type in Supabase<br />
          2. Click "Refresh Profile Data" button to fetch latest values<br />
          3. Check the debug information below to see if changes are reflected<br />
          4. Open browser console for detailed logging
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* Database vs AuthContext Comparison */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, border: '2px solid', borderColor: 'warning.main' }}>
            <Typography variant="h6" gutterBottom color="warning.main">
              🔍 Database vs AuthContext Debug
            </Typography>

            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Issue:</strong> Manual database changes not reflected in UI<br />
                <strong>Cause:</strong> AuthContext caches profile data and doesn't auto-refresh<br />
                <strong>Solution:</strong> Click "Refresh Profile Data" after making database changes
              </Typography>
            </Alert>

            <Typography variant="subtitle2" gutterBottom>
              Current AuthContext Values:
            </Typography>
            <Box sx={{ bgcolor: 'background.default', p: 2, borderRadius: 1, mb: 2 }}>
              <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
{`User Email: ${user?.email || 'Not logged in'}
Account Type: ${accountType || 'null'}
Educational Tier: ${educationalTier || 'null'}
Is Educational User: ${isEducationalUser}
Can Access Advanced Analysis: ${canAccessAdvancedAnalysis}
Can Access Publication Ready: ${canAccessPublicationReady}
Can Access Cloud Storage: ${canAccessCloudStorage}
Legacy Can Access Pro Features: ${canAccessProFeatures}`}
              </Typography>
            </Box>

            <Typography variant="body2" color="text.secondary">
              To test: Change values in Supabase → Click refresh button → Check if values update above
            </Typography>
          </Paper>
        </Grid>

        {/* Account Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Account Information
            </Typography>
            
            <List>
              <ListItem>
                <ListItemIcon>
                  {accountDisplay.icon}
                </ListItemIcon>
                <ListItemText 
                  primary="Account Type"
                  secondary={
                    <Chip 
                      label={accountDisplay.label}
                      color={accountDisplay.color as any}
                      size="small"
                    />
                  }
                />
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  <SchoolIcon />
                </ListItemIcon>
                <ListItemText 
                  primary="Educational User"
                  secondary={
                    <Chip 
                      label={isEducationalUser ? 'Yes' : 'No'}
                      color={isEducationalUser ? 'success' : 'default'}
                      size="small"
                    />
                  }
                />
              </ListItem>

              {isEducationalUser && (
                <ListItem>
                  <ListItemIcon>
                    <StarIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Educational Tier"
                    secondary={
                      <Chip 
                        label={educationalTier || 'None'}
                        color={educationalTier === 'pro' ? 'primary' : 'secondary'}
                        size="small"
                      />
                    }
                  />
                </ListItem>
              )}

              <ListItem>
                <ListItemIcon>
                  {user ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                </ListItemIcon>
                <ListItemText 
                  primary="Authentication Status"
                  secondary={user ? `Logged in as ${user.email}` : 'Not logged in'}
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Feature Access */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Feature Access Permissions
            </Typography>
            
            <List>
              <ListItem>
                <ListItemIcon>
                  {canAccessAdvancedAnalysis ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                </ListItemIcon>
                <ListItemText 
                  primary="Advanced Analysis"
                  secondary={canAccessAdvancedAnalysis ? 'Access granted' : 'Access denied'}
                />
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  {canAccessPublicationReady ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                </ListItemIcon>
                <ListItemText 
                  primary="Publication Ready"
                  secondary={canAccessPublicationReady ? 'Access granted' : 'Access denied'}
                />
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  {canAccessCloudStorage ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                </ListItemIcon>
                <ListItemText 
                  primary="Cloud Storage"
                  secondary={canAccessCloudStorage ? 'Access granted' : 'Access denied'}
                />
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  {canAccessProFeatures ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                </ListItemIcon>
                <ListItemText 
                  primary="Legacy Pro Features"
                  secondary={canAccessProFeatures ? 'Access granted (backward compatibility)' : 'Access denied'}
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Critical Issue Debug */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, border: '2px solid', borderColor: 'error.main' }}>
            <Typography variant="h6" gutterBottom color="error">
              🚨 Critical Issue Debug: Educational User Sidebar Access
            </Typography>

            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Issue:</strong> Educational users should have access to Advanced Analysis features in the sidebar navigation.
                <br />
                <strong>Expected:</strong> Educational users (accounttype = 'edu') should see enabled Advanced Analysis menu items.
                <br />
                <strong>Root Cause:</strong> Sidebar was using legacy `canAccessProFeatures` instead of granular permissions.
              </Typography>
            </Alert>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Permission Calculation Debug:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    {canAccessAdvancedAnalysis ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                  </ListItemIcon>
                  <ListItemText
                    primary={`canAccessAdvancedAnalysis: ${canAccessAdvancedAnalysis}`}
                    secondary={`Should be TRUE for educational users (accounttype = 'edu')`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {canAccessPublicationReady ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                  </ListItemIcon>
                  <ListItemText
                    primary={`canAccessPublicationReady: ${canAccessPublicationReady}`}
                    secondary={`Should be FALSE for educational free users (requires upgrade)`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {canAccessProFeatures ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                  </ListItemIcon>
                  <ListItemText
                    primary={`canAccessProFeatures (legacy): ${canAccessProFeatures}`}
                    secondary={`Fixed to use canAccessAdvancedAnalysis for backward compatibility`}
                  />
                </ListItem>
              </List>
            </Box>

            {isEducationalUser && accountType === 'edu' && (
              <Alert severity={canAccessAdvancedAnalysis ? "success" : "error"} sx={{ mb: 2 }}>
                <Typography variant="body2">
                  {canAccessAdvancedAnalysis
                    ? "✅ FIXED: Educational user can access Advanced Analysis features!"
                    : "❌ ISSUE: Educational user cannot access Advanced Analysis features!"
                  }
                </Typography>
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Feature Gate Tests */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Feature Gate Component Tests
            </Typography>

            <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
              Advanced Analysis Gate Test:
            </Typography>
            <AdvancedAnalysisGate showPrompt={false}>
              <Alert severity="success">
                ✅ Advanced Analysis Gate: Access granted! This content is visible.
              </Alert>
            </AdvancedAnalysisGate>

            <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
              Publication Ready Gate Test:
            </Typography>
            <PublicationReadyGate showPrompt={false}>
              <Alert severity="success">
                ✅ Publication Ready Gate: Access granted! This content is visible.
              </Alert>
            </PublicationReadyGate>
          </Paper>
        </Grid>

        {/* Educational User Specific Information */}
        {isEducationalUser && (
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SchoolIcon />
                Educational Account Benefits
              </Typography>

              {accountType === 'edu' && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Educational Free Tier:</strong><br />
                    • You have free access to Advanced Analysis features<br />
                    • Upgrade to Pro ($10/month) for Publication Ready tools and Cloud Storage<br />
                    • No educational discount - same Pro pricing applies
                  </Typography>
                </Alert>
              )}

              {accountType === 'edu_pro' && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Educational Pro Tier:</strong><br />
                    • You have full Pro feature access<br />
                    • Advanced Analysis + Publication Ready + Cloud Storage<br />
                    • Maintaining educational account status
                  </Typography>
                </Alert>
              )}

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Feature Access Summary:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="Data Import & Local Storage" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {canAccessAdvancedAnalysis ? <CheckIcon color="success" fontSize="small" /> : <CancelIcon color="error" fontSize="small" />}
                  </ListItemIcon>
                  <ListItemText 
                    primary="Advanced Analysis" 
                    secondary={canAccessAdvancedAnalysis ? "Included free" : "Not available"}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {canAccessPublicationReady ? <CheckIcon color="success" fontSize="small" /> : <CancelIcon color="error" fontSize="small" />}
                  </ListItemIcon>
                  <ListItemText 
                    primary="Publication Ready" 
                    secondary={canAccessPublicationReady ? "Pro subscription active" : "Requires Pro upgrade"}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {canAccessCloudStorage ? <CheckIcon color="success" fontSize="small" /> : <CancelIcon color="error" fontSize="small" />}
                  </ListItemIcon>
                  <ListItemText 
                    primary="Cloud Storage" 
                    secondary={canAccessCloudStorage ? "Pro subscription active" : "Requires Pro upgrade"}
                  />
                </ListItem>
              </List>
            </Paper>
          </Grid>
        )}

        {/* Educational Subscription Type Fix Test */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              🔧 Educational Subscription Type Fix Test
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              This section tests the fix for educational users with edu_subscription_type = 'pro'
            </Typography>

            <Alert
              severity={
                isEducationalUser && educationalTier === 'pro' && canAccessPublicationReady && canAccessCloudStorage
                  ? "success"
                  : isEducationalUser && educationalTier === 'pro'
                  ? "error"
                  : "info"
              }
              sx={{ mb: 2 }}
            >
              <Typography variant="body2">
                {isEducationalUser && educationalTier === 'pro' && canAccessPublicationReady && canAccessCloudStorage
                  ? "✅ FIX WORKING: Educational user with Pro subscription has full Pro access!"
                  : isEducationalUser && educationalTier === 'pro'
                  ? "❌ FIX NEEDED: Educational user with Pro subscription lacks Pro features!"
                  : isEducationalUser && educationalTier === 'free'
                  ? "ℹ️ Educational user with free tier (expected limited access)"
                  : "ℹ️ Not an educational user or no subscription data"
                }
              </Typography>
            </Alert>

            <List dense>
              <ListItem>
                <ListItemIcon>
                  <InfoIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Account Type"
                  secondary={accountType || 'Not set'}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <InfoIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Educational Tier"
                  secondary={educationalTier || 'Not set'}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  {canAccessPublicationReady ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                </ListItemIcon>
                <ListItemText
                  primary="Publication Ready Access"
                  secondary={
                    canAccessPublicationReady
                      ? "✅ Access granted"
                      : "❌ Access denied"
                  }
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  {canAccessCloudStorage ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                </ListItemIcon>
                <ListItemText
                  primary="Cloud Storage Access"
                  secondary={
                    canAccessCloudStorage
                      ? "✅ Access granted"
                      : "❌ Access denied"
                  }
                />
              </ListItem>
            </List>

            <Typography variant="body2" color="text.secondary" sx={{ mt: 2, fontStyle: 'italic' }}>
              Expected behavior: Educational users with edu_subscription_type = 'pro' should have access to both Publication Ready and Cloud Storage features.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EducationalTierTest;
