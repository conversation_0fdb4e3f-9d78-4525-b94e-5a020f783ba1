import React, { useState } from 'react';
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Chip,
  Alert
} from '@mui/material';
import {
  Save as SaveIcon,
  Folder as FolderIcon,
  Cloud as CloudIcon
} from '@mui/icons-material';
import { useResults, ResultItem } from '../../context/ResultsContext';
import { useAuth } from '../../context/AuthContext';

interface AddToResultsButtonProps {
  resultData: Omit<ResultItem, 'id' | 'timestamp' | 'selected'>;
  onSuccess?: (resultId: string) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  variant?: 'contained' | 'outlined' | 'text';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  sx?: any;
}

const AddToResultsButton: React.FC<AddToResultsButtonProps> = ({
  resultData,
  onSuccess,
  onError,
  disabled = false,
  variant = 'contained',
  size = 'medium',
  fullWidth = false,
  sx = {}
}) => {
  const { addResult, projects, currentProjectId, getProjectResults } = useResults();
  const { canAccessProFeatures } = useAuth();
  
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string>(currentProjectId || 'default');

  const handleAddResult = () => {
    if (canAccessProFeatures && projects.length > 1) {
      // Show project selection dialog for Pro users with multiple projects
      setDialogOpen(true);
    } else {
      // Directly add to current/default project
      addResultToProject(currentProjectId || 'default');
    }
  };

  const addResultToProject = (projectId: string) => {
    try {
      const resultWithProject = {
        ...resultData,
        projectId: projectId === 'default' ? undefined : projectId
      };
      
      const resultId = addResult(resultWithProject);
      
      if (onSuccess) {
        onSuccess(resultId);
      }
      
      setDialogOpen(false);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add result';
      if (onError) {
        onError(errorMessage);
      }
    }
  };

  const handleConfirmAdd = () => {
    addResultToProject(selectedProjectId);
  };

  const getProjectIcon = (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    if (!project) return <FolderIcon fontSize="small" />;
    
    return project.isLocal ? (
      <FolderIcon fontSize="small" />
    ) : (
      <CloudIcon fontSize="small" color="primary" />
    );
  };

  const getProjectName = (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    return project ? project.name : 'Default Project';
  };

  return (
    <>
      <Button
        variant={variant}
        color="secondary"
        startIcon={<SaveIcon />}
        onClick={handleAddResult}
        disabled={disabled}
        size={size}
        fullWidth={fullWidth}
        sx={{
          px: 3,
          borderRadius: 2,
          textTransform: 'none',
          ...sx
        }}
      >
        Add to Results Manager
      </Button>

      {/* Project Selection Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add to Results Manager</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Choose which project to add this result to:
          </Typography>
          
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel id="project-select-label">Project</InputLabel>
            <Select
              labelId="project-select-label"
              value={selectedProjectId}
              label="Project"
              onChange={(e) => setSelectedProjectId(e.target.value)}
            >
              {projects.map((project) => (
                <MenuItem key={project.id} value={project.id}>
                  <Box display="flex" alignItems="center" gap={1} width="100%">
                    {getProjectIcon(project.id)}
                    <Typography sx={{ flexGrow: 1 }}>
                      {project.name}
                    </Typography>
                    <Chip
                      label={getProjectResults(project.id).length}
                      size="small"
                      variant="outlined"
                      sx={{ minWidth: 'auto', height: 20 }}
                    />
                    {!project.isLocal && (
                      <CloudIcon fontSize="small" color="primary" />
                    )}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {canAccessProFeatures && (
            <Alert severity="info" sx={{ mt: 2 }}>
              Results are automatically organized by project. You can move results between projects later in the Results Manager.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleConfirmAdd} variant="contained" color="secondary">
            Add to {getProjectName(selectedProjectId)}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AddToResultsButton;
