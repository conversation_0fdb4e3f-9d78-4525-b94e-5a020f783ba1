import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>per,
  Step,
  <PERSON><PERSON><PERSON><PERSON>,
  Step<PERSON>ontent,
  StepButton,
  Button,
  Typography,
  Paper,
  styled,
  useTheme,
  alpha,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  HelpOutline as HelpOutlineIcon,
  Check as CheckIcon,
  NavigateNext as NavigateNextIcon,
  NavigateBefore as NavigateBeforeIcon,
  RestartAlt as RestartAltIcon,
  PlayArrow as PlayArrowIcon,
  Lightbulb as LightbulbIcon
} from '@mui/icons-material';

// Step interfaces and types
export interface AnalysisStep {
  label: string;
  description?: string;
  optional?: boolean;
  help?: string;
  content: React.ReactNode;
  validation?: () => boolean | string; // Return true if valid, or error message if invalid
  tip?: string;
}

interface AnalysisStepsProps {
  steps: AnalysisStep[];
  activeStep?: number;
  onChange?: (step: number) => void;
  onComplete?: () => void;
  onReset?: () => void;
  variant?: 'default' | 'outlined' | 'navigation';
  orientation?: 'vertical' | 'horizontal';
  showStepDescription?: boolean;
  disableNavigation?: boolean;
  autoValidate?: boolean;
  showTips?: boolean;
  finalActionLabel?: string;
  finalActionIcon?: React.ReactNode;
}

const StyledStepper = styled(Stepper)(({ theme }) => ({
  '& .MuiStepLabel-iconContainer': {
    paddingRight: theme.spacing(1),
  },
  '& .MuiStepConnector-root': {
    marginLeft: theme.spacing(1.3),
  },
}));

const ActionButton = styled(Button, {
  shouldForwardProp: (prop) => prop !== 'isNext',
})<{ isNext?: boolean }>(({ theme, isNext }) => ({
  marginTop: theme.spacing(1),
  marginRight: theme.spacing(1),
  ...(isNext && {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.primary.dark,
    },
  }),
}));

const AnalysisSteps: React.FC<AnalysisStepsProps> = ({
  steps,
  activeStep: externalActiveStep,
  onChange,
  onComplete,
  onReset,
  variant = 'default',
  orientation = 'vertical',
  showStepDescription = true,
  disableNavigation = false,
  autoValidate = true,
  showTips = true,
  finalActionLabel = 'Generate Results',
  finalActionIcon = <PlayArrowIcon />,
}) => {
  const [internalActiveStep, setInternalActiveStep] = useState(0);
  const [validationErrors, setValidationErrors] = useState<Record<number, string>>({});
  const [completedSteps, setCompletedSteps] = useState<Record<number, boolean>>({});
  const theme = useTheme();
  
  // Determine if we're using controlled or uncontrolled activeStep
  const activeStep = externalActiveStep !== undefined ? externalActiveStep : internalActiveStep;
  const setActiveStep = (step: number) => {
    if (onChange) {
      onChange(step);
    } else {
      setInternalActiveStep(step);
    }
  };

  const handleNext = () => {
    if (autoValidate && steps[activeStep].validation) {
      const validationResult = steps[activeStep].validation();
      if (validationResult !== true) {
        setValidationErrors({
          ...validationErrors,
          [activeStep]: typeof validationResult === 'string' ? validationResult : 'This step has errors'
        });
        return;
      }
    }
    
    // Mark current step as completed
    setCompletedSteps({
      ...completedSteps,
      [activeStep]: true
    });
    
    // Clear any existing error for this step
    const newValidationErrors = { ...validationErrors };
    delete newValidationErrors[activeStep];
    setValidationErrors(newValidationErrors);
    
    // Move to next step or complete
    if (activeStep === steps.length - 1) {
      if (onComplete) onComplete();
    } else {
      setActiveStep(activeStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(activeStep - 1);
  };

  const handleStep = (step: number) => () => {
    if (!disableNavigation) {
      setActiveStep(step);
    }
  };

  const handleReset = () => {
    setActiveStep(0);
    setValidationErrors({});
    setCompletedSteps({});
    if (onReset) onReset();
  };

  const isStepComplete = (step: number) => {
    return completedSteps[step] === true;
  };

  const isStepValid = (step: number) => {
    return validationErrors[step] === undefined;
  };

  // Generate content based on variant
  const renderStepperContent = () => {
    return (
      <Box sx={{ mb: 2 }}>
        {orientation === 'vertical' ? (
          <StyledStepper activeStep={activeStep} orientation="vertical">
            {steps.map((step, index) => (
              <Step key={index} completed={isStepComplete(index)}>
                <StepButton 
                  onClick={handleStep(index)}
                  optional={
                    step.optional ? 
                      <Typography variant="caption">Optional</Typography> : 
                      undefined
                  }
                  error={!isStepValid(index)}
                  disabled={disableNavigation}
                >
                  <StepLabel
                    StepIconProps={{
                      error: !isStepValid(index),
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {step.label}
                      {step.help && (
                        <Tooltip title={step.help} arrow placement="top">
                          <IconButton size="small" sx={{ ml: 0.5, p: 0.5 }}>
                            <HelpOutlineIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </StepLabel>
                </StepButton>
                <StepContent>
                  {showStepDescription && step.description && (
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {step.description}
                    </Typography>
                  )}
                  
                  {validationErrors[index] && (
                    <Typography 
                      variant="body2" 
                      color="error" 
                      sx={{ 
                        mb: 2,
                        p: 1,
                        borderRadius: 1,
                        backgroundColor: alpha(theme.palette.error.main, 0.05)
                      }}
                    >
                      {validationErrors[index]}
                    </Typography>
                  )}
                  
                  {step.content}
                  
                  {showTips && step.tip && (
                    <Box 
                      sx={{ 
                        mt: 2,
                        p: 1.5,
                        borderRadius: 1,
                        display: 'flex',
                        backgroundColor: alpha(theme.palette.info.main, 0.05),
                        border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                      }}
                    >
                      <LightbulbIcon color="info" sx={{ mr: 1, fontSize: 20 }} />
                      <Typography variant="body2" color="text.secondary">
                        <Typography component="span" variant="body2" fontWeight="medium" color="info.main">
                          Tip:
                        </Typography>{' '}
                        {step.tip}
                      </Typography>
                    </Box>
                  )}
                  
                  <Box sx={{ mb: 2, mt: 2, display: 'flex', justifyContent: 'space-between' }}>
                    <div>
                      <ActionButton
                        disabled={activeStep === 0}
                        onClick={handleBack}
                        startIcon={<NavigateBeforeIcon />}
                        variant="outlined"
                        size="small"
                      >
                        Back
                      </ActionButton>
                      <ActionButton
                        variant="contained"
                        onClick={handleNext}
                        startIcon={activeStep === steps.length - 1 ? finalActionIcon : <NavigateNextIcon />}
                        isNext
                        size="small"
                      >
                        {activeStep === steps.length - 1 ? finalActionLabel : 'Next'}
                      </ActionButton>
                    </div>
                    
                    {activeStep !== 0 && activeStep !== steps.length - 1 && (
                      <ActionButton
                        variant="text"
                        onClick={handleReset}
                        startIcon={<RestartAltIcon />}
                        size="small"
                      >
                        Reset
                      </ActionButton>
                    )}
                  </Box>
                </StepContent>
              </Step>
            ))}
          </StyledStepper>
        ) : (
          <>
            <StyledStepper activeStep={activeStep} alternativeLabel>
              {steps.map((step, index) => (
                <Step key={index} completed={isStepComplete(index)}>
                  <StepButton 
                    onClick={handleStep(index)}
                    error={!isStepValid(index)}
                    disabled={disableNavigation}
                  >
                    <StepLabel
                      error={!isStepValid(index)}
                      optional={
                        step.optional ? 
                          <Typography variant="caption">Optional</Typography> : 
                          undefined
                      }
                    >
                      {step.label}
                    </StepLabel>
                  </StepButton>
                </Step>
              ))}
            </StyledStepper>
            
            <Box sx={{ mt: 3, p: 2, border: `1px solid ${theme.palette.divider}`, borderRadius: 1 }}>
              {showStepDescription && steps[activeStep].description && (
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {steps[activeStep].description}
                </Typography>
              )}
              
              {validationErrors[activeStep] && (
                <Typography 
                  variant="body2" 
                  color="error" 
                  sx={{ 
                    mb: 2,
                    p: 1,
                    borderRadius: 1,
                    backgroundColor: alpha(theme.palette.error.main, 0.05)
                  }}
                >
                  {validationErrors[activeStep]}
                </Typography>
              )}
              
              {steps[activeStep].content}
              
              {showTips && steps[activeStep].tip && (
                <Box 
                  sx={{ 
                    mt: 2,
                    p: 1.5,
                    borderRadius: 1,
                    display: 'flex',
                    backgroundColor: alpha(theme.palette.info.main, 0.05),
                    border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                  }}
                >
                  <LightbulbIcon color="info" sx={{ mr: 1, fontSize: 20 }} />
                  <Typography variant="body2" color="text.secondary">
                    <Typography component="span" variant="body2" fontWeight="medium" color="info.main">
                      Tip:
                    </Typography>{' '}
                    {steps[activeStep].tip}
                  </Typography>
                </Box>
              )}
              
              <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2, justifyContent: 'space-between' }}>
                <div>
                  <ActionButton
                    disabled={activeStep === 0}
                    onClick={handleBack}
                    startIcon={<NavigateBeforeIcon />}
                    variant="outlined"
                    size="small"
                  >
                    Back
                  </ActionButton>
                  <ActionButton
                    variant="contained"
                    onClick={handleNext}
                    startIcon={activeStep === steps.length - 1 ? finalActionIcon : <NavigateNextIcon />}
                    isNext
                    size="small"
                  >
                    {activeStep === steps.length - 1 ? finalActionLabel : 'Next'}
                  </ActionButton>
                </div>
                
                {activeStep !== 0 && activeStep !== steps.length - 1 && (
                  <ActionButton
                    variant="text"
                    onClick={handleReset}
                    startIcon={<RestartAltIcon />}
                    size="small"
                  >
                    Reset
                  </ActionButton>
                )}
              </Box>
            </Box>
          </>
        )}

        {activeStep === steps.length && (
          <Paper square elevation={0} sx={{ p: 3, borderRadius: 1, mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Analysis Complete!
            </Typography>
            <Typography variant="body1" paragraph>
              All steps have been completed. You can now view your results.
            </Typography>
            <Button onClick={handleReset} variant="outlined" startIcon={<RestartAltIcon />}>
              Start a New Analysis
            </Button>
          </Paper>
        )}
      </Box>
    );
  };

  // Handle variant-specific styling
  let containerSx = {};
  
  switch (variant) {
    case 'outlined':
      containerSx = {
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: 2,
        padding: 2,
        backgroundColor: theme.palette.background.paper,
      };
      break;
    case 'navigation':
      containerSx = {
        backgroundColor: alpha(theme.palette.primary.main, 0.03),
        borderRadius: 2,
        padding: 2,
      };
      break;
    default:
      // Default variant has no special styling
      break;
  }

  return (
    <Box sx={containerSx}>
      {variant === 'navigation' && (
        <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
          <Typography variant="subtitle1" fontWeight="medium">
            Analysis Setup
          </Typography>
          <Divider sx={{ flex: 1, ml: 2 }} />
        </Box>
      )}
      
      {renderStepperContent()}
    </Box>
  );
};

export default AnalysisSteps;