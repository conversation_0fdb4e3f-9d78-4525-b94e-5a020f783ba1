import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  useTheme,
  alpha,
  Divider,
  CircularProgress,
  Collapse,
  IconButton,
  Tooltip,
  <PERSON>ge,
  <PERSON>m,
  Fade
} from '@mui/material';
import {
  Science as ScienceIcon,
  BarChart as BarChartIcon,
  Timeline as TimelineIcon,
  Functions as FunctionsIcon,
  CompareArrows as CompareArrowsIcon,
  Check as CheckIcon,
  ArrowForward as ArrowForwardIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  Lightbulb as LightbulbIcon,
  RestartAlt as RestartAltIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';

// Analysis type enum
export enum AnalysisType {
  DESCRIPTIVE = 'descriptive',
  TTEST = 'ttest',
  CORRELATION = 'correlation',
  REGRESSION = 'regression',
  ANOVA = 'anova',
  NONPARAMETRIC = 'nonparametric'
}

// Interface for analysis info
interface AnalysisInfo {
  type: AnalysisType;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  sampleSize: string;
  statisticalPower: string;
  prerequisites: string[];
  route: string;
}

// Props interface
interface AnalysisWizardProps {
  onAnalysisSelect?: (type: AnalysisType, route: string) => void;
  variant?: 'full' | 'compact';
}

const AnalysisWizard: React.FC<AnalysisWizardProps> = ({ 
  onAnalysisSelect,
  variant = 'full'
}) => {
  const { datasets, activeDataset } = useData();
  const theme = useTheme();
  
  // State
  const [activeStep, setActiveStep] = useState(0);
  const [selectedAnalysis, setSelectedAnalysis] = useState<AnalysisInfo | null>(null);
  const [showTip, setShowTip] = useState(true);
  const [loading, setLoading] = useState(false);
  
  // Analysis definitions
  const analyses: AnalysisInfo[] = [
    {
      type: AnalysisType.DESCRIPTIVE,
      title: 'Descriptive Statistics',
      description: 'Explore and summarize your data with measures of central tendency, dispersion, and distribution.',
      icon: <FunctionsIcon fontSize="large" />,
      color: theme.palette.primary.main,
      sampleSize: 'Any',
      statisticalPower: 'N/A',
      prerequisites: ['At least one variable'],
      route: '/stats/descriptives'
    },
    {
      type: AnalysisType.TTEST,
      title: 't-Tests',
      description: 'Compare means between groups to determine if differences are statistically significant.',
      icon: <CompareArrowsIcon fontSize="large" />,
      color: theme.palette.secondary.main,
      sampleSize: '20+ per group',
      statisticalPower: 'Medium to high',
      prerequisites: ['Numeric dependent variable', 'Normally distributed data', 'Categorical grouping variable (for independent t-test)'],
      route: '/inference/ttest'
    },
    {
      type: AnalysisType.CORRELATION,
      title: 'Correlation Analysis',
      description: 'Measure the strength and direction of relationships between variables.',
      icon: <TimelineIcon fontSize="large" />,
      color: theme.palette.info.main,
      sampleSize: '30+',
      statisticalPower: 'Medium',
      prerequisites: ['At least two numeric variables', 'Linear relationship', 'No significant outliers'],
      route: '/correlation/pearson'
    },
    {
      type: AnalysisType.REGRESSION,
      title: 'Regression Analysis',
      description: 'Model the relationship between variables and predict outcomes.',
      icon: <TimelineIcon fontSize="large" />,
      color: theme.palette.success.main,
      sampleSize: '50+',
      statisticalPower: 'High',
      prerequisites: ['Numeric dependent variable', 'One or more predictor variables', 'Linear relationship (for linear regression)'],
      route: '/correlation/linear'
    },
    {
      type: AnalysisType.ANOVA,
      title: 'ANOVA',
      description: 'Compare means across multiple groups to identify significant differences.',
      icon: <BarChartIcon fontSize="large" />,
      color: theme.palette.warning.main,
      sampleSize: '30+ per group',
      statisticalPower: 'Medium to high',
      prerequisites: ['Numeric dependent variable', 'Categorical factor with 3+ groups', 'Normally distributed data', 'Homogeneity of variances'],
      route: '/inference/anova'
    },
    {
      type: AnalysisType.NONPARAMETRIC,
      title: 'Non-parametric Tests',
      description: 'Alternative statistical methods when parametric assumptions are not met.',
      icon: <ScienceIcon fontSize="large" />,
      color: theme.palette.error.main,
      sampleSize: 'Varies by test',
      statisticalPower: 'Lower than parametric',
      prerequisites: ['Non-normal data or ordinal variables', 'Small sample size or outliers'],
      route: '/inference/nonparametric'
    }
  ];
  
  // Steps for the wizard
  const steps = ['Choose Analysis', 'Review Requirements', 'Start Analysis'];
  
  // Handle analysis selection
  const handleSelectAnalysis = (analysis: AnalysisInfo) => {
    setSelectedAnalysis(analysis);
    setActiveStep(1);
  };
  
  // Handle back button
  const handleBack = () => {
    setActiveStep(activeStep - 1);
  };
  
  // Handle next button
  const handleNext = () => {
    if (activeStep === steps.length - 1) {
      // Final step - start the analysis
      if (selectedAnalysis && onAnalysisSelect) {
        setLoading(true);
        // Simulate loading for better UX
        setTimeout(() => {
          onAnalysisSelect(selectedAnalysis.type, selectedAnalysis.route);
          setLoading(false);
        }, 800);
      }
    } else {
      // Move to next step
      setActiveStep(activeStep + 1);
    }
  };
  
  // Handle restart
  const handleRestart = () => {
    setActiveStep(0);
    setSelectedAnalysis(null);
  };
  
  // Check if data requirements are met
  const checkRequirements = (): boolean => {
    if (!activeDataset || !selectedAnalysis) return false;
    
    const hasNumericVariables = activeDataset.columns.some((col: any) => col.type === 'numeric');
    const hasCategoricalVariables = activeDataset.columns.some((col: any) => col.type === 'categorical');
    
    switch (selectedAnalysis.type) {
      case AnalysisType.DESCRIPTIVE:
        return activeDataset.columns.length > 0;
      case AnalysisType.TTEST:
        return hasNumericVariables && hasCategoricalVariables;
      case AnalysisType.CORRELATION:
        // Need at least two numeric variables
        return activeDataset.columns.filter((col: any) => col.type === 'numeric').length >= 2;
      case AnalysisType.REGRESSION:
        // Need at least one numeric dependent variable and one predictor
        return activeDataset.columns.filter((col: any) => col.type === 'numeric').length >= 2;
      case AnalysisType.ANOVA:
        // Need numeric dependent and categorical with 3+ levels
        if (!hasNumericVariables) return false;
        // Check if any categorical variable has 3+ levels
        return activeDataset.columns.some((col: any) => {
          if (col.type === 'categorical') {
            const uniqueValues = new Set(activeDataset.data.map((row: any) => row[col.name]));
            return uniqueValues.size >= 3;
          }
          return false;
        });
      case AnalysisType.NONPARAMETRIC:
        // Most non-parametric tests need at least one categorical and one numeric/ordinal
        return hasNumericVariables && hasCategoricalVariables;
      default:
        return false;
    }
  };
  
  // Render analysis cards for selection
  const renderAnalysisCards = () => {
    return (
      <Grid container spacing={3}>
        {analyses.map((analysis) => (
          <Grid item xs={12} sm={6} md={4} key={analysis.type}>
            <Card 
              sx={{ 
                height: '100%',
                transition: 'all 0.2s',
                border: selectedAnalysis?.type === analysis.type ? `2px solid ${analysis.color}` : '1px solid rgba(0,0,0,0.12)',
                '&:hover': {
                  boxShadow: theme.shadows[4],
                  transform: 'translateY(-4px)'
                }
              }}
            >
              <CardActionArea 
                sx={{ height: '100%' }}
                onClick={() => handleSelectAnalysis(analysis)}
              >
                <CardContent>
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    mb: 1.5,
                    color: analysis.color 
                  }}>
                    {React.cloneElement(analysis.icon as React.ReactElement, { style: { color: analysis.color } })}
                    <Typography variant="h6" component="div" sx={{ ml: 1, fontWeight: 500 }}>
                      {analysis.title}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1.5 }}>
                    {analysis.description}
                  </Typography>
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between',
                    mt: 'auto'
                  }}>
                    <Typography variant="caption" color="text.secondary">
                      Sample size: <b>{analysis.sampleSize}</b>
                    </Typography>
                    <ArrowForwardIcon fontSize="small" sx={{ color: analysis.color }} />
                  </Box>
                </CardContent>
              </CardActionArea>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };
  
  // Render analysis requirements
  const renderRequirements = () => {
    if (!selectedAnalysis) return null;
    
    const requirementsMet = checkRequirements();
    
    return (
      <Box sx={{ mt: 2 }}>
        <Paper sx={{ p: 3, mb: 3, bgcolor: alpha(selectedAnalysis.color, 0.05) }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ 
              bgcolor: alpha(selectedAnalysis.color, 0.1), 
              borderRadius: '50%',
              p: 1,
              display: 'flex',
              mr: 2
            }}>
              {React.cloneElement(selectedAnalysis.icon as React.ReactElement, { 
                style: { color: selectedAnalysis.color } 
              })}
            </Box>
            <Box>
              <Typography variant="h6" component="div">
                {selectedAnalysis.title}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {selectedAnalysis.description}
              </Typography>
            </Box>
          </Box>
          
          <Divider sx={{ my: 2 }} />
          
          <Typography variant="subtitle1" gutterBottom>
            Analysis Requirements
          </Typography>
          
          <Box sx={{ mb: 2 }}>
            {selectedAnalysis.prerequisites.map((req, index) => (
              <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CheckIcon fontSize="small" sx={{ mr: 1, color: theme.palette.success.main }} />
                <Typography variant="body2">
                  {req}
                </Typography>
              </Box>
            ))}
          </Box>
          
          <Divider sx={{ my: 2 }} />
          
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                Recommended Sample Size
              </Typography>
              <Typography variant="body2">
                {selectedAnalysis.sampleSize}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                Statistical Power
              </Typography>
              <Typography variant="body2">
                {selectedAnalysis.statisticalPower}
              </Typography>
            </Grid>
          </Grid>
          
          <Box sx={{ mt: 3, p: 2, bgcolor: requirementsMet ? alpha(theme.palette.success.main, 0.1) : alpha(theme.palette.warning.main, 0.1), borderRadius: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {requirementsMet ? (
                <CheckIcon sx={{ color: theme.palette.success.main, mr: 1 }} />
              ) : (
                <InfoIcon sx={{ color: theme.palette.warning.main, mr: 1 }} />
              )}
              <Typography variant="body2" fontWeight="medium">
                {requirementsMet 
                  ? 'Your data meets the basic requirements for this analysis' 
                  : 'Your data may not meet all requirements for this analysis'}
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Box>
    );
  };
  
  // Render confirmation step
  const renderConfirmation = () => {
    if (!selectedAnalysis) return null;
    
    return (
      <Box sx={{ mt: 2 }}>
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Box sx={{ 
            width: 70, 
            height: 70, 
            borderRadius: '50%', 
            bgcolor: alpha(selectedAnalysis.color, 0.1),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto',
            mb: 2
          }}>
            {React.cloneElement(selectedAnalysis.icon as React.ReactElement, { 
              style: { color: selectedAnalysis.color, fontSize: 40 } 
            })}
          </Box>
          
          <Typography variant="h5" gutterBottom>
            Ready to start {selectedAnalysis.title}
          </Typography>
          
          <Typography variant="body1" color="text.secondary" paragraph>
            You'll be guided through the process with step-by-step instructions
          </Typography>
          
          <Box sx={{ mt: 3 }}>
            {loading ? (
              <CircularProgress size={36} />
            ) : (
              <Button
                variant="contained"
                size="large"
                onClick={handleNext}
                sx={{ 
                  px: 4, 
                  py: 1.5,
                  bgcolor: selectedAnalysis.color,
                  '&:hover': {
                    bgcolor: alpha(selectedAnalysis.color, 0.8)
                  }
                }}
              >
                Start Analysis
              </Button>
            )}
          </Box>
        </Paper>
      </Box>
    );
  };
  
  // Render correct content based on active step
  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return renderAnalysisCards();
      case 1:
        return renderRequirements();
      case 2:
        return renderConfirmation();
      default:
        return 'Unknown step';
    }
  };
  
  // If variant is compact, render simplified version
  if (variant === 'compact') {
    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Quick Analysis
        </Typography>
        
        <Grid container spacing={2}>
          {analyses.slice(0, 3).map((analysis) => (
            <Grid item xs={12} sm={4} key={analysis.type}>
              <Card 
                sx={{ 
                  transition: 'all 0.2s',
                  border: '1px solid rgba(0,0,0,0.12)',
                  '&:hover': {
                    boxShadow: theme.shadows[3],
                    borderColor: analysis.color
                  }
                }}
              >
                <CardActionArea 
                  onClick={() => onAnalysisSelect?.(analysis.type, analysis.route)}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center',
                      color: analysis.color 
                    }}>
                      {React.cloneElement(analysis.icon as React.ReactElement, { 
                        style: { color: analysis.color, fontSize: 20 } 
                      })}
                      <Typography variant="subtitle2" component="div" sx={{ ml: 1, fontWeight: 500 }}>
                        {analysis.title}
                      </Typography>
                    </Box>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }
  
  // Main full variant render
  return (
    <Box sx={{ width: '100%', mb: 4 }}>
      <Paper sx={{ p: 3, mb: 2 }}>
        <Typography variant="h5" gutterBottom>
          Analysis Wizard
        </Typography>
        
        <Typography variant="body2" color="text.secondary" paragraph>
          Follow these steps to select and configure the right statistical analysis for your data.
        </Typography>
        
        <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        {showTip && (
          <Fade in={showTip}>
            <Box 
              sx={{ 
                p: 2, 
                bgcolor: alpha(theme.palette.info.main, 0.05), 
                borderRadius: 2,
                mb: 3,
                position: 'relative',
                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                <LightbulbIcon sx={{ color: theme.palette.info.main, mr: 1.5, mt: 0.5 }} />
                <Box>
                  <Typography variant="subtitle2" sx={{ color: theme.palette.info.main, mb: 0.5 }}>
                    Choosing the Right Analysis
                  </Typography>
                  <Typography variant="body2">
                    The type of analysis you need depends on your research question and data. Descriptive statistics are great for summarizing data, while t-tests and ANOVA help compare groups. Correlation and regression examine relationships between variables.
                  </Typography>
                </Box>
                <IconButton 
                  size="small" 
                  onClick={() => setShowTip(false)}
                  sx={{ ml: 'auto' }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>
          </Fade>
        )}
        
        {getStepContent(activeStep)}
        
        <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
          <Button
            color="inherit"
            disabled={activeStep === 0}
            onClick={handleBack}
            sx={{ mr: 1 }}
          >
            Back
          </Button>
          <Box sx={{ flex: '1 1 auto' }} />
          {activeStep !== steps.length - 1 && (
            <Button
              variant="contained"
              onClick={handleNext}
              disabled={activeStep === 1 && !selectedAnalysis}
            >
              Next
            </Button>
          )}
          {activeStep !== 0 && (
            <Button
              color="inherit"
              sx={{ ml: 1 }}
              startIcon={<RestartAltIcon />}
              onClick={handleRestart}
            >
              Restart
            </Button>
          )}
        </Box>
      </Paper>
      
      <Box 
        sx={{ 
          p: 2, 
          bgcolor: alpha(theme.palette.secondary.main, 0.05), 
          borderRadius: 2,
          display: 'flex',
          alignItems: 'center',
          border: `1px solid ${alpha(theme.palette.secondary.main, 0.2)}`
        }}
      >
        <SchoolIcon sx={{ color: theme.palette.secondary.main, mr: 1.5 }} />
        <Box>
          <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>
            Need help choosing?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Learn more about different types of statistical analyses in our learning center.
          </Typography>
        </Box>
        <Button 
          size="small" 
          variant="outlined" 
          color="secondary"
          sx={{ ml: 'auto' }}
          onClick={() => window.open('#/help/tutorials', '_blank')}
        >
          Tutorial
        </Button>
      </Box>
    </Box>
  );
};

export default AnalysisWizard;