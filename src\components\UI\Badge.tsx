import React from 'react';
import { Box, BoxProps, styled, Theme } from '@mui/material';

type BadgeVariant = 'filled' | 'outlined' | 'light';
type BadgeColor = 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'default';
type BadgeSize = 'small' | 'medium' | 'large';

interface BadgeProps extends BoxProps {
  label: string;
  color?: BadgeColor;
  variant?: BadgeVariant;
  size?: BadgeSize;
  icon?: React.ReactNode;
}

const getColorStyles = (color: BadgeColor, variant: BadgeVariant, theme: Theme) => {
  const mainColor = color === 'default' 
    ? theme.palette.grey[500] 
    : theme.palette[color]?.main;
  
  const lightColor = color === 'default'
    ? theme.palette.grey[200]
    : theme.palette[color]?.light;
  
  const darkColor = color === 'default'
    ? theme.palette.grey[700]
    : theme.palette[color]?.dark;
  
  const contrastText = color === 'default'
    ? theme.palette.getContrastText(theme.palette.grey[500])
    : theme.palette[color]?.contrastText;
  
  switch (variant) {
    case 'outlined':
      return {
        color: mainColor,
        backgroundColor: 'transparent',
        border: `1px solid ${mainColor}`,
      };
    case 'light':
      return {
        color: darkColor,
        backgroundColor: lightColor,
        border: 'none',
      };
    case 'filled':
    default:
      return {
        color: contrastText,
        backgroundColor: mainColor,
        border: 'none',
      };
  }
};

const StyledBadge = styled(Box, {
  // Correctly filter out the custom props being passed
  shouldForwardProp: (prop) => !['badgeColor', 'badgeVariant', 'badgeSize'].includes(prop as string), 
})<{ 
  badgeColor: BadgeColor;
  badgeVariant: BadgeVariant;
  badgeSize: BadgeSize;
}>(({ theme, badgeColor, badgeVariant, badgeSize }) => {
  const colorStyles = getColorStyles(badgeColor, badgeVariant, theme);
  
  const sizeStyles = {
    small: {
      fontSize: '0.75rem',
      padding: '0.125rem 0.5rem',
      borderRadius: '4px',
    },
    medium: {
      fontSize: '0.875rem',
      padding: '0.25rem 0.75rem',
      borderRadius: '6px',
    },
    large: {
      fontSize: '1rem',
      padding: '0.375rem 1rem',
      borderRadius: '8px',
    },
  }[badgeSize];
  
  return {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontWeight: 500,
    lineHeight: 1.5,
    whiteSpace: 'nowrap',
    ...colorStyles,
    ...sizeStyles,
  };
});

const Badge: React.FC<BadgeProps> = ({
  label,
  color = 'default',
  variant = 'filled',
  size = 'medium',
  icon,
  sx,
  ...other
}) => {
  return (
    <StyledBadge
      badgeColor={color}
      badgeVariant={variant}
      badgeSize={size}
      sx={sx}
      {...other}
    >
      {icon && (
        <Box 
          component="span" 
          sx={{ 
            display: 'flex',
            mr: 0.5,
            '& svg': {
              fontSize: size === 'small' ? '0.875rem' : size === 'medium' ? '1rem' : '1.25rem',
            },
          }}
        >
          {icon}
        </Box>
      )}
      {label}
    </StyledBadge>
  );
};

export default Badge;
