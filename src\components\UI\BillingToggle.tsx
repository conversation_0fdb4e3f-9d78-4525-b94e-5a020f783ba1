import React from 'react';
import { Box, But<PERSON>, Chip } from '@mui/material';

interface BillingToggleProps {
  value: 'monthly' | 'annual';
  onChange: (value: 'monthly' | 'annual') => void;
  showSavings?: boolean;
  savingsText?: string;
}

const BillingToggle: React.FC<BillingToggleProps> = ({
  value,
  onChange,
  showSavings = true,
  savingsText = 'Save 20%'
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        bgcolor: 'background.paper',
        borderRadius: 3,
        p: 0.5,
        border: 1,
        borderColor: 'divider',
        boxShadow: 1
      }}
    >
      <Button
        variant={value === 'monthly' ? 'contained' : 'text'}
        onClick={() => onChange('monthly')}
        sx={{
          borderRadius: 2,
          px: 3,
          py: 1,
          textTransform: 'none',
          fontWeight: value === 'monthly' ? 'bold' : 'normal'
        }}
      >
        Monthly
      </Button>
      <Button
        variant={value === 'annual' ? 'contained' : 'text'}
        onClick={() => onChange('annual')}
        sx={{
          borderRadius: 2,
          px: 3,
          py: 1,
          textTransform: 'none',
          fontWeight: value === 'annual' ? 'bold' : 'normal',
          position: 'relative'
        }}
      >
        Annual
        {showSavings && (
          <Chip
            label={savingsText}
            size="small"
            color="success"
            sx={{
              position: 'absolute',
              top: -8,
              right: -8,
              fontSize: '0.7rem',
              height: 18
            }}
          />
        )}
      </Button>
    </Box>
  );
};

export default BillingToggle;
