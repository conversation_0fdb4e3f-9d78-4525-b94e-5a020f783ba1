import React from 'react';
import { 
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  ButtonProps as MuiButtonProps, 
  styled,
  alpha,
  CircularProgress 
} from '@mui/material';

interface ButtonProps extends MuiButtonProps {
  isLoading?: boolean;
  rounded?: boolean;
  gradient?: boolean;
  fullWidth?: boolean;
}

const StyledButton = styled(MuiButton, {
  shouldForwardProp: (prop) => prop !== 'rounded' && prop !== 'gradient',
})<ButtonProps>(({ theme, rounded, gradient, color = 'primary' }) => {
  const mainColor = theme.palette[color]?.main || theme.palette.primary.main;
  const contrastText = theme.palette[color]?.contrastText || '#ffffff';
  
  return {
    fontWeight: 500,
    textTransform: 'none',
    boxShadow: 'none',
    position: 'relative',
    
    // Rounded style
    ...(rounded && {
      borderRadius: '50px',
      paddingLeft: theme.spacing(3),
      paddingRight: theme.spacing(3),
    }),
    
    // Gradient style for contained variant
    ...(gradient && {
      background: `linear-gradient(135deg, ${mainColor} 0%, ${alpha(mainColor, 0.8)} 100%)`,
      color: contrastText,
      '&:hover': {
        background: `linear-gradient(135deg, ${mainColor} 20%, ${alpha(mainColor, 0.9)} 100%)`,
      },
    }),
    
    // General hover effect
    '&:hover': {
      boxShadow: '0 3px 6px rgba(0, 0, 0, 0.1)',
      transform: 'translateY(-1px)',
    },
    
    '&:active': {
      boxShadow: 'none',
      transform: 'translateY(0)',
    },
    
    // Disable transform effect when disabled
    '&.Mui-disabled': {
      transform: 'none',
    },
  };
});

const Button: React.FC<ButtonProps> = ({
  children,
  isLoading = false,
  disabled = false,
  rounded = false,
  gradient = false,
  color = 'primary',
  startIcon,
  ...rest
}) => {
  return (
    <StyledButton
      disabled={disabled || isLoading}
      rounded={rounded}
      gradient={gradient}
      color={color}
      startIcon={isLoading ? null : startIcon}
      {...rest}
    >
      {isLoading && (
        <CircularProgress
          size={20}
          color="inherit"
          sx={{
            position: 'absolute',
            left: '50%',
            marginLeft: '-10px',
          }}
        />
      )}
      <span style={{ visibility: isLoading ? 'hidden' : 'visible' }}>
        {children}
      </span>
    </StyledButton>
  );
};

export default Button;