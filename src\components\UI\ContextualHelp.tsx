import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Paper,
  <PERSON><PERSON>se,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  IconButton,
  Fade,
  Zoom,
  Tooltip,
  useTheme,
  alpha,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Help as HelpIcon,
  HelpOutline as HelpOutlineIcon,
  Lightbulb as LightbulbIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  School as SchoolIcon,
  Close as CloseIcon,
  PlayCircleOutline as PlayCircleOutlineIcon,
  KeyboardArrowRight as KeyboardArrowRightIcon,
  DataUsage as DataUsageIcon,
  BarChart as BarChartIcon,
  WarningAmber as WarningAmberIcon,
  EventNote as EventNoteIcon,
  FormatQuote as FormatQuoteIcon
} from '@mui/icons-material';

export interface HelpItem {
  title: string;
  content: React.ReactNode;
  type?: 'info' | 'tip' | 'warning' | 'note' | 'quote' | 'example';
  icon?: React.ReactNode;
}

export interface ContextualHelpProps {
  items: HelpItem[];
  title?: string;
  initiallyExpanded?: boolean;
  variant?: 'panel' | 'accordion' | 'inline' | 'tooltip' | 'dialog';
  placement?: 'left' | 'right' | 'top' | 'bottom' | 'inline';
  triggerIcon?: React.ReactNode;
  triggerText?: string;
  maxItems?: number;
  collapsible?: boolean;
  showIcons?: boolean;
  width?: string | number;
  dense?: boolean;
}

const ContextualHelp: React.FC<ContextualHelpProps> = ({
  items,
  title = 'Help & Tips',
  initiallyExpanded = false,
  variant = 'panel',
  placement = 'inline',
  triggerIcon = <HelpOutlineIcon />,
  triggerText = 'Help',
  maxItems = 3,
  collapsible = true,
  showIcons = true,
  width = 'auto',
  dense = false
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(initiallyExpanded);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(false);
  
  // Handle expand/collapse
  const toggleExpand = () => {
    setExpanded(!expanded);
  };
  
  // Handle dialog open/close
  const toggleDialog = () => {
    setDialogOpen(!dialogOpen);
  };
  
  // Handle accordion change
  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAccordion(isExpanded ? panel : false);
  };
  
  // Get icon for item type
  const getIcon = (type?: string, customIcon?: React.ReactNode) => {
    if (customIcon) return customIcon;
    
    switch (type) {
      case 'info':
        return <InfoIcon color="info" />;
      case 'tip':
        return <LightbulbIcon color="warning" />;
      case 'warning':
        return <WarningAmberIcon color="error" />;
      case 'note':
        return <EventNoteIcon color="action" />;
      case 'quote':
        return <FormatQuoteIcon color="secondary" />;
      case 'example':
        return <DataUsageIcon color="primary" />;
      default:
        return <InfoIcon color="info" />;
    }
  };
  
  // Get background color for item type
  const getBackgroundColor = (type?: string) => {
    switch (type) {
      case 'info':
        return alpha(theme.palette.info.main, 0.05);
      case 'tip':
        return alpha(theme.palette.warning.main, 0.05);
      case 'warning':
        return alpha(theme.palette.error.main, 0.05);
      case 'note':
        return alpha(theme.palette.grey[500], 0.05);
      case 'quote':
        return alpha(theme.palette.secondary.main, 0.05);
      case 'example':
        return alpha(theme.palette.primary.main, 0.05);
      default:
        return alpha(theme.palette.info.main, 0.05);
    }
  };
  
  // Get border color for item type
  const getBorderColor = (type?: string) => {
    switch (type) {
      case 'info':
        return alpha(theme.palette.info.main, 0.2);
      case 'tip':
        return alpha(theme.palette.warning.main, 0.2);
      case 'warning':
        return alpha(theme.palette.error.main, 0.2);
      case 'note':
        return alpha(theme.palette.grey[500], 0.2);
      case 'quote':
        return alpha(theme.palette.secondary.main, 0.2);
      case 'example':
        return alpha(theme.palette.primary.main, 0.2);
      default:
        return alpha(theme.palette.info.main, 0.2);
    }
  };
  
  // Render help items in panel format
  const renderPanel = () => {
    const displayItems = expanded ? items : items.slice(0, maxItems);
    
    return (
      <Paper 
        sx={{ 
          p: dense ? 1.5 : 2, 
          width,
          boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
          border: `1px solid ${theme.palette.divider}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <HelpIcon fontSize="small" sx={{ mr: 1, color: theme.palette.primary.main }} />
          <Typography variant="subtitle1" fontWeight="medium">
            {title}
          </Typography>
          {collapsible && (
            <IconButton 
              size="small" 
              onClick={toggleExpand} 
              sx={{ ml: 'auto' }}
              aria-label={expanded ? "Collapse help" : "Expand help"}
            >
              <ExpandMoreIcon sx={{ 
                transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                transition: 'transform 0.3s'
              }} />
            </IconButton>
          )}
        </Box>
        
        <Divider sx={{ mb: 2 }} />
        
        <Box sx={{ mb: 1 }}>
          {displayItems.map((item, index) => (
            <Box 
              key={index} 
              sx={{ 
                mb: 2,
                p: dense ? 1 : 1.5,
                borderRadius: 1,
                backgroundColor: getBackgroundColor(item.type),
                border: `1px solid ${getBorderColor(item.type)}`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                {showIcons && (
                  <Box sx={{ mr: 1.5, mt: 0.25 }}>
                    {getIcon(item.type, item.icon)}
                  </Box>
                )}
                <Box>
                  <Typography variant={dense ? "body2" : "subtitle2"} gutterBottom>
                    {item.title}
                  </Typography>
                  <Box sx={{ color: theme.palette.text.secondary, fontSize: theme.typography.body2.fontSize }}>
                    {item.content}
                  </Box>
                </Box>
              </Box>
            </Box>
          ))}
        </Box>
        
        {items.length > maxItems && !expanded && (
          <Button 
            size="small" 
            onClick={toggleExpand}
            endIcon={<KeyboardArrowRightIcon />}
          >
            Show {items.length - maxItems} more
          </Button>
        )}
      </Paper>
    );
  };
  
  // Render help items in accordion format
  const renderAccordion = () => {
    return (
      <Box sx={{ width }}>
        <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
          <HelpIcon fontSize="small" sx={{ mr: 1, color: theme.palette.primary.main }} />
          {title}
        </Typography>
        
        {items.map((item, index) => (
          <Accordion
            key={index}
            expanded={expandedAccordion === `panel-${index}`}
            onChange={handleAccordionChange(`panel-${index}`)}
            sx={{ 
              mb: 1,
              boxShadow: 'none',
              '&:before': { display: 'none' },
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: '4px !important',
              overflow: 'hidden'
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{ 
                backgroundColor: expandedAccordion === `panel-${index}` 
                  ? getBackgroundColor(item.type)
                  : alpha(theme.palette.background.default, 0.5)
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {showIcons && (
                  <Box sx={{ mr: 1.5, display: 'flex', alignItems: 'center' }}>
                    {getIcon(item.type, item.icon)}
                  </Box>
                )}
                <Typography variant="subtitle2">
                  {item.title}
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails sx={{ backgroundColor: getBackgroundColor(item.type), pt: 0 }}>
              <Box sx={{ color: theme.palette.text.secondary, fontSize: theme.typography.body2.fontSize }}>
                {item.content}
              </Box>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    );
  };
  
  // Render help items in inline format
  const renderInline = () => {
    return (
      <Box sx={{ width, mb: 2 }}>
        {items.map((item, index) => (
          <Alert 
            key={index}
            severity={
              item.type === 'warning' ? 'error' :
              item.type === 'tip' ? 'warning' :
              item.type === 'info' ? 'info' : 'info'
            }
            sx={{ mb: 2 }}
            icon={showIcons ? getIcon(item.type, item.icon) : undefined}
          >
            <AlertTitle>{item.title}</AlertTitle>
            {item.content}
          </Alert>
        ))}
      </Box>
    );
  };
  
  // Render tooltip
  const renderTooltip = () => {
    return (
      <Box sx={{ display: 'inline-block' }}>
        <Tooltip
          title={
            <Box sx={{ maxWidth: 300 }}>
              <Typography variant="subtitle2" gutterBottom>
                {title}
              </Typography>
              <Divider sx={{ my: 1 }} />
              {items.map((item, index) => (
                <Box key={index} sx={{ mb: index < items.length - 1 ? 1.5 : 0, mt: 1 }}>
                  <Typography variant="body2" fontWeight="medium" gutterBottom>
                    {item.title}
                  </Typography>
                  <Box sx={{ fontSize: theme.typography.caption.fontSize }}>
                    {item.content}
                  </Box>
                </Box>
              ))}
            </Box>
          }
          arrow
          placement="top"
        >
          <IconButton color="primary" size="small" aria-label="Help">
            {triggerIcon}
          </IconButton>
        </Tooltip>
      </Box>
    );
  };
  
  // Render dialog
  const renderDialog = () => {
    return (
      <>
        <Button
          startIcon={triggerIcon}
          size="small"
          variant="outlined"
          onClick={toggleDialog}
        >
          {triggerText}
        </Button>
        
        <Dialog 
          open={dialogOpen} 
          onClose={toggleDialog}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ display: 'flex', alignItems: 'center' }}>
            <HelpIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
            {title}
            <IconButton
              aria-label="close"
              onClick={toggleDialog}
              sx={{ ml: 'auto' }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <List disablePadding>
              {items.map((item, index) => (
                <React.Fragment key={index}>
                  <ListItem
                    alignItems="flex-start"
                    sx={{ 
                      backgroundColor: getBackgroundColor(item.type),
                      mb: 1,
                      borderRadius: 1,
                      border: `1px solid ${getBorderColor(item.type)}`,
                      py: 1.5
                    }}
                  >
                    {showIcons && (
                      <ListItemIcon sx={{ mt: 0.5, minWidth: 40 }}>
                        {getIcon(item.type, item.icon)}
                      </ListItemIcon>
                    )}
                    <ListItemText
                      primary={item.title}
                      secondary={item.content}
                      primaryTypographyProps={{ fontWeight: 500 }}
                      secondaryTypographyProps={{ color: 'text.secondary' }}
                    />
                  </ListItem>
                </React.Fragment>
              ))}
            </List>
          </DialogContent>
          <DialogActions>
            <Button 
              onClick={toggleDialog} 
              variant="contained"
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </>
    );
  };
  
  // Render the appropriate variant
  const renderContent = () => {
    switch (variant) {
      case 'accordion':
        return renderAccordion();
      case 'inline':
        return renderInline();
      case 'tooltip':
        return renderTooltip();
      case 'dialog':
        return renderDialog();
      case 'panel':
      default:
        return renderPanel();
    }
  };
  
  // Position the help content based on placement
  if (variant === 'tooltip' || variant === 'dialog') {
    return renderContent();
  }
  
  return (
    <Box 
      sx={{ 
        width,
        float: placement === 'left' || placement === 'right' ? placement : undefined,
        mb: placement === 'top' || placement === 'inline' ? 2 : 0,
        mt: placement === 'bottom' ? 2 : 0,
        mr: placement === 'left' ? 2 : 0,
        ml: placement === 'right' ? 2 : 0,
        clear: placement === 'left' || placement === 'right' ? 'both' : undefined
      }}
    >
      {renderContent()}
    </Box>
  );
};

export default ContextualHelp;