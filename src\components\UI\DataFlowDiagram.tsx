import React from 'react';
import {
  Box,
  Typography,
  Paper,
  useTheme,
  alpha,
  Tooltip,
  IconButton,
  Button,
  Collapse,
  Divider
} from '@mui/material';
import {
  ArrowForward as ArrowForwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Storage as StorageIcon,
  BarChart as BarChartIcon,
  ImportExport as ImportExportIcon,
  TableChart as TableChartIcon,
  Functions as FunctionsIcon,
  Science as ScienceIcon,
  Timeline as TimelineIcon,
  CompareArrows as CompareArrowsIcon,
  Help as HelpIcon,
  Info as InfoIcon
} from '@mui/icons-material';

interface FlowStep {
  id: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
  color?: string;
  state?: 'active' | 'completed' | 'disabled' | 'warning' | 'error';
  onClick?: () => void;
}

interface FlowConnection {
  from: string;
  to: string;
  label?: string;
  direction?: 'horizontal' | 'vertical';
  state?: 'active' | 'disabled' | 'warning' | 'error';
}

export interface DataFlowDiagramProps {
  steps: FlowStep[];
  connections: FlowConnection[];
  title?: string;
  description?: string;
  variant?: 'boxed' | 'process' | 'timeline';
  showLabels?: boolean;
  showIcons?: boolean;
  compact?: boolean;
  interactive?: boolean;
  onClick?: (stepId: string) => void;
}

const DataFlowDiagram: React.FC<DataFlowDiagramProps> = ({
  steps,
  connections,
  title,
  description,
  variant = 'boxed',
  showLabels = true,
  showIcons = true,
  compact = false,
  interactive = true,
  onClick
}) => {
  const theme = useTheme();
  
  // Get icon based on step id pattern
  const getDefaultIcon = (id: string) => {
    if (id.includes('data') || id.includes('import')) {
      return <StorageIcon />;
    } else if (id.includes('transform') || id.includes('process')) {
      return <ImportExportIcon />;
    } else if (id.includes('table') || id.includes('view')) {
      return <TableChartIcon />;
    } else if (id.includes('stats') || id.includes('descriptive')) {
      return <FunctionsIcon />;
    } else if (id.includes('test') || id.includes('inferential')) {
      return <ScienceIcon />;
    } else if (id.includes('correlation') || id.includes('relation')) {
      return <CompareArrowsIcon />;
    } else if (id.includes('chart') || id.includes('plot') || id.includes('visual')) {
      return <BarChartIcon />;
    } else if (id.includes('result') || id.includes('output')) {
      return <TimelineIcon />;
    }
    return <InfoIcon />;
  };
  
  // Get color based on state
  const getColorForState = (state?: string, defaultColor?: string) => {
    if (defaultColor) return defaultColor;
    
    switch (state) {
      case 'active':
        return theme.palette.primary.main;
      case 'completed':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'error':
        return theme.palette.error.main;
      case 'disabled':
        return theme.palette.action.disabled;
      default:
        return theme.palette.grey[700];
    }
  };
  
  // Get background color based on state
  const getBackgroundForState = (state?: string, color?: string) => {
    const baseColor = color || getColorForState(state);
    
    switch (state) {
      case 'active':
        return alpha(baseColor, 0.1);
      case 'completed':
        return alpha(baseColor, 0.1);
      case 'warning':
        return alpha(baseColor, 0.1);
      case 'error':
        return alpha(baseColor, 0.1);
      case 'disabled':
        return alpha(theme.palette.action.disabled, 0.1);
      default:
        return alpha(theme.palette.grey[700], 0.05);
    }
  };
  
  // Handle step click
  const handleStepClick = (step: FlowStep) => {
    if (interactive && step.state !== 'disabled') {
      if (step.onClick) {
        step.onClick();
      } else if (onClick) {
        onClick(step.id);
      }
    }
  };
  
  // Render a connection between steps
  const renderConnection = (connection: FlowConnection) => {
    const arrowColor = getColorForState(connection.state);
    
    if (connection.direction === 'vertical') {
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            my: compact ? 0.5 : 1,
            position: 'relative',
            height: compact ? 16 : 24
          }}
        >
          <ArrowDownwardIcon
            fontSize={compact ? 'small' : 'medium'}
            sx={{ color: arrowColor }}
          />
          {connection.label && (
            <Typography
              variant="caption"
              sx={{
                position: 'absolute',
                right: -5,
                transform: 'translateX(100%)',
                color: theme.palette.text.secondary
              }}
            >
              {connection.label}
            </Typography>
          )}
        </Box>
      );
    }
    
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mx: compact ? 0.5 : 1,
          position: 'relative',
          width: compact ? 16 : 24
        }}
      >
        <ArrowForwardIcon
          fontSize={compact ? 'small' : 'medium'}
          sx={{ color: arrowColor }}
        />
        {connection.label && (
          <Typography
            variant="caption"
            sx={{
              position: 'absolute',
              bottom: -5,
              transform: 'translateY(100%)',
              color: theme.palette.text.secondary
            }}
          >
            {connection.label}
          </Typography>
        )}
      </Box>
    );
  };
  
  // Render steps based on variant
  const renderBoxedVariant = () => {
    // Group steps and connections into rows
    // This is a simplified version - in a real implementation,
    // you might want to use a library like ReactFlow for more complex diagrams
    
    // For this example, we'll just render all steps in a single row
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          flexWrap: 'wrap',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {steps.map((step, index) => {
          const isLast = index === steps.length - 1;
          const connection = connections.find(c => c.from === step.id);
          
          return (
            <React.Fragment key={step.id}>
              <Paper
                sx={{
                  p: compact ? 1 : 2,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center',
                  borderRadius: 1,
                  backgroundColor: getBackgroundForState(step.state, step.color),
                  border: `1px solid ${alpha(getColorForState(step.state, step.color), 0.3)}`,
                  width: compact ? 100 : 150,
                  cursor: interactive && step.state !== 'disabled' ? 'pointer' : 'default',
                  opacity: step.state === 'disabled' ? 0.6 : 1,
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: interactive && step.state !== 'disabled' ? theme.shadows[2] : 'none',
                    backgroundColor: interactive && step.state !== 'disabled' 
                      ? alpha(getColorForState(step.state, step.color), 0.15)
                      : getBackgroundForState(step.state, step.color)
                  }
                }}
                onClick={() => handleStepClick(step)}
              >
                {showIcons && (
                  <Box
                    sx={{
                      color: getColorForState(step.state, step.color),
                      mb: 1
                    }}
                  >
                    {step.icon || getDefaultIcon(step.id)}
                  </Box>
                )}
                <Typography
                  variant={compact ? 'body2' : 'subtitle2'}
                  sx={{
                    fontWeight: 500,
                    color: getColorForState(step.state, step.color)
                  }}
                >
                  {step.label}
                </Typography>
                {step.description && (
                  <Typography variant="caption" color="text.secondary">
                    {step.description}
                  </Typography>
                )}
              </Paper>
              
              {!isLast && connection && (
                renderConnection(connection)
              )}
            </React.Fragment>
          );
        })}
      </Box>
    );
  };
  
  // Render process variant (horizontal flow)
  const renderProcessVariant = () => {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          overflowX: 'auto',
          pb: 2
        }}
      >
        {steps.map((step, index) => {
          const isLast = index === steps.length - 1;
          const connection = connections.find(c => c.from === step.id);
          
          return (
            <React.Fragment key={step.id}>
              <Paper
                sx={{
                  px: compact ? 2 : 3,
                  py: compact ? 1.5 : 2,
                  display: 'flex',
                  alignItems: 'center',
                  borderRadius: 1,
                  backgroundColor: getBackgroundForState(step.state, step.color),
                  border: `1px solid ${alpha(getColorForState(step.state, step.color), 0.3)}`,
                  minWidth: compact ? 'auto' : 120,
                  cursor: interactive && step.state !== 'disabled' ? 'pointer' : 'default',
                  opacity: step.state === 'disabled' ? 0.6 : 1,
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: interactive && step.state !== 'disabled' ? theme.shadows[2] : 'none',
                    backgroundColor: interactive && step.state !== 'disabled' 
                      ? alpha(getColorForState(step.state, step.color), 0.15)
                      : getBackgroundForState(step.state, step.color)
                  }
                }}
                onClick={() => handleStepClick(step)}
              >
                {showIcons && (
                  <Box
                    sx={{
                      color: getColorForState(step.state, step.color),
                      mr: 1.5,
                      display: 'flex'
                    }}
                  >
                    {step.icon || getDefaultIcon(step.id)}
                  </Box>
                )}
                <Box>
                  <Typography
                    variant={compact ? 'body2' : 'subtitle2'}
                    sx={{
                      fontWeight: 500,
                      color: getColorForState(step.state, step.color)
                    }}
                  >
                    {step.label}
                  </Typography>
                  {step.description && !compact && (
                    <Typography variant="caption" color="text.secondary" display="block">
                      {step.description}
                    </Typography>
                  )}
                </Box>
              </Paper>
              
              {!isLast && connection && (
                renderConnection(connection)
              )}
            </React.Fragment>
          );
        })}
      </Box>
    );
  };
  
  // Render timeline variant (vertical flow)
  const renderTimelineVariant = () => {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'stretch'
        }}
      >
        {steps.map((step, index) => {
          const isLast = index === steps.length - 1;
          const connection = connections.find(c => c.from === step.id);
          
          return (
            <React.Fragment key={step.id}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  position: 'relative'
                }}
              >
                <Box
                  sx={{
                    width: 24,
                    height: 24,
                    borderRadius: '50%',
                    backgroundColor: getBackgroundForState(step.state, step.color),
                    border: `2px solid ${getColorForState(step.state, step.color)}`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 1
                  }}
                >
                  {showIcons && step.icon && React.cloneElement(step.icon as React.ReactElement, {
                    style: { fontSize: 14, color: getColorForState(step.state, step.color) }
                  })}
                </Box>
                
                <Box
                  sx={{
                    ml: 2,
                    flex: 1,
                    cursor: interactive && step.state !== 'disabled' ? 'pointer' : 'default',
                    opacity: step.state === 'disabled' ? 0.6 : 1
                  }}
                  onClick={() => handleStepClick(step)}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontWeight: 500,
                      color: getColorForState(step.state, step.color)
                    }}
                  >
                    {step.label}
                  </Typography>
                  {step.description && (
                    <Typography variant="body2" color="text.secondary">
                      {step.description}
                    </Typography>
                  )}
                </Box>
              </Box>
              
              {!isLast && (
                <Box
                  sx={{
                    ml: '12px', // Half of circle width (24/2)
                    height: 24,
                    borderLeft: `2px solid ${getColorForState(connection?.state)}`,
                    my: 0.5
                  }}
                />
              )}
            </React.Fragment>
          );
        })}
      </Box>
    );
  };
  
  // Render based on variant
  const renderVariant = () => {
    switch (variant) {
      case 'process':
        return renderProcessVariant();
      case 'timeline':
        return renderTimelineVariant();
      case 'boxed':
      default:
        return renderBoxedVariant();
    }
  };
  
  return (
    <Box>
      {title && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1" fontWeight="medium">
            {title}
          </Typography>
          {description && (
            <Tooltip title={description} arrow>
              <IconButton size="small" sx={{ ml: 0.5 }}>
                <HelpIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      )}
      
      <Paper
        sx={{
          p: compact ? 1.5 : 3,
          backgroundColor: alpha(theme.palette.background.default, 0.5),
          borderRadius: 1
        }}
      >
        {renderVariant()}
      </Paper>
    </Box>
  );
};

export default DataFlowDiagram;