import React, { useState, useRef } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Card,
  CardContent,
  Divider,
  <PERSON>,
  Button,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
  Collapse,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Snackbar,
  Alert,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Fade,
  Zoom,
  CircularProgress
} from '@mui/material';
import {
  Share as ShareIcon,
  Download as DownloadIcon,
  FileCopy as FileCopyIcon,
  MoreVert as MoreVertIcon,
  Save as SaveIcon,
  BarChart as BarChartIcon,
  TableChart as TableChartIcon,
  Article as ArticleIcon,
  FormatQuote as FormatQuoteIcon,
  ArrowCircleDown as ArrowCircleDownIcon,
  InfoOutlined as InfoOutlinedIcon,
  ErrorOutline as ErrorOutlineIcon,
  CheckCircleOutline as CheckCircleOutlineIcon,
  Report as ReportIcon,
  Code as CodeIcon,
  ExpandMore as ExpandMoreIcon,
  Functions as FunctionsIcon,
  CloudDownload as CloudDownloadIcon,
  InsertDriveFile as InsertDriveFileIcon,
  Image as ImageIcon
} from '@mui/icons-material';
import html2canvas from 'html2canvas';

interface StatisticalTest {
  name: string;
  value: number;
  pValue?: number;
  df?: number;
  significant?: boolean;
  description?: string;
}

interface Assumption {
  name: string;
  status: 'passed' | 'warning' | 'failed' | 'unknown';
  message?: string;
  severity?: 'low' | 'medium' | 'high';
}

interface EnhancedAnalysisResultCardProps {
  title: string;
  description?: string;
  timestamp?: Date;
  stats?: {
    label: string;
    value: string | number;
    tooltip?: string;
    color?: string;
  }[];
  statisticalTests?: StatisticalTest[];
  pValue?: number;
  significance?: number;
  confidenceInterval?: [number, number];
  chart?: React.ReactNode;
  chartTitle?: string;
  table?: {
    columns: string[];
    rows: (string | number)[][];
  };
  interpretations?: string[];
  assumptions?: Assumption[];
  footnotes?: string[];
  code?: string;
  citations?: string[];
  onSave?: () => void;
  onCopy?: () => void;
  onShare?: () => void;
  onExport?: (format: 'pdf' | 'png' | 'csv' | 'json') => void;
  variant?: 'default' | 'compact' | 'expanded';
  showCitations?: boolean;
  showCode?: boolean;
  showAssumptions?: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`result-tabpanel-${index}`}
      aria-labelledby={`result-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const EnhancedAnalysisResultCard: React.FC<EnhancedAnalysisResultCardProps> = ({
  title,
  description,
  timestamp,
  stats,
  statisticalTests,
  pValue,
  significance = 0.05,
  confidenceInterval,
  chart,
  chartTitle,
  table,
  interpretations,
  assumptions,
  footnotes,
  code,
  citations,
  onSave,
  onCopy,
  onShare,
  onExport,
  variant = 'default',
  showCitations = true,
  showCode = false,
  showAssumptions = true
}) => {
  const theme = useTheme();
  const resultCardRef = useRef<HTMLDivElement>(null);
  
  // State
  const [tabValue, setTabValue] = useState(0);
  const [expanded, setExpanded] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [isExporting, setIsExporting] = useState(false);
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // Handle menu open/close
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };
  
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };
  
  // Handle expand/collapse
  const toggleExpand = () => {
    setExpanded(!expanded);
  };
  
  // Copy to clipboard
  const handleCopy = () => {
    if (onCopy) {
      onCopy();
    } else {
      // Default implementation - create a text representation
      let textContent = `${title}\n`;
      if (description) textContent += `${description}\n\n`;
      if (statisticalTests && statisticalTests.length > 0) {
        textContent += 'Statistical Tests:\n';
        statisticalTests.forEach(test => {
          textContent += `- ${test.name}: ${test.value}\n`;
        });
        textContent += '\n';
      }
      if (interpretations && interpretations.length > 0) {
        textContent += 'Interpretations:\n';
        interpretations.forEach(interp => {
          textContent += `- ${interp}\n`;
        });
      }
      
      navigator.clipboard.writeText(textContent)
        .then(() => {
          setSnackbarMessage('Results copied to clipboard');
          setSnackbarOpen(true);
        })
        .catch(() => {
          setSnackbarMessage('Failed to copy results');
          setSnackbarOpen(true);
        });
    }
    handleMenuClose();
  };
  
  // Save result
  const handleSave = () => {
    if (onSave) {
      onSave();
    } else {
      setSnackbarMessage('Save functionality not implemented');
      setSnackbarOpen(true);
    }
    handleMenuClose();
  };
  
  // Export as image
  const handleExportImage = async () => {
    if (resultCardRef.current) {
      setIsExporting(true);
      
      try {
        const canvas = await html2canvas(resultCardRef.current, {
          backgroundColor: theme.palette.background.paper,
          scale: 2, // Higher resolution
          logging: false
        });
        
        // Convert to PNG
        const image = canvas.toDataURL('image/png');
        
        // Create link and trigger download
        const link = document.createElement('a');
        link.download = `${title.replace(/\s+/g, '_')}_result.png`;
        link.href = image;
        link.click();
        
        setSnackbarMessage('Image exported successfully');
        setSnackbarOpen(true);
      } catch (error) {
        console.error('Error exporting image:', error);
        setSnackbarMessage('Failed to export image');
        setSnackbarOpen(true);
      } finally {
        setIsExporting(false);
      }
    }
    handleMenuClose();
  };
  
  // Export based on format
  const handleExport = (format: 'pdf' | 'png' | 'csv' | 'json') => {
    if (onExport) {
      onExport(format);
    } else if (format === 'png') {
      handleExportImage();
    } else {
      setSnackbarMessage(`Export to ${format.toUpperCase()} not implemented`);
      setSnackbarOpen(true);
    }
    handleMenuClose();
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };
  
  // Determine significance color
  const getSignificanceColor = (p: number | undefined, alpha: number) => {
    if (p === undefined) return theme.palette.text.secondary;
    return p <= alpha 
      ? theme.palette.success.main 
      : theme.palette.error.main;
  };
  
  // Render significance indicator
  const renderSignificanceIndicator = () => {
    if (pValue === undefined) return null;
    
    const isSignificant = pValue <= significance;
    
    return (
      <Chip
        label={isSignificant ? 'Significant' : 'Not Significant'}
        color={isSignificant ? 'success' : 'error'}
        size="small"
        icon={isSignificant ? <CheckCircleOutlineIcon /> : <ErrorOutlineIcon />}
        sx={{ fontWeight: 500 }}
      />
    );
  };
  
  // Render stats cards
  const renderStatsCards = () => {
    if (!stats || stats.length === 0) return null;
    
    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
        {stats.map((stat, index) => (
          <Tooltip key={index} title={stat.tooltip || ''} arrow>
            <Card 
              sx={{ 
                minWidth: 120, 
                boxShadow: 'none', 
                border: `1px solid ${theme.palette.divider}` 
              }}
            >
              <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                <Typography variant="caption" color="text.secondary" display="block">
                  {stat.label}
                </Typography>
                <Typography 
                  variant="h6" 
                  fontWeight="medium"
                  sx={{ color: stat.color ? stat.color : 'inherit' }}
                >
                  {typeof stat.value === 'number' ? 
                    stat.value.toLocaleString(undefined, { maximumFractionDigits: 4 }) : 
                    stat.value}
                </Typography>
              </CardContent>
            </Card>
          </Tooltip>
        ))}
      </Box>
    );
  };
  
  // Render assumptions check
  const renderAssumptions = () => {
    if (!assumptions || assumptions.length === 0) return null;

    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          Assumption Checks
        </Typography>
        
        <TableContainer component={Paper} variant="outlined">
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Assumption</TableCell>
                <TableCell align="center">Status</TableCell>
                <TableCell>Details</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {assumptions.map((assumption, index) => {
                let icon;
                let color;

                switch (assumption.status) {
                  case 'passed':
                    icon = <CheckCircleOutlineIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />;
                    color = theme.palette.success.main;
                    break;
                  case 'warning':
                    icon = <ReportIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />;
                    color = theme.palette.warning.main;
                    break;
                  case 'failed':
                    icon = <ErrorOutlineIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />;
                    color = theme.palette.error.main;
                    break;
                  default:
                    icon = <InfoOutlinedIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />;
                    color = theme.palette.text.secondary;
                }

                return (
                  <TableRow key={index}>
                    <TableCell component="th" scope="row">
                      <Typography variant="body2" fontWeight="medium">
                        {assumption.name}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        icon={icon}
                        label={assumption.status}
                        size="small"
                        sx={{
                          bgcolor: alpha(color, 0.1),
                          color,
                          fontWeight: 500,
                          textTransform: 'capitalize',
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      {assumption.message && (
                        <Typography variant="caption" color="text.secondary">
                          {assumption.message}
                        </Typography>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    );
  };
  
  // Render statistical tests
  const renderStatisticalTests = () => {
    if (!statisticalTests || statisticalTests.length === 0) return null;
    
    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          Statistical Tests
        </Typography>
        
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>Test</TableCell>
              <TableCell align="right">Value</TableCell>
              {/* Conditionally render df header if any test has it */}
              {statisticalTests.some(test => test.df !== undefined && test.name.toLowerCase() !== 'p-value') && (
                <TableCell align="right">df</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {statisticalTests.map((test, index) => (
              <TableRow key={index}>
                <TableCell component="th" scope="row">
                  <Tooltip title={test.description || ''} arrow>
                    <Typography variant="body2">
                      {test.name}
                    </Typography>
                  </Tooltip>
                </TableCell>
                <TableCell 
                  align="right"
                  sx={{
                    color: test.name.toLowerCase() === 'p-value' && test.value !== undefined 
                           ? getSignificanceColor(test.value as number, significance) 
                           : 'inherit',
                    fontWeight: test.name.toLowerCase() === 'p-value' && test.value !== undefined && (test.value as number) <= significance 
                                ? 'bold' 
                                : 'normal'
                  }}
                >
                  {typeof test.value === 'number'
                    ? test.name.toLowerCase() === 'p-value' && test.value < 0.001
                      ? '< 0.001'
                      : test.value.toLocaleString(undefined, { maximumFractionDigits: 4 })
                    : test.value}
                </TableCell>
                {/* Conditionally render df cell if any test has it and current test is not p-value */}
                {statisticalTests.some(t => t.df !== undefined && t.name.toLowerCase() !== 'p-value') && (
                  <TableCell align="right">
                    {test.df !== undefined ? test.df : (test.name.toLowerCase() !== 'p-value' ? '-' : '')}
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Box>
    );
  };
  
  // Render data table
  const renderDataTable = () => {
    if (!table || !table.columns || !table.rows) return null;
    
    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          Data Summary
        </Typography>
        
        <TableContainer component={Paper} variant="outlined">
          <Table size="small">
            <TableHead>
              <TableRow>
                {table.columns.map((column, index) => (
                  <TableCell key={index} align={index === 0 ? 'left' : 'right'}>
                    {column}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {table.rows.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <TableCell 
                      key={cellIndex} 
                      align={cellIndex === 0 ? 'left' : 'right'}
                      sx={{
                        fontWeight: cellIndex === 0 ? 'medium' : 'normal'
                      }}
                    >
                      {typeof cell === 'number' 
                        ? cell.toLocaleString(undefined, { maximumFractionDigits: 4 }) 
                        : cell}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    );
  };
  
  // Render interpretations
  const renderInterpretations = () => {
    if (!interpretations || interpretations.length === 0) return null;
    
    return (
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <FormatQuoteIcon color="primary" sx={{ mr: 1, transform: 'scaleX(-1)' }} />
          <Typography variant="subtitle2">
            Interpretation
          </Typography>
        </Box>
        
        <Paper 
          variant="outlined" 
          sx={{ 
            p: 2, 
            bgcolor: alpha(theme.palette.primary.main, 0.03),
            borderLeft: `4px solid ${theme.palette.primary.main}`
          }}
        >
          {interpretations.map((interpretation, index) => (
            <Typography 
              key={index} 
              variant="body2" 
              paragraph={index < interpretations.length - 1}
              sx={{ mb: index < interpretations.length - 1 ? 1.5 : 0 }}
            >
              {interpretation}
            </Typography>
          ))}
        </Paper>
      </Box>
    );
  };
  
  // Render code snippet
  const renderCode = () => {
    if (!code || !showCode) return null;
    
    return (
      <Collapse in={expanded}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <CodeIcon sx={{ mr: 1, fontSize: '1rem' }} />
            Code
          </Typography>
          
          <Paper 
            variant="outlined" 
            sx={{ 
              p: 2, 
              bgcolor: alpha(theme.palette.grey[900], 0.05),
              fontFamily: 'monospace',
              fontSize: '0.85rem',
              overflow: 'auto',
              maxHeight: 200
            }}
          >
            <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
              {code}
            </pre>
          </Paper>
        </Box>
      </Collapse>
    );
  };
  
  // Render citations
  const renderCitations = () => {
    if (!citations || citations.length === 0 || !showCitations) return null;
    
    return (
      <Collapse in={expanded}>
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Citations
          </Typography>
          
          <Paper 
            variant="outlined" 
            sx={{ 
              p: 1.5, 
              bgcolor: alpha(theme.palette.background.default, 0.5)
            }}
          >
            {citations.map((citation, index) => (
              <Typography 
                key={index} 
                variant="caption" 
                display="block" 
                sx={{ 
                  mb: index < citations.length - 1 ? 1 : 0,
                  pb: index < citations.length - 1 ? 1 : 0,
                  borderBottom: index < citations.length - 1 ? `1px solid ${theme.palette.divider}` : 'none'
                }}
              >
                <Typography component="span" variant="caption" fontWeight="medium">
                  [{index + 1}]
                </Typography>{' '}
                {citation}
              </Typography>
            ))}
          </Paper>
        </Box>
      </Collapse>
    );
  };
  
  // Render footnotes
  const renderFootnotes = () => {
    if (!footnotes || footnotes.length === 0) return null;
    
    return (
      <Box sx={{ mt: 3 }}>
        <Divider sx={{ mb: 2 }} />
        
        {footnotes.map((footnote, index) => (
          <Typography 
            key={index} 
            variant="caption" 
            color="text.secondary" 
            display="block"
            sx={{ mb: 0.5 }}
          >
            * {footnote}
          </Typography>
        ))}
      </Box>
    );
  };
  
  // Render tabs for compact view
  const renderTabs = () => {
    return (
      <Box sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="result tabs"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab 
              label="Results" 
              id="result-tab-0" 
              aria-controls="result-tabpanel-0"
              icon={<FunctionsIcon />}
              iconPosition="start"
            />
            <Tab 
              label="Visualization" 
              id="result-tab-1" 
              aria-controls="result-tabpanel-1"
              icon={<BarChartIcon />}
              iconPosition="start"
              disabled={!chart}
            />
            <Tab 
              label="Data" 
              id="result-tab-2" 
              aria-controls="result-tabpanel-2"
              icon={<TableChartIcon />}
              iconPosition="start"
              disabled={!table}
            />
            <Tab 
              label="Interpretation" 
              id="result-tab-3" 
              aria-controls="result-tabpanel-3"
              icon={<ArticleIcon />}
              iconPosition="start"
              disabled={!interpretations || interpretations.length === 0}
            />
          </Tabs>
        </Box>
        
        <TabPanel value={tabValue} index={0}>
          {renderStatsCards()}
          {renderStatisticalTests()}
          {showAssumptions && renderAssumptions()}
        </TabPanel>
        
        <TabPanel value={tabValue} index={1}>
          {chart && (
            <Box sx={{ mb: 2 }}>
              {chartTitle && (
                <Typography variant="subtitle2" gutterBottom align="center">
                  {chartTitle}
                </Typography>
              )}
              {chart}
            </Box>
          )}
        </TabPanel>
        
        <TabPanel value={tabValue} index={2}>
          {renderDataTable()}
        </TabPanel>
        
        <TabPanel value={tabValue} index={3}>
          {renderInterpretations()}
        </TabPanel>
      </Box>
    );
  };
  
  return (
    <div ref={resultCardRef}>
      <Card 
        elevation={3} 
        sx={{ 
          position: 'relative',
          overflow: 'visible',
          mb: 3
        }}
      >
        {isExporting && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: alpha(theme.palette.background.paper, 0.7),
              zIndex: 10,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column'
            }}
          >
            <CircularProgress size={40} />
            <Typography variant="body2" sx={{ mt: 2 }}>
              Exporting...
            </Typography>
          </Box>
        )}
        
        <CardContent>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" component="h2" gutterBottom fontWeight="medium">
                {title}
              </Typography>
              
              {description && (
                <Typography variant="body2" color="text.secondary" paragraph>
                  {description}
                </Typography>
              )}
              
              {pValue !== undefined && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  {renderSignificanceIndicator()}
                  
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      ml: 1,
                      color: getSignificanceColor(pValue, significance),
                      fontWeight: pValue <= significance ? 'medium' : 'normal'
                    }}
                  >
                    p {pValue < 0.001 ? '< 0.001' : `= ${pValue.toFixed(3)}`}
                  </Typography>
                  
                  {confidenceInterval && (
                    <Typography variant="body2" sx={{ ml: 2 }}>
                      95% CI [{confidenceInterval[0].toFixed(2)}, {confidenceInterval[1].toFixed(2)}]
                    </Typography>
                  )}
                </Box>
              )}
              
              {timestamp && (
                <Typography variant="caption" color="text.secondary" display="block">
                  {timestamp.toLocaleString()}
                </Typography>
              )}
            </Box>
            
            <Box>
              <IconButton
                aria-label="more"
                aria-controls="result-menu"
                aria-haspopup="true"
                onClick={handleMenuOpen}
                size="small"
              >
                <MoreVertIcon fontSize="small" />
              </IconButton>
              
              <Menu
                id="result-menu"
                anchorEl={menuAnchorEl}
                keepMounted
                open={Boolean(menuAnchorEl)}
                onClose={handleMenuClose}
              >
                <MenuItem onClick={handleCopy}>
                  <ListItemIcon>
                    <FileCopyIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Copy to Clipboard</ListItemText>
                </MenuItem>
                
                <MenuItem onClick={handleSave}>
                  <ListItemIcon>
                    <SaveIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Save Analysis</ListItemText>
                </MenuItem>
                
                <Divider />
                
                <MenuItem onClick={() => handleExport('png')}>
                  <ListItemIcon>
                    <ImageIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Export as Image</ListItemText>
                </MenuItem>
                
                <MenuItem onClick={() => handleExport('csv')}>
                  <ListItemIcon>
                    <InsertDriveFileIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Export as CSV</ListItemText>
                </MenuItem>
                
                <MenuItem onClick={() => handleExport('pdf')}>
                  <ListItemIcon>
                    <CloudDownloadIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Export as PDF</ListItemText>
                </MenuItem>
              </Menu>
            </Box>
          </Box>
          
          <Divider sx={{ mb: 2 }} />
          
          {/* Content based on variant */}
          {variant === 'compact' ? (
            renderTabs()
          ) : (
            <>
              {/* Stats and Main Content */}
              <Box sx={{ display: 'flex', flexDirection: 'column', mb: 3 }}>
                {renderStatsCards()}
                {renderStatisticalTests()}
                
                {/* Chart */}
                {chart && (
                  <Box sx={{ mb: 3 }}>
                    {chartTitle && (
                      <Typography variant="subtitle2" gutterBottom align="center">
                        {chartTitle}
                      </Typography>
                    )}
                    {chart}
                  </Box>
                )}
                
                {/* Data Table */}
                {renderDataTable()}
                
                {/* Interpretations */}
                {renderInterpretations()}
                
                {/* Assumptions */}
                {showAssumptions && renderAssumptions()}
                
                {/* Code Snippet */}
                {renderCode()}
                
                {/* Citations */}
                {renderCitations()}
                
                {/* Show More Button */}
                {((code && showCode) || (citations && citations.length > 0 && showCitations)) && (
                  <Button
                    variant="text"
                    size="small"
                    onClick={toggleExpand}
                    endIcon={
                      <ExpandMoreIcon 
                        sx={{ 
                          transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                          transition: 'transform 0.3s' 
                        }} 
                      />
                    }
                    sx={{ alignSelf: 'flex-start', mt: 1 }}
                  >
                    {expanded ? 'Show Less' : 'Show More'}
                  </Button>
                )}
                
                {/* Footnotes */}
                {renderFootnotes()}
              </Box>
            </>
          )}
          
          {/* Action Buttons */}
          <Box 
            sx={{ 
              display: 'flex', 
              justifyContent: 'flex-end',
              mt: 2, 
              pt: 2,
              borderTop: `1px solid ${theme.palette.divider}`
            }}
          >
            <Button
              startIcon={<FileCopyIcon />}
              size="small"
              onClick={handleCopy}
              sx={{ mr: 1 }}
            >
              Copy
            </Button>
            
            <Button
              startIcon={<SaveIcon />}
              size="small"
              variant="outlined"
              onClick={handleSave}
              sx={{ mr: 1 }}
            >
              Save
            </Button>
            
            <Button
              startIcon={<DownloadIcon />}
              size="small"
              variant="contained"
              color="primary"
              onClick={() => handleExport('png')}
            >
              Export
            </Button>
          </Box>
        </CardContent>
      </Card>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity="success" elevation={6} variant="filled">
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default EnhancedAnalysisResultCard;
