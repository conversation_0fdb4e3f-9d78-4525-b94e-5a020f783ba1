import React, { useState } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  useMediaQuery,
  Collapse,
  Card,
  CardContent,
  Grid,
  alpha
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';

interface FeatureCategory {
  name: string;
  description: string;
  features: {
    [userType: string]: {
      access: boolean;
      note?: string;
    };
  };
}

interface UserTier {
  id: string;
  name: string;
  color: string;
  badge?: string;
}

interface FeatureComparisonTableProps {
  compact?: boolean;
  showDescriptions?: boolean;
  highlightTier?: string;
}

const userTiers: UserTier[] = [
  { id: 'guest', name: 'Guest', color: '#4caf50' },
  { id: 'standard', name: 'Standard', color: '#2196f3', badge: 'Free' },
  { id: 'pro', name: 'Pro', color: '#ff9800', badge: 'Premium' },
  { id: 'educational', name: 'Educational', color: '#9c27b0', badge: 'Free' },
  { id: 'educational_pro', name: 'Educational Pro', color: '#673ab7', badge: 'Premium' }
];

const featureCategories: FeatureCategory[] = [
  {
    name: 'Sample Datasets',
    description: 'Access to built-in sample datasets for learning and practice',
    features: {
      guest: { access: true, note: 'Full access to all sample datasets' },
      standard: { access: true, note: 'Full access to all sample datasets' },
      pro: { access: true, note: 'Full access to all sample datasets' },
      educational: { access: true, note: 'Full access to all sample datasets' },
      educational_pro: { access: true, note: 'Full access to all sample datasets' }
    }
  },
  {
    name: 'Imported Datasets',
    description: 'Upload and analyze your own data files (CSV, Excel, etc.)',
    features: {
      guest: { access: false, note: 'Cannot upload personal data' },
      standard: { access: true, note: 'Upload and analyze your own data' },
      pro: { access: true, note: 'Upload and analyze your own data' },
      educational: { access: true, note: 'Upload and analyze your own data' },
      educational_pro: { access: true, note: 'Upload and analyze your own data' }
    }
  },
  {
    name: 'Cloud Datasets',
    description: 'Store datasets in the cloud for access from any device',
    features: {
      guest: { access: false, note: 'No cloud storage available' },
      standard: { access: false, note: 'Local storage only' },
      pro: { access: true, note: 'Up to 2 datasets in cloud storage' },
      educational: { access: false, note: 'Local storage only' },
      educational_pro: { access: true, note: 'Up to 2 datasets in cloud storage' }
    }
  },
  {
    name: 'Advanced Features',
    description: 'Advanced statistical methods, ANOVA, regression, and more',
    features: {
      guest: { access: true, note: 'Preview only with sample data' },
      standard: { access: false, note: 'Basic analysis only' },
      pro: { access: true, note: 'Full access to all advanced features' },
      educational: { access: true, note: 'Free access for .edu users' },
      educational_pro: { access: true, note: 'Full access to all advanced features' }
    }
  },
  {
    name: 'Publication Features',
    description: 'APA tables, methods text generation, publication-ready outputs',
    features: {
      guest: { access: true, note: 'Preview only with sample data' },
      standard: { access: false, note: 'Not available' },
      pro: { access: true, note: 'Full publication-ready tools' },
      educational: { access: false, note: 'Upgrade to Educational Pro' },
      educational_pro: { access: true, note: 'Full publication-ready tools' }
    }
  }
];

const FeatureComparisonTable: React.FC<FeatureComparisonTableProps> = ({
  compact = false,
  showDescriptions = true,
  highlightTier
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);

  const toggleCategory = (categoryName: string) => {
    setExpandedCategories(prev =>
      prev.includes(categoryName)
        ? prev.filter(name => name !== categoryName)
        : [...prev, categoryName]
    );
  };

  const getAccessIcon = (access: boolean, note?: string) => {
    if (access) {
      return (
        <Tooltip title={note || 'Full access'}>
          <CheckIcon sx={{ color: 'success.main', fontSize: compact ? 20 : 24 }} />
        </Tooltip>
      );
    } else {
      return (
        <Tooltip title={note || 'No access'}>
          <CancelIcon sx={{ color: 'error.main', fontSize: compact ? 20 : 24 }} />
        </Tooltip>
      );
    }
  };

  const getTierChip = (tier: UserTier) => (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
      <Typography
        variant={compact ? 'body2' : 'subtitle1'}
        sx={{
          fontWeight: 'bold',
          color: tier.color,
          textAlign: 'center'
        }}
      >
        {tier.name}
      </Typography>
      {tier.badge && (
        <Chip
          label={tier.badge}
          size="small"
          sx={{
            bgcolor: alpha(tier.color, 0.1),
            color: tier.color,
            fontSize: '0.7rem',
            height: 20
          }}
        />
      )}
    </Box>
  );

  // Mobile card view
  if (isMobile) {
    return (
      <Box sx={{ width: '100%' }}>
        <Typography variant="h6" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
          Feature Comparison
        </Typography>
        
        {featureCategories.map((category) => (
          <Card key={category.name} sx={{ mb: 2 }}>
            <CardContent sx={{ p: 2 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  cursor: 'pointer'
                }}
                onClick={() => toggleCategory(category.name)}
              >
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  {category.name}
                </Typography>
                <IconButton size="small">
                  {expandedCategories.includes(category.name) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Box>
              
              {showDescriptions && (
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1, mb: 2 }}>
                  {category.description}
                </Typography>
              )}
              
              <Collapse in={expandedCategories.includes(category.name)}>
                <Grid container spacing={1} sx={{ mt: 1 }}>
                  {userTiers.map((tier) => (
                    <Grid item xs={6} sm={4} key={tier.id}>
                      <Box
                        sx={{
                          p: 1,
                          border: 1,
                          borderColor: 'divider',
                          borderRadius: 1,
                          textAlign: 'center',
                          bgcolor: highlightTier === tier.id ? alpha(tier.color, 0.05) : 'transparent'
                        }}
                      >
                        <Typography variant="caption" sx={{ color: tier.color, fontWeight: 'bold' }}>
                          {tier.name}
                        </Typography>
                        <Box sx={{ mt: 0.5 }}>
                          {getAccessIcon(
                            category.features[tier.id]?.access || false,
                            category.features[tier.id]?.note
                          )}
                        </Box>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Collapse>
            </CardContent>
          </Card>
        ))}
      </Box>
    );
  }

  // Desktop table view
  return (
    <Box sx={{ width: '100%', overflowX: 'auto' }}>
      <Typography variant="h6" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
        Feature Comparison
      </Typography>
      
      <TableContainer component={Paper} elevation={2}>
        <Table sx={{ minWidth: 650 }}>
          <TableHead>
            <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
              <TableCell sx={{ fontWeight: 'bold', minWidth: 200 }}>
                Feature Category
                {showDescriptions && (
                  <Tooltip title="Click on feature names for detailed descriptions">
                    <InfoIcon sx={{ ml: 1, fontSize: 16, color: 'text.secondary' }} />
                  </Tooltip>
                )}
              </TableCell>
              {userTiers.map((tier) => (
                <TableCell
                  key={tier.id}
                  align="center"
                  sx={{
                    fontWeight: 'bold',
                    minWidth: 120,
                    bgcolor: highlightTier === tier.id ? alpha(tier.color, 0.1) : 'transparent'
                  }}
                >
                  {getTierChip(tier)}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {featureCategories.map((category, index) => (
              <TableRow
                key={category.name}
                sx={{
                  '&:nth-of-type(odd)': { bgcolor: alpha(theme.palette.grey[100], 0.5) },
                  '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.02) }
                }}
              >
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                      {category.name}
                    </Typography>
                    {showDescriptions && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                        {category.description}
                      </Typography>
                    )}
                  </Box>
                </TableCell>
                {userTiers.map((tier) => (
                  <TableCell
                    key={tier.id}
                    align="center"
                    sx={{
                      bgcolor: highlightTier === tier.id ? alpha(tier.color, 0.05) : 'transparent'
                    }}
                  >
                    {getAccessIcon(
                      category.features[tier.id]?.access || false,
                      category.features[tier.id]?.note
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default FeatureComparisonTable;
