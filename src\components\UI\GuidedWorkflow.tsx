import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Paper,
  Button,
  IconButton,
  Divider,
  Tooltip,
  useTheme,
  alpha,
  Stepper,
  Step,
  <PERSON><PERSON>abel,
  StepContent,
  StepButton,
  <PERSON>lapse,
  Alert,
  AlertTitle,
  CircularProgress,
  Badge,
  Fade,
  Zoom,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Card,
  CardContent,
  CardActions,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import {
  PlayArrow as PlayArrowIcon,
  Check as CheckIcon,
  Info as InfoIcon,
  Help as HelpIcon,
  ArrowForward as ArrowForwardIcon,
  NavigateNext as NavigateNextIcon,
  NavigateBefore as NavigateBeforeIcon,
  RestartAlt as RestartAltIcon,
  Warning as WarningIcon,
  Lightbulb as LightbulbIcon,
  School as SchoolIcon,
  CloseOutlined as CloseOutlinedIcon,
  ErrorOutline as ErrorOutlineIcon,
  CheckCircleOutline as CheckCircleOutlineIcon,
  Autorenew as AutorenewIcon,
  QuestionAnswer as QuestionAnswerIcon,
  Bookmarks as BookmarksIcon,
  BookmarkBorder as BookmarkBorderIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';

// Interfaces
export interface WorkflowStep {
  id: string;
  title: string;
  description?: string;
  content: React.ReactNode;
  optional?: boolean;
  validation?: () => boolean | string;
  nextButton?: string;
  tip?: string;
  prerequisite?: WorkflowPrerequisite;
  helpContent?: React.ReactNode;
  isActionStep?: boolean;
  status?: 'completed' | 'current' | 'upcoming' | 'skipped';
}

export interface WorkflowPrerequisite {
  condition: boolean;
  message: string;
  severity?: 'error' | 'warning' | 'info';
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface GuidedWorkflowProps {
  steps: WorkflowStep[];
  title?: string;
  description?: string;
  variant?: 'vertical' | 'horizontal' | 'numbered' | 'wizard';
  saveProgress?: boolean;
  persistenceKey?: string;
  onComplete?: () => void;
  enableBookmarking?: boolean;
  showStepNavigation?: boolean;
  allowSkipSteps?: boolean;
  initialStep?: number;
}

const GuidedWorkflow: React.FC<GuidedWorkflowProps> = ({
  steps,
  title = 'Analysis Workflow',
  description,
  variant = 'vertical',
  saveProgress = true,
  persistenceKey = 'guided-workflow',
  onComplete,
  enableBookmarking = false,
  showStepNavigation = true,
  allowSkipSteps = false,
  initialStep = 0
}) => {
  const theme = useTheme();
  const { activeDataset } = useData();
  
  // State
  const [activeStep, setActiveStep] = useState(initialStep);
  const [completedSteps, setCompletedSteps] = useState<Record<number, boolean>>({});
  const [skippedSteps, setSkippedSteps] = useState<Record<number, boolean>>({});
  const [helpOpen, setHelpOpen] = useState(false);
  const [bookmarkedSteps, setBookmarkedSteps] = useState<number[]>([]);
  const [showPrerequisiteAlert, setShowPrerequisiteAlert] = useState(true);
  const [showTipDialog, setShowTipDialog] = useState(false);
  const [dontShowTipsAgain, setDontShowTipsAgain] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  
  // Load saved progress
  useEffect(() => {
    if (saveProgress && persistenceKey) {
      const savedData = localStorage.getItem(`${persistenceKey}-progress`);
      if (savedData) {
        try {
          const { completed, skipped, active, bookmarked, dontShowTips } = JSON.parse(savedData);
          setCompletedSteps(completed || {});
          setSkippedSteps(skipped || {});
          setActiveStep(active || 0);
          setBookmarkedSteps(bookmarked || []);
          setDontShowTipsAgain(dontShowTips || false);
        } catch (e) {
          console.error('Failed to load workflow progress:', e);
        }
      }
    }
  }, [saveProgress, persistenceKey]);
  
  // Save progress
  useEffect(() => {
    if (saveProgress && persistenceKey) {
      const dataToSave = {
        completed: completedSteps,
        skipped: skippedSteps,
        active: activeStep,
        bookmarked: bookmarkedSteps,
        dontShowTips: dontShowTipsAgain
      };
      localStorage.setItem(`${persistenceKey}-progress`, JSON.stringify(dataToSave));
    }
  }, [saveProgress, persistenceKey, completedSteps, skippedSteps, activeStep, bookmarkedSteps, dontShowTipsAgain]);
  
  // Show tip on step change
  useEffect(() => {
    if (!dontShowTipsAgain && steps[activeStep]?.tip) {
      // Show tip dialog after a short delay
      const timer = setTimeout(() => {
        setShowTipDialog(true);
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [activeStep, dontShowTipsAgain, steps]);
  
  // Check if all steps are completed
  const isLastStep = activeStep === steps.length - 1;
  const allStepsCompleted = steps.every((_, index) => completedSteps[index] || skippedSteps[index]);
  
  // Check if current step has a prerequisite that's not met
  const currentStepHasUnmetPrerequisite = (): boolean => {
    const currentStep = steps[activeStep];
    if (!currentStep?.prerequisite) return false;
    
    return !currentStep.prerequisite.condition;
  };
  
  // Handle step navigation
  const handleNext = () => {
    const currentStep = steps[activeStep];
    
    // Validate current step if validation function exists
    if (currentStep.validation) {
      const validationResult = currentStep.validation();
      if (validationResult !== true) {
        setValidationError(typeof validationResult === 'string' ? validationResult : 'Validation failed');
        return;
      }
    }
    
    // Clear any validation error
    setValidationError(null);
    
    // Mark current step as completed
    setCompletedSteps(prev => ({
      ...prev,
      [activeStep]: true
    }));
    
    // Check if we've completed all steps
    if (isLastStep) {
      setIsCompleted(true);
      if (onComplete) {
        onComplete();
      }
    } else {
      // Move to next step
      setActiveStep(prevActiveStep => prevActiveStep + 1);
    }
  };
  
  const handleBack = () => {
    setActiveStep(prevActiveStep => prevActiveStep - 1);
  };
  
  const handleStepClick = (step: number) => {
    if (showStepNavigation) {
      setActiveStep(step);
    }
  };
  
  const handleSkip = () => {
    if (allowSkipSteps) {
      setSkippedSteps(prev => ({
        ...prev,
        [activeStep]: true
      }));
      
      if (isLastStep) {
        setIsCompleted(true);
        if (onComplete) {
          onComplete();
        }
      } else {
        setActiveStep(prevActiveStep => prevActiveStep + 1);
      }
    }
  };
  
  const handleReset = () => {
    setActiveStep(0);
    setCompletedSteps({});
    setSkippedSteps({});
    setIsCompleted(false);
    setValidationError(null);
  };
  
  // Handle bookmarking
  const toggleBookmark = (step: number) => {
    if (bookmarkedSteps.includes(step)) {
      setBookmarkedSteps(prev => prev.filter(s => s !== step));
    } else {
      setBookmarkedSteps(prev => [...prev, step]);
    }
  };
  
  // Handle help dialog
  const toggleHelp = () => {
    setHelpOpen(!helpOpen);
  };
  
  // Handle tip dialog
  const handleCloseTip = () => {
    setShowTipDialog(false);
  };
  
  // Helper to check if a step is bookmarked
  const isStepBookmarked = (step: number) => bookmarkedSteps.includes(step);
  
  // Helper to check step status
  const getStepStatus = (index: number) => {
    if (completedSteps[index]) return 'completed';
    if (skippedSteps[index]) return 'skipped';
    if (index === activeStep) return 'current';
    return 'upcoming';
  };
  
  // Render prerequisite alert
  const renderPrerequisiteAlert = () => {
    const currentStep = steps[activeStep];
    if (!currentStep?.prerequisite || !currentStepHasUnmetPrerequisite() || !showPrerequisiteAlert) {
      return null;
    }
    
    const { message, severity = 'warning', action } = currentStep.prerequisite;
    
    return (
      <Alert 
        severity={severity}
        sx={{ mb: 2 }}
        onClose={() => setShowPrerequisiteAlert(false)}
        action={
          action ? (
            <Button 
              color="inherit" 
              size="small" 
              onClick={action.onClick}
            >
              {action.label}
            </Button>
          ) : undefined
        }
      >
        <AlertTitle>{severity === 'error' ? 'Required' : 'Recommended'}</AlertTitle>
        {message}
      </Alert>
    );
  };
  
  // Render validation error
  const renderValidationError = () => {
    if (!validationError) return null;
    
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        <AlertTitle>Validation Error</AlertTitle>
        {validationError}
      </Alert>
    );
  };
  
  // Render tip dialog
  const renderTipDialog = () => {
    const currentStep = steps[activeStep];
    if (!currentStep?.tip) return null;
    
    return (
      <Dialog
        open={showTipDialog}
        onClose={handleCloseTip}
        maxWidth="sm"
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center' }}>
          <LightbulbIcon color="warning" sx={{ mr: 1 }} />
          Tip for This Step
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {currentStep.tip}
          </DialogContentText>
          
          <FormControlLabel
            control={
              <Checkbox
                checked={dontShowTipsAgain}
                onChange={(e) => setDontShowTipsAgain(e.target.checked)}
                color="primary"
              />
            }
            label="Don't show tips anymore"
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseTip} color="primary">
            Got it
          </Button>
        </DialogActions>
      </Dialog>
    );
  };
  
  // Render help dialog
  const renderHelpDialog = () => {
    const currentStep = steps[activeStep];
    
    return (
      <Dialog
        open={helpOpen}
        onClose={toggleHelp}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center' }}>
          <HelpIcon color="primary" sx={{ mr: 1 }} />
          Help for {currentStep?.title || 'This Step'}
          <IconButton
            aria-label="close"
            onClick={toggleHelp}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseOutlinedIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {currentStep?.helpContent ? (
            currentStep.helpContent
          ) : (
            <Typography variant="body1">
              No additional help content is available for this step.
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={toggleHelp} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    );
  };
  
  // Render vertical stepper
  const renderVerticalStepper = () => {
    return (
      <Box sx={{ maxWidth: '100%' }}>
        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => {
            const stepProps: { completed?: boolean; optional?: React.ReactNode } = {};
            const labelProps: { optional?: React.ReactNode } = {};
            
            if (step.optional) {
              labelProps.optional = (
                <Typography variant="caption">Optional</Typography>
              );
            }
            
            if (completedSteps[index]) {
              stepProps.completed = true;
            }
            
            return (
              <Step key={step.id} {...stepProps}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                  <StepButton
                    onClick={() => handleStepClick(index)}
                    disabled={!showStepNavigation}
                    sx={{ flexGrow: 1, textAlign: 'left', mr: enableBookmarking ? 1 : 0 }}
                  >
                    <StepLabel optional={labelProps.optional}>
                      {step.title}
                    </StepLabel>
                  </StepButton>
                  {enableBookmarking && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleBookmark(index);
                      }}
                      color={isStepBookmarked(index) ? 'primary' : 'default'}
                      aria-label={isStepBookmarked(index) ? 'Remove bookmark' : 'Add bookmark'}
                    >
                      {isStepBookmarked(index) ? <BookmarksIcon fontSize="small" /> : <BookmarkBorderIcon fontSize="small" />}
                    </IconButton>
                  )}
                </Box>
                
                <StepContent>
                  {step.description && (
                    <Typography variant="body2" color="text.secondary" paragraph>
                      {step.description}
                    </Typography>
                  )}
                  
                  {index === activeStep && renderPrerequisiteAlert()}
                  {index === activeStep && renderValidationError()}
                  
                  <Box sx={{ mb: 2 }}>
                    {step.content}
                  </Box>
                  
                  <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Button
                        disabled={index === 0}
                        onClick={handleBack}
                        sx={{ mr: 1 }}
                        startIcon={<NavigateBeforeIcon />}
                      >
                        Back
                      </Button>
                      
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleNext}
                        endIcon={isLastStep ? <CheckIcon /> : <NavigateNextIcon />}
                        disabled={currentStepHasUnmetPrerequisite() && !allowSkipSteps}
                      >
                        {isLastStep ? 'Finish' : (step.nextButton || 'Next')}
                      </Button>
                      
                      {allowSkipSteps && currentStepHasUnmetPrerequisite() && (
                        <Button
                          sx={{ ml: 1 }}
                          variant="text"
                          color="warning"
                          onClick={handleSkip}
                        >
                          Skip
                        </Button>
                      )}
                    </Box>
                    
                    {step.helpContent && (
                      <Button
                        startIcon={<HelpIcon />}
                        color="inherit"
                        onClick={toggleHelp}
                      >
                        Help
                      </Button>
                    )}
                  </Box>
                </StepContent>
              </Step>
            );
          })}
        </Stepper>
        
        {isCompleted && (
          <Paper square elevation={0} sx={{ p: 3, mt: 3, borderRadius: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <CheckCircleOutlineIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="h6" component="h3">
                All steps completed!
              </Typography>
            </Box>
            
            <Typography paragraph>
              You have successfully completed all necessary steps in this workflow.
            </Typography>
            
            <Button onClick={handleReset} startIcon={<RestartAltIcon />}>
              Reset Workflow
            </Button>
          </Paper>
        )}
      </Box>
    );
  };
  
  // Render horizontal stepper
  const renderHorizontalStepper = () => {
    return (
      <Box sx={{ width: '100%' }}>
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((step, index) => {
            const stepProps: { completed?: boolean; optional?: React.ReactNode } = {};
            const labelProps: { optional?: React.ReactNode } = {};
            
            if (step.optional) {
              labelProps.optional = (
                <Typography variant="caption">Optional</Typography>
              );
            }
            
            if (completedSteps[index]) {
              stepProps.completed = true;
            }
            
            return (
              <Step key={step.id} {...stepProps}>
                <StepLabel {...labelProps} optional={labelProps.optional}>
                  <Typography variant="body2">{step.title}</Typography>
                </StepLabel>
              </Step>
            );
          })}
        </Stepper>
        
        <Box sx={{ mt: 4, mb: 2 }}>
          {renderPrerequisiteAlert()}
          {renderValidationError()}
          
          <Paper
            variant="outlined"
            sx={{ p: 3 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, justifyContent: 'space-between' }}>
              <Typography variant="h6" component="h3">
                {steps[activeStep]?.title || ''}
              </Typography>
              
              {enableBookmarking && (
                <IconButton 
                  size="small" 
                  onClick={() => toggleBookmark(activeStep)}
                  color={isStepBookmarked(activeStep) ? 'primary' : 'default'}
                >
                  {isStepBookmarked(activeStep) ? <BookmarksIcon /> : <BookmarkBorderIcon />}
                </IconButton>
              )}
            </Box>
            
            {steps[activeStep]?.description && (
              <Typography variant="body2" color="text.secondary" paragraph>
                {steps[activeStep].description}
              </Typography>
            )}
            
            <Divider sx={{ my: 2 }} />
            
            <Box sx={{ mb: 2 }}>
              {steps[activeStep]?.content || 'No content available for this step.'}
            </Box>
          </Paper>
        </Box>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
          <Box>
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
              sx={{ mr: 1 }}
              startIcon={<NavigateBeforeIcon />}
            >
              Back
            </Button>
            
            <Button
              variant="contained"
              color="primary"
              onClick={handleNext}
              endIcon={isLastStep ? <CheckIcon /> : <NavigateNextIcon />}
              disabled={currentStepHasUnmetPrerequisite() && !allowSkipSteps}
            >
              {isLastStep ? 'Finish' : (steps[activeStep]?.nextButton || 'Next')}
            </Button>
            
            {allowSkipSteps && currentStepHasUnmetPrerequisite() && (
              <Button
                sx={{ ml: 1 }}
                variant="text"
                color="warning"
                onClick={handleSkip}
              >
                Skip
              </Button>
            )}
          </Box>
          
          {steps[activeStep]?.helpContent && (
            <Button
              startIcon={<HelpIcon />}
              color="inherit"
              onClick={toggleHelp}
            >
              Help
            </Button>
          )}
        </Box>
        
        {isCompleted && (
          <Paper square elevation={0} sx={{ p: 3, mt: 3, borderRadius: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <CheckCircleOutlineIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="h6" component="h3">
                All steps completed!
              </Typography>
            </Box>
            
            <Typography paragraph>
              You have successfully completed all necessary steps in this workflow.
            </Typography>
            
            <Button onClick={handleReset} startIcon={<RestartAltIcon />}>
              Reset Workflow
            </Button>
          </Paper>
        )}
      </Box>
    );
  };
  
  // Render numbered list
  const renderNumberedList = () => {
    return (
      <Box sx={{ width: '100%' }}>
        <Box sx={{ mb: 3 }}>
          {steps.map((step, index) => {
            const isActive = index === activeStep;
            const status = getStepStatus(index);
            
            return (
              <Box 
                key={step.id} 
                sx={{ 
                  mb: 2, 
                  position: 'relative',
                  opacity: status === 'upcoming' ? 0.7 : 1
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <Box 
                    sx={{ 
                      flexGrow: 1,
                      position: 'relative',
                      cursor: showStepNavigation ? 'pointer' : 'default',
                      mr: enableBookmarking ? 1 : 0
                    }}
                    onClick={() => handleStepClick(index)}
                  >
                    <Paper
                      variant={isActive ? 'elevation' : 'outlined'}
                      elevation={isActive ? 2 : 0}
                      sx={{ 
                        p: 2, 
                        borderLeft: `4px solid ${
                          status === 'completed' ? theme.palette.success.main :
                          status === 'skipped' ? theme.palette.warning.main :
                          status === 'current' ? theme.palette.primary.main :
                          theme.palette.grey[300]
                        }`,
                        backgroundColor: isActive ? alpha(theme.palette.primary.main, 0.05) : 'inherit'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box
                          sx={{
                            width: 28,
                            height: 28,
                            borderRadius: '50%',
                            bgcolor: status === 'completed' ? theme.palette.success.main :
                                    status === 'skipped' ? theme.palette.warning.main :
                                    status === 'current' ? theme.palette.primary.main :
                                    theme.palette.grey[300],
                            color: status === 'upcoming' ? theme.palette.getContrastText(theme.palette.grey[300]) : '#fff',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontWeight: 'bold',
                            mr: 2
                          }}
                        >
                          {status === 'completed' ? <CheckIcon fontSize="small" /> : index + 1}
                        </Box>
                        
                        <Typography variant="subtitle1" fontWeight={isActive ? 'medium' : 'normal'} sx={{ flexGrow: 1 }}>
                          {step.title}
                          {step.optional && (
                            <Typography component="span" variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
                              (Optional)
                            </Typography>
                          )}
                        </Typography>
                      </Box>
                      
                      {step.description && (
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1, ml: 5 }}>
                          {step.description}
                        </Typography>
                      )}
                    </Paper>
                  </Box>

                  {enableBookmarking && (
                    <IconButton 
                      size="small" 
                      onClick={() => toggleBookmark(index)}
                      sx={{ mt: 2, flexShrink: 0 }}
                      color={isStepBookmarked(index) ? 'primary' : 'default'}
                      aria-label={isStepBookmarked(index) ? 'Remove bookmark' : 'Add bookmark'}
                    >
                      {isStepBookmarked(index) ? <BookmarksIcon fontSize="small" /> : <BookmarkBorderIcon fontSize="small" />}
                    </IconButton>
                  )}
                </Box>
                
                {isActive && (
                  <Box sx={{ mt: 2, ml: 5 }}>
                    {renderPrerequisiteAlert()}
                    {renderValidationError()}
                    
                    <Box sx={{ mb: 3 }}>
                      {step.content}
                    </Box>
                    
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box>
                        <Button
                          disabled={index === 0}
                          onClick={handleBack}
                          sx={{ mr: 1 }}
                          startIcon={<NavigateBeforeIcon />}
                        >
                          Back
                        </Button>
                        
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={handleNext}
                          endIcon={isLastStep ? <CheckIcon /> : <NavigateNextIcon />}
                          disabled={currentStepHasUnmetPrerequisite() && !allowSkipSteps}
                        >
                          {isLastStep ? 'Finish' : (step.nextButton || 'Next')}
                        </Button>
                        
                        {allowSkipSteps && currentStepHasUnmetPrerequisite() && (
                          <Button
                            sx={{ ml: 1 }}
                            variant="text"
                            color="warning"
                            onClick={handleSkip}
                          >
                            Skip
                          </Button>
                        )}
                      </Box>
                      
                      {step.helpContent && (
                        <Button
                          startIcon={<HelpIcon />}
                          color="inherit"
                          onClick={toggleHelp}
                        >
                          Help
                        </Button>
                      )}
                    </Box>
                  </Box>
                )}
                
                {index < steps.length - 1 && !isActive && (
                  <Box 
                    sx={{ 
                      position: 'absolute',
                      left: 14,
                      top: 38,
                      bottom: -16,
                      width: 2,
                      bgcolor: theme.palette.divider
                    }}
                  />
                )}
              </Box>
            );
          })}
        </Box>
        
        {isCompleted && (
          <Paper square elevation={0} sx={{ p: 3, borderRadius: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <CheckCircleOutlineIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="h6" component="h3">
                All steps completed!
              </Typography>
            </Box>
            
            <Typography paragraph>
              You have successfully completed all necessary steps in this workflow.
            </Typography>
            
            <Button onClick={handleReset} startIcon={<RestartAltIcon />}>
              Reset Workflow
            </Button>
          </Paper>
        )}
      </Box>
    );
  };
  
  // Render wizard view
  const renderWizard = () => {
    const currentStep = steps[activeStep];
    
    return (
      <Box sx={{ width: '100%' }}>
        <Card elevation={3}>
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              px: 3, 
              py: 2,
              borderBottom: `1px solid ${theme.palette.divider}`,
              backgroundColor: alpha(theme.palette.primary.main, 0.05)
            }}
          >
            <Typography variant="h6" component="h3">
              {currentStep?.title || ''}
            </Typography>
            
            <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2" sx={{ mr: 1 }}>
                Step {activeStep + 1} of {steps.length}
              </Typography>
              
              {enableBookmarking && (
                <IconButton 
                  size="small" 
                  onClick={() => toggleBookmark(activeStep)}
                  color={isStepBookmarked(activeStep) ? 'primary' : 'default'}
                >
                  {isStepBookmarked(activeStep) ? <BookmarksIcon /> : <BookmarkBorderIcon />}
                </IconButton>
              )}
              
              {currentStep?.helpContent && (
                <IconButton size="small" onClick={toggleHelp} color="primary">
                  <HelpIcon />
                </IconButton>
              )}
            </Box>
          </Box>
          
          <CardContent>
            {currentStep?.description && (
              <Typography variant="body2" color="text.secondary" paragraph>
                {currentStep.description}
              </Typography>
            )}
            
            {renderPrerequisiteAlert()}
            {renderValidationError()}
            
            <Box sx={{ my: 2 }}>
              {currentStep?.content || 'No content available for this step.'}
            </Box>
          </CardContent>
          
          <CardActions sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
              startIcon={<NavigateBeforeIcon />}
            >
              Back
            </Button>
            
            <Box sx={{ flex: 1 }} />
            
            {allowSkipSteps && currentStepHasUnmetPrerequisite() && (
              <Button
                variant="text"
                color="warning"
                onClick={handleSkip}
              >
                Skip
              </Button>
            )}
            
            <Button
              variant="contained"
              color="primary"
              onClick={handleNext}
              endIcon={isLastStep ? <CheckIcon /> : <NavigateNextIcon />}
              disabled={currentStepHasUnmetPrerequisite() && !allowSkipSteps}
            >
              {isLastStep ? 'Finish' : (currentStep?.nextButton || 'Next')}
            </Button>
          </CardActions>
        </Card>
        
        {/* Wizard Progress Indicator */}
        <Box sx={{ display: 'flex', mt: 3, mb: 2 }}>
          {steps.map((_, index) => {
            const status = getStepStatus(index);
            return (
              <Box 
                key={index}
                sx={{
                  height: 8,
                  flex: 1,
                  mx: 0.5,
                  borderRadius: 4,
                  bgcolor: 
                    status === 'completed' ? theme.palette.success.main :
                    status === 'skipped' ? theme.palette.warning.main :
                    status === 'current' ? theme.palette.primary.main :
                    theme.palette.grey[300],
                  cursor: showStepNavigation ? 'pointer' : 'default'
                }}
                onClick={() => handleStepClick(index)}
              />
            );
          })}
        </Box>
      </Box>
    );
  };
  
  // Render based on variant
  const renderVariant = () => {
    switch (variant) {
      case 'horizontal':
        return renderHorizontalStepper();
      case 'numbered':
        return renderNumberedList();
      case 'wizard':
        return renderWizard();
      case 'vertical':
      default:
        return renderVerticalStepper();
    }
  };
  
  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          {title}
        </Typography>
        
        {description && (
          <Typography variant="body1" color="text.secondary" paragraph>
            {description}
          </Typography>
        )}
      </Box>
      
      {/* Main Content */}
      {renderVariant()}
      
      {/* Help Dialog */}
      {renderHelpDialog()}
      
      {/* Tip Dialog */}
      {renderTipDialog()}
    </Box>
  );
};

export default GuidedWorkflow;