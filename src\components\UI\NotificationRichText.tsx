import React from 'react';
import { Typography, Box } from '@mui/material';
import { parseTextWithYouTubeLinks } from '../../utils/youtubeUtils';
import YouTubeLink from './YouTubeLink';

interface NotificationRichTextProps {
  text: string;
  variant?: 'body2' | 'body1' | 'caption';
  color?: string;
  sx?: any;
  showYouTubePreview?: boolean;
}

const NotificationRichText: React.FC<NotificationRichTextProps> = ({
  text,
  variant = 'body2',
  color = 'text.secondary',
  sx = {},
  showYouTubePreview = true
}) => {
  const segments = parseTextWithYouTubeLinks(text);

  // If no YouTube links found, render as regular text
  if (segments.length === 1 && segments[0].type === 'text') {
    return (
      <Typography 
        variant={variant} 
        color={color} 
        sx={{
          whiteSpace: 'normal',
          wordWrap: 'break-word',
          lineHeight: 1.4,
          ...sx
        }}
      >
        {text}
      </Typography>
    );
  }

  // Render text with YouTube links
  return (
    <Typography 
      variant={variant} 
      color={color} 
      component="div"
      sx={{
        whiteSpace: 'normal',
        wordWrap: 'break-word',
        lineHeight: 1.4,
        ...sx
      }}
    >
      {segments.map((segment, index) => {
        if (segment.type === 'text') {
          return (
            <span key={index}>
              {segment.content}
            </span>
          );
        } else if (segment.type === 'youtube' && segment.info) {
          return (
            <Box 
              key={index} 
              component="span" 
              sx={{ 
                display: 'inline-block',
                verticalAlign: 'middle',
                mx: 0.5
              }}
            >
              <YouTubeLink
                url={segment.content}
                info={segment.info}
                variant="inline"
                showPreview={showYouTubePreview}
              />
            </Box>
          );
        }
        return null;
      })}
    </Typography>
  );
};

export default NotificationRichText;
