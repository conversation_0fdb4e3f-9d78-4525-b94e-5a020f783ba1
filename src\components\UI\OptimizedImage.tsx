/**
 * Optimized Image Component with WebP support and lazy loading
 * Auto-generated by optimize-images.js
 */

import React, { useState } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
  loading?: 'lazy' | 'eager';
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  style,
  loading = 'lazy'
}) => {
  const [imageError, setImageError] = useState(false);
  
  // Generate WebP source path
  const webpSrc = src.replace(/\.(png|jpg|jpeg)$/i, '.webp');
  
  const handleError = () => {
    setImageError(true);
  };
  
  if (imageError) {
    // Fallback to original image if WebP fails
    return (
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        style={style}
        loading={loading}
      />
    );
  }
  
  return (
    <picture>
      <source srcSet={webpSrc} type="image/webp" />
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        style={style}
        loading={loading}
        onError={handleError}
      />
    </picture>
  );
};

export default OptimizedImage;
