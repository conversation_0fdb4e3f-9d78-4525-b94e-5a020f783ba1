import React from 'react';
import { Box, Typography, Paper, Breadcrumbs, Link, styled } from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';

interface Breadcrumb {
  label: string;
  href?: string;
}

interface PageTitleProps {
  title: string;
  description?: string;
  breadcrumbs?: Breadcrumb[];
  action?: React.ReactNode;
  variant?: 'default' | 'compact' | 'transparent';
  icon?: React.ReactNode;
}

const StyledPaper = styled(Paper, {
  shouldForwardProp: (prop) => prop !== 'variant',
})<{ variant?: 'default' | 'compact' | 'transparent' }>(({ theme, variant }) => ({
  padding: variant === 'compact' ? theme.spacing(2) : theme.spacing(3),
  marginBottom: theme.spacing(3),
  borderRadius: theme.spacing(1.5),
  ...(variant === 'transparent' && {
    backgroundColor: 'transparent',
    boxShadow: 'none',
    padding: theme.spacing(1, 0, 2, 0),
  }),
}));

const PageTitle: React.FC<PageTitleProps> = ({
  title,
  description,
  breadcrumbs,
  action,
  variant = 'default',
  icon,
}) => {
  return (
    <StyledPaper variant={variant}>
      {breadcrumbs && breadcrumbs.length > 0 && (
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          aria-label="breadcrumb"
          sx={{ mb: 1.5, '& .MuiBreadcrumbs-ol': { flexWrap: 'nowrap' } }}
        >
          {breadcrumbs.map((breadcrumb, index) => {
            const isLast = index === breadcrumbs.length - 1;
            return isLast ? (
              <Typography key={index} color="text.primary" variant="body2">
                {breadcrumb.label}
              </Typography>
            ) : (
              <Link
                key={index}
                color="inherit"
                href={breadcrumb.href}
                variant="body2"
                underline="hover"
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                {breadcrumb.label}
              </Link>
            );
          })}
        </Breadcrumbs>
      )}

      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {icon && <Box sx={{ mr: 1.5, display: 'flex', color: 'primary.main' }}>{icon}</Box>}
          <Typography variant="h5" component="h1" fontWeight="medium">
            {title}
          </Typography>
        </Box>
        {action && <Box>{action}</Box>}
      </Box>

      {description && (
        <Typography 
          variant="body2" 
          color="text.secondary" 
          sx={{ 
            mt: 1,
            maxWidth: '800px',
            lineHeight: 1.6
          }}
        >
          {description}
        </Typography>
      )}
    </StyledPaper>
  );
};

export default PageTitle;