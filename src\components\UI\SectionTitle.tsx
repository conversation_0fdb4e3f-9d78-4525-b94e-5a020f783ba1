import React from 'react';
import { Box, Typography, Divider, Tooltip, IconButton, styled } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface SectionTitleProps {
  title: string;
  subtitle?: string;
  tooltip?: string;
  action?: React.ReactNode;
  divider?: boolean;
  icon?: React.ReactNode;
}

const Root = styled(Box)({
  marginBottom: '16px',
});

const SectionTitle: React.FC<SectionTitleProps> = ({
  title,
  subtitle,
  tooltip,
  action,
  divider = false,
  icon,
}) => {
  return (
    <Root>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 0.5 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {icon && <Box sx={{ mr: 1, color: 'primary.main' }}>{icon}</Box>}
          <Typography variant="h6" component="h2" fontWeight="medium">
            {title}
            {tooltip && (
              <Tooltip title={tooltip} arrow placement="top">
                <IconButton size="small" sx={{ ml: 0.5, p: 0.5 }}>
                  <HelpOutlineIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Typography>
        </Box>
        {action && <Box>{action}</Box>}
      </Box>

      {subtitle && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1, maxWidth: '800px' }}>
          {subtitle}
        </Typography>
      )}

      {divider && <Divider sx={{ mt: subtitle ? 1 : 1.5, mb: 2 }} />}
    </Root>
  );
};

export default SectionTitle;