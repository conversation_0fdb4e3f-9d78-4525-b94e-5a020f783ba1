import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  SelectProps,
  styled,
  SelectChangeEvent,
  Box,
  Chip,
  Typography,
  Checkbox,
  ListItemText,
  OutlinedInput,
} from '@mui/material';

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.spacing(1),
    backgroundColor: '#fff',
    transition: 'box-shadow 0.2s ease-in-out',
    
    '&:hover': {
      boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
    },
    
    '&.Mui-focused': {
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
    },
  },
}));

interface Option {
  value: string | number;
  label: string;
  disabled?: boolean;
  description?: string;
}

interface SelectFieldProps extends Omit<SelectProps, 'onChange'> {
  options: Option[];
  label?: string;
  helperText?: string;
  error?: boolean;
  fullWidth?: boolean;
  showEmptyOption?: boolean;
  emptyOptionLabel?: string;
  onChange?: (value: string | string[] | number | number[] | null) => void;
  multiple?: boolean;
  chips?: boolean;
  required?: boolean;
}

const SelectField: React.FC<SelectFieldProps> = ({
  options,
  label,
  helperText,
  error,
  fullWidth = true,
  showEmptyOption = false,
  emptyOptionLabel = 'None',
  value,
  onChange,
  multiple = false,
  chips = false,
  required = false,
  ...props
}) => {
  const handleChange = (event: SelectChangeEvent<unknown>) => {
    if (onChange) {
      onChange(event.target.value);
    }
  };

  const renderValue = (selected: unknown) => {
    if (multiple && chips && Array.isArray(selected)) {
      return (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {selected.map((value) => {
            const option = options.find((opt) => opt.value === value);
            return (
              <Chip
                key={value}
                label={option?.label || value}
                size="small"
                sx={{ borderRadius: 1 }}
              />
            );
          })}
        </Box>
      );
    }

    // Single select or non-chip display
    if (!selected) return emptyOptionLabel;
    
    if (Array.isArray(selected)) {
      const selectedLabels = selected.map(value => {
        const option = options.find(opt => opt.value === value);
        return option?.label || value;
      });
      return selectedLabels.join(', ');
    }
    
    const option = options.find(opt => opt.value === selected);
    return option?.label || selected;
  };

  return (
    <StyledFormControl fullWidth={fullWidth} error={error} required={required}>
      {label && <InputLabel id={`select-label-${label}`}>{label}</InputLabel>}
      <Select
        labelId={`select-label-${label}`}
        value={value}
        onChange={handleChange}
        renderValue={renderValue}
        label={label}
        input={<OutlinedInput />}
        multiple={multiple}
        {...props}
      >
        {showEmptyOption && !required && (
          <MenuItem value="">
            <em>{emptyOptionLabel}</em>
          </MenuItem>
        )}
        {options.map((option) => (
          <MenuItem 
            key={option.value} 
            value={option.value} 
            disabled={option.disabled}
          >
            {multiple && <Checkbox checked={Array.isArray(value) && value.indexOf(option.value) > -1} />}
            <ListItemText 
              primary={option.label} 
              secondary={option.description ? (
                <Typography variant="caption" color="text.secondary">
                  {option.description}
                </Typography>
              ) : null}
            />
          </MenuItem>
        ))}
      </Select>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </StyledFormControl>
  );
};

export default SelectField;