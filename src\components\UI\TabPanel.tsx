import React from 'react';
import { Box, BoxProps } from '@mui/material';

interface TabPanelProps extends BoxProps {
  children?: React.ReactNode;
  index: number;
  value: number;
  keepMounted?: boolean;
}

const TabPanel: React.FC<TabPanelProps> = ({
  children,
  value,
  index,
  keepMounted = false,
  sx,
  ...other
}) => {
  const hidden = value !== index;
  
  return (
    <Box
      role="tabpanel"
      hidden={hidden}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      sx={{
        height: '100%',
        width: '100%',
        overflow: 'auto',
        display: hidden ? 'none' : 'block',
        ...sx
      }}
      {...other}
    >
      {(keepMounted || value === index) && (
        <Box sx={{ height: '100%' }}>
          {children}
        </Box>
      )}
    </Box>
  );
};

export default TabPanel;