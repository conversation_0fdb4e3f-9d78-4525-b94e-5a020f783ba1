import React from 'react';
import { 
  Tabs as MuiTabs, 
  TabsProps as MuiTabsProps, 
  styled,
  Tab as MuiTab,
  TabProps as MuiTabProps,
  useTheme
} from '@mui/material';

// Use customVariant to avoid conflict with MUI's variant prop
export interface StyledTabsProps extends MuiTabsProps {
  customVariant?: 'default' | 'card' | 'pill'; 
}

export interface StyledTabProps extends MuiTabProps {
  disableRipple?: boolean;
  // Match MUI's expected icon type more closely
  icon?: React.ReactElement | string; 
  label: string;
}

const StyledTabs = styled(MuiTabs, {
  // Forward MUI's variant, but not our customVariant
  shouldForwardProp: (prop) => prop !== 'customVariant', 
})<StyledTabsProps>(({ theme, customVariant = 'default' }) => ({ // Use customVariant here
  minHeight: 48,
  
  '& .MuiTabs-indicator': {
    // Use customVariant for styling logic
    height: customVariant === 'default' ? 3 : 0, 
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  
  // Use customVariant for styling logic
  ...(customVariant === 'card' && { 
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.spacing(1),
    padding: theme.spacing(0.5),
    minHeight: 40,
    
    '& .MuiTab-root': {
      minHeight: 36,
      borderRadius: theme.spacing(0.75),
      transition: 'all 0.2s',
      
      '&.Mui-selected': {
        backgroundColor: theme.palette.primary.main,
        color: theme.palette.primary.contrastText,
      },
    },
  }),
  
  // Use customVariant for styling logic
  ...(customVariant === 'pill' && { 
    backgroundColor: 'transparent',
    
    '& .MuiTab-root': {
      minHeight: 40,
      borderRadius: '50px',
      padding: theme.spacing(0, 2),
      transition: 'all 0.2s',
      marginRight: theme.spacing(1),
      border: `1px solid ${theme.palette.divider}`,
      
      '&.Mui-selected': {
        backgroundColor: theme.palette.primary.main,
        color: theme.palette.primary.contrastText,
        borderColor: theme.palette.primary.main,
      },
    },
  }),
}));

const StyledTab = styled(MuiTab)<StyledTabProps>(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 500,
  minHeight: 48,
  padding: theme.spacing(0, 2),
  
  [theme.breakpoints.up('sm')]: {
    minWidth: 0,
    padding: theme.spacing(0, 2),
  },
}));

interface TabItem {
  label: string;
  // Match MUI's expected icon type more closely
  icon?: React.ReactElement | string; 
  disabled?: boolean;
}

interface TabsProps {
  items: TabItem[];
  value: number;
  onChange: (event: React.SyntheticEvent, newValue: number) => void;
  // Use customVariant here to match StyledTabsProps
  customVariant?: 'default' | 'card' | 'pill'; 
  centered?: boolean;
  scrollable?: boolean;
  tabsProps?: Partial<MuiTabsProps>;
}

const Tabs: React.FC<TabsProps> = ({
  items,
  value,
  onChange,
  // Use customVariant here, default to 'default'
  customVariant = 'default', 
  centered = false,
  scrollable = false,
  tabsProps,
}) => {
  const theme = useTheme();
  
  return (
    <StyledTabs
      value={value}
      onChange={onChange}
      // Pass MUI's variant prop correctly
      variant={scrollable ? 'scrollable' : 'standard'} 
      scrollButtons={scrollable ? 'auto' : false}
      allowScrollButtonsMobile={scrollable}
      centered={!scrollable && centered}
      // Pass our customVariant prop to StyledTabs
      customVariant={customVariant} 
      {...tabsProps}
    >
      {items.map((item, index) => (
        <StyledTab
          key={index}
          label={item.label}
          icon={item.icon}
          iconPosition="start"
          disabled={item.disabled}
          disableRipple
        />
      ))}
    </StyledTabs>
  );
};

export default Tabs;
