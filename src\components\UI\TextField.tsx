import React from 'react';

interface TextFieldProps {
  label?: string;
  value: string | number;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: 'text' | 'number' | 'email' | 'password';
  disabled?: boolean;
  error?: string;
  helperText?: string;
  fullWidth?: boolean;
  variant?: 'outlined' | 'filled' | 'standard';
}

const TextField: React.FC<TextFieldProps> = ({
  label,
  value,
  onChange,
  placeholder,
  type = 'text',
  disabled = false,
  error,
  helperText,
  fullWidth = false,
  variant = 'outlined'
}) => {
  return (
    <div className={`text-field-container ${fullWidth ? 'full-width' : ''}`}>
      {label && <label className="text-field-label">{label}</label>}
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        className={`text-field-input ${variant} ${error ? 'error' : ''}`}
      />
      {error && <div className="text-field-error">{error}</div>}
      {helperText && <div className="text-field-helper">{helperText}</div>}
    </div>
  );
};

export default TextField;
