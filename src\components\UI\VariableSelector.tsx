import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Chip,
  ListItemText,
  Checkbox,
  Autocomplete,
  TextField,
  Paper,
  Grid,
  Card,
  CardActionArea,
  CardContent,
  alpha,
  useTheme,
  Tooltip,
  IconButton,
  <PERSON>lapse,
  Divider,
  styled
} from '@mui/material';
import {
  Functions as FunctionsIcon,
  Category as CategoryIcon,
  CalendarMonth as CalendarMonthIcon,
  ExpandMore as ExpandMoreIcon,
  InfoOutlined as InfoOutlinedIcon,
  FilterAlt as FilterAltIcon,
  Check as CheckIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { Column, DataType } from '../../types';

export interface VariableSelectorProps {
  label?: string;
  helperText?: string;
  value?: string | string[];
  onChange?: (value: string | string[]) => void;
  datasetId?: string;
  multiple?: boolean;
  required?: boolean;
  allowedTypes?: DataType[];
  excludeTypes?: DataType[];
  size?: 'small' | 'medium';
  disabled?: boolean;
  error?: boolean;
  variant?: 'default' | 'card' | 'chip' | 'autocomplete';
  chipColor?: 'primary' | 'secondary' | 'default' | 'info' | 'success' | 'warning' | 'error';
  columnFilter?: (column: Column) => boolean;
  minSelections?: number;
  maxSelections?: number;
  showTypeIcons?: boolean;
  placeholder?: string;
  fullWidth?: boolean;
  grouped?: boolean;
  disabledValues?: string[]; // Add disabledValues prop
}

const StyledAutocomplete = styled(Autocomplete)(({ theme }) => ({
  '& .MuiAutocomplete-tag': {
    margin: 2,
  },
}));

const VariableSelector: React.FC<VariableSelectorProps> = ({
  label = 'Variables',
  helperText,
  value = '',
  onChange,
  datasetId,
  multiple = false,
  required = false,
  allowedTypes = [DataType.NUMERIC, DataType.CATEGORICAL, DataType.DATE],
  excludeTypes = [],
  size = 'medium',
  disabled = false,
  error = false,
  variant = 'default',
  chipColor = 'primary',
  columnFilter,
  minSelections = 0,
  maxSelections,
  showTypeIcons = true,
  placeholder = 'Select variables',
  fullWidth = true,
  grouped = false,
}) => {
  const { datasets, currentDataset: activeDataset } = useData();
  const theme = useTheme();
  const [infoExpanded, setInfoExpanded] = useState(false);
  
  // Find the dataset to use: preference to datasetId prop, then activeDataset
  const dataset = datasetId
    ? datasets.find(d => d.id === datasetId)
    : activeDataset;
  
  // Filter columns based on allowed/excluded types and custom filter
  const availableColumns: Column[] = dataset
    ? dataset.columns.filter(column => {
        // Apply allowed types filter
        if (!allowedTypes.includes(column.type)) {
          return false;
        }
        
        // Apply excluded types filter
        if (excludeTypes.includes(column.type)) {
          return false;
        }
        
        // Apply custom column filter if provided
        if (columnFilter && !columnFilter(column)) {
          return false;
        }
        
        return true;
      })
    : [];
  
  // Group columns by type if needed
  const groupedColumns = grouped
    ? {
        [DataType.NUMERIC]: availableColumns.filter(c => c.type === DataType.NUMERIC),
        [DataType.CATEGORICAL]: availableColumns.filter(c => c.type === DataType.CATEGORICAL),
        [DataType.DATE]: availableColumns.filter(c => c.type === DataType.DATE),
      }
    : null;
  
  // Set up state for internal value handling
  const [internalValue, setInternalValue] = useState<string | string[]>(value);
  
  // Update internal value when prop changes
  useEffect(() => {
    setInternalValue(value);
  }, [value]);
  
  // Handle change events and propagate to parent
  const handleChange = (newValue: string | string[]) => {
    setInternalValue(newValue);
    if (onChange) {
      onChange(newValue);
    }
  };
  
  // Get icon based on variable type
  const getTypeIcon = (type: DataType) => {
    switch (type) {
      case DataType.NUMERIC:
        return <FunctionsIcon fontSize="small" sx={{ color: theme.palette.primary.main }} />;
      case DataType.CATEGORICAL:
        return <CategoryIcon fontSize="small" sx={{ color: theme.palette.secondary.main }} />;
      case DataType.DATE:
        return <CalendarMonthIcon fontSize="small" sx={{ color: theme.palette.info.main }} />;
      default:
        return null;
    }
  };
  
  // Helper function to format column for display
  const getColumnDisplay = (column: Column) => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {showTypeIcons && (
          <Box sx={{ mr: 1 }}>
            {getTypeIcon(column.type)}
          </Box>
        )}
        <Typography>
          {column.name}
        </Typography>
      </Box>
    );
  };
  
  // Check if a column is selected
  const isSelected = (columnName: string) => {
    if (multiple && Array.isArray(internalValue)) {
      return internalValue.includes(columnName);
    }
    return internalValue === columnName;
  };
  
  // Render appropriate variant
  switch (variant) {
    case 'card':
      return (
        <Box sx={{ mt: 1, mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" fontWeight="medium">
              {label}
              {required && ' *'}
            </Typography>
            
            {availableColumns.length > 0 && (
              <Box>
                <Tooltip title="Variable information">
                  <IconButton
                    size="small"
                    onClick={() => setInfoExpanded(!infoExpanded)}
                  >
                    <ExpandMoreIcon sx={{ 
                      transform: infoExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                      transition: 'transform 0.3s'
                    }} />
                  </IconButton>
                </Tooltip>
              </Box>
            )}
          </Box>
          
          {helperText && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {helperText}
            </Typography>
          )}
          
          {availableColumns.length === 0 ? (
            <Paper
              elevation={0}
              sx={{
                p: 2,
                borderStyle: 'dashed',
                borderWidth: 1,
                borderColor: theme.palette.divider,
                backgroundColor: alpha(theme.palette.background.default, 0.5),
                borderRadius: 1,
                textAlign: 'center',
              }}
            >
              <Typography variant="body2" color="text.secondary">
                No variables available for selection
              </Typography>
            </Paper>
          ) : (
            <>
              <Collapse in={infoExpanded} timeout="auto" unmountOnExit>
                <Paper sx={{ p: 2, mb: 2, backgroundColor: alpha(theme.palette.background.default, 0.5) }}>
                  <Typography variant="body2" fontWeight="medium">
                    Available Variables
                  </Typography>
                  <Divider sx={{ my: 1 }} />
                  <Grid container spacing={1}>
                    {availableColumns.map((column) => (
                      <Grid item xs={12} sm={6} md={4} key={column.name}>
                        <Box sx={{ display: 'flex', alignItems: 'center', p: 0.5 }}>
                          {getTypeIcon(column.type)}
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            {column.name}
                          </Typography>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Paper>
              </Collapse>
              
              <Grid container spacing={1}>
                {availableColumns.map((column) => (
                  <Grid item xs={12} sm={6} md={4} key={column.name}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        border: isSelected(column.name) 
                          ? `2px solid ${theme.palette.primary.main}`
                          : `2px solid transparent`,
                        transition: 'all 0.2s',
                        backgroundColor: isSelected(column.name)
                          ? alpha(theme.palette.primary.main, 0.05)
                          : theme.palette.background.paper,
                        '&:hover': {
                          backgroundColor: isSelected(column.name)
                            ? alpha(theme.palette.primary.main, 0.1)
                            : alpha(theme.palette.background.default, 0.5),
                        }
                      }}
                      onClick={() => {
                        if (multiple) {
                          const currentValue = Array.isArray(internalValue) ? internalValue : [];
                          if (currentValue.includes(column.name)) {
                            // Remove if already selected
                            handleChange(currentValue.filter(v => v !== column.name));
                          } else {
                            // Add if not selected and under max selections
                            if (!maxSelections || currentValue.length < maxSelections) {
                              handleChange([...currentValue, column.name]);
                            }
                          }
                        } else {
                          // Single selection
                          handleChange(column.name);
                        }
                      }}
                    >
                      <CardActionArea sx={{ p: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {getTypeIcon(column.type)}
                            <Typography variant="body2" sx={{ ml: 1, fontWeight: isSelected(column.name) ? 500 : 400 }}>
                              {column.name}
                            </Typography>
                          </Box>
                          
                          {isSelected(column.name) && (
                            <CheckIcon color="primary" fontSize="small" />
                          )}
                        </Box>
                      </CardActionArea>
                    </Card>
                  </Grid>
                ))}
              </Grid>
              
              {multiple && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Selected: {Array.isArray(internalValue) ? internalValue.length : 0}
                    {maxSelections ? ` / ${maxSelections}` : ''}
                  </Typography>
                </Box>
              )}
              
              {error && (
                <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
                  {multiple 
                    ? `Please select ${minSelections} to ${maxSelections || 'unlimited'} variables`
                    : 'Please select a variable'}
                </Typography>
              )}
            </>
          )}
        </Box>
      );
      
    case 'chip':
      return (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            {label}
            {required && ' *'}
          </Typography>
          
          {helperText && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {helperText}
            </Typography>
          )}
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {availableColumns.map((column) => (
              <Chip
                key={column.name}
                label={column.name}
                color={isSelected(column.name) ? chipColor : 'default'}
                variant={isSelected(column.name) ? 'filled' : 'outlined'}
                onClick={() => {
                  if (multiple) {
                    const currentValue = Array.isArray(internalValue) ? internalValue : [];
                    if (currentValue.includes(column.name)) {
                      // Remove if already selected
                      handleChange(currentValue.filter(v => v !== column.name));
                    } else {
                      // Add if not selected and under max selections
                      if (!maxSelections || currentValue.length < maxSelections) {
                        handleChange([...currentValue, column.name]);
                      }
                    }
                  } else {
                    // Single selection
                    handleChange(isSelected(column.name) ? '' : column.name);
                  }
                }}
                icon={showTypeIcons ? getTypeIcon(column.type) || undefined : undefined}
                disabled={disabled || (multiple && maxSelections ? (Array.isArray(internalValue) && internalValue.length >= maxSelections && !isSelected(column.name)) : false)}
              />
            ))}
          </Box>
          
          {error && (
            <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
              {multiple 
                ? `Please select ${minSelections} to ${maxSelections || 'unlimited'} variables`
                : 'Please select a variable'}
            </Typography>
          )}
        </Box>
      );
    
    case 'autocomplete':
      return (
        <StyledAutocomplete
          multiple={multiple}
          options={availableColumns.map(col => col.name)}
          value={multiple ? (Array.isArray(internalValue) ? internalValue : []) : (internalValue || null)}
          onChange={(_, newValue) => handleChange(newValue === null ? '' : (newValue as string | string[]))}
          groupBy={grouped ? (option) => {
            const column = availableColumns.find(col => col.name === option);
            if (column) {
              switch (column.type) {
                case DataType.NUMERIC: return 'Numeric';
                case DataType.CATEGORICAL: return 'Categorical';
                case DataType.DATE: return 'Date';
                default: return 'Other';
              }
            }
            return 'Other';
          } : undefined}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => {
              const column = availableColumns.find(col => col.name === option);
              return (
                <Chip
                  key={option}
                  {...(getTagProps({ index }) as any)} // Cast to any to bypass type error
                  label={option}
                  size={size}
                  color={chipColor}
                  icon={showTypeIcons && column ? getTypeIcon(column.type) || undefined : undefined}
                />
              );
            })
          }
          renderOption={(props, option) => {
            const column = availableColumns.find(col => col.name === option);
            return (
              <li {...(props as any)} key={option}> {/* Cast props to any */}
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {showTypeIcons && column && (
                    <Box sx={{ mr: 1 }}>
                      {getTypeIcon(column.type)}
                    </Box>
                  )}
                  {option}
                </Box>
              </li>
            );
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              label={label + (required ? ' *' : '')}
              placeholder={placeholder}
              size={size}
              error={error}
              helperText={
                error
                  ? multiple
                    ? `Please select ${minSelections} to ${maxSelections || 'unlimited'} variables`
                    : 'Please select a variable'
                  : helperText
              }
              required={required}
              fullWidth={fullWidth}
            />
          )}
          disabled={disabled}
          fullWidth={fullWidth}
          disableCloseOnSelect={multiple}
          limitTags={3}
        />
      );
      
    case 'default':
    default:
      return (
        <FormControl 
          fullWidth={fullWidth}
          size={size}
          error={error}
          required={required}
          disabled={disabled}
          sx={{ mb: 2 }}
        >
          <InputLabel id={`variable-select-${label.replace(/\s+/g, '-').toLowerCase()}`}>
            {label}
          </InputLabel>
          
          <Select
            labelId={`variable-select-${label.replace(/\s+/g, '-').toLowerCase()}`}
            value={internalValue}
            onChange={(e) => handleChange(e.target.value as string | string[])}
            label={label}
            multiple={multiple}
            renderValue={(selected) => {
              if (multiple && Array.isArray(selected)) {
                return (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => {
                      const column = availableColumns.find(col => col.name === value);
                      return (
                        <Chip
                          key={value}
                          label={value}
                          size="small"
                          color={chipColor}
                          icon={(showTypeIcons && column ? getTypeIcon(column.type) : undefined) || undefined}
                        />
                      );
                    })}
                  </Box>
                );
              }
              
              const column = availableColumns.find(col => col.name === selected);
              if (showTypeIcons && column) {
                return (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {getTypeIcon(column.type)}
                    <Box sx={{ ml: 1 }}>{selected}</Box>
                  </Box>
                );
              }
              
              return selected;
            }}
          >
            {grouped && groupedColumns ? (
              <>
                {groupedColumns[DataType.NUMERIC].length > 0 && (
                  [
                    <MenuItem key="numeric-header" disabled divider>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <FunctionsIcon fontSize="small" sx={{ mr: 1, color: theme.palette.primary.main }} />
                        <Typography variant="body2" fontWeight="medium">
                          Numeric Variables
                        </Typography>
                      </Box>
                    </MenuItem>,
                    ...groupedColumns[DataType.NUMERIC].map((column) => (
                      <MenuItem key={column.name} value={column.name}>
                        {multiple ? (
                          <>
                            <Checkbox checked={isSelected(column.name)} />
                            <ListItemText primary={column.name} />
                          </>
                        ) : (
                          getColumnDisplay(column)
                        )}
                      </MenuItem>
                    ))
                  ]
                )}
                
                {groupedColumns[DataType.CATEGORICAL].length > 0 && (
                  [
                    <MenuItem key="categorical-header" disabled divider>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <CategoryIcon fontSize="small" sx={{ mr: 1, color: theme.palette.secondary.main }} />
                        <Typography variant="body2" fontWeight="medium">
                          Categorical Variables
                        </Typography>
                      </Box>
                    </MenuItem>,
                    ...groupedColumns[DataType.CATEGORICAL].map((column) => (
                      <MenuItem key={column.name} value={column.name}>
                        {multiple ? (
                          <>
                            <Checkbox checked={isSelected(column.name)} />
                            <ListItemText primary={column.name} />
                          </>
                        ) : (
                          getColumnDisplay(column)
                        )}
                      </MenuItem>
                    ))
                  ]
                )}
                
                {groupedColumns[DataType.DATE].length > 0 && (
                  [
                    <MenuItem key="date-header" disabled divider>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <CalendarMonthIcon fontSize="small" sx={{ mr: 1, color: theme.palette.info.main }} />
                        <Typography variant="body2" fontWeight="medium">
                          Date Variables
                        </Typography>
                      </Box>
                    </MenuItem>,
                    ...groupedColumns[DataType.DATE].map((column) => (
                      <MenuItem key={column.name} value={column.name}>
                        {multiple ? (
                          <>
                            <Checkbox checked={isSelected(column.name)} />
                            <ListItemText primary={column.name} />
                          </>
                        ) : (
                          getColumnDisplay(column)
                        )}
                      </MenuItem>
                    ))
                  ]
                )}
              </>
            ) : (
              availableColumns.map((column) => (
                <MenuItem key={column.name} value={column.name}>
                  {multiple ? (
                    <>
                      <Checkbox checked={isSelected(column.name)} />
                      <ListItemText primary={column.name} />
                    </>
                  ) : (
                    getColumnDisplay(column)
                  )}
                </MenuItem>
              ))
            )}
          </Select>
          
          {helperText && <FormHelperText>{helperText}</FormHelperText>}
        </FormControl>
      );
  }
};

export default VariableSelector;
