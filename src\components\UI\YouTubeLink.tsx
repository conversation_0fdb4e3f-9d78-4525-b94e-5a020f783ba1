import React, { useState } from 'react';
import {
  Link,
  Tooltip,
  Box,
  Typography,
  Card,
  CardMedia,
  CardContent,
  Fade,
  IconButton
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  OpenInNew as OpenInNewIcon
} from '@mui/icons-material';
import { YouTubeVideoInfo } from '../../utils/youtubeUtils';

interface YouTubeLinkProps {
  url: string;
  info: YouTubeVideoInfo;
  variant?: 'inline' | 'card';
  showPreview?: boolean;
}

const YouTubeLink: React.FC<YouTubeLinkProps> = ({ 
  url, 
  info, 
  variant = 'inline',
  showPreview = true 
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent notification menu from closing immediately
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const handleImageError = () => {
    setImageError(true);
  };

  // Simple inline link version
  if (variant === 'inline') {
    const tooltipContent = showPreview && !imageError ? (
      <Card sx={{ maxWidth: 300, m: 1 }}>
        <CardMedia
          component="img"
          height="140"
          image={info.thumbnailUrl}
          alt="YouTube video thumbnail"
          onLoad={handleImageLoad}
          onError={handleImageError}
          sx={{
            objectFit: 'cover',
            backgroundColor: 'grey.200'
          }}
        />
        <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PlayIcon color="error" fontSize="small" />
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              YouTube Video
            </Typography>
          </Box>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
            Click to watch in new tab
          </Typography>
        </CardContent>
      </Card>
    ) : null;

    return (
      <Tooltip
        title={tooltipContent || "Watch YouTube video"}
        placement="top"
        arrow
        enterDelay={300}
        leaveDelay={200}
        componentsProps={{
          tooltip: {
            sx: { p: 0, backgroundColor: 'transparent', boxShadow: 'none' }
          }
        }}
      >
        <Link
          component="button"
          onClick={handleClick}
          sx={{
            color: 'error.main',
            textDecoration: 'none',
            display: 'inline-flex',
            alignItems: 'center',
            gap: 0.5,
            fontWeight: 500,
            fontSize: 'inherit',
            '&:hover': {
              textDecoration: 'underline',
              color: 'error.dark'
            },
            cursor: 'pointer',
            border: 'none',
            background: 'none',
            padding: 0,
            font: 'inherit'
          }}
        >
          <PlayIcon fontSize="small" />
          Watch Video
          <OpenInNewIcon fontSize="small" sx={{ ml: 0.5, opacity: 0.7 }} />
        </Link>
      </Tooltip>
    );
  }

  // Card version for admin preview
  return (
    <Card 
      sx={{ 
        maxWidth: 400, 
        cursor: 'pointer',
        '&:hover': {
          boxShadow: 2
        }
      }}
      onClick={handleClick}
    >
      <Box sx={{ position: 'relative' }}>
        <CardMedia
          component="img"
          height="200"
          image={info.thumbnailUrl}
          alt="YouTube video thumbnail"
          onLoad={handleImageLoad}
          onError={handleImageError}
          sx={{
            objectFit: 'cover',
            backgroundColor: 'grey.200'
          }}
        />
        <Fade in={imageLoaded && !imageError}>
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              borderRadius: '50%',
              width: 60,
              height: 60,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <PlayIcon sx={{ color: 'white', fontSize: 32 }} />
          </Box>
        </Fade>
      </Box>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <PlayIcon color="error" fontSize="small" />
          <Typography variant="body2" color="error" sx={{ fontWeight: 500 }}>
            YouTube Video
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary">
          {url}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default YouTubeLink;
