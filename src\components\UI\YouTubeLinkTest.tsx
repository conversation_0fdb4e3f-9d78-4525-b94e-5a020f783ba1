import React from 'react';
import { Box, Typography, Paper, Divider } from '@mui/material';
import NotificationRichText from './NotificationRichText';
import YouTubeLink from './YouTubeLink';
import { getYouTubeVideoInfo } from '../../utils/youtubeUtils';

const YouTubeLinkTest: React.FC = () => {
  const testMessages = [
    "Check out this great tutorial: https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "Multiple videos: https://youtu.be/dQw4w9WgXcQ and https://www.youtube.com/watch?v=oHg5SJYRHA0",
    "Mobile link: https://m.youtube.com/watch?v=dQw4w9WgXcQ works too!",
    "Regular text without any links should display normally.",
    "Mixed content with https://www.youtube.com/watch?v=dQw4w9WgXcQ and some text after the link."
  ];

  const sampleVideoInfo = getYouTubeVideoInfo("https://www.youtube.com/watch?v=dQw4w9WgXcQ");

  return (
    <Box sx={{ p: 3, maxWidth: 800 }}>
      <Typography variant="h4" gutterBottom>
        YouTube Link Enhancement Test
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Test Messages with YouTube Links:
      </Typography>
      
      {testMessages.map((message, index) => (
        <Paper key={index} sx={{ p: 2, mb: 2, backgroundColor: 'grey.50' }}>
          <Typography variant="subtitle2" color="primary" gutterBottom>
            Test {index + 1}:
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontStyle: 'italic' }}>
            Input: "{message}"
          </Typography>
          <Divider sx={{ my: 1 }} />
          <Typography variant="subtitle2" gutterBottom>
            Rendered Output:
          </Typography>
          <NotificationRichText
            text={message}
            variant="body2"
            color="text.primary"
            showYouTubePreview={true}
          />
        </Paper>
      ))}

      <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
        Individual YouTube Link Component:
      </Typography>
      
      {sampleVideoInfo && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Inline Version:
          </Typography>
          <YouTubeLink
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ"
            info={sampleVideoInfo}
            variant="inline"
            showPreview={true}
          />
          
          <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
            Card Version:
          </Typography>
          <YouTubeLink
            url="https://www.youtube.com/watch?v=dQw4w9WgXcQ"
            info={sampleVideoInfo}
            variant="card"
            showPreview={true}
          />
        </Paper>
      )}

      <Paper sx={{ p: 2, backgroundColor: 'info.light', color: 'info.contrastText' }}>
        <Typography variant="body2">
          <strong>Features Implemented:</strong>
          <br />
          ✓ Automatic YouTube link detection
          <br />
          ✓ Clickable links that open in new tab
          <br />
          ✓ Hover preview with video thumbnail
          <br />
          ✓ Support for multiple YouTube URL formats
          <br />
          ✓ Graceful fallback for non-YouTube text
          <br />
          ✓ Integration with notification system
          <br />
          ✓ Admin preview in NotificationManager
        </Typography>
      </Paper>
    </Box>
  );
};

export default YouTubeLinkTest;
