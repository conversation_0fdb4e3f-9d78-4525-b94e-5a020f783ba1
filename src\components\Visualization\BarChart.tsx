import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  SelectChangeEvent,
  Slider,
  TextField,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  CircularProgress,
  useTheme,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Fade
} from '@mui/material';
import {
  BarChart as BarChartIconMui, // Renamed to avoid conflict
  Tune as TuneIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  DataObject as DataObjectIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, Column } from '../../types';
import { calculateFrequencies, calculateMean } from '@/utils/stats';
import { getOrderedChartData, getOrderedCategoriesByColumnId } from '../../utils/dataUtilities';
import * as Plotly from 'plotly.js'; // Import Plotly

// Define Plotly types
type PlotlyData = Partial<Plotly.PlotData>;
type PlotlyLayout = Partial<Plotly.Layout>;
type PlotlyConfig = Partial<Plotly.Config>;

// Define valid color scheme names
const colorSchemes = {
  default: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
  pastel: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5', '#d9d9d9', '#bc80bd'],
  bold: ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628', '#f781bf', '#999999'],
  sequential: ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#08519c', '#08306b'],
  diverging: ['#a50026', '#d73027', '#f46d43', '#fdae61', '#fee090', '#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
};
type ColorSchemeName = keyof typeof colorSchemes;

// Chart Settings Interface
interface ChartSettings {
  title: string;
  xAxisLabel: string;
  yAxisLabel: string;
  showValuesOnBars: boolean;
  barMode: 'group' | 'stack' | 'relative';
  orientation: 'v' | 'h';
  colorScheme: ColorSchemeName;
  legendPosition: { x: number; y: number; xanchor?: 'auto' | 'left' | 'center' | 'right'; yanchor?: 'auto' | 'top' | 'middle' | 'bottom'; };
  barGap: number;
  sortBars: 'none' | 'ascending' | 'descending';
  maxBars: number;
}

// Default settings
const defaultChartSettings: ChartSettings = {
  title: 'Bar Chart',
  xAxisLabel: 'Categories',
  yAxisLabel: 'Values',
  showValuesOnBars: true,
  barMode: 'group',
  orientation: 'v',
  colorScheme: 'default',
  legendPosition: { y: -0.2, x: 0.5, xanchor: 'center', yanchor: 'top' },
  barGap: 0.2,
  sortBars: 'none',
  maxBars: 15,
};

const PLOTLY_BAR_CHART_DIV_ID = 'plotlyBarChartDiv';

const BarChart: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const plotlyDivRef = useRef<HTMLDivElement>(null);
  
  // State
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [categoryVariable, setCategoryVariable] = useState<string>('');
  const [valueVariables, setValueVariables] = useState<string[]>([]);
  const [aggregationMethod, setAggregationMethod] = useState<string>('sum');
  const [displayMode, setDisplayMode] = useState<'frequency' | 'percentage'>('frequency');
  const [chartSettings, setChartSettings] = useState<ChartSettings>(defaultChartSettings);
  const [activePanel, setActivePanel] = useState<'variables' | 'settings'>('variables');
  const [plotlyConfig, setPlotlyConfig] = useState<{ data: PlotlyData[], layout: PlotlyLayout } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const activeDataset = React.useMemo(() => {
    if (!selectedDatasetId) return null;
    return datasets.find(ds => ds.id === selectedDatasetId) || null;
  }, [datasets, selectedDatasetId]);
  
  // Memoized columns
  const categoricalColumns = React.useMemo(() => 
    activeDataset?.columns.filter(col => col.type === DataType.CATEGORICAL) || [],
    [activeDataset]
  );
  const numericColumns = React.useMemo(() =>
    activeDataset?.columns.filter(col => col.type === DataType.NUMERIC) || [],
    [activeDataset]
  );
  
  // Effect to update selected dataset ID
  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
      setCategoryVariable('');
      setValueVariables([]);
      setPlotlyConfig(null);
      setError(null);
    }
  }, [currentDataset]); // Dependency array changed to [currentDataset]

  // Effect to render Plotly chart
  useEffect(() => {
    if (plotlyConfig && plotlyDivRef.current) {
      const config: PlotlyConfig = {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d']
      };

      // Enhanced layout for better positioning
      const enhancedLayout = {
        ...plotlyConfig.layout,
        height: 500,
        width: undefined, // Let it be responsive
        autosize: true
      };

      Plotly.newPlot(PLOTLY_BAR_CHART_DIV_ID, plotlyConfig.data, enhancedLayout, config);
    }
    return () => {
      if (plotlyDivRef.current && typeof Plotly !== 'undefined' && Plotly.purge) {
        Plotly.purge(plotlyDivRef.current);
      }
    };
  }, [plotlyConfig]);

  // Auto-generate chart when variables change
  useEffect(() => {
    if (categoryVariable && activeDataset) {
      generatePlotlyData();
    }
  }, [categoryVariable, valueVariables, aggregationMethod, displayMode, chartSettings, activeDataset]);

  // Keyboard accessibility for panel switching
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + 1 for Variables panel, Alt + 2 for Settings panel
      if (event.altKey && !event.ctrlKey && !event.shiftKey) {
        if (event.key === '1') {
          event.preventDefault();
          setActivePanel('variables');
        } else if (event.key === '2') {
          event.preventDefault();
          setActivePanel('settings');
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);
  
  // --- Handlers ---
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    setSelectedDatasetId(event.target.value);
    setCategoryVariable('');
    setValueVariables([]);
    setPlotlyConfig(null);
    setError(null);
  };

  const handleCategoryVariableChange = (event: SelectChangeEvent<string>) => {
    setCategoryVariable(event.target.value);
  };

  const handleValueVariablesChange = (event: SelectChangeEvent<string[]>) => {
    const values = event.target.value;
    setValueVariables(typeof values === 'string' ? values.split(',') : values);
  };

  const handleAggregationMethodChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAggregationMethod(event.target.value);
  };

  const handleDisplayModeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDisplayMode(event.target.value as 'frequency' | 'percentage');
  };

  const handleSettingsChange = (setting: keyof ChartSettings, value: any) => {
    if (setting === 'barMode') value = value as 'group' | 'stack' | 'relative';
    if (setting === 'orientation') value = value as 'v' | 'h';
    if (setting === 'colorScheme') value = value as ColorSchemeName;
    if (setting === 'sortBars') value = value as 'none' | 'ascending' | 'descending';
    setChartSettings(prev => ({ ...prev, [setting]: value }));
  };

  // --- Data Generation for Plotly ---
  const generatePlotlyData = () => {
    if (!activeDataset || !categoryVariable) { // Changed currentDataset to activeDataset
      setError('Please select a dataset and a category variable.');
      return;
    }
    
    setLoading(true);
    setError(null);
    setPlotlyConfig(null);

    try {
      const categoryCol = activeDataset.columns.find(col => col.id === categoryVariable); // Changed currentDataset to activeDataset
      if (!categoryCol) throw new Error('Category column not found.');

      let plotData: PlotlyData[] = [];
      let layoutUpdate: Partial<PlotlyLayout> = {};
      let processedDataForSort: any[] = []; 
      let dataKeyForSort = '';
      let currentYAxisLabel = defaultChartSettings.yAxisLabel; 

      // Case 1: Frequency/Percentage
      if (valueVariables.length === 0) {
        // Use ordered chart data for consistent category ordering
        const chartData = getOrderedChartData(categoryVariable, null, activeDataset, 'count');
        const totalCount = activeDataset.data.length;
        if (totalCount === 0) throw new Error('No data for category variable.');

        processedDataForSort = chartData.map(({ category, value }) => ({
          category,
          value: displayMode === 'percentage' ? (value / totalCount) * 100 : value
        }));

        currentYAxisLabel = displayMode === 'percentage' ? 'Percentage (%)' : 'Frequency';
        dataKeyForSort = 'value';
        layoutUpdate.showlegend = false;
      }
      // Case 2: Aggregation
      else {
        const valueColumns = valueVariables.map(valId => 
          activeDataset.columns.find(col => col.id === valId) // Changed currentDataset to activeDataset
        ).filter((col): col is Column => !!col); 
        if (valueColumns.length !== valueVariables.length) throw new Error('One or more value columns not found.');

        const groupedData: { [key: string]: { [key: string]: number[] } } = {};
        activeDataset.data.forEach(row => { // Changed currentDataset to activeDataset
          const categoryValue = String(row[categoryCol.name]);
          if (!groupedData[categoryValue]) {
            groupedData[categoryValue] = {};
            valueColumns.forEach(col => { groupedData[categoryValue][col.name] = []; });
          }
          valueColumns.forEach(col => {
            const val = row[col.name];
            if (typeof val === 'number' && !isNaN(val)) {
              groupedData[categoryValue][col.name].push(val);
            }
          });
        });

        // Use ordered categories for consistent ordering
        const orderedCategories = getOrderedCategoriesByColumnId(categoryVariable, activeDataset);
        processedDataForSort = orderedCategories.map(category => {
          const dataPoint: { [key: string]: any } = { category };
          valueColumns.forEach(col => {
            const values = groupedData[category]?.[col.name] || [];
            if (values.length === 0) dataPoint[col.name] = 0;
            else {
              switch (aggregationMethod) {
                case 'sum': dataPoint[col.name] = values.reduce((s, v) => s + v, 0); break;
                case 'average': dataPoint[col.name] = calculateMean(values); break;
                case 'count': dataPoint[col.name] = values.length; break;
                case 'max': dataPoint[col.name] = Math.max(...values); break;
                case 'min': dataPoint[col.name] = Math.min(...values); break;
                default: dataPoint[col.name] = values.reduce((s, v) => s + v, 0);
              }
            }
          });
          return dataPoint;
        });

        if (valueVariables.length === 1) dataKeyForSort = valueColumns[0].name;
        else dataKeyForSort = ''; 

        currentYAxisLabel = valueVariables.length === 1 
          ? `${aggregationMethod.charAt(0).toUpperCase() + aggregationMethod.slice(1)} of ${valueColumns[0].name}`
          : 'Aggregated Value';
        layoutUpdate.showlegend = true;
      }

      // Apply Sorting
      if (chartSettings.sortBars !== 'none' && dataKeyForSort) {
        processedDataForSort.sort((a, b) => {
          const valA = a[dataKeyForSort] ?? 0;
          const valB = b[dataKeyForSort] ?? 0;
          return chartSettings.sortBars === 'ascending' ? valA - valB : valB - valA;
        });
      }

      // Apply Max Bars Limit
      if (processedDataForSort.length > chartSettings.maxBars) {
        processedDataForSort = processedDataForSort.slice(0, chartSettings.maxBars);
      }

      // --- Create Plotly Traces ---
      const categories = processedDataForSort.map(d => d.category);
      const colors = getChartColors();

      // Improved text formatting with proper decimal places
      const getTextFormat = (values: number[]) => {
        const maxValue = Math.max(...values);
        if (displayMode === 'percentage') return '.1f';
        if (maxValue < 1) return '.2f';
        if (maxValue < 100) return '.1f';
        return '.0f'; // No decimals for larger numbers
      };

      if (valueVariables.length === 0) {
        const values = processedDataForSort.map(d => d.value);
        const textFormat = getTextFormat(values);
        plotData.push({
          x: chartSettings.orientation === 'v' ? categories : values,
          y: chartSettings.orientation === 'v' ? values : categories,
          type: 'bar', name: currentYAxisLabel, orientation: chartSettings.orientation,
          marker: { color: colors[0] },
          text: chartSettings.showValuesOnBars ? values : undefined,
          texttemplate: chartSettings.showValuesOnBars ? `%{text:${textFormat}}${displayMode === 'percentage' ? '%' : ''}` : undefined,
          textposition: 'auto', hoverinfo: 'x+y+name',
        });
      } else {
        const valueColumns = valueVariables.map(valId => numericColumns.find(col => col.id === valId)).filter((c): c is Column => !!c);
        valueColumns.forEach((col, index) => {
          const values = processedDataForSort.map(d => d[col.name]);
          const textFormat = getTextFormat(values);
          plotData.push({
            x: chartSettings.orientation === 'v' ? categories : values,
            y: chartSettings.orientation === 'v' ? values : categories,
            type: 'bar', name: col.name, orientation: chartSettings.orientation,
            marker: { color: colors[index % colors.length] },
            text: chartSettings.showValuesOnBars ? values : undefined,
            texttemplate: chartSettings.showValuesOnBars ? `%{text:${textFormat}}` : undefined,
            textposition: 'auto', hoverinfo: 'x+y+name',
          });
        });
      }

      // --- Create Plotly Layout ---
      const layout: PlotlyLayout = {
        title: { text: chartSettings.title, font: { size: 16 } },
        xaxis: { title: { text: chartSettings.orientation === 'v' ? chartSettings.xAxisLabel : currentYAxisLabel }, automargin: true },
        yaxis: { title: { text: chartSettings.orientation === 'v' ? currentYAxisLabel : chartSettings.xAxisLabel }, automargin: true },
        barmode: valueVariables.length > 1 ? chartSettings.barMode : undefined,
        bargap: chartSettings.barGap,
        margin: { t: 50, b: 50, l: 70, r: 30 }, // Optimized margins for better positioning
        paper_bgcolor: theme.palette.mode === 'dark' ? '#333' : '#fff',
        plot_bgcolor: theme.palette.mode === 'dark' ? '#222' : '#fff',
        font: { color: theme.palette.text.primary },
        legend: {
           ...chartSettings.legendPosition,
           bgcolor: theme.palette.mode === 'dark' ? 'rgba(51,51,51,0.8)' : 'rgba(255,255,255,0.8)',
           bordercolor: theme.palette.divider, borderwidth: 1, orientation: 'h'
        },
        autosize: true,
        ...layoutUpdate
      };
      if (chartSettings.orientation === 'v') layout.xaxis = { ...layout.xaxis, type: 'category' };
      else layout.yaxis = { ...layout.yaxis, type: 'category' };

      setPlotlyConfig({ data: plotData, layout });

    } catch (err) {
      setError(`Error generating chart data: ${err instanceof Error ? err.message : String(err)}`);
      setPlotlyConfig(null);
    } finally {
      setLoading(false);
    }
  };
  
  // Reset settings
  const resetChartSettings = () => {
    setChartSettings(defaultChartSettings);
  };
  
  // Download chart
  const downloadChart = () => {
    if (plotlyDivRef.current && plotlyConfig) {
      const downloadOpts: Plotly.DownloadImgopts = {
        format: 'svg',
        filename: chartSettings.title.replace(/\s+/g, '_') || 'barchart',
        width: plotlyDivRef.current.offsetWidth || 800,
        height: plotlyDivRef.current.offsetHeight || 500,
      };
      Plotly.downloadImage(PLOTLY_BAR_CHART_DIV_ID, downloadOpts);
    } else {
      setError('Chart data not available for download.');
    }
  };
  
  // Generate bar chart with current settings
  const generateBarChart = () => {
    generatePlotlyData();
  };
  
  // Get colors
  const getChartColors = () => {
    return colorSchemes[chartSettings.colorScheme] || colorSchemes.default;
  };

  // Button disabled states
  const isGenerateDisabled = !categoryVariable || loading;
  const isDownloadDisabled = !plotlyConfig || loading;
  
  // --- Render ---
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>Bar Chart Generator</Typography>

      {/* Three-Panel Layout */}
      <Grid container spacing={2}>
        {/* Left Panel Container - Variables or Settings */}
        <Grid item xs={12} sm={12} md={3} lg={3}>
          {/* Panel Toggle Tabs */}
          <Paper
            elevation={1}
            sx={{
              mb: 1,
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)'
            }}
          >
            <Tabs
              value={activePanel}
              onChange={(_, newValue) => setActivePanel(newValue)}
              variant="fullWidth"
              sx={{
                minHeight: 44,
                '& .MuiTab-root': {
                  minHeight: 44,
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  color: theme.palette.text.secondary,
                  textTransform: 'none',
                  transition: 'all 0.2s ease-in-out',
                  '&.Mui-selected': {
                    color: theme.palette.primary.main,
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(25, 118, 210, 0.08)',
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
                  }
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                }
              }}
            >
              <Tooltip title="Variable Selection Panel" placement="top">
                <Tab
                  value="variables"
                  label="Variables"
                  icon={<DataObjectIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
              <Tooltip title="Chart Settings Panel" placement="top">
                <Tab
                  value="settings"
                  label="Settings"
                  icon={<SettingsIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
            </Tabs>
          </Paper>

          {/* Variable Selection Panel */}
          <Fade in={activePanel === 'variables'} timeout={300}>
            <Box sx={{ display: activePanel === 'variables' ? 'block' : 'none' }}>
              <Paper elevation={2} sx={{ p: 2, height: 'fit-content' }}>
                <Typography variant="h6" gutterBottom>
                  Variable Selection
                </Typography>
                {/* Dataset Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="dataset-select-label">Dataset</InputLabel>
                  <Select labelId="dataset-select-label" value={selectedDatasetId} label="Dataset" onChange={handleDatasetChange} disabled={datasets.length === 0}>
                    {datasets.map(ds => <MenuItem key={ds.id} value={ds.id}>{ds.name} ({ds.data.length} rows)</MenuItem>)}
                  </Select>
                </FormControl>

                {/* Category Variable */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="category-variable-label">Category Variable</InputLabel>
                  <Select labelId="category-variable-label" value={categoryVariable} label="Category Variable" onChange={handleCategoryVariableChange} disabled={categoricalColumns.length === 0}>
                    {categoricalColumns.map(col => <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>)}
                  </Select>
                  <Typography variant="caption" color="text.secondary">Variable for the axis categories</Typography>
                </FormControl>

                {/* Value Variables */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="value-variables-label">Value Variable(s) (Optional)</InputLabel>
                  <Select
                    labelId="value-variables-label" multiple value={valueVariables} label="Value Variable(s) (Optional)"
                    onChange={handleValueVariablesChange} disabled={numericColumns.length === 0}
                    renderValue={(selected) => selected.map(id => numericColumns.find(c => c.id === id)?.name).join(', ')}
                  >
                    {numericColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        <Checkbox checked={valueVariables.indexOf(column.id) > -1} size="small"/>
                        <Typography variant="body2">{column.name}</Typography>
                      </MenuItem>
                    ))}
                  </Select>
                  <Typography variant="caption" color="text.secondary">If empty, shows frequency/percentage.</Typography>
                </FormControl>
                {/* Aggregation Method */}
                {valueVariables.length > 0 && (
                  <FormControl component="fieldset" fullWidth margin="normal">
                    <Typography variant="subtitle2" gutterBottom>Aggregation Method</Typography>
                    <RadioGroup value={aggregationMethod} onChange={handleAggregationMethodChange} row>
                      <FormControlLabel value="sum" control={<Radio size="small" />} label="Sum" />
                      <FormControlLabel value="average" control={<Radio size="small" />} label="Average" />
                      <FormControlLabel value="count" control={<Radio size="small" />} label="Count" />
                      <FormControlLabel value="max" control={<Radio size="small" />} label="Max" />
                      <FormControlLabel value="min" control={<Radio size="small" />} label="Min" />
                    </RadioGroup>
                  </FormControl>
                )}

                {/* Display Mode */}
                {valueVariables.length === 0 && categoryVariable && (
                  <FormControl component="fieldset" fullWidth margin="normal">
                    <Typography variant="subtitle2" gutterBottom>Display Mode</Typography>
                    <RadioGroup value={displayMode} onChange={handleDisplayModeChange} row>
                      <FormControlLabel value="frequency" control={<Radio size="small" />} label="Frequency" />
                      <FormControlLabel value="percentage" control={<Radio size="small" />} label="Percentage" />
                    </RadioGroup>
                  </FormControl>
                )}

                {/* Action Buttons */}
                <Box mt={2} display="flex" gap={1} flexDirection="column">
                  <Button
                    variant="contained"
                    onClick={generatePlotlyData}
                    disabled={isGenerateDisabled}
                    startIcon={loading ? <CircularProgress size={20} /> : <BarChartIconMui />}
                    fullWidth
                  >
                    {loading ? 'Generating...' : 'Generate Chart'}
                  </Button>
                  <Box display="flex" gap={1} justifyContent="center">
                    <Tooltip title="Download Chart">
                      <IconButton onClick={downloadChart} disabled={isDownloadDisabled}>
                        <SaveIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reset Settings">
                      <IconButton onClick={resetChartSettings}>
                        <RefreshIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>

          {/* Chart Settings Panel */}
          <Fade in={activePanel === 'settings'} timeout={300}>
            <Box sx={{ display: activePanel === 'settings' ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 2,
                  mb: 2
                }}>
                  Chart Settings
                </Typography>

                {/* Labels & Title Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 1 }}>
                    Labels & Title
                  </Typography>
                  <TextField
                    fullWidth
                    label="Chart Title"
                    value={chartSettings.title}
                    onChange={(e) => handleSettingsChange('title', e.target.value)}
                    margin="normal"
                    size="small"
                  />
                  <TextField
                    fullWidth
                    label="X-Axis Label"
                    value={chartSettings.xAxisLabel}
                    onChange={(e) => handleSettingsChange('xAxisLabel', e.target.value)}
                    margin="normal"
                    size="small"
                  />
                  <TextField
                    fullWidth
                    label="Y-Axis Label"
                    value={chartSettings.yAxisLabel}
                    onChange={(e) => handleSettingsChange('yAxisLabel', e.target.value)}
                    margin="normal"
                    size="small"
                  />
                </Box>

                {/* Appearance Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Appearance
                  </Typography>
                  <FormControl fullWidth margin="normal" size="small">
                    <InputLabel id="color-scheme-label">Color Scheme</InputLabel>
                    <Select
                      labelId="color-scheme-label"
                      value={chartSettings.colorScheme}
                      label="Color Scheme"
                      onChange={(e) => handleSettingsChange('colorScheme', e.target.value as ColorSchemeName)}
                    >
                      {Object.keys(colorSchemes).map(scheme =>
                        <MenuItem key={scheme} value={scheme}>
                          {scheme.charAt(0).toUpperCase() + scheme.slice(1)}
                        </MenuItem>
                      )}
                    </Select>
                  </FormControl>
                  <FormControl fullWidth margin="normal" size="small">
                    <InputLabel id="barmode-label">Bar Mode (Multiple Vars)</InputLabel>
                    <Select
                      labelId="barmode-label"
                      value={chartSettings.barMode}
                      label="Bar Mode (Multiple Vars)"
                      onChange={(e) => handleSettingsChange('barMode', e.target.value as 'group' | 'stack' | 'relative')}
                      disabled={valueVariables.length <= 1}
                    >
                      <MenuItem value="group">Grouped</MenuItem>
                      <MenuItem value="stack">Stacked</MenuItem>
                      <MenuItem value="relative">Relative (Stacked %)</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                {/* Display Options Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Display Options
                  </Typography>
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={chartSettings.showValuesOnBars}
                          onChange={(e) => handleSettingsChange('showValuesOnBars', e.target.checked)}
                          size="small"
                        />
                      }
                      label="Show Values on Bars"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={chartSettings.orientation === 'h'}
                          onChange={(e) => handleSettingsChange('orientation', e.target.checked ? 'h' : 'v')}
                          size="small"
                        />
                      }
                      label="Horizontal Bars"
                    />
                  </FormGroup>
                  <FormControl fullWidth margin="normal" size="small">
                    <InputLabel id="sort-bars-label">Sort Bars</InputLabel>
                    <Select
                      labelId="sort-bars-label"
                      value={chartSettings.sortBars}
                      label="Sort Bars"
                      onChange={(e) => handleSettingsChange('sortBars', e.target.value as 'none' | 'ascending' | 'descending')}
                      disabled={valueVariables.length > 1}
                    >
                      <MenuItem value="none">None</MenuItem>
                      <MenuItem value="ascending">Ascending</MenuItem>
                      <MenuItem value="descending">Descending</MenuItem>
                    </Select>
                    <Typography variant="caption" color="text.secondary">
                      Sorting enabled only for single value variable or frequency/percentage mode.
                    </Typography>
                  </FormControl>
                </Box>

                {/* Size & Styling Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Size & Styling
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Typography gutterBottom>Bar Gap (Between Categories)</Typography>
                    <Slider
                      value={chartSettings.barGap}
                      min={0}
                      max={1}
                      step={0.05}
                      onChange={(_e, value) => handleSettingsChange('barGap', value)}
                      valueLabelDisplay="auto"
                      size="small"
                    />
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <Typography gutterBottom>Maximum Bars</Typography>
                    <Slider
                      value={chartSettings.maxBars}
                      min={5}
                      max={50}
                      step={1}
                      onChange={(_e, value) => handleSettingsChange('maxBars', value)}
                      valueLabelDisplay="auto"
                      size="small"
                    />
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>
        </Grid>

        {/* Chart Display Panel */}
        <Grid item xs={12} sm={12} md={9} lg={9}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Chart Preview
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  Active: {activePanel === 'variables' ? 'Variables' : 'Settings'}
                </Typography>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: activePanel === 'variables' ? theme.palette.primary.main : theme.palette.warning.main,
                    boxShadow: `0 0 0 2px ${activePanel === 'variables' ? theme.palette.primary.main + '20' : theme.palette.warning.main + '20'}`
                  }}
                />
              </Box>
            </Box>

            {error && (
              <Box sx={{ mb: 2, p: 2, backgroundColor: theme.palette.error.light + '20', borderRadius: 1, border: `1px solid ${theme.palette.error.light}` }}>
                <Typography color="error">{error}</Typography>
              </Box>
            )}

            <Box
              ref={plotlyDivRef}
              id={PLOTLY_BAR_CHART_DIV_ID}
              sx={{
                height: 500,
                width: '100%',
                display: plotlyConfig ? 'block' : 'flex',
                justifyContent: plotlyConfig ? 'flex-start' : 'center',
                alignItems: plotlyConfig ? 'flex-start' : 'center',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.01)' : 'rgba(0, 0, 0, 0.01)',
                overflow: 'hidden'
              }}
            >
              {loading ? (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
                  <CircularProgress />
                  <Typography color="text.secondary">Generating chart...</Typography>
                </Box>
              ) : plotlyConfig ? (
                // Chart will be rendered here by Plotly
                <></>
              ) : (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2} p={4}>
                  <BarChartIconMui sx={{ fontSize: 48, color: 'text.disabled' }} />
                  <Typography color="text.secondary" textAlign="center">
                    {!activeDataset
                      ? 'Select a dataset to begin'
                      : !categoryVariable
                      ? 'Select a category variable to generate the bar chart'
                      : 'Chart will appear here once generated'
                    }
                  </Typography>
                  {activeDataset && !categoryVariable && (
                    <Typography variant="body2" color="text.disabled" textAlign="center">
                      Switch to the Variables panel to select your data
                    </Typography>
                  )}
                  <Typography variant="body2" color="text.disabled" textAlign="center">
                    Keyboard shortcuts: Alt+1 (Variables) | Alt+2 (Settings)
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BarChart;
