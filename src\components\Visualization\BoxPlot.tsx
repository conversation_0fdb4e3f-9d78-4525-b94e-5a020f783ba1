import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  SelectChangeEvent,
  TextField,
  FormGroup,
  FormControlLabel,
  Switch,
  CircularProgress,
  useTheme,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Fade
} from '@mui/material';
import {
  Tune as TuneIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  ShowChart as ShowChartIcon,
  CandlestickChart as CandlestickChartIcon,
  Settings as SettingsIcon,
  DataObject as DataObjectIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, Column } from '../../types';
import { calculateQuartiles, calculateMean } from '@/utils/stats';
import { getOrderedCategoriesByColumnId } from '../../utils/dataUtilities';
// Import Plotly correctly after installing the package and types
import * as Plotly from 'plotly.js';

// Define Plotly types based on @types/plotly.js (adjust if needed based on actual types)
type PlotlyData = Partial<Plotly.PlotData>;
type PlotlyLayout = Partial<Plotly.Layout>;
type PlotlyConfig = Partial<Plotly.Config>;

interface ChartSettings {
  title: string;
  xAxisLabel: string;
  yAxisLabel: string;
  showOutliers: boolean;
  showMean: boolean;
  horizontal: boolean;
  colorScheme: string;
  showNotches: boolean;
}

const defaultChartSettings: ChartSettings = {
  title: 'Box Plot',
  xAxisLabel: 'Category',
  yAxisLabel: 'Value',
  showOutliers: true,
  showMean: false, // Plotly 'boxmean' can be true, 'sd', or false
  horizontal: false,
  colorScheme: 'default',
  showNotches: false,
};

const colorSchemes: Record<string, string[]> = {
  default: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
  pastel: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5', '#d9d9d9', '#bc80bd'],
  bold: ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628', '#f781bf', '#999999'],
  sequential: ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#08519c', '#08306b'],
  diverging: ['#a50026', '#d73027', '#f46d43', '#fdae61', '#fee090', '#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
};

interface SummaryStatRow {
  category: string;
  min: number;
  max: number;
  median: number;
  q1: number;
  q3: number;
  mean: number;
  iqr: number;
  sampleSize: number;
}

const PLOTLY_DIV_ID = 'plotlyBoxPlotDiv';

// Define the component correctly using React.FC
const BoxPlot: React.FC = () => {
  // Hooks should be called inside the component body
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const plotlyDivRef = useRef<HTMLDivElement>(null);

  // State variables
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [valueVariable, setValueVariable] = useState<string>('');
  const [categoryVariable, setCategoryVariable] = useState<string>(''); // Grouping variable
  const [stratificationVariable, setStratificationVariable] = useState<string>('');
  const [chartSettings, setChartSettings] = useState<ChartSettings>(defaultChartSettings);
  const [activePanel, setActivePanel] = useState<'variables' | 'settings'>('variables');
  const [plotlyChartConfig, setPlotlyChartConfig] = useState<{ data: PlotlyData[], layout: PlotlyLayout } | null>(null);
  const [summaryData, setSummaryData] = useState<SummaryStatRow[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const activeDataset = React.useMemo(() => {
    if (!selectedDatasetId) return null;
    return datasets.find(ds => ds.id === selectedDatasetId) || null;
  }, [datasets, selectedDatasetId]);

  // Memoized column lists
  const numericColumns = React.useMemo(() => 
    activeDataset?.columns.filter(col => col.type === DataType.NUMERIC) || [],
    [activeDataset]
  );
  const categoricalColumns = React.useMemo(() =>
    activeDataset?.columns.filter(col => col.type === DataType.CATEGORICAL) || [],
    [activeDataset]
  );

  // Effect to update selected dataset ID when context changes
  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
      // Reset selections when dataset changes
      setValueVariable('');
      setCategoryVariable('');
      setStratificationVariable('');
      setPlotlyChartConfig(null);
      setSummaryData([]);
      setError(null); // Clear errors on dataset change
    }
  }, [currentDataset]); // Dependency array changed to [currentDataset]

  // Effect to render Plotly chart when config changes
  useEffect(() => {
    if (plotlyChartConfig && plotlyDivRef.current) {
      const config: PlotlyConfig = { responsive: true };
      Plotly.newPlot(PLOTLY_DIV_ID, plotlyChartConfig.data, plotlyChartConfig.layout, config);
    }
    // Optional: Cleanup function to purge the div when component unmounts or config changes
    return () => {
      if (plotlyDivRef.current) {
        // Check if Plotly object and purge method exist before calling
        if (typeof Plotly !== 'undefined' && Plotly.purge) {
           Plotly.purge(plotlyDivRef.current);
        }
      }
    };
  }, [plotlyChartConfig]);

  // Keyboard accessibility for panel switching
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + 1 for Variables panel, Alt + 2 for Settings panel
      if (event.altKey && !event.ctrlKey && !event.shiftKey) {
        if (event.key === '1') {
          event.preventDefault();
          setActivePanel('variables');
        } else if (event.key === '2') {
          event.preventDefault();
          setActivePanel('settings');
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);
  
  // Handlers
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newId = event.target.value;
    setSelectedDatasetId(newId);
    // Reset variables when dataset changes manually
    setValueVariable('');
    setCategoryVariable('');
    setStratificationVariable('');
    setPlotlyChartConfig(null);
    setSummaryData([]);
    setError(null);
  };

  const handleSettingsChange = (setting: keyof ChartSettings, value: any) => {
    setChartSettings(prev => ({ ...prev, [setting]: value }));
  };

  const generateBoxPlot = () => {
    if (!activeDataset || !valueVariable) {
      setError('Please select a dataset and a value variable.');
      return;
    }

    setLoading(true);
    setError(null);
    setPlotlyChartConfig(null); // Clear previous chart config
    setSummaryData([]);

    try {
      const valueCol = activeDataset.columns.find(col => col.id === valueVariable);
      const categoryCol = categoryVariable ? activeDataset.columns.find(col => col.id === categoryVariable) : null;
      const stratificationCol = stratificationVariable ? activeDataset.columns.find(col => col.id === stratificationVariable) : null;

      if (!valueCol) throw new Error('Value column not found.');

      const dataForPlotly: PlotlyData[] = []; // Use PlotlyData type
      const currentSummaryData: SummaryStatRow[] = [];
      const colors = colorSchemes[chartSettings.colorScheme] || colorSchemes.default;
      const allData = activeDataset.data;

      // Case 1: Numerical Variable only
      if (!categoryCol && !stratificationCol) {
        const values = allData.map(row => row[valueCol.name]).filter(v => typeof v === 'number' && !isNaN(v)) as number[];
        if (values.length === 0) throw new Error('No numeric data found for the selected value variable.');
        
        dataForPlotly.push({
          y: chartSettings.horizontal ? undefined : values,
          x: chartSettings.horizontal ? values : undefined,
          type: 'box',
          name: valueCol.name,
          marker: { color: colors[0] },
          boxpoints: chartSettings.showOutliers ? 'outliers' : false,
          boxmean: chartSettings.showMean ? 'sd' : false,
          // @ts-ignore - notched is a valid property but might be missing in older @types/plotly.js
          notched: chartSettings.showNotches, 
          orientation: chartSettings.horizontal ? 'h' : 'v',
        });

        const sortedValues = [...values].sort((a, b) => a - b);
        const [q1, median, q3] = calculateQuartiles(sortedValues);
        currentSummaryData.push({
          category: valueCol.name,
          min: Math.min(...sortedValues),
          max: Math.max(...sortedValues),
          q1, median, q3,
          mean: calculateMean(sortedValues),
          iqr: q3 - q1,
          sampleSize: sortedValues.length,
        });
      }
      // Case 2: Numerical + Grouping variable
      else if (categoryCol && !stratificationCol) {
        const groupedData: Record<string, number[]> = {};
        allData.forEach(row => {
          const catValue = String(row[categoryCol.name]);
          const numValue = row[valueCol.name];
          if (typeof numValue === 'number' && !isNaN(numValue)) {
            if (!groupedData[catValue]) groupedData[catValue] = [];
            groupedData[catValue].push(numValue);
          }
        });

        // Use ordered categories for consistent ordering
        const orderedCategories = getOrderedCategoriesByColumnId(categoryVariable, activeDataset);
        orderedCategories.forEach((groupName, index) => {
          const values = groupedData[groupName];
          if (!values || values.length === 0) return;

          dataForPlotly.push({
            y: chartSettings.horizontal ? undefined : values,
            x: chartSettings.horizontal ? values : undefined,
            type: 'box',
            name: groupName,
            marker: { color: colors[index % colors.length] },
          boxpoints: chartSettings.showOutliers ? 'outliers' : false,
          boxmean: chartSettings.showMean ? 'sd' : false,
          // @ts-ignore - notched is a valid property but might be missing in older @types/plotly.js
          notched: chartSettings.showNotches,
          orientation: chartSettings.horizontal ? 'h' : 'v',
        });

          const sortedValues = [...values].sort((a, b) => a - b);
          const [q1, median, q3] = calculateQuartiles(sortedValues);
          currentSummaryData.push({
            category: groupName,
            min: Math.min(...sortedValues),
            max: Math.max(...sortedValues),
            q1, median, q3,
            mean: calculateMean(sortedValues),
            iqr: q3 - q1,
            sampleSize: sortedValues.length,
          });
        });
      }
      // Case 3: Numerical + Grouping + Stratification variable
      else if (valueCol && categoryCol && stratificationCol) {
        const processedData: Record<string, Record<string, number[]>> = {};
        allData.forEach(row => {
          const numVal = row[valueCol.name];
          const grpVal = String(row[categoryCol.name]);
          const strVal = String(row[stratificationCol.name]);

          if (typeof numVal === 'number' && !isNaN(numVal)) {
            if (!processedData[strVal]) processedData[strVal] = {};
            if (!processedData[strVal][grpVal]) processedData[strVal][grpVal] = [];
            processedData[strVal][grpVal].push(numVal);
          }
        });
        
        // Use ordered categories for both stratification and grouping variables
        const orderedStratCategories = getOrderedCategoriesByColumnId(stratificationVariable, activeDataset);
        const orderedGroupCategories = getOrderedCategoriesByColumnId(categoryVariable, activeDataset);

        orderedStratCategories.forEach((stratName, index) => {
          const groupData = processedData[stratName];
          if (!groupData) return;

          const traceY: number[] = [];
          const traceX: string[] = [];
          const allStratValues: number[] = [];

          orderedGroupCategories.forEach(groupName => {
            const valuesInGroup = groupData[groupName];
            if (valuesInGroup) {
              valuesInGroup.forEach(val => {
                traceY.push(val);
                traceX.push(groupName);
                allStratValues.push(val);
              });
            }
          });

          if (traceY.length > 0) {
            dataForPlotly.push({
              y: chartSettings.horizontal ? traceX : traceY,
              x: chartSettings.horizontal ? traceY : traceX,
              type: 'box',
              name: stratName,
              marker: { color: colors[index % colors.length] },
          boxpoints: chartSettings.showOutliers ? 'outliers' : false,
          boxmean: chartSettings.showMean ? 'sd' : false,
          // @ts-ignore - notched is a valid property but might be missing in older @types/plotly.js
          notched: chartSettings.showNotches, 
          orientation: chartSettings.horizontal ? 'h' : 'v',
        });
          }
          
          if (allStratValues.length > 0) {
            const sortedValues = [...allStratValues].sort((a, b) => a - b);
            const [q1, median, q3] = calculateQuartiles(sortedValues);
            currentSummaryData.push({
              category: `${stratName} (overall)`, // Clarified summary category
              min: Math.min(...sortedValues),
              max: Math.max(...sortedValues),
              q1, median, q3,
              mean: calculateMean(sortedValues),
              iqr: q3 - q1,
              sampleSize: sortedValues.length,
            });
          }
        });
      } else if (stratificationCol && !categoryCol) {
          // Handle case where stratification is selected without grouping (treat as error or ignore stratification)
          setError('Stratification requires a grouping variable to be selected.');
          // Optionally, proceed as Case 1 (ignoring stratification)
          // For now, just show error and don't generate plot data
          setLoading(false);
          return; 
      } else {
         // Should not happen if logic above is correct, but acts as a fallback
         throw new Error('Invalid combination of variables selected.');
      }

      // Define Layout using Plotly types
      const layout: PlotlyLayout = {
        title: { text: chartSettings.title },
        xaxis: { 
          title: { text: chartSettings.horizontal ? chartSettings.yAxisLabel : chartSettings.xAxisLabel },
          automargin: true 
        },
        yaxis: { 
          title: { text: chartSettings.horizontal ? chartSettings.xAxisLabel : chartSettings.yAxisLabel },
          automargin: true 
        },
        boxmode: (categoryCol || stratificationCol) ? 'group' : undefined,
        autosize: true,
        // margin: { t: 50, b: 50, l: 60, r: 30 }, // Removed to allow automargin and autosize to work better
        paper_bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#fff',
        plot_bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.default : '#fff',
        font: { color: theme.palette.text.primary },
        legend: {
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.5)' : 'rgba(255,255,255,0.5)',
            bordercolor: theme.palette.divider,
            borderwidth: 1
        }
      };
      // Set axis type based on orientation and grouping/stratification
      if (chartSettings.horizontal) {
        if (categoryCol || stratificationCol) layout.yaxis = { ...layout.yaxis, type: 'category' };
      } else {
        if (categoryCol || stratificationCol) layout.xaxis = { ...layout.xaxis, type: 'category' };
      }

      setPlotlyChartConfig({ data: dataForPlotly, layout });
      setSummaryData(currentSummaryData);

    } catch (err) {
      setError(`Error generating box plot: ${err instanceof Error ? err.message : String(err)}`);
      setPlotlyChartConfig(null); // Ensure no stale chart config on error
      setSummaryData([]);
    } finally {
      setLoading(false);
    }
  };

  const resetChartSettings = () => {
    setChartSettings(defaultChartSettings);
  };

  const downloadChart = async () => {
    if (plotlyChartConfig) {
      try {
        // Dynamically import Plotly for download functionality
        const Plotly = await import('plotly.js');
        const downloadOpts = {
          format: 'svg' as const,
          filename: chartSettings.title.replace(/\s+/g, '_') || 'boxplot',
          width: 600,
          height: 400,
        };
        await Plotly.downloadImage(PLOTLY_DIV_ID, downloadOpts);
      } catch (error) {
        setError('Failed to download chart. Please try again.');
      }
    } else {
      setError('Chart data not available for download.');
    }
  };

  // Button is disabled if no chart config exists or if loading
  const isDownloadDisabled = !plotlyChartConfig || loading; 

  // Render component
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Box Plot Generator
      </Typography>

      {/* Information Section */}
      <Box mb={2} p={2} sx={{ backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          Generate box plots to visualize the distribution and identify outliers in numerical data. Use keyboard shortcuts: <strong>Alt+1</strong> for Variables panel, <strong>Alt+2</strong> for Settings panel.
        </Typography>
      </Box>

      {/* Three-Panel Layout */}
      <Grid container spacing={2}>
        {/* Left Panel Container - Variables or Settings */}
        <Grid item xs={12} sm={12} md={3} lg={3}>
          {/* Panel Toggle Tabs */}
          <Paper
            elevation={1}
            sx={{
              mb: 1,
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)'
            }}
          >
            <Tabs
              value={activePanel}
              onChange={(_, newValue) => setActivePanel(newValue)}
              variant="fullWidth"
              sx={{
                minHeight: 48,
                '& .MuiTab-root': {
                  minHeight: 48,
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  color: theme.palette.text.secondary,
                  '&.Mui-selected': {
                    color: theme.palette.primary.main,
                  },
                  '&:hover': {
                    color: theme.palette.primary.main,
                    backgroundColor: theme.palette.action.hover,
                  }
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: theme.palette.primary.main,
                }
              }}
            >
              <Tooltip title="Variable Selection Panel" placement="top">
                <Tab
                  value="variables"
                  label="Variables"
                  icon={<DataObjectIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
              <Tooltip title="Chart Settings Panel" placement="top">
                <Tab
                  value="settings"
                  label="Settings"
                  icon={<SettingsIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
            </Tabs>
          </Paper>

          {/* Variable Selection Panel */}
          <Fade in={activePanel === 'variables'} timeout={300}>
            <Box sx={{ display: activePanel === 'variables' ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 1
                }}>
                  Data Selection
                </Typography>

                {/* Dataset Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="dataset-select-label">Dataset</InputLabel>
                  <Select
                    labelId="dataset-select-label"
                    value={selectedDatasetId}
                    label="Dataset"
                    onChange={handleDatasetChange}
                    disabled={datasets.length === 0}
                  >
                    {datasets.length === 0 ? (
                      <MenuItem value="" disabled>No datasets available</MenuItem>
                    ) : (
                      datasets.map(ds => (
                        <MenuItem key={ds.id} value={ds.id}>
                          {ds.name} ({ds.data.length} rows)
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>

                {/* Value Variable Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="value-variable-label">Numerical Variable</InputLabel>
                  <Select
                    labelId="value-variable-label"
                    value={valueVariable}
                    label="Numerical Variable"
                    onChange={e => setValueVariable(e.target.value)}
                    disabled={numericColumns.length === 0}
                  >
                    {numericColumns.length === 0 ? (
                      <MenuItem value="" disabled>No numeric variables</MenuItem>
                    ) : (
                      numericColumns.map(col => (
                        <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>

                {/* Grouping Variable Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="category-variable-label">Grouping Variable (Optional)</InputLabel>
                  <Select
                    labelId="category-variable-label"
                    value={categoryVariable}
                    label="Grouping Variable (Optional)"
                    onChange={e => setCategoryVariable(e.target.value)}
                    disabled={categoricalColumns.length === 0}
                  >
                    <MenuItem value=""><em>None</em></MenuItem>
                    {categoricalColumns.map(col => (
                      <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Stratification Variable Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="stratification-variable-label">Stratification Variable (Optional)</InputLabel>
                  <Select
                    labelId="stratification-variable-label"
                    value={stratificationVariable}
                    label="Stratification Variable (Optional)"
                    onChange={e => setStratificationVariable(e.target.value)}
                    disabled={categoricalColumns.length === 0 || !categoryVariable}
                  >
                    <MenuItem value=""><em>None</em></MenuItem>
                    {categoricalColumns.filter(col => col.id !== categoryVariable).map(col => (
                      <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                    ))}
                  </Select>
                  {!categoryVariable && stratificationVariable && (
                    <Typography variant="caption" color="error">
                      Grouping variable must be selected to use stratification.
                    </Typography>
                  )}
                </FormControl>

                {/* Basic Chart Options */}
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Display Options
                  </Typography>
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.showOutliers}
                          onChange={e => handleSettingsChange('showOutliers', e.target.checked)}
                        />
                      }
                      label="Show Outliers"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.showMean}
                          onChange={e => handleSettingsChange('showMean', e.target.checked)}
                        />
                      }
                      label="Show Mean (+SD)"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.horizontal}
                          onChange={e => handleSettingsChange('horizontal', e.target.checked)}
                        />
                      }
                      label="Horizontal Orientation"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.showNotches}
                          onChange={e => handleSettingsChange('showNotches', e.target.checked)}
                        />
                      }
                      label="Show Notches"
                    />
                  </FormGroup>
                </Box>

                {/* Action Buttons */}
                <Box mt={2} display="flex" gap={1} flexDirection="column">
                  <Button
                    variant="contained"
                    onClick={generateBoxPlot}
                    disabled={!valueVariable || loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <CandlestickChartIcon />}
                    fullWidth
                  >
                    {loading ? 'Generating...' : 'Generate Chart'}
                  </Button>
                  <Box display="flex" gap={1} justifyContent="center">
                    <Tooltip title="Download Chart">
                      <IconButton onClick={downloadChart} disabled={!plotlyChartConfig || loading}>
                        <SaveIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reset Settings">
                      <IconButton onClick={resetChartSettings}>
                        <RefreshIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>
          {/* Chart Settings Panel */}
          <Fade in={activePanel === 'settings'} timeout={300}>
            <Box sx={{ display: activePanel === 'settings' ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 1
                }}>
                  Chart Settings
                </Typography>

                {/* Labels & Title Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Labels & Title
                  </Typography>
                  <TextField
                    fullWidth
                    label="Chart Title"
                    value={chartSettings.title}
                    onChange={e => handleSettingsChange('title', e.target.value)}
                    margin="normal"
                    variant="outlined"
                    size="small"
                  />
                  <TextField
                    fullWidth
                    label="X-Axis Label"
                    value={chartSettings.xAxisLabel}
                    onChange={e => handleSettingsChange('xAxisLabel', e.target.value)}
                    margin="normal"
                    variant="outlined"
                    size="small"
                  />
                  <TextField
                    fullWidth
                    label="Y-Axis Label"
                    value={chartSettings.yAxisLabel}
                    onChange={e => handleSettingsChange('yAxisLabel', e.target.value)}
                    margin="normal"
                    variant="outlined"
                    size="small"
                  />
                </Box>

                {/* Appearance Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Appearance
                  </Typography>
                  <FormControl fullWidth margin="normal" size="small">
                    <InputLabel id="color-scheme-label">Color Scheme</InputLabel>
                    <Select
                      labelId="color-scheme-label"
                      value={chartSettings.colorScheme}
                      label="Color Scheme"
                      onChange={e => handleSettingsChange('colorScheme', e.target.value)}
                    >
                      {Object.keys(colorSchemes).map(scheme => (
                        <MenuItem key={scheme} value={scheme}>
                          {scheme.charAt(0).toUpperCase() + scheme.slice(1)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                {/* Apply Button */}
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={generateBoxPlot}
                  disabled={!valueVariable || loading || !selectedDatasetId}
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  Apply Customizations & Regenerate
                </Button>
              </Paper>
            </Box>
          </Fade>
        </Grid>

        {/* Chart Display Panel */}
        <Grid item xs={12} sm={12} md={9} lg={9}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Chart Preview
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  Active: {activePanel === 'variables' ? 'Variables' : 'Settings'}
                </Typography>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: activePanel === 'variables' ? theme.palette.primary.main : theme.palette.warning.main,
                    boxShadow: `0 0 0 2px ${activePanel === 'variables' ? theme.palette.primary.main + '20' : theme.palette.warning.main + '20'}`
                  }}
                />
              </Box>
            </Box>
            {error && (
              <Box sx={{ mb: 2, p: 2, backgroundColor: theme.palette.error.light + '20', borderRadius: 1, border: `1px solid ${theme.palette.error.light}` }}>
                <Typography color="error">{error}</Typography>
              </Box>
            )}

            {/* Plotly Chart Container */}
            <Box
              sx={{
                minHeight: 500,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.01)' : 'rgba(0, 0, 0, 0.01)'
              }}
            >
              {loading ? (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
                  <CircularProgress />
                  <Typography color="text.secondary">Generating box plot...</Typography>
                </Box>
              ) : plotlyChartConfig ? (
                <div
                  ref={plotlyDivRef}
                  id={PLOTLY_DIV_ID}
                  style={{
                    width: '100%',
                    height: '500px'
                  }}
                />
              ) : (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2} p={4}>
                  <CandlestickChartIcon sx={{ fontSize: 48, color: 'text.disabled' }} />
                  <Typography color="text.secondary" textAlign="center">
                    {!activeDataset
                      ? 'Select a dataset to begin'
                      : !valueVariable
                      ? 'Select a numerical variable to generate the box plot'
                      : 'Chart will appear here once generated'
                    }
                  </Typography>
                  {activeDataset && !valueVariable && (
                    <Typography variant="body2" color="text.disabled" textAlign="center">
                      Switch to the Variables panel to select your data
                    </Typography>
                  )}
                </Box>
              )}
            </Box>

            {/* Summary Statistics Table */}
            {summaryData.length > 0 && !loading && !error && (
              <Box mt={3}>
                <Typography variant="subtitle2" gutterBottom>Summary Statistics</Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Category</TableCell>
                        <TableCell align="right">Min</TableCell>
                        <TableCell align="right">Q1</TableCell>
                        <TableCell align="right">Median</TableCell>
                        <TableCell align="right">Q3</TableCell>
                        <TableCell align="right">Max</TableCell>
                        <TableCell align="right">Mean</TableCell>
                        <TableCell align="right">IQR</TableCell>
                        <TableCell align="right">N</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {summaryData.map((row, index) => (
                        <TableRow key={`${row.category}-${index}`}>
                          <TableCell component="th" scope="row">{row.category}</TableCell>
                          <TableCell align="right">{row.min.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.q1.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.median.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.q3.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.max.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.mean.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.iqr.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.sampleSize}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BoxPlot;
