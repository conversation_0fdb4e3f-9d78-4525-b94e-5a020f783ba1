import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import {
  <PERSON><PERSON>hart as Bar<PERSON>hart<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>hart as ScatterPlotI<PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON>ChartI<PERSON>,
  ShowChart as LineChartIcon, // Using ShowChart for general plots
  Show<PERSON>hart as ShowChartIcon, // Also import for Error Bar Chart
  Info as InfoIcon,
  Launch as LaunchIcon,
  Category as CategoryIcon, // Using CategoryIcon for Box Plot/Raincloud
  Assessment as HistogramIcon, // Using Assessment for Histogram
  Cloud as CloudIcon, // Import CloudIcon
} from '@mui/icons-material';

interface DataVisualizationOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Categorical' | 'Distribution' | 'Relationship' | 'Other';
  color: string;
}

interface DataVisualizationOptionsProps {
  onNavigate: (path: string) => void;
}

export const dataVisualizationOptions: DataVisualizationOption[] = [
  {
    name: 'Bar Chart',
    shortDescription: 'Compare values across categories',
    detailedDescription: 'Visualize the distribution of categorical data or compare numerical values across different categories using vertical or horizontal bars.',
    path: 'charts/bar',
    icon: <BarChartIcon />,
    category: 'Categorical',
    color: '#4CAF50', // Green
  },
  {
    name: 'Box Plot',
    shortDescription: 'Show distribution and outliers',
    detailedDescription: 'Display the distribution of a dataset, showing median, quartiles, and potential outliers. Useful for comparing distributions between groups.',
    path: 'charts/boxplot',
    icon: <CategoryIcon />, // Reusing CategoryIcon
    category: 'Distribution',
    color: '#FF9800', // Orange
  },
  {
    name: 'Histogram',
    shortDescription: 'Visualize frequency distribution',
    detailedDescription: 'Represent the frequency distribution of numerical data using bins. Helps understand the shape, center, and spread of the data.',
    path: 'charts/histogram',
    icon: <HistogramIcon />, // Reusing AssessmentIcon
    category: 'Distribution',
    color: '#2196F3', // Blue
  },
  {
    name: 'Pie Chart',
    shortDescription: 'Show parts of a whole',
    detailedDescription: 'Illustrate the proportion of each category relative to the whole. Best used for a small number of categories.',
    path: 'charts/pie',
    icon: <PieChartIcon />,
    category: 'Categorical',
    color: '#9C27B0', // Purple
  },
  {
    name: 'RainCloud Plot',
    shortDescription: 'Combine box plot, scatter plot, and density',
    detailedDescription: 'A hybrid visualization combining a box plot, scatter plot (or jittered points), and a half-violin or density plot to show the distribution and individual data points.',
    path: 'charts/raincloud',
    icon: <CloudIcon />, // Using CloudIcon as per Visualization component
    category: 'Distribution',
    color: '#E91E63', // Pink
  },
  {
    name: 'Scatter Plot',
    shortDescription: 'Show relationship between two variables',
    detailedDescription: 'Plot individual data points on a two-dimensional graph to show the relationship or correlation between two numerical variables.',
    path: 'charts/scatter',
    icon: <ScatterPlotIcon />,
    category: 'Relationship',
    color: '#009688', // Teal
  },
  {
    name: 'Error Bar Chart',
    shortDescription: 'Display means with uncertainty measures',
    detailedDescription: 'Show mean values with error bars indicating variability or uncertainty. Error bars can represent standard error, standard deviation, or confidence intervals.',
    path: 'charts/errorbar',
    icon: <ShowChartIcon />,
    category: 'Distribution',
    color: '#795548', // Brown
  },
  {
    name: 'Sankey Diagram',
    shortDescription: 'Visualize flow relationships',
    detailedDescription: 'Show flow relationships between categorical variables with proportional link widths. Ideal for visualizing transitions, pathways, or multi-level categorical relationships.',
    path: 'charts/sankey',
    icon: <CategoryIcon />, // Using CategoryIcon for flow diagrams
    category: 'Other',
    color: '#607D8B', // Blue Grey
  },
];

const DataVisualizationOptions: React.FC<DataVisualizationOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const categories = ['All', 'Categorical', 'Distribution', 'Relationship', 'Other'];

  const filteredOptions = selectedCategory === 'All'
    ? dataVisualizationOptions
    : dataVisualizationOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Categorical': return <BarChartIcon />;
      case 'Distribution': return <HistogramIcon />;
      case 'Relationship': return <ScatterPlotIcon />;
      default: return <LineChartIcon />; // Default icon for 'Other' or 'All'
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Data Visualization Tools
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Create insightful charts and plots from your data
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Explore various visualization options to understand patterns, distributions, and relationships within your datasets.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography variant="h6" fontWeight="bold">
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {option.shortDescription}
                </Typography>

                <Typography variant="body2" paragraph>
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Comparing categories?</strong> Try Bar Chart or Pie Chart
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Understanding data distribution?</strong> Use Histogram, Box Plot, or RainCloud Plot
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • <strong>Exploring relationships between variables?</strong> Scatter Plot is the tool for you
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default DataVisualizationOptions;
