import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  SelectChangeEvent,
  TextField,
  FormGroup,
  FormControlLabel,
  Switch,
  CircularProgress,
  useTheme,
  IconButton,
  Tooltip,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Slider,
  Tabs,
  Tab,
  Fade
} from '@mui/material';
import {
  ShowChart as ShowChartIcon,
  Tune as TuneIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  Settings as SettingsIcon,
  DataObject as DataObjectIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, Column } from '../../types';
import { calculateMean, calculateStandardDeviation, calculateStandardError } from '@/utils/stats';
import { getOrderedCategoriesByColumnId } from '../../utils/dataUtilities';
import Plot from 'react-plotly.js';
import * as Plotly from 'plotly.js';

// Types for Plotly data
interface PlotlyData extends Partial<Plotly.PlotData> {
  x?: (string | number)[];
  y?: number[];
  error_y?: {
    type: 'data' | 'percent' | 'sqrt' | 'constant';
    array?: number[];
    value?: number;
    visible: boolean;
    color?: string;
    thickness?: number;
    width?: number;
  };
  type: 'scatter';
  mode: 'markers' | 'lines' | 'markers+lines';
  marker?: {
    color?: string;
    size?: number;
    symbol?: string;
  };
  name?: string;
}

// Chart settings interface
interface ChartSettings {
  title: string;
  xAxisLabel: string;
  yAxisLabel: string;
  errorBarType: 'se' | 'sd' | 'ci95' | 'ci90' | 'ci99' | 'custom';
  customMultiplier: number;
  showDataPoints: boolean;
  showConnectingLines: boolean;
  colorScheme: string;
  markerSize: number;
  errorBarWidth: number;
  errorBarThickness: number;
  showLegend: boolean;
  showGrid: boolean;
}

// Default settings
const defaultChartSettings: ChartSettings = {
  title: 'Error Bar Chart',
  xAxisLabel: 'Categories',
  yAxisLabel: 'Values',
  errorBarType: 'se',
  customMultiplier: 1.96,
  showDataPoints: true,
  showConnectingLines: false,
  colorScheme: 'default',
  markerSize: 8,
  errorBarWidth: 4,
  errorBarThickness: 2,
  showLegend: true,
  showGrid: true
};

// Color schemes following established patterns
const colorSchemes: Record<string, string[]> = {
  default: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
  pastel: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5', '#d9d9d9', '#bc80bd'],
  bold: ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628', '#f781bf', '#999999'],
  muted: ['#6b7280', '#9ca3af', '#d1d5db', '#374151', '#4b5563', '#6b7280', '#9ca3af', '#d1d5db'],
  sequential: ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#08519c', '#08306b'],
  diverging: ['#a50026', '#d73027', '#f46d43', '#fdae61', '#fee090', '#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
};

// Error bar type labels
const errorBarTypeLabels = {
  se: 'Standard Error (SE)',
  sd: 'Standard Deviation (SD)',
  ci95: '95% Confidence Interval',
  ci90: '90% Confidence Interval',
  ci99: '99% Confidence Interval',
  custom: 'Custom Multiplier'
};

// Plotly div ID for download functionality
const PLOTLY_ERROR_BAR_DIV_ID = 'plotly-error-bar-chart';

const ErrorBarChart: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const plotlyDivRef = useRef<HTMLDivElement>(null);

  // State variables
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [numericalVariable, setNumericalVariable] = useState<string>('');
  const [groupingVariable, setGroupingVariable] = useState<string>('');
  const [factorVariable, setFactorVariable] = useState<string>('');
  const [chartSettings, setChartSettings] = useState<ChartSettings>(defaultChartSettings);
  const [plotlyData, setPlotlyData] = useState<PlotlyData[]>([]);
  const [plotlyLayout, setPlotlyLayout] = useState<Partial<Plotly.Layout>>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [activePanel, setActivePanel] = useState<'variables' | 'settings'>('variables');

  const activeDataset = React.useMemo(() => {
    if (!selectedDatasetId) return null;
    return datasets.find(ds => ds.id === selectedDatasetId) || null;
  }, [datasets, selectedDatasetId]);

  // Get available columns
  const numericalColumns = React.useMemo(() =>
    activeDataset?.columns.filter(col => col.type === DataType.NUMERIC) || [],
    [activeDataset]
  );
  const categoricalColumns = React.useMemo(() =>
    activeDataset?.columns.filter(col => col.type === DataType.CATEGORICAL) || [],
    [activeDataset]
  );

  // Effect to update selected dataset ID when context changes
  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
      // Reset selections when dataset changes
      setNumericalVariable('');
      setGroupingVariable('');
      setFactorVariable('');
      setPlotlyData([]);
      setPlotlyLayout({});
      setError('');
    }
  }, [currentDataset]);

  // Calculate confidence interval multiplier
  const getConfidenceMultiplier = (type: string): number => {
    switch (type) {
      case 'ci90': return 1.645;
      case 'ci95': return 1.96;
      case 'ci99': return 2.576;
      case 'custom': return chartSettings.customMultiplier;
      default: return 1;
    }
  };

  // Calculate error bar values
  const calculateErrorBars = (values: number[], type: string): number => {
    if (values.length === 0) return 0;
    
    const mean = calculateMean(values);
    const n = values.length;
    
    switch (type) {
      case 'se':
        return calculateStandardError(values);
      case 'sd':
        return calculateStandardDeviation(values);
      case 'ci90':
      case 'ci95':
      case 'ci99':
      case 'custom':
        const se = calculateStandardError(values);
        const multiplier = getConfidenceMultiplier(type);
        return se * multiplier;
      default:
        return 0;
    }
  };

  // Generate chart data
  const generateChartData = () => {
    if (!activeDataset || !numericalVariable) {
      setError('Please select a dataset and a numerical variable.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const numericalCol = activeDataset.columns.find(col => col.id === numericalVariable);
      const groupingCol = groupingVariable ? activeDataset.columns.find(col => col.id === groupingVariable) : null;
      const factorCol = factorVariable ? activeDataset.columns.find(col => col.id === factorVariable) : null;

      if (!numericalCol) {
        throw new Error('Numerical variable not found.');
      }

      const colors = colorSchemes[chartSettings.colorScheme] || colorSchemes.default;
      const traces: PlotlyData[] = [];

      // Set up layout first
      const layout: Partial<Plotly.Layout> = {
        title: {
          text: chartSettings.title,
          font: { size: 16, color: theme.palette.text.primary }
        },
        xaxis: {
          title: { text: chartSettings.xAxisLabel },
          showgrid: chartSettings.showGrid,
          gridcolor: theme.palette.divider,
          color: theme.palette.text.primary,
          zeroline: false,
          automargin: true
        },
        yaxis: {
          title: { text: chartSettings.yAxisLabel },
          showgrid: chartSettings.showGrid,
          gridcolor: theme.palette.divider,
          color: theme.palette.text.primary,
          zeroline: false,
          automargin: true
        },
        showlegend: chartSettings.showLegend && (factorVariable || (!groupingVariable && !factorVariable)),
        legend: {
          orientation: 'v',
          x: 1.02,
          y: 1
        },
        plot_bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.default : '#fff',
        paper_bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#fff',
        font: { color: theme.palette.text.primary },
        margin: { l: 80, r: 80, t: 80, b: 80 },
        autosize: true
      };

      if (!groupingVariable && !factorVariable) {
        // Single series - overall mean with error bars
        const values = activeDataset.data
          .map(row => row[numericalCol.name])
          .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

        if (values.length === 0) {
          throw new Error('No valid numerical data found.');
        }

        const mean = calculateMean(values);
        const errorValue = calculateErrorBars(values, chartSettings.errorBarType);

        traces.push({
          x: ['Overall'],
          y: [mean],
          error_y: {
            type: 'data',
            array: [errorValue],
            visible: true,
            color: colors[0],
            thickness: chartSettings.errorBarThickness,
            width: chartSettings.errorBarWidth
          },
          type: 'scatter',
          mode: chartSettings.showDataPoints ?
            (chartSettings.showConnectingLines ? 'markers+lines' : 'markers') :
            (chartSettings.showConnectingLines ? 'lines' : 'markers'),
          marker: {
            color: colors[0],
            size: chartSettings.markerSize,
            symbol: 'circle'
          },
          name: numericalCol.name,
          line: chartSettings.showConnectingLines ? { color: colors[0] } : undefined
        });
      } else if (groupingVariable && !factorVariable) {
        // Single grouping variable
        const orderedCategories = getOrderedCategoriesByColumnId(groupingVariable, activeDataset);

        orderedCategories.forEach((category, index) => {
          const categoryValues = activeDataset.data
            .filter(row => String(row[groupingCol!.name]) === category)
            .map(row => row[numericalCol.name])
            .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

          if (categoryValues.length > 0) {
            const mean = calculateMean(categoryValues);
            const errorValue = calculateErrorBars(categoryValues, chartSettings.errorBarType);

            traces.push({
              x: [category],
              y: [mean],
              error_y: {
                type: 'data',
                array: [errorValue],
                visible: true,
                color: colors[index % colors.length],
                thickness: chartSettings.errorBarThickness,
                width: chartSettings.errorBarWidth
              },
              type: 'scatter',
              mode: chartSettings.showDataPoints ?
                (chartSettings.showConnectingLines ? 'markers+lines' : 'markers') :
                (chartSettings.showConnectingLines ? 'lines' : 'markers'),
              marker: {
                color: colors[index % colors.length],
                size: chartSettings.markerSize,
                symbol: 'circle'
              },
              name: category,
              line: chartSettings.showConnectingLines ? { color: colors[index % colors.length] } : undefined
            });
          }
        });
      } else if (groupingVariable && factorVariable) {
        // Both grouping and factor variables - implement proper positioning
        const orderedGroupCategories = getOrderedCategoriesByColumnId(groupingVariable, activeDataset);
        const orderedFactorCategories = getOrderedCategoriesByColumnId(factorVariable, activeDataset);

        // Calculate offset for positioning multiple factors within each group
        const factorCount = orderedFactorCategories.length;
        const offsetWidth = 0.8 / factorCount; // Total width of 0.8 divided by number of factors
        const startOffset = -(factorCount - 1) * offsetWidth / 2; // Center the group

        orderedFactorCategories.forEach((factor, factorIndex) => {
          const xValues: (string | number)[] = [];
          const yValues: number[] = [];
          const errorValues: number[] = [];

          orderedGroupCategories.forEach((group, groupIndex) => {
            const categoryValues = activeDataset.data
              .filter(row =>
                String(row[groupingCol!.name]) === group &&
                String(row[factorCol!.name]) === factor
              )
              .map(row => row[numericalCol.name])
              .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

            if (categoryValues.length > 0) {
              // Calculate offset position for this factor within the group
              const offset = startOffset + factorIndex * offsetWidth;
              xValues.push(groupIndex + offset);
              yValues.push(calculateMean(categoryValues));
              errorValues.push(calculateErrorBars(categoryValues, chartSettings.errorBarType));
            }
          });

          if (xValues.length > 0) {
            traces.push({
              x: xValues,
              y: yValues,
              error_y: {
                type: 'data',
                array: errorValues,
                visible: true,
                color: colors[factorIndex % colors.length],
                thickness: chartSettings.errorBarThickness,
                width: chartSettings.errorBarWidth
              },
              type: 'scatter',
              mode: chartSettings.showDataPoints ?
                (chartSettings.showConnectingLines ? 'markers+lines' : 'markers') :
                (chartSettings.showConnectingLines ? 'lines' : 'markers'),
              marker: {
                color: colors[factorIndex % colors.length],
                size: chartSettings.markerSize,
                symbol: 'circle'
              },
              name: factor,
              line: chartSettings.showConnectingLines ? { color: colors[factorIndex % colors.length] } : undefined
            });
          }
        });

        // Update layout to use custom tick labels for grouped categories
        layout.xaxis = {
          ...layout.xaxis,
          tickmode: 'array',
          tickvals: orderedGroupCategories.map((_, index) => index),
          ticktext: orderedGroupCategories
        };
      }

      setPlotlyData(traces);
      setPlotlyLayout(layout);

    } catch (err) {
      setError(`Error generating chart: ${err instanceof Error ? err.message : String(err)}`);
      setPlotlyData([]);
    } finally {
      setLoading(false);
    }
  };

  // Download chart
  const downloadChart = async () => {
    if (plotlyData.length > 0) {
      try {
        const downloadOpts = {
          format: 'svg' as const,
          filename: chartSettings.title.replace(/\s+/g, '_') || 'error_bar_chart',
          width: 800,
          height: 600,
        };
        await Plotly.downloadImage(PLOTLY_ERROR_BAR_DIV_ID, downloadOpts);
      } catch (error) {
        setError('Failed to download chart. Please try again.');
      }
    } else {
      setError('Chart data not available for download.');
    }
  };

  // Reset settings
  const resetChartSettings = () => {
    setChartSettings(defaultChartSettings);
  };

  // Handle dataset change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newId = event.target.value;
    setSelectedDatasetId(newId);
    // Reset variables when dataset changes manually
    setNumericalVariable('');
    setGroupingVariable('');
    setFactorVariable('');
    setPlotlyData([]);
    setPlotlyLayout({});
    setError('');
  };

  // Handle variable changes
  const handleNumericalVariableChange = (event: SelectChangeEvent) => {
    setNumericalVariable(event.target.value);
  };

  const handleGroupingVariableChange = (event: SelectChangeEvent) => {
    setGroupingVariable(event.target.value);
  };

  const handleFactorVariableChange = (event: SelectChangeEvent) => {
    setFactorVariable(event.target.value);
  };

  // Handle chart settings changes
  const handleChartSettingChange = (setting: keyof ChartSettings, value: any) => {
    setChartSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  // Auto-generate chart when variables change
  useEffect(() => {
    if (numericalVariable && activeDataset) {
      generateChartData();
    }
  }, [numericalVariable, groupingVariable, factorVariable, chartSettings, activeDataset]);

  // Keyboard accessibility for panel switching
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + 1 for Variables panel, Alt + 2 for Settings panel
      if (event.altKey && !event.ctrlKey && !event.shiftKey) {
        if (event.key === '1') {
          event.preventDefault();
          setActivePanel('variables');
        } else if (event.key === '2') {
          event.preventDefault();
          setActivePanel('settings');
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Error Bar Chart Generator
      </Typography>

      {/* Information Section */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box display="flex" alignItems="center" gap={1}>
            <InfoIcon color="primary" />
            <Typography variant="h6">About Error Bar Charts</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" color="text.secondary" paragraph>
            Error bar charts display the mean values of groups with error bars indicating the variability or uncertainty in the data. 
            Error bars can represent different measures:
          </Typography>
          <Box component="ul" sx={{ pl: 2, mb: 2 }}>
            <Typography component="li" variant="body2" color="text.secondary">
              <strong>Standard Error (SE):</strong> Shows the precision of the sample mean
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              <strong>Standard Deviation (SD):</strong> Shows the spread of individual data points
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              <strong>Confidence Intervals:</strong> Shows the range likely to contain the true population mean
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary" paragraph>
            Use error bar charts to compare means between groups while showing the uncertainty in your estimates.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
            <strong>Tip:</strong> Use Alt+1 for Variables panel, Alt+2 for Settings panel to quickly switch between panels.
          </Typography>
        </AccordionDetails>
      </Accordion>

      {/* Three-Panel Layout */}
      <Grid container spacing={2}>
        {/* Left Panel Container - Variables or Settings */}
        <Grid item xs={12} sm={12} md={3} lg={3}>
          {/* Panel Toggle Tabs */}
          <Paper
            elevation={1}
            sx={{
              mb: 1,
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)'
            }}
          >
            <Tabs
              value={activePanel}
              onChange={(_, newValue) => setActivePanel(newValue)}
              variant="fullWidth"
              sx={{
                minHeight: 44,
                '& .MuiTab-root': {
                  minHeight: 44,
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  color: theme.palette.text.secondary,
                  textTransform: 'none',
                  transition: 'all 0.2s ease-in-out',
                  '&.Mui-selected': {
                    color: theme.palette.primary.main,
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(25, 118, 210, 0.08)',
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
                  }
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                }
              }}
            >
              <Tooltip title="Variable Selection Panel" placement="top">
                <Tab
                  value="variables"
                  label="Variables"
                  icon={<DataObjectIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
              <Tooltip title="Chart Settings Panel" placement="top">
                <Tab
                  value="settings"
                  label="Settings"
                  icon={<SettingsIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
            </Tabs>
          </Paper>

          {/* Variable Selection Panel */}
          <Fade in={activePanel === 'variables'} timeout={300}>
            <Box sx={{ display: activePanel === 'variables' ? 'block' : 'none' }}>
              <Paper elevation={2} sx={{ p: 2, height: 'fit-content' }}>
                <Typography variant="h6" gutterBottom>
                  Variable Selection
                </Typography>

                {/* Dataset Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel>Dataset</InputLabel>
                  <Select
                    value={selectedDatasetId}
                    onChange={handleDatasetChange}
                    label="Dataset"
                  >
                    {datasets.map((dataset) => (
                      <MenuItem key={dataset.id} value={dataset.id}>
                        {dataset.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Numerical Variable */}
                <FormControl fullWidth margin="normal" size="small" required>
                  <InputLabel>Numerical Variable</InputLabel>
                  <Select
                    value={numericalVariable}
                    onChange={handleNumericalVariableChange}
                    label="Numerical Variable"
                  >
                    {numericalColumns.map((col) => (
                      <MenuItem key={col.id} value={col.id}>
                        {col.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Grouping Variable */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel>Grouping Variable (Optional)</InputLabel>
                  <Select
                    value={groupingVariable}
                    onChange={handleGroupingVariableChange}
                    label="Grouping Variable (Optional)"
                  >
                    <MenuItem value="">
                      <em>None</em>
                    </MenuItem>
                    {categoricalColumns.map((col) => (
                      <MenuItem key={col.id} value={col.id}>
                        {col.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Factor Variable */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel>Factor Variable (Optional)</InputLabel>
                  <Select
                    value={factorVariable}
                    onChange={handleFactorVariableChange}
                    label="Factor Variable (Optional)"
                    disabled={!groupingVariable}
                  >
                    <MenuItem value="">
                      <em>None</em>
                    </MenuItem>
                    {categoricalColumns
                      .filter(col => col.id !== groupingVariable)
                      .map((col) => (
                        <MenuItem key={col.id} value={col.id}>
                          {col.name}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>

                {/* Error Bar Type */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel>Error Bar Type</InputLabel>
                  <Select
                    value={chartSettings.errorBarType}
                    onChange={(e) => handleChartSettingChange('errorBarType', e.target.value)}
                    label="Error Bar Type"
                  >
                    {Object.entries(errorBarTypeLabels).map(([key, label]) => (
                      <MenuItem key={key} value={key}>
                        {label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Custom Multiplier */}
                {chartSettings.errorBarType === 'custom' && (
                  <TextField
                    fullWidth
                    margin="normal"
                    size="small"
                    label="Custom Multiplier"
                    type="number"
                    value={chartSettings.customMultiplier}
                    onChange={(e) => handleChartSettingChange('customMultiplier', parseFloat(e.target.value) || 1)}
                    inputProps={{ step: 0.1, min: 0.1 }}
                  />
                )}

                {/* Action Buttons */}
                <Box mt={2} display="flex" gap={1} flexDirection="column">
                  <Button
                    variant="contained"
                    onClick={generateChartData}
                    disabled={!numericalVariable || loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <ShowChartIcon />}
                    fullWidth
                  >
                    {loading ? 'Generating...' : 'Generate Chart'}
                  </Button>
                  <Box display="flex" gap={1} justifyContent="center">
                    <Tooltip title="Download Chart">
                      <IconButton onClick={downloadChart} disabled={plotlyData.length === 0}>
                        <SaveIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reset Settings">
                      <IconButton onClick={resetChartSettings}>
                        <RefreshIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>

          {/* Chart Settings Panel */}
          <Fade in={activePanel === 'settings'} timeout={300}>
            <Box sx={{ display: activePanel === 'settings' ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 1
                }}>
                  Chart Settings
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {/* Title and Labels Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Labels & Title
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                      <TextField
                        fullWidth
                        size="small"
                        label="Chart Title"
                        value={chartSettings.title}
                        onChange={(e) => handleChartSettingChange('title', e.target.value)}
                      />
                      <TextField
                        fullWidth
                        size="small"
                        label="X-Axis Label"
                        value={chartSettings.xAxisLabel}
                        onChange={(e) => handleChartSettingChange('xAxisLabel', e.target.value)}
                      />
                      <TextField
                        fullWidth
                        size="small"
                        label="Y-Axis Label"
                        value={chartSettings.yAxisLabel}
                        onChange={(e) => handleChartSettingChange('yAxisLabel', e.target.value)}
                      />
                    </Box>
                  </Box>

                  {/* Color Scheme Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Appearance
                    </Typography>
                    <FormControl fullWidth size="small">
                      <InputLabel>Color Scheme</InputLabel>
                      <Select
                        value={chartSettings.colorScheme}
                        onChange={(e) => handleChartSettingChange('colorScheme', e.target.value)}
                        label="Color Scheme"
                      >
                        {Object.keys(colorSchemes).map((scheme) => (
                          <MenuItem key={scheme} value={scheme}>
                            {scheme.charAt(0).toUpperCase() + scheme.slice(1)}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>

                  {/* Visual Options Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Display Options
                    </Typography>
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Switch
                            size="small"
                            checked={chartSettings.showDataPoints}
                            onChange={(e) => handleChartSettingChange('showDataPoints', e.target.checked)}
                          />
                        }
                        label="Show Data Points"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            size="small"
                            checked={chartSettings.showConnectingLines}
                            onChange={(e) => handleChartSettingChange('showConnectingLines', e.target.checked)}
                          />
                        }
                        label="Show Connecting Lines"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            size="small"
                            checked={chartSettings.showLegend}
                            onChange={(e) => handleChartSettingChange('showLegend', e.target.checked)}
                          />
                        }
                        label="Show Legend"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            size="small"
                            checked={chartSettings.showGrid}
                            onChange={(e) => handleChartSettingChange('showGrid', e.target.checked)}
                          />
                        }
                        label="Show Grid"
                      />
                    </FormGroup>
                  </Box>

                  {/* Size Controls Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Size & Styling
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <Box>
                        <Typography variant="body2" gutterBottom>
                          Marker Size: {chartSettings.markerSize}
                        </Typography>
                        <Slider
                          value={chartSettings.markerSize}
                          onChange={(_, value) => handleChartSettingChange('markerSize', value)}
                          min={4}
                          max={20}
                          step={1}
                          size="small"
                          valueLabelDisplay="auto"
                        />
                      </Box>
                      <Box>
                        <Typography variant="body2" gutterBottom>
                          Error Bar Width: {chartSettings.errorBarWidth}
                        </Typography>
                        <Slider
                          value={chartSettings.errorBarWidth}
                          onChange={(_, value) => handleChartSettingChange('errorBarWidth', value)}
                          min={2}
                          max={10}
                          step={1}
                          size="small"
                          valueLabelDisplay="auto"
                        />
                      </Box>
                      <Box>
                        <Typography variant="body2" gutterBottom>
                          Error Bar Thickness: {chartSettings.errorBarThickness}
                        </Typography>
                        <Slider
                          value={chartSettings.errorBarThickness}
                          onChange={(_, value) => handleChartSettingChange('errorBarThickness', value)}
                          min={1}
                          max={5}
                          step={0.5}
                          size="small"
                          valueLabelDisplay="auto"
                        />
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>
        </Grid>

        {/* Chart Display Panel */}
        <Grid item xs={12} sm={12} md={9} lg={9}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Chart Preview
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  Active: {activePanel === 'variables' ? 'Variables' : 'Settings'}
                </Typography>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: activePanel === 'variables' ? theme.palette.primary.main : theme.palette.warning.main,
                    boxShadow: `0 0 0 2px ${activePanel === 'variables' ? theme.palette.primary.main + '20' : theme.palette.warning.main + '20'}`
                  }}
                />
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Box
              ref={plotlyDivRef}
              sx={{
                minHeight: 500,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.01)' : 'rgba(0, 0, 0, 0.01)'
              }}
            >
              {loading ? (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
                  <CircularProgress />
                  <Typography color="text.secondary">Generating chart...</Typography>
                </Box>
              ) : plotlyData.length > 0 ? (
                <Plot
                  divId={PLOTLY_ERROR_BAR_DIV_ID}
                  data={plotlyData}
                  layout={{
                    ...plotlyLayout,
                    height: 500
                  }}
                  config={{
                    displayModeBar: true,
                    displaylogo: false,
                    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                    responsive: true,
                    toImageButtonOptions: {
                      format: 'svg',
                      filename: chartSettings.title.replace(/\s+/g, '_') || 'error_bar_chart',
                      width: 800,
                      height: 600,
                      scale: 1
                    }
                  }}
                  style={{ width: '100%', height: '500px' }}
                />
              ) : (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2} p={4}>
                  <ShowChartIcon sx={{ fontSize: 48, color: 'text.disabled' }} />
                  <Typography color="text.secondary" textAlign="center">
                    {!activeDataset
                      ? 'Select a dataset to begin'
                      : !numericalVariable
                      ? 'Select a numerical variable to generate the error bar chart'
                      : 'Chart will appear here once generated'
                    }
                  </Typography>
                  {activeDataset && !numericalVariable && (
                    <Typography variant="body2" color="text.disabled" textAlign="center">
                      Switch to the Variables panel to select your data
                    </Typography>
                  )}
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ErrorBarChart;
