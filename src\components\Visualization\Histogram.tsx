import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  SelectChangeEvent,
  Slider,
  TextField,
  FormGroup,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  useTheme,
  IconButton,
  Tooltip,
  Switch,
  Tabs,
  Tab,
  Fade
} from '@mui/material';
import {
  BarChart as BarChartIcon,
  <PERSON>ne as TuneIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  DataObject as DataObjectIcon
} from '@mui/icons-material';
import Plot from 'react-plotly.js';
import Plotly from 'plotly.js'; // Using the main library
import { useData } from '../../context/DataContext';
import { DataType } from '../../types';
import { calculateMean, calculateMedian, calculateStandardDeviation } from '@/utils/stats';

interface ChartSettings {
  title: string;
  xAxisLabel: string;
  yAxisLabel: string;
  showNormalCurve: boolean;
  showMeanLine: boolean;
  showMedianLine: boolean;
  bins: number; // nbinsx for plotly
  colorScheme: string;
  histnorm: '' | 'percent' | 'probability' | 'density' | 'probability density'; // for density plot
  showOutliers: boolean;
  // showValues is not directly applicable in Plotly histogram like in Recharts bar labels
  // barGap is handled by bargap in layout or marker.pad in trace
}

const defaultChartSettings: ChartSettings = {
  title: 'Histogram',
  xAxisLabel: 'Value',
  yAxisLabel: 'Frequency',
  showNormalCurve: true,
  showMeanLine: true,
  showMedianLine: false,
  bins: 10,
  colorScheme: 'default',
  histnorm: '', // Default is count/frequency
  showOutliers: true,
};

// Predefined color schemes (can be used for traces)
const colorSchemes = {
  default: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
  pastel: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5', '#d9d9d9', '#bc80bd'],
  bold: ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628', '#f781bf', '#999999', '#66c2a5'],
  // Plotly has its own colorway cycle, but we can define custom ones if needed.
};

const Histogram: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();

  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [valueVariable, setValueVariable] = useState<string>('');
  const [groupVariable, setGroupVariable] = useState<string>('');

  const [chartSettings, setChartSettings] = useState<ChartSettings>(defaultChartSettings);
  const [activePanel, setActivePanel] = useState<'variables' | 'settings'>('variables');

  const [plotlyData, setPlotlyData] = useState<Plotly.Data[]>([]);
  const [plotlyLayout, setPlotlyLayout] = useState<Partial<Plotly.Layout>>({});
  const [statistics, setStatistics] = useState<{
    mean: number;
    median: number; // Added median calculation
    stdDev: number;
    min: number;
    max: number;
    totalCount: number;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const activeDataset = React.useMemo(() => {
    if (!selectedDatasetId) return null;
    return datasets.find(ds => ds.id === selectedDatasetId) || null;
  }, [datasets, selectedDatasetId]);
  
  // Memoized columns
  const categoricalColumns = React.useMemo(() => 
    activeDataset?.columns.filter(col => col.type === DataType.CATEGORICAL) || [],
    [activeDataset]
  );
  const numericColumns = React.useMemo(() =>
    activeDataset?.columns.filter(col => col.type === DataType.NUMERIC) || [],
    [activeDataset]
  );

  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
      setValueVariable('');
      setGroupVariable('');
      setPlotlyData([]);
      setPlotlyLayout({});
      setStatistics(null);
    }
  }, [currentDataset]); // Dependency array changed to [currentDataset]

  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setValueVariable('');
    setGroupVariable('');
    setPlotlyData([]);
    setPlotlyLayout({});
    setStatistics(null);
  };

  const handleValueVariableChange = (event: SelectChangeEvent<string>) => {
    setValueVariable(event.target.value);
  };

  const handleGroupVariableChange = (event: SelectChangeEvent<string>) => {
    setGroupVariable(event.target.value);
  };

  const handleSettingsChange = (setting: keyof ChartSettings, value: any) => {
    setChartSettings(prevSettings => ({
      ...prevSettings,
      [setting]: value
    }));
    // Regenerate on relevant changes
    if (['bins', 'showOutliers', 'histnorm', 'colorScheme', 'title', 'xAxisLabel', 'yAxisLabel', 'showNormalCurve', 'showMeanLine', 'showMedianLine'].includes(setting)) {
        // Defer generation to button click or useEffect, or call directly if preferred
        // For now, we'll let the "Generate Histogram" button handle it.
        // If you want live updates, call generateHistogram() here.
    }
  };
  
  // Generate normal curve data for Plotly
  const generateNormalCurveData = (
    mean: number,
    stdDev: number,
    minX: number,
    maxX: number,
    totalCount: number,
    binWidth?: number // Optional, for scaling if not using density
  ): Partial<Plotly.ScatterData> => {
    const xValues = [];
    const yValues = [];
    const numPoints = 100;
    const range = maxX - minX;
    const step = range / numPoints;

    for (let i = 0; i <= numPoints; i++) {
      const x = minX + i * step;
      // Normal PDF formula
      let y = (1 / (stdDev * Math.sqrt(2 * Math.PI))) * Math.exp(-0.5 * Math.pow((x - mean) / stdDev, 2));
      
      // Scale y if histnorm is not a density type
      if (chartSettings.histnorm === '' && binWidth && totalCount > 0) { // histnorm '' is count
        y = y * totalCount * binWidth;
      }
      
      xValues.push(x);
      yValues.push(y);
    }

    return {
      x: xValues,
      y: yValues,
      type: 'scatter',
      mode: 'lines',
      name: 'Normal Curve',
      yaxis: chartSettings.histnorm === '' ? 'y1' : 'y2', // Use secondary y-axis for density if primary is count
      line: { color: 'red', width: 2 },
    };
  };


  const generateHistogram = () => {
    if (!activeDataset || !valueVariable) {
      setError('Please select a dataset and a value variable.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const valueCol = activeDataset.columns.find(col => col.id === valueVariable);
      const groupCol = groupVariable ? activeDataset.columns.find(col => col.id === groupVariable) : null;

      if (!valueCol) throw new Error('Value column not found.');

      let allValuesForStats: number[] = [];
      const traces: Plotly.Data[] = [];
      const selectedColorScheme = colorSchemes[chartSettings.colorScheme as keyof typeof colorSchemes] || colorSchemes.default;

      if (groupCol) {
        const uniqueGroups = Array.from(new Set(activeDataset.data.map(row => String(row[groupCol.name]))));
        uniqueGroups.forEach((group, index) => {
          let groupValues = activeDataset.data
            .filter(row => String(row[groupCol.name]) === group)
            .map(row => row[valueCol.name])
            .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

          if (!chartSettings.showOutliers && groupValues.length > 0) {
            const mean = calculateMean(groupValues);
            const stdDev = calculateStandardDeviation(groupValues);
            groupValues = groupValues.filter(x => Math.abs((x - mean) / stdDev) <= 3);
          }
          allValuesForStats.push(...groupValues);
          
          if (groupValues.length > 0) {
            traces.push({
              x: groupValues,
              type: 'histogram',
              name: group,
              nbinsx: chartSettings.bins,
              histnorm: chartSettings.histnorm,
              marker: { color: selectedColorScheme[index % selectedColorScheme.length] },
              opacity: 0.75,
            } as Partial<Plotly.PlotData>); // Use Partial or a more specific histogram trace type
          }
        });
      } else {
        let values = activeDataset.data
          .map(row => row[valueCol.name])
          .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

        if (!chartSettings.showOutliers && values.length > 0) {
          const meanVal = calculateMean(values);
          const stdDevVal = calculateStandardDeviation(values);
          values = values.filter(x => Math.abs((x - meanVal) / stdDevVal) <= 3);
        }
        allValuesForStats.push(...values);

        if (values.length > 0) {
          traces.push({
            x: values,
            type: 'histogram',
            name: valueCol.name,
            nbinsx: chartSettings.bins,
            histnorm: chartSettings.histnorm,
            marker: { color: selectedColorScheme[0] },
          } as Partial<Plotly.PlotData>); // Use Partial or a more specific histogram trace type
        }
      }
      
      if (allValuesForStats.length === 0) {
        setError('No valid numeric data to plot after filtering.');
        setLoading(false);
        setPlotlyData([]);
        return;
      }

      const mean = calculateMean(allValuesForStats);
      const median = calculateMedian(allValuesForStats);
      const stdDev = calculateStandardDeviation(allValuesForStats);
      const minVal = Math.min(...allValuesForStats);
      const maxVal = Math.max(...allValuesForStats);

      setStatistics({ mean, median, stdDev, min: minVal, max: maxVal, totalCount: allValuesForStats.length });

      const newLayout: Partial<Plotly.Layout> = {
        title: {text: chartSettings.title},
        xaxis: { title: {text: chartSettings.xAxisLabel}, autorange: true },
        yaxis: { title: {text: chartSettings.histnorm === '' ? 'Frequency' : chartSettings.histnorm.charAt(0).toUpperCase() + chartSettings.histnorm.slice(1)} , autorange: true },
        barmode: groupCol ? 'overlay' : 'stack', // 'overlay' for grouped, 'stack' or 'group' for single
        bargap: 0.05,
        legend: { traceorder: 'normal' },
        shapes: [],
      };
      
      if (chartSettings.histnorm !== '' && chartSettings.showNormalCurve) { // If density, use secondary y-axis for normal curve
        newLayout.yaxis2 = {
          title: {text: 'Density'},
          overlaying: 'y',
          side: 'right',
          showgrid: false,
          autorange: true,
        };
      }


      if (chartSettings.showMeanLine && statistics) {
        newLayout.shapes?.push({
          type: 'line',
          x0: mean,
          x1: mean,
          y0: 0,
          y1: 1,
          yref: 'paper', // stretches across the entire y-axis
          line: { color: 'red', width: 2, dash: 'dash' },
          name: 'Mean',
        });
      }
      if (chartSettings.showMedianLine && statistics) {
        newLayout.shapes?.push({
          type: 'line',
          x0: median,
          x1: median,
          y0: 0,
          y1: 1,
          yref: 'paper',
          line: { color: 'green', width: 2, dash: 'dashdot' },
          name: 'Median',
        });
      }

      if (chartSettings.showNormalCurve && stdDev > 0) {
        // Calculate binWidth for scaling normal curve if histnorm is count
        let binWidth;
        if (chartSettings.histnorm === '' && traces.length > 0 && (traces[0] as any).x) {
            const xData = (traces[0] as { x: number[] }).x; // Type assertion
            const dataMin = Math.min(...xData);
            const dataMax = Math.max(...xData);
            binWidth = (dataMax - dataMin) / chartSettings.bins;
        }

        const normalCurveTrace = generateNormalCurveData(mean, stdDev, minVal, maxVal, allValuesForStats.length, binWidth);
        traces.push(normalCurveTrace as Plotly.Data);
      }
      
      setPlotlyData(traces);
      setPlotlyLayout(newLayout);

    } catch (err) {
      setError(`Error generating histogram: ${err instanceof Error ? err.message : String(err)}`);
      setPlotlyData([]);
      setPlotlyLayout({});
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    // This effect can regenerate the chart when settings change, if desired for live updates.
    // For now, generation is tied to the button.
    // If live updates are needed, call generateHistogram() here based on chartSettings dependencies.
    // Example: if (valueVariable && activeDataset) generateHistogram();
  }, [chartSettings, valueVariable, groupVariable, activeDataset?.id]);

  // Keyboard accessibility for panel switching
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + 1 for Variables panel, Alt + 2 for Settings panel
      if (event.altKey && !event.ctrlKey && !event.shiftKey) {
        if (event.key === '1') {
          event.preventDefault();
          setActivePanel('variables');
        } else if (event.key === '2') {
          event.preventDefault();
          setActivePanel('settings');
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);


  const resetChartSettings = () => {
    setChartSettings(defaultChartSettings);
    // Optionally, regenerate histogram with default settings if data is selected
    // if (valueVariable && currentDataset) generateHistogram(); 
  };

  const downloadChart = () => {
    const plotElement = document.getElementById('plotly-histogram');
    if (plotElement && plotlyData.length > 0) {
      Plotly.downloadImage(plotElement as Plotly.PlotlyHTMLElement, {
        format: 'svg',
        filename: `${chartSettings.title.replace(/\s+/g, '_')}_histogram`,
        width: 800, // Default width
        height: 600, // Default height
      }).catch((err: Error) => {
        setError(`Error downloading chart: ${err.message}`);
      });
    } else {
      setError('Chart not available for download.');
    }
  };
  
  // Get colors from selected color scheme - Plotly handles its own colorway by default
  // This function might be used if we want to manually assign colors to traces
  const getPlotlyColors = () => {
    return colorSchemes[chartSettings.colorScheme as keyof typeof colorSchemes] || colorSchemes.default;
  };


  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Histogram Generator
      </Typography>

      {/* Information Section */}
      <Box mb={2} p={2} sx={{ backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          Generate histograms to visualize the distribution of numerical data. Use keyboard shortcuts: <strong>Alt+1</strong> for Variables panel, <strong>Alt+2</strong> for Settings panel.
        </Typography>
      </Box>

      {/* Three-Panel Layout */}
      <Grid container spacing={2}>
        {/* Left Panel Container - Variables or Settings */}
        <Grid item xs={12} sm={12} md={3} lg={3}>
          {/* Panel Toggle Tabs */}
          <Paper
            elevation={1}
            sx={{
              mb: 1,
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)'
            }}
          >
            <Tabs
              value={activePanel}
              onChange={(_, newValue) => setActivePanel(newValue)}
              variant="fullWidth"
              sx={{
                minHeight: 48,
                '& .MuiTab-root': {
                  minHeight: 48,
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  color: theme.palette.text.secondary,
                  '&.Mui-selected': {
                    color: theme.palette.primary.main,
                  },
                  '&:hover': {
                    color: theme.palette.primary.main,
                    backgroundColor: theme.palette.action.hover,
                  }
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: theme.palette.primary.main,
                }
              }}
            >
              <Tooltip title="Variable Selection Panel" placement="top">
                <Tab
                  value="variables"
                  label="Variables"
                  icon={<DataObjectIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
              <Tooltip title="Chart Settings Panel" placement="top">
                <Tab
                  value="settings"
                  label="Settings"
                  icon={<SettingsIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
            </Tabs>
          </Paper>

          {/* Variable Selection Panel */}
          <Fade in={activePanel === 'variables'} timeout={300}>
            <Box sx={{ display: activePanel === 'variables' ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 1
                }}>
                  Data Selection
                </Typography>

                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="dataset-select-label">Dataset</InputLabel>
                  <Select
                    labelId="dataset-select-label"
                    value={selectedDatasetId}
                    label="Dataset"
                    onChange={handleDatasetChange}
                    disabled={datasets.length === 0}
                  >
                    {datasets.length === 0 ? (
                      <MenuItem value="" disabled>No datasets available</MenuItem>
                    ) : (
                      datasets.map(dataset => (
                        <MenuItem key={dataset.id} value={dataset.id}>
                          {dataset.name} ({dataset.data.length} rows)
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>

                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="value-variable-label">Value Variable</InputLabel>
                  <Select
                    labelId="value-variable-label"
                    value={valueVariable}
                    label="Value Variable"
                    onChange={handleValueVariableChange}
                    disabled={numericColumns.length === 0 || !selectedDatasetId}
                  >
                    {numericColumns.length === 0 ? (
                      <MenuItem value="" disabled>No numeric variables</MenuItem>
                    ) : (
                      numericColumns.map(column => (
                        <MenuItem key={column.id} value={column.id}>
                          {column.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>

                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="group-variable-label">Group By (Optional)</InputLabel>
                  <Select
                    labelId="group-variable-label"
                    value={groupVariable}
                    label="Group By (Optional)"
                    onChange={handleGroupVariableChange}
                    disabled={categoricalColumns.length === 0 || !selectedDatasetId}
                  >
                    <MenuItem value="">No Grouping</MenuItem>
                    {categoricalColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <Box sx={{ mt: 2 }}>
                  <Typography gutterBottom>Number of Bins</Typography>
                  <Slider
                    value={chartSettings.bins}
                    min={5}
                    max={100}
                    step={1}
                    marks={[{ value: 5, label: '5' }, { value: 25, label: '25' }, { value: 50, label: '50' }, { value: 100, label: '100' }]}
                    onChange={(_e, value) => handleSettingsChange('bins', value as number)}
                    valueLabelDisplay="auto"
                    size="small"
                  />
                </Box>

                <FormGroup sx={{ mt: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        size="small"
                        checked={chartSettings.histnorm === 'density' || chartSettings.histnorm === 'probability density'}
                        onChange={(e) => handleSettingsChange('histnorm', e.target.checked ? 'density' : '')}
                      />
                    }
                    label="Density Plot"
                  />
                  <Typography variant="caption" color="text.secondary" sx={{ml:4}}>
                    (vs. Frequency)
                  </Typography>

                  <FormControlLabel
                    control={
                      <Switch
                        size="small"
                        checked={chartSettings.showOutliers}
                        onChange={(e) => handleSettingsChange('showOutliers', e.target.checked)}
                      />
                    }
                    label="Include Outliers"
                  />
                </FormGroup>

                {/* Action Buttons */}
                <Box mt={2} display="flex" gap={1} flexDirection="column">
                  <Button
                    variant="contained"
                    onClick={generateHistogram}
                    disabled={!valueVariable || loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <BarChartIcon />}
                    fullWidth
                  >
                    {loading ? 'Generating...' : 'Generate Chart'}
                  </Button>
                  <Box display="flex" gap={1} justifyContent="center">
                    <Tooltip title="Download Chart">
                      <IconButton onClick={downloadChart} disabled={plotlyData.length === 0}>
                        <SaveIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reset Settings">
                      <IconButton onClick={resetChartSettings}>
                        <RefreshIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>

          {/* Chart Settings Panel */}
          <Fade in={activePanel === 'settings'} timeout={300}>
            <Box sx={{ display: activePanel === 'settings' ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 1
                }}>
                  Chart Settings
                </Typography>

                {/* Labels & Title Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Labels & Title
                  </Typography>
                  <TextField
                    fullWidth
                    label="Chart Title"
                    value={chartSettings.title}
                    onChange={(e) => handleSettingsChange('title', e.target.value)}
                    margin="normal"
                    variant="outlined"
                    size="small"
                  />
                  <TextField
                    fullWidth
                    label="X-Axis Label"
                    value={chartSettings.xAxisLabel}
                    onChange={(e) => handleSettingsChange('xAxisLabel', e.target.value)}
                    margin="normal"
                    variant="outlined"
                    size="small"
                  />
                  <TextField
                    fullWidth
                    label="Y-Axis Label"
                    value={chartSettings.yAxisLabel}
                    onChange={(e) => handleSettingsChange('yAxisLabel', e.target.value)}
                    margin="normal"
                    variant="outlined"
                    size="small"
                  />
                </Box>

                {/* Appearance Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Appearance
                  </Typography>
                  <FormControl fullWidth margin="normal" size="small">
                    <InputLabel id="color-scheme-label">Color Scheme</InputLabel>
                    <Select
                      labelId="color-scheme-label"
                      value={chartSettings.colorScheme}
                      label="Color Scheme"
                      onChange={(e) => handleSettingsChange('colorScheme', e.target.value)}
                    >
                      {Object.keys(colorSchemes).map(scheme => (
                        <MenuItem key={scheme} value={scheme}>
                          {scheme.charAt(0).toUpperCase() + scheme.slice(1)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                {/* Display Options Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Display Options
                  </Typography>
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.showNormalCurve}
                          onChange={(e) => handleSettingsChange('showNormalCurve', e.target.checked)}
                        />
                      }
                      label="Show Normal Curve"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.showMeanLine}
                          onChange={(e) => handleSettingsChange('showMeanLine', e.target.checked)}
                        />
                      }
                      label="Show Mean Line"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.showMedianLine}
                          onChange={(e) => handleSettingsChange('showMedianLine', e.target.checked)}
                        />
                      }
                      label="Show Median Line"
                    />
                  </FormGroup>
                </Box>

                {/* Apply Button */}
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={generateHistogram}
                  disabled={!valueVariable || loading || !selectedDatasetId}
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  Apply Customizations & Regenerate
                </Button>
              </Paper>
            </Box>
          </Fade>
        </Grid>

        {/* Chart Display Panel */}
        <Grid item xs={12} sm={12} md={9} lg={9}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Chart Preview
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  Active: {activePanel === 'variables' ? 'Variables' : 'Settings'}
                </Typography>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: activePanel === 'variables' ? theme.palette.primary.main : theme.palette.warning.main,
                    boxShadow: `0 0 0 2px ${activePanel === 'variables' ? theme.palette.primary.main + '20' : theme.palette.warning.main + '20'}`
                  }}
                />
              </Box>
            </Box>

            {error && (
              <Box sx={{ mb: 2, p: 2, backgroundColor: theme.palette.error.light + '20', borderRadius: 1, border: `1px solid ${theme.palette.error.light}` }}>
                <Typography color="error">{error}</Typography>
              </Box>
            )}

            <Box
              id="plotly-histogram-container"
              sx={{
                minHeight: 500,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.01)' : 'rgba(0, 0, 0, 0.01)'
              }}
            >
              {loading ? (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
                  <CircularProgress />
                  <Typography color="text.secondary">Generating histogram...</Typography>
                </Box>
              ) : plotlyData.length > 0 ? (
                <Plot
                  divId="plotly-histogram"
                  data={plotlyData}
                  layout={{
                    ...plotlyLayout,
                    height: 500,
                    autosize: true,
                    paper_bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#fff',
                    plot_bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.default : '#fff',
                    font: { color: theme.palette.text.primary }
                  }}
                  useResizeHandler={true}
                  style={{ width: '100%', height: '500px' }}
                  config={{
                    displayModeBar: true,
                    displaylogo: false,
                    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d'],
                    responsive: true,
                    toImageButtonOptions: {
                      format: 'svg',
                      filename: chartSettings.title.replace(/\s+/g, '_') || 'histogram',
                      width: 800,
                      height: 600,
                      scale: 1
                    }
                  }}
                />
              ) : (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2} p={4}>
                  <BarChartIcon sx={{ fontSize: 48, color: 'text.disabled' }} />
                  <Typography color="text.secondary" textAlign="center">
                    {!activeDataset
                      ? 'Select a dataset to begin'
                      : !valueVariable
                      ? 'Select a value variable to generate the histogram'
                      : 'Chart will appear here once generated'
                    }
                  </Typography>
                  {activeDataset && !valueVariable && (
                    <Typography variant="body2" color="text.disabled" textAlign="center">
                      Switch to the Variables panel to select your data
                    </Typography>
                  )}
                </Box>
              )}
            </Box>

            {statistics && plotlyData.length > 0 && (
              <Box mt={2} p={2} bgcolor="background.paper" borderRadius={1} boxShadow={1}>
                <Typography variant="subtitle2" gutterBottom>Summary Statistics</Typography>
                <Grid container spacing={1}>
                  <Grid item xs={6} sm={3}><Typography variant="body2">Count: <strong>{statistics.totalCount}</strong></Typography></Grid>
                  <Grid item xs={6} sm={3}><Typography variant="body2">Mean: <strong>{statistics.mean.toFixed(2)}</strong></Typography></Grid>
                  <Grid item xs={6} sm={3}><Typography variant="body2">Median: <strong>{statistics.median.toFixed(2)}</strong></Typography></Grid>
                  <Grid item xs={6} sm={3}><Typography variant="body2">Std. Dev: <strong>{statistics.stdDev.toFixed(2)}</strong></Typography></Grid>
                </Grid>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Histogram;
