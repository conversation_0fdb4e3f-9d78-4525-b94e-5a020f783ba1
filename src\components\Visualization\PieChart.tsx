import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  SelectChangeEvent,
  Slider,
  TextField,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  CircularProgress,
  useTheme,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Fade
} from '@mui/material';
import {
  PieChart as PieChartIconMui, // Renamed
  Tune as TuneIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  DataObject as DataObjectIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, Column } from '../../types';
import { calculateFrequencies, calculateMean } from '@/utils/stats';
import { getOrderedChartData, getOrderedCategoriesByColumnId } from '../../utils/dataUtilities';
import * as Plotly from 'plotly.js'; // Import Plotly

// Define Plotly types
type PlotlyData = Partial<Plotly.PlotData>;
type PlotlyLayout = Partial<Plotly.Layout>;
type PlotlyConfig = Partial<Plotly.Config>;

// Define valid color scheme names
const colorSchemes = {
  default: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
  pastel: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5', '#d9d9d9', '#bc80bd'],
  bold: ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628', '#f781bf', '#999999'],
  sequential: ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#08519c', '#08306b'],
  diverging: ['#a50026', '#d73027', '#f46d43', '#fdae61', '#fee090', '#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
};
type ColorSchemeName = keyof typeof colorSchemes;

// Chart Settings Interface for Plotly Pie/Donut
interface ChartSettings {
  title: string;
  textInfo: 'percent' | 'value' | 'label' | 'label+percent' | 'label+value' | 'value+percent' | 'none'; // Maps to textinfo
  hole: number; // 0 for pie, >0 for donut (e.g., 0.4)
  colorScheme: ColorSchemeName;
  legendPosition: { x: number; y: number; xanchor?: 'auto' | 'left' | 'center' | 'right'; yanchor?: 'auto' | 'top' | 'middle' | 'bottom'; };
  sort: boolean; // Plotly sorts descending by default if true
  maxSlices: number; // Handled in data processing
  labelPosition: 'inside' | 'outside' | 'auto' | 'none'; // Maps to textposition
  pull: number; // Separation between slices (0 to 1)
}

// Default settings for Plotly Pie
const defaultChartSettings: ChartSettings = {
  title: 'Pie Chart',
  textInfo: 'label+percent', 
  hole: 0, 
  colorScheme: 'default',
  legendPosition: { y: 0.5, x: 1.1, xanchor: 'left', yanchor: 'middle' }, 
  sort: true, // Enable Plotly's default descending sort
  maxSlices: 10,
  labelPosition: 'outside',
  pull: 0, 
};

const PLOTLY_PIE_CHART_DIV_ID = 'plotlyPieChartDiv';

const PieChart: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const plotlyDivRef = useRef<HTMLDivElement>(null);
  
  // State
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [categoryVariable, setCategoryVariable] = useState<string>('');
  const [valueVariable, setValueVariable] = useState<string>(''); 
  const [aggregationMethod, setAggregationMethod] = useState<string>('sum');
  const [displayMode, setDisplayMode] = useState<'frequency' | 'percentage'>('frequency');
  const [chartSettings, setChartSettings] = useState<ChartSettings>(defaultChartSettings);
  const [activePanel, setActivePanel] = useState<'variables' | 'settings'>('variables');
  const [plotlyConfig, setPlotlyConfig] = useState<{ data: PlotlyData[], layout: PlotlyLayout } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const activeDataset = React.useMemo(() => {
    if (!selectedDatasetId) return null;
    return datasets.find(ds => ds.id === selectedDatasetId) || null;
  }, [datasets, selectedDatasetId]);
  
  // Memoized columns
  const categoricalColumns = React.useMemo(() => 
    activeDataset?.columns.filter(col => col.type === DataType.CATEGORICAL) || [],
    [activeDataset]
  );
  const numericColumns = React.useMemo(() =>
    activeDataset?.columns.filter(col => col.type === DataType.NUMERIC) || [],
    [activeDataset]
  );
  
  // Effect to update selected dataset ID
  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
      setCategoryVariable('');
      setValueVariable('');
      setPlotlyConfig(null);
      setError(null);
    }
  }, [currentDataset]); // Dependency array changed to [currentDataset]

  // Effect to render Plotly chart
  useEffect(() => {
    if (plotlyConfig && plotlyDivRef.current) {
      const config: PlotlyConfig = {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d']
      };

      // Enhanced layout for better positioning
      const enhancedLayout = {
        ...plotlyConfig.layout,
        height: 500,
        width: undefined, // Let it be responsive
        autosize: true
      };

      Plotly.newPlot(PLOTLY_PIE_CHART_DIV_ID, plotlyConfig.data, enhancedLayout, config);
    }
    return () => {
      if (plotlyDivRef.current) {
         if (typeof Plotly !== 'undefined' && Plotly.purge) {
            Plotly.purge(plotlyDivRef.current);
         }
      }
    };
  }, [plotlyConfig]);

  // Auto-generate chart when variables change
  useEffect(() => {
    if (categoryVariable && activeDataset) {
      generatePlotlyData();
    }
  }, [categoryVariable, valueVariable, aggregationMethod, displayMode, chartSettings, activeDataset]);

  // Keyboard accessibility for panel switching
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + 1 for Variables panel, Alt + 2 for Settings panel
      if (event.altKey && !event.ctrlKey && !event.shiftKey) {
        if (event.key === '1') {
          event.preventDefault();
          setActivePanel('variables');
        } else if (event.key === '2') {
          event.preventDefault();
          setActivePanel('settings');
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);
  
  // --- Handlers ---
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    setSelectedDatasetId(event.target.value);
    setCategoryVariable('');
    setValueVariable('');
    setPlotlyConfig(null);
    setError(null);
  };

  const handleCategoryVariableChange = (event: SelectChangeEvent<string>) => {
    setCategoryVariable(event.target.value);
  };

  const handleValueVariableChange = (event: SelectChangeEvent<string>) => {
    setValueVariable(event.target.value); 
  };

  const handleAggregationMethodChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAggregationMethod(event.target.value);
  };

  const handleDisplayModeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDisplayMode(event.target.value as 'frequency' | 'percentage');
  };

  const handleSettingsChange = (setting: keyof ChartSettings, value: any) => {
     if (setting === 'hole' && typeof value === 'boolean') {
        value = value ? 0.4 : 0; 
     }
     if (setting === 'colorScheme') value = value as ColorSchemeName;
     if (setting === 'textInfo') value = value as ChartSettings['textInfo'];
     if (setting === 'labelPosition') value = value as ChartSettings['labelPosition'];
     
     setChartSettings(prev => ({ ...prev, [setting]: value }));
  };

  // --- Data Generation for Plotly ---
  const generatePlotlyData = () => {
    if (!activeDataset || !categoryVariable) {
      setError('Please select a dataset and a category variable.');
      return;
    }
    
    setLoading(true);
    setError(null);
    setPlotlyConfig(null);

    try {
      const categoryCol = activeDataset.columns.find(col => col.id === categoryVariable);
      if (!categoryCol) throw new Error('Category column not found.');
      
      let valueCol: Column | undefined | null = null;
      if (valueVariable) { 
          valueCol = activeDataset.columns.find(col => col.id === valueVariable);
          if (!valueCol) throw new Error('Selected value column not found.');
      }

      let processedData: { label: string; value: number }[] = [];
      let traceName = categoryCol.name; 

      // Case 1: Frequency/Percentage (No Value Variable)
      if (!valueVariable) {
        // Use ordered chart data for consistent category ordering
        const chartData = getOrderedChartData(categoryVariable, null, activeDataset, 'count');
        const totalCount = activeDataset.data.length;
        if (totalCount === 0) throw new Error('No data for category variable.');

        processedData = chartData.map(({ category, value }) => ({
          label: category,
          value: displayMode === 'percentage' ? (value / totalCount) * 100 : value
        }));
        traceName = displayMode === 'percentage' ? 'Percentage' : 'Frequency';
      }
      // Case 2: Aggregation (Value Variable selected)
      else if (valueCol) {
        traceName = `${aggregationMethod.charAt(0).toUpperCase() + aggregationMethod.slice(1)} of ${valueCol.name} by ${categoryCol.name}`;
        const groupedData: { [key: string]: number[] } = {};
        activeDataset.data.forEach(row => {
          const categoryValue = String(row[categoryCol.name]);
          if (!groupedData[categoryValue]) groupedData[categoryValue] = [];
          const val = row[valueCol.name]; 
          if (typeof val === 'number' && !isNaN(val)) {
            groupedData[categoryValue].push(val);
          }
        });

        // Use ordered categories for consistent ordering
        const orderedCategories = getOrderedCategoriesByColumnId(categoryVariable, activeDataset);
        processedData = orderedCategories.map(label => {
          const values = groupedData[label] || [];
          let aggregatedValue = 0;
          if (values.length > 0) {
            switch (aggregationMethod) {
              case 'sum': aggregatedValue = values.reduce((s, v) => s + v, 0); break;
              case 'average': aggregatedValue = calculateMean(values); break;
              case 'count': aggregatedValue = values.length; break;
              case 'max': aggregatedValue = Math.max(...values); break;
              case 'min': aggregatedValue = Math.min(...values); break;
              default: aggregatedValue = values.reduce((s, v) => s + v, 0);
            }
          }
          return { label, value: aggregatedValue };
        });
        traceName = `${aggregationMethod.charAt(0).toUpperCase() + aggregationMethod.slice(1)} of ${valueCol.name}`;
      }

      // Filter out zero/negative/NaN values
      processedData = processedData.filter(item => item.value > 0 && !isNaN(item.value));
      if (processedData.length === 0) throw new Error('No positive data available to plot.');

      // Apply Max Slices Limit ("Other" category) BEFORE sorting if sort is disabled
      // If sort is enabled, Plotly handles it, but we might need to apply max slices after Plotly sorts internally (not directly possible here)
      // Let's apply max slices before passing data to Plotly, regardless of sort setting for simplicity.
      if (processedData.length > chartSettings.maxSlices) {
        // Sort temporarily to group smallest slices, even if final sort is disabled
        const sortedForOther = [...processedData].sort((a, b) => b.value - a.value); 
        const topSlices = sortedForOther.slice(0, chartSettings.maxSlices - 1);
        const otherSlices = sortedForOther.slice(chartSettings.maxSlices - 1);
        const otherValue = otherSlices.reduce((sum, item) => sum + item.value, 0);
        if (otherValue > 0) {
           topSlices.push({ label: 'Other', value: otherValue });
        }
        processedData = topSlices;
      }

      // --- Create Plotly Trace ---
      const labels = processedData.map(d => d.label);
      const values = processedData.map(d => d.value);
      const colors = getChartColors();

      const plotData: PlotlyData[] = [{
          type: 'pie',
          labels: labels,
          values: values,
          name: traceName, 
          hole: chartSettings.hole,
          textinfo: chartSettings.textInfo,
          textposition: chartSettings.labelPosition,
          marker: { 
              colors: colors.slice(0, processedData.length), 
              line: { color: theme.palette.background.paper, width: 1 }
           },
           pull: chartSettings.pull > 0 ? processedData.map((_, i) => i === 0 ? chartSettings.pull : 0) : 0, 
           sort: chartSettings.sort, // Let Plotly handle sorting (descending)
           // @ts-ignore - hoverinfo type definition might be incomplete for 'label+percent' on pie traces
           hoverinfo: 'label+percent', 
           insidetextorientation: 'radial' 
       }];

      // --- Create Plotly Layout ---
      const layout: PlotlyLayout = {
        title: { text: chartSettings.title, font: { size: 16 } },
        margin: { t: 50, b: 50, l: 50, r: 50 }, // Optimized margins for better positioning
        paper_bgcolor: theme.palette.mode === 'dark' ? '#333' : '#fff',
        plot_bgcolor: theme.palette.mode === 'dark' ? '#222' : '#fff',
        font: { color: theme.palette.text.primary },
        showlegend: true,
        legend: {
           ...chartSettings.legendPosition,
           bgcolor: theme.palette.mode === 'dark' ? 'rgba(51,51,51,0.8)' : 'rgba(255,255,255,0.8)',
           bordercolor: theme.palette.divider, borderwidth: 1,
           orientation: (chartSettings.legendPosition.xanchor === 'center' && (chartSettings.legendPosition.y === -0.2 || chartSettings.legendPosition.y === 1.2)) ? 'h' : 'v'
        },
        autosize: true
      };

      setPlotlyConfig({ data: plotData, layout });

    } catch (err) {
      setError(`Error generating chart data: ${err instanceof Error ? err.message : String(err)}`);
      setPlotlyConfig(null);
    } finally {
      setLoading(false);
    }
  };
  
  // Reset settings
  const resetChartSettings = () => {
    setChartSettings(defaultChartSettings);
  };
  
  // Download chart
  const downloadChart = () => {
    if (plotlyDivRef.current && plotlyConfig) {
      const downloadOpts: Plotly.DownloadImgopts = {
        format: 'svg',
        filename: chartSettings.title.replace(/\s+/g, '_') || 'piechart',
        width: plotlyDivRef.current.offsetWidth || 600,
        height: plotlyDivRef.current.offsetHeight || 500,
      };
      Plotly.downloadImage(PLOTLY_PIE_CHART_DIV_ID, downloadOpts);
    } else {
      setError('Chart data not available for download.');
    }
  };
  
  // Generate pie chart with current settings
  const generatePieChart = () => {
    generatePlotlyData();
  };
  
  // Get colors
  const getChartColors = () => {
    return colorSchemes[chartSettings.colorScheme] || colorSchemes.default;
  };

  // Button disabled states
  const isGenerateDisabled = !categoryVariable || loading;
  const isDownloadDisabled = !plotlyConfig || loading;
  
  // --- Render ---
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>Pie Chart Generator</Typography>

      {/* Three-Panel Layout */}
      <Grid container spacing={2}>
        {/* Left Panel Container - Variables or Settings */}
        <Grid item xs={12} sm={12} md={3} lg={3}>
          {/* Panel Toggle Tabs */}
          <Paper
            elevation={1}
            sx={{
              mb: 1,
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)'
            }}
          >
            <Tabs
              value={activePanel}
              onChange={(_, newValue) => setActivePanel(newValue)}
              variant="fullWidth"
              sx={{
                minHeight: 44,
                '& .MuiTab-root': {
                  minHeight: 44,
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  color: theme.palette.text.secondary,
                  textTransform: 'none',
                  transition: 'all 0.2s ease-in-out',
                  '&.Mui-selected': {
                    color: theme.palette.primary.main,
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(25, 118, 210, 0.08)',
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
                  }
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                }
              }}
            >
              <Tooltip title="Variable Selection Panel" placement="top">
                <Tab
                  value="variables"
                  label="Variables"
                  icon={<DataObjectIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
              <Tooltip title="Chart Settings Panel" placement="top">
                <Tab
                  value="settings"
                  label="Settings"
                  icon={<SettingsIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
            </Tabs>
          </Paper>

          {/* Variable Selection Panel */}
          <Fade in={activePanel === 'variables'} timeout={300}>
            <Box sx={{ display: activePanel === 'variables' ? 'block' : 'none' }}>
              <Paper elevation={2} sx={{ p: 2, height: 'fit-content' }}>
                <Typography variant="h6" gutterBottom>
                  Variable Selection
                </Typography>
                {/* Dataset Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="dataset-select-label">Dataset</InputLabel>
                  <Select labelId="dataset-select-label" value={selectedDatasetId} label="Dataset" onChange={handleDatasetChange} disabled={datasets.length === 0}>
                    {datasets.map(ds => <MenuItem key={ds.id} value={ds.id}>{ds.name} ({ds.data.length} rows)</MenuItem>)}
                  </Select>
                </FormControl>

                {/* Category Variable */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="category-variable-label">Category Variable</InputLabel>
                  <Select labelId="category-variable-label" value={categoryVariable} label="Category Variable" onChange={handleCategoryVariableChange} disabled={categoricalColumns.length === 0}>
                    {categoricalColumns.map(col => <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>)}
                  </Select>
                  <Typography variant="caption" color="text.secondary">Variable defining the slices</Typography>
                </FormControl>

                {/* Value Variable */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="value-variable-label">Value Variable (Optional)</InputLabel>
                  <Select labelId="value-variable-label" value={valueVariable} label="Value Variable (Optional)" onChange={handleValueVariableChange} disabled={numericColumns.length === 0}>
                     <MenuItem value=""><em>None (Use Frequency/Percentage)</em></MenuItem>
                     {numericColumns.map(col => <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>)}
                  </Select>
                  <Typography variant="caption" color="text.secondary">Variable determining slice size (if selected)</Typography>
                </FormControl>

                {/* Aggregation Method */}
                {valueVariable && (
                  <FormControl component="fieldset" fullWidth margin="normal">
                    <Typography variant="subtitle2" gutterBottom>Aggregation Method</Typography>
                    <RadioGroup value={aggregationMethod} onChange={handleAggregationMethodChange} row>
                      <FormControlLabel value="sum" control={<Radio size="small" />} label="Sum" />
                      <FormControlLabel value="average" control={<Radio size="small" />} label="Average" />
                      <FormControlLabel value="count" control={<Radio size="small" />} label="Count" />
                      <FormControlLabel value="max" control={<Radio size="small" />} label="Max" />
                      <FormControlLabel value="min" control={<Radio size="small" />} label="Min" />
                    </RadioGroup>
                  </FormControl>
                )}

                {/* Display Mode */}
                {!valueVariable && categoryVariable && (
                  <FormControl component="fieldset" fullWidth margin="normal">
                    <Typography variant="subtitle2" gutterBottom>Display Mode</Typography>
                    <RadioGroup value={displayMode} onChange={handleDisplayModeChange} row>
                      <FormControlLabel value="frequency" control={<Radio size="small" />} label="Frequency" />
                      <FormControlLabel value="percentage" control={<Radio size="small" />} label="Percentage" />
                    </RadioGroup>
                  </FormControl>
                )}

                {/* Action Buttons */}
                <Box mt={2} display="flex" gap={1} flexDirection="column">
                  <Button
                    variant="contained"
                    onClick={generatePlotlyData}
                    disabled={isGenerateDisabled}
                    startIcon={loading ? <CircularProgress size={20} /> : <PieChartIconMui />}
                    fullWidth
                  >
                    {loading ? 'Generating...' : 'Generate Pie Chart'}
                  </Button>
                  <Box display="flex" gap={1} justifyContent="center">
                    <Tooltip title="Download Chart">
                      <IconButton onClick={downloadChart} disabled={isDownloadDisabled}>
                        <SaveIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reset Settings">
                      <IconButton onClick={resetChartSettings}>
                        <RefreshIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>

          {/* Chart Settings Panel */}
          <Fade in={activePanel === 'settings'} timeout={300}>
            <Box sx={{ display: activePanel === 'settings' ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 2,
                  mb: 2
                }}>
                  Chart Settings
                </Typography>

                {/* Labels & Title Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 1 }}>
                    Labels & Title
                  </Typography>
                  <TextField
                    fullWidth
                    label="Chart Title"
                    value={chartSettings.title}
                    onChange={(e) => handleSettingsChange('title', e.target.value)}
                    margin="normal"
                    size="small"
                  />
                  <FormControl fullWidth margin="normal" size="small">
                    <InputLabel id="textinfo-label">Show on Slices</InputLabel>
                    <Select
                      labelId="textinfo-label"
                      value={chartSettings.textInfo}
                      label="Show on Slices"
                      onChange={(e) => handleSettingsChange('textInfo', e.target.value as ChartSettings['textInfo'])}
                    >
                       <MenuItem value="label+percent">Label + Percent</MenuItem>
                       <MenuItem value="label+value">Label + Value</MenuItem>
                       <MenuItem value="label">Label Only</MenuItem>
                       <MenuItem value="percent">Percent Only</MenuItem>
                       <MenuItem value="value">Value Only</MenuItem>
                       <MenuItem value="none">None</MenuItem>
                    </Select>
                  </FormControl>
                  <FormControl fullWidth margin="normal" size="small">
                    <InputLabel id="label-position-label">Label Position</InputLabel>
                    <Select
                      labelId="label-position-label"
                      value={chartSettings.labelPosition}
                      label="Label Position"
                      onChange={(e) => handleSettingsChange('labelPosition', e.target.value as ChartSettings['labelPosition'])}
                    >
                       <MenuItem value="inside">Inside</MenuItem>
                       <MenuItem value="outside">Outside</MenuItem>
                       <MenuItem value="auto">Auto</MenuItem>
                       <MenuItem value="none">None (Hides labels)</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                {/* Appearance Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Appearance
                  </Typography>
                  <FormControl fullWidth margin="normal" size="small">
                    <InputLabel id="color-scheme-label">Color Scheme</InputLabel>
                    <Select
                      labelId="color-scheme-label"
                      value={chartSettings.colorScheme}
                      label="Color Scheme"
                      onChange={(e) => handleSettingsChange('colorScheme', e.target.value as ColorSchemeName)}
                    >
                      {Object.keys(colorSchemes).map(scheme =>
                        <MenuItem key={scheme} value={scheme}>
                          {scheme.charAt(0).toUpperCase() + scheme.slice(1)}
                        </MenuItem>
                      )}
                    </Select>
                  </FormControl>
                </Box>

                {/* Display Options Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Display Options
                  </Typography>
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={chartSettings.hole > 0}
                          onChange={(e) => handleSettingsChange('hole', e.target.checked)}
                          size="small"
                        />
                      }
                      label="Donut Chart"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={chartSettings.sort}
                          onChange={(e) => handleSettingsChange('sort', e.target.checked)}
                          size="small"
                        />
                      }
                      label="Sort Slices (Descending)"
                    />
                  </FormGroup>
                </Box>

                {/* Size & Styling Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Size & Styling
                  </Typography>
                  {chartSettings.hole > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography gutterBottom>Donut Hole Size</Typography>
                      <Slider
                        value={chartSettings.hole}
                        min={0.1}
                        max={0.8}
                        step={0.05}
                        onChange={(_e, value) => handleSettingsChange('hole', value)}
                        valueLabelDisplay="auto"
                        size="small"
                      />
                    </Box>
                  )}
                  <Box sx={{ mt: 2 }}>
                    <Typography gutterBottom>Slice Separation (Pull)</Typography>
                    <Slider
                      value={chartSettings.pull}
                      min={0}
                      max={0.5}
                      step={0.05}
                      onChange={(_e, value) => handleSettingsChange('pull', value)}
                      valueLabelDisplay="auto"
                      size="small"
                    />
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <Typography gutterBottom>Maximum Slices (incl. "Other")</Typography>
                    <Slider
                      value={chartSettings.maxSlices}
                      min={2}
                      max={20}
                      step={1}
                      onChange={(_e, value) => handleSettingsChange('maxSlices', value)}
                      valueLabelDisplay="auto"
                      size="small"
                    />
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>
        </Grid>

        {/* Chart Display Panel */}
        <Grid item xs={12} sm={12} md={9} lg={9}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Chart Preview
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  Active: {activePanel === 'variables' ? 'Variables' : 'Settings'}
                </Typography>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: activePanel === 'variables' ? theme.palette.primary.main : theme.palette.warning.main,
                    boxShadow: `0 0 0 2px ${activePanel === 'variables' ? theme.palette.primary.main + '20' : theme.palette.warning.main + '20'}`
                  }}
                />
              </Box>
            </Box>

            {error && (
              <Box sx={{ mb: 2, p: 2, backgroundColor: theme.palette.error.light + '20', borderRadius: 1, border: `1px solid ${theme.palette.error.light}` }}>
                <Typography color="error">{error}</Typography>
              </Box>
            )}

            <Box
              ref={plotlyDivRef}
              id={PLOTLY_PIE_CHART_DIV_ID}
              sx={{
                height: 500,
                width: '100%',
                display: plotlyConfig ? 'block' : 'flex',
                justifyContent: plotlyConfig ? 'flex-start' : 'center',
                alignItems: plotlyConfig ? 'flex-start' : 'center',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.01)' : 'rgba(0, 0, 0, 0.01)',
                overflow: 'hidden'
              }}
            >
              {loading ? (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
                  <CircularProgress />
                  <Typography color="text.secondary">Generating chart...</Typography>
                </Box>
              ) : plotlyConfig ? (
                // Chart will be rendered here by Plotly
                <></>
              ) : (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2} p={4}>
                  <PieChartIconMui sx={{ fontSize: 48, color: 'text.disabled' }} />
                  <Typography color="text.secondary" textAlign="center">
                    {!activeDataset
                      ? 'Select a dataset to begin'
                      : !categoryVariable
                      ? 'Select a category variable to generate the pie chart'
                      : 'Chart will appear here once generated'
                    }
                  </Typography>
                  {activeDataset && !categoryVariable && (
                    <Typography variant="body2" color="text.disabled" textAlign="center">
                      Switch to the Variables panel to select your data
                    </Typography>
                  )}
                  <Typography variant="body2" color="text.disabled" textAlign="center">
                    Keyboard shortcuts: Alt+1 (Variables) | Alt+2 (Settings)
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PieChart;
