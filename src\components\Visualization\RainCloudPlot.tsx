import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Slider,
  SelectChangeEvent,
  TextField,
  FormGroup,
  FormControlLabel,
  Switch,
  CircularProgress,
  useTheme,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Fade,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  ShowChart as ShowChartIcon,
  Cloud as CloudIcon,
  Settings as SettingsIcon,
  DataObject as DataObjectIcon,
  ExpandMore as ExpandMoreIcon,
  Help as HelpIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType } from '../../types';
import { calculateQuartiles, calculateMean } from '@/utils/stats';
// Import Plotly correctly after installing the package and types
import * as Plotly from 'plotly.js';

// Define Plotly types based on @types/plotly.js with extensions for violin plots
type PlotlyData = Partial<Plotly.PlotData> & {
  // Additional properties for violin plots
  box?: {
    visible?: boolean;
  };
  meanline?: {
    visible?: boolean;
  };
  side?: 'positive' | 'negative' | 'both';
  jitter?: number;
  scalegroup?: string;
  legendgroup?: string;
  points?: string | false;
  pointpos?: number;
  scalemode?: string;
  hoveron?: string | 'points+kde';
  text?: string;
  span?: number[];
  y0?: string;
  x0?: string;
};
type PlotlyLayout = Partial<Plotly.Layout> & {
  violinmode?: 'group' | 'overlay';
  violingap?: number;
  violingroupgap?: number;
};
type PlotlyConfig = Partial<Plotly.Config>;

interface ChartSettings {
  title: string;
  xAxisLabel: string;
  yAxisLabel: string;
  showOutliers: boolean;
  showMean: boolean;
  horizontal: boolean;
  colorScheme: string;
  jitterAmount: number;
  showBox: boolean;
}

const defaultChartSettings: ChartSettings = {
  title: 'Violin Plot',
  xAxisLabel: 'Category',
  yAxisLabel: 'Value',
  showOutliers: true,
  showMean: true,
  horizontal: false,
  colorScheme: 'default',
  jitterAmount: 0,
  showBox: true
};

const colorSchemes: Record<string, string[]> = {
  default: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
  pastel: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5', '#d9d9d9', '#bc80bd'],
  bold: ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628', '#f781bf', '#999999'],
  sequential: ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#08519c', '#08306b'],
  diverging: ['#a50026', '#d73027', '#f46d43', '#fdae61', '#fee090', '#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
};

interface SummaryStatRow {
  category: string;
  min: number;
  max: number;
  median: number;
  q1: number;
  q3: number;
  mean: number;
  iqr: number;
  sampleSize: number;
}

const PLOTLY_DIV_ID = 'plotlyRainCloudPlotDiv';

// Define the component correctly using React.FC
const RainCloudPlot: React.FC = () => {
  // Hooks should be called inside the component body
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const plotlyDivRef = useRef<HTMLDivElement>(null);

  // State variables
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [valueVariable, setValueVariable] = useState<string>('');
  const [categoryVariable, setCategoryVariable] = useState<string>(''); // Categorical variable
  const [groupingVariable, setGroupingVariable] = useState<string>(''); // Binary grouping variable
  const [chartSettings, setChartSettings] = useState<ChartSettings>(defaultChartSettings);
  const [activePanel, setActivePanel] = useState<'variables' | 'settings'>('variables');
  const [plotlyChartConfig, setPlotlyChartConfig] = useState<{ data: PlotlyData[], layout: PlotlyLayout } | null>(null);
  const [summaryData, setSummaryData] = useState<SummaryStatRow[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const activeDataset = React.useMemo(() => {
    if (!selectedDatasetId) return null;
    return datasets.find(ds => ds.id === selectedDatasetId) || null;
  }, [datasets, selectedDatasetId]);

  // Memoized column lists
  const numericColumns = React.useMemo(() =>
    activeDataset?.columns.filter(col => col.type === DataType.NUMERIC) || [],
    [activeDataset]
  );
  const categoricalColumns = React.useMemo(() =>
    activeDataset?.columns.filter(col => col.type === DataType.CATEGORICAL) || [],
    [activeDataset]
  );

  // Effect to update selected dataset ID when context changes
  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
      // Reset selections when dataset changes
      setValueVariable('');
      setCategoryVariable('');
      setGroupingVariable('');
      setPlotlyChartConfig(null);
      setSummaryData([]);
      setError(null); // Clear errors on dataset change
    }
  }, [currentDataset]);

  // Effect to render Plotly chart when config changes
  useEffect(() => {
    if (plotlyChartConfig && plotlyDivRef.current) {
      const config: PlotlyConfig = { responsive: true };
      Plotly.newPlot(PLOTLY_DIV_ID, plotlyChartConfig.data, plotlyChartConfig.layout, config);
    }
    // Optional: Cleanup function to purge the div when component unmounts or config changes
    return () => {
      if (plotlyDivRef.current) {
        // Check if Plotly object and purge method exist before calling
        if (typeof Plotly !== 'undefined' && Plotly.purge) {
           Plotly.purge(plotlyDivRef.current);
        }
      }
    };
  }, [plotlyChartConfig]);

  // Auto-generate chart when variables or settings change
  useEffect(() => {
    if (valueVariable && activeDataset) {
      generateRainCloudPlot();
    }
  }, [valueVariable, categoryVariable, groupingVariable, chartSettings, activeDataset]);

  // Keyboard accessibility for panel switching
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + 1 for Variables panel, Alt + 2 for Settings panel
      if (event.altKey && !event.ctrlKey && !event.shiftKey) {
        if (event.key === '1') {
          event.preventDefault();
          setActivePanel('variables');
        } else if (event.key === '2') {
          event.preventDefault();
          setActivePanel('settings');
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Handlers
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newId = event.target.value;
    setSelectedDatasetId(newId);
    // Reset variables when dataset changes manually
    setValueVariable('');
    setCategoryVariable('');
    setGroupingVariable('');
    setPlotlyChartConfig(null);
    setSummaryData([]);
    setError(null);
  };

  const handleSettingsChange = (setting: keyof ChartSettings, value: any) => {
    setChartSettings(prev => ({ ...prev, [setting]: value }));
  };

  const generateRainCloudPlot = () => {
    if (!activeDataset || !valueVariable) {
      setError('Please select a dataset and a numerical variable.');
      return;
    }

    setLoading(true);
    setError(null);
    setPlotlyChartConfig(null); // Clear previous chart config
    setSummaryData([]);

    try {
      const valueCol = activeDataset.columns.find(col => col.id === valueVariable);
      const categoryCol = categoryVariable ? activeDataset.columns.find(col => col.id === categoryVariable) : null;
      const groupingCol = groupingVariable ? activeDataset.columns.find(col => col.id === groupingVariable) : null;

      if (!valueCol) throw new Error('Value column not found.');
      if (groupingCol && groupingCol.type !== DataType.CATEGORICAL) throw new Error('Grouping variable must be categorical.');
      
      // Check if grouping variable is binary
      if (groupingCol) {
        const uniqueGroups = [...new Set(activeDataset.data.map(row => row[groupingCol.name]))];
        if (uniqueGroups.length > 2) {
          throw new Error('Grouping variable must be binary (have only two unique values).');
        }
      }

      const dataForPlotly: PlotlyData[] = [];
      const currentSummaryData: SummaryStatRow[] = [];
      const colors = colorSchemes[chartSettings.colorScheme] || colorSchemes.default;
      const allData = activeDataset.data;

      // Group data by category and grouping variable
      const groupedData: Record<string, Record<string, number[]>> = {};
      allData.forEach(row => {
        const catValue = categoryCol ? String(row[categoryCol.name]) : 'All Data';
        const groupValue = groupingCol ? String(row[groupingCol.name]) : 'All';
        const numValue = row[valueCol.name];

        if (typeof numValue === 'number' && !isNaN(numValue)) {
          if (!groupedData[catValue]) groupedData[catValue] = {};
          if (!groupedData[catValue][groupValue]) groupedData[catValue][groupValue] = [];
          groupedData[catValue][groupValue].push(numValue);
        }
      });

      const categoryNames = categoryCol 
        ? [...new Set(activeDataset.data.map(row => row[categoryCol.name]))].map(String).sort()
        : ['All Data'];
      
      const groupingNames = groupingCol 
        ? [...new Set(activeDataset.data.map(row => row[groupingCol.name]))].map(String).sort()
        : ['All'];

      // Create traces for each category and group combination
      categoryNames.forEach((catName, catIndex) => {
        groupingNames.forEach((groupName, groupIndex) => {
          const values = groupedData[catName]?.[groupName] || [];
          if (values.length === 0) return;

          // Determine side and point position for violin
          let side: 'positive' | 'negative' | 'both' = 'both';
          let pointpos = 0;
          
          if (groupingCol && groupingNames.length === 2) {
            // For binary grouping, first group is positive, second is negative
            side = groupIndex === 0 ? 'positive' : 'negative';
            pointpos = groupIndex === 0 ? 0.45 : -0.45;
          }

          const traceName = groupingCol ? groupName : catName;
          const legendGroup = groupingCol ? groupName : catName;
          const scaleGroup = groupingCol ? groupName : catName;
          const color = colors[groupingCol ? groupIndex % colors.length : catIndex % colors.length];

          // Create violin trace following the example format
          const violinTrace: PlotlyData = {
            type: 'violin',
            name: traceName,
            text: `sample length: ${values.length}`,
            hoveron: 'points',
            meanline: {
              visible: chartSettings.showMean
            },
            legendgroup: legendGroup,
            scalegroup: scaleGroup,
            points: chartSettings.showOutliers ? 'all' : false,
            pointpos: pointpos,
            box: {
              visible: chartSettings.showBox
            },
            jitter: chartSettings.jitterAmount,
            scalemode: 'count',
            marker: {
              line: {
                width: 2,
                color: color
              },
              symbol: 'dots'
            },
            showlegend: groupingCol ? (catIndex === 0) : true, // Show legend once per group
            side: side,
            span: [0],
            line: {
              color: color
            },
            orientation: chartSettings.horizontal ? 'h' : 'v'
          };

          // Set x/y based on orientation
          if (chartSettings.horizontal) {
            violinTrace.y0 = catName;
            violinTrace.x = values;
          } else {
            violinTrace.x0 = catName;
            violinTrace.y = values;
          }

          dataForPlotly.push(violinTrace);

          // Calculate summary statistics
          const sortedValues = [...values].sort((a, b) => a - b);
          if (sortedValues.length > 0) {
            const [q1, median, q3] = calculateQuartiles(sortedValues);
            currentSummaryData.push({
              category: groupingCol ? `${catName} - ${groupName}` : catName,
              min: Math.min(...sortedValues),
              max: Math.max(...sortedValues),
              q1, median, q3,
              mean: calculateMean(sortedValues),
              iqr: q3 - q1,
              sampleSize: sortedValues.length,
            });
          }
        });
      });

      // Define Layout following the example format
      const layout: PlotlyLayout = {
        title: {
          text: chartSettings.title + (groupingCol ? '<br><i>scaled by number of observations per group' : '')
        },
        xaxis: {
          title: { text: chartSettings.horizontal ? chartSettings.yAxisLabel : chartSettings.xAxisLabel },
          showgrid: !chartSettings.horizontal
        },
        yaxis: {
          title: { text: chartSettings.horizontal ? chartSettings.xAxisLabel : chartSettings.yAxisLabel },
          showgrid: chartSettings.horizontal
        },
        hovermode: 'closest',
        width: undefined, // Let it be responsive
        height: 600,
        paper_bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#fff',
        plot_bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.default : '#fff',
        font: { color: theme.palette.text.primary },
        legend: {
          tracegroupgap: 0
        },
        violingap: 0,
        violingroupgap: 0,
        violinmode: groupingCol ? 'overlay' : 'group'
      };

      setPlotlyChartConfig({ data: dataForPlotly, layout });
      setSummaryData(currentSummaryData);

    } catch (err) {
      setError(`Error generating violin plot: ${err instanceof Error ? err.message : String(err)}`);
      setPlotlyChartConfig(null);
      setSummaryData([]);
    } finally {
      setLoading(false);
    }
  };

  const resetChartSettings = () => {
    setChartSettings(defaultChartSettings);
  };

  const downloadChart = () => {
    if (plotlyDivRef.current && plotlyChartConfig) {
      const downloadOpts: Plotly.DownloadImgopts = {
        format: 'svg',
        filename: chartSettings.title.replace(/\s+/g, '_') || 'violin_plot',
        width: plotlyDivRef.current.offsetWidth || 600,
        height: plotlyDivRef.current.offsetHeight || 400,
      };
      Plotly.downloadImage(PLOTLY_DIV_ID, downloadOpts);
    } else {
      setError('Chart data not available for download.');
    }
  };



  // Render component
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Rain Cloud Plot Generator
      </Typography>

      {/* Information Section */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box display="flex" alignItems="center" gap={1}>
            <HelpIcon color="primary" />
            <Typography variant="subtitle1">About Rain Cloud Plots</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" color="text.secondary" paragraph>
            Rain cloud plots combine violin plots, box plots, and individual data points to provide a comprehensive view of data distribution.
            They are particularly useful for visualizing the shape, central tendency, and variability of your data.
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            <strong>Keyboard Shortcuts:</strong> Alt+1 (Variables Panel), Alt+2 (Settings Panel)
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Select a numerical variable to get started. Optionally add categorical variables for grouping and comparison.
          </Typography>
        </AccordionDetails>
      </Accordion>

      {/* Three-Panel Layout */}
      <Grid container spacing={2}>
        {/* Left Panel Container - Variables or Settings */}
        <Grid item xs={12} sm={12} md={3} lg={3}>
          {/* Panel Toggle Tabs */}
          <Paper
            elevation={1}
            sx={{
              mb: 1,
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)'
            }}
          >
            <Tabs
              value={activePanel}
              onChange={(_, newValue) => setActivePanel(newValue)}
              variant="fullWidth"
              sx={{
                minHeight: 44,
                '& .MuiTab-root': {
                  minHeight: 44,
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  color: theme.palette.text.secondary,
                  textTransform: 'none',
                  transition: 'all 0.2s ease-in-out',
                  '&.Mui-selected': {
                    color: theme.palette.primary.main,
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(25, 118, 210, 0.08)',
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
                  }
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                }
              }}
            >
              <Tooltip title="Variable Selection Panel" placement="top">
                <Tab
                  value="variables"
                  label="Variables"
                  icon={<DataObjectIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
              <Tooltip title="Chart Settings Panel" placement="top">
                <Tab
                  value="settings"
                  label="Settings"
                  icon={<SettingsIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
            </Tabs>
          </Paper>

          {/* Variable Selection Panel */}
          <Fade in={activePanel === 'variables'} timeout={300}>
            <Box sx={{ display: activePanel === 'variables' ? 'block' : 'none' }}>
              <Paper elevation={2} sx={{ p: 2, height: 'fit-content' }}>
                <Typography variant="h6" gutterBottom>
                  Variable Selection
                </Typography>

                {/* Dataset Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel>Dataset</InputLabel>
                  <Select
                    value={selectedDatasetId}
                    onChange={handleDatasetChange}
                    label="Dataset"
                    disabled={datasets.length === 0}
                  >
                    {datasets.map((dataset) => (
                      <MenuItem key={dataset.id} value={dataset.id}>
                        {dataset.name} ({dataset.data.length} rows)
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Value Variable Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel>Numerical Variable (Required)</InputLabel>
                  <Select
                    value={valueVariable}
                    onChange={(e) => setValueVariable(e.target.value)}
                    label="Numerical Variable (Required)"
                    disabled={numericColumns.length === 0}
                  >
                    {numericColumns.map((col) => (
                      <MenuItem key={col.id} value={col.id}>
                        {col.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Categorical Variable Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel>Factor Variable (Optional)</InputLabel>
                  <Select
                    value={categoryVariable}
                    onChange={(e) => setCategoryVariable(e.target.value)}
                    label="Factor Variable (Optional)"
                    disabled={categoricalColumns.length === 0}
                  >
                    <MenuItem value=""><em>None</em></MenuItem>
                    {categoricalColumns.map((col) => (
                      <MenuItem key={col.id} value={col.id}>
                        {col.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Binary Grouping Variable Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel>Grouping Variable (Optional, Binary)</InputLabel>
                  <Select
                    value={groupingVariable}
                    onChange={(e) => setGroupingVariable(e.target.value)}
                    label="Grouping Variable (Optional, Binary)"
                    disabled={categoricalColumns.length === 0}
                  >
                    <MenuItem value=""><em>None</em></MenuItem>
                    {categoricalColumns.map((col) => (
                      <MenuItem key={col.id} value={col.id}>
                        {col.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Basic Chart Options */}
                <Box mt={2}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Display Options
                  </Typography>
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.showOutliers}
                          onChange={(e) => handleSettingsChange('showOutliers', e.target.checked)}
                        />
                      }
                      label="Show All Points"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.showMean}
                          onChange={(e) => handleSettingsChange('showMean', e.target.checked)}
                        />
                      }
                      label="Show Mean Line"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.showBox}
                          onChange={(e) => handleSettingsChange('showBox', e.target.checked)}
                        />
                      }
                      label="Show Box Plot"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.horizontal}
                          onChange={(e) => handleSettingsChange('horizontal', e.target.checked)}
                        />
                      }
                      label="Horizontal Orientation"
                    />
                  </FormGroup>
                </Box>

                {/* Action Buttons */}
                <Box mt={2} display="flex" gap={1} flexDirection="column">
                  <Button
                    variant="contained"
                    onClick={generateRainCloudPlot}
                    disabled={!valueVariable || loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <ShowChartIcon />}
                    fullWidth
                  >
                    {loading ? 'Generating...' : 'Generate Rain Cloud Plot'}
                  </Button>
                  <Box display="flex" gap={1} justifyContent="center">
                    <Tooltip title="Download Chart">
                      <IconButton onClick={downloadChart} disabled={!plotlyChartConfig}>
                        <SaveIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reset Settings">
                      <IconButton onClick={resetChartSettings}>
                        <RefreshIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>

          {/* Chart Settings Panel */}
          <Fade in={activePanel === 'settings'} timeout={300}>
            <Box sx={{ display: activePanel === 'settings' ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 1
                }}>
                  Chart Settings
                </Typography>

                <Box display="flex" flexDirection="column" gap={3}>
                  {/* Labels & Title Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Labels & Title
                    </Typography>
                    <TextField
                      fullWidth
                      size="small"
                      label="Chart Title"
                      value={chartSettings.title}
                      onChange={(e) => handleSettingsChange('title', e.target.value)}
                      margin="dense"
                    />
                    <TextField
                      fullWidth
                      size="small"
                      label="X-Axis Label"
                      value={chartSettings.xAxisLabel}
                      onChange={(e) => handleSettingsChange('xAxisLabel', e.target.value)}
                      margin="dense"
                    />
                    <TextField
                      fullWidth
                      size="small"
                      label="Y-Axis Label"
                      value={chartSettings.yAxisLabel}
                      onChange={(e) => handleSettingsChange('yAxisLabel', e.target.value)}
                      margin="dense"
                    />
                  </Box>

                  {/* Appearance Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Appearance
                    </Typography>
                    <FormControl fullWidth size="small" margin="dense">
                      <InputLabel>Color Scheme</InputLabel>
                      <Select
                        value={chartSettings.colorScheme}
                        onChange={(e) => handleSettingsChange('colorScheme', e.target.value)}
                        label="Color Scheme"
                      >
                        {Object.keys(colorSchemes).map((scheme) => (
                          <MenuItem key={scheme} value={scheme}>
                            {scheme.charAt(0).toUpperCase() + scheme.slice(1)}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>

                  {/* Size & Styling Section */}
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Size & Styling
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Jitter Amount: {chartSettings.jitterAmount}
                      </Typography>
                      <Slider
                        value={chartSettings.jitterAmount}
                        onChange={(_, value) => handleSettingsChange('jitterAmount', value)}
                        step={0.05}
                        marks
                        min={0}
                        max={0.5}
                        valueLabelDisplay="auto"
                        size="small"
                      />
                    </Box>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>
        </Grid>

        {/* Chart Display Panel */}
        <Grid item xs={12} sm={12} md={9} lg={9}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Chart Preview
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  Active: {activePanel === 'variables' ? 'Variables' : 'Settings'}
                </Typography>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: activePanel === 'variables' ? theme.palette.primary.main : theme.palette.warning.main,
                    boxShadow: `0 0 0 2px ${activePanel === 'variables' ? theme.palette.primary.main + '20' : theme.palette.warning.main + '20'}`
                  }}
                />
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Box
              ref={plotlyDivRef}
              sx={{
                minHeight: 500,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.01)' : 'rgba(0, 0, 0, 0.01)'
              }}
            >
              {loading ? (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
                  <CircularProgress />
                  <Typography color="text.secondary">Generating rain cloud plot...</Typography>
                </Box>
              ) : plotlyChartConfig ? (
                <div
                  id={PLOTLY_DIV_ID}
                  style={{
                    width: '100%',
                    height: '500px'
                  }}
                />
              ) : (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2} p={4}>
                  <CloudIcon sx={{ fontSize: 48, color: 'text.disabled' }} />
                  <Typography color="text.secondary" textAlign="center">
                    {!activeDataset
                      ? 'Select a dataset to begin'
                      : !valueVariable
                      ? 'Select a numerical variable to generate the rain cloud plot'
                      : 'Chart will appear here once generated'
                    }
                  </Typography>
                  {activeDataset && !valueVariable && (
                    <Typography variant="body2" color="text.disabled" textAlign="center">
                      Switch to the Variables panel to select your data
                    </Typography>
                  )}
                </Box>
              )}
            </Box>

            {/* Summary Statistics Table */}
            {summaryData.length > 0 && !loading && !error && (
              <Box mt={3}>
                <Typography variant="subtitle2" gutterBottom>Summary Statistics</Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Category</TableCell>
                        <TableCell align="right">Min</TableCell>
                        <TableCell align="right">Q1</TableCell>
                        <TableCell align="right">Median</TableCell>
                        <TableCell align="right">Q3</TableCell>
                        <TableCell align="right">Max</TableCell>
                        <TableCell align="right">Mean</TableCell>
                        <TableCell align="right">IQR</TableCell>
                        <TableCell align="right">N</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {summaryData.map((row, index) => (
                        <TableRow key={`${row.category}-${index}`}>
                          <TableCell component="th" scope="row">{row.category}</TableCell>
                          <TableCell align="right">{row.min.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.q1.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.median.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.q3.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.max.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.mean.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.iqr.toFixed(2)}</TableCell>
                          <TableCell align="right">{row.sampleSize}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RainCloudPlot;