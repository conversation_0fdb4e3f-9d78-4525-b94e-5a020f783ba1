import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  SelectChangeEvent,
  TextField,
  FormGroup,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  useTheme,
  IconButton,
  Tooltip,
  Switch,
  Slider,
  <PERSON>vider,
  Tabs,
  Tab,
  Fade
} from '@mui/material';
import {
  Tune as TuneIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Bubble<PERSON>hart as BubbleChartIcon,
  Timeline as TimelineIcon,
  ScatterPlot as ScatterPlotIconMui, // Renamed
  Settings as SettingsIcon,
  DataObject as DataObjectIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, Column } from '../../types';
// Removed Recharts imports
// Removed statisticalFunctions import for now (regression/correlation omitted)
import * as Plotly from 'plotly.js'; // Import Plotly

// Define Plotly types
type PlotlyData = Partial<Plotly.PlotData>;
type PlotlyLayout = Partial<Plotly.Layout>;
type PlotlyConfig = Partial<Plotly.Config>;

// Define valid color scheme names
const colorSchemes = {
  default: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
  pastel: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5', '#d9d9d9', '#bc80bd'],
  bold: ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628', '#f781bf', '#999999'],
  sequential: ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#08519c', '#08306b'],
  diverging: ['#a50026', '#d73027', '#f46d43', '#fdae61', '#fee090', '#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
};
type ColorSchemeName = keyof typeof colorSchemes;

// Chart Settings Interface adjusted for Plotly
interface ChartSettings {
  title: string;
  xAxisLabel: string;
  yAxisLabel: string;
  zAxisLabel: string; // For 3D
  showGrid: boolean;
  colorScheme: ColorSchemeName;
  pointSize: number; // Default point size
  labelPoints: boolean; // Show text labels on points
  use3D: boolean; // Toggle between 2D/3D
  jitter: number; // Percentage
}

// Default settings adjusted for Plotly
const defaultChartSettings: ChartSettings = {
  title: 'Scatter Plot',
  xAxisLabel: 'X Variable',
  yAxisLabel: 'Y Variable',
  zAxisLabel: 'Z Variable',
  showGrid: true,
  colorScheme: 'default',
  pointSize: 6, // Plotly default marker size
  labelPoints: false,
  use3D: false,
  jitter: 0
};

const PLOTLY_SCATTER_DIV_ID = 'plotlyScatterDiv';

// Data point structure for internal processing
interface ProcessedDataPoint {
  x: number;
  y: number;
  z?: number;
  size?: number;
  color?: string; // Actual color value
  category?: string; // Original category for grouping/legend
  label?: string; // Text label for the point
}

const ScatterPlot: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const plotlyDivRef = useRef<HTMLDivElement>(null);
  
  // State
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [xVariable, setXVariable] = useState<string>('');
  const [yVariable, setYVariable] = useState<string>('');
  const [zVariable, setZVariable] = useState<string>(''); // Optional Z for 3D
  const [sizeVariable, setSizeVariable] = useState<string>(''); // Optional size mapping
  const [colorVariable, setColorVariable] = useState<string>(''); // Optional color grouping
  const [labelVariable, setLabelVariable] = useState<string>(''); // Optional point label
  
  const [chartSettings, setChartSettings] = useState<ChartSettings>(defaultChartSettings);
  const [activePanel, setActivePanel] = useState<'variables' | 'settings'>('variables');
  
  const [plotlyConfig, setPlotlyConfig] = useState<{ data: PlotlyData[], layout: PlotlyLayout } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const activeDataset = React.useMemo(() => {
    if (!selectedDatasetId) return null;
    return datasets.find(ds => ds.id === selectedDatasetId) || null;
  }, [datasets, selectedDatasetId]);
  
  // Memoized columns
  const numericColumns = React.useMemo(() => 
    activeDataset?.columns.filter(col => col.type === DataType.NUMERIC) || [],
    [activeDataset]
  );
  const categoricalColumns = React.useMemo(() =>
    activeDataset?.columns.filter(col => col.type === DataType.CATEGORICAL) || [],
    [activeDataset]
  );
  const allColumns = React.useMemo(() => activeDataset?.columns || [], [activeDataset]);

  // Effect to update selected dataset ID
  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
      setXVariable(''); setYVariable(''); setZVariable('');
      setSizeVariable(''); setColorVariable(''); setLabelVariable('');
      setPlotlyConfig(null); setError(null);
    }
  }, [currentDataset]); // Dependency array changed to [currentDataset]

  // Effect to render Plotly chart
  useEffect(() => {
    if (plotlyConfig && plotlyDivRef.current) {
      const config: PlotlyConfig = {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d', 'autoScale2d']
      };

      // Enhanced layout with proper sizing
      const enhancedLayout = {
        ...plotlyConfig.layout,
        height: 500,
        width: undefined, // Let it be responsive
        autosize: true,
        margin: { t: 50, b: 50, l: 60, r: 30 }
      };

      Plotly.newPlot(PLOTLY_SCATTER_DIV_ID, plotlyConfig.data, enhancedLayout, config);
    }
    return () => {
      if (plotlyDivRef.current && typeof Plotly !== 'undefined' && Plotly.purge) {
        Plotly.purge(plotlyDivRef.current);
      }
    };
  }, [plotlyConfig]);

  // Keyboard accessibility for panel switching
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + 1 for Variables panel, Alt + 2 for Settings panel
      if (event.altKey && !event.ctrlKey && !event.shiftKey) {
        if (event.key === '1') {
          event.preventDefault();
          setActivePanel('variables');
        } else if (event.key === '2') {
          event.preventDefault();
          setActivePanel('settings');
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);
  
  // --- Handlers ---
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    setSelectedDatasetId(event.target.value);
    setXVariable(''); setYVariable(''); setZVariable('');
    setSizeVariable(''); setColorVariable(''); setLabelVariable('');
    setPlotlyConfig(null); setError(null);
  };
  const handleXVariableChange = (event: SelectChangeEvent<string>) => setXVariable(event.target.value);
  const handleYVariableChange = (event: SelectChangeEvent<string>) => setYVariable(event.target.value);
  const handleZVariableChange = (event: SelectChangeEvent<string>) => setZVariable(event.target.value);
  const handleSizeVariableChange = (event: SelectChangeEvent<string>) => setSizeVariable(event.target.value);
  const handleColorVariableChange = (event: SelectChangeEvent<string>) => setColorVariable(event.target.value);
  const handleLabelVariableChange = (event: SelectChangeEvent<string>) => setLabelVariable(event.target.value);

  const handleSettingsChange = (setting: keyof ChartSettings, value: any) => {
     if (setting === 'colorScheme') value = value as ColorSchemeName;
     setChartSettings(prev => ({ ...prev, [setting]: value }));
  };

  // --- Data Generation for Plotly ---
  const generatePlotlyData = () => {
    if (!activeDataset || !xVariable || !yVariable) {
      setError('Please select at least X and Y variables.');
      return;
    }
    if (chartSettings.use3D && !zVariable) {
      setError('Please select a Z variable for 3D plot.');
      return;
    }
    
    setLoading(true);
    setError(null);
    setPlotlyConfig(null);

    try {
      const xCol = activeDataset.columns.find(col => col.id === xVariable);
      const yCol = activeDataset.columns.find(col => col.id === yVariable);
      const zCol = zVariable ? activeDataset.columns.find(col => col.id === zVariable) : null;
      const sizeCol = sizeVariable ? activeDataset.columns.find(col => col.id === sizeVariable) : null;
      const colorCol = colorVariable ? activeDataset.columns.find(col => col.id === colorVariable) : null;
      const labelCol = labelVariable ? activeDataset.columns.find(col => col.id === labelVariable) : null;

      if (!xCol || !yCol || (zVariable && !zCol) || (sizeVariable && !sizeCol) || (colorVariable && !colorCol) || (labelVariable && !labelCol)) {
        throw new Error('One or more selected columns not found.');
      }

      let processedData: ProcessedDataPoint[] = [];
      const xValuesForRange: number[] = []; // To calculate range for jitter
      const yValuesForRange: number[] = [];

      activeDataset.data.forEach((row, index) => {
        const xVal = row[xCol.name];
        const yVal = row[yCol.name];
        const zVal = zCol ? row[zCol.name] : undefined;
        
        // Basic validation for required axes
        if (typeof xVal !== 'number' || isNaN(xVal) || typeof yVal !== 'number' || isNaN(yVal)) return;
        if (chartSettings.use3D && (typeof zVal !== 'number' || isNaN(zVal))) return;

        xValuesForRange.push(xVal);
        yValuesForRange.push(yVal);

        const point: ProcessedDataPoint = { x: xVal, y: yVal };
        if (chartSettings.use3D && typeof zVal === 'number') point.z = zVal;
        if (sizeCol && typeof row[sizeCol.name] === 'number' && !isNaN(row[sizeCol.name])) point.size = row[sizeCol.name];
        if (colorCol) point.category = String(row[colorCol.name]);
        point.label = labelCol ? String(row[labelCol.name]) : `Point ${index + 1}`;
        
        processedData.push(point);
      });

      if (processedData.length === 0) throw new Error('No valid data points found for selected variables.');

      // Apply Jitter
      if (chartSettings.jitter > 0) {
          const jitterAmount = chartSettings.jitter / 100; // Convert percentage to fraction
          const xRange = Math.max(...xValuesForRange) - Math.min(...xValuesForRange);
          const yRange = Math.max(...yValuesForRange) - Math.min(...yValuesForRange);
          // Avoid division by zero if range is 0
          const xJitter = xRange > 0 ? jitterAmount * xRange : 0; 
          const yJitter = yRange > 0 ? jitterAmount * yRange : 0;

          processedData = processedData.map(p => ({
              ...p,
              x: p.x + (Math.random() - 0.5) * xJitter,
              y: p.y + (Math.random() - 0.5) * yJitter,
          }));
      }

      // --- Create Plotly Traces ---
      const plotData: PlotlyData[] = [];
      const colors = getChartColors();
      const traceType = chartSettings.use3D ? 'scatter3d' : 'scattergl'; // Use scattergl for potentially better 2D performance
      const mode = chartSettings.labelPoints ? 'markers+text' : 'markers';

      if (colorCol) { // Group by color variable
        const uniqueCategories = [...new Set(processedData.map(p => p.category))];
        uniqueCategories.forEach((category, index) => {
          const categoryData = processedData.filter(p => p.category === category);
          // Ensure z values are numbers if use3D is true
          const zValues = chartSettings.use3D ? categoryData.map(p => p.z).filter((z): z is number => typeof z === 'number') : undefined;
          if (chartSettings.use3D && zValues?.length !== categoryData.length) {
              console.warn("Some points filtered out for category", category, "due to missing Z value in 3D mode.");
              // Potentially filter categoryData further if needed, but Plotly might handle mismatched array lengths gracefully.
          }

          const trace: PlotlyData = {
            x: categoryData.map(p => p.x),
            y: categoryData.map(p => p.y),
            z: zValues, // Assign potentially filtered zValues
            type: traceType,
            // @ts-ignore - Type definition might miss 'markers+text'
            mode: mode,
            name: category || 'Undefined',
            text: chartSettings.labelPoints ? categoryData.map(p => p.label ?? '') : undefined, // Provide default empty string for label
            textposition: 'top center',
            marker: {
              color: colors[index % colors.length],
              size: sizeVariable 
                    ? categoryData.map(p => p.size ?? chartSettings.pointSize) // Default size if undefined
                    : chartSettings.pointSize,
              sizemode: sizeVariable ? 'diameter' : undefined, 
              sizeref: sizeVariable ? 2.0 * Math.max(...categoryData.map(p => p.size || 0)) / (40**2) : undefined, 
              sizemin: 4, 
              opacity: 0.7,
            },
            hoverinfo: 'all', // Simplified hoverinfo
          };
          // Only add z property if it's actually defined (for 2D/3D consistency)
          if (!chartSettings.use3D) delete trace.z; 
          
          plotData.push(trace);
        });
      } else { // Single trace
        // Ensure z values are numbers if use3D is true
        const zValues = chartSettings.use3D ? processedData.map(p => p.z).filter((z): z is number => typeof z === 'number') : undefined;
         if (chartSettings.use3D && zValues?.length !== processedData.length) {
              console.warn("Some points filtered out due to missing Z value in 3D mode.");
              // Potentially filter processedData further if needed.
          }

        const trace: PlotlyData = {
          x: processedData.map(p => p.x),
          y: processedData.map(p => p.y),
          z: zValues,
          type: traceType,
           // @ts-ignore - Type definition might miss 'markers+text'
          mode: mode,
          name: yCol?.name || 'Y', 
          text: chartSettings.labelPoints ? processedData.map(p => p.label ?? '') : undefined, // Default empty string
          textposition: 'top center',
          marker: {
            color: colors[0],
            size: sizeVariable 
                  ? processedData.map(p => p.size ?? chartSettings.pointSize) // Default size
                  : chartSettings.pointSize,
            sizemode: sizeVariable ? 'diameter' : undefined,
            sizeref: sizeVariable ? 2.0 * Math.max(...processedData.map(p => p.size || 0)) / (40**2) : undefined,
            sizemin: 4,
            opacity: 0.7,
          },
          hoverinfo: 'all', // Simplified hoverinfo
        };
         if (!chartSettings.use3D) delete trace.z; 

        plotData.push(trace);
      }

      // --- Create Plotly Layout ---
      const layout: PlotlyLayout = {
        title: { text: chartSettings.title },
        margin: { t: 50, b: 50, l: 60, r: 30 },
        paper_bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#fff',
        plot_bgcolor: theme.palette.mode === 'dark' ? theme.palette.background.default : '#fff',
        font: { color: theme.palette.text.primary },
        hovermode: 'closest',
        showlegend: !!colorCol,
        legend: {
           bgcolor: theme.palette.mode === 'dark' ? 'rgba(51,51,51,0.8)' : 'rgba(255,255,255,0.8)',
           bordercolor: theme.palette.divider,
           borderwidth: 1,
        },
        autosize: true,
      };

      if (chartSettings.use3D) {
        layout.scene = {
          xaxis: { title: { text: chartSettings.xAxisLabel }, showgrid: chartSettings.showGrid, zeroline: false, backgroundcolor: theme.palette.mode === 'dark' ? '#222' : '#fff', gridcolor: theme.palette.divider, zerolinecolor: theme.palette.divider },
          yaxis: { title: { text: chartSettings.yAxisLabel }, showgrid: chartSettings.showGrid, zeroline: false, backgroundcolor: theme.palette.mode === 'dark' ? '#222' : '#fff', gridcolor: theme.palette.divider, zerolinecolor: theme.palette.divider },
          zaxis: { title: { text: chartSettings.zAxisLabel }, showgrid: chartSettings.showGrid, zeroline: false, backgroundcolor: theme.palette.mode === 'dark' ? '#222' : '#fff', gridcolor: theme.palette.divider, zerolinecolor: theme.palette.divider },
          camera: { eye: { x: 1.25, y: 1.25, z: 1.25 } } 
        };
      } else {
        layout.xaxis = { title: { text: chartSettings.xAxisLabel }, showgrid: chartSettings.showGrid, zeroline: false }; // Wrap title in {text: ...}
        layout.yaxis = { title: { text: chartSettings.yAxisLabel }, showgrid: chartSettings.showGrid, zeroline: false }; // Wrap title in {text: ...}
      }

      setPlotlyConfig({ data: plotData, layout });

    } catch (err) {
      setError(`Error generating chart data: ${err instanceof Error ? err.message : String(err)}`);
      setPlotlyConfig(null);
    } finally {
      setLoading(false);
    }
  };
  
  // Reset settings
  const resetChartSettings = () => {
    setChartSettings(defaultChartSettings);
  };
  
  const downloadChart = () => {
    if (plotlyConfig && plotlyDivRef.current) {
      const downloadOpts = {
        format: 'svg',
        filename: chartSettings.title.replace(/\s+/g, '_') || 'scatterplot',
        width: plotlyDivRef.current.offsetWidth || 800,
        height: plotlyDivRef.current.offsetHeight || 600,
      };
      Plotly.downloadImage(PLOTLY_SCATTER_DIV_ID, downloadOpts);
    } else {
      setError('Chart data not available for download.');
    }
  };
  
  // Generate scatter plot with current settings
  const generateScatterPlot = () => {
    generatePlotlyData();
  };
  
  // Get colors
  const getChartColors = () => {
    return colorSchemes[chartSettings.colorScheme] || colorSchemes.default;
  };

  // Button disabled states
  const isGenerateDisabled = !xVariable || !yVariable || (chartSettings.use3D && !zVariable) || loading;
  const isDownloadDisabled = !plotlyConfig || loading;
  
  // --- Render ---
  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Scatter Plot Generator
      </Typography>

      {/* Information Section */}
      <Box mb={2} p={2} sx={{ backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          Generate scatter plots to explore relationships between variables. Supports 2D/3D plots with optional size, color, and label mappings. Use keyboard shortcuts: <strong>Alt+1</strong> for Variables panel, <strong>Alt+2</strong> for Settings panel.
        </Typography>
      </Box>

      {/* Three-Panel Layout */}
      <Grid container spacing={2}>
        {/* Left Panel Container - Variables or Settings */}
        <Grid item xs={12} sm={12} md={3} lg={3}>
          {/* Panel Toggle Tabs */}
          <Paper
            elevation={1}
            sx={{
              mb: 1,
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)'
            }}
          >
            <Tabs
              value={activePanel}
              onChange={(_, newValue) => setActivePanel(newValue)}
              variant="fullWidth"
              sx={{
                minHeight: 48,
                '& .MuiTab-root': {
                  minHeight: 48,
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  color: theme.palette.text.secondary,
                  '&.Mui-selected': {
                    color: theme.palette.primary.main,
                  },
                  '&:hover': {
                    color: theme.palette.primary.main,
                    backgroundColor: theme.palette.action.hover,
                  }
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: theme.palette.primary.main,
                }
              }}
            >
              <Tooltip title="Variable Selection Panel" placement="top">
                <Tab
                  value="variables"
                  label="Variables"
                  icon={<DataObjectIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
              <Tooltip title="Chart Settings Panel" placement="top">
                <Tab
                  value="settings"
                  label="Settings"
                  icon={<SettingsIcon fontSize="small" />}
                  iconPosition="start"
                />
              </Tooltip>
            </Tabs>
          </Paper>

          {/* Variable Selection Panel */}
          <Fade in={activePanel === 'variables'} timeout={300}>
            <Box sx={{ display: activePanel === 'variables' ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 1
                }}>
                  Data Selection
                </Typography>

                {/* Dataset Selector */}
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="dataset-select-label">Dataset</InputLabel>
                  <Select
                    labelId="dataset-select-label"
                    value={selectedDatasetId}
                    label="Dataset"
                    onChange={handleDatasetChange}
                    disabled={datasets.length === 0}
                  >
                    {datasets.length === 0 ? (
                      <MenuItem value="" disabled>No datasets available</MenuItem>
                    ) : (
                      datasets.map(ds => (
                        <MenuItem key={ds.id} value={ds.id}>
                          {ds.name} ({ds.data.length} rows)
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>

                {/* Required Variables */}
                <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                  Required Variables
                </Typography>
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="x-variable-label">X-Axis Variable</InputLabel>
                  <Select
                    labelId="x-variable-label"
                    value={xVariable}
                    label="X-Axis Variable"
                    onChange={handleXVariableChange}
                    disabled={numericColumns.length === 0}
                  >
                    {numericColumns.length === 0 ? (
                      <MenuItem value="" disabled>No numeric variables</MenuItem>
                    ) : (
                      numericColumns.map(col => (
                        <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="y-variable-label">Y-Axis Variable</InputLabel>
                  <Select
                    labelId="y-variable-label"
                    value={yVariable}
                    label="Y-Axis Variable"
                    onChange={handleYVariableChange}
                    disabled={numericColumns.length === 0}
                  >
                    {numericColumns.length === 0 ? (
                      <MenuItem value="" disabled>No numeric variables</MenuItem>
                    ) : (
                      numericColumns.map(col => (
                        <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>

                {/* Optional Variables */}
                <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                  Additional Dimensions (Optional)
                </Typography>
                <FormControl fullWidth margin="normal" size="small" disabled={!chartSettings.use3D}>
                  <InputLabel id="z-variable-label">Z-Axis Variable (3D)</InputLabel>
                  <Select
                    labelId="z-variable-label"
                    value={zVariable}
                    label="Z-Axis Variable (3D)"
                    onChange={handleZVariableChange}
                    disabled={!chartSettings.use3D || numericColumns.length === 0}
                  >
                    <MenuItem value="">None</MenuItem>
                    {numericColumns.map(col => (
                      <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="size-variable-label">Point Size Variable</InputLabel>
                  <Select
                    labelId="size-variable-label"
                    value={sizeVariable}
                    label="Point Size Variable"
                    onChange={handleSizeVariableChange}
                  >
                    <MenuItem value="">Default Size</MenuItem>
                    {numericColumns.map(col => (
                      <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="color-variable-label">Color Category</InputLabel>
                  <Select
                    labelId="color-variable-label"
                    value={colorVariable}
                    label="Color Category"
                    onChange={handleColorVariableChange}
                  >
                    <MenuItem value="">Single Color</MenuItem>
                    {categoricalColumns.map(col => (
                      <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel id="label-variable-label">Point Labels</InputLabel>
                  <Select
                    labelId="label-variable-label"
                    value={labelVariable}
                    label="Point Labels"
                    onChange={handleLabelVariableChange}
                  >
                    <MenuItem value="">Index</MenuItem>
                    {allColumns.map(col => (
                      <MenuItem key={col.id} value={col.id}>{col.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Action Buttons */}
                <Box mt={2} display="flex" gap={1} flexDirection="column">
                  <Button
                    variant="contained"
                    onClick={generatePlotlyData}
                    disabled={isGenerateDisabled}
                    startIcon={loading ? <CircularProgress size={20} /> : <ScatterPlotIconMui />}
                    fullWidth
                  >
                    {loading ? 'Generating...' : 'Generate Chart'}
                  </Button>
                  <Box display="flex" gap={1} justifyContent="center">
                    <Tooltip title="Download Chart">
                      <IconButton onClick={downloadChart} disabled={isDownloadDisabled}>
                        <SaveIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reset Settings">
                      <IconButton onClick={resetChartSettings}>
                        <RefreshIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Fade>
          {/* Chart Settings Panel */}
          <Fade in={activePanel === 'settings'} timeout={300}>
            <Box sx={{ display: activePanel === 'settings' ? 'block' : 'none' }}>
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  height: 'fit-content',
                  maxHeight: '600px',
                  overflowY: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{
                  position: 'sticky',
                  top: 0,
                  backgroundColor: 'inherit',
                  zIndex: 1,
                  pb: 1
                }}>
                  Chart Settings
                </Typography>

                {/* Labels & Title Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Labels & Title
                  </Typography>
                  <TextField
                    fullWidth
                    label="Chart Title"
                    value={chartSettings.title}
                    onChange={(e) => handleSettingsChange('title', e.target.value)}
                    margin="normal"
                    variant="outlined"
                    size="small"
                  />
                  <TextField
                    fullWidth
                    label="X-Axis Label"
                    value={chartSettings.xAxisLabel}
                    onChange={(e) => handleSettingsChange('xAxisLabel', e.target.value)}
                    margin="normal"
                    variant="outlined"
                    size="small"
                  />
                  <TextField
                    fullWidth
                    label="Y-Axis Label"
                    value={chartSettings.yAxisLabel}
                    onChange={(e) => handleSettingsChange('yAxisLabel', e.target.value)}
                    margin="normal"
                    variant="outlined"
                    size="small"
                  />
                  {chartSettings.use3D && (
                    <TextField
                      fullWidth
                      label="Z-Axis Label"
                      value={chartSettings.zAxisLabel}
                      onChange={(e) => handleSettingsChange('zAxisLabel', e.target.value)}
                      margin="normal"
                      variant="outlined"
                      size="small"
                    />
                  )}
                </Box>

                {/* Appearance Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Appearance
                  </Typography>
                  <FormControl fullWidth margin="normal" size="small">
                    <InputLabel id="color-scheme-label">Color Scheme</InputLabel>
                    <Select
                      labelId="color-scheme-label"
                      value={chartSettings.colorScheme}
                      label="Color Scheme"
                      onChange={(e) => handleSettingsChange('colorScheme', e.target.value as ColorSchemeName)}
                    >
                      {Object.keys(colorSchemes).map(scheme => (
                        <MenuItem key={scheme} value={scheme}>
                          {scheme.charAt(0).toUpperCase() + scheme.slice(1)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                {/* Size & Styling Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Size & Styling
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Typography gutterBottom>Default Point Size</Typography>
                    <Slider
                      value={chartSettings.pointSize}
                      min={2}
                      max={20}
                      step={1}
                      onChange={(_e, value) => handleSettingsChange('pointSize', value)}
                      valueLabelDisplay="auto"
                      disabled={!!sizeVariable}
                      size="small"
                    />
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <Typography gutterBottom>Jitter Amount (%)</Typography>
                    <Slider
                      value={chartSettings.jitter}
                      min={0}
                      max={20}
                      step={1}
                      onChange={(_e, value) => handleSettingsChange('jitter', value)}
                      valueLabelDisplay="auto"
                      size="small"
                    />
                  </Box>
                </Box>

                {/* Display Options Section */}
                <Box mb={3}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Display Options
                  </Typography>
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.showGrid}
                          onChange={(e) => handleSettingsChange('showGrid', e.target.checked)}
                        />
                      }
                      label="Show Grid Lines"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.labelPoints}
                          onChange={(e) => handleSettingsChange('labelPoints', e.target.checked)}
                        />
                      }
                      label="Label Points"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          size="small"
                          checked={chartSettings.use3D}
                          onChange={(e) => handleSettingsChange('use3D', e.target.checked)}
                        />
                      }
                      label="Enable 3D Plot"
                    />
                  </FormGroup>
                </Box>

                {/* Apply Button */}
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={generateScatterPlot}
                  disabled={!xVariable || !yVariable || loading || !selectedDatasetId}
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  Apply Customizations & Regenerate
                </Button>
              </Paper>
            </Box>
          </Fade>
        </Grid>

        {/* Chart Display Panel */}
        <Grid item xs={12} sm={12} md={9} lg={9}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Chart Preview
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  Active: {activePanel === 'variables' ? 'Variables' : 'Settings'}
                </Typography>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: activePanel === 'variables' ? theme.palette.primary.main : theme.palette.warning.main,
                    boxShadow: `0 0 0 2px ${activePanel === 'variables' ? theme.palette.primary.main + '20' : theme.palette.warning.main + '20'}`
                  }}
                />
              </Box>
            </Box>
            {error && (
              <Box sx={{ mb: 2, p: 2, backgroundColor: theme.palette.error.light + '20', borderRadius: 1, border: `1px solid ${theme.palette.error.light}` }}>
                <Typography color="error">{error}</Typography>
              </Box>
            )}

            {/* Plotly Chart Container */}
            <Box
              sx={{
                minHeight: 500,
                height: 500,
                width: '100%',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.01)' : 'rgba(0, 0, 0, 0.01)',
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              {loading && (
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  gap={2}
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    zIndex: 10
                  }}
                >
                  <CircularProgress />
                  <Typography color="text.secondary">Generating scatter plot...</Typography>
                </Box>
              )}

              {!loading && !plotlyConfig && (
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  gap={2}
                  p={4}
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    textAlign: 'center',
                    zIndex: 9
                  }}
                >
                  <ScatterPlotIconMui sx={{ fontSize: 48, color: 'text.disabled' }} />
                  <Typography color="text.secondary" textAlign="center">
                    {!activeDataset
                      ? 'Select a dataset to begin'
                      : !xVariable || !yVariable
                      ? 'Select X and Y variables to generate the scatter plot'
                      : chartSettings.use3D && !zVariable
                      ? 'Select a Z variable for 3D plot'
                      : 'Chart will appear here once generated'
                    }
                  </Typography>
                  {activeDataset && (!xVariable || !yVariable) && (
                    <Typography variant="body2" color="text.disabled" textAlign="center">
                      Switch to the Variables panel to select your data
                    </Typography>
                  )}
                </Box>
              )}

              {/* Plotly Chart Div */}
              <div
                ref={plotlyDivRef}
                id={PLOTLY_SCATTER_DIV_ID}
                style={{
                  width: '100%',
                  height: '100%',
                  visibility: plotlyConfig && !loading ? 'visible' : 'hidden'
                }}
              />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ScatterPlot;
