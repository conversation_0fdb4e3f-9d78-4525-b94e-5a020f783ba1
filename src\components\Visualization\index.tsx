import React, { useState, useEffect, Suspense, lazy } from 'react';
import {
  Box,
  Tabs,
  Tab,
  Paper,
  useTheme,
  CircularProgress // Import CircularProgress for fallback
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON> as BarChartIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  Timeline as TimelineIcon,
  StackedBarChart as StackedBarChartIcon,
  CandlestickChart as CandlestickChartIcon,
  Cloud as CloudIcon,
  AccountTree as SankeyIcon,
  ShowChart as ShowChartIcon
} from '@mui/icons-material';

// Lazy load chart components
const BarChart = lazy(() => import('./BarChart'));
const PieChart = lazy(() => import('./PieChart'));
const Histogram = lazy(() => import('./Histogram'));
const BoxPlot = lazy(() => import('./BoxPlot'));
const ScatterPlot = lazy(() => import('./ScatterPlot'));
const RainCloudPlot = lazy(() => import('./RainCloudPlot'));
const SankeyDiagram = lazy(() => import('./SankeyDiagram'));
const ErrorBarChart = lazy(() => import('./ErrorBarChart'));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface VisualizationProps {
  initialTab?: string;
}

// Tab panel component
const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`visualization-tabpanel-${index}`}
      aria-labelledby={`visualization-tab-${index}`}
      {...other}
      style={{ width: '100%' }}
    >
      {value === index && (
        <Box sx={{ pt: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Helper function for a11y props
const a11yProps = (index: number) => {
  return {
    id: `visualization-tab-${index}`,
    'aria-controls': `visualization-tabpanel-${index}`,
  };
};

// Map tab names to indices
const tabNameToIndex: Record<string, number> = {
  'bar': 0,
  'pie': 1,
  'histogram': 2,
  'boxplot': 3,
  'scatter': 4,
  'raincloud': 5,
  'sankey': 6,
  'errorbar': 7
};

// Main component for the Visualization section
const Visualization: React.FC<VisualizationProps> = ({ initialTab = '' }) => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);

  // Set initial tab based on URL or prop
  useEffect(() => {
    if (initialTab && tabNameToIndex[initialTab] !== undefined) {
      setTabValue(tabNameToIndex[initialTab]);
    }
  }, [initialTab]);

  // Handle tab change
  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper elevation={1} sx={{ width: '100%' }}>
        <Tabs
          value={tabValue}
          onChange={handleChange}
          aria-label="Visualization Tabs"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <Tab 
            icon={<BarChartIcon />} 
            label="Bar Chart" 
            {...a11yProps(0)} 
            sx={{ py: 2 }}
          />
          <Tab 
            icon={<PieChartIcon />} 
            label="Pie Chart" 
            {...a11yProps(1)} 
            sx={{ py: 2 }}
          />
          <Tab 
            icon={<StackedBarChartIcon />} 
            label="Histogram" 
            {...a11yProps(2)}
            sx={{ py: 2 }}
          />
          <Tab 
            icon={<CandlestickChartIcon />} 
            label="Box Plot" 
            {...a11yProps(3)}
            sx={{ py: 2 }}
          />
          <Tab 
            icon={<TimelineIcon />} 
            label="Scatter Plot" 
            {...a11yProps(4)}
            sx={{ py: 2 }}
          />
          <Tab
            icon={<CloudIcon />}
            label="Rain Cloud Plot"
            {...a11yProps(5)}
            sx={{ py: 2 }}
          />
          <Tab
            icon={<SankeyIcon />}
            label="Sankey Diagram"
            {...a11yProps(6)}
            sx={{ py: 2 }}
          />
          <Tab
            icon={<ShowChartIcon />}
            label="Error Bar Chart"
            {...a11yProps(7)}
            sx={{ py: 2 }}
          />
        </Tabs>

        <Box sx={{ p: 0 }}>
          <TabPanel value={tabValue} index={0}>
            <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}><CircularProgress /></Box>}>
              <BarChart />
            </Suspense>
          </TabPanel>
          <TabPanel value={tabValue} index={1}>
            <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}><CircularProgress /></Box>}>
              <PieChart />
            </Suspense>
          </TabPanel>
          <TabPanel value={tabValue} index={2}>
            <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}><CircularProgress /></Box>}>
              <Histogram />
            </Suspense>
          </TabPanel>
          <TabPanel value={tabValue} index={3}>
            <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}><CircularProgress /></Box>}>
              <BoxPlot />
            </Suspense>
          </TabPanel>
          <TabPanel value={tabValue} index={4}>
            <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}><CircularProgress /></Box>}>
              <ScatterPlot />
            </Suspense>
          </TabPanel>
          <TabPanel value={tabValue} index={5}>
            <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}><CircularProgress /></Box>}>
              <RainCloudPlot />
            </Suspense>
          </TabPanel>
          <TabPanel value={tabValue} index={6}>
            <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}><CircularProgress /></Box>}>
              <SankeyDiagram />
            </Suspense>
          </TabPanel>
          <TabPanel value={tabValue} index={7}>
            <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}><CircularProgress /></Box>}>
              <ErrorBarChart />
            </Suspense>
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};

export default Visualization;
