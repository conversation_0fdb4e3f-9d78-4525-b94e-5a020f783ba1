// Environment configuration for feature flags and Stripe integration
export const config = {
  // Feature flags
  enableStripeDev: import.meta.env.VITE_ENABLE_STRIPE_DEV === 'true',
  stripeDevMode: import.meta.env.VITE_STRIPE_DEV_MODE === 'true',
  
  // Environment detection
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
  
  // Stripe configuration
  stripe: {
    publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',
    priceIds: {
      proMonthly: import.meta.env.VITE_STRIPE_PRO_MONTHLY_PRICE_ID || '',
      proAnnual: import.meta.env.VITE_STRIPE_PRO_ANNUAL_PRICE_ID || '',
      eduMonthly: import.meta.env.VITE_STRIPE_EDU_MONTHLY_PRICE_ID || '',
      eduAnnual: import.meta.env.VITE_STRIPE_EDU_ANNUAL_PRICE_ID || '',
    }
  }
};

// Helper functions
export const isStripeEnabled = (): boolean => {
  return config.enableStripeDev && (config.isDevelopment || config.stripeDevMode);
};

export const shouldShowDevPricing = (): boolean => {
  return config.isDevelopment && config.enableStripeDev;
};

// Stripe price ID helper
export const getStripePriceId = (accountType: 'pro' | 'edu', billingCycle: 'monthly' | 'annual'): string => {
  const key = `${accountType}${billingCycle.charAt(0).toUpperCase() + billingCycle.slice(1)}` as keyof typeof config.stripe.priceIds;
  return config.stripe.priceIds[key];
};

// Development-only route guard
export const isDevelopmentRoute = (path: string): boolean => {
  const devRoutes = ['/pricing-dev', '/stripe-test', '/payment-dev'];
  return config.isDevelopment && devRoutes.some(route => path.includes(route));
};
