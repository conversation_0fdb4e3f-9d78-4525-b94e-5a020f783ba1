import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '../utils/supabaseClient';
import { useLocation, useNavigate } from 'react-router-dom';

interface SubscriptionData {
  id: string;
  stripe_subscription_id: string | null;
  stripe_price_id: string;
  status: 'active' | 'canceled' | 'past_due' | 'incomplete' | 'unpaid';
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  billing_cycle: 'monthly' | 'yearly';
  created_at: string;
  updated_at: string;
}

interface UserProfile {
  username?: string;
  avatar_url?: string;
  full_name?: string;
  institution?: string;
  country?: string;
  accounttype?: 'standard' | 'pro' | 'edu' | 'edu_pro';
  edu_subscription_type?: 'free' | 'pro' | null;
  is_admin?: boolean;
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  userProfile: UserProfile | null;
  isGuest: boolean;
  isAuthenticated: boolean;
  loading: boolean;
  canAccessSampleData: boolean;
  canImportData: boolean;
  canAccessProFeatures: boolean; // New: Access to Advanced Analysis and Publication Ready
  showSignupSuccessMessage: boolean; // New: Flag to show signup success message
  clearSignupSuccessMessage: () => void; // New: Function to clear the message flag
  signIn: (email: string, password: string) => Promise<{ error: any | null }>;
  signUp: (email: string, password: string, options?: { data?: Record<string, any> }) => Promise<{ error: any | null, user: User | null }>;
  signInWithGoogle: () => Promise<{ error: any | null }>; // Add Google sign-in function
  loginAsGuest: () => void;
  logoutGuest: () => void;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: any | null }>;
  updateProfile: (data: { username?: string, avatar_url?: string, full_name?: string, institution?: string, country?: string }) => Promise<{ error: any | null }>;
  uploadAvatar: (file: File) => Promise<{ error: any | null, publicUrl?: string | null }>;
  logLoginEvent: (event_type: 'app_open' | 'login' | 'signin' | 'sign_in' | 'signed_in', details?: Record<string, any>) => Promise<void>; // Login event logging only
  accountType: 'standard' | 'pro' | 'edu' | 'edu_pro' | null;
  refreshProfile: () => Promise<void>; // New: Function to refresh profile data
  // New subscription-related properties
  subscriptionData: SubscriptionData | null;
  subscriptionStatus: 'active' | 'canceled' | 'past_due' | 'incomplete' | 'unpaid' | null;
  hasActiveSubscription: boolean;
  nextPaymentDate: string | null;
  billingCycle: 'monthly' | 'yearly' | null;
  refreshSubscription: () => Promise<void>;
  canUpgradeAccount: boolean;
  // New educational tier properties
  canAccessAdvancedAnalysis: boolean;
  canAccessPublicationReady: boolean;
  canAccessCloudStorage: boolean;
  isEducationalUser: boolean;
  educationalTier: 'free' | 'pro' | null;
  // Admin-related properties
  isAdmin: boolean;
  canAccessAdminDashboard: boolean;
  refreshAdminStatus: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isGuest, setIsGuest] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [showSignupSuccessMessage, setShowSignupSuccessMessage] = useState(false);
  const [accountType, setAccountType] = useState<'standard' | 'pro' | 'edu' | 'edu_pro' | null>(null);

  // Admin-related state
  const [isAdmin, setIsAdmin] = useState<boolean>(false);

  // Logout state management to prevent race conditions
  const [isLoggingOut, setIsLoggingOut] = useState<boolean>(false);
  const [logoutRetryCount, setLogoutRetryCount] = useState<number>(0);

  // New subscription-related state
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<'active' | 'canceled' | 'past_due' | 'incomplete' | 'unpaid' | null>(null);

  // New educational tier state
  const [educationalTier, setEducationalTier] = useState<'free' | 'pro' | null>(null);

  const location = useLocation();
  const navigate = useNavigate();

  const canAccessSampleData = !!user || isGuest;
  const canImportData = !!user;

  // New educational tier computed properties
  const isEducationalUser = useMemo(() => {
    return accountType === 'edu' || accountType === 'edu_pro';
  }, [accountType]);

  const canAccessAdvancedAnalysis = useMemo(() => {
    // Guest users can access with sample data
    if (isGuest) {
      console.log('🔍 Advanced Analysis: Guest access granted');
      return true;
    }

    // Authenticated users need Pro OR Educational (free or paid)
    if (user && accountType) {
      const hasAccess = accountType === 'pro' ||
                       accountType === 'edu' ||
                       accountType === 'edu_pro';
      console.log('🔍 Advanced Analysis Permission:', {
        userEmail: user.email,
        accountType,
        hasAccess,
        reason: hasAccess ? 'Account type allows access' : 'Account type does not allow access'
      });
      return hasAccess;
    }

    console.log('🔍 Advanced Analysis: No access - no user or account type');
    return false;
  }, [isGuest, user, accountType]);

  const canAccessPublicationReady = useMemo(() => {
    // Guest users can access with sample data
    if (isGuest) return true;

    // Authenticated users need Pro OR Educational Pro (paid only)
    if (user && accountType) {
      const hasRegularPro = accountType === 'pro';
      const hasEducationalPro = accountType === 'edu_pro' || (accountType === 'edu' && educationalTier === 'pro');

      console.log('🔍 Publication Ready Permission:', {
        userEmail: user.email,
        accountType,
        educationalTier,
        hasRegularPro,
        hasEducationalPro,
        hasAccess: hasRegularPro || hasEducationalPro
      });

      return hasRegularPro || hasEducationalPro;
    }

    return false;
  }, [isGuest, user, accountType, educationalTier]);

  const canAccessCloudStorage = useMemo(() => {
    // Only Pro users (regular or educational) get cloud storage
    if (user && accountType) {
      const hasRegularPro = accountType === 'pro';
      const hasEducationalPro = accountType === 'edu_pro' || (accountType === 'edu' && educationalTier === 'pro');

      console.log('🔍 Cloud Storage Permission:', {
        userEmail: user.email,
        accountType,
        educationalTier,
        hasRegularPro,
        hasEducationalPro,
        hasAccess: hasRegularPro || hasEducationalPro
      });

      return hasRegularPro || hasEducationalPro;
    }

    return false;
  }, [user, accountType, educationalTier]);

  // Legacy property for backward compatibility
  // NOTE: This should be gradually replaced with granular permissions
  // For now, maintaining backward compatibility while allowing educational users access
  const canAccessProFeatures = canAccessAdvancedAnalysis;

  // Admin-related computed properties
  const canAccessAdminDashboard = useMemo(() => {
    // Only authenticated admin users can access admin dashboard
    return !!user && !isGuest && isAdmin;
  }, [user, isGuest, isAdmin]);

  // New computed properties for subscription management
  const hasActiveSubscription = subscriptionStatus === 'active';
  const nextPaymentDate = subscriptionData?.current_period_end || null;
  const billingCycle = subscriptionData?.billing_cycle || null;
  const canUpgradeAccount = !!user && (accountType === 'standard' || accountType === 'edu');

  // Function to clear the signup success message flag and session storage item
  const clearSignupSuccessMessage = useCallback(() => {
    setShowSignupSuccessMessage(false);
    sessionStorage.removeItem('showSignupSuccess'); // Clear from session storage
  }, []);

  // Function to fetch complete user profile data
  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      console.log('🔍 Fetching profile for user:', userId);
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('username, avatar_url, full_name, institution, country, accounttype, edu_subscription_type')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error("❌ Error fetching profile:", profileError);
        return null;
      }

      console.log('✅ Profile fetched successfully:', profile);
      return profile as UserProfile;
    } catch (error) {
      console.error("❌ Failed to fetch user profile:", error);
      return null;
    }
  };

  // Function to refresh profile data
  const refreshProfile = async () => {
    if (!user) {
      console.log('⚠️ No user available for profile refresh');
      return;
    }

    console.log('🔄 Refreshing profile for user:', user.id);
    const profile = await fetchUserProfile(user.id);
    setUserProfile(profile);

    // Update accountType from profile
    if (profile?.accounttype) {
      console.log('✅ Account type updated:', profile.accounttype);
      setAccountType(profile.accounttype);
    } else {
      console.log('⚠️ No account type found in profile, defaulting to standard');
      setAccountType('standard');
    }

    // Update educational tier from profile
    if (profile?.edu_subscription_type) {
      console.log('✅ Educational tier updated:', profile.edu_subscription_type);
      setEducationalTier(profile.edu_subscription_type);
    } else {
      console.log('⚠️ No educational tier found, setting to null');
      setEducationalTier(null);
    }

    // Update admin status from profile
    if (profile?.is_admin !== undefined) {
      console.log('✅ Admin status updated:', profile.is_admin);
      setIsAdmin(profile.is_admin);
    } else {
      console.log('⚠️ No admin status found in profile, defaulting to false');
      setIsAdmin(false);
    }

    // Debug: Log final permission calculations
    console.log('🔍 Permission Debug After Profile Refresh:', {
      accountType: profile?.accounttype,
      educationalTier: profile?.edu_subscription_type,
      isAdmin: profile?.is_admin,
      userEmail: user?.email
    });
  };

  // Function to refresh subscription data
  const refreshSubscription = useCallback(async () => {
    if (!user || !accountType || (accountType !== 'pro' && accountType !== 'edu')) {
      setSubscriptionData(null);
      setSubscriptionStatus(null);
      return;
    }

    // Skip subscription calls when on admin dashboard to prevent conflicts
    const isOnAdminDashboard = window.location.hash.includes('admin-dashboard');
    if (isOnAdminDashboard) {
      console.log('⚠️ Skipping subscription refresh on admin dashboard');
      return;
    }

    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        // Don't log 406 errors as they're expected when subscriptions table doesn't exist
        if (error.code !== '406' && !error.message?.includes('406')) {
          console.error('Error fetching subscription:', error);
        }
        return;
      }

      if (data) {
        setSubscriptionData(data);
        setSubscriptionStatus(data.status);
      } else {
        setSubscriptionData(null);
        setSubscriptionStatus(null);
      }
    } catch (error: any) {
      // Silently handle 406 errors and subscription-related errors
      if (!error.message?.includes('406') && !error.message?.includes('subscription')) {
        console.error('Error refreshing subscription:', error);
      }
    }
  }, [user, accountType]);

  // Function to refresh admin status
  const refreshAdminStatus = useCallback(async () => {
    if (!user) {
      setIsAdmin(false);
      return;
    }

    try {
      console.log('🔄 Refreshing admin status for user:', user.id);
      const { data, error } = await supabase.rpc('is_user_admin', { user_id: user.id });

      if (error) {
        console.error('❌ Error checking admin status:', error);
        setIsAdmin(false);
        return;
      }

      console.log('✅ Admin status refreshed:', data);
      setIsAdmin(data || false);
    } catch (error) {
      console.error('❌ Error refreshing admin status:', error);
      setIsAdmin(false);
    }
  }, [user]);

  // Fetch subscription data when user or account type changes
  // Skip subscription calls when on admin dashboard to prevent conflicts
  useEffect(() => {
    const isOnAdminDashboard = window.location.hash.includes('admin-dashboard');

    if (user && accountType && (accountType === 'pro' || accountType === 'edu') && !isOnAdminDashboard) {
      refreshSubscription();
    } else {
      setSubscriptionData(null);
      setSubscriptionStatus(null);
    }
  }, [user, accountType, refreshSubscription]);

  // Refresh admin status when user changes
  useEffect(() => {
    if (user && !isGuest) {
      refreshAdminStatus();
    } else {
      setIsAdmin(false);
    }
  }, [user, isGuest, refreshAdminStatus]);

  // Comprehensive state cleanup function
  const clearAuthenticationState = useCallback((broadcastToOtherTabs: boolean = false) => {
    console.log('🧹 Clearing all authentication state...');

    try {
      // Broadcast logout signal to other tabs before clearing state
      if (broadcastToOtherTabs) {
        const logoutSignal = {
          timestamp: Date.now(),
          reason: 'logout'
        };
        localStorage.setItem('datastatpro-logout-signal', JSON.stringify(logoutSignal));

        // Remove the signal after a brief delay to prevent it from persisting
        setTimeout(() => {
          localStorage.removeItem('datastatpro-logout-signal');
        }, 1000);
      }

      // Clear React state
      setSession(null);
      setUser(null);
      setIsGuest(false);
      setUserProfile(null);
      setAccountType(null);
      setIsAdmin(false);
      setSubscriptionData(null);
      setSubscriptionStatus(null);
      setEducationalTier(null);

      // Clear session storage
      sessionStorage.removeItem('isGuest');
      sessionStorage.removeItem('showSignupSuccess');

      // Clear any auth-related localStorage flags
      localStorage.removeItem('datastatpro-auth-loading-stuck');

      console.log('✅ Authentication state cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing authentication state:', error);
    }
  }, []);

  // Cross-tab authentication synchronization
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      // Handle cross-tab logout synchronization
      if (event.key === 'datastatpro-logout-signal' && event.newValue) {
        console.log('🔄 Cross-tab logout signal received');
        const logoutData = JSON.parse(event.newValue);

        // Only process if this is a different tab (different timestamp)
        if (logoutData.timestamp && Date.now() - logoutData.timestamp < 5000) {
          console.log('🔄 Synchronizing logout across tabs');
          clearAuthenticationState();

          // Navigate to auth page if not already there
          if (!window.location.hash.includes('auth')) {
            navigate('/app#auth');
          }
        }
      }

      // Handle cross-tab login synchronization
      if (event.key === 'datastatpro-login-signal' && event.newValue) {
        console.log('🔄 Cross-tab login signal received');
        // Refresh the current session to sync with other tabs
        supabase.auth.getSession().then(({ data: { session } }) => {
          if (session && !user) {
            console.log('🔄 Syncing login state from other tab');
            // The auth state change handler will handle the rest
          }
        });
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [user, clearAuthenticationState, navigate]);

  useEffect(() => {
    // Set up authentication loading timeout to detect stuck states
    const authLoadingTimeout = setTimeout(() => {
      if (loading) {
        console.warn('Authentication loading timeout - marking for cache recovery');
        localStorage.setItem('datastatpro-auth-loading-stuck', 'true');
        setLoading(false); // Force loading to false to prevent infinite loading
      }
    }, 10000); // 10 second timeout

    // Check for guest status in session storage on initial load
    const guestStatus = sessionStorage.getItem('isGuest');
    if (guestStatus === 'true') {
      clearTimeout(authLoadingTimeout);
      setIsGuest(true);
      setUser(null);
      setSession(null);
      setLoading(false);
      return;
    }

    // Check for signup success message in session storage on initial load
    const signupSuccess = sessionStorage.getItem('showSignupSuccess');
    if (signupSuccess === 'true') {
      setShowSignupSuccessMessage(true);
      // Do NOT clear it here, let App.tsx clear it after displaying
    }

    supabase.auth.getSession().then(async ({ data: { session: currentSession } }) => {
      clearTimeout(authLoadingTimeout); // Clear timeout on successful session retrieval
      localStorage.removeItem('datastatpro-auth-loading-stuck'); // Clear stuck flag

      setSession(currentSession);
      const currentUser = currentSession?.user ?? null;
      setUser(currentUser);
      setIsGuest(false);

      // Fetch complete profile data if user exists
      if (currentUser) {
        console.log('🔍 Initial session: Fetching profile for user:', currentUser.id);
        const profile = await fetchUserProfile(currentUser.id);
        setUserProfile(profile);

        if (profile?.accounttype) {
          setAccountType(profile.accounttype);
        } else {
          setAccountType('standard'); // Default to standard if no account type
        }

        // Set educational tier
        if (profile?.edu_subscription_type) {
          setEducationalTier(profile.edu_subscription_type);
        } else {
          setEducationalTier(null);
        }
      } else {
        setUserProfile(null);
        setAccountType(null);
        setEducationalTier(null);
      }
      setLoading(false);

      // Log 'app_open' if a user is already authenticated on initial load
      if (currentUser) {
        logLoginEvent('app_open');
      }
    }).catch((error) => {
      clearTimeout(authLoadingTimeout); // Clear timeout on error
      console.error('Failed to get session:', error);
      localStorage.setItem('datastatpro-auth-loading-stuck', 'true');
      setLoading(false); // Set loading to false even on error
    });

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, newSession) => { // Made async
        if (_event === 'SIGNED_OUT') {
          console.log('🔄 Auth state change: SIGNED_OUT event received');

          // Use centralized state clearing function and broadcast to other tabs
          clearAuthenticationState(true);

          // Only navigate if not already in a logout process to prevent race conditions
          if (!isLoggingOut) {
            console.log('🔄 Navigating to auth page after signout');
            navigate('/app#auth');
          } else {
            console.log('⚠️ Skipping navigation - logout already in progress');
          }

          // Reset logout state after a brief delay to allow navigation to complete
          setTimeout(() => {
            setIsLoggingOut(false);
            setLogoutRetryCount(0);
          }, 100);
        } else if (newSession) {
          setIsGuest(false);
          sessionStorage.removeItem('isGuest');

          // Check for signup confirmation in URL hash
          const hashParams = new URLSearchParams(location.hash.substring(1)); // Parse hash part
          const type = hashParams.get('type');

          if (type === 'signup' && newSession.user) {
            setShowSignupSuccessMessage(true);
            sessionStorage.setItem('showSignupSuccess', 'true'); // Persist across refreshes

            // Clean up URL hash parameters to prevent message from reappearing on refresh
            // Use window.history.replaceState to remove hash parameters without full navigation
            const currentUrl = window.location.href;
            const urlWithoutHash = currentUrl.split('#')[0];
            window.history.replaceState({}, document.title, urlWithoutHash);
          }
        }
        setSession(newSession);
        const currentUser = newSession?.user ?? null;
        setUser(currentUser);

        // Fetch complete profile data if user exists
        if (currentUser) {
          console.log('🔍 Auth state change: Fetching profile for user:', currentUser.id);
          const profile = await fetchUserProfile(currentUser.id);
          setUserProfile(profile);

          if (profile?.accounttype) {
            setAccountType(profile.accounttype);
          } else {
            setAccountType('standard'); // Default to standard if no account type
          }

          // Set educational tier
          if (profile?.edu_subscription_type) {
            setEducationalTier(profile.edu_subscription_type);
          } else {
            setEducationalTier(null);
          }
        } else {
          setUserProfile(null);
          setAccountType(null);
          setEducationalTier(null);
        }
        setLoading(false);

        // Log 'app_open' when a user signs in
        if (_event === 'SIGNED_IN' && currentUser) {
          logLoginEvent('app_open');

          // Broadcast login signal to other tabs
          const loginSignal = {
            timestamp: Date.now(),
            userId: currentUser.id
          };
          localStorage.setItem('datastatpro-login-signal', JSON.stringify(loginSignal));

          // Remove the signal after a brief delay
          setTimeout(() => {
            localStorage.removeItem('datastatpro-login-signal');
          }, 1000);
        }
      }
    );

    return () => {
      clearTimeout(authLoadingTimeout); // Clean up timeout
      subscription.unsubscribe();
    };
  }, [location.hash, location.pathname, location.search, navigate, isLoggingOut, clearAuthenticationState]); // Added dependencies

  // Effect to handle navigation after successful sign-in
  useEffect(() => {
    // Check if the user is authenticated and not a guest
    // And if the current hash indicates they are on an auth-related page within the app
    const currentHash = location.hash;
    const isAuthPageInApp = currentHash.startsWith('#/app/auth/login') || currentHash.startsWith('#/app/auth/register') || currentHash.startsWith('#/app/auth/reset-password');

    if (user && !isGuest && isAuthPageInApp) {
      navigate('/dashboard');
    }
  }, [user, isGuest, location.hash, navigate]);

  const signIn = async (email: string, password: string) => {
    const { error, data } = await supabase.auth.signInWithPassword({ email, password });
    
    if (!error) {
      // Check if user email is confirmed
      if (data.user && data.user.confirmed_at) {
        setIsGuest(false);
        sessionStorage.removeItem('isGuest');
        return { error: null };
      } else {
        // User exists but email is not confirmed
        await supabase.auth.signOut();
        return { error: { message: "Please confirm your email before signing in" } };
      }
    }
    return { error };
  };

  const signUp = async (email: string, password: string, options?: { data?: Record<string, any> }) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: options?.data,
        emailRedirectTo: window.location.origin,
      }
    });
    return { error, user: data?.user || null };
  };

  const signInWithGoogle = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/app/#/dashboard`, // Redirect to dashboard after Google login with hash routing
      },
    });
    return { error };
  };

  const signOut = async (retryAttempt: number = 0) => {
    // Prevent multiple simultaneous logout attempts
    if (isLoggingOut && retryAttempt === 0) {
      console.log('⚠️ Logout already in progress, skipping duplicate request');
      return;
    }

    const maxRetries = 2;
    const isRetry = retryAttempt > 0;

    try {
      console.log(`🔄 ${isRetry ? `Retrying logout (attempt ${retryAttempt + 1}/${maxRetries + 1})` : 'Starting logout process'}...`);

      if (!isRetry) {
        setIsLoggingOut(true);
        setLogoutRetryCount(0);
      }

      // Call Supabase signOut with timeout to prevent hanging
      const signOutPromise = supabase.auth.signOut();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Logout timeout')), 10000)
      );

      await Promise.race([signOutPromise, timeoutPromise]);

      console.log('✅ Supabase signOut completed successfully');

      // Reset retry count on success
      setLogoutRetryCount(0);

      // Note: State clearing is now handled by the auth state change handler
      // to prevent race conditions and ensure consistent behavior

    } catch (error) {
      console.error(`❌ Error during Supabase sign out (attempt ${retryAttempt + 1}):`, error);

      // Retry logic for network errors
      if (retryAttempt < maxRetries && (
        error instanceof Error && (
          error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('fetch')
        )
      )) {
        console.log(`🔄 Retrying logout in ${(retryAttempt + 1) * 1000}ms...`);
        setLogoutRetryCount(retryAttempt + 1);

        setTimeout(() => {
          signOut(retryAttempt + 1);
        }, (retryAttempt + 1) * 1000);
        return;
      }

      // If all retries failed or non-retryable error, manually clear state
      console.log('🔄 Manually clearing state due to signOut failure');
      clearAuthenticationState(true); // Broadcast to other tabs even on manual clear

      // Reset logout state and navigate manually since auth state change won't fire
      setIsLoggingOut(false);
      setLogoutRetryCount(0);
      navigate('/app#auth');
    }
  };

  // Clear all dataset-related local storage for Guest login security
  const clearDatasetStorage = () => {
    try {
      // Clear main dataset storage (both new and legacy keys)
      localStorage.removeItem('datastatpro_datasets');
      localStorage.removeItem('statistica_datasets');

      // Clear results and projects storage (both new and legacy keys)
      localStorage.removeItem('datastatpro_results');
      localStorage.removeItem('statistica_results');
      localStorage.removeItem('datastatpro_projects');
      localStorage.removeItem('statistica_projects');

      // Clear analysis configuration and results storage
      const analysisKeys = [
        'descriptive_analysis_config',
        'descriptive_analysis_results',
        'cross_tabulation_config',
        'cross_tabulation_results',
        'linear_regression_results',
        'posthoc_test_results',
        'mediation_results',
        'moderation_results',
        'cohort_calculator_cell_values',
        'cohort_calculator_strata',
        'cohort_calculator_results',
        'cohort_calculator_current_stratum_index',
        'analysisAssistantTrainingData'
      ];

      // Clear t-test results for all test types
      const ttestTypes = ['one_sample', 'independent', 'paired'];
      ttestTypes.forEach(type => {
        analysisKeys.push(`ttest_results_${type}`);
        analysisKeys.push(`ttest_assumptions_${type}`);
      });

      // Clear guided workflow progress data
      Object.keys(localStorage).forEach(key => {
        if (key.endsWith('-progress')) {
          analysisKeys.push(key);
        }
      });

      // Remove all analysis-related keys
      analysisKeys.forEach(key => {
        localStorage.removeItem(key);
      });

      console.log('🧹 Cleared all dataset-related storage for Guest login');
    } catch (error) {
      console.error('Error clearing dataset storage:', error);
    }
  };

  const loginAsGuest = () => {
    setLoading(true);

    // Clear all dataset-related storage to prevent privilege escalation
    clearDatasetStorage();

    setSession(null);
    setUser(null);
    setIsGuest(true);
    sessionStorage.setItem('isGuest', 'true');
    setLoading(false);
  };

  // Function to log login events only to the 'events' table
  const logLoginEvent = async (event_type: 'app_open' | 'login' | 'signin' | 'sign_in' | 'signed_in', details?: Record<string, any>) => {
    try {
      const currentUser = await supabase.auth.getUser();
      const userId = currentUser.data.user?.id;

      if (!userId) {
        console.warn("Attempted to log login event without a user_id:", event_type);
        return;
      }

      // Validate that only login-related events are logged
      const allowedEvents = ['app_open', 'login', 'signin', 'sign_in', 'signed_in'];
      if (!allowedEvents.includes(event_type)) {
        console.warn("Attempted to log non-login event:", event_type);
        return;
      }

      const { error } = await supabase
        .from('events')
        .insert([
          { user_id: userId, event_type, details }
        ]);

      if (error) {
        console.error("Error logging login event:", error);
      } else {
        console.log(`✅ Login event logged: ${event_type}`);
      }
    } catch (error) {
      console.error("Unexpected error logging login event:", error);
    }
  };

  const logoutGuest = () => {
    setLoading(true);
    setIsGuest(false);
    sessionStorage.removeItem('isGuest');
    setLoading(false);
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    return { error };
  };

  const updateProfile = async (data: { username?: string, avatar_url?: string, full_name?: string, institution?: string, country?: string }) => {
    if (!user) return { error: { message: "User not authenticated" } };

    console.log('🔄 Updating profile for user:', user.id, data);
    const { error } = await supabase
      .from('profiles')
      .update({
        ...data,
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id);

    if (!error) {
      console.log('✅ Profile updated successfully, refreshing profile data...');
      // Refresh profile data after successful update
      await refreshProfile();
    } else {
      console.error('❌ Error updating profile:', error);
    }

    return { error };
  };

  const uploadAvatar = async (file: File) => {
    if (!user) return { error: { message: "User not authenticated" } };

    const MAX_FILE_SIZE = 100 * 1024;
    if (file.size > MAX_FILE_SIZE) {
      return { error: { message: "Avatar image must be less than 100KB in size." } };
    }

    const fileExt = file.name.split('.').pop();
    const fileName = `${user.id}.${fileExt}`;
    const filePath = `${user.id}/${fileName}`;

    const { error: uploadError } = await supabase.storage
      .from('avatars')
      .upload(filePath, file, { upsert: true });

    if (uploadError) {
      return { error: uploadError };
    }

    const { data: { publicUrl } } = supabase.storage
      .from('avatars')
      .getPublicUrl(filePath);

    if (!publicUrl) {
      return { error: { message: "Could not get public URL for avatar." } };
    }
    
    const { error: updateError } = await updateProfile({ avatar_url: publicUrl });
    if (updateError) {
      return { error: updateError };
    }

    return { error: null, publicUrl };
  };

  const value = {
    session,
    user,
    userProfile,
    isGuest,
    isAuthenticated: !!user && !isGuest,
    loading,
    canAccessSampleData,
    canImportData,
    canAccessProFeatures,
    showSignupSuccessMessage, // Provide new state
    clearSignupSuccessMessage, // Provide new function
    signIn,
    signUp,
    signInWithGoogle, // Provide new function
    signOut,
    loginAsGuest,
    logoutGuest,
    resetPassword,
    updateProfile,
    uploadAvatar,
    logLoginEvent, // Add logLoginEvent to the value object (renamed from logEvent for login-only tracking)
    accountType,
    refreshProfile, // Add refreshProfile function
    // New subscription-related properties
    subscriptionData,
    subscriptionStatus,
    hasActiveSubscription,
    nextPaymentDate,
    billingCycle,
    refreshSubscription,
    canUpgradeAccount,
    // New educational tier properties
    canAccessAdvancedAnalysis,
    canAccessPublicationReady,
    canAccessCloudStorage,
    isEducationalUser,
    educationalTier,
    // Admin-related properties
    isAdmin,
    canAccessAdminDashboard,
    refreshAdminStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
