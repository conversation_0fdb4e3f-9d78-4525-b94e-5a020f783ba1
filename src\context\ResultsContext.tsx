import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { saveAs } from 'file-saver';
import mermaid from 'mermaid';
import { saveProject, loadProject, listProjects, deleteProject, SavedProject, ProjectContent } from '../utils/services/projectService';
import { useAuth } from './AuthContext';

// Define the structure for a single result item
export interface ResultItem {
  id: string;
  title: string;
  type: 'descriptive' | 'normality' | 'ttest' | 'anova' | 'correlation' | 'regression' | 'nonparametric' | 'other';
  timestamp: number;
  component: string;
  data: any;
  selected?: boolean;
  projectId?: string; // Optional project association for Pro users
}

// Define the structure for a project
export interface Project {
  id: string;
  name: string;
  isLocal: boolean; // true for local projects, false for cloud projects
  resultCount: number;
  lastModified: Date;
  cloudProject?: SavedProject | null; // Reference to cloud project if applicable
}

// Shared interface for HTML export formatting
interface HTMLExportConfig {
  decimalPlaces: number;
  showConfidenceIntervals: boolean;
  tableStyle: string;
  headerStyle: string;
  cellStyle: string;
}

// Default HTML export configuration
const DEFAULT_HTML_CONFIG: HTMLExportConfig = {
  decimalPlaces: 3,
  showConfidenceIntervals: true,
  tableStyle: "border-collapse: collapse; width: 100%; margin: 20px 0;",
  headerStyle: "border: 1px solid #ddd; padding: 12px; background-color: #f2f2f2; font-weight: bold;",
  cellStyle: "border: 1px solid #ddd; padding: 12px;"
};

// Helper functions for consistent formatting
const formatNumber = (value: number | undefined, decimals: number = 3): string => {
  if (value === undefined || isNaN(value)) return 'N/A';
  return value.toFixed(decimals);
};

const formatPValue = (value: number | undefined): string => {
  if (value === undefined || isNaN(value)) return 'N/A';
  return value < 0.001 ? '< .001' : value.toFixed(3);
};

const formatConfidenceInterval = (lower: number | undefined, upper: number | undefined, decimals: number = 3): string => {
  if (lower === undefined || upper === undefined || isNaN(lower) || isNaN(upper)) return 'N/A';
  return `[${lower.toFixed(decimals)}, ${upper.toFixed(decimals)}]`;
};

// Define the context interface
interface ResultsContextType {
  results: ResultItem[];
  projects: Project[];
  currentProjectId: string | null;
  addResult: (result: Omit<ResultItem, 'id' | 'timestamp' | 'selected'>) => string;
  updateResult: (id: string, data: Partial<ResultItem>) => void;
  removeResult: (id: string) => void;
  clearResults: () => void;
  toggleResultSelection: (id: string) => void;
  selectAllResults: () => void;
  deselectAllResults: () => void;
  getSelectedResults: () => ResultItem[];
  exportToHTML: () => void;
  reorderResults: (newResults: ResultItem[]) => void;
  // Project management methods
  createProject: (name: string, isLocal?: boolean) => Promise<string>;
  deleteProject: (projectId: string) => Promise<void>;
  setCurrentProject: (projectId: string | null) => void;
  getProjectResults: (projectId: string | null) => ResultItem[];
  moveResultToProject: (resultId: string, projectId: string | null) => void;
  saveProjectToCloud: (projectId: string) => Promise<void>;
  loadProjectFromCloud: (cloudProjectId: string) => Promise<void>;
  listCloudProjects: () => Promise<SavedProject[]>;
  syncProjects: () => Promise<void>;
  // Sync status and error handling
  syncStatus: Record<string, 'syncing' | 'success' | 'error' | ''>;
  syncErrors: Record<string, string>;
  manualSyncProject: (projectId: string) => Promise<void>;
}

// Create the context with default values
const ResultsContext = createContext<ResultsContextType | undefined>(undefined);

// Storage keys
const RESULTS_STORAGE_KEY = 'datastatpro_results';
const PROJECTS_STORAGE_KEY = 'datastatpro_projects';
const LEGACY_RESULTS_KEY = 'statistica_results';
const LEGACY_PROJECTS_KEY = 'statistica_projects';

// Migration function for results
const migrateResultsStorage = (): ResultItem[] => {
  // Check for new key first
  const newData = localStorage.getItem(RESULTS_STORAGE_KEY);
  if (newData) {
    try {
      return JSON.parse(newData);
    } catch (e) {
      console.error('Error parsing new results:', e);
    }
  }

  // Check for legacy key and migrate
  const legacyData = localStorage.getItem(LEGACY_RESULTS_KEY);
  if (legacyData) {
    try {
      const parsedData = JSON.parse(legacyData);
      localStorage.setItem(RESULTS_STORAGE_KEY, legacyData);
      localStorage.removeItem(LEGACY_RESULTS_KEY);
      console.log('📦 Migrated results from legacy storage key');
      return parsedData;
    } catch (e) {
      console.error('Error parsing legacy results:', e);
      localStorage.removeItem(LEGACY_RESULTS_KEY);
    }
  }

  return [];
};

// Migration function for projects
const migrateProjectsStorage = (): Project[] => {
  // Check for new key first
  const newData = localStorage.getItem(PROJECTS_STORAGE_KEY);
  if (newData) {
    try {
      return JSON.parse(newData);
    } catch (e) {
      console.error('Error parsing new projects:', e);
    }
  }

  // Check for legacy key and migrate
  const legacyData = localStorage.getItem(LEGACY_PROJECTS_KEY);
  if (legacyData) {
    try {
      const parsedData = JSON.parse(legacyData);
      localStorage.setItem(PROJECTS_STORAGE_KEY, legacyData);
      localStorage.removeItem(LEGACY_PROJECTS_KEY);
      console.log('📦 Migrated projects from legacy storage key');
      return parsedData;
    } catch (e) {
      console.error('Error parsing legacy projects:', e);
      localStorage.removeItem(LEGACY_PROJECTS_KEY);
    }
  }

  return [{ id: 'default', name: 'Default Project', isLocal: true, resultCount: 0, lastModified: new Date() }];
};

// Provider component
export const ResultsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();

  const [results, setResults] = useState<ResultItem[]>(() => {
    // Load saved results from localStorage on initialization with migration
    return migrateResultsStorage();
  });

  // Project-related state
  const [projects, setProjects] = useState<Project[]>(() => {
    // Load saved projects from localStorage on initialization with migration
    return migrateProjectsStorage();
  });

  const [currentProjectId, setCurrentProjectId] = useState<string | null>('default');

  // Save results to localStorage whenever they change
  React.useEffect(() => {
    localStorage.setItem(RESULTS_STORAGE_KEY, JSON.stringify(results));
  }, [results]);

  // Save projects to localStorage whenever they change
  React.useEffect(() => {
    localStorage.setItem(PROJECTS_STORAGE_KEY, JSON.stringify(projects));
  }, [projects]);

  // State for sync status and error handling
  const [syncStatus, setSyncStatus] = useState<Record<string, 'syncing' | 'success' | 'error' | ''>>({});
  const [syncErrors, setSyncErrors] = useState<Record<string, string>>({});

  // Helper function to auto-sync cloud projects when results change
  const autoSyncCloudProject = async (projectId: string) => {
    // Use a timeout to ensure state updates have been processed
    setTimeout(async () => {
      const project = projects.find(p => p.id === projectId);
      if (project && !project.isLocal && project.cloudProject) {
        try {
          setSyncStatus(prev => ({ ...prev, [projectId]: 'syncing' }));
          setSyncErrors(prev => ({ ...prev, [projectId]: '' }));

          await saveProjectToCloud(projectId);

          setSyncStatus(prev => ({ ...prev, [projectId]: 'success' }));
          // Clear success status after 3 seconds
          setTimeout(() => {
            setSyncStatus(prev => ({ ...prev, [projectId]: '' }));
          }, 3000);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to sync cloud project';
          console.error('Failed to auto-sync cloud project:', error);
          setSyncStatus(prev => ({ ...prev, [projectId]: 'error' }));
          setSyncErrors(prev => ({ ...prev, [projectId]: errorMessage }));
        }
      }
    }, 100); // Small delay to ensure state consistency
  };

  // Auto-sync cloud projects when user logs in
  useEffect(() => {
    if (isAuthenticated && user) {
      // Sync projects when user logs in
      syncProjects().catch(error => {
        console.error('Failed to sync projects on login:', error);
      });
    }
  }, [isAuthenticated, user]);

  // Update project result counts when results change
  React.useEffect(() => {
    setProjects(prevProjects =>
      prevProjects.map(project => ({
        ...project,
        resultCount: results.filter(result =>
          (result.projectId || 'default') === project.id
        ).length,
        lastModified: new Date()
      }))
    );
  }, [results]);

  // Add a new result
  const addResult = (result: Omit<ResultItem, 'id' | 'timestamp' | 'selected'>) => {
    const id = `result_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const projectId = result.projectId || currentProjectId || 'default';
    const newResult: ResultItem = {
      ...result,
      id,
      timestamp: Date.now(),
      selected: false,
      // Assign to current project if not specified, or default project for backward compatibility
      projectId
    };

    setResults(prevResults => [...prevResults, newResult]);

    // Auto-sync cloud project if applicable
    autoSyncCloudProject(projectId);

    return id;
  };

  // Update an existing result
  const updateResult = (id: string, data: Partial<ResultItem>) => {
    let updatedProjectId: string | undefined;

    setResults(prevResults =>
      prevResults.map(result => {
        if (result.id === id) {
          const updated = { ...result, ...data };
          updatedProjectId = updated.projectId || 'default';
          return updated;
        }
        return result;
      })
    );

    // Auto-sync cloud project if applicable
    if (updatedProjectId) {
      autoSyncCloudProject(updatedProjectId);
    }
  };

  // Remove a result
  const removeResult = (id: string) => {
    let removedProjectId: string | undefined;

    setResults(prevResults => {
      const result = prevResults.find(r => r.id === id);
      removedProjectId = result?.projectId || 'default';
      return prevResults.filter(result => result.id !== id);
    });

    // Auto-sync cloud project if applicable
    if (removedProjectId) {
      autoSyncCloudProject(removedProjectId);
    }
  };

  // Clear all results
  const clearResults = () => {
    setResults([]);
  };

  // Toggle selection of a result
  const toggleResultSelection = (id: string) => {
    setResults(prevResults =>
      prevResults.map(result =>
        result.id === id ? { ...result, selected: !result.selected } : result
      )
    );
  };

  // Select all results
  const selectAllResults = () => {
    setResults(prevResults =>
      prevResults.map(result => ({ ...result, selected: true }))
    );
  };

  // Deselect all results
  const deselectAllResults = () => {
    setResults(prevResults =>
      prevResults.map(result => ({ ...result, selected: false }))
    );
  };

  // Get selected results
  const getSelectedResults = () => {
    return results.filter(result => result.selected);
  };

  // Reorder results (for drag and drop functionality)
  const reorderResults = (newResults: ResultItem[]) => {
    setResults(newResults);
    // Persist the new order to localStorage
    localStorage.setItem('statistica_results', JSON.stringify(newResults));
  };

  // Helper function to format date
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Project management methods
  const createProject = async (name: string, isLocal: boolean = true): Promise<string> => {
    const id = `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newProject: Project = {
      id,
      name,
      isLocal,
      resultCount: 0,
      lastModified: new Date()
    };

    setProjects(prevProjects => [...prevProjects, newProject]);
    return id;
  };

  const deleteProjectLocal = async (projectId: string): Promise<void> => {
    if (projectId === 'default') {
      throw new Error('Cannot delete the default project');
    }

    const project = projects.find(p => p.id === projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    // If it's a cloud project, delete from cloud storage
    if (!project.isLocal && project.cloudProject) {
      const { error } = await deleteProject(project.cloudProject.id);
      if (error) {
        throw new Error(`Failed to delete cloud project: ${error.message}`);
      }
    }

    // Move all results from this project to default project
    setResults(prevResults =>
      prevResults.map(result =>
        result.projectId === projectId
          ? { ...result, projectId: 'default' }
          : result
      )
    );

    // Remove project from local state
    setProjects(prevProjects => prevProjects.filter(p => p.id !== projectId));

    // If current project is being deleted, switch to default
    if (currentProjectId === projectId) {
      setCurrentProjectId('default');
    }
  };

  const setCurrentProject = (projectId: string | null) => {
    setCurrentProjectId(projectId);
  };

  const getProjectResults = (projectId: string | null): ResultItem[] => {
    const targetProjectId = projectId || 'default';
    return results.filter(result => (result.projectId || 'default') === targetProjectId);
  };

  const moveResultToProject = (resultId: string, projectId: string | null) => {
    const targetProjectId = projectId || 'default';
    setResults(prevResults =>
      prevResults.map(result =>
        result.id === resultId
          ? { ...result, projectId: targetProjectId }
          : result
      )
    );
  };

  const saveProjectToCloud = async (projectId: string): Promise<void> => {
    const project = projects.find(p => p.id === projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    const projectResults = getProjectResults(projectId);
    const projectContent: ProjectContent = {
      results: projectResults,
      metadata: {
        totalResults: projectResults.length,
        lastModified: new Date().toISOString(),
        version: '1.0'
      }
    };

    let savedProject;

    // For existing cloud projects, we need to update them
    if (!project.isLocal && project.cloudProject) {
      // Try to save with a new name first to avoid data loss
      const backupName = `${project.name}_backup_${Date.now()}`;
      const { data: backupData, error: backupError } = await saveProject(backupName, projectContent);

      if (backupError) {
        throw new Error(`Failed to create backup: ${backupError.message}`);
      }

      // Now try to delete the old version
      const { error: deleteError } = await deleteProject(project.cloudProject.id);
      if (deleteError) {
        // If delete fails, clean up the backup and throw error
        await deleteProject(backupData!.id);
        throw new Error(`Failed to delete existing cloud project: ${deleteError.message}`);
      }

      // Create the new version with the original name
      const { data, error } = await saveProject(project.name, projectContent);
      if (error) {
        // If creation fails, we still have the backup - don't delete it
        throw new Error(`Failed to save updated project to cloud: ${error.message}. Backup created with name: ${backupName}`);
      }

      // Success - clean up the backup
      await deleteProject(backupData!.id);
      savedProject = data;
    } else {
      // Create new cloud project
      const { data, error } = await saveProject(project.name, projectContent);
      if (error) {
        throw new Error(`Failed to save project to cloud: ${error.message}`);
      }
      savedProject = data;
    }

    // Update project to reference the new cloud project
    setProjects(prevProjects =>
      prevProjects.map(p =>
        p.id === projectId
          ? { ...p, isLocal: false, lastModified: new Date(), cloudProject: savedProject }
          : p
      )
    );
  };

  const loadProjectFromCloud = async (cloudProjectId: string): Promise<void> => {
    const { data: projectContent, error } = await loadProject(cloudProjectId);
    if (error) {
      throw new Error(`Failed to load project from cloud: ${error.message}`);
    }

    if (!projectContent) {
      throw new Error('Project content is empty');
    }

    // Get the cloud project metadata
    const cloudProjects = await listCloudProjects();
    const cloudProject = cloudProjects.find(cp => cp.id === cloudProjectId);

    if (!cloudProject) {
      throw new Error('Cloud project metadata not found');
    }

    // Check if this cloud project is already loaded
    const existingProject = projects.find(p => p.cloudProject?.id === cloudProjectId);
    if (existingProject) {
      // Switch to existing project
      setCurrentProject(existingProject.id);
      return;
    }

    // Create a direct cloud project reference (not a local copy)
    const projectId = `cloud_${cloudProjectId}`;
    const newProject: Project = {
      id: projectId,
      name: cloudProject.project_name,
      isLocal: false,
      resultCount: projectContent.results.length,
      lastModified: new Date(cloudProject.updated_at),
      cloudProject: cloudProject
    };

    // Add the cloud project to projects list
    setProjects(prevProjects => [...prevProjects, newProject]);

    // Add all results from the cloud project with the cloud project ID
    projectContent.results.forEach(result => {
      const newResult: ResultItem = {
        ...result,
        id: result.id || `result_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        projectId,
        selected: false
      };
      setResults(prevResults => [...prevResults, newResult]);
    });

    // Set the cloud project as current
    setCurrentProject(projectId);
  };

  const listCloudProjects = async (): Promise<SavedProject[]> => {
    const { data, error } = await listProjects();
    if (error) {
      throw new Error(`Failed to list cloud projects: ${error.message}`);
    }
    return data || [];
  };

  const syncProjects = async (): Promise<void> => {
    try {
      const cloudProjects = await listCloudProjects();

      // First, update existing local projects with cloud project references
      setProjects(prevProjects => {
        const updatedProjects = prevProjects.map(localProject => {
          const matchingCloudProject = cloudProjects.find(
            cp => cp.project_name === localProject.name
          );

          if (matchingCloudProject && localProject.isLocal) {
            return {
              ...localProject,
              isLocal: false,
              cloudProject: matchingCloudProject
            };
          }

          return localProject;
        });

        // Then, add any cloud projects that aren't already loaded
        const newCloudProjects: Project[] = [];

        for (const cloudProject of cloudProjects) {
          const existingProject = updatedProjects.find(
            p => p.cloudProject?.id === cloudProject.id
          );

          if (!existingProject) {
            // Load this cloud project automatically
            const projectId = `cloud_${cloudProject.id}`;
            newCloudProjects.push({
              id: projectId,
              name: cloudProject.project_name,
              isLocal: false,
              resultCount: 0, // Will be updated when results are loaded
              lastModified: new Date(cloudProject.updated_at),
              cloudProject: cloudProject
            });

            // Load the project content asynchronously
            loadProject(cloudProject.id).then(({ data: projectContent, error }) => {
              if (!error && projectContent) {
                // Add all results from the cloud project
                projectContent.results.forEach(result => {
                  const newResult: ResultItem = {
                    ...result,
                    id: result.id || `result_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    projectId,
                    selected: false
                  };
                  setResults(prevResults => {
                    // Check if result already exists to avoid duplicates
                    const exists = prevResults.some(r => r.id === newResult.id);
                    return exists ? prevResults : [...prevResults, newResult];
                  });
                });
              }
            }).catch(error => {
              console.error(`Failed to load cloud project ${cloudProject.id}:`, error);
            });
          }
        }

        return [...updatedProjects, ...newCloudProjects];
      });
    } catch (error) {
      console.error('Error syncing projects:', error);
      // Don't throw error for sync failures to avoid breaking the UI
    }
  };





























  // Export results to HTML
  const exportToHTML = () => {
    const selectedResults = getSelectedResults();
    if (selectedResults.length === 0) {
      alert('Please select at least one result to export');
      return;
    }

    // Create HTML content
    let htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Statistical Analysis Results</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 1200px; margin: 0 auto; padding: 20px; }
          h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
          h2 { color: #2980b9; margin-top: 30px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
          h3 { color: #3498db; }
          table { border-collapse: collapse; width: 100%; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
          th { background-color: #f2f2f2; }
          tr:nth-child(even) { background-color: #f9f9f9; }
          .result-container { margin-bottom: 40px; border: 1px solid #ddd; padding: 20px; border-radius: 5px; }
          .meta-info { color: #7f8c8d; font-size: 0.9em; margin-bottom: 15px; }
          .interpretation { background-color: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 20px 0; }
        </style>
      </head>
      <body>
        <h1>Statistical Analysis Results</h1>
        <p>Generated on ${new Date().toLocaleDateString()}</p>
    `;

    // Add each selected result
    for (const result of selectedResults) {
      htmlContent += `
        <div class="result-container">
          <h2>${result.title}</h2>
          <div class="meta-info">
            <p><strong>Analysis Type:</strong> ${result.type.charAt(0).toUpperCase() + result.type.slice(1)}</p>
            <p><strong>Date:</strong> ${formatDate(result.timestamp)}</p>
          </div>
      `;

      // Add specific content based on result type
      switch (result.type) {
        case 'descriptive':
          // Handle Publication Ready components (Table1, Table1a, Table1b)
          if (result.data.results) {
            htmlContent += `<h3>Descriptive Statistics</h3>`;
            htmlContent += createHTMLDescriptiveTable(result.data.results, result.data.totalSampleSize);

            // Add write-up if available (Table1)
            if (result.data.writeUp) {
              htmlContent += `
                <h3>Statistical Summary</h3>
                <div class="interpretation">
                  <p>${result.data.writeUp.replace(/\n/g, '<br>')}</p>
                </div>
              `;
            }

            // Add interpretation if available (Table1b)
            if (result.data.interpretation) {
              htmlContent += `
                <h3>Interpretation</h3>
                <div class="interpretation">
                  <p>${result.data.interpretation.replace(/\n/g, '<br>')}</p>
                </div>
              `;
            }
          }
          // Fallback for legacy format
          else if (result.data.statistics) {
            htmlContent += createHTMLStatisticsTable(result.data.statistics);
          }
          break;
          
        case 'normality':
          if (result.data.statistics) {
            htmlContent += `<h3>Normality Test Results</h3>`;
            htmlContent += createHTMLNormalityTable(result.data.statistics);
            
            if (result.data.interpretation) {
              htmlContent += `
                <h3>Interpretation</h3>
                <div class="interpretation">
                  <p>${result.data.interpretation.replace(/\n/g, '<br>')}</p>
                </div>
              `;
            }
          }
          break;
          
        case 'ttest':
          // Add descriptive statistics if available
          if (result.data.testResults && result.data.testResults.groups) {
            htmlContent += `<h3>Descriptive Statistics</h3>`;
            
            // Create descriptive statistics from groups data
            const descriptiveStats = {};
            result.data.testResults.groups.forEach((group, index) => {
              descriptiveStats[`Group ${index+1} (${group.name})`] = {};
              descriptiveStats[`Group ${index+1} (${group.name})`]['N'] = group.n;
              descriptiveStats[`Group ${index+1} (${group.name})`]['Mean'] = group.mean?.toFixed(4);
              descriptiveStats[`Group ${index+1} (${group.name})`]['Median'] = group.median?.toFixed(4);
              descriptiveStats[`Group ${index+1} (${group.name})`]['Std. Deviation'] = group.sd?.toFixed(4);
            });
            
            htmlContent += createHTMLStatisticsTable(descriptiveStats);
          } else if (result.data.descriptiveStats) {
            htmlContent += `<h3>Descriptive Statistics</h3>`;
            htmlContent += createHTMLStatisticsTable(result.data.descriptiveStats);
          }
          
          // Add T-test results
          if (result.data.testResults) {
            htmlContent += `<h3>T-Test Results</h3>`;
            htmlContent += createHTMLTTestTable(result.data.testResults);
          }
          
          // Add graph information if available
          if (result.data.testResults && result.data.testResults.chartData) {
            htmlContent += `
              <h3>Graph</h3>
              <div class="graph-info">
                <p>A graph visualization is available in the application.</p>
              </div>
            `;
          } else if (result.data.graph) {
            htmlContent += `
              <h3>Graph</h3>
              <div class="graph-info">
                <p>A graph visualization is available in the application.</p>
              </div>
            `;
          }
          
          // Add interpretation if available
          if (result.data.testResults && result.data.testResults.interpretation) {
            htmlContent += `
              <h3>Interpretation</h3>
              <div class="interpretation">
                <p>${result.data.testResults.interpretation.replace(/\n/g, '<br>')}</p>
              </div>
            `;
          } else if (result.data.interpretation) {
            htmlContent += `
              <h3>Interpretation</h3>
              <div class="interpretation">
                <p>${result.data.interpretation.replace(/\n/g, '<br>')}</p>
              </div>
            `;
          }
          break;

        case 'regression':
          // Handle Regression Table component
          if (result.data.results) {
            htmlContent += `<h3>${result.data.regressionType ? result.data.regressionType.charAt(0).toUpperCase() + result.data.regressionType.slice(1) : 'Regression'} Results</h3>`;
            htmlContent += createHTMLRegressionTable(result.data.results, result.data.regressionType);

            if (result.data.dependentVariable) {
              htmlContent += `<p><strong>Dependent Variable:</strong> ${result.data.dependentVariable}</p>`;
            }
            if (result.data.independentVariables && result.data.independentVariables.length > 0) {
              htmlContent += `<p><strong>Independent Variables:</strong> ${result.data.independentVariables.join(', ')}</p>`;
            }

            // Add interpretation section if available
            if (result.data.interpretation) {
              htmlContent += `
                <h4>${result.data.regressionType ? result.data.regressionType.charAt(0).toUpperCase() + result.data.regressionType.slice(1) : 'Regression'} Interpretation</h4>
                <div class="interpretation" style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0; border-radius: 4px;">
                  <p style="margin: 0; line-height: 1.6; color: #333;">${result.data.interpretation}</p>
                </div>
              `;
            }
          }
          break;

        case 'other':
          // Handle Table2, SMD, PostHoc, and Flow Diagram components
          if (result.component === 'Table2' && result.data.results) {
            htmlContent += `<h3>Group Comparisons</h3>`;
            htmlContent += createHTMLTable2(result.data.results, result.data.groupingVariable, {
              totalSampleSize: result.data.totalSampleSize,
              totalNPerGroup: result.data.totalNPerGroup,
              groupingCategories: result.data.groupingCategories
            });

            if (result.data.writeUp) {
              htmlContent += `
                <h3>Statistical Summary</h3>
                <div class="interpretation">
                  <p>${result.data.writeUp.replace(/\n/g, '<br>')}</p>
                </div>
              `;
            }
          } else if (result.component === 'SMDTable' && result.data.results) {
            htmlContent += `<h3>Standardized Mean Differences</h3>`;
            htmlContent += createHTMLSMDTable(result.data.results);

            if (result.data.groupingVariable) {
              htmlContent += `<p><strong>Grouping Variable:</strong> ${result.data.groupingVariable}</p>`;
            }
          } else if (result.component === 'PostHocTests' && result.data.results) {
            htmlContent += `<h3>Post-Hoc Test Results</h3>`;
            htmlContent += createHTMLPostHocTable(result.data.results);

            if (result.data.interpretation) {
              htmlContent += `
                <h3>Interpretation</h3>
                <div class="interpretation">
                  <p>${result.data.interpretation.replace(/\n/g, '<br>')}</p>
                </div>
              `;
            }
          } else if (result.component === 'FlowDiagram' && result.data.stages) {
            htmlContent += `<h3>Research Flow Chart</h3>`;
            htmlContent += createHTMLFlowDiagram(
              result.data.stages,
              result.data.diagramDirection,
              result.data.mermaidSyntax
            );
          } else if (result.component === 'StatisticalMethodsGenerator' && result.data.methodsText) {
            htmlContent += `<h3>Statistical Methods</h3>`;
            htmlContent += `
              <div class="methods-section">
                <p>${result.data.methodsText.replace(/\n/g, '<br>')}</p>
              </div>
            `;

            if (result.data.analysesIncluded && result.data.analysesIncluded.length > 0) {
              htmlContent += `
                <h4>Analyses Included</h4>
                <ul>
              `;
              result.data.analysesIncluded.forEach((analysis: any) => {
                htmlContent += `<li>${analysis.analysisType}: ${analysis.count} analysis${analysis.count !== 1 ? 'es' : ''}</li>`;
              });
              htmlContent += `</ul>`;
            }

            if (result.data.wordCount) {
              htmlContent += `<p><em>Word count: ${result.data.wordCount}</em></p>`;
            }
          } else {
            htmlContent += `<p>Result data available in the application</p>`;
          }
          break;

        // Add more cases for other result types
        default:
          htmlContent += `<p>Result data available in the application</p>`;
      }

      htmlContent += `</div>`;
    }

    htmlContent += `
      </body>
      </html>
    `;

    // Create a blob and download the HTML file
    const blob = new Blob([htmlContent], { type: 'text/html' });
    saveAs(blob, 'statistical-analysis-results.html');
  };

  // Helper function to create HTML statistics table
  const createHTMLStatisticsTable = (statistics: any) => {
    let tableHTML = `
      <table>
        <tr>
          <th>Statistic</th>
          <th>Value</th>
        </tr>
    `;

    Object.entries(statistics).forEach(([key, value]) => {
      tableHTML += `
        <tr>
          <td>${key}</td>
          <td>${value}</td>
        </tr>
      `;
    });

    tableHTML += `</table>`;
    return tableHTML;
  };

  // Helper function to create HTML descriptive statistics table for Publication Ready components
  const createHTMLDescriptiveTable = (results: any, totalSampleSize?: number) => {
    if (!results || typeof results !== 'object') {
      return '<p>No descriptive statistics available</p>';
    }

    // Handle different result structures based on component type
    if (Array.isArray(results)) {
      // Check if it's Table1 format (array of Table1Result objects)
      if (results.length > 0 && results[0].variableName && results[0].results) {
        return createHTMLTable1Format(results, totalSampleSize);
      }
      // Check if it's Table1a format (array with categoryData)
      else if (results.length > 0 && results[0].categoryData) {
        return createHTMLTable1aFormat(results);
      }
      // Check if it's Table1b format (array with numeric statistics)
      else if (results.length > 0 && results[0].n !== undefined && results[0].mean !== undefined) {
        return createHTMLTable1bFormat(results);
      }
    }

    // Fallback for unknown format
    return '<p>Descriptive statistics data format not recognized</p>';
  };

  // Helper function for Table1 format
  const createHTMLTable1Format = (results: any[], totalSampleSize?: number) => {
    const sampleSize = totalSampleSize || results.length > 0 ? 100 : 0; // Default fallback

    let tableHTML = `
      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <thead>
          <tr>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left; background-color: #f2f2f2; font-weight: bold;">Variable</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">Overall (n=${sampleSize})</th>
          </tr>
        </thead>
        <tbody>
    `;

    results.forEach(result => {
      const stats = result.results;

      // Main variable row (bold, with empty data cell)
      tableHTML += `
        <tr>
          <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">${result.variableName}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: right;"></td>
        </tr>
      `;

      // Sub-rows for statistics
      if (result.dataType === 'numeric') {
        // Mean (SD) row
        if (stats.mean !== undefined && stats.standardDeviation !== undefined) {
          tableHTML += `
            <tr>
              <td style="border: 1px solid #ddd; padding: 12px; padding-left: 32px;">Mean (SD)</td>
              <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${stats.mean.toFixed(2)} (${stats.standardDeviation.toFixed(2)})</td>
            </tr>
          `;
        }

        // Range row
        if (stats.range !== undefined) {
          tableHTML += `
            <tr>
              <td style="border: 1px solid #ddd; padding: 12px; padding-left: 32px;">Range</td>
              <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${stats.range.toFixed(2)}</td>
            </tr>
          `;
        }
      } else {
        // Categorical data - each category gets its own row
        if (stats.frequencies && stats.percentages) {
          Object.entries(stats.frequencies).forEach(([category, freq]) => {
            const percentage = stats.percentages[category];
            tableHTML += `
              <tr>
                <td style="border: 1px solid #ddd; padding: 12px; padding-left: 32px;">${category}</td>
                <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${freq} (${percentage.toFixed(1)}%)</td>
              </tr>
            `;
          });
        }
      }
    });

    tableHTML += `
        </tbody>
      </table>
    `;
    return tableHTML;
  };

  // Helper function for Table1a format
  const createHTMLTable1aFormat = (results: any[]) => {
    if (!results || results.length === 0) {
      return '<p>No Table1a data available</p>';
    }

    // Extract common categories from the first result
    const firstResult = results[0];
    const commonCategories = Object.keys(firstResult.categoryData || {});

    if (commonCategories.length === 0) {
      return '<p>No category data available</p>';
    }

    let tableHTML = `
      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <thead>
          <tr>
            <th rowspan="2" style="border: 1px solid #ddd; padding: 12px; text-align: left; background-color: #f2f2f2; font-weight: bold;">Variable</th>
    `;

    // Category headers with colspan=2
    commonCategories.forEach(category => {
      tableHTML += `<th colspan="2" style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">${category}</th>`;
    });

    tableHTML += `
          </tr>
          <tr>
    `;

    // Sub-headers for n and %
    commonCategories.forEach(() => {
      tableHTML += `
        <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">n</th>
        <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">%</th>
      `;
    });

    tableHTML += `
          </tr>
        </thead>
        <tbody>
    `;

    // Data rows
    results.forEach(result => {
      tableHTML += `
        <tr>
          <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">${result.variableName}</td>
      `;

      // Data cells for each category
      commonCategories.forEach(category => {
        const categoryData = result.categoryData[category];
        if (categoryData) {
          tableHTML += `
            <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${categoryData.n}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${categoryData.percentage.toFixed(1)}%</td>
          `;
        } else {
          tableHTML += `
            <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">N/A</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">N/A</td>
          `;
        }
      });

      tableHTML += `</tr>`;
    });

    tableHTML += `
        </tbody>
      </table>
    `;
    return tableHTML;
  };

  // Helper function for Table1b format
  const createHTMLTable1bFormat = (results: any[]) => {
    let tableHTML = `
      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <thead>
          <tr>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left; background-color: #f2f2f2; font-weight: bold;">Variable</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">N</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">Mean</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">SD</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">Median</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">Q1</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">Q3</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">IQR</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">Min</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">Max</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">Normality (p-value)</th>
          </tr>
        </thead>
        <tbody>
    `;

    results.forEach(result => {
      // Format normality p-value like the original (show <0.001 for very small values)
      let normalityText = 'N/A';
      if (result.normalityPValue !== undefined) {
        normalityText = result.normalityPValue < 0.001 ? '<0.001' : result.normalityPValue.toFixed(3);
      }

      tableHTML += `
        <tr>
          <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">${result.variableName}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.n || 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.mean ? result.mean.toFixed(2) : 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.standardDeviation ? result.standardDeviation.toFixed(2) : 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.median ? result.median.toFixed(2) : 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.q1 ? result.q1.toFixed(2) : 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.q3 ? result.q3.toFixed(2) : 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.iqr ? result.iqr.toFixed(2) : 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.min ? result.min.toFixed(2) : 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.max ? result.max.toFixed(2) : 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${normalityText}</td>
        </tr>
      `;
    });

    tableHTML += `
        </tbody>
      </table>
    `;
    return tableHTML;
  };

  // Helper function to create HTML normality test table
  const createHTMLNormalityTable = (statistics: any) => {
    return `
      <table>
        <tr>
          <th>Statistic</th>
          <th>Value</th>
        </tr>
        <tr>
          <td>Sample Size (N)</td>
          <td>${statistics.n}</td>
        </tr>
        <tr>
          <td>Mean</td>
          <td>${statistics.mean?.toFixed(4)}</td>
        </tr>
        <tr>
          <td>Standard Deviation</td>
          <td>${statistics.standardDeviation?.toFixed(4)}</td>
        </tr>
        <tr>
          <td>Skewness</td>
          <td>${statistics.skewness?.toFixed(4)}</td>
        </tr>
        <tr>
          <td>Kurtosis</td>
          <td>${statistics.kurtosis?.toFixed(4)}</td>
        </tr>
        <tr>
          <td>Normality Test p-value</td>
          <td>${statistics.isNormal?.pValue?.toFixed(4)}</td>
        </tr>
        <tr>
          <td>Normal Distribution</td>
          <td>${statistics.isNormal?.isNormal ? 'Yes (p ≥ 0.05)' : 'No (p < 0.05)'}</td>
        </tr>
      </table>
    `;
  };

  // Helper function to create HTML t-test table
  const createHTMLTTestTable = (testResults: any) => {
    return `
      <table>
        <tr>
          <th>Statistic</th>
          <th>Value</th>
        </tr>
        <tr>
          <td>Test Type</td>
          <td>${testResults.test || testResults.testType || 'Independent Samples t-Test'}</td>
        </tr>
        <tr>
          <td>t-statistic</td>
          <td>${(testResults.statistic || testResults.tStat || testResults.t)?.toFixed(4) || 'N/A'}</td>
        </tr>
        <tr>
          <td>Degrees of Freedom</td>
          <td>${testResults.df || 'N/A'}</td>
        </tr>
        <tr>
          <td>p-value</td>
          <td>${testResults.pValue?.toFixed(4) || 'N/A'}</td>
        </tr>
        <tr>
          <td>Mean Difference</td>
          <td>${testResults.meanDifference?.toFixed(4) || 'N/A'}</td>
        </tr>
        <tr>
          <td>Standard Error</td>
          <td>${(testResults.standardError || testResults.se)?.toFixed(4) || 'N/A'}</td>
        </tr>
        <tr>
          <td>Effect Size (Cohen's d)</td>
          <td>${(testResults.effectSize || testResults.cohensD)?.toFixed(4) || 'N/A'}</td>
        </tr>
        <tr>
          <td>95% Confidence Interval</td>
          <td>${Array.isArray(testResults.confidenceInterval) && testResults.confidenceInterval.length >= 2 ? 
            `${testResults.confidenceInterval[0]?.toFixed(4)} to ${testResults.confidenceInterval[1]?.toFixed(4)}` : 'N/A'}</td>
        </tr>
        <tr>
          <td>Significant</td>
          <td>${testResults.pValue < 0.05 ? 'Yes' : 'No'}</td>
        </tr>
      </table>
    `;
  };

  // Helper function to create HTML table for Table2 results
  const createHTMLTable2 = (results: any[], groupingVariable: string, additionalData?: any) => {
    if (!results || !Array.isArray(results)) {
      return '<p>No group comparison data available</p>';
    }

    // Extract grouping categories from the first result or additional data
    const firstResult = results[0];
    if (!firstResult || !firstResult.descriptives) {
      return '<p>No group comparison data available</p>';
    }

    const groupingCategories = additionalData?.groupingCategories ||
      Object.keys(firstResult.descriptives).filter(key => key !== 'Overall');
    const totalSampleSize = additionalData?.totalSampleSize || 100;
    const totalNPerGroup = additionalData?.totalNPerGroup || {};

    // Calculate total N per group if not provided
    const calculatedTotalNPerGroup = { ...totalNPerGroup };
    if (Object.keys(calculatedTotalNPerGroup).length === 0) {
      groupingCategories.forEach(category => {
        calculatedTotalNPerGroup[category] = firstResult.descriptives[category]?.n || 0;
      });
    }

    let tableHTML = `
      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <thead>
          <tr>
            <th rowspan="2" style="border: 1px solid #ddd; padding: 12px; text-align: left; background-color: #f2f2f2; font-weight: bold;">Variable</th>
            <th colspan="${groupingCategories.length + 1}" style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">${groupingVariable || 'Grouping Variable'}</th>
            <th rowspan="2" style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">p</th>
          </tr>
          <tr>
    `;

    // Add sub-headers for each category and Total
    groupingCategories.forEach(category => {
      const n = calculatedTotalNPerGroup[category] || 0;
      tableHTML += `<th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">${category} (n=${n})</th>`;
    });
    tableHTML += `<th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">Total (n=${totalSampleSize})</th>`;

    tableHTML += `
          </tr>
        </thead>
        <tbody>
    `;

    // Add Total n (%) row
    tableHTML += `
      <tr>
        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Total n (%)</td>
    `;
    groupingCategories.forEach(category => {
      const n = calculatedTotalNPerGroup[category] || 0;
      const percentage = totalSampleSize > 0 ? ((n / totalSampleSize) * 100).toFixed(1) : '0.0';
      tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${n} (${percentage}%)</td>`;
    });
    tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${totalSampleSize} (100.0%)</td>`;
    tableHTML += `<td style="border: 1px solid #ddd; padding: 12px;"></td>`; // Empty p-value cell
    tableHTML += `</tr>`;

    // Add data rows for each variable
    results.forEach(result => {
      // Main variable row
      tableHTML += `
        <tr>
          <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">${result.rowVariableName || 'N/A'}</td>
      `;

      // Data cells for each group and Total
      if (result.rowDataType === 'numeric') {
        // Numeric variables: show mean (SD)
        groupingCategories.forEach(category => {
          const stats = result.descriptives[category];
          if (stats && stats.mean !== undefined) {
            const meanStr = stats.mean.toFixed(2);
            const sdStr = stats.standardDeviation ? stats.standardDeviation.toFixed(2) : 'N/A';
            tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${meanStr} (${sdStr})</td>`;
          } else {
            tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">N/A</td>`;
          }
        });

        // Total column
        const overallStats = result.descriptives.Overall;
        if (overallStats && overallStats.mean !== undefined) {
          const meanStr = overallStats.mean.toFixed(2);
          const sdStr = overallStats.standardDeviation ? overallStats.standardDeviation.toFixed(2) : 'N/A';
          tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${meanStr} (${sdStr})</td>`;
        } else {
          tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">N/A</td>`;
        }
      } else {
        // Categorical variables: empty cells for main row
        groupingCategories.forEach(() => {
          tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;"></td>`;
        });
        tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;"></td>`; // Total column
      }

      // P-value cell
      if (result.testResult && result.testResult.pValue !== undefined) {
        const pValueStr = result.testResult.pValue.toFixed(3);
        const superscript = result.testSuperscript || '';
        tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${pValueStr}${superscript}</td>`;
      } else {
        tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">N/A</td>`;
      }

      tableHTML += `</tr>`;

      // Add categorical sub-rows if applicable
      if (result.rowDataType === 'categorical' && result.categories && result.descriptives.Overall.frequencies) {
        result.categories.forEach(rowCategory => {
          tableHTML += `
            <tr>
              <td style="border: 1px solid #ddd; padding: 12px; padding-left: 32px;">${rowCategory}</td>
          `;

          // Data cells for each group
          groupingCategories.forEach(groupCategory => {
            const groupStats = result.descriptives[groupCategory];
            if (groupStats && groupStats.frequencies && groupStats.frequencies[rowCategory] !== undefined) {
              const freq = groupStats.frequencies[rowCategory];
              const percentage = groupStats.columnPercentages && groupStats.columnPercentages[rowCategory]
                ? groupStats.columnPercentages[rowCategory].toFixed(1)
                : 'N/A';
              tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${freq} (${percentage}%)</td>`;
            } else {
              tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">N/A</td>`;
            }
          });

          // Total column for this category
          const overallStats = result.descriptives.Overall;
          if (overallStats && overallStats.frequencies && overallStats.frequencies[rowCategory] !== undefined) {
            const freq = overallStats.frequencies[rowCategory];
            const percentage = overallStats.percentages && overallStats.percentages[rowCategory]
              ? overallStats.percentages[rowCategory].toFixed(1)
              : 'N/A';
            tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${freq} (${percentage}%)</td>`;
          } else {
            tableHTML += `<td style="border: 1px solid #ddd; padding: 12px; text-align: center;">N/A</td>`;
          }

          // Empty p-value cell for sub-rows
          tableHTML += `<td style="border: 1px solid #ddd; padding: 12px;"></td>`;
          tableHTML += `</tr>`;
        });
      }
    });

    tableHTML += `
        </tbody>
      </table>
    `;

    return tableHTML;
  };

  // Helper function to create HTML table for SMD results
  const createHTMLSMDTable = (results: any[]) => {
    if (!results || !Array.isArray(results)) {
      return '<p>No SMD data available</p>';
    }

    let tableHTML = `
      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <thead>
          <tr>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left; background-color: #f2f2f2; font-weight: bold;">Variable</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">SMD (Cohen's d)</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">95% CI</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">Remarks/Interpretation</th>
          </tr>
        </thead>
        <tbody>
    `;

    results.forEach(result => {
      const ciText = (result.ciLower !== undefined && result.ciUpper !== undefined &&
                     !isNaN(result.ciLower) && !isNaN(result.ciUpper))
        ? `[${result.ciLower.toFixed(3)}, ${result.ciUpper.toFixed(3)}]`
        : 'N/A';

      tableHTML += `
        <tr>
          <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">${result.variableName || 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.smd && !isNaN(result.smd) ? result.smd.toFixed(3) : 'N/A'}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${ciText}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${result.remarks || 'N/A'}</td>
        </tr>
      `;
    });

    tableHTML += `
        </tbody>
      </table>
    `;
    return tableHTML;
  };

  // Helper function to create HTML table for Regression results
  const createHTMLRegressionTable = (results: any, regressionType: string) => {
    if (!results || typeof results !== 'object') {
      return '<p>No regression data available</p>';
    }

    const regType = regressionType || 'linear';

    let tableHTML = `
      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <thead>
          <tr>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left; background-color: #f2f2f2; font-weight: bold;">Variable</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">B</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">SE</th>
    `;

    // Add regression-specific columns (reordered to match UI: OR/HR before CI for logistic/cox)
    if (regType === 'linear') {
      tableHTML += `
        <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">95% CI</th>
        <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">β</th>
        <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">t</th>
      `;
    } else if (regType === 'logistic') {
      tableHTML += `
        <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">OR</th>
        <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">95% CI</th>
      `;
    } else if (regType === 'cox') {
      tableHTML += `
        <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">HR</th>
        <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">95% CI</th>
      `;
    }

    tableHTML += `
            <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">p</th>
          </tr>
        </thead>
        <tbody>
    `;

    // Add intercept row (not for Cox)
    if (regType !== 'cox' && results.intercept !== undefined) {
      const interceptCI = results.interceptStdError
        ? `[${(results.intercept - 1.96 * results.interceptStdError).toFixed(2)}, ${(results.intercept + 1.96 * results.interceptStdError).toFixed(2)}]`
        : 'N/A';

      const interceptT = results.interceptStdError
        ? (results.intercept / results.interceptStdError).toFixed(2)
        : 'N/A';

      const interceptP = results.interceptPValue !== undefined
        ? (results.interceptPValue < 0.001 ? '< .001' : results.interceptPValue.toFixed(3))
        : 'N/A';

      tableHTML += `
        <tr>
          <td style="border: 1px solid #ddd; padding: 12px;">(Intercept)</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${results.intercept.toFixed(2)}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${results.interceptStdError ? results.interceptStdError.toFixed(2) : 'N/A'}</td>
      `;

      if (regType === 'linear') {
        tableHTML += `
          <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${interceptCI}</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">—</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${interceptT}</td>
        `;
      } else if (regType === 'logistic') {
        tableHTML += `
          <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">—</td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${interceptCI}</td>
        `;
      }

      tableHTML += `
          <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${interceptP}</td>
        </tr>
      `;
    }

    // Add coefficient rows
    if (results.coefficients && typeof results.coefficients === 'object') {
      Object.entries(results.coefficients).forEach(([variableName, coefficient]: [string, any]) => {
        const displayName = variableName.includes('_')
          ? variableName.replace('_', ' = ')
          : variableName;

        const stdError = results.std_errors?.[variableName] || results.stdErrors?.[variableName];
        const pValue = results.p_values?.[variableName];
        const ci = results.confidence_intervals?.[variableName];
        const hazardRatio = results.hazard_ratios?.[variableName];

        const ciText = ci && Array.isArray(ci) && ci.length >= 2
          ? `[${ci[0].toFixed(2)}, ${ci[1].toFixed(2)}]`
          : 'N/A';

        const pText = pValue !== undefined
          ? (pValue < 0.001 ? '< .001' : pValue.toFixed(3))
          : 'N/A';

        const tValue = (coefficient && stdError)
          ? (coefficient / stdError).toFixed(2)
          : 'N/A';

        tableHTML += `
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px;">${displayName}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${coefficient ? coefficient.toFixed(2) : 'N/A'}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${stdError ? stdError.toFixed(2) : 'N/A'}</td>
        `;

        if (regType === 'linear') {
          tableHTML += `
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${ciText}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">—</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${tValue}</td>
          `;
        } else if (regType === 'logistic' || regType === 'cox') {
          tableHTML += `
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${hazardRatio ? hazardRatio.toFixed(2) : 'N/A'}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${ciText}</td>
          `;
        }

        tableHTML += `
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${pText}</td>
          </tr>
        `;
      });
    }

    tableHTML += `
        </tbody>
      </table>
    `;

    // Add Model Fit Statistics
    tableHTML += `<h4>Model Fit Statistics</h4>`;

    if (regType === 'linear' && results.rSquared !== undefined) {
      const fStat = results.fStatistic ? results.fStatistic.toFixed(2) : 'N/A';
      const dfModel = results.dfModel || 'N/A';
      const dfError = results.dfError || 'N/A';
      const pValue = results.pValue !== undefined
        ? (results.pValue < 0.001 ? '< .001' : results.pValue.toFixed(3))
        : 'N/A';
      const rSquared = results.rSquared.toFixed(2);

      tableHTML += `
        <p style="margin: 10px 0;">
          F(${dfModel}, ${dfError}) = ${fStat}, p ${pValue === 'N/A' ? '= N/A' : (pValue === '< .001' ? '< .001' : `= ${pValue}`)}, R² = ${rSquared}
        </p>
      `;
    } else if (regType === 'logistic' && results.pseudoRSquared !== undefined) {
      const logLikelihood = results.logLikelihood ? results.logLikelihood.toFixed(2) : 'N/A';
      const aic = results.aic ? results.aic.toFixed(2) : 'N/A';
      const pseudoR2 = results.pseudoRSquared.toFixed(2);
      const accuracy = results.accuracy ? results.accuracy.toFixed(2) : 'N/A';
      const precision = results.precision ? results.precision.toFixed(2) : 'N/A';
      const recall = results.recall ? results.recall.toFixed(2) : 'N/A';
      const f1Score = results.f1Score ? results.f1Score.toFixed(2) : 'N/A';
      const auc = results.auc ? results.auc.toFixed(2) : 'N/A';

      tableHTML += `
        <p style="margin: 10px 0;">
          Log-Likelihood: ${logLikelihood}, AIC: ${aic}, Pseudo R²: ${pseudoR2}
        </p>
        <p style="margin: 10px 0;">
          Accuracy: ${accuracy}, Precision: ${precision}, Recall: ${recall}, F1-Score: ${f1Score}, AUC: ${auc}
        </p>
      `;
    } else if (regType === 'cox' && results.concordance !== undefined) {
      const logLikelihood = results.log_likelihood ? results.log_likelihood.toFixed(2) : 'N/A';
      const aic = results.aic ? results.aic.toFixed(2) : 'N/A';
      const concordance = results.concordance.toFixed(2);
      const nObs = results.n_observations || 'N/A';
      const nEvents = results.n_events || 'N/A';

      tableHTML += `
        <p style="margin: 10px 0;">
          Log-Likelihood: ${logLikelihood}, AIC: ${aic}, Concordance Index: ${concordance}
        </p>
        <p style="margin: 10px 0;">
          N Observations: ${nObs}, N Events: ${nEvents}
        </p>
      `;
    }

    // Add model configuration information
    if ((results.baseCategories && Object.keys(results.baseCategories).length > 0) ||
        results.positiveCategory ||
        results.eventCategory) {
      tableHTML += `<h4>Model Configuration</h4>`;

      if (results.positiveCategory) {
        tableHTML += `<p style="margin: 10px 0; font-style: italic; color: #666;">Positive outcome: ${results.positiveCategory}</p>`;
      }

      if (results.eventCategory) {
        tableHTML += `<p style="margin: 10px 0; font-style: italic; color: #666;">Event category: ${results.eventCategory}</p>`;
      }

      if (results.baseCategories && Object.keys(results.baseCategories).length > 0) {
        const baseCategories = Object.entries(results.baseCategories)
          .map(([variable, category]) => `${variable}: ${category}`)
          .join(', ');
        tableHTML += `<p style="margin: 10px 0; font-style: italic; color: #666;">Base categories: ${baseCategories}</p>`;
      }
    }

    return tableHTML;
  };

  // Helper function to create HTML table for PostHoc results
  const createHTMLPostHocTable = (results: any) => {
    if (!results || typeof results !== 'object') {
      return '<p>No post-hoc test data available</p>';
    }

    let tableHTML = '';

    // Group Statistics Table
    if (results.groupStats) {
      tableHTML += `<h4>Group Statistics</h4>`;
      tableHTML += `
        <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
          <thead>
            <tr>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: left; background-color: #f2f2f2; font-weight: bold;">Group</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">N</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">Mean</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">Std Dev</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">95% CI</th>
            </tr>
          </thead>
          <tbody>
      `;

      Object.entries(results.groupStats).forEach(([group, stats]: [string, any]) => {
        const ciText = (stats.ci_lower !== undefined && stats.ci_upper !== undefined)
          ? `[${stats.ci_lower.toFixed(3)}, ${stats.ci_upper.toFixed(3)}]`
          : 'N/A';

        tableHTML += `
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: 500;">${group}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${stats.n || 'N/A'}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${stats.mean ? stats.mean.toFixed(3) : 'N/A'}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${stats.std ? stats.std.toFixed(3) : 'N/A'}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${ciText}</td>
          </tr>
        `;
      });

      tableHTML += `
          </tbody>
        </table>
      `;
    }

    // Overall ANOVA Results
    if (results.overallTest) {
      tableHTML += `<h4>Overall ANOVA Results</h4>`;
      tableHTML += `
        <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
          <thead>
            <tr>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: left; background-color: #f2f2f2; font-weight: bold;">Statistic</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">Value</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="border: 1px solid #ddd; padding: 12px;">F-statistic</td>
              <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${results.overallTest.fStatistic ? results.overallTest.fStatistic.toFixed(3) : 'N/A'}</td>
            </tr>
            <tr>
              <td style="border: 1px solid #ddd; padding: 12px;">P-value</td>
              <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${results.overallTest.pValue ? results.overallTest.pValue.toFixed(3) : 'N/A'}</td>
            </tr>
            <tr>
              <td style="border: 1px solid #ddd; padding: 12px;">Significant</td>
              <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${results.overallTest.significant ? 'Yes' : 'No'}</td>
            </tr>
          </tbody>
        </table>
      `;
    }

    // Pairwise Comparisons Table
    if (results.pairwiseComparisons && Array.isArray(results.pairwiseComparisons)) {
      tableHTML += `<h4>Pairwise Comparisons</h4>`;
      tableHTML += `
        <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
          <thead>
            <tr>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: left; background-color: #f2f2f2; font-weight: bold;">Comparison</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">Mean Difference</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">Adjusted p-value</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f2f2f2; font-weight: bold;">Significant</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">Effect Size</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: right; background-color: #f2f2f2; font-weight: bold;">95% CI</th>
            </tr>
          </thead>
          <tbody>
      `;

      results.pairwiseComparisons.forEach((comp: any) => {
        const ciText = (comp.ciLower !== undefined && comp.ciUpper !== undefined)
          ? `[${comp.ciLower.toFixed(3)}, ${comp.ciUpper.toFixed(3)}]`
          : 'N/A';

        const significantStyle = comp.significant
          ? 'background-color: rgba(76, 175, 80, 0.1);'
          : '';

        tableHTML += `
          <tr style="${significantStyle}">
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: 500;">${comp.group1} vs ${comp.group2}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${comp.meanDiff ? comp.meanDiff.toFixed(3) : (comp.meanDifference ? comp.meanDifference.toFixed(3) : 'N/A')}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${comp.adjustedPValue ? comp.adjustedPValue.toFixed(3) : (comp.pValue ? comp.pValue.toFixed(3) : 'N/A')}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">${comp.significant ? 'Yes' : 'No'}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${comp.effectSize ? comp.effectSize.toFixed(3) : 'N/A'}</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">${ciText}</td>
          </tr>
        `;
      });

      tableHTML += `
          </tbody>
        </table>
      `;
    }

    return tableHTML;
  };

  // Helper function to create HTML for Flow Diagram
  const createHTMLFlowDiagram = (stages: any[], diagramDirection: string, mermaidSyntax?: string) => {
    if (!stages || !Array.isArray(stages)) {
      return '<p>No flow diagram data available</p>';
    }

    let htmlContent = `
      <div style="margin: 20px 0;">
        <p><strong>Diagram Direction:</strong> ${diagramDirection || 'Top to Bottom'}</p>
    `;

    // Create visual flow diagram representation
    htmlContent += createFlowDiagramFallback(stages);

    // Add Mermaid syntax for reference
    if (mermaidSyntax) {
      htmlContent += `
        <h4>Mermaid Syntax</h4>
        <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #ddd; font-family: 'Courier New', monospace; font-size: 12px;">
          <code>${mermaidSyntax}</code>
        </pre>
      `;
    }

    htmlContent += `</div>`;
    return htmlContent;
  };

  // Fallback function for Flow Diagram when SVG rendering fails
  const createFlowDiagramFallback = (stages: any[]) => {
    let fallbackContent = `
      <div style="border: 1px solid #ddd; border-radius: 8px; padding: 20px; background-color: #fafafa; margin: 20px 0;">
        <h4>Flow Chart Stages</h4>
        <div style="display: flex; flex-direction: column; gap: 15px;">
    `;

    stages.forEach((stage, index) => {
      const isLastStage = index === stages.length - 1;
      const stageColor = stage.color || '#4CAF50';

      fallbackContent += `
        <div style="display: flex; align-items: center; gap: 15px;">
          <div style="
            background-color: ${stageColor};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            min-width: 200px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          ">
            ${stage.label || `Stage ${index + 1}`}
            ${stage.count !== undefined ? `<br><span style="font-size: 14px; font-weight: normal;">n = ${stage.count}</span>` : ''}
          </div>
          ${stage.description ? `
            <div style="
              background-color: white;
              padding: 10px 15px;
              border-radius: 5px;
              border-left: 4px solid ${stageColor};
              flex-grow: 1;
              font-style: italic;
              color: #666;
            ">
              ${stage.description}
            </div>
          ` : ''}
        </div>
        ${!isLastStage ? `
          <div style="text-align: center; color: #666; font-size: 18px;">
            ↓
          </div>
        ` : ''}
      `;
    });

    fallbackContent += `
        </div>
      </div>
    `;

    return fallbackContent;
  };

  // Manual sync function for user-triggered synchronization
  const manualSyncProject = async (projectId: string): Promise<void> => {
    const project = projects.find(p => p.id === projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    if (project.isLocal) {
      throw new Error('Cannot sync local projects');
    }

    setSyncStatus(prev => ({ ...prev, [projectId]: 'syncing' }));
    setSyncErrors(prev => ({ ...prev, [projectId]: '' }));

    try {
      await saveProjectToCloud(projectId);
      setSyncStatus(prev => ({ ...prev, [projectId]: 'success' }));
      // Clear success status after 3 seconds
      setTimeout(() => {
        setSyncStatus(prev => ({ ...prev, [projectId]: '' }));
      }, 3000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to sync project';
      setSyncStatus(prev => ({ ...prev, [projectId]: 'error' }));
      setSyncErrors(prev => ({ ...prev, [projectId]: errorMessage }));
      throw error; // Re-throw for UI handling
    }
  };

  // Context value
  const contextValue: ResultsContextType = {
    results,
    projects,
    currentProjectId,
    addResult,
    updateResult,
    removeResult,
    clearResults,
    toggleResultSelection,
    selectAllResults,
    deselectAllResults,
    getSelectedResults,
    exportToHTML,
    reorderResults,
    // Project management methods
    createProject,
    deleteProject: deleteProjectLocal,
    setCurrentProject,
    getProjectResults,
    moveResultToProject,
    saveProjectToCloud,
    loadProjectFromCloud,
    listCloudProjects,
    syncProjects,
    // Sync status and error handling
    syncStatus,
    syncErrors,
    manualSyncProject
  };

  return (
    <ResultsContext.Provider value={contextValue}>
      {children}
    </ResultsContext.Provider>
  );
};

// Custom hook to use the results context
export const useResults = () => {
  const context = useContext(ResultsContext);
  if (context === undefined) {
    throw new Error('useResults must be used within a ResultsProvider');
  }
  return context;
};