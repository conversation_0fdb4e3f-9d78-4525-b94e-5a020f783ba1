import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { ThemeProvider as MuiThemeProvider, createTheme, useMediaQuery, CssBaseline } from '@mui/material';
import { lightThemeOptions, darkThemeOptions } from '../theme';

type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeContextType {
  themeMode: ThemeMode;
  setThemeMode: (mode: ThemeMode) => void;
  appliedTheme: 'light' | 'dark'; // The actual theme being applied
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const AppThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [themeMode, setThemeModeState] = useState<ThemeMode>(() => {
    const storedPreference = localStorage.getItem('themePreference') as ThemeMode | null;
    return storedPreference || 'system';
  });

  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');

  const appliedTheme = useMemo(() => {
    if (themeMode === 'system') {
      return prefersDarkMode ? 'dark' : 'light';
    }
    return themeMode;
  }, [themeMode, prefersDarkMode]);

  const setThemeMode = (mode: ThemeMode) => {
    localStorage.setItem('themePreference', mode);
    setThemeModeState(mode);
  };

  // Create the MUI theme object based on the applied theme
  const muiTheme = useMemo(() => {
    return createTheme(appliedTheme === 'dark' ? darkThemeOptions : lightThemeOptions);
  }, [appliedTheme]);

  const contextValue = useMemo(() => ({
    themeMode,
    setThemeMode,
    appliedTheme
  }), [themeMode, setThemeMode, appliedTheme]);

  return (
    <ThemeContext.Provider value={contextValue}>
      <MuiThemeProvider theme={muiTheme}>
        <CssBaseline /> {/* Add CssBaseline here */}
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  );
};

export const useAppTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useAppTheme must be used within an AppThemeProvider');
  }
  return context;
};
