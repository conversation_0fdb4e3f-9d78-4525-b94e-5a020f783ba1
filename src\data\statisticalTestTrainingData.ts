export const trainingData = [
  // Independent Samples t-test
  { query: "compare means of two independent groups", test: "Independent Samples t-test" },
  { query: "difference in test scores between male and female students", test: "Independent Samples t-test" },
  { query: "compare average height of two distinct populations", test: "Independent Samples t-test" },
  { query: "is there a significant difference between two unrelated samples", test: "Independent Samples t-test" },
  { query: "test if two groups are statistically different on a continuous variable", test: "Independent Samples t-test" },
  { query: "compare the average income of two different cities", test: "Independent Samples t-test" },
  { query: "compare group a and group b on a numerical outcome", test: "Independent Samples t-test" },
  { query: "is there a difference in means for two separate samples", test: "Independent Samples t-test" },
  { query: "compare two independent groups", test: "Independent Samples t-test" },
  { query: "difference between two groups", test: "Independent Samples t-test" },
  { query: "group a vs group b", test: "Independent Samples t-test" },
  { query: "control experimental two groups", test: "Independent Samples t-test" },

  // Linear Regression
  { query: "is there a relationship between hours studied and exam performance", test: "Linear Regression" },
  { query: "predict house prices based on square footage", test: "Linear Regression" },
  { query: "effect of advertising spend on sales", test: "Linear Regression" },
  { query: "model the relationship between a dependent variable and one or more independent variables", test: "Linear Regression" },
  { query: "forecast future sales based on past marketing efforts", test: "Linear Regression" },
  { query: "predict a continuous variable", test: "Linear Regression" },
  { query: "predict a number", test: "Linear Regression" },
  { query: "simple linear regression", test: "Linear Regression" },
  { query: "multiple linear regression", test: "Linear Regression" },
  { query: "predict blood pressure", test: "Linear Regression" },

  // Paired Samples t-test
  { query: "pre and post treatment measurements for the same group", test: "Paired Samples t-test" },
  { query: "compare blood pressure before and after medication in the same patients", test: "Paired Samples t-test" },
  { query: "evaluate the impact of a training program on employee productivity", test: "Paired Samples t-test" },
  { query: "test for a difference between two related measurements", test: "Paired Samples t-test" },
  { query: "assess the change in a variable over time for the same subjects", test: "Paired Samples t-test" },
  { query: "pre-post analysis", test: "Paired Samples t-test" },
  { query: "related groups comparison", test: "Paired Samples t-test" },
  { query: "before after study", test: "Paired Samples t-test" },

  // One-Way ANOVA
  { query: "compare average across three different plant species", test: "One-Way ANOVA" },
  { query: "difference in customer satisfaction across multiple product versions", test: "One-Way ANOVA" },
  { query: "impact of different teaching methods on student engagement", test: "One-Way ANOVA" },
  { query: "test for differences among means of three or more independent groups", test: "One-Way ANOVA" },
  { query: "determine if there's a significant effect of a single categorical factor on a continuous outcome", test: "One-Way ANOVA" },
  { query: "compare three groups", test: "One-Way ANOVA" },
  { query: "compare multiple groups", test: "One-Way ANOVA" },
  { query: "anova for independent groups", test: "One-Way ANOVA" },
  { query: "compare over two groups", test: "One-Way ANOVA" },
  { query: "multiple group comparison anova", test: "One-Way ANOVA" },
  { query: "comparing more than two groups", test: "One-Way ANOVA" },
  { query: "anova test", test: "One-Way ANOVA" },
  { query: "3 groups", test: "One-Way ANOVA" },
  { query: "4 groups", test: "One-Way ANOVA" },
  { query: "multiple independent groups", test: "One-Way ANOVA" },
  { query: "anova for multiple groups", test: "One-Way ANOVA" },

  // Logistic Regression
  { query: "determine if a categorical variable influences a binary outcome", test: "Logistic Regression" },
  { query: "predict customer churn based on demographic data", test: "Logistic Regression" },
  { query: "likelihood of loan default given credit score", test: "Logistic Regression" },
  { query: "model the probability of a binary event occurring", test: "Logistic Regression" },
  { query: "classify emails as spam or not spam based on content", test: "Logistic Regression" },
  { query: "predict a binary outcome", test: "Logistic Regression" },
  { query: "predict disease status", test: "Logistic Regression" },
  { query: "predict categorical variable", test: "Logistic Regression" },
  { query: "predict a category", test: "Logistic Regression" },

  // Correlation Analysis
  { query: "relationship between two continuous variables", test: "Correlation Analysis" },
  { query: "strength and direction of association between two variables", test: "Correlation Analysis" },
  { query: "explore the link between exercise and cholesterol levels", test: "Correlation Analysis" },
  { query: "measure how two quantitative variables move together", test: "Correlation Analysis" },
  { query: "investigate the association between temperature and ice cream sales", test: "Correlation Analysis" },
  { query: "correlation between two numerical variables", test: "Correlation Analysis" },
  { query: "how related are two variables", test: "Correlation Analysis" },
  { query: "pearson correlation", test: "Correlation Analysis" },
  { query: "spearman correlation", test: "Correlation Analysis" },

  // One-Sample t-test
  { query: "compare a sample mean to a known population mean", test: "One-Sample t-test" },
  { query: "is the average weight of a new product batch different from the standard", test: "One-Sample t-test" },
  { query: "test if a drug's effect differs from a placebo's known effect", test: "One-Sample t-test" },
  { query: "determine if a sample mean is significantly different from a hypothesized population mean", test: "One-Sample t-test" },

  // Kruskal-Wallis Test
  { query: "compare more than two independent groups, non-parametric", test: "Kruskal-Wallis Test" },
  { query: "difference in rankings among several independent samples", test: "Kruskal-Wallis Test" },
  { query: "non-parametric alternative to one-way ANOVA", test: "Kruskal-Wallis Test" },
  { query: "test if there are significant differences in medians across multiple independent groups", test: "Kruskal-Wallis Test" },
  { query: "compare three or more groups non-normal data", test: "Kruskal-Wallis Test" },

  // Mann-Whitney U Test
  { query: "compare two independent groups, non-parametric", test: "Mann-Whitney U Test" },
  { query: "difference in distributions between two independent samples", test: "Mann-Whitney U Test" },
  { query: "non-parametric alternative to independent samples t-test", test: "Mann-Whitney U Test" },
  { query: "test if two independent samples come from the same distribution", test: "Mann-Whitney U Test" },
  { query: "compare two groups non-normal data", test: "Mann-Whitney U Test" },

  // Wilcoxon Signed-Rank Test
  { query: "compare two related samples, non-parametric", test: "Wilcoxon Signed-Rank Test" },
  { query: "difference in paired observations, non-parametric", test: "Wilcoxon Signed-Rank Test" },
  { query: "non-parametric alternative to paired samples t-test", test: "Wilcoxon Signed-Rank Test" },
  { query: "test for differences between two related samples when data is not normally distributed", test: "Wilcoxon Signed-Rank Test" },
  { query: "pre-post non-normal data", test: "Wilcoxon Signed-Rank Test" },

  // Two-Way ANOVA
  { query: "analyze the effect of two categorical independent variables on a continuous dependent variable", test: "Two-Way ANOVA" },
  { query: "investigate interaction effects between two factors", test: "Two-Way ANOVA" },
  { query: "compare means across groups defined by two factors", test: "Two-Way ANOVA" },
  { query: "examine the main effects and interaction effects of two independent variables", test: "Two-Way ANOVA" },
  { query: "two factors continuous outcome", test: "Two-Way ANOVA" },
  { query: "interaction effects in two-way ANOVA", test: "Two-Way ANOVA" },

  // Repeated Measures ANOVA
  { query: "compare means of three or more related groups", test: "Repeated Measures ANOVA" },
  { query: "effect of time on a dependent variable within the same subjects", test: "Repeated Measures ANOVA" },
  { query: "assess changes in performance over multiple trials", test: "Repeated Measures ANOVA" },
  { query: "test for differences in means across multiple time points for the same subjects", test: "Repeated Measures ANOVA" },
  { query: "analyze repeated measurements over time", test: "Repeated Measures ANOVA" },

  // Friedman Test
  { query: "test for differences among three or more related samples, non-parametric", test: "Friedman Test" },
  { query: "compare multiple treatments applied to the same subjects, non-parametric", test: "Friedman Test" },
  { query: "non-parametric alternative to repeated measures ANOVA", test: "Friedman Test" },
  { query: "assess differences among repeated measurements on the same subjects when data is not normal", test: "Friedman Test" },

  // Descriptive Statistics
  { query: "summarize central tendency and spread of data", test: "Descriptive Statistics" },
  { query: "calculate mean, median, mode, standard deviation", test: "Descriptive Statistics" },
  { query: "understand the distribution of a single variable", test: "Descriptive Statistics" },
  { query: "get summary statistics for a dataset", test: "Descriptive Statistics" },
  { query: "explore data patterns", test: "Descriptive Statistics" },
  { query: "data summary", test: "Descriptive Statistics" },

  // Normality Test
  { query: "check if data follows a normal distribution", test: "Normality Test" },
  { query: "assess the gaussian nature of a dataset", test: "Normality Test" },
  { query: "determine if a dataset is normally distributed", test: "Normality Test" },
  { query: "is my data normal", test: "Normality Test" },
  { query: "test normality", test: "Normality Test" },

  // Histogram
  { query: "visualize data distribution", test: "Histogram" },
  { query: "show frequency of data points in bins", test: "Histogram" },
  { query: "display the shape of a continuous variable's distribution", test: "Histogram" },

  // Bar Chart
  { query: "display categories and their counts", test: "Bar Chart" },
  { query: "compare values across different categories", test: "Bar Chart" },
  { query: "show the frequency of categorical data", test: "Bar Chart" },
  { query: "visualize categorical frequency", test: "Bar Chart" },
  { query: "clustered bar chart", test: "Bar Chart" },
  { query: "grouped bar chart", test: "Bar Chart" },

  // Pie Chart
  { query: "show parts of a whole", test: "Pie Chart" },
  { query: "represent proportions of a total", test: "Pie Chart" },
  { query: "visualize the composition of a single categorical variable", test: "Pie Chart" },
  { query: "visualize categorical proportions", test: "Pie Chart" },

  // Scatter Plot
  { query: "display relationship between two continuous variables", test: "Scatter Plot" },
  { query: "identify trends or clusters in bivariate data", test: "Scatter Plot" },
  { query: "show the relationship between two quantitative variables", test: "Scatter Plot" },
  { query: "visualize linear relationship with trendline", test: "Scatter Plot" },
  { query: "faceted scatter plot", test: "Scatter Plot" },

  // Box Plot
  { query: "show distribution and outliers for a single variable", test: "Box Plot" },
  { query: "compare distributions across different groups", test: "Box Plot" },
  { query: "visualize the five-number summary of a dataset", test: "Box Plot" },
  { query: "visually compare group variances", test: "Box Plot" },
  { query: "stratified box plot", test: "Box Plot" },

  // Sankey Diagram
  { query: "visualize flow between categories", test: "Sankey Diagram" },
  { query: "show transitions between states", test: "Sankey Diagram" },
  { query: "flow diagram", test: "Sankey Diagram" },
  { query: "pathway analysis", test: "Sankey Diagram" },
  { query: "movement between groups", test: "Sankey Diagram" },
  { query: "flow relationships", test: "Sankey Diagram" },
  { query: "categorical flow", test: "Sankey Diagram" },
  { query: "transition diagram", test: "Sankey Diagram" },
  { query: "show how data flows from one category to another", test: "Sankey Diagram" },
  { query: "visualize categorical relationships", test: "Sankey Diagram" },
  { query: "multi-level flow visualization", test: "Sankey Diagram" },
  { query: "multi-stage pathway analysis", test: "Sankey Diagram" },
  { query: "career progression visualization", test: "Sankey Diagram" },
  { query: "customer journey mapping", test: "Sankey Diagram" },
  { query: "academic pathway analysis", test: "Sankey Diagram" },
  { query: "employee progression tracking", test: "Sankey Diagram" },
  { query: "student academic journey", test: "Sankey Diagram" },
  { query: "treatment pathway visualization", test: "Sankey Diagram" },
  { query: "process flow analysis", test: "Sankey Diagram" },
  { query: "sequential categorical analysis", test: "Sankey Diagram" },

  // Cross-Tabulation
  { query: "examine relationships between two categorical variables", test: "Cross-Tabulation" },
  { query: "create a contingency table", test: "Cross-Tabulation" },
  { query: "analyze the relationship between two nominal variables", test: "Cross-Tabulation" },
  { query: "cross tabulation of categorical data", test: "Cross-Tabulation" },

  // Frequency Tables
  { query: "count occurrences of each category in a variable", test: "Frequency Tables" },
  { query: "summarize qualitative data", test: "Frequency Tables" },
  { query: "get counts and percentages for categorical variables", test: "Frequency Tables" },

  // Additional queries from Analysis Assistant
  { query: "test for equal variances", test: "Normality Test" }, // Levene's Test is an assumption test
  { query: "identify outliers", test: "Descriptive Statistics" }, // IQR Method, Z-Score Analysis
  { query: "perform post-hoc tests after ANOVA", test: "One-Way ANOVA" }, // Post-hoc tests are part of ANOVA workflow
  { query: "test association between two categorical variables", test: "Cross-Tabulation" }, // Chi-Square is a test, but Cross-Tabulation is the descriptive part
  { query: "measure strength of association for categorical variables", test: "Cross-Tabulation" }, // Cramer's V is related to Chi-Square/Cross-Tabulation
  { query: "validate regression model assumptions with Residual Plots", test: "Linear Regression" }, // Residual Plots are for regression diagnostics
  { query: "show relationships stratified by a third variable", test: "Bar Chart" }, // Grouped Bar Chart
  { query: "show distributions stratified by a third variable", test: "Box Plot" }, // Stratified Box Plot
  { query: "show relationships stratified by a third variable", test: "Scatter Plot" }, // Faceted Scatter Plot
  { query: "visualize correlations between multiple variables", test: "Correlation Analysis" }, // Correlation Matrix, Heatmap
  { query: "summarize and analyze data with Pivot Tables", test: "Descriptive Statistics" }, // Pivot Analysis
  { query: "compare population mean non-parametric", test: "Wilcoxon Signed-Rank Test" },
  { query: "compare one group to a median value", test: "Wilcoxon Signed-Rank Test" },
  { query: "compare two related groups non-parametric", test: "Wilcoxon Signed-Rank Test" },
  { query: "compare two independent groups non-parametric", test: "Mann-Whitney U Test" },
  { query: "compare three or more related groups non-parametric", test: "Friedman Test" },
  { query: "analyze effects and interaction of two categorical variables", test: "Two-Way ANOVA" },
  { query: "test for normal distribution", test: "Normality Test" },
  { query: "check for linearity and homoscedasticity", test: "Linear Regression" },
  { query: "evaluate binary classification model performance", test: "Logistic Regression" },
  { query: "roc curve", test: "Logistic Regression" },
  { query: "data transformation", test: "Descriptive Statistics" }, // Assuming a general category for data prep
  { query: "grouped statistics", test: "Descriptive Statistics" },
  { query: "compare means between two groups", test: "Independent Samples t-test" },
  { query: "compare two groups", test: "Independent Samples t-test" },
  { query: "two independent groups", test: "Independent Samples t-test" },
  { query: "difference between two groups", test: "Independent Samples t-test" },
  { query: "group a vs group b", test: "Independent Samples t-test" },
  { query: "control experimental two", test: "Independent Samples t-test" },
  { query: "normal distribution", test: "Normality Test" },
  { query: "normally distributed", test: "Normality Test" },
  { query: "is my data normal", test: "Normality Test" },
  { query: "check normality", test: "Normality Test" },
  { query: "test normality", test: "Normality Test" },
  { query: "pre-post", test: "Paired Samples t-test" },
  { query: "pre post", test: "Paired Samples t-test" },
  { query: "related groups", test: "Paired Samples t-test" },
  { query: "paired measurements", test: "Paired Samples t-test" },
  { query: "before after", test: "Paired Samples t-test" },
  { query: "pre/post", test: "Paired Samples t-test" },
  { query: "compare three groups", test: "One-Way ANOVA" },
  { query: "compare multiple groups", test: "One-Way ANOVA" },
  { query: "anova", test: "One-Way ANOVA" },
  { query: "more than two groups", test: "One-Way ANOVA" },
  { query: "3 groups", test: "One-Way ANOVA" },
  { query: "4 groups", test: "One-Way ANOVA" },
  { query: "multiple independent groups", test: "One-Way ANOVA" },
  { query: "correlation", test: "Correlation Analysis" },
  { query: "relationship between two variables", test: "Correlation Analysis" },
  { query: "association numerical", test: "Correlation Analysis" },
  { query: "how related are", test: "Correlation Analysis" },
  { query: "association categorical", test: "Cross-Tabulation" },
  { query: "relationship categorical", test: "Cross-Tabulation" },
  { query: "chi-square", test: "Cross-Tabulation" },
  { query: "contingency table", test: "Cross-Tabulation" },
  { query: "what test should i use for categorical data", test: "Cross-Tabulation" },
  { query: "test for categorical data", test: "Cross-Tabulation" },
  { query: "categorical data test", test: "Cross-Tabulation" },
  { query: "test categorical", test: "Cross-Tabulation" },
  { query: "analyze categorical", test: "Cross-Tabulation" },
  { query: "contingency table analysis", test: "Cross-Tabulation" },
  { query: "compare over two groups", test: "One-Way ANOVA" },
  { query: "multiple group comparison anova", test: "One-Way ANOVA" },
  { query: "summarize my data", test: "Descriptive Statistics" },
  { query: "explore data patterns", test: "Descriptive Statistics" },
  { query: "pivot table", test: "Descriptive Statistics" },
  { query: "data summary", test: "Descriptive Statistics" },
  { query: "how do i predict blood pressure", test: "Linear Regression" },
  { query: "predict continuous variable", test: "Linear Regression" },
  { query: "predict a number", test: "Linear Regression" },
  { query: "linear regression", test: "Linear Regression" },
  { query: "how do i predict disease status", test: "Logistic Regression" },
  { query: "predict categorical variable", test: "Logistic Regression" },
  { query: "predict a category", test: "Logistic Regression" },
  { query: "logistic regression", test: "Logistic Regression" },
  { query: "predict binary outcome", test: "Logistic Regression" },
  { query: "what to do if my data is not normally distributed", test: "Normality Test" },
  { query: "data not normal", test: "Normality Test" },
  { query: "non-normal data", test: "Normality Test" },
  { query: "nonparametric tests", test: "Normality Test" },
  { query: "non-parametric", test: "Normality Test" },
  { query: "compare means between two groups", test: "Independent Samples t-test" },
  { query: "compare two groups", test: "Independent Samples t-test" },
  { query: "two independent groups", test: "Independent Samples t-test" },
  { query: "difference between two groups", test: "Independent Samples t-test" },
  { query: "group a vs group b", test: "Independent Samples t-test" },
  { query: "control experimental two", test: "Independent Samples t-test" },
  { query: "pre-post", test: "Paired Samples t-test" },
  { query: "pre post", test: "Paired Samples t-test" },
  { query: "related groups", test: "Paired Samples t-test" },
  { query: "paired measurements", test: "Paired Samples t-test" },
  { query: "before after", test: "Paired Samples t-test" },
  { query: "pre/post", test: "Paired Samples t-test" },
  { query: "compare three groups", test: "One-Way ANOVA" },
  { query: "compare multiple groups", test: "One-Way ANOVA" },
  { query: "anova", test: "One-Way ANOVA" },
  { query: "more than two groups", test: "One-Way ANOVA" },
  { query: "3 groups", test: "One-Way ANOVA" },
  { query: "4 groups", test: "One-Way ANOVA" },
  { query: "multiple independent groups", test: "One-Way ANOVA" },
  { query: "correlation", test: "Correlation Analysis" },
  { query: "relationship between two variables", test: "Correlation Analysis" },
  { query: "association numerical", test: "Correlation Analysis" },
  { query: "how related are", test: "Correlation Analysis" },
  { query: "association categorical", test: "Cross-Tabulation" },
  { query: "relationship categorical", test: "Cross-Tabulation" },
  { query: "chi-square", test: "Cross-Tabulation" },
  { query: "contingency table", test: "Cross-Tabulation" },
  { query: "what test should i use for categorical data", test: "Cross-Tabulation" },
  { query: "test for categorical data", test: "Cross-Tabulation" },
  { query: "categorical data test", test: "Cross-Tabulation" },
  { query: "test categorical", test: "Cross-Tabulation" },
  { query: "analyze categorical", test: "Cross-Tabulation" },
  { query: "contingency table analysis", test: "Cross-Tabulation" },
  { query: "compare over two groups", test: "One-Way ANOVA" },
  { query: "multiple group comparison anova", test: "One-Way ANOVA" },
  { query: "summarize my data", test: "Descriptive Statistics" },
  { query: "explore data patterns", test: "Descriptive Statistics" },
  { query: "pivot table", test: "Descriptive Statistics" },
  { query: "data summary", test: "Descriptive Statistics" },
  { query: "how do i predict blood pressure", test: "Linear Regression" },
  { query: "predict continuous variable", test: "Linear Regression" },
  { query: "predict a number", test: "Linear Regression" },
  { query: "linear regression", test: "Linear Regression" },
  { query: "how do i predict disease status", test: "Logistic Regression" },
  { query: "predict categorical variable", test: "Logistic Regression" },
  { query: "predict a category", test: "Logistic Regression" },
  { query: "logistic regression", test: "Logistic Regression" },
  { query: "predict binary outcome", test: "Logistic Regression" },
  { query: "what to do if my data is not normally distributed", test: "Normality Test" },
  { query: "data not normal", test: "Normality Test" },
  { query: "non-normal data", test: "Normality Test" },
  { query: "nonparametric tests", test: "Normality Test" },
  { query: "non-parametric", test: "Normality Test" },
  { query: "odds ratio", test: "Logistic Regression" },
  { query: "odds ratio", test: "Case-Control" },

  // Sample Size and Power Estimation
  { query: "how many subjects do I need for my study", test: "Sample Size" },
  { query: "calculate required sample size", test: "Sample Size" },
  { query: "determine sample size for an experiment", test: "Sample Size" },
  { query: "what is the power of my study", test: "Power Analysis" },
  { query: "calculate statistical power", test: "Power Analysis" },
  { query: "power estimation for t-test", test: "Power Analysis" },
  { query: "sample size calculator", test: "Sample Size Calculator" },

  // Epi Calculator
  { query: "epidemiological calculator", test: "Epi-Calculator" },
  { query: "2x2 table analysis", test: "Epi-Calculator" },
  { query: "calculate odds ratio from a 2x2 table", test: "Epi-Calculator" },
  { query: "calculate risk ratio from a 2x2 table", test: "Epi-Calculator" },

  // Study Designs
  { query: "study at a single point in time", test: "Cross-Sectional Study" },
  { query: "prevalence study", test: "Cross-Sectional Study" },
  { query: "what is a cross-sectional study", test: "Cross-Sectional Study" },
  { query: "follow a group over time", test: "Cohort Study" },
  { query: "prospective study design", test: "Cohort Study" },
  { query: "retrospective cohort study", test: "Cohort Study" },
  { query: "incidence study", test: "Cohort Study" },
  { query: "compare people with and without a disease", test: "Case-Control" },
  { query: "study to find risk factors for a condition", test: "Case-Control" },
  { query: "retrospective study design", test: "Case-Control" },
  { query: "matched case-control study analysis", test: "Matched Case-Control" },
  { query: "analysis for matched pairs", test: "Matched Case-Control" },
  { query: "conditional logistic regression", test: "Matched Case-Control" },

  // Measures of Association
  { query: "calculate odds ratio", test: "Odds Ratio" },
  { query: "what is odds ratio", test: "Odds Ratio" },
  { query: "interpret odds ratio", test: "Odds Ratio" },
  { query: "odds ratio for case-control study", test: "Odds Ratio" },
  { query: "calculate risk ratio", test: "Risk Ratio" },
  { query: "relative risk calculation", test: "Risk Ratio" },
  { query: "what is risk ratio", test: "Risk Ratio" },
  { query: "interpret risk ratio", test: "Risk Ratio" },
  { query: "risk ratio for cohort study", test: "Risk Ratio" },
  { query: "adjusted odds ratio", test: "Adjusted OR" },
  { query: "calculate adjusted OR", test: "Adjusted OR" },
  { query: "control for confounding variables odds ratio", test: "Adjusted OR" },
  { query: "adjusted risk ratio", test: "Adjusted RR" },
  { query: "calculate adjusted RR", test: "Adjusted RR" },
  { query: "control for confounding variables risk ratio", test: "Adjusted RR" },
  { query: "multivariable logistic regression for adjusted odds ratio", test: "Adjusted OR"}
];

export const statisticalTests = [
  "Independent Samples t-test",
  "Linear Regression",
  "Paired Samples t-test",
  "One-Way ANOVA",
  "Logistic Regression",
  "Correlation Analysis",
  "One-Sample t-test",
  "Kruskal-Wallis Test",
  "Mann-Whitney U Test",
  "Wilcoxon Signed-Rank Test",
  "Two-Way ANOVA",
  "Repeated Measures ANOVA",
  "Friedman Test",
  "Descriptive Statistics",
  "Normality Test",
  "Histogram",
  "Bar Chart",
  "Pie Chart",
  "Scatter Plot",
  "Box Plot",
  "Sankey Diagram",
  "Cross-Tabulation",
  "Frequency Tables",
  "Case-Control",
  "Cross-Sectional Study",
  "Longitudinal Study",
  "Cohort Study",
  "Cross-Sectional vs Longitudinal",
  "Longitudinal vs Cohort",
  "Sample Size",
  "Power Analysis",
  "Epi-Calculator",
  "Matched Case-Control",
  "Odds Ratio",
  "Risk Ratio",
  "Adjusted OR",
  "Adjusted RR",
  "Sample Size Calculator"
  ];
