# Statistical Methods Generator - User Guide

## Overview

The Statistical Methods Generator is a powerful tool that automatically creates publication-ready Statistical Methods sections based on your completed analyses in DataStatPro. It follows academic writing standards and includes appropriate statistical terminology.

## How to Use

### Step 1: Complete Your Analyses
Before using the Methods Generator, complete your statistical analyses using DataStatPro's analysis tools:
- Descriptive Statistics
- t-tests (Independent, Paired, One-sample)
- ANOVA (One-way, Two-way, Repeated Measures)
- Regression (Linear, Logistic, Cox)
- Correlation Analysis
- Non-parametric Tests
- Chi-square Tests
- And more...

### Step 2: Select Analyses
1. Navigate to Publication Ready → Statistical Methods Generator
2. Review the list of completed analyses in the left panel
3. Select the analyses you want to include in your methods section
4. Use "Select All" or "Clear" buttons for bulk selection

### Step 3: Review Generated Text
The tool automatically generates methods text that includes:
- Descriptive analysis approach
- Specific inferential tests used
- Statistical assumptions checking
- Significance criteria (p < 0.05)
- Software citation

### Step 4: Customize and Edit
1. Use the Edit button to modify the generated text
2. Choose your preferred output format:
   - **Paragraph**: Flowing narrative text (recommended for most journals)
   - **Structured**: Organized by analysis sections
   - **HTML**: Web-formatted markup for online publications

### Step 5: Save and Export
1. Save to Results Manager for future reference
2. Export in HTML format
3. Copy to clipboard for use in your manuscript

## Output Formats

### Paragraph Format
Generates flowing narrative text suitable for most academic journals:
```
Descriptive statistics were calculated for all study variables (N = 150). 
Continuous variables were summarized using means and standard deviations...
```

### Structured Format
Organizes content by analysis type:
```
Descriptive Analysis:
Descriptive statistics were calculated for all study variables...

Inferential Analysis:
Independent samples t-tests were conducted to compare means...
```

### HTML Format
Provides web-formatted markup:
```html
<div class="statistical-methods">
  <h3>Statistical Methods</h3>
  <p>Descriptive statistics were calculated...</p>
</div>
```

## Keyboard Shortcuts

- **Ctrl+A**: Select all analyses
- **Ctrl+D**: Deselect all analyses  
- **Ctrl+R**: Regenerate methods text
- **Ctrl+E**: Toggle edit/preview mode
- **Ctrl+C**: Copy text to clipboard

## Best Practices

### Analysis Selection
1. **Logical Order**: Select analyses in logical order (descriptive first, then inferential)
2. **Complete Sets**: Include related analyses together (e.g., ANOVA with post-hoc tests)
3. **Relevance**: Only include analyses that are relevant to your research questions

### Text Customization
1. **Study-Specific Details**: Add specific details about your study design
2. **Sample Characteristics**: Include relevant sample size and demographic information
3. **Assumption Violations**: Specify how assumption violations were handled
4. **Effect Sizes**: Mention effect size calculations if relevant

### Quality Checks
The tool provides automatic validation with:
- **Warnings**: Issues that should be addressed
- **Suggestions**: Recommendations for improvement
- **Success Indicators**: Confirmation when text meets standards

## Supported Analysis Types

### Descriptive Statistics
- Basic descriptive statistics
- Frequency distributions
- Cross-tabulations
- Normality assessments

### Inferential Tests
- Independent samples t-tests
- Paired samples t-tests
- One-sample t-tests
- One-way ANOVA
- Two-way ANOVA
- Repeated measures ANOVA
- Linear regression
- Logistic regression
- Cox regression
- Pearson/Spearman correlations

### Non-parametric Tests
- Mann-Whitney U tests
- Wilcoxon signed-rank tests
- Kruskal-Wallis tests
- Friedman tests
- Chi-square tests

### Advanced Analyses
- Meta-analysis
- Survival analysis
- Factor analysis
- Cluster analysis
- Reliability analysis

## Troubleshooting

### No Analyses Available
**Problem**: "No completed analyses found" message appears
**Solution**: Complete some statistical analyses first using DataStatPro's analysis tools

### Poor Quality Text
**Problem**: Generated text doesn't meet expectations
**Solutions**:
- Ensure analyses contain sufficient detail
- Select related analyses together
- Use the edit mode to customize text
- Check validation warnings and suggestions

### Missing Analysis Types
**Problem**: Specific analysis type not recognized
**Solution**: The tool is continuously updated. Contact support for new analysis type requests

## Academic Writing Standards

The generator follows these academic writing principles:

### Clarity and Precision
- Uses precise statistical terminology
- Avoids ambiguous language
- Specifies exact procedures used

### Completeness
- Includes all necessary methodological details
- Mentions assumption checking
- Provides software citation

### Consistency
- Maintains consistent terminology throughout
- Follows standard reporting guidelines
- Uses appropriate verb tenses

## Integration with Results Manager

### Saving Results
- Generated methods sections are saved as "other" type results
- Include metadata about selected analyses
- Preserve both original and customized text

### Export Options
- HTML export includes proper formatting
- Methods sections integrate with other results
- Suitable for comprehensive reports

## Tips for Different Journal Requirements

### High-Impact Journals
- Include detailed assumption checking
- Mention effect size calculations
- Provide comprehensive methodological details

### Specialized Journals
- Customize terminology for field-specific requirements
- Add discipline-specific methodological considerations
- Include relevant citations beyond software

### Conference Presentations
- Use structured format for clarity
- Focus on key analytical approaches
- Emphasize novel methodological aspects

## Getting Help

For additional support:
1. Review the validation warnings and suggestions
2. Check the keyboard shortcuts and tips section
3. Consult DataStatPro documentation
4. Contact <EMAIL> for specific questions

---

*This guide is part of DataStatPro's comprehensive statistical analysis platform. For more information, visit our documentation or contact support.*
