# Categorical Descriptives and Association: Comprehensive Reference Guide

This comprehensive guide covers descriptive statistics and association measures for categorical data, including frequency analysis, chi-square tests, measures of association, odds ratios, and specialized tests for categorical data analysis.

## Overview

Categorical data analysis involves examining the distribution and relationships between variables measured on nominal or ordinal scales. These methods are fundamental for understanding patterns, associations, and dependencies in categorical datasets.

## Frequency Tables and Cross-Tabulations

### 1. Frequency Tables

**Purpose:** Summarize the distribution of a single categorical variable.

**Components:**
- **Frequency (f):** Count of observations in each category
- **Relative Frequency:** $p_i = \frac{f_i}{n}$
- **Percentage:** $\text{Percentage}_i = p_i \times 100\%$
- **Cumulative Frequency:** Running total of frequencies

**Example Structure:**
| Category | Frequency | Relative Frequency | Percentage | Cumulative % |
|----------|-----------|-------------------|------------|--------------|
| A        | 25        | 0.25              | 25%        | 25%          |
| B        | 40        | 0.40              | 40%        | 65%          |
| C        | 35        | 0.35              | 35%        | 100%         |

### 2. Cross-Tabulation (Contingency Tables)

**Purpose:** Examine the relationship between two categorical variables.

**2×2 Contingency Table:**
|           | Variable B |           |         |
|-----------|------------|-----------|---------|
| Variable A| B₁         | B₂        | Total   |
| A₁        | a          | b         | a + b   |
| A₂        | c          | d         | c + d   |
| Total     | a + c      | b + d     | n       |

**Expected Frequencies:**
$$E_{ij} = \frac{(\text{Row Total}_i) \times (\text{Column Total}_j)}{n}$$

## Proportions and Percentages

### 1. Sample Proportion

**Formula:**
$$\hat{p} = \frac{x}{n}$$

Where:
- x = number of successes
- n = sample size

### 2. Confidence Interval for Proportion

**Normal Approximation (large samples):**
$$CI = \hat{p} \pm z_{\alpha/2}\sqrt{\frac{\hat{p}(1-\hat{p})}{n}}$$

**Wilson Score Interval (better for small samples):**
$$CI = \frac{\hat{p} + \frac{z^2}{2n} \pm z\sqrt{\frac{\hat{p}(1-\hat{p})}{n} + \frac{z^2}{4n^2}}}{1 + \frac{z^2}{n}}$$

### 3. Difference in Proportions

**Formula:**
$$\hat{p}_1 - \hat{p}_2$$

**Confidence Interval:**
$$CI = (\hat{p}_1 - \hat{p}_2) \pm z_{\alpha/2}\sqrt{\frac{\hat{p}_1(1-\hat{p}_1)}{n_1} + \frac{\hat{p}_2(1-\hat{p}_2)}{n_2}}$$

## Chi-Square Tests

### 1. Chi-Square Test of Independence

**Purpose:** Tests whether two categorical variables are independent.

**Null Hypothesis:** H₀: Variables are independent  
**Alternative Hypothesis:** H₁: Variables are associated

**Test Statistic:**
$$\chi^2 = \sum_{i=1}^{r}\sum_{j=1}^{c}\frac{(O_{ij} - E_{ij})^2}{E_{ij}}$$

Where:
- $O_{ij}$ = observed frequency in cell (i,j)
- $E_{ij}$ = expected frequency in cell (i,j)

**Degrees of Freedom:**
$$df = (r-1)(c-1)$$

**Assumptions:**
- Independent observations
- Expected frequencies ≥ 5 in at least 80% of cells
- No expected frequencies < 1

### 2. Chi-Square Goodness of Fit Test

**Purpose:** Tests whether observed frequencies match expected frequencies from a theoretical distribution.

**Test Statistic:**
$$\chi^2 = \sum_{i=1}^{k}\frac{(O_i - E_i)^2}{E_i}$$

**Degrees of Freedom:**
$$df = k - 1 - \text{number of estimated parameters}$$

### 3. Yates' Continuity Correction

**For 2×2 tables:**
$$\chi^2 = \sum_{i,j}\frac{(|O_{ij} - E_{ij}| - 0.5)^2}{E_{ij}}$$

**Use when:** Any expected frequency is between 5 and 10

## Measures of Association

### 1. Cramér's V

**Purpose:** Measures strength of association between two categorical variables.

**Formula:**
$$V = \sqrt{\frac{\chi^2}{n \times \min(r-1, c-1)}}$$

**Interpretation:**
- 0 ≤ V ≤ 1
- V = 0: No association
- V = 1: Perfect association
- Small: V < 0.1, Medium: 0.1 ≤ V < 0.3, Large: V ≥ 0.3

### 2. Phi Coefficient (φ)

**Purpose:** Measures association for 2×2 tables.

**Formula:**
$$\phi = \sqrt{\frac{\chi^2}{n}} = \frac{ad - bc}{\sqrt{(a+b)(c+d)(a+c)(b+d)}}$$

**Properties:**
- -1 ≤ φ ≤ 1
- φ = 0: No association
- |φ| = 1: Perfect association

### 3. Lambda (λ)

**Purpose:** Proportional reduction in error measure based on modal categories.

**Symmetric Lambda:**
$$\lambda = \frac{(\sum f_{r,max} + \sum f_{c,max}) - (f_{max} + f_{max})}{2n - (f_{max} + f_{max})}$$

**Asymmetric Lambda (Y dependent on X):**
$$\lambda_{Y|X} = \frac{\sum f_{r,max} - f_{c,max}}{n - f_{c,max}}$$

**Interpretation:**
- 0 ≤ λ ≤ 1
- λ = 0: No reduction in error
- λ = 1: Perfect prediction

### 4. Contingency Coefficient

**Formula:**
$$C = \sqrt{\frac{\chi^2}{\chi^2 + n}}$$

**Properties:**
- 0 ≤ C < 1
- Maximum value depends on table size
- Less interpretable than Cramér's V

## Odds Ratios and Relative Risk

### 1. Odds Ratio (OR)

**For 2×2 table:**
$$OR = \frac{ad}{bc} = \frac{\text{odds in group 1}}{\text{odds in group 2}}$$

**Log Odds Ratio:**
$$\ln(OR) = \ln(a) + \ln(d) - \ln(b) - \ln(c)$$

**Confidence Interval for ln(OR):**
$$CI = \ln(OR) \pm z_{\alpha/2}\sqrt{\frac{1}{a} + \frac{1}{b} + \frac{1}{c} + \frac{1}{d}}$$

**Interpretation:**
- OR = 1: No association
- OR > 1: Positive association
- OR < 1: Negative association

### 2. Relative Risk (RR)

**Formula:**
$$RR = \frac{a/(a+b)}{c/(c+d)} = \frac{\text{risk in exposed group}}{\text{risk in unexposed group}}$$

**Confidence Interval for ln(RR):**
$$CI = \ln(RR) \pm z_{\alpha/2}\sqrt{\frac{1}{a} - \frac{1}{a+b} + \frac{1}{c} - \frac{1}{c+d}}$$

**Interpretation:**
- RR = 1: No difference in risk
- RR > 1: Increased risk in exposed group
- RR < 1: Decreased risk in exposed group

### 3. Number Needed to Treat (NNT)

**Formula:**
$$NNT = \frac{1}{|p_1 - p_2|} = \frac{1}{\text{Absolute Risk Reduction}}$$

**Interpretation:** Number of patients that need to be treated to prevent one additional adverse outcome.

## Specialized Tests for Categorical Data

### 1. Fisher's Exact Test

**Purpose:** Exact test for 2×2 tables when chi-square assumptions are violated.

**Test Statistic:** Uses hypergeometric distribution

**Probability:**
$$P = \frac{(a+b)!(c+d)!(a+c)!(b+d)!}{a!b!c!d!n!}$$

**Use When:**
- Small sample sizes
- Expected frequencies < 5
- Need exact p-values

### 2. McNemar's Test

**Purpose:** Tests for changes in paired categorical data (before/after designs).

**Test Statistic:**
$$\chi^2 = \frac{(b-c)^2}{b+c}$$

**With Continuity Correction:**
$$\chi^2 = \frac{(|b-c|-1)^2}{b+c}$$

**Table Structure:**
|        | After + | After - | Total |
|--------|---------|---------|-------|
| Before +| a       | b       | a+b   |
| Before -| c       | d       | c+d   |
| Total  | a+c     | b+d     | n     |

**Assumptions:**
- Paired observations
- Dichotomous variables
- Large sample (b + c ≥ 25)

### 3. Cochran's Q Test

**Purpose:** Extension of McNemar's test for more than two time points.

**Test Statistic:**
$$Q = \frac{k(k-1)\sum_{j=1}^{k}(C_j - \bar{C})^2}{\sum_{i=1}^{n}R_i - \sum_{i=1}^{n}R_i^2}$$

Where:
- k = number of time points
- $C_j$ = column totals
- $R_i$ = row totals

## Effect Size Measures

### 1. Cohen's w

**For goodness of fit:**
$$w = \sqrt{\sum_{i=1}^{k}\frac{(p_{0i} - p_{1i})^2}{p_{0i}}}$$

**For independence:**
$$w = \sqrt{\frac{\chi^2}{n}}$$

**Interpretation:**
- Small: w = 0.1
- Medium: w = 0.3
- Large: w = 0.5

### 2. Cohen's h

**For difference in proportions:**
$$h = 2(\arcsin\sqrt{p_1} - \arcsin\sqrt{p_2})$$

**Interpretation:**
- Small: h = 0.2
- Medium: h = 0.5
- Large: h = 0.8

## Sample Size and Power Considerations

### 1. Sample Size for Chi-Square Test

**Formula:**
$$n = \frac{(z_{\alpha/2} + z_\beta)^2}{w^2}$$

Where:
- w = effect size (Cohen's w)
- $z_\beta$ = z-value for desired power

### 2. Sample Size for Proportion

**Single proportion:**
$$n = \frac{z_{\alpha/2}^2 \times p(1-p)}{E^2}$$

**Two proportions:**
$$n = \frac{2\bar{p}(1-\bar{p})(z_{\alpha/2} + z_\beta)^2}{(p_1 - p_2)^2}$$

## Practical Guidelines

### Choosing Appropriate Tests

**For Independence:**
- Large samples: Chi-square test
- Small samples: Fisher's exact test
- Ordered categories: Mantel-Haenszel test

**For Paired Data:**
- Two time points: McNemar's test
- Multiple time points: Cochran's Q test

**For Association Strength:**
- 2×2 tables: Phi coefficient, Odds ratio
- Larger tables: Cramér's V
- Ordinal data: Spearman's rank correlation

### Assumption Checking

**Chi-Square Test:**
- Check expected frequencies
- Ensure independence of observations
- Consider continuity correction for 2×2 tables

**Fisher's Exact Test:**
- Use when chi-square assumptions violated
- Computationally intensive for large tables
- Provides exact p-values

### Reporting Guidelines

**Essential Elements:**
- Sample sizes and frequencies
- Test statistics and p-values
- Effect sizes and confidence intervals
- Description of categories and coding

**Example:**
"A chi-square test of independence revealed a significant association between treatment group and outcome, χ²(1, N = 200) = 8.45, p = 0.004, Cramér's V = 0.21, indicating a medium effect size. The odds ratio was 2.34 (95% CI [1.32, 4.15]), suggesting patients in the treatment group had 2.34 times higher odds of positive outcomes."

This comprehensive guide provides the foundation for understanding and applying descriptive statistics and association measures for categorical data analysis.
