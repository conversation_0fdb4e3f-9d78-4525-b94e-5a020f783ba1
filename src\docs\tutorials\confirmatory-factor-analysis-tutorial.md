# Confirmatory Factor Analysis (CFA): Comprehensive Reference Guide

This comprehensive guide covers Confirmatory Factor Analysis (CFA), a structural equation modeling technique used to test hypothesized factor structures and validate measurement models. CFA is essential for construct validation, psychometric evaluation, and theory testing in multivariate research.

## Overview

Confirmatory Factor Analysis is a theory-driven statistical technique that tests whether a hypothesized factor structure fits the observed data. Unlike Exploratory Factor Analysis (EFA), CFA requires researchers to specify the number of factors, which variables load on which factors, and the relationships between factors before analysis.

## Theoretical Foundation

### 1. Measurement Model

**Basic CFA Model:**
$$\mathbf{x} = \mathbf{\Lambda_x \xi} + \mathbf{\delta}$$

Where:
- $\mathbf{x}$ = vector of observed variables
- $\mathbf{\Lambda_x}$ = matrix of factor loadings
- $\mathbf{\xi}$ = vector of latent factors
- $\mathbf{\delta}$ = vector of measurement errors

**Model-Implied Covariance Matrix:**
$$\mathbf{\Sigma(\theta)} = \mathbf{\Lambda_x \Phi \Lambda_x'} + \mathbf{\Theta_\delta}$$

Where:
- $\mathbf{\Phi}$ = factor covariance matrix
- $\mathbf{\Theta_\delta}$ = error covariance matrix
- $\mathbf{\theta}$ = vector of model parameters

### 2. Identification Requirements

**Order Condition:**
$$t \leq \frac{p(p+1)}{2}$$

Where:
- t = number of free parameters
- p = number of observed variables

**Rank Condition:**
- Information matrix must be positive definite
- No linear dependencies among parameters

**Scale Setting:**
- Fix one loading per factor to 1.0 (reference indicator)
- Or fix factor variance to 1.0

## Estimation Methods

### 1. Maximum Likelihood (ML)

**Fit Function:**
$$F_{ML} = \ln|\mathbf{\Sigma(\theta)}| + \text{tr}(\mathbf{S\Sigma(\theta)}^{-1}) - \ln|\mathbf{S}| - p$$

**Assumptions:**
- Multivariate normality
- Continuous variables
- Large sample size (N > 200)

**Test Statistic:**
$$\chi^2 = (N-1)F_{ML}$$

### 2. Weighted Least Squares (WLS)

**Fit Function:**
$$F_{WLS} = (\mathbf{s} - \mathbf{\sigma(\theta)})'\mathbf{W}^{-1}(\mathbf{s} - \mathbf{\sigma(\theta)})$$

Where:
- $\mathbf{s}$ = vector of sample moments
- $\mathbf{\sigma(\theta)}$ = vector of model-implied moments
- $\mathbf{W}$ = weight matrix

**Advantages:**
- Robust to non-normality
- Handles ordinal variables
- Asymptotically distribution-free

### 3. Robust Maximum Likelihood

**Satorra-Bentler Correction:**
$$\chi^2_{SB} = \frac{\chi^2_{ML}}{c}$$

Where c is a scaling correction factor.

**Yuan-Bentler Correction:**
- Adjusts both test statistic and standard errors
- Better performance with small samples

## Model Fit Assessment

### 1. Absolute Fit Indices

**Chi-Square Test:**
- $H_0$: Model fits perfectly
- Sensitive to sample size
- Significant result indicates poor fit

**Root Mean Square Error of Approximation (RMSEA):**
$$RMSEA = \sqrt{\frac{\max(\chi^2 - df, 0)}{df(N-1)}}$$

**Interpretation:**
- < 0.05: Close fit
- 0.05-0.08: Fair fit
- 0.08-0.10: Mediocre fit
- > 0.10: Poor fit

**Standardized Root Mean Square Residual (SRMR):**
$$SRMR = \sqrt{\frac{2\sum_{i \leq j}(r_{ij} - \hat{r}_{ij})^2}{p(p+1)}}$$

**Interpretation:**
- < 0.05: Good fit
- < 0.08: Acceptable fit

### 2. Incremental Fit Indices

**Comparative Fit Index (CFI):**
$$CFI = 1 - \frac{\max(\chi^2_t - df_t, 0)}{\max(\chi^2_b - df_b, 0)}$$

Where subscripts t and b refer to target and baseline models.

**Tucker-Lewis Index (TLI):**
$$TLI = \frac{(\chi^2_b/df_b) - (\chi^2_t/df_t)}{(\chi^2_b/df_b) - 1}$$

**Interpretation (CFI & TLI):**
- > 0.95: Excellent fit
- 0.90-0.95: Acceptable fit
- < 0.90: Poor fit

### 3. Information Criteria

**Akaike Information Criterion (AIC):**
$$AIC = \chi^2 + 2t$$

**Bayesian Information Criterion (BIC):**
$$BIC = \chi^2 + t \ln(N)$$

**Model Comparison:**
- Lower values indicate better fit
- Useful for non-nested model comparison

## Model Specification

### 1. Single-Factor Model

**Specification:**
- All indicators load on one factor
- Error terms uncorrelated
- Factor variance fixed or loading fixed

**Identification:**
- Minimum 3 indicators required
- df = p(p-1)/2 - p = p(p-3)/2

### 2. Multi-Factor Model

**Correlated Factors:**

Factor correlation matrix with correlations between factors:
$$\phi_{12}, \phi_{13}, \phi_{23}$$

Where $\phi_{ij}$ represents the correlation between factors i and j.

**Orthogonal Factors:**
$$\mathbf{\Phi} = \mathbf{I}$$

### 3. Higher-Order Models

**Second-Order Factor:**
$$\mathbf{\xi} = \mathbf{\Gamma \eta} + \mathbf{\zeta}$$

Where:
- $\mathbf{\eta}$ = second-order factors
- $\mathbf{\Gamma}$ = second-order loadings
- $\mathbf{\zeta}$ = disturbances

## Reliability and Validity

### 1. Reliability Measures

**Composite Reliability (CR):**
$$CR = \frac{(\sum \lambda_i)^2}{(\sum \lambda_i)^2 + \sum \text{Var}(\epsilon_i)}$$

**Interpretation:**
- > 0.70: Acceptable
- > 0.80: Good
- > 0.90: Excellent

**Coefficient Omega:**
$$\omega = \frac{(\sum \lambda_i)^2}{(\sum \lambda_i)^2 + \sum \psi_{ii} + 2\sum_{i<j}\psi_{ij}}$$

### 2. Validity Assessment

**Convergent Validity:**
- Factor loadings > 0.50
- Average Variance Extracted (AVE) > 0.50

**Average Variance Extracted (AVE):**
$$AVE = \frac{\sum \lambda_i^2}{\sum \lambda_i^2 + \sum \text{Var}(\epsilon_i)}$$

**Discriminant Validity:**
- $\sqrt{AVE} >$ factor correlations
- Confidence interval of factor correlation excludes 1.0

### 3. Measurement Invariance

**Configural Invariance:**
- Same factor structure across groups
- No equality constraints

**Metric Invariance:**
- Equal factor loadings across groups
- $\mathbf{\Lambda}_1 = \mathbf{\Lambda}_2$

**Scalar Invariance:**
- Equal intercepts across groups
- $\mathbf{\tau}_1 = \mathbf{\tau}_2$

**Strict Invariance:**
- Equal error variances across groups
- $\mathbf{\Theta}_1 = \mathbf{\Theta}_2$

## Model Modification

### 1. Modification Indices

**Lagrange Multiplier Test:**
$$LM = \frac{(\partial F/\partial \theta_j)^2}{\partial^2 F/\partial \theta_j^2}$$

**Interpretation:**
- Expected decrease in χ² if parameter is freed
- Values > 3.84 suggest significant improvement

**Expected Parameter Change (EPC):**
- Estimated value of freed parameter
- Helps assess practical significance

### 2. Residual Analysis

**Standardized Residuals:**
$$z_{ij} = \frac{s_{ij} - \hat{\sigma}_{ij}}{\sqrt{\text{Var}(s_{ij} - \hat{\sigma}_{ij})}}$$

**Acceptable Range:** |z| < 2.58

**Normalized Residuals:**
- Adjusted for sampling variability
- Better for model diagnosis

### 3. Modification Guidelines

**Theoretical Justification:**
- Modifications must make theoretical sense
- Avoid purely statistical modifications

**Cross-Validation:**
- Test modified model on independent sample
- Prevents capitalization on chance

## Advanced Applications

### 1. Multi-Trait Multi-Method (MTMM)

**Correlated Traits-Correlated Methods:**
- Separate trait and method factors
- Assess convergent and discriminant validity

**Model Specification:**
$$x_{ij} = \lambda_{Ti}\xi_{Ti} + \lambda_{Mj}\xi_{Mj} + \epsilon_{ij}$$

Where T = trait, M = method.

### 2. Bifactor Models

**General Factor Plus Specific Factors:**
$$x_i = \lambda_{Gi}\xi_G + \lambda_{Si}\xi_{Si} + \epsilon_i$$

**Advantages:**
- Models general and specific variance
- Better fit than higher-order models

### 3. Longitudinal CFA

**Measurement Invariance Over Time:**
- Test stability of factor structure
- Assess true change vs. measurement error

**Autoregressive Models:**
$$\xi_{t+1} = \beta\xi_t + \zeta_{t+1}$$

## Assumptions and Diagnostics

### 1. Sample Size Requirements

**Minimum Requirements:**
- 5-10 observations per parameter
- Absolute minimum: 100-150 cases
- Complex models: 200+ cases

**Power Analysis:**
- Consider effect size (RMSEA)
- Desired power (typically 0.80)
- Significance level (α = 0.05)

### 2. Distributional Assumptions

**Multivariate Normality:**
- Mardia's coefficient < 3.0 (skewness)
- Mardia's coefficient < 10.0 (kurtosis)

**Outlier Detection:**
- Mahalanobis distance
- Leverage values
- Standardized residuals

### 3. Missing Data

**Missing Completely at Random (MCAR):**
- Little's MCAR test
- Missing data patterns

**Handling Methods:**
- Full Information Maximum Likelihood (FIML)
- Multiple imputation
- Avoid listwise deletion

## Practical Guidelines

### 1. Model Building Strategy

**Step 1:** Specify measurement model
**Step 2:** Assess model identification
**Step 3:** Estimate model parameters
**Step 4:** Evaluate model fit
**Step 5:** Modify model if necessary
**Step 6:** Cross-validate results

### 2. Reporting Standards

**Model Specification:**
- Number of factors and indicators
- Fixed and free parameters
- Identification constraints

**Estimation Details:**
- Estimation method
- Software used
- Convergence information

**Fit Assessment:**
- Multiple fit indices
- Confidence intervals
- Model comparison results

### 3. Common Pitfalls

**Specification Errors:**
- Under-identification
- Improper constraints
- Omitted parameters

**Estimation Problems:**
- Non-convergence
- Improper solutions
- Boundary estimates

**Interpretation Issues:**
- Over-reliance on fit indices
- Ignoring theoretical considerations
- Excessive model modification

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Sample characteristics
- Measurement instruments
- Estimation method
- Software and version

### 2. Results Section

**Required Information:**
- Model fit indices with confidence intervals
- Parameter estimates with standard errors
- Reliability and validity evidence
- Model modification details

### 3. Example Reporting

"A confirmatory factor analysis was conducted using maximum likelihood estimation (Mplus 8.0) to test a three-factor measurement model (N = 425). The model demonstrated acceptable fit: χ²(87) = 156.3, p < 0.001; RMSEA = 0.043 (90% CI: 0.032-0.054); CFI = 0.96; SRMR = 0.048. All factor loadings were significant (p < 0.001) and ranged from 0.58 to 0.89. Composite reliability exceeded 0.80 for all factors, and discriminant validity was supported."

This comprehensive guide provides the foundation for conducting and interpreting Confirmatory Factor Analysis in measurement validation and structural equation modeling applications.
