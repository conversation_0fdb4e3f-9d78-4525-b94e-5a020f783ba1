# Correlation and Linear Regression Analysis: Comprehensive Reference Guide

This comprehensive guide covers correlation analysis and linear regression modeling, including assumptions, diagnostics, model selection, and interpretation guidelines with detailed mathematical formulations and practical examples.

## Overview

Correlation and regression analysis are fundamental statistical techniques for examining relationships between variables. Correlation measures the strength and direction of linear relationships, while regression models these relationships to make predictions and understand variable dependencies.

## Correlation Analysis

### 1. Pearson Product-Moment Correlation

**Purpose:** Measures the linear relationship between two continuous variables.

**Formula:**
$$r = \frac{\sum_{i=1}^{n}(x_i - \bar{x})(y_i - \bar{y})}{\sqrt{\sum_{i=1}^{n}(x_i - \bar{x})^2}\sqrt{\sum_{i=1}^{n}(y_i - \bar{y})^2}}$$

**Alternative formula:**
$$r = \frac{n\sum xy - \sum x \sum y}{\sqrt{[n\sum x^2 - (\sum x)^2][n\sum y^2 - (\sum y)^2]}}$$

**Properties:**
- Range: -1 ≤ r ≤ 1
- r = 1: Perfect positive linear relationship
- r = -1: Perfect negative linear relationship
- r = 0: No linear relationship

**Interpretation Guidelines:**
- |r| < 0.3: Weak correlation
- 0.3 ≤ |r| < 0.7: Moderate correlation
- |r| ≥ 0.7: Strong correlation

### 2. Spearman Rank Correlation

**Purpose:** Measures monotonic relationships between variables, robust to outliers.

**Formula:**
$$r_s = 1 - \frac{6\sum d_i^2}{n(n^2-1)}$$

Where $d_i$ = difference between ranks of corresponding values.

**When tied ranks exist:**
$$r_s = \frac{\sum_{i=1}^{n}(R_x - \bar{R_x})(R_y - \bar{R_y})}{\sqrt{\sum_{i=1}^{n}(R_x - \bar{R_x})^2}\sqrt{\sum_{i=1}^{n}(R_y - \bar{R_y})^2}}$$

**Use Cases:**
- Ordinal data
- Non-linear monotonic relationships
- Presence of outliers
- Non-normal distributions

### 3. Correlation Assumptions and Testing

**Pearson Correlation Assumptions:**
- Linear relationship
- Continuous variables
- Bivariate normality
- Homoscedasticity

**Significance Test:**
$$t = \frac{r\sqrt{n-2}}{\sqrt{1-r^2}}$$

With df = n - 2

**Confidence Interval for r:**
$$CI = \tanh\left(z_r \pm \frac{z_{\alpha/2}}{\sqrt{n-3}}\right)$$

Where $z_r = \frac{1}{2}\ln\left(\frac{1+r}{1-r}\right)$ (Fisher's z-transformation)

## Simple Linear Regression

### 1. Linear Regression Model

**Population Model:**
$$Y_i = \beta_0 + \beta_1 X_i + \epsilon_i$$

**Sample Model:**
$$\hat{Y}_i = b_0 + b_1 X_i$$

Where:
- $\beta_0$ = population intercept
- $\beta_1$ = population slope
- $\epsilon_i$ = error term
- $b_0, b_1$ = sample estimates

### 2. Least Squares Estimation

**Slope:**
$$b_1 = \frac{\sum_{i=1}^{n}(x_i - \bar{x})(y_i - \bar{y})}{\sum_{i=1}^{n}(x_i - \bar{x})^2} = \frac{SS_{xy}}{SS_{xx}}$$

**Intercept:**
$$b_0 = \bar{y} - b_1\bar{x}$$

**Alternative formulas:**
$$b_1 = \frac{n\sum xy - \sum x \sum y}{n\sum x^2 - (\sum x)^2}$$

$$b_0 = \frac{\sum y - b_1 \sum x}{n}$$

### 3. Regression Assumptions

**LINEAR:** Linear relationship between X and Y
**INDEPENDENCE:** Observations are independent
**NORMALITY:** Residuals are normally distributed
**EQUAL VARIANCE:** Homoscedasticity of residuals

**Residual:**
$$e_i = y_i - \hat{y}_i$$

### 4. Standard Errors and Confidence Intervals

**Standard Error of Slope:**
$$SE(b_1) = \sqrt{\frac{MSE}{SS_{xx}}} = \sqrt{\frac{MSE}{\sum(x_i - \bar{x})^2}}$$

**Standard Error of Intercept:**
$$SE(b_0) = \sqrt{MSE\left(\frac{1}{n} + \frac{\bar{x}^2}{SS_{xx}}\right)}$$

**Mean Square Error:**
$$MSE = \frac{SSE}{n-2} = \frac{\sum(y_i - \hat{y}_i)^2}{n-2}$$

**Confidence Intervals:**
$$b_1 \pm t_{\alpha/2,n-2} \times SE(b_1)$$
$$b_0 \pm t_{\alpha/2,n-2} \times SE(b_0)$$

## Multiple Linear Regression

### 1. Multiple Regression Model

**Population Model:**
$$Y_i = \beta_0 + \beta_1 X_{1i} + \beta_2 X_{2i} + ... + \beta_k X_{ki} + \epsilon_i$$

**Matrix Form:**
$$\mathbf{Y} = \mathbf{X}\boldsymbol{\beta} + \boldsymbol{\epsilon}$$

**Least Squares Solution:**
$$\hat{\boldsymbol{\beta}} = (\mathbf{X}^T\mathbf{X})^{-1}\mathbf{X}^T\mathbf{Y}$$

### 2. Coefficient Interpretation

**Partial Regression Coefficient:**
- $\beta_j$ = change in Y for one-unit increase in $X_j$, holding all other variables constant

**Standardized Coefficients:**
$$\beta_j^* = \beta_j \times \frac{s_{x_j}}{s_y}$$

### 3. Model Selection Techniques

**Forward Selection:**
1. Start with no variables
2. Add variables that significantly improve model
3. Stop when no improvement

**Backward Elimination:**
1. Start with all variables
2. Remove non-significant variables
3. Stop when all remaining variables are significant

**Stepwise Selection:**
- Combination of forward and backward
- Variables can be added or removed at each step

**Selection Criteria:**
- **AIC:** $AIC = n \ln(SSE/n) + 2k$
- **BIC:** $BIC = n \ln(SSE/n) + k \ln(n)$
- **Adjusted R²:** $R_{adj}^2 = 1 - \frac{(1-R^2)(n-1)}{n-k-1}$

## Model Evaluation and Diagnostics

### 1. Coefficient of Determination

**R-squared:**
$$R^2 = \frac{SSR}{SST} = 1 - \frac{SSE}{SST}$$

Where:
- SSR = Sum of Squares Regression
- SSE = Sum of Squares Error
- SST = Total Sum of Squares

**Adjusted R-squared:**
$$R_{adj}^2 = 1 - \frac{SSE/(n-k-1)}{SST/(n-1)}$$

**Interpretation:**
- R² = proportion of variance in Y explained by X
- Adjusted R² penalizes for additional predictors

### 2. ANOVA for Regression

**F-test for Overall Significance:**
$$F = \frac{MSR}{MSE} = \frac{SSR/k}{SSE/(n-k-1)}$$

**ANOVA Table:**
| Source     | df    | SS  | MS      | F       |
|------------|-------|-----|---------|---------|
| Regression | k     | SSR | MSR     | MSR/MSE |
| Error      | n-k-1 | SSE | MSE     |         |
| Total      | n-1   | SST |         |         |

### 3. Residual Analysis

**Standardized Residuals:**
$$r_i = \frac{e_i}{\sqrt{MSE}}$$

**Studentized Residuals:**
$$t_i = \frac{e_i}{\sqrt{MSE(1-h_{ii})}}$$

Where $h_{ii}$ = leverage value

**Diagnostic Plots:**
- Residuals vs. Fitted Values (linearity, homoscedasticity)
- Normal Q-Q Plot (normality)
- Residuals vs. Leverage (influential points)
- Cook's Distance (influential observations)

### 4. Outliers and Influential Points

**Leverage:**
$$h_{ii} = \mathbf{x}_i^T(\mathbf{X}^T\mathbf{X})^{-1}\mathbf{x}_i$$

**Cook's Distance:**
$$D_i = \frac{r_i^2}{k+1} \times \frac{h_{ii}}{1-h_{ii}}$$

**Criteria:**
- High leverage: $h_{ii} > 2(k+1)/n$
- Outlier: $|t_i| > 2$ or $|t_i| > 3$
- Influential: $D_i > 4/n$ or $D_i > 1$

## Prediction and Inference

### 1. Prediction Intervals vs. Confidence Intervals

**Confidence Interval for Mean Response:**
$$\hat{Y}_0 \pm t_{\alpha/2,n-2} \times SE(\hat{Y}_0)$$

**Prediction Interval for Individual Response:**
$$\hat{Y}_0 \pm t_{\alpha/2,n-2} \times SE(pred)$$

Where:
$$SE(\hat{Y}_0) = \sqrt{MSE \times \mathbf{x}_0^T(\mathbf{X}^T\mathbf{X})^{-1}\mathbf{x}_0}$$

$$SE(pred) = \sqrt{MSE \times (1 + \mathbf{x}_0^T(\mathbf{X}^T\mathbf{X})^{-1}\mathbf{x}_0)}$$

### 2. Hypothesis Testing

**Test for Individual Coefficients:**
$$H_0: \beta_j = 0 \text{ vs. } H_1: \beta_j \neq 0$$

$$t = \frac{b_j}{SE(b_j)}$$

**Test for Multiple Coefficients:**
$$F = \frac{(SSE_R - SSE_F)/(df_R - df_F)}{SSE_F/df_F}$$

Where R = reduced model, F = full model

## Practical Guidelines

### 1. Model Building Process

**Steps:**
1. Exploratory data analysis
2. Check assumptions
3. Fit initial model
4. Residual analysis
5. Model refinement
6. Validation

### 2. Assumption Checking

**Linearity:**
- Scatterplots of Y vs. each X
- Residuals vs. fitted values plot

**Independence:**
- Durbin-Watson test for autocorrelation
- Plot residuals vs. time (if applicable)

**Normality:**
- Q-Q plot of residuals
- Shapiro-Wilk test
- Histogram of residuals

**Homoscedasticity:**
- Residuals vs. fitted values
- Breusch-Pagan test
- White test

### 3. Common Issues and Solutions

**Multicollinearity:**
- **Detection:** VIF > 10, condition index > 30
- **Solutions:** Remove variables, ridge regression, PCA

**Non-linearity:**
- **Solutions:** Polynomial terms, transformations, splines

**Heteroscedasticity:**
- **Solutions:** Weighted least squares, robust standard errors

**Non-normality:**
- **Solutions:** Transformations, robust regression

### 4. Reporting Guidelines

**Essential Elements:**
- Model equation with coefficients
- R² and adjusted R²
- F-statistic and p-value
- Individual coefficient tests
- Confidence intervals
- Assumption checking results
- Sample size and missing data

**Example:**
"A simple linear regression revealed that study hours significantly predicted exam scores, F(1, 98) = 45.2, p < 0.001, R² = 0.32. The regression equation was: Exam Score = 65.4 + 2.8(Study Hours). For each additional hour of study, exam scores increased by 2.8 points (95% CI [2.0, 3.6])."

This comprehensive guide provides the foundation for understanding and applying correlation and regression analysis in statistical research and data analysis.
