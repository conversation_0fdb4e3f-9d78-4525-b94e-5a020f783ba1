# Exploratory Factor Analysis (EFA): Comprehensive Reference Guide

This comprehensive guide covers Exploratory Factor Analysis (EFA), a statistical technique used to identify underlying factors that explain patterns of correlations among observed variables. EFA is essential for data reduction, construct validation, and understanding latent structures in multivariate data.

## Overview

Exploratory Factor Analysis is a multivariate statistical technique that explores the underlying structure of a set of observed variables by identifying common factors that explain the correlations among variables. Unlike Confirmatory Factor Analysis (CFA), EFA does not require prior hypotheses about the factor structure and is used for exploratory purposes to discover the latent dimensions in data.

## Theoretical Foundation

### 1. Factor Model

**Basic Factor Model:**
$$X_i = \lambda_{i1}F_1 + \lambda_{i2}F_2 + ... + \lambda_{im}F_m + \epsilon_i$$

Where:
- $X_i$ = observed variable i
- $F_j$ = common factor j
- $\lambda_{ij}$ = factor loading of variable i on factor j
- $\epsilon_i$ = unique factor (error) for variable i
- m = number of common factors

**Matrix Form:**
$$\mathbf{X} = \mathbf{\Lambda F} + \mathbf{\epsilon}$$

Where:
- $\mathbf{X}$ = vector of observed variables
- $\mathbf{\Lambda}$ = matrix of factor loadings
- $\mathbf{F}$ = vector of common factors
- $\mathbf{\epsilon}$ = vector of unique factors

### 2. Correlation Structure

**Reproduced Correlation:**
$$r_{ij} = \sum_{k=1}^{m} \lambda_{ik}\lambda_{jk}$$

**Communality:**
$$h_i^2 = \sum_{j=1}^{m} \lambda_{ij}^2$$

**Uniqueness:**
$$u_i^2 = 1 - h_i^2$$

## Factor Extraction Methods

### 1. Principal Component Analysis (PCA)

**Eigenvalue Decomposition:**
$$\mathbf{R} = \mathbf{V\Lambda V'}$$

Where:
- $\mathbf{R}$ = correlation matrix
- $\mathbf{V}$ = matrix of eigenvectors
- $\mathbf{\Lambda}$ = diagonal matrix of eigenvalues

**Component Scores:**
$$F_j = \sum_{i=1}^{p} w_{ij}X_i$$

Where $w_{ij}$ are component score coefficients.

### 2. Principal Axis Factoring (PAF)

**Iterative Process:**
1. Initial communality estimates in diagonal of R
2. Extract factors from reduced correlation matrix
3. Compute new communality estimates
4. Repeat until convergence

**Convergence Criterion:**
$$\sum_{i=1}^{p}(h_i^{2(k+1)} - h_i^{2(k)})^2 < \epsilon$$

### 3. Maximum Likelihood Estimation

**Likelihood Function:**
$$L = \frac{1}{(2\pi)^{np/2}|\mathbf{\Sigma}|^{n/2}} \exp\left(-\frac{1}{2}\text{tr}(\mathbf{S\Sigma}^{-1})\right)$$

Where:
- $\mathbf{S}$ = sample covariance matrix
- $\mathbf{\Sigma}$ = model-implied covariance matrix
- n = sample size

## Determining Number of Factors

### 1. Kaiser Criterion (Eigenvalue > 1)

**Rule:** Retain factors with eigenvalues > 1.0

**Rationale:** Factors should explain more variance than a single standardized variable.

### 2. Scree Plot

**Visual Method:** Plot eigenvalues and look for "elbow" where slope changes dramatically.

**Cattell's Rule:** Retain factors before the point where eigenvalues level off.

### 3. Parallel Analysis

**Procedure:**
1. Generate random correlation matrices
2. Compute eigenvalues for random data
3. Compare actual eigenvalues to random eigenvalues
4. Retain factors where actual > random

**Decision Rule:**
$$\lambda_{actual,i} > \lambda_{random,i}$$

### 4. Variance Explained Criteria

**Total Variance Explained:**
$$\text{Proportion} = \frac{\sum_{j=1}^{m}\lambda_j}{\sum_{j=1}^{p}\lambda_j}$$

**Common Thresholds:**
- Social Sciences: 60-70%
- Natural Sciences: 80-90%

## Factor Rotation Methods

### 1. Orthogonal Rotations

**Varimax Rotation:**
- Maximizes variance of squared loadings within factors
- Produces simple structure with high and low loadings
- Most commonly used orthogonal rotation

**Quartimax Rotation:**
- Maximizes variance of squared loadings within variables
- Tends to produce general factor

**Equamax Rotation:**
- Combination of Varimax and Quartimax
- Balances simplicity of factors and variables

### 2. Oblique Rotations

**Direct Oblimin:**
- Allows factors to correlate
- Parameter δ controls degree of obliqueness
- δ = 0: Most oblique solution

**Promax Rotation:**
- Two-step process: Varimax followed by oblique transformation
- Computationally efficient
- Allows moderate factor correlations

**Factor Correlation Matrix:**
$$\mathbf{\Phi} = \mathbf{T}^{-1}\mathbf{T}^{-1'}$$

Where $\mathbf{T}$ is the transformation matrix.

## Factor Interpretation and Scoring

### 1. Factor Loading Interpretation

**Loading Magnitude Guidelines:**
- |λ| ≥ 0.70: Excellent
- |λ| ≥ 0.63: Very good
- |λ| ≥ 0.55: Good
- |λ| ≥ 0.45: Fair
- |λ| ≥ 0.32: Poor

**Cross-loadings:** Variables loading > 0.32 on multiple factors

### 2. Factor Score Computation

**Regression Method:**
$$\mathbf{F} = \mathbf{R}^{-1}\mathbf{\Lambda}(\mathbf{\Lambda}'\mathbf{R}^{-1}\mathbf{\Lambda})^{-1}\mathbf{X}$$

**Bartlett Method:**
$$\mathbf{F} = (\mathbf{\Lambda}'\mathbf{\Psi}^{-1}\mathbf{\Lambda})^{-1}\mathbf{\Lambda}'\mathbf{\Psi}^{-1}\mathbf{X}$$

Where $\mathbf{\Psi}$ is the uniqueness matrix.

**Anderson-Rubin Method:**
- Produces uncorrelated factor scores
- Standardized with mean 0 and variance 1

## Assumptions and Prerequisites

### 1. Sample Size Requirements

**Minimum Requirements:**
- Absolute minimum: 100 cases
- Preferred: 200+ cases
- Rule of thumb: 5-10 cases per variable
- Complex models: 20+ cases per variable

**Kaiser-Meyer-Olkin (KMO) Test:**
$$KMO = \frac{\sum_{i \neq j}r_{ij}^2}{\sum_{i \neq j}r_{ij}^2 + \sum_{i \neq j}a_{ij}^2}$$

**KMO Interpretation:**
- 0.90+: Marvelous
- 0.80-0.89: Meritorious
- 0.70-0.79: Middling
- 0.60-0.69: Mediocre
- 0.50-0.59: Miserable
- < 0.50: Unacceptable

### 2. Bartlett's Test of Sphericity

**Null Hypothesis:** Correlation matrix is identity matrix

**Test Statistic:**
$$\chi^2 = -\left(n - 1 - \frac{2p + 5}{6}\right)\ln|\mathbf{R}|$$

**Degrees of Freedom:**
$$df = \frac{p(p-1)}{2}$$

### 3. Data Assumptions

**Linearity:** Relationships between variables should be linear

**Normality:** Multivariate normality preferred for ML estimation

**Homoscedasticity:** Equal variances across groups

**No Extreme Outliers:** Can distort factor structure

## Model Evaluation

### 1. Goodness of Fit Indices

**Chi-Square Test:**
$$\chi^2 = (n-1)F_{ML}$$

Where $F_{ML}$ is the maximum likelihood fit function.

**Root Mean Square Error of Approximation (RMSEA):**
$$RMSEA = \sqrt{\frac{\chi^2 - df}{df(n-1)}}$$

**Comparative Fit Index (CFI):**
$$CFI = 1 - \frac{\max(\chi^2_{model} - df_{model}, 0)}{\max(\chi^2_{baseline} - df_{baseline}, 0)}$$

### 2. Residual Analysis

**Standardized Residuals:**
$$z_{ij} = \frac{r_{ij} - \hat{r}_{ij}}{\sqrt{\text{Var}(r_{ij} - \hat{r}_{ij})}}$$

**Acceptable Range:** |z| < 2.58 (p < 0.01)

## Advanced Topics

### 1. Higher-Order Factor Analysis

**Second-Order Model:**
$$\mathbf{F}_1 = \mathbf{\Lambda}_2\mathbf{F}_2 + \mathbf{\epsilon}_1$$

Where $\mathbf{F}_1$ are first-order factors and $\mathbf{F}_2$ are second-order factors.

### 2. Exploratory Structural Equation Modeling (ESEM)

**Integration of EFA and SEM:**
- Allows cross-loadings in confirmatory framework
- More flexible than traditional CFA
- Better fit for complex psychological constructs

### 3. Robust Factor Analysis

**Handling Non-normality:**
- Weighted Least Squares (WLS)
- Diagonally Weighted Least Squares (DWLS)
- Robust Maximum Likelihood

## Practical Guidelines

### 1. Planning the Analysis

**Variable Selection:**
- Include 3-5 variables per expected factor
- Avoid highly correlated variables (r > 0.90)
- Consider theoretical relevance

**Sample Size Planning:**
- Minimum 5:1 variable-to-case ratio
- Larger samples for complex structures
- Consider missing data patterns

### 2. Interpretation Guidelines

**Factor Naming:**
- Examine highest loading variables
- Consider theoretical meaning
- Use substantive knowledge
- Avoid over-interpretation

**Reporting Standards:**
- Factor extraction method
- Rotation method
- Number of factors retained
- Variance explained
- Factor loadings matrix
- Factor correlations (if oblique)

### 3. Common Issues and Solutions

**Heywood Cases:**
- Communalities > 1.0
- Solutions: Reduce factors, change extraction method

**Factor Indeterminacy:**
- Multiple solutions possible
- Focus on interpretability and replicability

**Overfactoring/Underfactoring:**
- Use multiple criteria for factor retention
- Consider theoretical expectations

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Sample size and characteristics
- Variable selection rationale
- Extraction method and justification
- Rotation method and rationale
- Factor retention criteria

### 2. Results Section

**Required Information:**
- KMO and Bartlett's test results
- Total variance explained
- Factor loadings table
- Factor correlations (if oblique)
- Factor score reliability

### 3. Example Reporting

"Exploratory factor analysis was conducted using principal axis factoring with Promax rotation on 15 items (N = 300). The Kaiser-Meyer-Olkin measure verified sampling adequacy (KMO = 0.85), and Bartlett's test of sphericity was significant (χ² = 1847.3, df = 105, p < 0.001). Three factors with eigenvalues > 1.0 were retained, explaining 68.4% of the total variance. Factor loadings ranged from 0.45 to 0.89, with no cross-loadings > 0.32."

This comprehensive guide provides the foundation for conducting and interpreting Exploratory Factor Analysis in statistical research and psychometric applications.
