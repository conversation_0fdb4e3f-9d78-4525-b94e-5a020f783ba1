# Logistic Regression Tutorial

This tutorial provides a guide to understanding and using the Logistic Regression analysis feature in the application.

## Introduction to Logistic Regression

Logistic Regression is a statistical method used for predicting the probability of a binary outcome (an outcome that can have only two possible values, e.g., Yes/No, 0/1, True/False). Unlike linear regression, which predicts a continuous outcome, logistic regression uses a logistic function to model the relationship between the independent variables (predictors) and the probability of the dependent variable belonging to a particular class.

It is widely used in various fields, including:
*   **Medicine:** Predicting the likelihood of a disease based on patient characteristics.
*   **Marketing:** Predicting whether a customer will purchase a product.
*   **Finance:** Predicting the probability of loan default.

## Using the Logistic Regression Component

The Logistic Regression component in this application allows you to perform logistic regression analysis on your datasets.

1.  **Select Dataset:** Choose the dataset you want to analyze from the "Dataset" dropdown.
2.  **Select Independent Variables (X):** Select one or more predictor variables from the "Independent Variables (X)" dropdown. These can be Numeric or Categorical.
3.  **Select Dependent Variable (Y - Binary):** Select a binary outcome variable from the "Dependent Variable (Y - Binary)" dropdown. This variable must be binary (e.g., 0/1) or a categorical variable with exactly two distinct values. If you select a categorical variable with two values, you will be prompted to map which value corresponds to '1' and which to '0'.
4.  **Select Confidence Level:** Choose the desired confidence level for confidence intervals (e.g., 95%).
5.  **Display Options:** Select which visualizations and information you want to display (e.g., regression curve, ROC curve, equation).
6.  **Run Logistic Regression:** Click the "Run Logistic Regression" button to perform the analysis.

## Computational and Formula Details

The component implements multiple logistic regression, handling both numeric and categorical independent variables.

### The Logistic Function and Logit Transformation

Logistic regression models the relationship between the predictors and the log odds of the outcome. The log odds (or logit) is a linear combination of the independent variables:

$logit(p) = \ln\left(\frac{p}{1-p}\right) = \beta_0 + \beta_1 X_1 + \beta_2 X_2 + \dots + \beta_n X_n$

Where:
*   $p$ is the probability of the dependent variable being 1 (the event of interest).
*   $\beta_0$ is the intercept.
*   $\beta_i$ are the coefficients for the independent variables $X_i$.

To get the predicted probability $p$ from the logit, the inverse of the logit transformation (the logistic function) is used:

$p = \frac{1}{1 + e^{-(\beta_0 + \beta_1 X_1 + \dots + \beta_n X_n)}}$

The `multipleLogisticRegression` function in src/utils/stats likely implements an iterative algorithm (like Iteratively Reweighted Least Squares - IRLS) to find the coefficients ($\beta_i$) that maximize the likelihood of observing the given data.

### Handling Categorical Independent Variables

Categorical independent variables with more than two categories are handled using **dummy coding**. For a categorical variable with $k$ categories, $k-1$ dummy variables are created. One category is chosen as the **base category**, and no dummy variable is created for it. Each dummy variable represents the difference between a specific category and the base category.

For example, if a categorical variable "Color" has categories "Red", "Green", and "Blue", and "Blue" is chosen as the base category, two dummy variables might be created:
*   Color_Red: 1 if Color is "Red", 0 otherwise.
*   Color_Green: 1 if Color is "Green", 0 otherwise.

The coefficient for Color_Red would represent the change in log odds when the color is "Red" compared to the base category "Blue", holding other variables constant.

The component allows you to select the base category for each categorical independent variable.

### Interpretation of Coefficients and Odds Ratios

*   **Coefficients ($\beta_i$):** The coefficients represent the change in the log odds of the outcome for a one-unit increase in the corresponding independent variable, holding other variables constant.
*   **Odds Ratio ($e^{\beta_i}$):** The exponential of a coefficient ($e^{\beta_i}$) is the odds ratio. It represents the factor by which the odds of the outcome change for a one-unit increase in the independent variable, holding other variables constant.
    *   If $e^{\beta_i} > 1$, the odds of the outcome increase as $X_i$ increases.
    *   If $e^{\beta_i} < 1$, the odds of the outcome decrease as $X_i$ increases.
    *   If $e^{\beta_i} = 1$, the odds of the outcome do not change as $X_i$ increases (the variable has no effect).

### Statistical Significance

The component calculates standard errors, z-values, and p-values for each coefficient and the intercept.
*   **Standard Error:** A measure of the variability of the coefficient estimate.
*   **z-value:** The ratio of the coefficient to its standard error ($\beta_i / SE(\beta_i)$). It is used to test the null hypothesis that the coefficient is zero.
*   **p-value:** The probability of observing a z-value as extreme as, or more extreme than, the calculated value, assuming the null hypothesis is true. A small p-value (typically < 0.05) indicates that the coefficient is statistically significant, meaning there is sufficient evidence to conclude that the independent variable is associated with the outcome.

### Model Fit Statistics

*   **Log Likelihood:** A measure of how well the model fits the data. Higher values indicate a better fit.
*   **AIC (Akaike Information Criterion):** A measure that balances model fit and complexity. Lower values indicate a better model.
*   **Pseudo R² (McFadden's):** A measure analogous to R² in linear regression, indicating the proportion of variance in the dependent variable explained by the model. Values range from 0 to 1, with higher values indicating a better fit. However, interpretation differs from linear regression R².

### Classification Metrics and Confusion Matrix

At a given probability threshold (defaulting to 0.5), the model classifies each observation into one of the two outcome classes. The **Confusion Matrix** summarizes the results:

|             | Predicted 0 | Predicted 1 |
| :---------- | :---------- | :---------- |
| **Actual 0** | True Negatives (TN) | False Positives (FP) |
| **Actual 1** | False Negatives (FN) | True Positives (TP) |

Based on the confusion matrix, several classification metrics are calculated:
*   **Accuracy:** $(TN + TP) / (TN + FP + FN + TP)$ - Overall proportion of correctly classified instances.
*   **Precision:** $TP / (TP + FP)$ - Proportion of positive predictions that were actually positive.
*   **Recall (Sensitivity):** $TP / (TP + FN)$ - Proportion of actual positives that were correctly identified.
*   **Specificity:** $TN / (TN + FP)$ - Proportion of actual negatives that were correctly identified.
*   **F1 Score:** $2 \times \frac{Precision \times Recall}{Precision + Recall}$ - Harmonic mean of precision and recall.

### ROC Curve and AUC

The **ROC (Receiver Operating Characteristic) curve** is a graphical plot that illustrates the diagnostic ability of a binary classifier system as its discrimination threshold is varied. It plots the True Positive Rate (Sensitivity) against the False Positive Rate (1 - Specificity) at various threshold settings.

The **AUC (Area Under the ROC Curve)** is a single scalar value that summarizes the overall performance of the classifier across all possible thresholds.
*   AUC = 0.5 indicates no discrimination (equivalent to random chance).
*   AUC = 1.0 indicates perfect discrimination.
*   AUC values between 0.5 and 1.0 indicate varying degrees of discrimination ability.

### Prediction Tool

The prediction tool allows you to input values for the independent variables and get a predicted probability of the outcome being 1.

The prediction is calculated using the logistic function with the estimated coefficients and the input values:

$p_{predicted} = \frac{1}{1 + e^{-(\beta_0 + \sum_{i=1}^n \beta_i X_{i, input})}}$

The component also attempts to calculate a confidence interval for the predicted probability, although this is currently limited to models with a single numeric predictor due to computational complexity.

## Examples

*(Note: Specific examples would require sample data. Below is a conceptual example.)*

Suppose you are predicting the probability of a customer clicking on an ad (1 = clicked, 0 = not clicked) based on their age (numeric) and location (categorical: Urban, Suburban, Rural).

After running the regression, you might get the following results:

| Parameter         | Estimate | p-value | Odds Ratio |
| :---------------- | :------- | :------ | :--------- |
| Intercept         | -3.5000  | < 0.001 | 0.0302     |
| Age               | 0.0500   | < 0.001 | 1.0513     |
| Location (Urban)  | 1.2000   | 0.015   | 3.3201     |
| Location (Suburban)| 0.8000   | 0.080   | 2.2255     |

*(Assuming Rural is the base category)*

**Interpretation:**

*   **Intercept:** When Age is 0 and Location is Rural (the base category), the log odds of clicking are -3.5000, corresponding to a probability of $1 / (1 + e^{3.5}) \approx 0.03$.
*   **Age:** For each one-year increase in age, the log odds of clicking increase by 0.0500. The odds ratio is 1.0513, meaning the odds of clicking increase by about 5.13% for each additional year of age, holding location constant. This effect is statistically significant (p < 0.001).
*   **Location (Urban):** Compared to the Rural base category, being in an Urban location increases the log odds of clicking by 1.2000. The odds ratio is 3.3201, meaning the odds of clicking are about 3.32 times higher in Urban areas compared to Rural areas, holding age constant. This effect is statistically significant (p = 0.015).
*   **Location (Suburban):** Compared to the Rural base category, being in a Suburban location increases the log odds of clicking by 0.8000. The odds ratio is 2.2255, meaning the odds of clicking are about 2.23 times higher in Suburban areas compared to Rural areas, holding age constant. This effect is not statistically significant (p = 0.080).

**Prediction Example:**

To predict the probability of clicking for a 40-year-old in a Suburban location:
*   Age = 40
*   Location = Suburban (Dummy for Suburban = 1, Dummy for Urban = 0)

$logit(p) = -3.5000 + (0.0500 \times 40) + (1.2000 \times 0) + (0.8000 \times 1)$  
$logit(p) = -3.5000 + 2.0000 + 0 + 0.8000$
$logit(p) = -0.7000$

$p = \frac{1}{1 + e^{-(-0.7000)}} = \frac{1}{1 + e^{0.7000}} \approx \frac{1}{1 + 2.0138} \approx \frac{1}{3.0138} \approx 0.3318$

The predicted probability of a 40-year-old in a Suburban location clicking the ad is approximately 0.3318 (or 33.18%). Since this is below 0.5, the model would classify this individual as "not clicked".

This tutorial provides a foundation for understanding the Logistic Regression component and its output. For more advanced statistical concepts or troubleshooting, refer to standard statistical resources.
