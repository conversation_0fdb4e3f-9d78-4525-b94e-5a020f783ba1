# Mediation and Moderation Analysis: Comprehensive Reference Guide

This comprehensive guide covers mediation and moderation analysis techniques for understanding complex relationships between variables. These methods are essential for testing theoretical mechanisms, identifying boundary conditions, and advancing causal understanding in behavioral and social sciences.

## Overview

Mediation and moderation analyses examine how and when variables are related. Mediation addresses the mechanism or process through which one variable affects another (the "how" question), while moderation examines the conditions under which relationships vary (the "when" or "for whom" question).

## Mediation Analysis

### 1. Simple Mediation Model

**Path Model:**
- X → M → Y (indirect path)
- X → Y (direct path)

**Regression Equations:**
$$M = i_1 + aX + e_1$$
$$Y = i_2 + c'X + bM + e_2$$

Where:
- a = effect of X on M
- b = effect of M on Y (controlling for X)
- c' = direct effect of X on Y
- c = total effect of X on Y

**Total Effect:**
$$c = c' + ab$$

### 2. Indirect Effect Estimation

**Product of Coefficients:**
$$ab = a \times b$$

**Standard Error (Sobel Test):**
$$SE_{ab} = \sqrt{a^2 s_b^2 + b^2 s_a^2}$$

**Sobel Test Statistic:**
$$z = \frac{ab}{SE_{ab}}$$

**Limitations:**
- Assumes normal distribution of ab
- Low power for small effects
- Not recommended for inference

### 3. Bootstrap Confidence Intervals

**Procedure:**
1. Resample data with replacement (B times)
2. Estimate ab for each bootstrap sample
3. Create empirical distribution of ab
4. Determine percentile-based confidence intervals

**Bias-Corrected and Accelerated (BCa):**
$$CI_{BCa} = [\Phi^{-1}(\alpha_1), \Phi^{-1}(\alpha_2)]$$

Where:
$$\alpha_1 = \Phi\left(z_0 + \frac{z_0 + z_{\alpha/2}}{1 - a(z_0 + z_{\alpha/2})}\right)$$

**Advantages:**
- No normality assumption
- Higher power than Sobel test
- Asymmetric confidence intervals

### 4. Multiple Mediation

**Parallel Mediators:**
$$Y = i + c'X + b_1M_1 + b_2M_2 + ... + b_kM_k + e$$

**Specific Indirect Effects:**
$$a_i b_i$$ for mediator i

**Total Indirect Effect:**
$$\sum_{i=1}^k a_i b_i$$

**Serial Mediation:**
$$X \rightarrow M_1 \rightarrow M_2 \rightarrow Y$$

**Indirect Effects:**
- $a_1 b_1$: Through $M_1$ only
- $a_2 b_2$: Through $M_2$ only  
- $a_1 d_{21} b_2$: Through $M_1$ then $M_2$

## Moderation Analysis

### 1. Simple Moderation Model

**Regression Equation:**
$$Y = i + b_1X + b_2W + b_3XW + e$$

Where:
- $b_1$ = effect of X when W = 0
- $b_2$ = effect of W when X = 0
- $b_3$ = interaction effect (moderation)

**Conditional Effects:**
$$\theta_{X \rightarrow Y} = b_1 + b_3W$$

**Simple Slopes:**
- At W = -1 SD: $b_1 - b_3 \sigma_W$
- At W = Mean: $b_1$
- At W = +1 SD: $b_1 + b_3 \sigma_W$

### 2. Centering Variables

**Mean Centering:**
$$X_c = X - \bar{X}$$
$$W_c = W - \bar{W}$$

**Benefits:**
- Reduces multicollinearity
- Makes lower-order terms interpretable
- Doesn't affect interaction term

**Grand Mean Centering vs. Group Mean Centering:**
- Grand mean: For between-subjects designs
- Group mean: For within-subjects/multilevel designs

### 3. Probing Interactions

**Johnson-Neyman Technique:**
Find values of W where conditional effect is significant:

$$W_{JN} = \frac{-b_1 \pm t_{\alpha/2,df} \sqrt{s_{b_1}^2 + 2b_1 s_{b_1,b_3} + b_1^2 s_{b_3}^2}}{b_3}$$

**Regions of Significance:**
- Values of W where effect is significant
- More informative than simple slopes

**Spotlight Analysis:**
- Test conditional effects at specific W values
- Theoretically meaningful values
- Percentiles of W distribution

### 4. Categorical Moderators

**Dummy Coding:**
$$Y = i + b_1X + b_2D_1 + b_3D_2 + b_4XD_1 + b_5XD_2 + e$$

For 3-group moderator with reference group.

**Effect Coding:**
- Sum of codes equals zero
- Coefficients represent deviations from grand mean

**Omnibus Test:**
$$F = \frac{(SSE_R - SSE_F)/(df_R - df_F)}{SSE_F/df_F}$$

## Moderated Mediation

### 1. Conceptual Framework

**First Stage Moderation:**
$$M = i_1 + a_1X + a_2W + a_3XW + e_1$$
$$Y = i_2 + c'X + bM + e_2$$

**Conditional Indirect Effect:**
$$\theta_{X \rightarrow M \rightarrow Y} = (a_1 + a_3W)b$$

**Second Stage Moderation:**
$$M = i_1 + aX + e_1$$
$$Y = i_2 + c'X + b_1M + b_2W + b_3MW + e_2$$

**Conditional Indirect Effect:**
$$\theta_{X \rightarrow M \rightarrow Y} = a(b_1 + b_3W)$$

### 2. Index of Moderated Mediation

**Definition:**
$$\omega = a_3b$$ (for first stage moderation)
$$\omega = ab_3$$ (for second stage moderation)

**Interpretation:**
- Quantifies how much mediation changes across moderator values
- Bootstrap confidence intervals for inference

### 3. Conditional Process Analysis

**Integrated Model:**
$$M = i_1 + a_1X + a_2W + a_3XW + e_1$$
$$Y = i_2 + c'X + b_1M + b_2W + b_3MW + b_4XW + e_2$$

**Multiple Conditional Effects:**
- Direct effect moderation: $b_4$
- Indirect effect moderation: $a_3b_1 + ab_3$

## Mediated Moderation

### 1. Conceptual Model

**Process:**
1. X and W interact to affect M
2. M mediates the X×W effect on Y

**Equations:**
$$M = i_1 + a_1X + a_2W + a_3XW + e_1$$
$$Y = i_2 + c'X + c_2W + bM + e_2$$

**Mediated Moderation Effect:**
$$a_3b$$

### 2. Interpretation

**Meaning:**
- How much of the X×W interaction on Y is mediated by M
- Explains mechanism of moderation effect

**Testing:**
- Bootstrap confidence intervals for $a_3b$
- Compare total interaction effect to mediated portion

## Advanced Topics

### 1. Multilevel Mediation

**2-1-1 Model:**
- Level 2 X → Level 1 M → Level 1 Y

**1-1-1 Model:**
- All variables at Level 1
- Random slopes for mediation paths

**Cross-Level Interactions:**
$$M_{ij} = \beta_{0j} + \beta_{1j}X_{ij} + r_{ij}$$
$$\beta_{1j} = \gamma_{10} + \gamma_{11}W_j + u_{1j}$$

### 2. Longitudinal Mediation

**Autoregressive Models:**
$$M_t = \alpha_1 M_{t-1} + a X_{t-1} + e_{1t}$$
$$Y_t = \alpha_2 Y_{t-1} + \beta_1 M_{t-1} + c' X_{t-1} + e_{2t}$$

**Cross-Lagged Panel Models:**
- Bidirectional relationships
- Temporal precedence
- Stability coefficients

### 3. Causal Mediation Analysis

**Potential Outcomes Framework:**
$$Y_i(t,m) = \text{potential outcome for unit i under treatment t and mediator value m}$$

**Natural Direct Effect (NDE):**
$$\delta(t) = E[Y_i(t,M_i(0)) - Y_i(0,M_i(0))]$$

**Natural Indirect Effect (NIE):**
$$\zeta(t) = E[Y_i(t,M_i(t)) - Y_i(t,M_i(0))]$$

**Assumptions:**
- No unmeasured confounding
- No mediator-outcome confounders affected by treatment

## Effect Sizes

### 1. Mediation Effect Sizes

**Proportion Mediated:**
$$P_M = \frac{ab}{c} = \frac{ab}{c' + ab}$$

**Ratio of Indirect to Direct Effect:**
$$\frac{ab}{c'}$$

**Completely Standardized Indirect Effect:**
$$ab_{cs} = a_{cs} \times b_{cs}$$

### 2. Moderation Effect Sizes

**Proportion of Variance Explained by Interaction:**
$$f^2 = \frac{R^2_{full} - R^2_{main}}{1 - R^2_{full}}$$

**Cohen's Guidelines:**
- Small: $f^2 = 0.02$
- Medium: $f^2 = 0.15$
- Large: $f^2 = 0.35$

## Assumptions and Diagnostics

### 1. Mediation Assumptions

**Temporal Precedence:**
- X precedes M precedes Y
- Longitudinal or experimental design preferred

**No Omitted Variables:**
- No unmeasured confounders of X-M, M-Y, or X-Y relationships
- Sensitivity analyses recommended

**Measurement Reliability:**
- Unreliable measures attenuate effects
- Correct for measurement error when possible

### 2. Moderation Assumptions

**Linearity:**
- Linear relationships between variables
- Check with scatterplots and residual plots

**Homoscedasticity:**
- Constant error variance across moderator values
- Breusch-Pagan test

**Normality:**
- Normal distribution of residuals
- Robust methods if violated

## Practical Guidelines

### 1. Sample Size Planning

**Mediation Analysis:**
- Minimum 200 for simple mediation
- Larger samples for multiple mediators
- Power depends on effect sizes of a and b paths

**Moderation Analysis:**
- Minimum 100 for detecting large interactions
- 200+ for medium interactions
- 400+ for small interactions

### 2. Variable Preparation

**Scaling:**
- Standardize continuous variables if desired
- Consider meaningful zero points

**Missing Data:**
- Multiple imputation preferred
- Avoid listwise deletion

**Outliers:**
- Examine influence on interaction terms
- Consider robust methods

### 3. Model Building Strategy

**Theory-Driven Approach:**
- Specify models based on theory
- Avoid exploratory fishing

**Model Comparison:**
- Test nested models
- Use information criteria for non-nested models

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Theoretical rationale for mediation/moderation
- Variable measurement and coding
- Analysis software and procedures
- Bootstrap specifications

### 2. Results Section

**Mediation Results:**
- Path coefficients with confidence intervals
- Indirect effects with bootstrap CIs
- Total and direct effects
- Effect sizes

**Moderation Results:**
- Interaction term significance
- Simple slopes analysis
- Regions of significance (if applicable)
- Interaction plots

### 3. Example Reporting

**Mediation:** "Bootstrap analysis (5,000 samples) revealed a significant indirect effect of training on performance through self-efficacy (ab = 0.23, 95% CI: 0.12-0.36). The direct effect remained significant (c' = 0.31, p < 0.01), indicating partial mediation. The indirect effect accounted for 43% of the total effect."

**Moderation:** "The interaction between stress and social support significantly predicted well-being (b = 0.18, p < 0.05). Simple slopes analysis revealed that stress was negatively related to well-being at low support (b = -0.42, p < 0.001) but not at high support (b = -0.06, p = 0.52). The Johnson-Neyman technique identified the region of significance for support scores below 4.2."

This comprehensive guide provides the foundation for conducting and interpreting mediation and moderation analyses in psychological, social, and behavioral research.
