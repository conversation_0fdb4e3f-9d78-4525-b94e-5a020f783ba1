# Survival Analysis: Comprehensive Reference Guide

This comprehensive guide covers survival analysis methods for analyzing time-to-event data, including <PERSON><PERSON><PERSON> estimation, log-rank tests, and Cox proportional hazards regression. These techniques are essential for medical research, reliability engineering, and any field studying duration until an event occurs.

## Overview

Survival analysis is a collection of statistical methods for analyzing data where the outcome variable is the time until an event occurs. The event can be death, disease recurrence, equipment failure, or any other occurrence of interest. Survival analysis handles censored data, where the event time is not observed for all subjects.

## Fundamental Concepts

### 1. Survival Function

**Definition:**
$$S(t) = P(T > t)$$

Where T is the random variable representing survival time.

**Properties:**
- $S(0) = 1$ (all subjects alive at start)
- $S(\infty) = 0$ (eventually all experience event)
- S(t) is non-increasing function

**Relationship to Distribution Function:**
$$S(t) = 1 - F(t)$$

### 2. Hazard Function

**Instantaneous Risk:**
$$h(t) = \lim_{\Delta t \to 0} \frac{P(t \leq T < t + \Delta t | T \geq t)}{\Delta t}$$

**Alternative Form:**
$$h(t) = \frac{f(t)}{S(t)} = -\frac{d}{dt}\ln S(t)$$

Where f(t) is the probability density function.

**Cumulative Hazard:**
$$H(t) = \int_0^t h(u) du = -\ln S(t)$$

### 3. Types of Censoring

**Right Censoring:**
- Most common type
- Event time is greater than observed time
- Occurs due to study end or loss to follow-up

**Left Censoring:**
- Event occurred before observation began
- Less common in practice

**Interval Censoring:**
- Event occurred within a time interval
- Exact time unknown

## Kaplan-Meier Estimation

### 1. Non-Parametric Estimator

**Kaplan-Meier Formula:**
$$\hat{S}(t) = \prod_{t_i \leq t} \left(1 - \frac{d_i}{n_i}\right)$$

Where:
- $t_i$ = distinct event times
- $d_i$ = number of events at time $t_i$
- $n_i$ = number at risk at time $t_i$

**Step Function:**
- Decreases only at event times
- Constant between events
- Jumps proportional to number of events

### 2. Variance Estimation

**Greenwood's Formula:**
$$\text{Var}[\hat{S}(t)] = [\hat{S}(t)]^2 \sum_{t_i \leq t} \frac{d_i}{n_i(n_i - d_i)}$$

**Standard Error:**
$$SE[\hat{S}(t)] = \sqrt{\text{Var}[\hat{S}(t)]}$$

**Confidence Intervals:**
$$\hat{S}(t) \pm z_{\alpha/2} \cdot SE[\hat{S}(t)]$$

### 3. Median Survival Time

**Definition:**
$$\hat{t}_{50} = \inf\{t: \hat{S}(t) \leq 0.5\}$$

**Confidence Interval:**
- Based on confidence bands for S(t)
- May be wide or undefined if few events

## Log-Rank Test

### 1. Two-Group Comparison

**Null Hypothesis:**
$$H_0: S_1(t) = S_2(t) \text{ for all } t$$

**Test Statistic:**
$$Z = \frac{\sum_{i=1}^k (O_{1i} - E_{1i})}{\sqrt{\sum_{i=1}^k V_i}}$$

Where:
- $O_{1i}$ = observed events in group 1 at time $t_i$
- $E_{1i}$ = expected events in group 1 at time $t_i$
- $V_i$ = variance at time $t_i$

**Expected Events:**
$$E_{1i} = \frac{n_{1i} \cdot d_i}{n_i}$$

**Variance:**
$$V_i = \frac{n_{1i} \cdot n_{2i} \cdot d_i \cdot (n_i - d_i)}{n_i^2 \cdot (n_i - 1)}$$

### 2. Multi-Group Extension

**Chi-Square Statistic:**
$$\chi^2 = \mathbf{(O - E)' V^{-1} (O - E)}$$

Where:
- $\mathbf{O}$ = vector of observed events
- $\mathbf{E}$ = vector of expected events
- $\mathbf{V}$ = covariance matrix

**Degrees of Freedom:** g - 1 (where g = number of groups)

### 3. Weighted Log-Rank Tests

**Wilcoxon Test:**
- Weights early failures more heavily
- Weight = $n_i$

**Tarone-Ware Test:**
- Intermediate weighting
- Weight = $\sqrt{n_i}$

**Fleming-Harrington Test:**
- General weight function: $w(t) = S(t)^p[1-S(t)]^q$

## Cox Proportional Hazards Model

### 1. Model Specification

**Hazard Function:**
$$h(t|x) = h_0(t) \exp(\beta_1 x_1 + \beta_2 x_2 + ... + \beta_p x_p)$$

Where:
- $h_0(t)$ = baseline hazard function
- $\mathbf{x}$ = vector of covariates
- $\boldsymbol{\beta}$ = vector of regression coefficients

**Hazard Ratio:**
$$HR = \frac{h(t|x_1)}{h(t|x_0)} = \exp(\boldsymbol{\beta}'\mathbf{(x_1 - x_0)})$$

### 2. Partial Likelihood

**Likelihood Function:**
$$L(\boldsymbol{\beta}) = \prod_{i=1}^D \frac{\exp(\boldsymbol{\beta}'\mathbf{x}_i)}{\sum_{j \in R(t_i)} \exp(\boldsymbol{\beta}'\mathbf{x}_j)}$$

Where:
- D = total number of events
- $R(t_i)$ = risk set at time $t_i$

**Log-Likelihood:**
$$\ell(\boldsymbol{\beta}) = \sum_{i=1}^D \left[\boldsymbol{\beta}'\mathbf{x}_i - \ln\left(\sum_{j \in R(t_i)} \exp(\boldsymbol{\beta}'\mathbf{x}_j)\right)\right]$$

### 3. Parameter Estimation

**Score Function:**
$$U(\boldsymbol{\beta}) = \frac{\partial \ell(\boldsymbol{\beta})}{\partial \boldsymbol{\beta}} = \sum_{i=1}^D \left[\mathbf{x}_i - \frac{\sum_{j \in R(t_i)} \mathbf{x}_j \exp(\boldsymbol{\beta}'\mathbf{x}_j)}{\sum_{j \in R(t_i)} \exp(\boldsymbol{\beta}'\mathbf{x}_j)}\right]$$

**Information Matrix:**
$$I(\boldsymbol{\beta}) = -\frac{\partial^2 \ell(\boldsymbol{\beta})}{\partial \boldsymbol{\beta} \partial \boldsymbol{\beta}'}$$

**Newton-Raphson Algorithm:**
$$\boldsymbol{\beta}^{(k+1)} = \boldsymbol{\beta}^{(k)} + I(\boldsymbol{\beta}^{(k)})^{-1} U(\boldsymbol{\beta}^{(k)})$$

### 4. Inference

**Wald Test:**
$$Z = \frac{\hat{\beta}_j}{SE(\hat{\beta}_j)} \sim N(0,1)$$

**Likelihood Ratio Test:**
$$LR = 2[\ell(\hat{\boldsymbol{\beta}}) - \ell(\boldsymbol{\beta}_0)] \sim \chi^2_p$$

**Confidence Intervals:**
$$\hat{\beta}_j \pm z_{\alpha/2} \cdot SE(\hat{\beta}_j)$$

**Hazard Ratio CI:**
$$\exp(\hat{\beta}_j \pm z_{\alpha/2} \cdot SE(\hat{\beta}_j))$$

## Model Assessment and Diagnostics

### 1. Proportional Hazards Assumption

**Schoenfeld Residuals:**
$$r_{ij} = \delta_i \left[x_{ij} - \frac{\sum_{k \in R(t_i)} x_{kj} \exp(\hat{\boldsymbol{\beta}}'\mathbf{x}_k)}{\sum_{k \in R(t_i)} \exp(\hat{\boldsymbol{\beta}}'\mathbf{x}_k)}\right]$$

**Test for Proportionality:**
- Plot scaled Schoenfeld residuals vs. time
- Test correlation with time
- Global test: $\chi^2$ with p degrees of freedom

**Time-Varying Coefficients:**
$$h(t|x) = h_0(t) \exp[\beta(t) x]$$

### 2. Model Fit Assessment

**Martingale Residuals:**
$$M_i = \delta_i - \hat{H}_0(t_i) \exp(\hat{\boldsymbol{\beta}}'\mathbf{x}_i)$$

**Deviance Residuals:**
$$D_i = \text{sign}(M_i) \sqrt{-2[M_i + \delta_i \ln(\delta_i - M_i)]}$$

**Cox-Snell Residuals:**
$$r_{CS,i} = \hat{H}_0(t_i) \exp(\hat{\boldsymbol{\beta}}'\mathbf{x}_i)$$

### 3. Influential Observations

**DFBETA:**
- Change in coefficient when observation i is deleted
- Standardized version: $|DFBETA| > 2/\sqrt{n}$

**Likelihood Displacement:**
$$LD_i = 2[\ell(\hat{\boldsymbol{\beta}}) - \ell(\hat{\boldsymbol{\beta}}_{(i)})]$$

## Advanced Topics

### 1. Stratified Cox Model

**Model:**
$$h_g(t|x) = h_{0g}(t) \exp(\boldsymbol{\beta}'\mathbf{x})$$

Where g indexes strata.

**Use Cases:**
- Non-proportional hazards for stratification variable
- Different baseline hazards across groups

### 2. Frailty Models

**Shared Frailty:**
$$h_{ij}(t|x_{ij}, w_i) = w_i h_0(t) \exp(\boldsymbol{\beta}'\mathbf{x}_{ij})$$

Where $w_i$ is the frailty term for cluster i.

**Gamma Frailty:**
- $w_i \sim \text{Gamma}(\theta, \theta)$
- Accounts for unobserved heterogeneity

### 3. Competing Risks

**Cause-Specific Hazard:**
$$h_k(t) = \lim_{\Delta t \to 0} \frac{P(t \leq T < t + \Delta t, \delta = k | T \geq t)}{\Delta t}$$

**Cumulative Incidence Function:**
$$F_k(t) = \int_0^t S(u) h_k(u) du$$

**Fine-Gray Model:**
- Models subdistribution hazard
- Treats competing events as censored

## Parametric Survival Models

### 1. Exponential Model

**Hazard Function:**
$$h(t) = \lambda$$

**Survival Function:**
$$S(t) = \exp(-\lambda t)$$

**Mean Survival Time:**
$$E[T] = \frac{1}{\lambda}$$

### 2. Weibull Model

**Hazard Function:**
$$h(t) = \frac{\gamma}{\lambda}\left(\frac{t}{\lambda}\right)^{\gamma-1}$$

**Survival Function:**
$$S(t) = \exp\left[-\left(\frac{t}{\lambda}\right)^\gamma\right]$$

**Shape Parameter Interpretation:**
- γ < 1: Decreasing hazard
- γ = 1: Constant hazard (exponential)
- γ > 1: Increasing hazard

### 3. Log-Normal Model

**Hazard Function:**
$$h(t) = \frac{\phi\left(\frac{\ln t - \mu}{\sigma}\right)}{\sigma t \left[1 - \Phi\left(\frac{\ln t - \mu}{\sigma}\right)\right]}$$

Where φ and Φ are standard normal PDF and CDF.

## Practical Guidelines

### 1. Study Design Considerations

**Sample Size Calculation:**
- Based on expected hazard ratio
- Number of events, not sample size
- Account for censoring rate

**Follow-up Duration:**
- Sufficient events for stable estimates
- Balance between information and cost

### 2. Data Preparation

**Time Scale Selection:**
- Calendar time vs. time since entry
- Age as time scale for age-related events

**Covariate Coding:**
- Reference categories for categorical variables
- Centering continuous variables
- Interaction terms

### 3. Model Building Strategy

**Variable Selection:**
- Clinical/theoretical importance
- Statistical significance
- Avoid overfitting

**Model Comparison:**
- Likelihood ratio tests for nested models
- AIC/BIC for non-nested models
- Cross-validation

## Reporting Guidelines

### 1. Method Section

**Essential Elements:**
- Study design and follow-up
- Event definition and ascertainment
- Censoring mechanism
- Statistical methods used

### 2. Results Section

**Kaplan-Meier Analysis:**
- Median survival times with CI
- Survival probabilities at key time points
- Log-rank test results

**Cox Regression:**
- Hazard ratios with 95% CI
- P-values for individual coefficients
- Overall model significance
- Assumption checking results

### 3. Example Reporting

"Median overall survival was 24.3 months (95% CI: 18.7-31.2) in the treatment group versus 18.1 months (95% CI: 14.2-22.8) in the control group (log-rank p = 0.032). In multivariable Cox regression, treatment was associated with reduced mortality risk (HR = 0.73, 95% CI: 0.55-0.97, p = 0.031) after adjusting for age, stage, and performance status. The proportional hazards assumption was satisfied (global test p = 0.18)."

This comprehensive guide provides the foundation for conducting and interpreting survival analysis in medical research, reliability studies, and other time-to-event applications.
