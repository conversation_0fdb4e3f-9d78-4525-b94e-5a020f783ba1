# T-Tests and Alternatives: Comprehensive Reference Guide

This comprehensive guide covers t-tests and their non-parametric alternatives, providing detailed explanations, formulas, assumptions, and interpretation guidelines for statistical hypothesis testing.

## Overview

T-tests are parametric statistical tests used to compare means and determine if observed differences are statistically significant. When t-test assumptions are violated, non-parametric alternatives provide robust solutions for hypothesis testing.

## Types of T-Tests

### 1. One-Sample T-Test

**Purpose:** Tests whether a sample mean differs significantly from a known population mean (μ₀).

**Null Hypothesis:** H₀: μ = μ₀  
**Alternative Hypothesis:** H₁: μ ≠ μ₀ (two-tailed) or H₁: μ > μ₀ or H₁: μ < μ₀ (one-tailed)

**Formula:**
$$t = \frac{\bar{x} - \mu_0}{s / \sqrt{n}}$$

Where:
- $\bar{x}$ = sample mean
- $\mu_0$ = hypothesized population mean
- $s$ = sample standard deviation
- $n$ = sample size

**Degrees of Freedom:** df = n - 1

**Effect Size (<PERSON>'s d):**
$$d = \frac{\bar{x} - \mu_0}{s}$$

**Interpretation:**
- Small effect: |d| = 0.2
- Medium effect: |d| = 0.5
- Large effect: |d| = 0.8

### 2. Independent Samples T-Test (Two-Sample)

**Purpose:** Compares means between two independent groups.

**Null Hypothesis:** H₀: μ₁ = μ₂  
**Alternative Hypothesis:** H₁: μ₁ ≠ μ₂ (two-tailed) or H₁: μ₁ > μ₂ or H₁: μ₁ < μ₂ (one-tailed)

#### Equal Variances Assumed (Pooled t-test):
$$t = \frac{\bar{x_1} - \bar{x_2}}{s_p \sqrt{\frac{1}{n_1} + \frac{1}{n_2}}}$$

**Pooled Standard Deviation:**
$$s_p = \sqrt{\frac{(n_1-1)s_1^2 + (n_2-1)s_2^2}{n_1 + n_2 - 2}}$$

**Degrees of Freedom:** df = n₁ + n₂ - 2

#### Unequal Variances (Welch's t-test):
$$t = \frac{\bar{x_1} - \bar{x_2}}{\sqrt{\frac{s_1^2}{n_1} + \frac{s_2^2}{n_2}}}$$

**Degrees of Freedom (Welch-Satterthwaite):**
$$df = \frac{(\frac{s_1^2}{n_1} + \frac{s_2^2}{n_2})^2}{\frac{(s_1^2/n_1)^2}{n_1-1} + \frac{(s_2^2/n_2)^2}{n_2-1}}$$

**Effect Size (Cohen's d):**
$$d = \frac{\bar{x_1} - \bar{x_2}}{s_p}$$ (for equal variances)

### 3. Paired Samples T-Test (Dependent Samples)

**Purpose:** Compares means of the same subjects measured at two different times or under two different conditions.

**Null Hypothesis:** H₀: μd = 0  
**Alternative Hypothesis:** H₁: μd ≠ 0 (two-tailed) or H₁: μd > 0 or H₁: μd < 0 (one-tailed)

**Formula:**
$$t = \frac{\bar{d}}{s_d / \sqrt{n}}$$

Where:
- $\bar{d}$ = mean of differences (d = x₁ - x₂)
- $s_d$ = standard deviation of differences
- $n$ = number of pairs

**Degrees of Freedom:** df = n - 1

**Effect Size (Cohen's d):**
$$d = \frac{\bar{d}}{s_d}$$

## T-Test Assumptions

### 1. Normality
- **One-sample & Paired:** Sample differences should be approximately normally distributed
- **Independent samples:** Each group should be approximately normally distributed
- **Assessment:** Shapiro-Wilk test, Q-Q plots, histograms
- **Robustness:** T-tests are relatively robust to normality violations with larger samples (n > 30)

### 2. Independence
- **Critical assumption:** Observations must be independent of each other
- **Violations:** Clustered data, repeated measures, time series data
- **Solutions:** Use appropriate statistical models (mixed-effects, repeated measures ANOVA)

### 3. Equal Variances (Homoscedasticity)
- **Applies to:** Independent samples t-test only
- **Assessment:** Levene's test, F-test for equality of variances
- **Solution:** Use Welch's t-test when variances are unequal

### 4. Continuous Data
- **Requirement:** Dependent variable should be measured at interval or ratio level
- **Alternatives:** Non-parametric tests for ordinal data

## Non-Parametric Alternatives

When t-test assumptions are violated, non-parametric tests provide robust alternatives that don't assume normal distributions.

### 1. Sign Test (Alternative to One-Sample T-Test)

**Purpose:** Tests whether the median of a population differs from a hypothesized value.

**Assumptions:**
- Data are paired or single sample
- Measurement scale is at least ordinal
- No assumption of normality

**Procedure:**
1. Calculate differences from hypothesized median
2. Count positive and negative differences (ignore zeros)
3. Use binomial distribution with p = 0.5

**Test Statistic:** Number of positive (or negative) differences
**Distribution:** Binomial(n, 0.5) where n = number of non-zero differences

**When to Use:**
- Small sample sizes
- Severely non-normal data
- Ordinal data
- Presence of outliers

### 2. Mann-Whitney U Test (Alternative to Independent Samples T-Test)

**Purpose:** Tests whether two independent samples come from populations with the same distribution.

**Null Hypothesis:** The distributions of both groups are equal
**Alternative Hypothesis:** The distributions differ (often interpreted as difference in medians)

**Assumptions:**
- Two independent samples
- Ordinal or continuous data
- Similar distribution shapes (for median comparison)

**Test Statistic:**
$$U_1 = n_1 n_2 + \frac{n_1(n_1+1)}{2} - R_1$$
$$U_2 = n_1 n_2 + \frac{n_2(n_2+1)}{2} - R_2$$

Where:
- $R_1$, $R_2$ = sum of ranks for groups 1 and 2
- $U = \min(U_1, U_2)$

**Effect Size (r):**
$$r = \frac{Z}{\sqrt{N}}$$
Where Z is the standardized test statistic and N is total sample size.

**Interpretation:**
- Small effect: r = 0.1
- Medium effect: r = 0.3
- Large effect: r = 0.5

### 3. Wilcoxon Signed-Rank Test (Alternative to Paired Samples T-Test)

**Purpose:** Tests whether the median difference between paired observations is zero.

**Assumptions:**
- Paired data
- Differences are continuous
- Differences are symmetrically distributed around the median
- At least ordinal scale

**Procedure:**
1. Calculate differences for each pair
2. Rank absolute differences (exclude zeros)
3. Sum ranks for positive and negative differences
4. Test statistic is the smaller sum

**Test Statistic:** W = smaller of W⁺ (sum of positive ranks) or W⁻ (sum of negative ranks)

**Effect Size (r):**
$$r = \frac{Z}{\sqrt{N}}$$

## Choosing the Right Test

### Decision Tree

1. **One sample or two samples?**
   - One sample → One-sample t-test vs. Sign test
   - Two samples → Continue to step 2

2. **Independent or paired samples?**
   - Independent → Independent samples t-test vs. Mann-Whitney U
   - Paired → Paired samples t-test vs. Wilcoxon signed-rank

3. **Check assumptions:**
   - **Normality:** Shapiro-Wilk test, visual inspection
   - **Equal variances:** Levene's test (for independent samples)
   - **Sample size:** Larger samples (n > 30) are more robust

4. **Select test:**
   - All assumptions met → Use t-test
   - Normality violated → Use non-parametric alternative
   - Unequal variances (independent samples) → Use Welch's t-test

### Sample Size Considerations

**T-tests:**
- Minimum: n ≥ 30 per group for robustness
- Power analysis recommended for optimal sample size

**Non-parametric tests:**
- Generally require larger samples for equivalent power
- More robust with small samples when assumptions are violated

## Interpretation Guidelines

### Statistical Significance
- **p-value < 0.05:** Statistically significant (reject H₀)
- **p-value ≥ 0.05:** Not statistically significant (fail to reject H₀)
- **Confidence intervals:** If CI doesn't include null value, result is significant

### Practical Significance
- **Effect size:** Magnitude of difference (Cohen's d, r)
- **Clinical/practical importance:** Consider real-world relevance
- **Confidence intervals:** Provide range of plausible values

### Reporting Results

**T-test example:**
"An independent samples t-test revealed a statistically significant difference between groups, t(48) = 3.21, p = 0.002, Cohen's d = 0.92, indicating a large effect size."

**Non-parametric example:**
"A Mann-Whitney U test indicated that Group 1 scores (Mdn = 75) were significantly higher than Group 2 scores (Mdn = 68), U = 234, p = 0.031, r = 0.31."

## Common Pitfalls and Solutions

### 1. Multiple Comparisons
- **Problem:** Increased Type I error rate with multiple tests
- **Solutions:** Bonferroni correction, FDR control, planned comparisons

### 2. Assumption Violations
- **Problem:** Invalid results when assumptions aren't met
- **Solutions:** Check assumptions, use appropriate alternatives, transform data

### 3. Effect Size Neglect
- **Problem:** Focusing only on p-values
- **Solutions:** Always report effect sizes and confidence intervals

### 4. Sample Size Issues
- **Problem:** Underpowered studies or overpowered trivial effects
- **Solutions:** Conduct power analysis, consider practical significance

## Advanced Considerations

### Robust Statistics
- **Trimmed means:** Remove extreme values
- **Bootstrap methods:** Resampling techniques
- **Permutation tests:** Distribution-free alternatives

### Bayesian Approaches
- **Bayesian t-tests:** Incorporate prior information
- **Bayes factors:** Evidence for H₀ vs. H₁
- **Credible intervals:** Bayesian confidence intervals

This comprehensive guide provides the foundation for understanding and applying t-tests and their alternatives. For specific implementation in statistical software, consult the relevant documentation and consider the context of your research question.
