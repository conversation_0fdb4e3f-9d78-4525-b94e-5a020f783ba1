import { useState, useEffect, useCallback, useRef } from 'react';
import { useRegisterSW } from 'virtual:pwa-register/react';
import { cacheManager, recoverFromCacheIssues } from '../utils/cacheManager';

export interface PWAUpdateState {
  // Update availability
  needRefresh: boolean;
  offlineReady: boolean;

  // Update process state
  isUpdating: boolean;
  updateProgress: number;
  updateError: string | null;

  // Version information
  currentVersion: string;
  availableVersion: string | null;
  lastUpdateCheck: Date | null;

  // User preferences
  autoUpdateEnabled: boolean;
  updateNotificationDismissed: boolean;

  // Offline notification management
  offlineReadyShown: boolean;
  sessionId: string;
}

export interface PWAUpdateActions {
  // Update actions
  updateServiceWorker: () => Promise<void>;
  checkForUpdates: () => Promise<void>;
  dismissUpdateNotification: () => void;

  // Settings
  setAutoUpdateEnabled: (enabled: boolean) => void;

  // Manual refresh
  forceRefresh: () => void;

  // Offline notification management
  markOfflineReadyAsShown: () => void;
  resetOfflineReadyNotification: () => void;
}

const STORAGE_KEYS = {
  AUTO_UPDATE: 'pwa-auto-update-enabled',
  LAST_UPDATE_CHECK: 'pwa-last-update-check',
  UPDATE_DISMISSED: 'pwa-update-dismissed',
  CURRENT_VERSION: 'pwa-current-version',
  OFFLINE_READY_SHOWN: 'pwa-offline-ready-shown',
  SESSION_ID: 'pwa-session-id'
};

// Get app version from build-time constants
const getAppVersion = (): string => {
  try {
    return __APP_VERSION__ || '1.0.0';
  } catch {
    return import.meta.env.VITE_APP_VERSION || '1.0.0';
  }
};

// Generate a unique session ID
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Get or create session ID
const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem(STORAGE_KEYS.SESSION_ID);
  if (!sessionId) {
    sessionId = generateSessionId();
    sessionStorage.setItem(STORAGE_KEYS.SESSION_ID, sessionId);
  }
  return sessionId;
};

export const usePWAUpdate = (): PWAUpdateState & PWAUpdateActions => {
  const sessionId = getSessionId();

  const [state, setState] = useState<PWAUpdateState>({
    needRefresh: false,
    offlineReady: false,
    isUpdating: false,
    updateProgress: 0,
    updateError: null,
    currentVersion: getAppVersion(),
    availableVersion: null,
    lastUpdateCheck: null,
    autoUpdateEnabled: localStorage.getItem(STORAGE_KEYS.AUTO_UPDATE) !== 'false',
    updateNotificationDismissed: false,
    offlineReadyShown: false,
    sessionId
  });

  const updateTimeoutRef = useRef<NodeJS.Timeout>();
  const registrationRef = useRef<ServiceWorkerRegistration>();

  // Initialize PWA registration
  const {
    offlineReady: [offlineReady, setOfflineReady],
    needRefresh: [needRefresh, setNeedRefresh],
    updateServiceWorker: originalUpdateServiceWorker,
  } = useRegisterSW({
    onRegistered(registration: ServiceWorkerRegistration | undefined) {
      console.log('SW Registered:', registration);
      registrationRef.current = registration;
      
      // Set up periodic update checks
      if (registration && state.autoUpdateEnabled) {
        setupPeriodicUpdateCheck(registration);
      }
    },
    onRegisterError(error: any) {
      console.error('SW registration error:', error);
      setState(prev => ({
        ...prev,
        updateError: 'Failed to register service worker'
      }));
    },
    onNeedRefresh() {
      console.log('SW needs refresh');
      setState(prev => ({
        ...prev,
        needRefresh: true,
        availableVersion: 'New version available'
      }));
    },
    onOfflineReady() {
      console.log('SW offline ready');

      // Check if offline ready notification has been shown in this session or previously
      const offlineReadyShownKey = `${STORAGE_KEYS.OFFLINE_READY_SHOWN}_${sessionId}`;
      const hasBeenShownThisSession = sessionStorage.getItem(offlineReadyShownKey) === 'true';
      const hasBeenShownBefore = localStorage.getItem(STORAGE_KEYS.OFFLINE_READY_SHOWN) === 'true';

      setState(prev => ({
        ...prev,
        offlineReady: true,
        offlineReadyShown: hasBeenShownThisSession || hasBeenShownBefore
      }));
    }
  });

  // Setup periodic update checking
  const setupPeriodicUpdateCheck = useCallback((registration: ServiceWorkerRegistration) => {
    const checkInterval = 30 * 60 * 1000; // Check every 30 minutes
    
    const performUpdateCheck = async () => {
      try {
        await registration.update();
        setState(prev => ({
          ...prev,
          lastUpdateCheck: new Date()
        }));
        localStorage.setItem(STORAGE_KEYS.LAST_UPDATE_CHECK, new Date().toISOString());
      } catch (error) {
        console.error('Update check failed:', error);
      }
    };

    // Initial check after 5 minutes
    updateTimeoutRef.current = setTimeout(performUpdateCheck, 5 * 60 * 1000);
    
    // Then check periodically
    const intervalId = setInterval(performUpdateCheck, checkInterval);
    
    return () => {
      clearInterval(intervalId);
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // Manual update check
  const checkForUpdates = useCallback(async () => {
    if (!registrationRef.current) {
      setState(prev => ({
        ...prev,
        updateError: 'Service worker not registered'
      }));
      return;
    }

    setState(prev => ({
      ...prev,
      isUpdating: true,
      updateError: null,
      updateProgress: 0
    }));

    try {
      // Simulate progress for better UX
      setState(prev => ({ ...prev, updateProgress: 25 }));
      
      await registrationRef.current.update();
      
      setState(prev => ({ 
        ...prev, 
        updateProgress: 75,
        lastUpdateCheck: new Date()
      }));
      
      localStorage.setItem(STORAGE_KEYS.LAST_UPDATE_CHECK, new Date().toISOString());
      
      // Complete the progress
      setState(prev => ({ ...prev, updateProgress: 100 }));
      
      // Reset progress after a short delay
      setTimeout(() => {
        setState(prev => ({ 
          ...prev, 
          isUpdating: false, 
          updateProgress: 0 
        }));
      }, 1000);
      
    } catch (error) {
      console.error('Manual update check failed:', error);
      setState(prev => ({
        ...prev,
        isUpdating: false,
        updateProgress: 0,
        updateError: 'Failed to check for updates'
      }));
    }
  }, []);

  // Enhanced update service worker
  const updateServiceWorker = useCallback(async () => {
    setState(prev => ({
      ...prev,
      isUpdating: true,
      updateError: null,
      updateProgress: 0
    }));

    try {
      setState(prev => ({ ...prev, updateProgress: 50 }));
      
      await originalUpdateServiceWorker(true);
      
      setState(prev => ({ ...prev, updateProgress: 100 }));
      
      // Update version info
      const newVersion = getAppVersion();
      localStorage.setItem(STORAGE_KEYS.CURRENT_VERSION, newVersion);
      
      // Reset state after update
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          isUpdating: false,
          updateProgress: 0,
          needRefresh: false,
          currentVersion: newVersion,
          availableVersion: null
        }));
      }, 500);
      
    } catch (error) {
      console.error('Service worker update failed:', error);
      setState(prev => ({
        ...prev,
        isUpdating: false,
        updateProgress: 0,
        updateError: 'Failed to update application'
      }));
    }
  }, [originalUpdateServiceWorker]);

  // Dismiss update notification
  const dismissUpdateNotification = useCallback(() => {
    setState(prev => ({
      ...prev,
      updateNotificationDismissed: true
    }));
    localStorage.setItem(STORAGE_KEYS.UPDATE_DISMISSED, 'true');
  }, []);

  // Set auto-update preference
  const setAutoUpdateEnabled = useCallback((enabled: boolean) => {
    setState(prev => ({
      ...prev,
      autoUpdateEnabled: enabled
    }));
    localStorage.setItem(STORAGE_KEYS.AUTO_UPDATE, enabled.toString());
    
    if (enabled && registrationRef.current) {
      setupPeriodicUpdateCheck(registrationRef.current);
    }
  }, [setupPeriodicUpdateCheck]);

  // Force refresh with cache recovery
  const forceRefresh = useCallback(async () => {
    try {
      // Attempt cache recovery before refresh
      await recoverFromCacheIssues();
      cacheManager.markForceRefresh();
    } catch (error) {
      console.warn('Cache recovery failed during force refresh:', error);
    } finally {
      // Always reload, even if cache recovery fails
      window.location.reload();
    }
  }, []);

  // Mark offline ready notification as shown
  const markOfflineReadyAsShown = useCallback(() => {
    const offlineReadyShownKey = `${STORAGE_KEYS.OFFLINE_READY_SHOWN}_${sessionId}`;
    sessionStorage.setItem(offlineReadyShownKey, 'true');
    localStorage.setItem(STORAGE_KEYS.OFFLINE_READY_SHOWN, 'true');

    setState(prev => ({
      ...prev,
      offlineReadyShown: true
    }));
  }, [sessionId]);

  // Reset offline ready notification (for testing purposes)
  const resetOfflineReadyNotification = useCallback(() => {
    const offlineReadyShownKey = `${STORAGE_KEYS.OFFLINE_READY_SHOWN}_${sessionId}`;
    sessionStorage.removeItem(offlineReadyShownKey);
    localStorage.removeItem(STORAGE_KEYS.OFFLINE_READY_SHOWN);

    setState(prev => ({
      ...prev,
      offlineReadyShown: false
    }));
  }, [sessionId]);

  // Update state when external values change
  useEffect(() => {
    setState(prev => ({
      ...prev,
      needRefresh,
      offlineReady
    }));
  }, [needRefresh, offlineReady]);

  // Load saved preferences on mount and perform cache recovery if needed
  useEffect(() => {
    const initializePWA = async () => {
      // Check for cache issues and recover if necessary
      if (cacheManager.shouldForceRefresh()) {
        console.log('Performing cache recovery due to detected issues');
        await recoverFromCacheIssues();
        cacheManager.markForceRefresh();
      }

      // Load saved preferences
      const lastUpdateCheck = localStorage.getItem(STORAGE_KEYS.LAST_UPDATE_CHECK);
      const updateDismissed = localStorage.getItem(STORAGE_KEYS.UPDATE_DISMISSED) === 'true';
      const savedVersion = localStorage.getItem(STORAGE_KEYS.CURRENT_VERSION);

      setState(prev => ({
        ...prev,
        lastUpdateCheck: lastUpdateCheck ? new Date(lastUpdateCheck) : null,
        updateNotificationDismissed: updateDismissed,
        currentVersion: savedVersion || prev.currentVersion
      }));

      // Perform periodic cache maintenance
      try {
        const clearedCount = await cacheManager.clearStaleCaches();
        if (clearedCount > 0) {
          console.log(`Cleared ${clearedCount} stale caches during initialization`);
        }
      } catch (error) {
        console.warn('Cache maintenance failed:', error);
      }
    };

    initializePWA();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...state,
    updateServiceWorker,
    checkForUpdates,
    dismissUpdateNotification,
    setAutoUpdateEnabled,
    forceRefresh,
    markOfflineReadyAsShown,
    resetOfflineReadyNotification
  };
};
