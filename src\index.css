/* Global styles for Statistica */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, 
body,
#root {
  height: 100%;
  width: 100%;
  overscroll-behavior: none; /* Prevent pull-to-refresh on mobile */
}

body {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  background-color: #f5f7fa;
  color: #333;
}

/* Improve touch targets on mobile */
@media (max-width: 600px) {
  button, 
  .MuiIconButton-root, 
  .MuiButton-root {
    min-height: 44px;
    min-width: 44px;
  }
  
  .MuiListItem-root {
    min-height: 48px;
  }
  
  .MuiInputBase-root {
    min-height: 44px;
  }
  
  /* Improve form field spacing */
  .MuiFormControl-root {
    margin-bottom: 16px;
  }
}

/* Improve scrolling behavior */
.scrollable {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Custom scrollbar styling */
.scrollable::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.scrollable::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.scrollable::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* Table responsive styling */
.responsive-table {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Prevent text selection on interactive elements */
button, 
a, 
.MuiIconButton-root, 
.MuiButton-root,
.MuiTab-root,
.MuiChip-root {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Improve focus visibility for accessibility */
*:focus-visible {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Reset focus styles when using mouse */
*:focus:not(:focus-visible) {
  outline: none;
}

/* Reduce animation on reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Helper class for handling text overflow */
.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Fade transition effect */
.fade-enter {
  opacity: 0;
}
.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms;
}
.fade-exit {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}

/* Animation for skeleton loading */
@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.skeleton {
  background: linear-gradient(to right, #f0f0f0 8%, #e0e0e0 18%, #f0f0f0 33%);
  background-size: 1000px 100%;
  animation: shimmer 2s infinite linear;
}

/* Fix for iOS border radius overflow issue */
.ios-fix {
  z-index: 1;
  isolation: isolate;
}

/* Print styles for exporting results */
@media print {
  body {
    background-color: white;
  }
  
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  /* Ensure content fits on printed page */
  .print-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* Ensure links are visible in printed output */
  a[href]:after {
    content: " (" attr(href) ")";
    font-size: 0.9em;
  }
}