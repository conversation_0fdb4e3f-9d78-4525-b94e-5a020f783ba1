import React, { useEffect } from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import AppRouter from './AppRouter' // Import new AppRouter
import './index.css'
import { StyledEngineProvider } from '@mui/material/styles';
import { AuthProvider } from './context/AuthContext';
import { DataProvider } from './context/DataContext';
import { AppThemeProvider } from './context/ThemeContext';
import { ResultsProvider } from './context/ResultsContext';
import { checkAndDisplayCompatibilityWarnings } from './utils/compatibilityChecker';
import { HelmetProvider } from 'react-helmet-async';
import { BrowserRouter } from 'react-router-dom'; // Change to BrowserRouter
import { cacheManager, recoverFromCacheIssues } from './utils/cacheManager';
import PWAErrorBoundary from './components/PWA/PWAErrorBoundary';

// Wrapper component to handle browser compatibility check
const AppWithCompatibilityCheck: React.FC = () => {
  useEffect(() => {
    // Initialize app with cache validation and recovery
    const initializeApp = async () => {
      try {
        // Check for browser compatibility issues
        checkAndDisplayCompatibilityWarnings();

        // Proactive cache validation and recovery
        if (cacheManager.shouldForceRefresh()) {
          console.log('🔧 Cache corruption detected, performing recovery...');
          localStorage.setItem('datastatpro-cache-corruption-detected', 'false');
          await recoverFromCacheIssues();
          cacheManager.markForceRefresh();
        }

        // Perform routine cache maintenance
        try {
          const clearedCount = await cacheManager.clearStaleCaches();
          if (clearedCount > 0) {
            console.log(`🧹 Cleared ${clearedCount} stale caches during app initialization`);
          }
        } catch (error) {
          console.warn('Cache maintenance failed:', error);
        }

        // Add viewport adjustment for iOS (avoid issues with viewport heights)
        const handleResize = () => {
          // Set CSS variable for viewport height that works on iOS
          document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
        };

        // Initial call
        handleResize();

        // Add event listener for resize
        window.addEventListener('resize', handleResize);

        // Track successful app initialization
        localStorage.removeItem('datastatpro-loading-failures');

        // Add global error handlers for cache corruption detection
        const handleUnhandledError = (event: ErrorEvent) => {
          console.error('Unhandled error:', event.error);

          // Check if error indicates cache corruption
          const errorMessage = event.error?.message || event.message || '';
          const cacheCorruptionPatterns = [
            /loading chunk \d+ failed/i,
            /failed to fetch/i,
            /networkerror/i,
            /cache/i,
            /service worker/i,
            /auth.*timeout/i,
            /session.*failed/i
          ];

          if (cacheCorruptionPatterns.some(pattern => pattern.test(errorMessage))) {
            console.warn('Cache corruption detected from unhandled error');
            localStorage.setItem('datastatpro-cache-corruption-detected', 'true');
          }

          // Check for authentication-related errors
          const authErrorPatterns = [
            /auth.*failed/i,
            /session.*expired/i,
            /token.*invalid/i,
            /supabase.*error/i
          ];

          if (authErrorPatterns.some(pattern => pattern.test(errorMessage))) {
            console.warn('Authentication error detected');
            localStorage.setItem('datastatpro-auth-loading-stuck', 'true');
          }
        };

        const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
          console.error('Unhandled promise rejection:', event.reason);

          // Similar check for promise rejections
          const reason = event.reason?.message || event.reason || '';
          if (typeof reason === 'string' && reason.includes('fetch')) {
            localStorage.setItem('datastatpro-cache-corruption-detected', 'true');
          }
        };

        window.addEventListener('error', handleUnhandledError);
        window.addEventListener('unhandledrejection', handleUnhandledRejection);

        // Cleanup function
        return () => {
          window.removeEventListener('resize', handleResize);
          window.removeEventListener('error', handleUnhandledError);
          window.removeEventListener('unhandledrejection', handleUnhandledRejection);
        };
      } catch (error) {
        console.error('App initialization failed:', error);

        // Track loading failures
        const failures = parseInt(localStorage.getItem('datastatpro-loading-failures') || '0') + 1;
        localStorage.setItem('datastatpro-loading-failures', failures.toString());

        // If multiple failures, mark for cache recovery
        if (failures > 2) {
          localStorage.setItem('datastatpro-cache-corruption-detected', 'true');
        }
      }
    };

    const cleanup = initializeApp();

    // Return cleanup function
    return () => {
      cleanup.then(cleanupFn => {
        if (typeof cleanupFn === 'function') {
          cleanupFn();
        }
      });
    };
  }, []);
  
  return (
    <PWAErrorBoundary>
      <StyledEngineProvider injectFirst>
        <AppThemeProvider>
          <BrowserRouter> {/* Change to BrowserRouter */}
            <AuthProvider>
              <DataProvider>
                <ResultsProvider>
                  <AppRouter /> {/* Use AppRouter instead of App */}
                </ResultsProvider>
              </DataProvider>
            </AuthProvider>
          </BrowserRouter>
        </AppThemeProvider>
      </StyledEngineProvider>
    </PWAErrorBoundary>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <HelmetProvider>
      <AppWithCompatibilityCheck />
    </HelmetProvider>
  </React.StrictMode>,
)
