import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
  useTheme,
  alpha,
  TextField as <PERSON><PERSON><PERSON><PERSON>t<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@mui/material';
import {
  Science as ScienceIcon,
  Compare as CompareIcon,
  BarChart as BarChartIcon,
  Functions as FunctionsIcon,
  FlashOn as FlashOnIcon,
  ArrowBackIos as ArrowBackIosIcon,
  Help as HelpIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useData } from '../context/DataContext';
import { DataType } from '../types';
import {
  Button,
  GuidedWorkflow,
  DatasetSelector,
  VariableSelector,
  EnhancedAnalysisResultCard,
  DataFlowDiagram,
  ContextualHelp
} from '../components/UI';
import { oneWayANOVA, twoWayANOVA, calculateRepeatedMeasuresANOVA, comprehensiveNormalityTest, performLeveneTest } from '@/utils/stats'; // Import necessary stats functions
// Import specific ANOVA components if needed for detailed results display, otherwise remove
// import OneWayANOVAComponent from '../components/InferentialStats/ANOVA/OneWayANOVA';
// import TwoWayANOVAComponent from '../components/InferentialStats/ANOVA/TwoWayANOVA';
// import RepeatedMeasuresANOVAComponent from '../components/InferentialStats/ANOVA/RepeatedMeasuresANOVA';
import { Helmet } from 'react-helmet-async';

// ANOVA workflow page component
const ANOVAWorkflowPage: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();

  // Analysis parameters state
  const [selectedDataset, setSelectedDataset] = useState<string>(currentDataset?.id || '');
  const [anovaType, setAnovaType] = useState<'oneway' | 'twoway' | 'repeated'>('oneway');
  const [dependentVariable, setDependentVariable] = useState<string>('');
  const [factorVariable, setFactorVariable] = useState<string>('');
  const [secondFactorVariable, setSecondFactorVariable] = useState<string>('');
  const [subjectId, setSubjectId] = useState<string>('');
  const [withinSubjectVariables, setWithinSubjectVariables] = useState<string[]>([]);
  const [significanceLevel, setSignificanceLevel] = useState<number>(0.05);
  const [checkAssumptions, setCheckAssumptions] = useState<boolean>(true);

  // Results state
  const [results, setResults] = useState<any | null>(null);
  const [assumptionResults, setAssumptionResults] = useState<any | null>(null);

  // Get the current dataset
  const dataset = datasets.find(d => d.id === selectedDataset);

  // Run assumption checks whenever relevant variables change
  useEffect(() => {
    checkAnovaAssumptions();
  }, [dataset, dependentVariable, factorVariable, secondFactorVariable, anovaType, checkAssumptions]); // Add checkAssumptions as a dependency


  // Check if we have the required variables for the selected test type
  const hasRequiredVariables = () => {
    if (!dataset) return false;

    switch (anovaType) {
      case 'oneway':
        return !!dependentVariable && !!factorVariable;
      case 'twoway':
        return !!dependentVariable && !!factorVariable && !!secondFactorVariable && factorVariable !== secondFactorVariable;
      case 'repeated':
        return !!subjectId && withinSubjectVariables.length >= 2;
      default:
        return false;
    }
  };

  // Helper function to get values for dependent variable
  const getDependentValues = (): number[] => {
    if (!dataset || !dependentVariable) return [];

    return dataset.data
      .map(row => Number(row[dependentVariable]))
      .filter(val => !isNaN(val));
  };

  // Helper function to get values for a specific group based on a factor variable
  const getGroupValues = (factorVar: string, groupValue: any): number[] => {
    if (!dataset || !dependentVariable || !factorVar) return [];

    return dataset.data
      .filter(row => row[factorVar] === groupValue)
      .map(row => Number(row[dependentVariable]))
      .filter(val => !isNaN(val));
  };

  // Check ANOVA assumptions (simplified for demo)
  const checkAnovaAssumptions = () => {
    if (!dataset || !hasRequiredVariables()) {
      setAssumptionResults(null);
      return;
    }

    try {
      let normalityResults: any[] = [];
      let insufficientGroups: any[] = [];
      let nonNormalGroups: any[] = [];

      if (anovaType === 'oneway' || anovaType === 'twoway') {
        // Check normality for each group based on factorVariable
        const factorValues = [...new Set(dataset.data.map(row => row[factorVariable]))];
        normalityResults = factorValues.map(value => {
          const groupData = getGroupValues(factorVariable, value);
          const normalityTest = comprehensiveNormalityTest(groupData, 0.05, ['auto']);
          return {
            group: String(value),
            n: groupData.length,
            isNormal: normalityTest.overallAssessment.isNormal,
            pValue: normalityTest.tests.shapiroWilk?.pValue ||
                    normalityTest.tests.kolmogorovSmirnov?.pValue ||
                    normalityTest.tests.jarqueBera?.pValue || NaN,
            statistic: normalityTest.tests.shapiroWilk?.statistic ||
                      normalityTest.tests.kolmogorovSmirnov?.statistic ||
                      normalityTest.tests.jarqueBera?.statistic || NaN
          };
        });

        insufficientGroups = normalityResults.filter(group => group.n < 5);
        nonNormalGroups = normalityResults.filter(group => !group.isNormal && group.n >= 5);

        const allGroupData = factorValues.map(value => getGroupValues(factorVariable, value));
        const homogeneityTestResult = performLeveneTest(...allGroupData.filter(d => d.length > 1));
        const homogeneityDetail = {
            name: "Homogeneity of Variances (Levene's Test)",
            status: homogeneityTestResult.pValue > significanceLevel ? 'passed' : 'failed',
            message: `Levene's test statistic: ${homogeneityTestResult.statistic.toFixed(3)}, p-value: ${formatPValue(homogeneityTestResult.pValue)}. A p-value > ${significanceLevel} suggests that the variances are equal.`,
        };

        setAssumptionResults({
          normalityResults,
          insufficientGroups,
          nonNormalGroups,
          allNormal: nonNormalGroups.length === 0,
          allSufficient: insufficientGroups.length === 0,
          homogeneityAssumption: homogeneityDetail
        });

      } else if (anovaType === 'repeated') {
         // Assumptions for Repeated Measures ANOVA are different (sphericity)
         // Placeholder for repeated measures assumptions
         setAssumptionResults({
           normalityResults: [],
           insufficientGroups: [],
           nonNormalGroups: [],
           allNormal: true,
           allSufficient: true,
           sphericityAssumption: {
             name: 'Sphericity',
             status: 'warning' as 'passed' | 'warning' | 'failed',
             message: 'Sphericity check not implemented in this demo.',
           }
         });
      }


    } catch (error) {
      console.error('Error checking assumptions:', error);
      setAssumptionResults(null);
    }
  };

  // Run the ANOVA analysis
  const runAnalysis = async () => {
    if (!dataset || !hasRequiredVariables()) {
      setResults(null);
      return;
    }

    // Ensure assumption results are up-to-date before running analysis
    checkAnovaAssumptions();


    try {
      let anovaResults: any = null;

      switch (anovaType) {
        case 'oneway':
          // Implement One-Way ANOVA calculation using oneWayANOVA function
          const dependentValues = getDependentValues();
          const factorValues = dataset.data.map(row => row[factorVariable]);

          // Need to group data by factor levels for the oneWayANOVA function
          const groupedData: { [key: string]: number[] } = {};
          dataset.data.forEach(row => {
            const group = String(row[factorVariable]);
            const value = Number(row[dependentVariable]);
            if (!isNaN(value)) {
              if (!groupedData[group]) {
                groupedData[group] = [];
              }
              groupedData[group].push(value);
            }
          });

          // Check if we have at least 3 groups with data
          const groupLevels = Object.keys(groupedData);
          if (groupLevels.length < 3 || groupLevels.some(level => groupedData[level].length === 0)) {
             console.error("One-Way ANOVA requires at least 3 groups with data.");
             setResults({ errorMessage: "One-Way ANOVA requires at least 3 groups with data." });
             return;
          }

          // Prepare data for oneWayANOVA function
          const dataForANOVA = groupLevels.map(level => groupedData[level]);

          try {
             anovaResults = oneWayANOVA(dataForANOVA);
             // Add group means and SDs to results for display
             const groupStats = groupLevels.map(level => {
                const data = groupedData[level];
                const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
                const sd = data.length > 1 ? Math.sqrt(data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (data.length - 1)) : 0;
                return { name: level, n: data.length, mean, sd };
             });
             anovaResults.groupStats = groupStats;

          } catch (anovaError: any) { // Add type assertion
             console.error("Error running One-Way ANOVA:", anovaError);
             setResults({ errorMessage: `Error running One-Way ANOVA: ${anovaError.message || anovaError}` }); // Handle potential non-Error objects
             return;
          }

          break;

        case 'twoway':
          const factorAValues = [...new Set(dataset.data.map(row => row[factorVariable]))];
          const factorBValues = [...new Set(dataset.data.map(row => row[secondFactorVariable]))];

          const dataForTwoWayANOVA: number[][][] = factorAValues.map(valA =>
            factorBValues.map(valB =>
              dataset.data
                .filter(row => row[factorVariable] === valA && row[secondFactorVariable] === valB)
                .map(row => Number(row[dependentVariable]))
                .filter(val => !isNaN(val))
            )
          );

          if (factorAValues.length < 2 || factorBValues.length < 2) {
            setResults({ errorMessage: "Two-Way ANOVA requires at least 2 levels for each factor." });
            return;
          }

          anovaResults = twoWayANOVA(dataForTwoWayANOVA, factorAValues.length, factorBValues.length);
          anovaResults.factorAName = factorVariable;
          anovaResults.factorBName = secondFactorVariable;
          anovaResults.factorALevels = factorAValues;
          anovaResults.factorBLevels = factorBValues;
          break;

        case 'repeated':
          if (!subjectId || withinSubjectVariables.length < 2) {
            setResults({ errorMessage: "Repeated Measures ANOVA requires a subject identifier and at least two within-subject variables." });
            return;
          }
          // The calculateRepeatedMeasuresANOVA function is async
          anovaResults = await calculateRepeatedMeasuresANOVA(dataset.data, subjectId, withinSubjectVariables);
          break;
      }

      // Set results
      setResults({
        ...anovaResults,
        anovaType,
        dependentVariable,
        factorVariable: anovaType === 'oneway' || anovaType === 'twoway' ? factorVariable : undefined,
        secondFactorVariable: anovaType === 'twoway' ? secondFactorVariable : undefined,
        subjectId: anovaType === 'repeated' ? subjectId : undefined,
        withinSubjectVariables: anovaType === 'repeated' ? withinSubjectVariables : undefined,
        significanceLevel,
        timestamp: new Date(),
        assumptionResults: checkAssumptions ? assumptionResults : undefined, // Include assumption results if checked
      });

    } catch (error: any) { // Add type assertion
      console.error('Error running analysis:', error);
      setResults({ errorMessage: `An unexpected error occurred: ${error.message || error}` }); // Handle potential non-Error objects
    }
  };

  // Helper function to format p-value
  const formatPValue = (p: number): string => {
    if (p === undefined || p === null) return 'N/A';
    if (p < 0.001) return 'p < 0.001';
    return `p = ${p.toFixed(3)}`;
  };

  const getInterpretation = () => {
    if (!results) return 'Run analysis to see interpretation.';

    const { pValue, dfBetween, dfWithin, F, etaSquared, groupStats, interaction, factorAName, factorBName, factorA, factorB, error, summary, sphericity } = results;

    switch (anovaType) {
        case 'oneway':
            if (pValue === undefined) return 'Could not calculate p-value.';
            const significant = pValue <= significanceLevel;
            return `The one-way ANOVA showed a statistically ${significant ? 'significant' : 'non-significant'} difference in ${dependentVariable} between groups, F(${dfBetween}, ${dfWithin}) = ${F?.toFixed(2)}, ${formatPValue(pValue)}.`;

        case 'twoway':
            if (!interaction || !factorA || !factorB || !error) return 'Incomplete results for Two-Way ANOVA.';
            const interactionSig = interaction.pValue <= significanceLevel;
            const factorASig = factorA.pValue <= significanceLevel;
            const factorBSig = factorB.pValue <= significanceLevel;
            let interpretation = `The two-way ANOVA was conducted to examine the effects of ${factorAName} and ${factorBName} on ${dependentVariable}. `;
            interpretation += `There was a ${interactionSig ? 'significant' : 'non-significant'} interaction between the two factors, F(${interaction.df}, ${error.df}) = ${interaction.F.toFixed(2)}, ${formatPValue(interaction.pValue)}. `;
            interpretation += `The main effect for ${factorAName} was ${factorASig ? 'significant' : 'not significant'}, F(${factorA.df}, ${error.df}) = ${factorA.F.toFixed(2)}, ${formatPValue(factorA.pValue)}. `;
            interpretation += `The main effect for ${factorBName} was ${factorBSig ? 'significant' : 'not significant'}, F(${factorB.df}, ${error.df}) = ${factorB.F.toFixed(2)}, ${formatPValue(factorB.pValue)}.`;
            return interpretation;

        case 'repeated':
            if (!summary || !sphericity) return 'Incomplete results for Repeated Measures ANOVA.';
            const withinSubjectsResult = summary.find((r: any) => r.source.includes('Within-Subjects'));
            if (!withinSubjectsResult) return 'Could not find within-subjects results.';
            const withinSig = withinSubjectsResult.p <= significanceLevel;
            const errorResult = summary.find((r: any) => r.source.includes('Error'));
            return `The repeated measures ANOVA revealed a ${withinSig ? 'significant' : 'non-significant'} effect of the within-subjects factor, F(${withinSubjectsResult.df}, ${errorResult?.df}) = ${withinSubjectsResult.F.toFixed(2)}, ${formatPValue(withinSubjectsResult.p)}. ${sphericity.message}`;

        default:
            return 'Run analysis to see interpretation.';
    }
  };

  // Define workflow steps
  const workflowSteps = [
    {
      id: 'select-dataset',
      title: 'Select Dataset',
      description: 'Choose the dataset for your analysis',
      content: (
        <DatasetSelector
          value={selectedDataset}
          onChange={(value) => setSelectedDataset(value)}
          variant="card"
          showEmpty={false}
          required
          minRows={4}
          helperText="Select a dataset with sufficient data for the analysis"
        />
      ),
      validation: () => selectedDataset ? true : 'Please select a dataset',
    },
    {
      id: 'choose-anova-type',
      title: 'Choose ANOVA Type',
      description: 'Select the appropriate ANOVA for your analysis question',
      content: (
        <Box>
          <Typography variant="body2" color="text.secondary" paragraph>
            The type of ANOVA you choose depends on your research question and data structure:
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Paper
                sx={{
                  p: 2,
                  height: '100%',
                  border: anovaType === 'oneway' ? `2px solid ${theme.palette.primary.main}` : undefined,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: theme.shadows[3],
                    borderColor: alpha(theme.palette.primary.main, 0.5)
                  }
                }}
                onClick={() => setAnovaType('oneway')}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CompareIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight={anovaType === 'oneway' ? 'bold' : 'normal'}>
                    One-Way ANOVA
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Compare means across three or more independent groups with one factor
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Example:</strong> Comparing test scores across three different teaching methods
                </Typography>
              </Paper>
            </Grid>

            <Grid item xs={12} md={4}>
              <Paper
                sx={{
                  p: 2,
                  height: '100%',
                  border: anovaType === 'twoway' ? `2px solid ${theme.palette.primary.main}` : undefined,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: theme.shadows[3],
                    borderColor: alpha(theme.palette.primary.main, 0.5)
                  }
                }}
                onClick={() => setAnovaType('twoway')}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CompareIcon color="secondary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight={anovaType === 'twoway' ? 'bold' : 'normal'}>
                    Two-Way ANOVA
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Examine the influence of two independent factors and their interaction
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Example:</strong> Analyzing how both fertilizer type and sunlight affect plant growth
                </Typography>
              </Paper>
            </Grid>

            <Grid item xs={12} md={4}>
              <Paper
                sx={{
                  p: 2,
                  height: '100%',
                  border: anovaType === 'repeated' ? `2px solid ${theme.palette.primary.main}` : undefined,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: theme.shadows[3],
                    borderColor: alpha(theme.palette.primary.main, 0.5)
                  }
                }}
                onClick={() => setAnovaType('repeated')}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CompareIcon color="info" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight={anovaType === 'repeated' ? 'bold' : 'normal'}>
                    Repeated Measures ANOVA
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Analyze differences across multiple measurements on the same subjects
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Example:</strong> Measuring performance at different time points after an intervention
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Box>
      ),
      validation: () => anovaType ? true : 'Please select an ANOVA type',
      helpContent: (
        <Box>
          <Typography variant="h6" gutterBottom>
            Choosing the Right ANOVA Test
          </Typography>

          <Typography variant="body1" paragraph>
            Analysis of Variance (ANOVA) is used to determine if there are statistically significant differences between the means of three or more independent groups.
          </Typography>

          <Typography variant="subtitle1" gutterBottom>
            One-Way ANOVA
          </Typography>
          <Typography variant="body2" paragraph>
            Use when you have one categorical independent variable (factor) with three or more groups and want to compare their means on a continuous dependent variable.
          </Typography>

          <Typography variant="subtitle1" gutterBottom>
            Two-Way ANOVA
          </Typography>
          <Typography variant="body2" paragraph>
            Use when you have two categorical independent variables (factors) and want to examine their individual and interactive effects on a continuous dependent variable.
          </Typography>

          <Typography variant="subtitle1" gutterBottom>
            Repeated Measures ANOVA
          </Typography>
          <Typography variant="body2" paragraph>
            Use when you have multiple measurements of the same dependent variable from the same subjects or matched subjects across different conditions or time points.
          </Typography>

          <Alert severity="info" sx={{ mt: 2 }}>
            <AlertTitle>When to use ANOVA vs. t-test</AlertTitle>
            <Typography variant="body2">
              Use a t-test when comparing only two groups. Use ANOVA when comparing three or more groups, or when examining the effects of multiple factors.
            </Typography>
          </Alert>
        </Box>
      ),
    },
    {
      id: 'select-variables',
      title: 'Select Variables',
      description: 'Choose the variables for your analysis based on the selected ANOVA type',
      content: (
        <Box>
          {!dataset ? (
            <Alert severity="warning">
              <AlertTitle>No Dataset Selected</AlertTitle>
              Please go back and select a dataset first.
            </Alert>
          ) : (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <VariableSelector
                  label="Dependent Variable (Outcome)"
                  datasetId={selectedDataset}
                  value={dependentVariable}
                  onChange={(value) => setDependentVariable(value as string)}
                  allowedTypes={[DataType.NUMERIC]}
                  required
                  helperText="Select a continuous numeric variable to analyze"
                  variant="autocomplete"
                  placeholder="Select a numeric variable"
                />
              </Grid>

              {anovaType === 'oneway' && (
                <Grid item xs={12}>
                  <VariableSelector
                  label="Factor Variable (Groups)"
                  datasetId={selectedDataset}
                  value={factorVariable}
                  onChange={(value) => setFactorVariable(value as string)}
                  allowedTypes={[DataType.CATEGORICAL, DataType.ORDINAL, DataType.BOOLEAN]}
                    required
                    helperText="Select a categorical variable with at least 3 groups"
                    variant="autocomplete"
                    placeholder="Select a categorical variable"
                  />
                </Grid>
              )}

              {anovaType === 'twoway' && (
                <>
                  <Grid item xs={12}>
                    <VariableSelector
                      label="First Factor Variable"
                      datasetId={selectedDataset}
                      value={factorVariable}
                      onChange={(value) => setFactorVariable(value as string)}
                      allowedTypes={[DataType.CATEGORICAL, DataType.ORDINAL, DataType.BOOLEAN]}
                      required
                      helperText="Select the first categorical factor variable"
                      variant="autocomplete"
                      placeholder="Select a categorical variable"
                    />
                  </Grid>
                   <Grid item xs={12}>
                    <VariableSelector
                      label="Second Factor Variable"
                      datasetId={selectedDataset}
                      value={secondFactorVariable}
                      onChange={(value) => setSecondFactorVariable(value as string)}
                      allowedTypes={[DataType.CATEGORICAL, DataType.ORDINAL, DataType.BOOLEAN]}
                      required
                      helperText="Select a different categorical factor variable"
                      disabledValues={[factorVariable]}
                      variant="autocomplete"
                      placeholder="Select a categorical variable"
                    />
                  </Grid>
                </>
              )}

              {anovaType === 'repeated' && (
                 <>
                   <Grid item xs={12} md={6}>
                     <VariableSelector
                       label="Subject Identifier"
                       datasetId={selectedDataset}
                       value={subjectId}
                       onChange={(value) => setSubjectId(value as string)}
                       allowedTypes={[DataType.CATEGORICAL, DataType.ORDINAL]}
                       required
                       helperText="Select the variable that uniquely identifies each subject."
                       variant="autocomplete"
                       placeholder="Select a subject ID variable"
                     />
                   </Grid>
                   <Grid item xs={12} md={6}>
                     <VariableSelector
                       label="Within-Subject Variables (Levels)"
                       datasetId={selectedDataset}
                       value={withinSubjectVariables}
                       onChange={(value) => setWithinSubjectVariables(value as string[])}
                       allowedTypes={[DataType.NUMERIC]}
                       required
                       multiple
                       minSelections={2}
                       helperText="Select at least two numeric variables for repeated measurements."
                       variant="autocomplete"
                       placeholder="Select variables"
                     />
                   </Grid>
                 </>
              )}
            </Grid>
          )}
        </Box>
      ),
      validation: () => {
        if (!dataset) return 'Please select a dataset first';
        if (!dependentVariable) return 'Please select a dependent variable';

        if (anovaType === 'oneway') {
          if (!factorVariable) return 'Please select a factor variable';
          // Add check for at least 3 groups in factorVariable if possible
        } else if (anovaType === 'twoway') {
          if (!factorVariable) return 'Please select the first factor variable';
          if (!secondFactorVariable) return 'Please select the second factor variable';
          if (factorVariable === secondFactorVariable) return 'The two factor variables must be different';
        } else if (anovaType === 'repeated') {
           if (!subjectId) return 'Please select a subject identifier variable.';
           if (withinSubjectVariables.length < 2) return 'Please select at least two within-subject variables.';
        }

        return true;
      },
    },
    {
      id: 'set-options',
      title: 'Set Analysis Options',
      description: 'Configure additional options for the ANOVA test',
      content: (
        <Box>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <MuiTextField
                label="Significance Level (α)"
                type="number"
                value={significanceLevel}
                onChange={(e) => setSignificanceLevel(parseFloat(e.target.value))}
                inputProps={{ min: 0.001, max: 0.999, step: 0.01 }}
                fullWidth
                size="small"
                helperText="Typical values: 0.05, 0.01, or 0.001"
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl component="fieldset" sx={{ mt: 2 }}>
                <FormControlLabel
                  control={
                    <Radio
                      checked={checkAssumptions}
                      onChange={(e) => setCheckAssumptions(e.target.checked)}
                    />
                  }
                  label="Check ANOVA assumptions before analysis"
                />
              </FormControl>
            </Grid>
          </Grid>

          <ContextualHelp
            title="About Statistical Options"
            items={[
              {
                title: "Significance Level (α)",
                content: "The significance level is the probability of rejecting the null hypothesis when it is true. A lower value makes the test more conservative.",
                type: "info"
              },
              {
                title: "Assumptions",
                content: "ANOVA tests have assumptions (e.g., normality, homogeneity of variances). Checking these helps ensure the validity of your results.",
                type: "tip"
              }
            ]}
            initiallyExpanded={true}
            variant="panel"
            showIcons={true}
            width="100%"
          />
        </Box>
      ),
      validation: () => {
        if (isNaN(significanceLevel) || significanceLevel <= 0 || significanceLevel >= 1) {
          return 'Significance level must be between 0 and 1';
        }
        return true;
      },
    },
    {
      id: 'review-and-run',
      title: 'Review & Run Analysis',
      description: 'Review your selections and run the ANOVA test',
      content: (
        <Box>
           <Paper sx={{ p: 2, mb: 3, backgroundColor: alpha(theme.palette.background.default, 0.5) }}>
            <Typography variant="subtitle1" gutterBottom>
              Analysis Summary
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2">
                  <strong>ANOVA Type:</strong> {
                    anovaType === 'oneway' ? 'One-Way ANOVA' :
                    anovaType === 'twoway' ? 'Two-Way ANOVA' :
                    'Repeated Measures ANOVA'
                  }
                </Typography>
                <Typography variant="body2">
                  <strong>Dataset:</strong> {dataset?.name || 'None selected'}
                </Typography>
                <Typography variant="body2">
                  <strong>Dependent Variable:</strong> {dependentVariable || 'None selected'}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                {anovaType === 'oneway' && (
                   <Typography variant="body2">
                     <strong>Factor Variable:</strong> {factorVariable || 'None selected'}
                   </Typography>
                )}
                 {anovaType === 'twoway' && (
                   <>
                     <Typography variant="body2">
                       <strong>First Factor:</strong> {factorVariable || 'None selected'}
                     </Typography>
                     <Typography variant="body2">
                       <strong>Second Factor:</strong> {secondFactorVariable || 'None selected'}
                     </Typography>
                   </>
                )}
                 {anovaType === 'repeated' && (
                   <>
                     <Typography variant="body2">
                       <strong>Subject Identifier:</strong> {subjectId || 'None selected'}
                     </Typography>
                     <Typography variant="body2">
                       <strong>Within-Subject Variables:</strong> {withinSubjectVariables.join(', ') || 'None selected'}
                     </Typography>
                   </>
                )}

                <Typography variant="body2">
                  <strong>Significance Level:</strong> {significanceLevel}
                </Typography>
                 <Typography variant="body2">
                   <strong>Check Assumptions:</strong> {checkAssumptions ? 'Yes' : 'No'}
                 </Typography>
              </Grid>
            </Grid>
          </Paper>

          {checkAssumptions && assumptionResults && (
            <Box mb={3}>
              <Typography variant="subtitle1" gutterBottom>
                Assumption Check Results
              </Typography>

              <Alert
                severity={assumptionResults.allNormal && assumptionResults.allSufficient && (anovaType !== 'oneway' || assumptionResults.homogeneityAssumption?.status !== 'failed') ? "success" : "warning"}
                sx={{ mb: 2 }}
              >
                <React.Fragment>
                  <AlertTitle>
                    {assumptionResults.allNormal && assumptionResults.allSufficient && (anovaType !== 'oneway' || assumptionResults.homogeneityAssumption?.status !== 'failed')
                      ? "Assumptions appear to be met"
                      : "Some assumptions may be violated"}
                  </AlertTitle>
                  <Typography variant="body2">
                    {assumptionResults.allNormal
                      ? "✓ Normality: All groups appear to be normally distributed."
                      : `⚠️ Normality: ${assumptionResults.nonNormalGroups.length} group(s) may not be normally distributed`}
                  </Typography>
                  <Typography variant="body2">
                    {assumptionResults.allSufficient
                      ? "✓ Sample size: All groups have sufficient data"
                      : `⚠️ Sample size: ${assumptionResults.insufficientGroups.length} group(s) have fewer than 5 observations`}
                  </Typography>
                   {anovaType === 'oneway' && assumptionResults.homogeneityAssumption && (
                     <Typography variant="body2">
                       {assumptionResults.homogeneityAssumption.status === 'passed'
                         ? `✓ ${assumptionResults.homogeneityAssumption.name}: ${assumptionResults.homogeneityAssumption.message}`
                         : `⚠️ ${assumptionResults.homogeneityAssumption.name}: ${assumptionResults.homogeneityAssumption.message}`}
                     </Typography>
                   )}
                   {anovaType === 'repeated' && assumptionResults.sphericityAssumption && (
                      <Typography variant="body2">
                        {assumptionResults.sphericityAssumption.status === 'passed'
                          ? `✓ ${assumptionResults.sphericityAssumption.name}: ${assumptionResults.sphericityAssumption.message}`
                          : `⚠️ ${assumptionResults.sphericityAssumption.name}: ${assumptionResults.sphericityAssumption.message}`}
                      </Typography>
                   )}
                </React.Fragment>
              </Alert>
               {assumptionResults.nonNormalGroups.length > 0 && (
                 <Box sx={{ mt: 1 }}>
                   <Typography variant="body2" color="text.secondary">
                     Groups potentially violating normality: {assumptionResults.nonNormalGroups.map((g: any) => g.group).join(', ')}
                   </Typography>
                 </Box>
               )}
               {assumptionResults.insufficientGroups.length > 0 && (
                 <Box sx={{ mt: 1 }}>
                   <Typography variant="body2" color="text.secondary">
                     Groups with insufficient data (&lt 5 observations): {assumptionResults.insufficientGroups.map((g: any) => g.group).join(', ')}
                   </Typography>
                 </Box>
               )}
            </Box>
          )}


          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Button
              gradient
              rounded
              startIcon={<FlashOnIcon />}
              onClick={runAnalysis}
              disabled={!hasRequiredVariables()}
              size="large"
            >
              Run ANOVA Analysis
            </Button>
          </Box>

          {/* Results section */}
          <Box sx={{ mt: 3 }}>
            {!results ? (
              <Alert severity="info">
                <AlertTitle>Analysis Results</AlertTitle>
                Results will appear here after clicking "Run ANOVA Analysis".
              </Alert>
            ) : results.errorMessage ? (
               <Alert severity="error">
                 <AlertTitle>Analysis Error</AlertTitle>
                 {results.errorMessage}
               </Alert>
            ) : (
              <EnhancedAnalysisResultCard
                key={results.timestamp}
                title={`${
                  anovaType === 'oneway' ? 'One-Way ANOVA' :
                  anovaType === 'twoway' ? 'Two-Way ANOVA' :
                  'Repeated Measures ANOVA'
                } Results`}
                description={
                  anovaType === 'oneway'
                    ? `Comparing ${dependentVariable} across groups of ${factorVariable}`
                    : anovaType === 'twoway'
                    ? `Examining effects of ${factorVariable} and ${secondFactorVariable} on ${dependentVariable}`
                    : `Analyzing repeated measurements for ${withinSubjectVariables.join(', ')}`
                }
                timestamp={results.timestamp}
                pValue={
                  anovaType === 'oneway' ? results.pValue :
                  anovaType === 'twoway' ? results.interaction?.pValue :
                  anovaType === 'repeated' ? (results.summary?.find((r: any) => r.source.includes('Within-Subjects'))?.p) : undefined
                }
                significance={significanceLevel}
                stats={
                  anovaType === 'oneway' && results.groupStats ? [
                     { label: 'F-statistic', value: results.F?.toFixed(2) },
                     { label: 'p-value', value: formatPValue(results.pValue) },
                     { label: 'Effect Size (η²)', value: results.etaSquared?.toFixed(3) },
                  ] : anovaType === 'twoway' ? [
                     { label: `F-stat (${results.factorAName})`, value: results.factorA?.F.toFixed(2) },
                     { label: `p-value (${results.factorAName})`, value: formatPValue(results.factorA?.pValue) },
                     { label: `F-stat (${results.factorBName})`, value: results.factorB?.F.toFixed(2) },
                     { label: `p-value (${results.factorBName})`, value: formatPValue(results.factorB?.pValue) },
                     { label: 'F-stat (Interaction)', value: results.interaction?.F.toFixed(2) },
                     { label: 'p-value (Interaction)', value: formatPValue(results.interaction?.pValue) },
                  ] : anovaType === 'repeated' && results.summary ? [
                     { label: 'F-statistic (Within-Subject)', value: results.summary.find((r: any) => r.source.includes('Within-Subjects'))?.F.toFixed(2) },
                     { label: 'p-value (Within-Subject)', value: formatPValue(results.summary.find((r: any) => r.source.includes('Within-Subjects'))?.p) },
                     { label: "Mauchly's W", value: results.sphericity?.mauchlyW.toFixed(3) },
                     { label: "Sphericity p-value", value: formatPValue(results.sphericity?.pValue) },
                  ] : []
                }
                statisticalTests={
                  anovaType === 'oneway' ? [
                    { name: 'F-statistic', value: results.F?.toFixed(3) },
                    { name: 'p-value', value: formatPValue(results.pValue), pValue: results.pValue, significant: results.pValue <= significanceLevel },
                    { name: 'Effect Size (η²)', value: results.etaSquared?.toFixed(3) },
                    { name: 'DF (between, within)', value: `${results.dfBetween}, ${results.dfWithin}` },
                  ] : anovaType === 'twoway' ? [
                    { name: `Factor A: ${results.factorAName}`, value: `F(${results.factorA?.df}, ${results.error?.df}) = ${typeof results.factorA?.F === 'number' ? results.factorA.F.toFixed(3) : 'N/A'}, p = ${formatPValue(results.factorA?.pValue)}, η² = ${typeof results.factorA?.etaSquared === 'number' ? results.factorA.etaSquared.toFixed(3) : 'N/A'}`, pValue: results.factorA?.pValue, significant: results.factorA?.pValue <= significanceLevel },
                    { name: `Factor B: ${results.factorBName}`, value: `F(${results.factorB?.df}, ${results.error?.df}) = ${typeof results.factorB?.F === 'number' ? results.factorB.F.toFixed(3) : 'N/A'}, p = ${formatPValue(results.factorB?.pValue)}, η² = ${typeof results.factorB?.etaSquared === 'number' ? results.factorB.etaSquared.toFixed(3) : 'N/A'}`, pValue: results.factorB?.pValue, significant: results.factorB?.pValue <= significanceLevel },
                    { name: 'Interaction (A x B)', value: `F(${results.interaction?.df}, ${results.error?.df}) = ${typeof results.interaction?.F === 'number' ? results.interaction.F.toFixed(3) : 'N/A'}, p = ${formatPValue(results.interaction?.pValue)}, η² = ${typeof results.interaction?.etaSquared === 'number' ? results.interaction.etaSquared.toFixed(3) : 'N/A'}`, pValue: results.interaction?.pValue, significant: results.interaction?.pValue <= significanceLevel },
                  ] : anovaType === 'repeated' && results.summary ?
                    results.summary.filter((row: any) => row.F && isFinite(row.F)).map((row: any) => ({
                      name: row.source,
                      value: `F(${row.df}, ${results.summary.find((r: any) => r.source.includes('Error'))?.df}) = ${typeof row.F === 'number' ? row.F.toFixed(3) : 'N/A'}, p = ${formatPValue(row.p)}, η²p = ${typeof row.etaSquared === 'number' ? row.etaSquared.toFixed(3) : 'N/A'}`,
                      pValue: row.p,
                      significant: row.p <= significanceLevel
                    }))
                  : []
                }
                confidenceInterval={undefined}
                chart={
                  (anovaType === 'oneway' && results.groupStats) || (anovaType === 'repeated' && results.descriptives) ? (
                    <Box
                      sx={{
                        height: 300,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'flex-end',
                        p: 2
                      }}
                    >
                      {/* Simulated bar chart for demonstration */}
                      <Box sx={{ display: 'flex', alignItems: 'flex-end', height: '100%' }}>
                        {(anovaType === 'oneway' ? results.groupStats : results.descriptives).map((stat: any, index: number) => (
                           <Box key={index} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mx: 1 }}>
                             <Box
                               sx={{
                                 width: 40,
                                 height: `${(stat.mean / Math.max(...(anovaType === 'oneway' ? results.groupStats : results.descriptives).map((s: any) => s.mean))) * 200}px`, // Scale height
                                 bgcolor: theme.palette.primary.main, // Use a consistent color or cycle
                                 display: 'flex',
                                 justifyContent: 'center',
                                 alignItems: 'flex-start',
                                 pt: 1,
                                 color: 'white',
                                 fontWeight: 'bold',
                                 borderRadius: '4px 4px 0 0'
                               }}
                             >
                               {stat.mean.toFixed(1)}
                             </Box>
                             <Typography variant="body2" sx={{ mt: 1 }}>
                               {stat.name || stat.condition}
                             </Typography>
                           </Box>
                        ))}
                      </Box>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 3, textAlign: 'center' }}>
                      Chart visualization not available for this ANOVA type.
                    </Typography>
                  )
                }
                chartTitle={
                  (anovaType === 'oneway' || anovaType === 'repeated') ? 'Comparison of Means' : undefined
                }
                table={
                  anovaType === 'oneway' && results.groupStats ? {
                     columns: ['Group', 'N', 'Mean', 'SD'],
                     rows: results.groupStats.map((stat: any) => [stat.name, stat.n, stat.mean.toFixed(2), stat.sd.toFixed(2)])
                  } : anovaType === 'twoway' && results.cellMeans ? {
                     columns: [`${results.factorAName}`, `${results.factorBName}`, 'N', 'Mean', 'SD'],
                     rows: results.factorALevels.flatMap((levelA: any, i: number) =>
                       results.factorBLevels.map((levelB: any, j: number) => [
                         levelA,
                         levelB,
                         results.cellCounts[i][j],
                         results.cellMeans[i][j].toFixed(2),
                         // Standard deviation would require more calculation, placeholder for now
                         'N/A'
                       ])
                     )
                  } : anovaType === 'repeated' && results.summary ? {
                     columns: ['Source', 'SS', 'df', 'MS', 'F', 'p-value', 'η²p'],
                     rows: results.summary.map((row: any) => [
                       String(row.source),
                       String(row.SS?.toFixed(3)),
                       String(row.df),
                       String(row.MS?.toFixed(3)),
                       String(row.F?.toFixed(3)),
                       String(formatPValue(row.p)),
                       String(row.etaSquared?.toFixed(3))
                     ]).filter((row: any[]) => row.every(cell => cell !== undefined && cell !== null))
                  } : undefined
                }
                interpretations={[getInterpretation()].filter(Boolean)}
                assumptions={[
                  {
                    name: 'Independence of observations',
                    status: 'passed',
                    message: 'Assumed based on study design'
                  },
                  {
                    name: 'No significant outliers',
                    status: 'warning',
                    message: 'Check your data for outliers using box plots'
                  },
                  ...(checkAssumptions && assumptionResults?.normalityResults ? [{
                     name: 'Normality',
                     status: assumptionResults.allNormal ? 'passed' : 'failed',
                     message: assumptionResults.allNormal
                       ? 'All groups appear normally distributed.'
                       : `Normality assumption may be violated in ${assumptionResults.nonNormalGroups.length} group(s). Details below:`
                  },
                  ...(assumptionResults.normalityResults.map((res: any) => ({
                    name: `  - Group "${res.group}"`,
                    status: res.n < 5 ? 'warning' : (res.isNormal ? 'passed' : 'failed'),
                    message: res.n < 5
                      ? `Insufficient data (n=${res.n}) to reliably test normality.`
                      : `Kolmogorov-Smirnov D=${res.statistic.toFixed(3)}, p=${formatPValue(res.pValue)}`
                  }))),
                  ] : []),
                   ...(checkAssumptions && anovaType === 'oneway' && assumptionResults?.homogeneityAssumption ? [assumptionResults.homogeneityAssumption] : []),
                   ...(checkAssumptions && anovaType === 'repeated' && assumptionResults?.sphericityAssumption ? [assumptionResults.sphericityAssumption] : []),
                ].filter(Boolean)}
                footnotes={[
                  `The significance level (α) was set at ${significanceLevel}.`,
                   ...(anovaType === 'oneway' && results?.groupStats ? [`Group means and standard deviations: ${results.groupStats.map((s: any) => `${s.name} (M=${s.mean.toFixed(2)}, SD=${s.sd.toFixed(2)})`).join(', ')}`] : []),
                   ...(anovaType === 'oneway' && results?.etaSquared !== undefined ? [`Effect size (Eta Squared, η²): ${results.etaSquared.toFixed(3)}`] : []), // Add eta squared if calculated
                ].filter(Boolean)}
                variant="default"
              />
            )}
          </Box>
        </Box>
      ),
      validation: () => {
        if (!hasRequiredVariables()) {
          return 'Please complete all previous steps before running the analysis';
        }
        if (checkAssumptions && !assumptionResults) {
           return 'Please wait for assumption checks to complete or uncheck "Check ANOVA assumptions".';
        }
         if (checkAssumptions && assumptionResults && (!assumptionResults.allNormal || !assumptionResults.allSufficient || (anovaType === 'oneway' && assumptionResults.homogeneityAssumption?.status === 'failed'))) {
           // Allow running analysis even if assumptions are violated, but show a warning
           // The validation should still pass, but the UI should highlight the warning
         }
        return true;
      },
       // Run assumption check when this step becomes active
       onStepEnter: checkAnovaAssumptions,
    },
  ];

  return (
    <>
      <Helmet>
        <title>ANOVA Analysis Workflow | DataStatPro</title>
        <meta name="description" content="Step-by-step guided workflow for performing One-Way and Two-Way ANOVA analyses to compare means across multiple groups." />
      </Helmet>

      <Box sx={{ p: 3, maxWidth: 1200, margin: '0 auto' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <MuiButton
            startIcon={<ArrowBackIosIcon />}
            sx={{ mr: 2 }}
            component="a"
            href="#/"
          >
            Back to Home
          </MuiButton>

          <Typography variant="h5" component="h1">
            ANOVA Analysis Workflow
          </Typography>
        </Box>

        <Typography variant="body1" paragraph color="text.secondary">
          This guided workflow will help you perform an Analysis of Variance (ANOVA) to compare means across multiple groups.
          Follow the steps below to select your data, choose the appropriate test type, and interpret the results.
        </Typography>

        <Paper sx={{ p: 3, mb: 3 }}>
          <GuidedWorkflow
            steps={workflowSteps}
            title="ANOVA Analysis"
            description="Follow these steps to conduct an ANOVA analysis on your data"
            variant="vertical"
            saveProgress={true}
            persistenceKey="anova-workflow"
            enableBookmarking={true}
            showStepNavigation={true}
            allowSkipSteps={false}
          />
        </Paper>
      </Box>
    </>
  );
};

export default ANOVAWorkflowPage;
