import React from 'react';
import { Box, Container } from '@mui/material';
import AdvancedAnalysisOptions from '../components/AdvancedAnalysisAliases/AdvancedAnalysisOptions';

interface AdvancedAnalysisPageProps {
  onNavigate: (path: string) => void;
}

const AdvancedAnalysisPage: React.FC<AdvancedAnalysisPageProps> = ({ onNavigate }) => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <AdvancedAnalysisOptions onNavigate={onNavigate} />
      </Box>
    </Container>
  );
};

export default AdvancedAnalysisPage;
