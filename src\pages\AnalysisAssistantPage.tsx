import React from 'react';
import { Box } from '@mui/material';
import AnalysisAssistant from '../components/AnalysisAssistant/AnalysisAssistant';

interface AnalysisAssistantPageProps {
  onNavigate: (path: string) => void;
}

const AnalysisAssistantPage: React.FC<AnalysisAssistantPageProps> = ({ onNavigate }) => {
  return (
    <Box sx={{ p: 3 }}>
      <AnalysisAssistant onNavigate={onNavigate} />
    </Box>
  );
};

export default AnalysisAssistantPage;