import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
  Collapse,
} from '@mui/material';
import {
  Launch as LaunchIcon,
  Info as InfoIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  // Import category icons
  ShowChart as DescriptiveIcon,
  Assessment as InferentialIcon,
  CompareArrows as CorrelationIcon,
  School as AdvancedIcon,
  BarChart as VisualizationIcon,
  Storage as DataManagementIcon,
  TableChart as PublicationIcon,
  Calculate as SampleSizeIcon,
  Science as EpiCalcIcon,
} from '@mui/icons-material';
import { TextField } from '@mui/material';
import { useAuth } from '../context/AuthContext'; // Import useAuth

// Import analysis options from each source file
import { descriptiveStatsOptions } from '../components/DescriptiveStats/DescriptiveStatsOptions';
import { inferentialStatsOptions } from '../components/InferentialStats/InferentialStatsOptions';
import { correlationAnalysisOptions } from '../components/CorrelationAnalysis/CorrelationAnalysisOptions';
import { advancedAnalysisOptions } from '../components/AdvancedAnalysisAliases/AdvancedAnalysisOptions';
import { epiCalcOptions } from '../components/EpiCalc/EpiCalcOptions';
import { sampleSizeCalculatorOptions } from '../components/SampleSizeCalculators/SampleSizeCalculatorsOptions';
import { dataVisualizationOptions } from '../components/Visualization/DataVisualizationOptions';
import { dataManagementOptions } from '../components/DataManagement/DataManagementOptions';
import { publicationReadyOptions } from '../components/PublicationReady/PublicationReadyOptions';

// Define a common interface for all analysis options
interface AnalysisOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: string; // Use a broader type for category
  color: string;
}

// Define category structure that matches sidebar navigation
interface CategoryGroup {
  name: string;
  icon: React.ReactNode;
  color: string;
  options: AnalysisOption[];
}

interface AnalysisIndexPageProps {
  onNavigate: (path: string) => void;
}

const AnalysisIndexPage: React.FC<AnalysisIndexPageProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const { canAccessProFeatures } = useAuth(); // Get accountType from context

  // Create hierarchical category groups that match sidebar navigation
  const categoryGroups: CategoryGroup[] = useMemo(() => {
    const groups: CategoryGroup[] = [
      {
        name: 'Data Management',
        icon: <DataManagementIcon />,
        color: '#795548', // Brown
        options: dataManagementOptions,
      },
      {
        name: 'Descriptive Statistics',
        icon: <DescriptiveIcon />,
        color: '#4CAF50', // Green
        options: descriptiveStatsOptions,
      },
      {
        name: 'Inferential Statistics',
        icon: <InferentialIcon />,
        color: '#2196F3', // Blue
        options: inferentialStatsOptions,
      },
      {
        name: 'Correlation Analysis',
        icon: <CorrelationIcon />,
        color: '#FF9800', // Orange
        options: correlationAnalysisOptions,
      },
      {
        name: 'Advanced Analysis',
        icon: <AdvancedIcon />,
        color: '#9C27B0', // Purple
        options: advancedAnalysisOptions,
      },
      {
        name: 'Data Visualization',
        icon: <VisualizationIcon />,
        color: '#00BCD4', // Cyan
        options: dataVisualizationOptions,
      },
      {
        name: 'Sample Size Calculator',
        icon: <SampleSizeIcon />,
        color: '#FF5722', // Deep Orange
        options: sampleSizeCalculatorOptions,
      },
      {
        name: 'Epi Calculator',
        icon: <EpiCalcIcon />,
        color: '#607D8B', // Blue Grey
        options: epiCalcOptions,
      },
      {
        name: 'Publication Ready',
        icon: <PublicationIcon />,
        color: '#3F51B5', // Indigo
        options: publicationReadyOptions,
      },
    ];

    // Filter groups based on search query
    if (searchQuery.trim()) {
      return groups.map(group => ({
        ...group,
        options: group.options.filter(option =>
          option.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          option.shortDescription.toLowerCase().includes(searchQuery.toLowerCase()) ||
          option.detailedDescription.toLowerCase().includes(searchQuery.toLowerCase())
        )
      })).filter(group => group.options.length > 0);
    }

    return groups;
  }, [searchQuery]);

  // State for collapsible sections
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  // Toggle section expansion
  const toggleSection = (sectionName: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionName)) {
      newExpanded.delete(sectionName);
    } else {
      newExpanded.add(sectionName);
    }
    setExpandedSections(newExpanded);
  };

  // Initialize all sections as expanded by default
  React.useEffect(() => {
    const allSectionNames = categoryGroups.map(group => group.name);
    setExpandedSections(new Set(allSectionNames));
  }, [categoryGroups]);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Analysis Index
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Browse and launch all available statistical analysis and data management tools
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Find the right tool for your data analysis needs by exploring our comprehensive index of statistical tests, calculators, visualization options, and data management utilities.
        </Typography>
      </Paper>

      {/* Search Bar */}
      <Box sx={{ mb: 2 }}>
        <TextField
          label="Search Analyses"
          variant="outlined"
          fullWidth
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: <SearchIcon color="action" />,
          }}
        />
      </Box>

      {/* Hierarchical Category Sections */}
      {categoryGroups.map((group) => (
        <Box key={group.name} sx={{ mb: 4 }}>
          {/* Category Header */}
          <Paper
            elevation={1}
            sx={{
              p: 2,
              mb: 2,
              backgroundColor: alpha(group.color, 0.05),
              borderLeft: `4px solid ${group.color}`,
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                backgroundColor: alpha(group.color, 0.1),
                transform: 'translateX(4px)',
              }
            }}
            onClick={() => toggleSection(group.name)}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: group.color,
                    width: 40,
                    height: 40,
                  }}
                >
                  {group.icon}
                </Avatar>
                <Box>
                  <Typography variant="h5" fontWeight="bold" color={group.color}>
                    {group.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {group.options.length} analysis{group.options.length !== 1 ? 'es' : ''} available
                  </Typography>
                </Box>
              </Box>
              <IconButton size="small" sx={{ color: group.color }}>
                {expandedSections.has(group.name) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Box>
          </Paper>

          {/* Category Options Grid */}
          <Collapse in={expandedSections.has(group.name)} timeout="auto" unmountOnExit>
            <Grid container spacing={3}>
              {group.options.map((option) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="h6" fontWeight="bold">
                      {option.name}
                    </Typography>
                    {((option.path.startsWith('advanced-analysis') || option.path.startsWith('publication-ready')) && !canAccessProFeatures) && (
                      <Box
                        sx={{
                          ml: 1,
                          bgcolor: theme.palette.warning.main,
                          color: 'white',
                          fontSize: '0.65rem',
                          px: 0.7,
                          borderRadius: 1,
                          display: 'inline-flex',
                          alignItems: 'center',
                          height: 16
                        }}
                      >
                        PRO
                      </Box>
                    )}
                  </Box>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {option.shortDescription}
                </Typography>

                <Typography variant="body2" paragraph>
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  disabled={
                    (option.path.startsWith('advanced-analysis') || option.path.startsWith('publication-ready')) &&
                    !canAccessProFeatures
                  }
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
              ))}
            </Grid>
          </Collapse>
        </Box>
      ))}

      {/* Optional: Add a help section similar to other pages if needed */}
      {/*
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              ... help text based on categories ...
            </Typography>
          </Box>
        </Box>
      </Paper>
      */}
    </Container>
  );
};

export default AnalysisIndexPage;
