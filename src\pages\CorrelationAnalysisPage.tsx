import React from 'react';
import { Box, Container } from '@mui/material';
import CorrelationAnalysisOptions from '../components/CorrelationAnalysis/CorrelationAnalysisOptions';

interface CorrelationAnalysisPageProps {
  onNavigate: (path: string) => void;
}

const CorrelationAnalysisPage: React.FC<CorrelationAnalysisPageProps> = ({ onNavigate }) => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <CorrelationAnalysisOptions onNavigate={onNavigate} />
      </Box>
    </Container>
  );
};

export default CorrelationAnalysisPage;
