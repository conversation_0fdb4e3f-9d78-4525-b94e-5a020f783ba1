import React from 'react';
import { Box, Container } from '@mui/material';
import DataManagementOptions from '../components/DataManagement/DataManagementOptions'; // Import DataManagementOptions

interface DataManagementPageProps {
  onNavigate: (path: string) => void; // Add onNavigate prop
}

const DataManagementPage: React.FC<DataManagementPageProps> = ({ onNavigate }) => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <DataManagementOptions onNavigate={onNavigate} /> {/* Render DataManagementOptions */}
      </Box>
    </Container>
  );
};

export default DataManagementPage;
