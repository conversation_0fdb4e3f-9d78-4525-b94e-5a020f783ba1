import React from 'react';
import { Box, Container } from '@mui/material';
import DataVisualizationOptions from '../components/Visualization/DataVisualizationOptions';

interface DataVisualizationPageProps {
  onNavigate: (path: string) => void;
}

const DataVisualizationPage: React.FC<DataVisualizationPageProps> = ({ onNavigate }) => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <DataVisualizationOptions onNavigate={onNavigate} />
      </Box>
    </Container>
  );
};

export default DataVisualizationPage;
