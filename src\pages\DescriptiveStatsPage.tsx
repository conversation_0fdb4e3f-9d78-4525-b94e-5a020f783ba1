import React from 'react';
import { Box, Container } from '@mui/material';
import DescriptiveStatsOptions from '../components/DescriptiveStats/DescriptiveStatsOptions';

interface DescriptiveStatsPageProps {
  onNavigate: (path: string) => void;
}

const DescriptiveStatsPage: React.FC<DescriptiveStatsPageProps> = ({ onNavigate }) => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <DescriptiveStatsOptions onNavigate={onNavigate} />
      </Box>
    </Container>
  );
};

export default DescriptiveStatsPage;
