import React, { useEffect, useState } from 'react';
import {
  <PERSON>,
  Typography,
  Alert,
  Paper,
  Button,
  Divider,
  Card,
  CardContent
} from '@mui/material';
import {
  Psychology as PsychologyIcon,
  Warning as WarningIcon,
  Code as CodeIcon,
  CleaningServices as CleanIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import TrainingInterface from '../components/AnalysisAssistant/TrainingInterface';
import TrainingDemo from '../components/AnalysisAssistant/TrainingDemo';
import { trainingDB } from '../utils/trainingDatabase';

/**
 * Development-only Training Page
 * This page is only accessible in development environment
 * and provides access to the Analysis Assistant training system
 */
const DevTrainingPage: React.FC = () => {
  const theme = useTheme();
  const [isDevelopment, setIsDevelopment] = useState(false);
  const [activeTab, setActiveTab] = useState<'interface' | 'demo' | 'cleanup'>('interface');
  const [duplicates, setDuplicates] = useState<{ [key: string]: number }>({});
  const [cleanupMessage, setCleanupMessage] = useState<string>('');

  useEffect(() => {
    // Check if we're in development environment
    const isDevEnv = process.env.NODE_ENV === 'development' ||
                     window.location.hostname === 'localhost' ||
                     window.location.hostname === '127.0.0.1' ||
                     window.location.port === '5173' ||
                     window.location.port === '5174' ||
                     window.location.port === '3000';

    setIsDevelopment(isDevEnv);

    if (isDevEnv) {
      // Check for duplicates
      const duplicateData = trainingDB.getDuplicateCuratedSuggestions();
      setDuplicates(duplicateData);
    }
  }, []);

  const handleCleanupDuplicates = () => {
    const removedCount = trainingDB.removeDuplicateCuratedSuggestions();
    setCleanupMessage(`Removed ${removedCount} duplicate curated suggestions`);
    setDuplicates({});

    // Clear message after 5 seconds
    setTimeout(() => setCleanupMessage(''), 5000);
  };

  const handleRefreshDuplicates = () => {
    const duplicateData = trainingDB.getDuplicateCuratedSuggestions();
    setDuplicates(duplicateData);
  };

  // If not in development, show access denied
  if (!isDevelopment) {
    return (
      <Box sx={{ p: 4, maxWidth: 800, mx: 'auto' }}>
        <Paper elevation={2} sx={{ p: 4, textAlign: 'center' }}>
          <WarningIcon sx={{ fontSize: 64, color: theme.palette.error.main, mb: 2 }} />
          <Typography variant="h4" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            This page is only accessible in development environment.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Current environment: {process.env.NODE_ENV || 'production'}
          </Typography>
        </Paper>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <CodeIcon sx={{ mr: 2, color: theme.palette.primary.main, fontSize: 32 }} />
        <Box>
          <Typography variant="h4">Development Training System</Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Analysis Assistant Training Data Management
          </Typography>
        </Box>
      </Box>

      {/* Development Warning */}
      <Alert 
        severity="warning" 
        sx={{ mb: 3 }}
        icon={<WarningIcon />}
      >
        <Typography variant="subtitle2" gutterBottom>
          Development Environment Only
        </Typography>
        <Typography variant="body2">
          This page is only accessible in development mode and provides tools to manage 
          the Analysis Assistant training data. Changes made here will affect the AI 
          suggestions for all users.
        </Typography>
      </Alert>

      {/* Tab Navigation */}
      <Paper elevation={1} sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', borderBottom: 1, borderColor: 'divider' }}>
          <Button
            variant={activeTab === 'interface' ? 'contained' : 'text'}
            onClick={() => setActiveTab('interface')}
            sx={{
              borderRadius: 0,
              px: 3,
              py: 1.5,
              textTransform: 'none'
            }}
            startIcon={<PsychologyIcon />}
          >
            Training Interface
          </Button>
          <Button
            variant={activeTab === 'demo' ? 'contained' : 'text'}
            onClick={() => setActiveTab('demo')}
            sx={{
              borderRadius: 0,
              px: 3,
              py: 1.5,
              textTransform: 'none'
            }}
            startIcon={<CodeIcon />}
          >
            Testing & Demo
          </Button>
          <Button
            variant={activeTab === 'cleanup' ? 'contained' : 'text'}
            onClick={() => setActiveTab('cleanup')}
            sx={{
              borderRadius: 0,
              px: 3,
              py: 1.5,
              textTransform: 'none'
            }}
            startIcon={<CleanIcon />}
          >
            Data Cleanup
          </Button>
        </Box>
      </Paper>

      {/* Content */}
      <Paper elevation={1} sx={{ minHeight: '70vh' }}>
        {activeTab === 'interface' && (
          <Box sx={{ p: 0 }}>
            <TrainingInterface />
          </Box>
        )}
        
        {activeTab === 'demo' && (
          <Box sx={{ p: 3 }}>
            <TrainingDemo />
          </Box>
        )}

        {activeTab === 'cleanup' && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
              Data Cleanup & Maintenance
            </Typography>

            {cleanupMessage && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {cleanupMessage}
              </Alert>
            )}

            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Duplicate Curated Suggestions
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  The training system may create duplicate curated suggestions if initialization
                  is called multiple times. Use the tools below to identify and clean up duplicates.
                </Typography>

                <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                  <Button
                    variant="outlined"
                    onClick={handleRefreshDuplicates}
                    startIcon={<CodeIcon />}
                  >
                    Check for Duplicates
                  </Button>
                  <Button
                    variant="contained"
                    color="warning"
                    onClick={handleCleanupDuplicates}
                    startIcon={<CleanIcon />}
                    disabled={Object.keys(duplicates).length === 0}
                  >
                    Remove Duplicates
                  </Button>
                </Box>

                {Object.keys(duplicates).length > 0 ? (
                  <Box>
                    <Typography variant="subtitle2" color="error" gutterBottom>
                      Found {Object.keys(duplicates).length} duplicate patterns:
                    </Typography>
                    {Object.entries(duplicates).map(([pattern, count]) => (
                      <Typography key={pattern} variant="body2" sx={{ ml: 2 }}>
                        • "{pattern}" - {count} copies
                      </Typography>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body2" color="success.main">
                    ✅ No duplicates found
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Box>
        )}
      </Paper>

      {/* Footer Info */}
      <Box sx={{ mt: 3, textAlign: 'center' }}>
        <Typography variant="caption" color="text.secondary">
          Environment: {process.env.NODE_ENV || 'production'} | 
          Host: {window.location.hostname}:{window.location.port}
        </Typography>
      </Box>
    </Box>
  );
};

export default DevTrainingPage;
