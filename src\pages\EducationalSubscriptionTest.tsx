import React from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  Grid,
  Chip
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  School as SchoolIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const EducationalSubscriptionTest: React.FC = () => {
  const {
    user,
    accountType,
    isEducationalUser,
    educationalTier,
    canAccessAdvancedAnalysis,
    canAccessPublicationReady,
    canAccessCloudStorage,
    refreshProfile
  } = useAuth();

  const handleRefreshProfile = async () => {
    console.log('🔄 Refreshing profile to check for subscription changes...');
    await refreshProfile();
    console.log('✅ Profile refresh completed');
  };

  const getTestResult = () => {
    if (!user) {
      return { status: 'info', message: 'Please sign in to test educational subscription features' };
    }

    if (!isEducationalUser) {
      return { status: 'info', message: 'This test is for educational users only' };
    }

    if (educationalTier === 'pro') {
      if (canAccessPublicationReady && canAccessCloudStorage) {
        return { 
          status: 'success', 
          message: '✅ FIX WORKING: Educational Pro subscription is properly unlocking Pro features!' 
        };
      } else {
        return { 
          status: 'error', 
          message: '❌ ISSUE: Educational Pro subscription is not unlocking Pro features' 
        };
      }
    }

    if (educationalTier === 'free') {
      if (canAccessAdvancedAnalysis && !canAccessPublicationReady && !canAccessCloudStorage) {
        return { 
          status: 'success', 
          message: '✅ Educational Free tier working correctly (Advanced Analysis only)' 
        };
      } else {
        return { 
          status: 'warning', 
          message: '⚠️ Educational Free tier permissions may be incorrect' 
        };
      }
    }

    return { status: 'info', message: 'Educational tier not detected or set' };
  };

  const testResult = getTestResult();

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Educational Subscription Test
        </Typography>
        <Typography variant="body1" color="text.secondary">
          This page tests the fix for educational users with Pro subscriptions not getting proper feature access.
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Test Result */}
        <Grid item xs={12}>
          <Alert severity={testResult.status as any} sx={{ mb: 3 }}>
            <Typography variant="body1">
              {testResult.message}
            </Typography>
          </Alert>
        </Grid>

        {/* User Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              User Information
            </Typography>
            <List dense>
              <ListItem>
                <ListItemText 
                  primary="Email" 
                  secondary={user?.email || 'Not signed in'}
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Account Type" 
                  secondary={
                    <Chip 
                      label={accountType || 'None'} 
                      color={accountType === 'edu' ? 'secondary' : accountType === 'edu_pro' ? 'primary' : 'default'}
                      size="small"
                      icon={isEducationalUser ? <SchoolIcon /> : <StarIcon />}
                    />
                  }
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Educational Tier" 
                  secondary={
                    <Chip 
                      label={educationalTier || 'None'} 
                      color={educationalTier === 'pro' ? 'primary' : 'default'}
                      size="small"
                    />
                  }
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Is Educational User" 
                  secondary={isEducationalUser ? 'Yes' : 'No'}
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Feature Access */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Feature Access
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  {canAccessAdvancedAnalysis ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                </ListItemIcon>
                <ListItemText 
                  primary="Advanced Analysis" 
                  secondary={canAccessAdvancedAnalysis ? 'Access granted' : 'Access denied'}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  {canAccessPublicationReady ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                </ListItemIcon>
                <ListItemText 
                  primary="Publication Ready" 
                  secondary={canAccessPublicationReady ? 'Access granted' : 'Access denied'}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  {canAccessCloudStorage ? <CheckIcon color="success" /> : <CancelIcon color="error" />}
                </ListItemIcon>
                <ListItemText 
                  primary="Cloud Storage" 
                  secondary={canAccessCloudStorage ? 'Access granted' : 'Access denied'}
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Test Instructions */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Test Instructions
            </Typography>
            <Typography variant="body2" paragraph>
              To test the educational subscription fix:
            </Typography>
            <List dense>
              <ListItem>
                <ListItemText 
                  primary="1. Sign in with an educational (.edu) email account"
                  secondary="This should create an account with accounttype = 'edu' and edu_subscription_type = 'free'"
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="2. Verify initial state"
                  secondary="Should have Advanced Analysis access, but NOT Publication Ready or Cloud Storage"
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="3. Update subscription in Supabase"
                  secondary="Change edu_subscription_type from 'free' to 'pro' in the profiles table"
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="4. Refresh profile"
                  secondary="Click the refresh button below to reload user data"
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="5. Verify fix"
                  secondary="Should now have access to ALL Pro features (Advanced Analysis, Publication Ready, Cloud Storage)"
                />
              </ListItem>
            </List>
            
            <Box sx={{ mt: 2 }}>
              <Button 
                variant="contained" 
                startIcon={<RefreshIcon />}
                onClick={handleRefreshProfile}
                disabled={!user}
              >
                Refresh Profile Data
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default EducationalSubscriptionTest;
