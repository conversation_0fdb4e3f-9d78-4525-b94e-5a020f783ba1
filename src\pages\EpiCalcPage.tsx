import React from 'react';
import { Box, Container } from '@mui/material';
import EpiCalcOptions from '../components/EpiCalc/EpiCalcOptions';

interface EpiCalcPageProps {
  onNavigate: (path: string) => void;
}

const EpiCalcPage: React.FC<EpiCalcPageProps> = ({ onNavigate }) => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <EpiCalcOptions onNavigate={onNavigate} />
      </Box>
    </Container>
  );
};

export default EpiCalcPage;
