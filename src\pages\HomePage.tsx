import React, { useState, useEffect } from 'react'; // Add useEffect here
import { Helmet } from 'react-helmet-async';
import {
  Box,
  Typography,
  Grid,
  Divider,
  List,
  ListItem,
  ListItemText,
  useTheme,
  useMediaQuery,
  alpha,
  Chip,
  Collapse,
  IconButton,
  Fade,
  Paper,
  Tooltip,
  Avatar,
  ListItemButton // Import ListItemButton if needed for clickable list items
} from '@mui/material';
import {
  TableChart as TableChartIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  B<PERSON>bleChart as BubbleChartIcon,
  Functions as FunctionsIcon,
  CompareArrows as CompareArrowsIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Timeline as TimelineIcon,
  ArrowForward as ArrowForwardIcon,
  Dashboard as DashboardIcon,
  Assignment as AssignmentIcon,
  Storage as StorageIcon,
  School as SchoolIcon,
  Science as ScienceIcon,
  Add as AddIcon,
  ViewCarousel as ViewCarouselIcon,
  ExpandMore as ExpandMoreIcon,
  FlashOn as FlashOnIcon,
  Lightbulb as LightbulbIcon,
  Schedule as ScheduleIcon,
  PlayCircleOutlined as PlayCircleOutlinedIcon
} from '@mui/icons-material';
import { Link } from '@mui/material'; // Correct import for Link
import { useData } from '../context/DataContext';
import { Card, Button, SectionTitle, StatsCard, Badge } from '../components/UI';

// Define props interface for HomePage
interface HomePageProps {
  onNavigate: (path: string) => void;
}

const HomePage: React.FC<HomePageProps> = ({ onNavigate }) => { // Destructure onNavigate from props
  const { datasets, setCurrentDataset, currentDataset } = useData(); // Use currentDataset
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  // State for workflow guide visibility
  const [showWorkflowGuide, setShowWorkflowGuide] = useState(true); 
  // State for quick start visibility - update when datasets or currentDataset changes
  const [showQuickStart, setShowQuickStart] = useState(!!currentDataset || datasets.length > 0);

  useEffect(() => {
    setShowQuickStart(!!currentDataset || datasets.length > 0);
  }, [datasets, currentDataset]); // Add currentDataset dependency

  // Handle dataset selection and navigation
  const handleSelectDatasetAndNavigate = (dataset: any, path: string) => {
    setCurrentDataset(dataset);
    onNavigate(path);
  };

  // Handle navigation click, ensuring dataset is selected if needed
  const handleNavigate = (path: string, requiresDataset = false) => {
    if (requiresDataset && !currentDataset && datasets.length > 0) {
      setCurrentDataset(datasets[0]); // Select first dataset if none active
    }
    // Remove '#' from path before passing to onNavigate
    onNavigate(path.startsWith('#') ? path.substring(1) : path);
  };

  // Toggle workflow guide
  const toggleWorkflowGuide = () => {
    setShowWorkflowGuide(!showWorkflowGuide);
  };

  // Render recommended workflow guide
  const renderWorkflowGuide = () => {
    const workflowSteps = [
      {
        title: 'Import & Clean Data',
        description: 'Upload your data and prepare it for analysis by cleaning, transforming, and validating.',
        icon: <StorageIcon />,
        path: '/data-management/import', // Use path for onNavigate
        time: '5-10 min',
        color: theme.palette.primary.main
      },
      {
        title: 'Exploratory Analysis',
        description: 'Explore your data with descriptive statistics and initial visualizations.',
        icon: <FunctionsIcon />,
        path: '/stats/descriptives', // Use path for onNavigate
        time: '10-15 min',
        color: theme.palette.secondary.main
      },
      {
        title: 'Statistical Tests',
        description: 'Run appropriate statistical tests based on your research questions.',
        icon: <ScienceIcon />,
        path: '/inference/ttest', // Use path for onNavigate
        time: '15-20 min',
        color: theme.palette.warning.main
      },
      {
        title: 'Visualize Results',
        description: 'Create compelling visualizations to communicate your findings.',
        icon: <BarChartIcon />,
        path: '/charts/bar', // Use path for onNavigate
        time: '10-15 min',
        color: theme.palette.info.main
      }
    ]; // Corrected: Removed stray characters here

    return (
      // Restore Box wrapping Collapse and add flexShrink
      <Box sx={{ flexShrink: 0 }}> 
        <Collapse in={showWorkflowGuide}>
          <Paper
            elevation={0}
            sx={{ // Corrected sx prop placement
              p: 3,
              mb: 3,
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`,
              bgcolor: theme.palette.background.paper // Keep background opaque
              // Removed position and zIndex added for testing
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <ViewCarouselIcon color="primary" sx={{ mr: 1.5 }} />
              <Typography variant="h6" fontWeight="medium">
                Recommended Analysis Workflow
              </Typography>
            </Box>

            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={2}>
              {workflowSteps.map((step, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      height: '100%',
                      position: 'relative',
                      pt: 2
                    }}
                  >
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        width: 36,
                        height: 36,
                        borderRadius: '50%',
                        bgcolor: alpha(step.color, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: step.color,
                        border: `2px solid ${step.color}`,
                        zIndex: 2
                      }}
                    >
                      <Typography variant="body1" fontWeight="bold">
                        {index + 1}
                      </Typography>
                    </Box>

                    {index < workflowSteps.length - 1 && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 18,
                          left: '50%',
                          width: '100%',
                          height: 2,
                          bgcolor: alpha(step.color, 0.2),
                          zIndex: 1
                        }}
                      />
                    )}

                    <Card
                      sx={{
                        p: 2,
                        mt: 2,
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        borderTop: `3px solid ${step.color}`
                      }}
                      hoverEffect
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                        <Box sx={{ color: step.color, mr: 1.5 }}>
                          {step.icon}
                        </Box>
                        <Typography variant="subtitle1" fontWeight="medium">
                          {step.title}
                        </Typography>
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flex: 1 }}>
                         {step.description}
                      </Typography>

                      {/* Removed Time Chip */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', mt: 'auto' }}> 
                        {/* Changed justifyContent to flex-end */}
                        <Button
                          size="small"
                          endIcon={<ArrowForwardIcon />}
                          // Use onNavigate with path
                          onClick={() => handleNavigate(step.path, index > 0)}
                          // Disable steps 2, 3, 4 if no dataset is currently selected
                          disabled={index > 0 && !currentDataset} 
                        >
                          Start
                        </Button>
                      </Box>
                    </Card>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Collapse>
      </Box>
    );
  };

  // Render quick start for users with data
  const renderQuickStart = () => {
    if (datasets.length === 0) return null;

    return (
      <Fade in={showQuickStart}>
        <Box sx={{ mb: 4 }}>
          <SectionTitle
            title="Quick Start Analysis"
            icon={<FlashOnIcon />}
            action={
              <Button
                variant="outlined"
                startIcon={<PlayCircleOutlinedIcon />}
                size="small"
                color="primary"
                // Use onNavigate
                onClick={() => handleNavigate('/stats/descriptives', true)}
              >
                Start Analysis
              </Button>
            }
          />

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Card
                hoverEffect
                borderHighlight
                // Remove component="a" and href
                // Add onClick handler
                onClick={() => handleNavigate('/stats/descriptives', true)}
                sx={{ display: 'block', textDecoration: 'none', cursor: 'pointer' }}
              >
                <Box sx={{ p: 2.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), mr: 1.5 }}>
                      <FunctionsIcon color="primary" />
                    </Avatar>
                    <Typography variant="subtitle1" fontWeight="medium">
                      Descriptive Statistics
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Summarize your data with means, medians, and more
                  </Typography>
                </Box>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                hoverEffect
                borderHighlight
                // Remove component="a" and href
                // Add onClick handler
                onClick={() => handleNavigate('/inference/ttest', true)}
                sx={{ display: 'block', textDecoration: 'none', cursor: 'pointer' }}
              >
                <Box sx={{ p: 2.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Avatar sx={{ bgcolor: alpha(theme.palette.secondary.main, 0.1), mr: 1.5 }}>
                      <ScienceIcon color="secondary" />
                    </Avatar>
                    <Typography variant="subtitle1" fontWeight="medium">
                      Statistical Tests
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Compare groups with t-tests, ANOVA, and non-parametric tests
                  </Typography>
                </Box>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                hoverEffect
                borderHighlight
                // Remove component="a" and href
                // Add onClick handler
                onClick={() => handleNavigate('/correlation/pearson', true)}
                sx={{ display: 'block', textDecoration: 'none', cursor: 'pointer' }}
              >
                <Box sx={{ p: 2.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Avatar sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), mr: 1.5 }}>
                      <CompareArrowsIcon color="success" />
                    </Avatar>
                    <Typography variant="subtitle1" fontWeight="medium">
                      Correlation Analysis
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Measure relationships between variables
                  </Typography>
                </Box>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card
                hoverEffect
                borderHighlight
                // Remove component="a" and href
                // Add onClick handler
                onClick={() => handleNavigate('/charts/bar', true)}
                sx={{ display: 'block', textDecoration: 'none', cursor: 'pointer' }}
              >
                <Box sx={{ p: 2.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Avatar sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), mr: 1.5 }}>
                      <BarChartIcon color="info" />
                    </Avatar>
                    <Typography variant="subtitle1" fontWeight="medium">
                      Data Visualization
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Create beautiful charts and graphs
                  </Typography>
                </Box>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Fade>
    );
  };

  return (
    <>
      <Helmet>
        <title>DataStatPro - Advanced Statistical Analysis</title>
        <meta name="description" content="Welcome to DataStatPro, your advanced web application for statistical analysis, data visualization, and insightful interpretations. Get started with your data journey." />
      </Helmet>
      <Box sx={{
      width: '100%',
      // minHeight: '100%', // Remove minHeight to let content dictate height
      overflow: 'auto', // Add scrolling for content
      display: 'flex',
      flexDirection: 'column',
      px: { xs: 1, md: 2 },
      py: 2
    }}>
      <Card
        hoverEffect={false}
        gradient
        sx={{
          p: { xs: 3, sm: 4 },
          mb: 3,
          backgroundImage: `linear-gradient(135deg, ${alpha(theme.palette.primary.light, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
          position: 'relative',
          minHeight: 'auto', // Added to ensure card can grow with content
          flexShrink: 0 // Prevent shrinking in flex container
          // overflow: 'hidden' // Temporarily removed to test visibility
        }}
      >
        {/* <Box sx={{
          position: 'absolute',
          right: -50,
          top: -30,
          opacity: 0.05,
          transform: 'rotate(15deg)',
          fontSize: 300
        }}>
          <DashboardIcon fontSize="inherit" />
        </Box> */}

        <Typography
          variant={isMobile ? "h5" : "h4"}
          gutterBottom
          color="primary"
          sx={{ fontWeight: 'bold', position: 'relative', zIndex: 1 }}
        >
          Welcome to DataStatPro
        </Typography>
        <Typography
          variant={isMobile ? "subtitle1" : "h6"}
          color="text.secondary"
          paragraph
          sx={{ mb: 2, position: 'relative', zIndex: 1 }}
        >
          Advanced Statistical Analysis Web Application
        </Typography>
        <Typography variant="body1" paragraph sx={{ position: 'relative', zIndex: 1, maxWidth: 800 }}>
          DataStatPro provides powerful tools for data analysis, visualization, and statistical inference.
          Use the intuitive interface to explore your data, run statistical tests, and generate
          publication-quality visualizations.
        </Typography>

        <Typography
          variant="body1"
          color="text.primary"
          paragraph
          sx={{ mt: 1, mb: 2, position: 'relative', zIndex: 1, maxWidth: 800 }}
        >
          Explore powerful tools for guided analysis and insightful interpretations. For any bugs or feature requests, please email us at{' '}
          <Link href="mailto:<EMAIL>" color="primary" underline="hover">
            <EMAIL>
          </Link>
          {' or '}
          <Link href="mailto:<EMAIL>" color="primary" underline="hover">
            <EMAIL>
          </Link>
          . We're here to help enhance your data analysis journey!
        </Typography>
 
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 4, position: 'relative', zIndex: 1 }}>
          <Button
            gradient
            rounded
            startIcon={<TableChartIcon />}
            endIcon={<ArrowForwardIcon />}
            // Use onNavigate
            onClick={() => handleNavigate('/data-management/import')}
            size={isMobile ? "medium" : "large"}
          >
            Get Started
          </Button>

          <Button
            variant="outlined"
            rounded
            startIcon={<ViewCarouselIcon />}
            onClick={toggleWorkflowGuide}
            size={isMobile ? "medium" : "large"}
            sx={{ borderWidth: 2 }}
          >
            {showWorkflowGuide ? 'Hide Workflow Guide' : 'Show Workflow Guide'}
          </Button>
        </Box>
      </Card>

      {/* Recommended Workflow Guide */}
      {renderWorkflowGuide()}

      {/* Quick Start Analysis Section */}
      {renderQuickStart()}

      {datasets.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <SectionTitle
            title="Your Datasets"
            icon={<StorageIcon />}
            action={
              <Button variant="outlined" size="small" onClick={() => handleNavigate('/data-management/import')}>
                Import New Data
              </Button>
            }
          />
          <Grid container spacing={2}>
            {datasets.slice(0, 3).map(dataset => (
              <Grid item xs={12} sm={6} md={4} key={dataset.id}>
                <Card hoverEffect borderHighlight>
                  <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="h6" noWrap sx={{ maxWidth: '70%' }}>
                        {dataset.name}
                      </Typography>
                      <Badge
                        label={`${dataset.columns.length} cols`}
                        color="primary"
                        variant="light" // Keep light variant for Badge if it exists
                        size="small"
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {dataset.data.length} rows, {dataset.columns.length} columns
                    </Typography>
                    <Typography variant="caption" display="block" color="text.secondary">
                      Created: {new Date(dataset.dateCreated).toLocaleDateString()}
                    </Typography>

                    <Divider sx={{ my: 1.5 }} />

                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        onClick={() => handleSelectDatasetAndNavigate(dataset, '/data-management/editor')}
                        variant="outlined"
                      >
                        Edit
                      </Button>
                      <Button
                        size="small"
                        onClick={() => handleSelectDatasetAndNavigate(dataset, '/stats/descriptives')}
                        variant="outlined"
                        color="secondary"
                      >
                        Analyze
                      </Button>
                      <Button
                        size="small"
                        onClick={() => handleSelectDatasetAndNavigate(dataset, '/charts/bar')}
                      >
                        Visualize
                      </Button>
                    </Box>
                  </Box>
                </Card>
              </Grid>
            ))}

            {/* Add Data Card */}
            <Grid item xs={12} sm={6} md={4}>
              <Card
                hoverEffect
                // Use onClick with onNavigate
                onClick={() => handleNavigate('/data-management/import')}
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  p: 3,
                  textDecoration: 'none',
                  border: `2px dashed ${theme.palette.divider}`,
                  bgcolor: 'transparent',
                  cursor: 'pointer'
                }}
              >
                <AddIcon sx={{ fontSize: 40, color: theme.palette.primary.main, mb: 1 }} />
                <Typography variant="subtitle1" color="primary" align="center">
                  Import New Dataset
                </Typography>
                <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 1 }}>
                  Add your own data or try sample datasets
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Learning & Resource Card */}
      <Card sx={{ mb: 4 }}>
        <Box sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <SchoolIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="subtitle1" fontWeight="medium">
              Statistical Learning Resources
            </Typography>
          </Box>

          <Divider sx={{ mb: 2 }} />

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4}>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  bgcolor: alpha(theme.palette.info.main, 0.05),
                  border: `1px solid ${alpha(theme.palette.info.main, 0.1)}`,
                  borderRadius: 1,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <LightbulbIcon color="info" sx={{ mr: 1 }} />
                  <Typography variant="subtitle2">
                    Which Test Should I Use?
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flex: 1 }}>
                  Guide to selecting the appropriate statistical test based on your research question and data type.
                </Typography>
                <Button
                  size="small"
                  variant="outlined"
                  color="info"
                  // Use onNavigate
                  onClick={() => handleNavigate('/whichtest')}
                >
                  Learn More
                </Button>
              </Paper>
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  bgcolor: alpha(theme.palette.warning.main, 0.05),
                  border: `1px solid ${alpha(theme.palette.warning.main, 0.1)}`,
                  borderRadius: 1,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <BarChartIcon color="warning" sx={{ mr: 1 }} />
                  <Typography variant="subtitle2">
                    Visualization Guide
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flex: 1 }}>
                  Tips for choosing the right chart type to effectively communicate your data insights.
                </Typography>
                <Button
                  size="small"
                  variant="outlined"
                  color="warning"
                  // Use onNavigate
                  onClick={() => handleNavigate('/visualizationguide')}
                >
                  View Guide
                </Button>
              </Paper>
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  bgcolor: alpha(theme.palette.success.main, 0.05),
                  border: `1px solid ${alpha(theme.palette.success.main, 0.1)}`,
                  borderRadius: 1,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <ScienceIcon color="success" sx={{ mr: 1 }} />
                  <Typography variant="subtitle2">
                    Statistical Methods
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flex: 1 }}>
                  Reference guide with common statistical methods and their interpretations.
                </Typography>
                <Button
                  size="small"
                  variant="outlined"
                  color="success"
                  // Use onNavigate
                  onClick={() => handleNavigate('/statisticalmethods')}
                >
                  View Reference
                </Button>
              </Paper>
            </Grid>
          </Grid>
        </Box>
      </Card>

      <SectionTitle
        title="Key Features"
        icon={<AssignmentIcon />}
        divider
      />

      <Grid
        container
        spacing={3}
        sx={{ mb: 4 }}
      >
        <Grid item xs={12} sm={6} md={4}>
          {/* Wrap StatsCard to make it clickable */}
          <Box onClick={() => handleNavigate('/data-management/import')} sx={{ cursor: 'pointer', height: '100%' }}>
            <StatsCard
              title="Data Management"
              value="20+ Tools"
              description="Import, clean, and transform your data."
              icon={<StorageIcon fontSize="inherit" />}
              color="primary"
              variant="default" // Fix variant
              // Remove actionLink and actionText as Box handles click
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          {/* Wrap StatsCard to make it clickable */}
          <Box onClick={() => handleNavigate('/stats/descriptives', true)} sx={{ cursor: 'pointer', height: '100%' }}>
            <StatsCard
              title="Statistical Analysis"
              value="30+ Analysis Tools"
              description="Comprehensive suite of statistical tests."
              icon={<FunctionsIcon fontSize="inherit" />}
              color="secondary"
              variant="default" // Fix variant
              // Remove actionLink and actionText as Box handles click
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          {/* Wrap StatsCard to make it clickable */}
          <Box onClick={() => handleNavigate('/charts/bar', true)} sx={{ cursor: 'pointer', height: '100%' }}>
            <StatsCard
              title="Data Visualization"
              value="15+ Charts"
              description="Create publication-quality charts and graphs."
              icon={<BubbleChartIcon fontSize="inherit" />}
              color="info"
              variant="default" // Fix variant
              // Remove actionLink and actionText as Box handles click
            />
          </Box>
        </Grid>
      </Grid>

      <SectionTitle
        title="Analysis Tools"
        icon={<CompareArrowsIcon />}
        divider
      />

      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <Card>
            <Box sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <BarChartIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" fontWeight="medium">
                  Charts & Graphs
                </Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <List dense>
                {/* Use ListItemButton and onNavigate */}
                <ListItemButton onClick={() => handleNavigate('/charts/bar', true)}>
                  <ListItemText primary="Bar Charts" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/charts/pie', true)}>
                  <ListItemText primary="Pie Charts" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/charts/histogram', true)}>
                  <ListItemText primary="Histograms" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/charts/boxplot', true)}>
                  <ListItemText primary="Box Plots" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/charts/scatter', true)}>
                  <ListItemText primary="Scatter Plots" />
                </ListItemButton>
              </List>
            </Box>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <Card>
            <Box sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <FunctionsIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" fontWeight="medium">
                  Descriptive Statistics
                </Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <List dense>
                {/* Use ListItemButton and onNavigate */}
                <ListItemButton onClick={() => handleNavigate('/stats/descriptives', true)}>
                  <ListItemText primary="Numeric Summaries" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/stats/frequencies', true)}>
                  <ListItemText primary="Frequency Tables" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/stats/crosstabs', true)}>
                  <ListItemText primary="Cross Tabulations" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/stats/normality', true)}>
                  <ListItemText primary="Normality Tests" />
                </ListItemButton>
              </List>
            </Box>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <Card>
            <Box sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CompareArrowsIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" fontWeight="medium">
                  Inferential Statistics
                </Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <List dense>
                {/* Use ListItemButton and onNavigate */}
                <ListItemButton onClick={() => handleNavigate('/inference/ttest', true)}>
                  <ListItemText primary="Statistical Tests" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/inference/anova', true)}>
                  <ListItemText primary="ANOVA" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/inference/nonparametric', true)}>
                  <ListItemText primary="Non-parametric Tests" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/inference/assumptions', true)}>
                  <ListItemText primary="Assumption Checks" />
                </ListItemButton>
              </List>
            </Box>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <Card>
            <Box sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TimelineIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" fontWeight="medium">
                  Correlation & Regression
                </Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <List dense>
                {/* Use ListItemButton and onNavigate */}
                <ListItemButton onClick={() => handleNavigate('/correlation/pearson', true)}>
                  <ListItemText primary="Correlation Matrix" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/correlation/linear', true)}>
                  <ListItemText primary="Linear Regression" />
                </ListItemButton>
                <ListItemButton onClick={() => handleNavigate('/correlation/logistic', true)}>
                  <ListItemText primary="Logistic Regression" />
                </ListItemButton>
              </List>
            </Box>
          </Card>
        </Grid>
      </Grid>
    </Box>
    </>
  );
};

export default HomePage;
