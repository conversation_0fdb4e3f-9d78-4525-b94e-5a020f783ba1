import React from 'react';
import { Box, Container } from '@mui/material';
import InferentialStatsOptions from '../components/InferentialStats/InferentialStatsOptions';

interface InferentialStatsPageProps {
  onNavigate: (path: string) => void;
  initialSubPage?: string; // Add initialSubPage prop
}

const InferentialStatsPage: React.FC<InferentialStatsPageProps> = ({ onNavigate, initialSubPage }) => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <InferentialStatsOptions onNavigate={onNavigate} initialCategory={initialSubPage} /> {/* Pass initialSubPage as initialCategory */}
      </Box>
    </Container>
  );
};

export default InferentialStatsPage;
