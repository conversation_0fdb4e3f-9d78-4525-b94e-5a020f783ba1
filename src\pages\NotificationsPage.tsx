import React, { useState, useMemo } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  CardContent,
  CardActions,
  <PERSON>ton,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Pagination,
  IconButton,
  Checkbox,
  Fab,
  Menu,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  Stack,
  useTheme,
  alpha,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Sort as SortIcon,
  MarkEmailRead as MarkEmailReadIcon,
  Delete as DeleteIcon,
  SelectAll as SelectAllIcon,
  Clear as ClearIcon,
  Refresh as RefreshIcon,
  NotificationsNone as NotificationsNoneIcon
} from '@mui/icons-material';
import { useNotifications } from '../hooks/useNotifications';
import NotificationRichText from '../components/UI/NotificationRichText';
import { format, isToday, isYesterday, parseISO } from 'date-fns';

interface NotificationFilters {
  type: string;
  status: string;
  dateRange: string;
  search: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

const ITEMS_PER_PAGE = 10;

const NotificationsPage: React.FC = () => {
  const { notifications, loading, markAsRead, markAllAsRead } = useNotifications();
  const theme = useTheme();
  
  // State for filters and pagination
  const [filters, setFilters] = useState<NotificationFilters>({
    type: 'all',
    status: 'all',
    dateRange: 'all',
    search: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
  
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedNotifications, setSelectedNotifications] = useState<Set<string>>(new Set());
  const [sortMenuAnchorEl, setSortMenuAnchorEl] = useState<null | HTMLElement>(null);

  // Filter and sort notifications
  const filteredNotifications = useMemo(() => {
    let filtered = [...notifications];

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(notification =>
        notification.title.toLowerCase().includes(searchLower) ||
        notification.message.toLowerCase().includes(searchLower)
      );
    }

    // Apply type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(notification => notification.type === filters.type);
    }

    // Apply status filter
    if (filters.status === 'read') {
      filtered = filtered.filter(notification => notification.is_read);
    } else if (filters.status === 'unread') {
      filtered = filtered.filter(notification => !notification.is_read);
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (filters.dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
      }
      
      if (filters.dateRange !== 'all') {
        filtered = filtered.filter(notification => 
          new Date(notification.created_at) >= filterDate
        );
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (filters.sortBy) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'status':
          aValue = a.is_read ? 1 : 0;
          bValue = b.is_read ? 1 : 0;
          break;
        default: // created_at
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
      }

      if (filters.sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [notifications, filters]);

  // Pagination
  const totalPages = Math.ceil(filteredNotifications.length / ITEMS_PER_PAGE);
  const paginatedNotifications = filteredNotifications.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  // Helper functions
  const formatNotificationDate = (dateString: string) => {
    const date = parseISO(dateString);
    if (isToday(date)) {
      return `Today at ${format(date, 'h:mm a')}`;
    } else if (isYesterday(date)) {
      return `Yesterday at ${format(date, 'h:mm a')}`;
    } else {
      return format(date, 'MMM d, yyyy \'at\' h:mm a');
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success': return theme.palette.success.main;
      case 'warning': return theme.palette.warning.main;
      case 'error': return theme.palette.error.main;
      default: return theme.palette.info.main;
    }
  };

  const getTypeLabel = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  // Event handlers
  const handleFilterChange = (key: keyof NotificationFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const handleSelectNotification = (notificationId: string) => {
    const newSelected = new Set(selectedNotifications);
    if (newSelected.has(notificationId)) {
      newSelected.delete(notificationId);
    } else {
      newSelected.add(notificationId);
    }
    setSelectedNotifications(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedNotifications.size === paginatedNotifications.length) {
      setSelectedNotifications(new Set());
    } else {
      setSelectedNotifications(new Set(paginatedNotifications.map(n => n.id)));
    }
  };

  const handleBulkMarkAsRead = () => {
    selectedNotifications.forEach(id => {
      const notification = notifications.find(n => n.id === id);
      if (notification && !notification.is_read) {
        markAsRead(id);
      }
    });
    setSelectedNotifications(new Set());
  };

  const clearFilters = () => {
    setFilters({
      type: 'all',
      status: 'all',
      dateRange: 'all',
      search: '',
      sortBy: 'created_at',
      sortOrder: 'desc'
    });
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
          Notification Center
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Stay updated with all your notifications and system messages
        </Typography>
      </Box>

      {/* Filters and Controls */}
      <Paper sx={{ p: 3, mb: 3, backgroundColor: alpha(theme.palette.primary.main, 0.02) }}>
        <Grid container spacing={2} alignItems="center">
          {/* Search */}
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              size="small"
              placeholder="Search notifications..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>

          {/* Type Filter */}
          <Grid item xs={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Type</InputLabel>
              <Select
                value={filters.type}
                label="Type"
                onChange={(e) => handleFilterChange('type', e.target.value)}
              >
                <MenuItem value="all">All Types</MenuItem>
                <MenuItem value="info">Info</MenuItem>
                <MenuItem value="success">Success</MenuItem>
                <MenuItem value="warning">Warning</MenuItem>
                <MenuItem value="error">Error</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Status Filter */}
          <Grid item xs={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Status</InputLabel>
              <Select
                value={filters.status}
                label="Status"
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="unread">Unread</MenuItem>
                <MenuItem value="read">Read</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Date Range Filter */}
          <Grid item xs={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Date</InputLabel>
              <Select
                value={filters.dateRange}
                label="Date"
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
              >
                <MenuItem value="all">All Time</MenuItem>
                <MenuItem value="today">Today</MenuItem>
                <MenuItem value="week">This Week</MenuItem>
                <MenuItem value="month">This Month</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Sort and Actions */}
          <Grid item xs={6} md={2}>
            <Stack direction="row" spacing={1}>
              <Tooltip title="Sort options">
                <IconButton
                  size="small"
                  onClick={(e) => setSortMenuAnchorEl(e.currentTarget)}
                >
                  <SortIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Clear filters">
                <IconButton size="small" onClick={clearFilters}>
                  <ClearIcon />
                </IconButton>
              </Tooltip>
            </Stack>
          </Grid>
        </Grid>
      </Paper>

      {/* Sort Menu */}
      <Menu
        anchorEl={sortMenuAnchorEl}
        open={Boolean(sortMenuAnchorEl)}
        onClose={() => setSortMenuAnchorEl(null)}
      >
        <MenuItem onClick={() => { handleFilterChange('sortBy', 'created_at'); setSortMenuAnchorEl(null); }}>
          Sort by Date
        </MenuItem>
        <MenuItem onClick={() => { handleFilterChange('sortBy', 'title'); setSortMenuAnchorEl(null); }}>
          Sort by Title
        </MenuItem>
        <MenuItem onClick={() => { handleFilterChange('sortBy', 'type'); setSortMenuAnchorEl(null); }}>
          Sort by Type
        </MenuItem>
        <MenuItem onClick={() => { handleFilterChange('sortBy', 'status'); setSortMenuAnchorEl(null); }}>
          Sort by Status
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => { handleFilterChange('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc'); setSortMenuAnchorEl(null); }}>
          {filters.sortOrder === 'asc' ? 'Sort Descending' : 'Sort Ascending'}
        </MenuItem>
      </Menu>

      {/* Bulk Actions */}
      {selectedNotifications.size > 0 && (
        <Paper sx={{ p: 2, mb: 3, backgroundColor: alpha(theme.palette.primary.main, 0.08) }}>
          <Stack direction="row" alignItems="center" spacing={2}>
            <Typography variant="body2">
              {selectedNotifications.size} notification{selectedNotifications.size !== 1 ? 's' : ''} selected
            </Typography>
            <Button
              size="small"
              startIcon={<MarkEmailReadIcon />}
              onClick={handleBulkMarkAsRead}
            >
              Mark as Read
            </Button>
            <Button
              size="small"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={() => setSelectedNotifications(new Set())}
            >
              Clear Selection
            </Button>
          </Stack>
        </Paper>
      )}

      {/* Results Summary */}
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          Showing {paginatedNotifications.length} of {filteredNotifications.length} notifications
        </Typography>
        {paginatedNotifications.length > 0 && (
          <Stack direction="row" alignItems="center" spacing={1}>
            <Checkbox
              size="small"
              checked={selectedNotifications.size === paginatedNotifications.length && paginatedNotifications.length > 0}
              indeterminate={selectedNotifications.size > 0 && selectedNotifications.size < paginatedNotifications.length}
              onChange={handleSelectAll}
            />
            <Typography variant="body2" color="text.secondary">
              Select All
            </Typography>
          </Stack>
        )}
      </Box>

      {/* Notifications List */}
      {paginatedNotifications.length === 0 ? (
        <Paper sx={{ p: 6, textAlign: 'center' }}>
          <NotificationsNoneIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No notifications found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {filters.search || filters.type !== 'all' || filters.status !== 'all' || filters.dateRange !== 'all'
              ? 'Try adjusting your filters to see more notifications.'
              : 'You\'re all caught up! New notifications will appear here.'}
          </Typography>
          {(filters.search || filters.type !== 'all' || filters.status !== 'all' || filters.dateRange !== 'all') && (
            <Button variant="outlined" onClick={clearFilters}>
              Clear Filters
            </Button>
          )}
        </Paper>
      ) : (
        <Stack spacing={2}>
          {paginatedNotifications.map((notification) => (
            <Card
              key={notification.id}
              sx={{
                position: 'relative',
                borderLeft: notification.is_read ? 'none' : `4px solid ${theme.palette.primary.main}`,
                backgroundColor: notification.is_read ? 'transparent' : alpha(theme.palette.primary.main, 0.02),
                '&:hover': {
                  backgroundColor: alpha(theme.palette.action.hover, 0.04),
                  transform: 'translateY(-1px)',
                  boxShadow: theme.shadows[4]
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <CardContent sx={{ pb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                  {/* Selection Checkbox */}
                  <Checkbox
                    size="small"
                    checked={selectedNotifications.has(notification.id)}
                    onChange={() => handleSelectNotification(notification.id)}
                    sx={{ mt: 0.5 }}
                  />

                  {/* Notification Content */}
                  <Box sx={{ flex: 1, minWidth: 0 }}>
                    {/* Header with title and type */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: notification.is_read ? 400 : 600,
                          flex: 1,
                          fontSize: '1.1rem'
                        }}
                      >
                        {notification.title}
                      </Typography>
                      <Chip
                        label={getTypeLabel(notification.type)}
                        size="small"
                        sx={{
                          backgroundColor: alpha(getTypeColor(notification.type), 0.1),
                          color: getTypeColor(notification.type),
                          fontWeight: 500
                        }}
                      />
                      {!notification.is_read && (
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: theme.palette.primary.main
                          }}
                        />
                      )}
                    </Box>

                    {/* Message */}
                    <NotificationRichText
                      text={notification.message}
                      variant="body1"
                      color="text.primary"
                      sx={{ mb: 2, lineHeight: 1.6 }}
                      showYouTubePreview={true}
                    />

                    {/* Date */}
                    <Typography variant="caption" color="text.secondary">
                      {formatNotificationDate(notification.created_at)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>

              {/* Actions */}
              <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
                <Box sx={{ ml: 5 }}> {/* Align with content (checkbox + gap) */}
                  {!notification.is_read && (
                    <Button
                      size="small"
                      startIcon={<MarkEmailReadIcon />}
                      onClick={() => markAsRead(notification.id)}
                    >
                      Mark as Read
                    </Button>
                  )}
                </Box>
              </CardActions>
            </Card>
          ))}
        </Stack>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={(_, page) => setCurrentPage(page)}
            color="primary"
            size="large"
            showFirstButton
            showLastButton
          />
        </Box>
      )}

      {/* Floating Action Button for Mark All as Read */}
      {notifications.some(n => !n.is_read) && (
        <Fab
          color="primary"
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            zIndex: theme.zIndex.fab
          }}
          onClick={markAllAsRead}
        >
          <MarkEmailReadIcon />
        </Fab>
      )}
    </Box>
  );
};

export default NotificationsPage;
