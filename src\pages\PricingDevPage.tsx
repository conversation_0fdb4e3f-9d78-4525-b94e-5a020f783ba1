import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  alpha,
  AppBar,
  <PERSON><PERSON>bar,
  IconButton
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Star as StarIcon,
  School as SchoolIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  ArrowBack as ArrowBackIcon,
  Home as HomeIcon,
  BugReport as BugReportIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import BillingToggle from '../components/UI/BillingToggle';
import StripeCheckoutButton from '../components/Payment/StripeCheckoutButton';
import { config, isStripeEnabled, getStripePriceId } from '../config/environment';

interface PricingTier {
  id: string;
  name: string;
  price: string;
  period: string;
  monthlyPrice?: string;
  annualPrice?: string;
  annualSavings?: string;
  billingOptions?: ('monthly' | 'annual')[];
  description: string;
  features: Array<{
    name: string;
    included: boolean;
    description?: string;
  }>;
  highlighted?: boolean;
  icon: React.ReactNode;
  buttonText: string;
  buttonVariant: 'contained' | 'outlined';
  badge?: string;
  color: string;
  emailRequirement?: string;
  stripeEnabled?: boolean;
}

const PricingDevPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const location = useLocation();
  const [visible, setVisible] = useState(false);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');

  useEffect(() => {
    const timer = setTimeout(() => setVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  // Redirect if not in development mode
  useEffect(() => {
    if (!config.isDevelopment) {
      navigate('/pricing');
    }
  }, [navigate]);

  // Handle subscription resumption after login
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    if (urlParams.get('returnTo') === 'subscription') {
      const subscriptionIntent = sessionStorage.getItem('subscriptionIntent');
      if (subscriptionIntent) {
        try {
          const intent = JSON.parse(subscriptionIntent);
          // Check if intent is recent (within 10 minutes)
          if (Date.now() - intent.timestamp < 10 * 60 * 1000) {
            sessionStorage.removeItem('subscriptionIntent');
            // You could show a modal or scroll to the relevant plan here
            console.log('Resuming subscription for:', intent.planName);
          }
        } catch (error) {
          console.error('Error parsing subscription intent:', error);
          sessionStorage.removeItem('subscriptionIntent');
        }
      }
    }
  }, [location]);

  const pricingTiers: PricingTier[] = [
    {
      id: 'guest',
      name: 'Guest Access',
      price: 'Free',
      period: 'Forever',
      description: 'Perfect for exploring and learning statistical analysis',
      icon: <PersonIcon sx={{ fontSize: 32 }} />,
      color: '#4caf50',
      buttonText: 'Start Exploring',
      buttonVariant: 'outlined',
      stripeEnabled: false,
      features: [
        { name: 'Full app exploration', included: true, description: 'Access all features and interface' },
        { name: 'Pro features preview', included: true, description: 'See advanced analysis capabilities' },
        { name: 'Built-in sample datasets', included: true, description: 'Practice with curated datasets' },
        { name: 'Teaching & demonstration', included: true, description: 'Perfect for educational use' },
        { name: 'Personal data import', included: false },
        { name: 'Cloud synchronization', included: false },
        { name: 'Multi-device access', included: false }
      ]
    },
    {
      id: 'standard',
      name: 'Standard Account',
      price: 'Free',
      period: 'Currently',
      description: 'Full local analysis capabilities with personal data',
      icon: <BusinessIcon sx={{ fontSize: 32 }} />,
      color: '#2196f3',
      buttonText: 'Create Account',
      buttonVariant: 'outlined',
      badge: 'Most Popular',
      stripeEnabled: false,
      features: [
        { name: 'All Guest Access features', included: true },
        { name: 'Personal data import', included: true, description: 'Upload CSV, Excel, and other formats' },
        { name: 'Local data storage', included: true, description: 'Data saved in your browser' },
        { name: 'Full analysis suite', included: true, description: 'Complete statistical toolkit' },
        { name: 'Export capabilities', included: true, description: 'Save results and visualizations' },
        { name: 'Pro analysis features', included: false, description: 'Advanced statistical methods' },
        { name: 'Cloud synchronization', included: false },
        { name: 'Multi-device access', included: false }
      ]
    },
    {
      id: 'pro',
      name: 'Pro Account',
      price: billingCycle === 'monthly' ? '$10' : '$96',
      period: billingCycle === 'monthly' ? 'per month' : 'per year',
      monthlyPrice: '$10',
      annualPrice: '$96',
      annualSavings: '20%',
      billingOptions: ['monthly', 'annual'],
      description: 'Professional analysis with cloud features and advanced tools',
      icon: <StarIcon sx={{ fontSize: 32 }} />,
      color: '#ff9800',
      buttonText: isStripeEnabled() ? 'Subscribe Now' : 'Request Early Access',
      buttonVariant: 'contained',
      highlighted: true,
      stripeEnabled: isStripeEnabled(),
      features: [
        { name: 'All Standard features', included: true },
        { name: 'Advanced Analysis', included: true, description: 'Advanced statistical methods' },
        { name: 'Publication Ready', included: true, description: 'APA tables, methods text, figures' },
        { name: 'Cloud data storage', included: true, description: 'Secure cloud backup' },
        { name: 'Multi-device sync', included: true, description: 'Access from anywhere' },
        { name: 'Priority support', included: true, description: 'Faster response times' },
        { name: 'Collaboration tools', included: true, description: 'Share projects with team' },
        { name: 'API access', included: true, description: 'Integrate with other tools' }
      ]
    },
    {
      id: 'edu',
      name: 'Educational Account',
      price: 'Free',
      period: 'for .edu emails',
      emailRequirement: 'Educational email required (.edu)',
      description: 'Advanced Analysis included free for educational users',
      icon: <SchoolIcon sx={{ fontSize: 32 }} />,
      color: '#9c27b0',
      buttonText: 'Create Educational Account',
      buttonVariant: 'outlined',
      badge: 'Advanced Analysis Free',
      stripeEnabled: false,
      features: [
        { name: 'All Standard Account features', included: true },
        { name: 'Advanced Analysis', included: true, description: 'FREE for .edu users' },
        { name: 'Advanced statistical tests', included: true, description: 'ANOVA, Regression, etc.' },
        { name: 'Interactive visualizations', included: true, description: 'Professional charts' },
        { name: 'Publication Ready', included: false, description: 'Upgrade to Pro for $10/month' },
        { name: 'Cloud Storage', included: false, description: 'Upgrade to Pro for $10/month' },
        { name: 'Multi-device sync', included: false, description: 'Upgrade to Pro for $10/month' }
      ],
      upgradeOption: {
        name: 'Educational Pro',
        price: '$10/month',
        description: 'Same price as regular Pro - no educational discount',
        features: [
          'Keep all current features',
          'Add Publication Ready tools',
          'Add Cloud Storage',
          'Priority support'
        ]
      }
    }
  ];

  const handleGetStarted = (tierId: string) => {
    switch (tierId) {
      case 'guest':
        navigate('/app');
        break;
      case 'standard':
        navigate('/app#/auth/login');
        break;
      default:
        // For pro and edu, Stripe integration will handle this
        break;
    }
  };

  return (
    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Development Header */}
      <AppBar position="static" sx={{ bgcolor: 'warning.main' }}>
        <Toolbar>
          <BugReportIcon sx={{ mr: 1 }} />
          <Typography variant="h6" sx={{ flexGrow: 1, color: 'warning.contrastText' }}>
            DEVELOPMENT MODE - Stripe Integration Testing
          </Typography>
          <Button
            color="inherit"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/pricing')}
          >
            Back to Production Pricing
          </Button>
        </Toolbar>
      </AppBar>

      {/* Navigation Header */}
      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 1 }}>
        <Container maxWidth="lg">
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton
                color="inherit"
                onClick={() => navigate('/')}
                sx={{ p: 1 }}
              >
                <HomeIcon />
              </IconButton>
              <Typography variant="h6" fontWeight="bold">
                DataStatPro
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Development Pricing Page
            </Typography>
          </Box>
        </Container>
      </Box>

      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Fade in={visible} timeout={600}>
          <Box textAlign="center" mb={6}>
            <Typography
              variant="h2"
              component="h1"
              fontWeight="bold"
              sx={{
                mb: 2,
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Choose Your Plan
            </Typography>
            <Typography
              variant="h5"
              color="text.secondary"
              sx={{ mb: 4, maxWidth: '600px', mx: 'auto', lineHeight: 1.6 }}
            >
              Select the perfect plan for your statistical analysis needs
            </Typography>
            
            {/* Stripe Status Alert */}
            <Alert
              severity={isStripeEnabled() ? 'success' : 'info'}
              sx={{
                maxWidth: '800px',
                mx: 'auto',
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-message': {
                  fontSize: '1rem'
                }
              }}
            >
              <Typography variant="body1" fontWeight="medium">
                {isStripeEnabled() ? '✅ Stripe Integration Active' : '🚧 Development Mode - Email Integration'}
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                {isStripeEnabled() 
                  ? 'Stripe test mode is enabled. Use test card numbers for payments.'
                  : 'Stripe integration is disabled. Subscription requests will use email contact.'
                }
              </Typography>
            </Alert>

            {/* Billing Cycle Toggle - Only show for Stripe-enabled tiers */}
            {isStripeEnabled() && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
                <BillingToggle
                  value={billingCycle}
                  onChange={setBillingCycle}
                  showSavings={true}
                  savingsText="Save 20%"
                />
              </Box>
            )}
          </Box>
        </Fade>

        {/* Pricing Cards */}
        <Grid container spacing={4} justifyContent="center">
          {pricingTiers.map((tier, index) => (
            <Grid item xs={12} sm={6} lg={3} key={tier.id}>
              <Fade in={visible} timeout={800 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    borderRadius: 3,
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    ...(tier.highlighted && {
                      border: `2px solid ${tier.color}`,
                      transform: 'scale(1.05)',
                      boxShadow: `0 12px 40px ${alpha(tier.color, 0.2)}`,
                    }),
                    '&:hover': {
                      transform: tier.highlighted ? 'scale(1.05)' : 'translateY(-8px)',
                      boxShadow: `0 16px 50px ${alpha(tier.color, 0.15)}`,
                    }
                  }}
                >
                  {/* Badge */}
                  {tier.badge && (
                    <Chip
                      label={tier.badge}
                      sx={{
                        position: 'absolute',
                        top: 16,
                        right: 16,
                        bgcolor: tier.color,
                        color: 'white',
                        fontWeight: 'bold',
                        zIndex: 1
                      }}
                    />
                  )}

                  <CardContent sx={{ flexGrow: 1, p: 3 }}>
                    <Box textAlign="center" mb={3}>
                      <Box
                        sx={{
                          width: 64,
                          height: 64,
                          borderRadius: '50%',
                          bgcolor: alpha(tier.color, 0.1),
                          color: tier.color,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mx: 'auto',
                          mb: 2
                        }}
                      >
                        {tier.icon}
                      </Box>
                      <Typography variant="h5" fontWeight="bold" gutterBottom>
                        {tier.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {tier.description}
                      </Typography>
                      <Box>
                        <Typography
                          variant="h3"
                          component="span"
                          fontWeight="bold"
                          color={tier.color}
                        >
                          {tier.price}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" component="span">
                          {tier.period && ` ${tier.period}`}
                        </Typography>
                        {tier.billingOptions && billingCycle === 'annual' && tier.annualSavings && (
                          <Box sx={{ mt: 1 }}>
                            <Chip
                              label={`Save ${tier.annualSavings}`}
                              size="small"
                              color="success"
                              sx={{ fontSize: '0.75rem' }}
                            />
                            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                              ${(parseFloat(tier.annualPrice?.replace('$', '') || '0') / 12).toFixed(0)}/month when billed annually
                            </Typography>
                          </Box>
                        )}
                        {tier.emailRequirement && (
                          <Typography variant="caption" color="warning.main" sx={{ display: 'block', mt: 1, fontStyle: 'italic' }}>
                            {tier.emailRequirement}
                          </Typography>
                        )}
                      </Box>
                    </Box>

                    <Divider sx={{ mb: 2 }} />

                    {/* Features List */}
                    <List dense sx={{ p: 0 }}>
                      {tier.features.map((feature, featureIndex) => (
                        <ListItem key={featureIndex} sx={{ px: 0, py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            {feature.included ? (
                              <CheckIcon sx={{ color: 'success.main', fontSize: 20 }} />
                            ) : (
                              <CancelIcon sx={{ color: 'text.disabled', fontSize: 20 }} />
                            )}
                          </ListItemIcon>
                          <ListItemText
                            primary={feature.name}
                            secondary={feature.description}
                            primaryTypographyProps={{
                              variant: 'body2',
                              color: feature.included ? 'text.primary' : 'text.disabled'
                            }}
                            secondaryTypographyProps={{
                              variant: 'caption',
                              sx: { fontSize: '0.7rem' }
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>

                  <CardActions sx={{ p: 3, pt: 0 }}>
                    {tier.stripeEnabled && (tier.id === 'pro' || tier.id === 'edu') ? (
                      <StripeCheckoutButton
                        priceId={getStripePriceId(tier.id as 'pro' | 'edu', billingCycle)}
                        planName={tier.name}
                        billingCycle={billingCycle}
                        requiresEducationalEmail={tier.id === 'edu'}
                      >
                        {tier.buttonText}
                      </StripeCheckoutButton>
                    ) : (
                      <Button
                        variant={tier.buttonVariant}
                        color="primary"
                        fullWidth
                        size="large"
                        onClick={() => handleGetStarted(tier.id)}
                        sx={{
                          py: 1.5,
                          fontWeight: 'bold',
                          textTransform: 'none',
                          borderRadius: 2,
                          ...(tier.buttonVariant === 'contained' && {
                            bgcolor: tier.color,
                            '&:hover': {
                              bgcolor: tier.color,
                              filter: 'brightness(0.9)',
                            }
                          })
                        }}
                      >
                        {tier.buttonText}
                      </Button>
                    )}
                  </CardActions>
                </Card>
              </Fade>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default PricingDevPage;
