import React from 'react';
import { Box, Container, Typography, useTheme } from '@mui/material';
import { Helmet } from 'react-helmet-async';

const PrivacyPolicyPage: React.FC = () => {
  const theme = useTheme();

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Helmet>
        <title>Privacy Policy - DataStatPro</title>
        <meta name="description" content="DataStatPro Privacy Policy" />
      </Helmet>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
          DataStatPro Privacy Policy
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Last updated: June 20, 2025
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Introduction
        </Typography>
        <Typography variant="body1">
          This policy applies to DataStatPro (www.datastatpro.com) and outlines how we handle your data.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Data Collection
        </Typography>
        <Typography variant="body1">
          We do not collect or store personal information. Use DataStatPro anonymously—no accounts, tracking, or identifiable data required.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Analytics
        </Typography>
        <Typography variant="body1">
          Google Analytics collects anonymized technical data (e.g., device type, browser) to improve user experience. IP addresses are anonymized; no personal identification is possible. Details in our Cookie Policy.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Cloud Integration
        </Typography>
        <Typography variant="body1">
          You may import datasets from services like Google Drive or Dropbox. Login occurs via their secure portals; we only access files you explicitly select (e.g., spreadsheets). Data is processed locally—never stored on our servers.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Data Security
        </Typography>
        <Typography variant="body1">
          All analyses occur on your device. No data leaves your browser or interacts with external servers, ensuring full confidentiality. Suitable for sensitive/regulated information.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          GDPR Compliance
        </Typography>
        <Typography variant="body1">
          DataStatPro adheres to GDPR principles. Data retrieved from cloud platforms flows directly to your device—no intermediary storage or processing by us.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Policy Updates
        </Typography>
        <Typography variant="body1">
          We may update this policy periodically. Check the "Last updated" date for changes.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          Contact
        </Typography>
        <Typography variant="body1">
          Questions? Reach <NAME_EMAIL>.
        </Typography>
      </Box>
    </Container>
  );
};

export default PrivacyPolicyPage;
