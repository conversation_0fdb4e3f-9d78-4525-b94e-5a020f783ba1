import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Box, Typography, Container, Alert } from '@mui/material';
import PublicationReadyOptions from '../components/PublicationReady/PublicationReadyOptions';

interface PublicationReadyPageProps {
  onNavigate: (path: string) => void;
}

const PublicationReadyPage: React.FC<PublicationReadyPageProps> = ({ onNavigate }) => {
  const { canAccessPublicationReady, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !canAccessPublicationReady) {
      navigate('/dashboard', { state: { message: 'Access to Publication Ready features requires Pro or Educational account, or use as Guest with sample data.' } });
    }
  }, [canAccessPublicationReady, loading, navigate]);

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6">Loading...</Typography>
      </Box>
    );
  }

  if (!canAccessPublicationReady) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6">Access Denied</Typography>
        <Typography variant="body2" color="text.secondary">
          Publication Ready features require Pro or Educational account, or use as Guest with sample data.
        </Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <PublicationReadyOptions onNavigate={onNavigate} />
      </Box>
    </Container>
  );
};
export default PublicationReadyPage;
