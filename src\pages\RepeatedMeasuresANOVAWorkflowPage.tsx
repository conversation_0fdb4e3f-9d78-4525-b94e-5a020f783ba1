import React from 'react';
import { Box, Container } from '@mui/material';
import { PageTitle } from '@/components/UI'; // Assuming PageTitle is exported from UI barrel
import { RepeatedMeasuresANOVA } from '@/components/InferentialStats/ANOVA/RepeatedMeasuresANOVA';

const RepeatedMeasuresANOVAWorkflowPage: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <PageTitle
        title="Repeated Measures ANOVA"
        description="Analyze differences across multiple measurements on the same subjects." // Use description prop
      />
      <Box sx={{ mt: 3 }}>
        <RepeatedMeasuresANOVA />
      </Box>
    </Container>
  );
};

export default RepeatedMeasuresANOVAWorkflowPage;
