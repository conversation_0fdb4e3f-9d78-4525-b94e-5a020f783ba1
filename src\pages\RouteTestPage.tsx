import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Alert,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import { routeRegistry } from '../routing/RouteRegistry';

const RouteTestPage: React.FC = () => {
  const [currentHash, setCurrentHash] = useState(window.location.hash);
  const [allRoutes, setAllRoutes] = useState<any[]>([]);
  const [routeResolution, setRouteResolution] = useState<any>(null);

  useEffect(() => {
    // Update route information
    const updateRouteInfo = () => {
      setCurrentHash(window.location.hash);
      setAllRoutes(routeRegistry.getAllRoutes());
      
      // Test route resolution
      const hash = window.location.hash.slice(1);
      const pathSegments = hash.split('/').filter(Boolean);
      let page = 'home';
      let subPage = '';
      
      if (pathSegments.length > 0) {
        if (pathSegments[0] === 'app' && pathSegments.length > 1) {
          page = pathSegments[1];
          subPage = pathSegments[2] || '';
        } else {
          page = pathSegments[0];
          subPage = pathSegments[1] || '';
        }
      }
      
      const foundRoute = routeRegistry.findMatchingRoute(page, subPage);
      setRouteResolution({
        hash,
        pathSegments,
        page,
        subPage,
        foundRoute: foundRoute ? {
          path: foundRoute.path,
          componentName: foundRoute.component?.name,
          requiresAuth: foundRoute.requiresAuth,
          allowGuest: foundRoute.allowGuest
        } : null
      });
    };

    updateRouteInfo();
    
    // Listen for hash changes
    window.addEventListener('hashchange', updateRouteInfo);
    
    return () => {
      window.removeEventListener('hashchange', updateRouteInfo);
    };
  }, []);

  const testRoutes = [
    '#home',
    '#dashboard',
    '#inference/ttest',
    '#inference/anova',
    '#stats/descriptives',
    '#data-management/import',
    '#publication-ready/table1',
    '#advanced-analysis/cfa'
  ];

  const navigateToRoute = (route: string) => {
    window.location.hash = route;
  };

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Route Testing & Debugging
      </Typography>
      
      <Grid container spacing={3}>
        {/* Current Route Status */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Current Route Status
            </Typography>
            
            <Box mb={2}>
              <Typography variant="body2" color="text.secondary">
                Current Hash:
              </Typography>
              <Chip label={currentHash || '(empty)'} variant="outlined" />
            </Box>

            {routeResolution && (
              <>
                <Typography variant="body2" color="text.secondary">
                  Parsed Page: <strong>{routeResolution.page}</strong>
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Parsed SubPage: <strong>{routeResolution.subPage || '(none)'}</strong>
                </Typography>
                
                <Divider sx={{ my: 2 }} />
                
                {routeResolution.foundRoute ? (
                  <Alert severity="success">
                    <Typography variant="body2">
                      <strong>Route Found:</strong> {routeResolution.foundRoute.path}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Component:</strong> {routeResolution.foundRoute.componentName}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Requires Auth:</strong> {routeResolution.foundRoute.requiresAuth ? 'Yes' : 'No'}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Allow Guest:</strong> {routeResolution.foundRoute.allowGuest ? 'Yes' : 'No'}
                    </Typography>
                  </Alert>
                ) : (
                  <Alert severity="error">
                    <Typography variant="body2">
                      <strong>No Route Found</strong> for page "{routeResolution.page}" 
                      {routeResolution.subPage && ` and subPage "${routeResolution.subPage}"`}
                    </Typography>
                  </Alert>
                )}
              </>
            )}
          </Paper>
        </Grid>

        {/* Route Testing */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Test Routes
            </Typography>
            
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Click to navigate to different routes:
            </Typography>
            
            <Box display="flex" flexDirection="column" gap={1}>
              {testRoutes.map((route) => (
                <Button
                  key={route}
                  variant="outlined"
                  size="small"
                  onClick={() => navigateToRoute(route)}
                  sx={{ justifyContent: 'flex-start' }}
                >
                  {route}
                </Button>
              ))}
            </Box>
          </Paper>
        </Grid>

        {/* All Registered Routes */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              All Registered Routes ({allRoutes.length})
            </Typography>
            
            <List dense>
              {allRoutes.slice(0, 20).map((route, index) => (
                <ListItem key={index} divider>
                  <ListItemText
                    primary={route.path}
                    secondary={`Component: ${route.component?.name || 'Unknown'} | Auth: ${route.requiresAuth ? 'Required' : 'Not Required'} | Guest: ${route.allowGuest ? 'Allowed' : 'Not Allowed'}`}
                  />
                </ListItem>
              ))}
              {allRoutes.length > 20 && (
                <ListItem>
                  <ListItemText
                    primary={`... and ${allRoutes.length - 20} more routes`}
                    secondary="Check browser console for full list"
                  />
                </ListItem>
              )}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RouteTestPage;
