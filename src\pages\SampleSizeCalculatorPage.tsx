import React, { useState, useEffect } from 'react';
import { Box, Typography, Tabs, Tab } from '@mui/material';
import { useLocation } from 'react-router-dom'; // Import useLocation
import OneSampleCalculator from '../components/SampleSizeCalculators/OneSampleCalculator';
import TwoSampleCalculator from '../components/SampleSizeCalculators/TwoSampleCalculator';
import PairedSampleCalculator from '../components/SampleSizeCalculators/PairedSampleCalculator';
import MoreThanTwoGroupsCalculator from '../components/SampleSizeCalculators/MoreThanTwoGroupsCalculator';
import TabPanel from '../components/UI/TabPanel';

const SampleSizeCalculatorPage: React.FC = () => {
  const location = useLocation(); // Get the current location
  const [value, setValue] = useState(0);

  useEffect(() => {
    // Parse the query parameter 'tab' from the URL
    const params = new URLSearchParams(location.search);
    const tabParam = params.get('tab');
    if (tabParam !== null) {
      const tabIndex = parseInt(tabParam, 10);
      if (!isNaN(tabIndex) && tabIndex >= 0 && tabIndex <= 3) { // Validate tab index
        setValue(tabIndex);
      }
    }
  }, [location.search]); // Re-run effect when location search changes

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Sample Size Calculator
      </Typography>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={value} onChange={handleChange} aria-label="sample size calculator tabs">
          <Tab label="One-Sample Scenarios" />
          <Tab label="Two Samples Scenarios" />
          <Tab label="Paired Samples Scenarios" />
          <Tab label="More than 2 Groups" />
        </Tabs>
      </Box>
      <TabPanel value={value} index={0}>
        <OneSampleCalculator />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <TwoSampleCalculator />
      </TabPanel>
      <TabPanel value={value} index={2}>
        <PairedSampleCalculator />
      </TabPanel>
      <TabPanel value={value} index={3}>
        <MoreThanTwoGroupsCalculator />
      </TabPanel>
    </Box>
  );
};

export default SampleSizeCalculatorPage;
