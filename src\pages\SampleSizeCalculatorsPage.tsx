import React from 'react';
import { Box, Container } from '@mui/material';
import SampleSizeCalculatorsOptions from '../components/SampleSizeCalculators/SampleSizeCalculatorsOptions';

interface SampleSizeCalculatorsPageProps {
  onNavigate: (path: string) => void;
}

const SampleSizeCalculatorsPage: React.FC<SampleSizeCalculatorsPageProps> = ({ onNavigate }) => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <SampleSizeCalculatorsOptions onNavigate={onNavigate} />
      </Box>
    </Container>
  );
};

export default SampleSizeCalculatorsPage;
