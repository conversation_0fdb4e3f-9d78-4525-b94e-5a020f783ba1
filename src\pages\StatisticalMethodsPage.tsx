import React, { useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import { Box, Typography, Paper, List, ListItem, ListItemText, Divider, Grid, ListItemButton } from '@mui/material';
import PageTitle from '../components/UI/PageTitle';
import { Science as ScienceIcon, Functions as FunctionsIcon, CompareArrows as CompareArrowsIcon, BarChart as BarChartIcon, Timeline as TimelineIcon } from '@mui/icons-material';

interface Section {
  id: string;
  title: string;
  icon?: React.ReactElement;
  content: React.ReactNode;
}

const sections: Section[] = [
  {
    id: 'desc-numerical',
    title: 'Descriptive Analysis of Numerical Data',
    icon: <FunctionsIcon />,
    content: (
      <Typography variant="body1" paragraph>
        Descriptive analysis summarizes numerical data using measures of central tendency (mean, median, mode) and spread (range, variance, standard deviation, IQR). It helps identify patterns, outliers, and data distribution shapes (e.g., normal, skewed). Tools like histograms, box plots, and density plots visualize distributions, while summary tables provide key metrics. For example, calculating the average income and standard deviation in a population reveals income inequality. Use this analysis to explore data before advanced modeling.
      </Typography>
    ),
  },
  {
    id: 'desc-categorical',
    title: 'Descriptive Analysis of Categorical Data',
    icon: <FunctionsIcon />,
    content: (
      <Typography variant="body1" paragraph>
        For categorical variables (e.g., gender, product categories), summarize data using frequency tables, proportions, or percentages. Bar charts and pie charts display category distributions, while mode identifies the most frequent value. For ordinal data (e.g., survey ratings), median or percentile rankings add depth. For instance, a frequency table showing "70% of customers prefer Product A" highlights dominant trends. This analysis sets the stage for inferential tests like chi-square.
      </Typography>
    ),
  },
  {
    id: 't-tests',
    title: 't-Tests',
    icon: <ScienceIcon />,
    content: (
      <>
        <Typography variant="body1" paragraph>
          <strong>One-sample t-test:</strong> Compares a sample mean to a known population mean (e.g., testing if average exam scores differ from 75).
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>Paired t-test:</strong> Measures mean differences in related groups (e.g., pre- vs. post-treatment results).
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>Independent samples t-test:</strong> Compares means between two unrelated groups (e.g., male vs. female salaries).
        </Typography>
        <Typography variant="body1" paragraph>
          Assumptions: Normality, equal variance (use Welch’s t-test if variances differ).
        </Typography>
      </>
    ),
  },
  {
    id: 'non-parametric',
    title: 'Non-Parametric Tests',
    icon: <ScienceIcon />,
    content: (
      <>
        <Typography variant="body1" paragraph>
          Use when data violates normality or is ordinal:
        </Typography>
        <List dense>
          <ListItem><ListItemText primary="Mann-Whitney U test: Non-parametric alternative to the independent t-test." /></ListItem>
          <ListItem><ListItemText primary="Wilcoxon signed-rank test: Replaces paired t-test for non-normal data." /></ListItem>
          <ListItem><ListItemText primary="Kruskal-Wallis test: Compares medians across ≥3 groups (alternative to ANOVA)." /></ListItem>
        </List>
      </>
    ),
  },
  {
    id: 'anova',
    title: 'ANOVA',
    icon: <ScienceIcon />,
    content: (
      <>
        <Typography variant="body1" paragraph>
          <strong>One-Way ANOVA:</strong> Tests mean differences across ≥3 independent groups (e.g., comparing crop yields for three fertilizers). Post-hoc tests (Tukey, Bonferroni) identify which groups differ.
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>Two-Way ANOVA:</strong> Examines the effect of two independent variables (factors) and their interaction (e.g., testing how both diet and exercise affect weight loss).
        </Typography>
      </>
    ),
  },
  {
    id: 'correlation-chi-square',
    title: 'Correlation & Chi-Square Test',
    icon: <CompareArrowsIcon />,
    content: (
      <>
        <Typography variant="body1" paragraph>
          <strong>Correlation (Pearson/Spearman):</strong> Quantifies linear (Pearson) or monotonic (Spearman) relationships between two numerical variables (e.g., height vs. weight).
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>Chi-Square Test of Association:</strong> Tests independence between categorical variables (e.g., checking if education level and job type are related).
        </Typography>
      </>
    ),
  },
  {
    id: 'regression',
    title: 'Regression Models',
    icon: <TimelineIcon />,
    content: (
      <>
        <Typography variant="body1" paragraph>
          <strong>Linear Regression:</strong> Predicts a continuous outcome using one/more predictors (e.g., forecasting sales based on ad spend).
        </Typography>
        <Typography variant="body1" paragraph>
          <strong>Logistic Regression:</strong> Predicts binary outcomes (e.g., yes/no) using predictors (e.g., likelihood of loan default based on income/credit score).
        </Typography>
      </>
    ),
  },
  {
    id: 'essential-charts',
    title: 'Essential Charts',
    icon: <BarChartIcon />,
    content: (
      <List dense>
        <ListItem><ListItemText primary="Bar Chart: Compares categories (e.g., sales by region)." /></ListItem>
        <ListItem><ListItemText primary="Pie Chart: Shows proportions (limit to ≤6 categories)." /></ListItem>
        <ListItem><ListItemText primary="Scatter Plot: Reveals relationships/correlations between variables." /></ListItem>
        <ListItem><ListItemText primary="Histogram: Displays numerical data distribution (e.g., age frequencies)." /></ListItem>
        <ListItem><ListItemText primary="Box Plot: Summarizes spread, median, and outliers (e.g., income distribution by education)." /></ListItem>
      </List>
    ),
  },
];

const StatisticalMethodsPage: React.FC = () => {
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);

  const scrollToSection = (index: number) => {
    sectionRefs.current[index]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  return (
    <>
      <Helmet>
        <title>Statistical Methods Guide - DataStatPro</title>
        <meta name="description" content="Overview of common statistical methods including descriptive analysis, t-tests, non-parametric tests, ANOVA, correlation, chi-square, regression models, and essential charts." />
      </Helmet>
      <Box sx={{ p: 3 }}>
        <PageTitle title="Statistical Methods" />
        <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, position: 'sticky', top: '80px' /* Adjust based on header height */ }}>
            <Typography variant="h6" gutterBottom>
              Methods Overview
            </Typography>
            <List component="nav" dense>
              {sections.map((section, index) => (
                <ListItemButton key={section.id} onClick={() => scrollToSection(index)}>
                  {section.icon && <Box sx={{ mr: 1.5, display: 'flex', alignItems: 'center', color: 'primary.main' }}>{React.cloneElement(section.icon, { fontSize: 'small' })}</Box>}
                  <ListItemText primary={section.title} primaryTypographyProps={{ variant: 'body2' }}/>
                </ListItemButton>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={9}>
          {sections.map((section, index) => (
            <Paper 
              key={section.id} 
              sx={{ p: 3, mb: 3 }} 
              ref={el => sectionRefs.current[index] = el}
              id={section.id}
            >
              <Typography variant="h5" gutterBottom component="div" sx={{display: 'flex', alignItems: 'center'}}>
                 {section.icon && <Box sx={{ mr: 1, display: 'flex', alignItems: 'center', color: 'primary.main' }}>{section.icon}</Box>}
                {section.title}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {section.content}
            </Paper>
          ))}
        </Grid>
      </Grid>
    </Box>
    </>
  );
};

export default StatisticalMethodsPage;
