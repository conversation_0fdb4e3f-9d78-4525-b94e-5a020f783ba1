import React from 'react';
import { Box, Container, Typography, useTheme, List, ListItem, ListItemIcon, ListItemText } from '@mui/material';
import { Helmet } from 'react-helmet-async';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';

const TermsOfServicePage: React.FC = () => {
  const theme = useTheme();

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Helmet>
        <title>Terms of Service - DataStatPro</title>
        <meta name="description" content="DataStatPro Terms of Service" />
      </Helmet>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
          DataStatPro Terms of Service
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Effective: June 20, 2025
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          1. Acceptance
        </Typography>
        <Typography variant="body1">
          By accessing DataStatPro (www.datastatpro.com) or its subdomains, you agree to these terms. If you disagree, discontinue use immediately. Content is protected by copyright/trademark laws.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          2. Permitted Use
        </Typography>
        <Typography variant="body1">
          You may download materials for personal/non-commercial use, including installing the PWA on personal devices. Automatic updates may modify/remove features. License terminates if terms are violated; destroy downloaded materials except legally installed PWA copies.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          3. Prohibited Activities
        </Typography>
        <Typography variant="body1" sx={{ mb: 1 }}>
          You agree not to:
        </Typography>
        <List dense>
          <ListItem>
            <ListItemIcon>
              <CheckCircleOutlineIcon fontSize="small" color="primary" />
            </ListItemIcon>
            <ListItemText primary="Use for illegal/fraudulent purposes;" />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <CheckCircleOutlineIcon fontSize="small" color="primary" />
            </ListItemIcon>
            <ListItemText primary="Reverse engineer, scrape, or disrupt services;" />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <CheckCircleOutlineIcon fontSize="small" color="primary" />
            </ListItemIcon>
            <ListItemText primary="Use commercially, modify content, or redistribute materials." />
          </ListItem>
        </List>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          4. Intellectual Property
        </Typography>
        <Typography variant="body1">
          All content, trademarks, and software remain DataStatPro’s property. Limited access granted; no derivatives allowed without permission.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          5. Privacy
        </Typography>
        <Typography variant="body1">
          Data use governed by our Privacy Policy.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          6. Disclaimer & Liability
        </Typography>
        <Typography variant="body1">
          Content provided “as is.” No warranties for accuracy, fitness, or non-infringement. We’re not liable for damages from website use.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          7. Third-Party Content
        </Typography>
        <Typography variant="body1">
          Links or integrations to third-party services are subject to their terms. We disclaim responsibility for their content/functionality.
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          8. Modifications
        </Typography>
        <Typography variant="body1">
          Terms may be updated anytime. Service access may be modified/terminated at our discretion.
        </Typography>
      </Box>
    </Container>
  );
};

export default TermsOfServicePage;
