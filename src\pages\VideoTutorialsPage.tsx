import React, { useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import { Box, Typography, Paper, List, ListItem, ListItemText, Divider, Grid, ListItemButton } from '@mui/material';
import PageTitle from '../components/UI/PageTitle';
import { PlayCircleOutline as PlayCircleOutlineIcon } from '@mui/icons-material';

interface Section {
  id: string;
  title: string;
  icon?: React.ReactElement;
  content: React.ReactNode;
}


const sections: Section[] = [
  {
    id: 'tutorial-1',
    title: '1. Getting Started with DataStatPro',
    icon: <PlayCircleOutlineIcon />,
    content: (
      <Box>
        <Typography variant="body1" paragraph>
          Learn the basics of navigating and using DataStatPro.
        </Typography>
        <iframe width="560" height="315" src="https://www.youtube.com/embed/A_3nZtut4g0?si=Xqm60Vvk3jrYBc9J" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
      </Box>
    ),
  },
  {
    id: 'tutorial-2',
    title: '2. Importing Your Data',
    icon: <PlayCircleOutlineIcon />,
    content: (
      <Box>
        <Typography variant="body1" paragraph>
          A step-by-step guide on importing your datasets into the platform.
        </Typography>
         <iframe width="560" height="315" src="https://www.youtube.com/embed/QNp-WLkbLq0" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
      </Box>
    ),
  },
   {
    id: 'tutorial-3',
    title: '3. Performing Descriptive Analysis',
    icon: <PlayCircleOutlineIcon />,
    content: (
      <Box>
        <Typography variant="body1" paragraph>
          Learn how to get summary statistics and visualize your data distribution.
        </Typography>
         <iframe width="560" height="315" src="https://www.youtube.com/embed/QNp-WLkbLq0" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
      </Box>
    ),
  },
   {
    id: 'tutorial-4',
    title: '4. Performing t-tests',
    icon: <PlayCircleOutlineIcon />,
    content: (
      <Box>
        <Typography variant="body1" paragraph>
          Learn how to perform t-tests in DataStatPro.
        </Typography>
         <iframe width="560" height="315" src="https://www.youtube.com/embed/QNp-WLkbLq0" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
      </Box>
    ),
  },
   {
    id: 'tutorial-5',
    title: '5. Performing ANOVA tests',
    icon: <PlayCircleOutlineIcon />,
    content: (
      <Box>
        <Typography variant="body1" paragraph>
          Learn how to perform ANOVA tests in DataStatPro.
        </Typography>
         <iframe width="560" height="315" src="https://www.youtube.com/embed/QNp-WLkbLq0" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
      </Box>
    ),
  },
   {
    id: 'tutorial-6',
    title: '6. Performing Regression Analysis',
    icon: <PlayCircleOutlineIcon />,
    content: (
      <Box>
        <Typography variant="body1" paragraph>
          Learn how to perform regression analysis in DataStatPro.
        </Typography>
         <iframe width="560" height="315" src="https://www.youtube.com/embed/QNp-WLkbLq0" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
      </Box>
    ),
  },
   {
    id: 'tutorial-7',
    title: '7. Creating Visualizations',
    icon: <PlayCircleOutlineIcon />,
    content: (
      <Box>
        <Typography variant="body1" paragraph>
          Learn how to create various visualizations in DataStatPro.
        </Typography>
         <iframe width="560" height="315" src="https://www.youtube.com/embed/QNp-WLkbLq0" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
      </Box>
    ),
  },
   {
    id: 'tutorial-8',
    title: '8. Pivot Analysis',
    icon: <PlayCircleOutlineIcon />,
    content: (
      <Box>
        <Typography variant="body1" paragraph>
          Learn how to perform pivot analysis in DataStatPro.
        </Typography>
         <iframe width="560" height="315" src="https://www.youtube.com/embed/QNp-WLkbLq0" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
      </Box>
    ),
  },
];

const VideoTutorialsPage: React.FC = () => {
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);

  const scrollToSection = (index: number) => {
    sectionRefs.current[index]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  return (
    <>
      <Helmet>
          <title>Video Tutorials - DataStatPro</title>
          <meta name="description" content="Watch video tutorials to learn how to use DataStatPro for data analysis and visualization." />
      </Helmet>
      <Box 
        sx={{ p: 3 }}
      >
        <PageTitle title="Video Tutorials" />
        <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, position: 'sticky', top: '80px' /* Adjust based on header height */ }}>
            <Typography variant="h6" gutterBottom>
              Tutorial Sections
            </Typography>
            <List component="nav" dense>
              {sections.map((section, index) => (
                <ListItemButton key={section.id} onClick={() => scrollToSection(index)}>
                  {section.icon && <Box sx={{ mr: 1.5, display: 'flex', alignItems: 'center', color: 'primary.main' }}>{React.cloneElement(section.icon, { fontSize: 'small' })}</Box>}
                  <ListItemText primary={section.title.replace(/^\d+\.\s*/, '')} primaryTypographyProps={{ variant: 'body2' }} />
                </ListItemButton>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={9}>
          {sections.map((section, index) => (
            <Paper
              key={section.id}
              sx={{ p: 3, mb: 3 }}
              ref={el => sectionRefs.current[index] = el}
              id={section.id}
            >
              <Typography variant="h5" gutterBottom component="div" sx={{display: 'flex', alignItems: 'center'}}>
                 {section.icon && <Box sx={{ mr: 1, display: 'flex', alignItems: 'center', color: 'primary.main' }}>{section.icon}</Box>}
                {section.title}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {section.content}
            </Paper>
          ))}
        </Grid>
      </Grid>
    </Box>
    </>
  );
};

export default VideoTutorialsPage;
