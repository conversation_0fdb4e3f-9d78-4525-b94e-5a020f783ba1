import React, { useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import { Box, Typography, Paper, List, ListItem, ListItemText, Divider, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Grid, ListItemButton } from '@mui/material';
import PageTitle from '../components/UI/PageTitle';
import { HelpOutline as HelpOutlineIcon, BarChart as BarChartIcon, Visibility as VisibilityIcon, ReportProblem as ReportProblemIcon, TableChart as TableChartIcon, Build as BuildIcon, CheckCircleOutline as CheckCircleOutlineIcon } from '@mui/icons-material';

interface Section {
  id: string;
  title: string;
  icon?: React.ReactElement;
  content: React.ReactNode;
}

const sections: Section[] = [
  {
    id: 'define-story',
    title: '1. Define Your Data Story',
    icon: <HelpOutlineIcon />,
    content: (
      <>
        <Typography variant="body1" paragraph>
          Ask: What message do you want to convey?
        </Typography>
        <List dense>
          <ListItem><ListItemText primary="Compare values (e.g., sales across regions)." /></ListItem>
          <ListItem><ListItemText primary="Show trends over time (e.g., monthly revenue growth)." /></ListItem>
          <ListItem><ListItemText primary="Display distributions (e.g., age groups in a population)." /></ListItem>
          <ListItem><ListItemText primary="Highlight proportions (e.g., market share by product)." /></ListItem>
          <ListItem><ListItemText primary="Reveal relationships (e.g., correlation between variables)." /></ListItem>
        </List>
      </>
    ),
  },
  {
    id: 'match-charts',
    title: '2. Match Chart Types to Goals',
    icon: <BarChartIcon />,
    content: (
      <>
        <Typography variant="h6" gutterBottom>A. Comparisons</Typography>
        <List dense>
          <ListItem><ListItemText primary="Bar/Column Charts: Compare discrete categories (e.g., product sales)." /></ListItem>
          <ListItem><ListItemText primary="Grouped/Stacked Bars: Compare subgroups within categories." /></ListItem>
          <ListItem><ListItemText primary="Radar Charts: Compare multiple variables (e.g., skill assessments)." /></ListItem>
        </List>

        <Typography variant="h6" gutterBottom sx={{mt:1}}>B. Trends Over Time</Typography>
        <List dense>
          <ListItem><ListItemText primary="Line Charts: Track continuous data trends (e.g., stock prices)." /></ListItem>
          <ListItem><ListItemText primary="Area Charts: Emphasize cumulative trends (e.g., total website visits)." /></ListItem>
        </List>

        <Typography variant="h6" gutterBottom sx={{mt:1}}>C. Distributions</Typography>
        <List dense>
          <ListItem><ListItemText primary="Histograms: Show frequency distribution of numerical data." /></ListItem>
          <ListItem><ListItemText primary="Box Plots: Visualize spread, outliers, and quartiles." /></ListItem>
          <ListItem><ListItemText primary="Violin Plots: Combine distribution and density (advanced)." /></ListItem>
        </List>

        <Typography variant="h6" gutterBottom sx={{mt:1}}>D. Proportions</Typography>
        <List dense>
          <ListItem><ListItemText primary="Pie/Doughnut Charts: Display parts of a whole (use ≤6 categories)." /></ListItem>
          <ListItem><ListItemText primary="Treemaps: Show hierarchical proportions (e.g., budget allocation)." /></ListItem>
        </List>

        <Typography variant="h6" gutterBottom sx={{mt:1}}>E. Relationships</Typography>
        <List dense>
          <ListItem><ListItemText primary="Scatter Plots: Reveal correlations between two variables." /></ListItem>
          <ListItem><ListItemText primary="Bubble Charts: Add a third dimension (size) to scatter plots." /></ListItem>
          <ListItem><ListItemText primary="Heatmaps: Visualize matrix data (e.g., user activity by hour)." /></ListItem>
        </List>
      </>
    ),
  },
  {
    id: 'optimize-clarity',
    title: '3. Optimize for Clarity',
    icon: <VisibilityIcon />,
    content: (
      <List dense>
        <ListItem><ListItemText primary="Avoid clutter: Simplify labels, limit colors, and remove redundant elements." /></ListItem>
        <ListItem><ListItemText primary="Prioritize accessibility: Use high-contrast colors and alt-text for digital charts." /></ListItem>
        <ListItem><ListItemText primary="Annotate key insights: Add brief notes to highlight trends or outliers." /></ListItem>
      </List>
    ),
  },
  {
    id: 'common-pitfalls',
    title: '4. Common Pitfalls to Avoid',
    icon: <ReportProblemIcon />,
    content: (
      <List dense>
        <ListItem><ListItemText primary="Misleading axes: Never truncate axes to exaggerate differences." /></ListItem>
        <ListItem><ListItemText primary="Overused 3D effects: They distort proportions (e.g., in pie charts)." /></ListItem>
        <ListItem>
          <ListItemText 
            primary="Inappropriate charts:"
            secondary={
              <>
                Don’t use pie charts for time-series data.
                <br />
                Avoid line charts for non-sequential categories.
              </>
            } 
          />
        </ListItem>
      </List>
    ),
  },
  {
    id: 'quick-reference',
    title: 'Quick Reference Table',
    icon: <TableChartIcon />,
    content: (
      <TableContainer component={Paper} sx={{ my: 0 }}> {/* Removed my:2 for tighter integration */}
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>Goal</TableCell>
              <TableCell>Recommended Charts</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow><TableCell>Compare categories</TableCell><TableCell>Bar, Column, Radar</TableCell></TableRow>
            <TableRow><TableCell>Show trends</TableCell><TableCell>Line, Area</TableCell></TableRow>
            <TableRow><TableCell>Visualize distributions</TableCell><TableCell>Histogram, Box Plot, Violin Plot</TableCell></TableRow>
            <TableRow><TableCell>Display proportions</TableCell><TableCell>Pie, Doughnut, Treemap</TableCell></TableRow>
            <TableRow><TableCell>Reveal relationships</TableCell><TableCell>Scatter Plot, Bubble Chart, Heatmap</TableCell></TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    ),
  },
  {
    id: 'tools-resources',
    title: 'Tools & Resources',
    icon: <BuildIcon />,
    content: (
      <List dense>
        <ListItem><ListItemText primary="Automated tools: Use AI-driven platforms (e.g., Tableau, Power BI) for smart chart suggestions." /></ListItem>
        <ListItem><ListItemText primary="Design frameworks: Follow Google’s Data Visualization Principles for clean, impactful visuals." /></ListItem>
        <ListItem><ListItemText primary="Interactive libraries: D3.js (advanced) or Plotly (user-friendly) for custom visualizations." /></ListItem>
      </List>
    ),
  },
  {
    id: 'final-thoughts',
    title: 'Final Thoughts',
    icon: <CheckCircleOutlineIcon />,
    content: (
      <Typography variant="body1" paragraph>
        Always test your visualization with a sample audience to ensure clarity and effectiveness. For complex datasets, combine multiple charts or use dashboards for deeper exploration.
      </Typography>
    ),
  }
];

const VisualizationGuidePage: React.FC = () => {
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);

  const scrollToSection = (index: number) => {
    sectionRefs.current[index]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  return (
    <>
      <Helmet>
          <title>Data Visualization Guide - DataStatPro</title>
          <meta name="description" content="Learn how to choose the right chart type for your data story. Guide covers comparisons, trends, distributions, proportions, relationships, optimization tips, and common pitfalls." />
      </Helmet>
      <Box sx={{ p: 3 }}>
        <PageTitle title="Visualization Guide" />
        <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, position: 'sticky', top: '80px' /* Adjust based on header height */ }}>
            <Typography variant="h6" gutterBottom>
              Guide Sections
            </Typography>
            <List component="nav" dense>
              {sections.map((section, index) => (
                <ListItemButton key={section.id} onClick={() => scrollToSection(index)}>
                  {section.icon && <Box sx={{ mr: 1.5, display: 'flex', alignItems: 'center', color: 'primary.main' }}>{React.cloneElement(section.icon, { fontSize: 'small' })}</Box>}
                  <ListItemText primary={section.title.replace(/^\d+\.\s*/, '')} primaryTypographyProps={{ variant: 'body2' }} />
                </ListItemButton>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={9}>
          {sections.map((section, index) => (
            <Paper 
              key={section.id} 
              sx={{ p: 3, mb: 3 }} 
              ref={el => sectionRefs.current[index] = el}
              id={section.id}
            >
              <Typography variant="h5" gutterBottom component="div" sx={{display: 'flex', alignItems: 'center'}}>
                 {section.icon && <Box sx={{ mr: 1, display: 'flex', alignItems: 'center', color: 'primary.main' }}>{section.icon}</Box>}
                {section.title}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {section.content}
            </Paper>
          ))}
        </Grid>
      </Grid>
    </Box>
    </>
  );
};

export default VisualizationGuidePage;
