import React, { useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import { Box, Typography, Paper, List, ListItem, ListItemText, Divider, Grid, ListItemButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';
import PageTitle from '../components/UI/PageTitle';
import { Search as SearchIcon, Category as CategoryIcon, PlaylistAddCheck as PlaylistAddCheckIcon, EditNote as EditNoteIcon, VerifiedUser as VerifiedUserIcon } from '@mui/icons-material';

interface Section {
  id: string;
  title: string;
  icon?: React.ReactElement;
  content: React.ReactNode;
}

const sections: Section[] = [
  {
    id: 'clarify-objective',
    title: '1. Clarify Research Objective',
    icon: <SearchIcon />,
    content: (
      <List dense>
        <ListItem>
          <ListItemText primary="Compare groups: Are you testing differences between groups (e.g., means, medians, or proportions)?" />
        </ListItem>
        <ListItem>
          <ListItemText primary="Analyze relationships: Are you examining correlations or associations between variables?" />
        </ListItem>
        <ListItem>
          <ListItemText primary="Predict outcomes: Are you modeling or forecasting trends?" />
        </ListItem>
      </List>
    ),
  },
  {
    id: 'identify-data-types',
    title: '2. Identify Your Data Types',
    icon: <CategoryIcon />,
    content: (
      <>
        <Typography variant="h6" gutterBottom>
          A. Variable Types
        </Typography>
        <List dense>
          <ListItem>
            <ListItemText 
              primary="Quantitative (Numerical): Continuous data (e.g., height, blood pressure)." 
              secondary="Tests: t-test, ANOVA, Pearson correlation, linear regression." 
            />
          </ListItem>
          <ListItem>
            <ListItemText 
              primary="Categorical:"
              secondary={
                <>
                  Nominal: Unordered categories (e.g., gender, race).
                  <br />
                  Ordinal: Ordered categories (e.g., survey ratings).
                  <br />
                  Tests: Chi-square, Fisher’s exact test, logistic regression.
                </>
              }
            />
          </ListItem>
        </List>
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          B. Data Distribution
        </Typography>
        <List dense>
          <ListItem>
            <ListItemText 
              primary="Normal distribution: Use parametric tests (e.g., t-test, ANOVA)." 
            />
          </ListItem>
          <ListItem>
            <ListItemText 
              primary="Non-normal/small sample: Use non-parametric tests (e.g., Mann-Whitney U, Kruskal-Wallis)." 
            />
          </ListItem>
        </List>
      </>
    ),
  },
  {
    id: 'select-tests',
    title: '3. Select Tests for Scenarios',
    icon: <PlaylistAddCheckIcon />,
    content: (
      // Using a Table for better readability as hinted in original comment
      <TableContainer component={Paper} sx={{ my: 0 }}>
        <Table size="small" aria-label="select tests for common scenarios table">
          <TableHead>
            <TableRow>
              <TableCell>Goal</TableCell>
              <TableCell>Data Type</TableCell>
              <TableCell>Groups</TableCell>
              <TableCell>Recommended Test</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              <TableCell>Compare means</TableCell>
              <TableCell>Quantitative, normal</TableCell>
              <TableCell>2</TableCell>
              <TableCell>Independent t-test</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Compare means</TableCell>
              <TableCell>Quantitative, non-normal</TableCell>
              <TableCell>2</TableCell>
              <TableCell>Mann-Whitney U test</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Compare means</TableCell>
              <TableCell>Quantitative, normal</TableCell>
              <TableCell>≥</TableCell>
              <TableCell>ANOVA</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Compare medians</TableCell>
              <TableCell>Quantitative, non-normal</TableCell>
              <TableCell>≥</TableCell>
              <TableCell>Kruskal-Wallis test</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Compare proportions</TableCell>
              <TableCell>Categorical</TableCell>
              <TableCell>2</TableCell>
              <TableCell>Chi-square or Fisher’s exact test</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Measure correlation</TableCell>
              <TableCell>Quantitative</TableCell>
              <TableCell>2 variables</TableCell>
              <TableCell>Pearson (linear) or Spearman (non-linear)</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Predict a continuous outcome</TableCell>
              <TableCell>Mixed</TableCell>
              <TableCell>Any</TableCell>
              <TableCell>Linear regression</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Predict a binary outcome</TableCell>
              <TableCell>Mixed</TableCell>
              <TableCell>Any</TableCell>
              <TableCell>Logistic regression</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    ),
  },
  {
    id: 'adjust-study-design',
    title: '4. Adjust for Study Design',
    icon: <EditNoteIcon />,
    content: (
      <List dense>
        <ListItem>
          <ListItemText 
            primary="Paired data (e.g., pre/post measurements):"
            secondary={
              <>
                Normal: Paired t-test.
                <br />
                Non-normal: Wilcoxon signed-rank test.
              </>
            }
          />
        </ListItem>
        <ListItem>
          <ListItemText primary="Repeated measures: Repeated-measures ANOVA or Friedman test." />
        </ListItem>
      </List>
    ),
  },
  {
    id: 'verify-assumptions',
    title: '5. Verify Test Assumptions',
    icon: <VerifiedUserIcon />,
    content: (
      <List dense>
        <ListItem>
          <ListItemText primary="Parametric tests: Check normality, equal variance, and independence." />
        </ListItem>
        <ListItem>
          <ListItemText primary="Non-parametric tests: Fewer assumptions but less statistical power." />
        </ListItem>
      </List>
    ),
  },
];

interface WhichTestPageProps {
  onNavigate: (path: string) => void;
}

const WhichTestPage: React.FC<WhichTestPageProps> = ({ onNavigate }) => {
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);

  const scrollToSection = (index: number) => {
    sectionRefs.current[index]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  return (
    <>
      <Helmet>
        <title>Which Test Should I Use? - DataStatPro Guide</title>
        <meta name="description" content="Guide to selecting the appropriate statistical test based on your research question, data types, data distribution, and study design. Covers t-tests, ANOVA, chi-square, and more." />
      </Helmet>
      <Box sx={{ p: 3 }}>
        <PageTitle title="Which Test Should I Use?" />

        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 2, position: 'sticky', top: '80px' /* Adjust based on header height */ }}>
              <Typography variant="h6" gutterBottom>
                Guide Sections
              </Typography>
              <List component="nav" dense>
                {sections.map((section, index) => (
                  <ListItemButton key={section.id} onClick={() => scrollToSection(index)}>
                    {section.icon && <Box sx={{ mr: 1.5, display: 'flex', alignItems: 'center', color: 'primary.main' }}>{React.cloneElement(section.icon, { fontSize: 'small' })}</Box>}
                    <ListItemText primary={section.title.replace(/^\d+\.\s*/, '')} primaryTypographyProps={{ variant: 'body2' }} />
                  </ListItemButton>
                ))}
              </List>
            </Paper>
          </Grid>
          <Grid item xs={12} md={9}>
            {sections.map((section, index) => (
              <Paper
                key={section.id}
                sx={{ p: 3, mb: 3 }}
                ref={el => sectionRefs.current[index] = el}
                id={section.id}
              >
                <Typography variant="h5" gutterBottom component="div" sx={{display: 'flex', alignItems: 'center'}}>
                   {section.icon && <Box sx={{ mr: 1, display: 'flex', alignItems: 'center', color: 'primary.main' }}>{section.icon}</Box>}
                  {section.title}
                </Typography>
                <Divider sx={{ mb: 2 }} />
                {section.content}
              </Paper>
            ))}
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default WhichTestPage;
