// Route guards for authentication and authorization

import { RouteGuard, RouteConfig, NavigationContext, RouteGuardResult } from '../types/routing';
import { adminGuard, adminDashboardGuard } from './AdminRouteGuards';

/**
 * Authentication guard - checks if user is authenticated when required
 */
export const authGuard: RouteGuard = {
  name: 'auth',
  check: (route: RouteConfig, context: NavigationContext): RouteGuardResult => {
    // If route doesn't require auth, allow access
    if (!route.requiresAuth) {
      return { allowed: true };
    }

    // If user is authenticated, allow access
    if (context.isAuthenticated) {
      return { allowed: true };
    }

    // If guest access is allowed and user is a guest, allow access
    if (route.allowGuest && context.isGuest) {
      return { allowed: true };
    }

    // Otherwise, redirect to auth
    return {
      allowed: false,
      redirectTo: 'auth',
      reason: 'Authentication required'
    };
  }
};

/**
 * Guest restriction guard - prevents guests from accessing certain pages
 */
export const guestRestrictionGuard: RouteGuard = {
  name: 'guestRestriction',
  check: (route: RouteConfig, context: NavigationContext): RouteGuardResult => {
    // If user is not a guest, allow access
    if (!context.isGuest) {
      return { allowed: true };
    }

    // Check if route allows guest access
    if (route.allowGuest) {
      return { allowed: true };
    }

    // If route requires auth and user is guest, redirect to dashboard
    if (route.requiresAuth) {
      return {
        allowed: false,
        redirectTo: 'dashboard',
        reason: 'Guest users cannot access this page'
      };
    }

    return { allowed: true };
  }
};

/**
 * Public access guard - handles public pages for non-authenticated users
 */
export const publicAccessGuard: RouteGuard = {
  name: 'publicAccess',
  check: (route: RouteConfig, context: NavigationContext): RouteGuardResult => {
    // If user is authenticated or guest, allow access (other guards will handle specific restrictions)
    if (context.isAuthenticated || context.isGuest) {
      return { allowed: true };
    }

    // For non-authenticated, non-guest users, check if route allows public access
    if (route.allowPublic) {
      return { allowed: true };
    }

    // For non-authenticated users, only allow specific public pages
    const allowedPublicPages = [
      'home',
      'auth',
      'reset-password',
      'whichtest',
      'visualizationguide',
      'statisticalmethods',
      'assistant',
      'knowledge-base',
      'analysis-index', // Allow public access to analysis index
      'dev-training' // Allow development training page access
    ];

    const routePath = route.path.split('/')[0]; // Get the main route path

    if (allowedPublicPages.includes(routePath) || routePath.startsWith('publication-ready')) {
      return { allowed: true };
    }

    // Redirect to auth for other pages when user is not authenticated and not guest
    return {
      allowed: false,
      redirectTo: 'auth',
      reason: 'Authentication required to access this page'
    };
  }
};

/**
 * Redirect guard - handles automatic redirects for authenticated users
 */
export const redirectGuard: RouteGuard = {
  name: 'redirect',
  check: (route: RouteConfig, context: NavigationContext): RouteGuardResult => {
    // If user is authenticated and trying to access auth or home page
    if (context.isAuthenticated && (route.path === 'auth' || route.path === 'home')) {
      // Don't redirect if it's a publication-ready page
      if (!route.path.startsWith('publication-ready')) {
        return {
          allowed: false,
          redirectTo: 'dashboard',
          reason: 'Authenticated users redirected to dashboard'
        };
      }
    }

    return { allowed: true };
  }
};

// Default guards applied to all routes
export const defaultGuards: RouteGuard[] = [
  publicAccessGuard,
  authGuard,
  adminGuard, // Check admin privileges before other guards
  adminDashboardGuard, // Specific guard for admin dashboard
  guestRestrictionGuard,
  redirectGuard
];

/**
 * Apply guards to a route and return the result
 */
export function applyRouteGuards(
  route: RouteConfig,
  context: NavigationContext,
  guards: RouteGuard[] = defaultGuards
): RouteGuardResult {
  for (const guard of guards) {
    const result = guard.check(route, context);
    if (!result.allowed) {
      return result;
    }
  }

  return { allowed: true };
}
