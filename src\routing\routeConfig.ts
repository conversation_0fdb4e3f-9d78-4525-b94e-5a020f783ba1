// Central route configuration and initialization

import { routeRegistry } from './RouteRegistry';

// Re-export routeRegistry for external use
export { routeRegistry };
import { coreRoutes } from './routes/coreRoutes';
import { dataManagementRoutes } from './routes/dataManagementRoutes';
import { statisticsRoutes } from './routes/statisticsRoutes';

// Import additional route modules
import { visualizationRoutes } from './routes/visualizationRoutes';
import { correlationRoutes } from './routes/correlationRoutes';
import { advancedRoutes } from './routes/advancedRoutes';
import { advancedRoutesSimple } from './routes/advancedRoutesSimple';
import { getDevRoutes } from './routes/devRoutes';
import { adminRoutes } from './routes/adminRoutes';

/**
 * Initialize all application routes
 * This function should be called once when the app starts
 */
export function initializeRoutes(): void {
  // Clear existing routes
  routeRegistry.clear();

  // Register all route modules
  console.log('🔍 Registering advanced routes:', advancedRoutes.length, 'routes');
  console.log('🔍 Advanced routes paths:', advancedRoutes.map(r => r.path));

  routeRegistry.register([
    ...coreRoutes,
    ...dataManagementRoutes,
    ...statisticsRoutes,
    ...visualizationRoutes,
    ...correlationRoutes,
    ...advancedRoutes,
    ...advancedRoutesSimple,
    ...adminRoutes, // Include admin routes
    ...getDevRoutes(), // Include development routes only in dev environment
  ]);

  console.log(`Registered ${routeRegistry.getAllRoutes().length} routes`);
}

/**
 * Get routes for navigation menu
 */
export function getNavigationRoutes() {
  return routeRegistry.getAllRoutes()
    .filter(route => !route.metadata?.hidden)
    .sort((a, b) => (a.metadata?.order || 999) - (b.metadata?.order || 999));
}

/**
 * Get routes by category for organized display
 */
export function getRoutesByCategory() {
  const routes = getNavigationRoutes();
  const categories: Record<string, typeof routes> = {};

  routes.forEach(route => {
    const category = route.metadata?.category || 'other';
    if (!categories[category]) {
      categories[category] = [];
    }
    categories[category].push(route);
  });

  return categories;
}

/**
 * Get public routes that don't require authentication
 */
export function getPublicRoutes() {
  return routeRegistry.getPublicRoutes();
}

/**
 * Get routes that require authentication
 */
export function getAuthenticatedRoutes() {
  return routeRegistry.getAuthRoutes();
}

/**
 * Check if a route exists
 */
export function routeExists(page: string, subPage?: string): boolean {
  return routeRegistry.findMatchingRoute(page, subPage) !== undefined;
}

/**
 * Get route metadata for a specific route
 */
export function getRouteMetadata(page: string, subPage?: string) {
  const route = routeRegistry.findMatchingRoute(page, subPage);
  return route?.metadata;
}

/**
 * Get breadcrumb data for current route
 */
export function getBreadcrumbs(page: string, subPage?: string) {
  const breadcrumbs = [];
  
  // Add main page
  const mainRoute = routeRegistry.findMatchingRoute(page);
  if (mainRoute?.metadata) {
    breadcrumbs.push({
      title: mainRoute.metadata.title,
      path: page,
      current: !subPage
    });
  }

  // Add sub page if exists
  if (subPage) {
    const subRoute = routeRegistry.findMatchingRoute(page, subPage);
    if (subRoute?.metadata) {
      breadcrumbs.push({
        title: subRoute.metadata.title,
        path: `${page}/${subPage}`,
        current: true
      });
    }
  }

  return breadcrumbs;
}
