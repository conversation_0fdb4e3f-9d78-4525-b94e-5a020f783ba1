// Admin routes configuration

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

// Lazy load admin components
const AdminDashboardPage = lazy(() => import('../../pages/AdminDashboardPage'));

export const adminRoutes: EnhancedRouteConfig[] = [
  {
    path: 'admin-dashboard',
    component: AdminDashboardPage,
    requiresAuth: true,
    requiresAdmin: true, // New: Requires admin privileges
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'Admin Dashboard',
      description: 'System administration and management console',
      category: 'admin',
      icon: 'Security',
      order: 999, // High order to appear at bottom of navigation
      hidden: false, // Will be conditionally hidden based on admin status
      adminOnly: true // Custom flag to identify admin-only routes
    }
  }
];

// Export admin route paths for easy reference
export const ADMIN_ROUTES = {
  DASHBOARD: 'admin-dashboard'
} as const;

// Utility function to check if a route is admin-only
export const isAdminRoute = (path: string): boolean => {
  return path.startsWith('admin-') || path.includes('admin');
};

// Utility function to get admin routes for navigation
export const getAdminNavigationRoutes = (): EnhancedRouteConfig[] => {
  return adminRoutes.filter(route => !route.metadata?.hidden);
};
