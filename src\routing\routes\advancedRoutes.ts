// Advanced analysis routes configuration

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

// Lazy load components
const PivotAnalysisViewer = lazy(() => import('../../components/PivotAnalysis/PivotAnalysisViewer'));
const SampleSizeCalculatorsOptions = lazy(() => import('../../components/SampleSizeCalculators/SampleSizeCalculatorsOptions'));
const OneSampleCalculator = lazy(() => import('../../components/SampleSizeCalculators/OneSampleCalculator'));
const TwoSampleCalculator = lazy(() => import('../../components/SampleSizeCalculators/TwoSampleCalculator'));
const PairedSampleCalculator = lazy(() => import('../../components/SampleSizeCalculators/PairedSampleCalculator'));
const MoreThanTwoGroupsCalculator = lazy(() => import('../../components/SampleSizeCalculators/MoreThanTwoGroupsCalculator'));
const EpiCalcPage = lazy(() => import('../../pages/EpiCalcPage'));
const EpiCalc = lazy(() => import('../../components/EpiCalc/EpiCalc'));
const AdvancedAnalysisPage = lazy(() => import('../../pages/AdvancedAnalysisPage'));
// Import advanced analysis components via aliases (workaround for spaces in folder names)
const SurvivalAnalysis = lazy(() => import('../../components/AdvancedAnalysisAliases/SurvivalAnalysis'));
const ExploratoryFactorAnalysis = lazy(() => import('../../components/AdvancedAnalysisAliases/ExploratoryFactorAnalysis'));
const ConfirmatoryFactorAnalysis = lazy(() => import('../../components/AdvancedAnalysisAliases/ConfirmatoryFactorAnalysis'));
const MetaAnalysis = lazy(() => import('../../components/AdvancedAnalysisAliases/MetaAnalysis'));
const ReliabilityAnalysis = lazy(() => import('../../components/AdvancedAnalysisAliases/ReliabilityAnalysis'));
const ClusterAnalysis = lazy(() => import('../../components/AdvancedAnalysisAliases/ClusterAnalysis'));
const MediationModeration = lazy(() => import('../../components/AdvancedAnalysisAliases/MediationModeration'));
const VariableTree = lazy(() => import('../../components/AdvancedAnalysisAliases/VariableTree'));
const PublicationReadyPage = lazy(() => import('../../pages/PublicationReadyPage'));
const Table1Generator = lazy(() => import('../../components/PublicationReady/Table1'));
const APAFormatter = lazy(() => import('../../components/PublicationReady/ConvertToAPA'));
const SMDTable = lazy(() => import('../../components/PublicationReady/SMDTable'));
const Table1a = lazy(() => import('../../components/PublicationReady/Table1a'));
const Table1b = lazy(() => import('../../components/PublicationReady/Table1b'));
const FlowDiagram = lazy(() => import('../../components/PublicationReady/FlowDiagram'));
const RegressionTable = lazy(() => import('../../components/PublicationReady/RegressionTable'));
const StatisticalMethodsGenerator = lazy(() => import('../../components/PublicationReady/StatisticalMethodsGenerator'));


const ResultsManagerPage = lazy(() => import('../../pages/ResultsManagerPage'));

export const advancedRoutes: EnhancedRouteConfig[] = [
  // Pivot Analysis
  {
    path: 'pivot',
    component: PivotAnalysisViewer,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: false,
    metadata: {
      title: 'Pivot Analysis',
      description: 'Create pivot tables and cross-tabulations',
      category: 'analysis',
      icon: 'TableChart',
      order: 6
    }
  },

  // Sample Size Calculators
  {
    path: 'samplesize',
    component: SampleSizeCalculatorsOptions,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: false,
    metadata: {
      title: 'Sample Size Calculators',
      description: 'Calculate required sample sizes for studies',
      category: 'tools',
      icon: 'Calculate',
      order: 7
    },
    children: [
      {
        path: 'samplesize/one-sample',
        component: OneSampleCalculator,
        requiresAuth: false,
        allowGuest: true,
        metadata: {
          title: 'One Sample Calculator',
          description: 'Calculate sample size for one sample tests',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/two-sample',
        component: TwoSampleCalculator,
        requiresAuth: false,
        allowGuest: true,
        metadata: {
          title: 'Two Sample Calculator',
          description: 'Calculate sample size for two sample comparisons',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/paired-sample',
        component: PairedSampleCalculator,
        requiresAuth: false,
        allowGuest: true,
        metadata: {
          title: 'Paired Sample Calculator',
          description: 'Calculate sample size for paired sample tests',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/more-than-two-groups',
        component: MoreThanTwoGroupsCalculator,
        requiresAuth: false,
        allowGuest: true,
        metadata: {
          title: 'More Than Two Groups Calculator',
          description: 'Calculate sample size for multiple group comparisons',
          category: 'tools'
        }
      }
    ]
  },

  // Epidemiological Calculators
  {
    path: 'epicalc',
    component: EpiCalcPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: false,
    metadata: {
      title: 'Epidemiological Calculators',
      description: 'Calculate epidemiological measures and statistics',
      category: 'tools',
      icon: 'LocalHospital',
      order: 8
    },
    children: [
      {
        path: 'epicalc/2x2',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        props: { initialTab: '2x2' },
        metadata: {
          title: '2x2 Table Analysis',
          description: 'Analyze 2x2 contingency tables',
          category: 'tools'
        }
      },
      {
        path: 'epicalc/diagnostic',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        props: { initialTab: 'diagnostic' },
        metadata: {
          title: 'Diagnostic Test Evaluation',
          description: 'Evaluate diagnostic test performance',
          category: 'tools'
        }
      },
      {
        path: 'epicalc/screening',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        props: { initialTab: 'screening' },
        metadata: {
          title: 'Screening Test Analysis',
          description: 'Analyze screening test characteristics',
          category: 'tools'
        }
      }
    ]
  },

  // Advanced Analysis
  {
    path: 'advanced-analysis',
    component: AdvancedAnalysisPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to advanced analysis
    metadata: {
      title: 'Advanced Analysis',
      description: 'Advanced statistical analysis methods',
      category: 'analysis',
      icon: 'Psychology',
      order: 9
    },
    children: [
      {
        path: 'advanced-analysis/survival',
        component: SurvivalAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to survival analysis
        metadata: {
          title: 'Survival Analysis',
          description: 'Kaplan-Meier and Cox regression analysis',
          category: 'analysis'
        }
      },
      {
        path: 'advanced-analysis/efa',
        component: ExploratoryFactorAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to EFA
        metadata: {
          title: 'Exploratory Factor Analysis',
          description: 'Exploratory factor analysis',
          category: 'analysis'
        }
      },
      {
        path: 'advanced-analysis/cfa',
        component: ConfirmatoryFactorAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to CFA
        metadata: {
          title: 'Confirmatory Factor Analysis',
          description: 'Confirmatory factor analysis',
          category: 'analysis'
        }
      },
      {
        path: 'advanced-analysis/cluster',
        component: ClusterAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to cluster analysis
        metadata: {
          title: 'Cluster Analysis',
          description: 'Cluster analysis and classification',
          category: 'analysis'
        }
      },
      {
        path: 'advanced-analysis/mediation',
        component: MediationModeration,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to mediation analysis
        metadata: {
          title: 'Mediation/Moderation Analysis',
          description: 'Mediation and moderation analysis',
          category: 'analysis'
        }
      },
      {
        path: 'advanced-analysis/reliability',
        component: ReliabilityAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to reliability analysis
        metadata: {
          title: 'Reliability Analysis',
          description: 'Assess reliability and internal consistency',
          category: 'analysis'
        }
      },
      {
        path: 'advanced-analysis/meta-analysis',
        component: MetaAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to meta-analysis
        metadata: {
          title: 'Meta-Analysis',
          description: 'Combine results from multiple studies',
          category: 'analysis'
        }
      },
      {
        path: 'advanced-analysis/variable-tree',
        component: VariableTree,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to variable tree
        metadata: {
          title: 'Variable Tree Analysis',
          description: 'Create hierarchical tree visualizations of variable relationships',
          category: 'analysis'
        }
      }
    ]
  },

  // Legacy routes for backward compatibility
  {
    path: 'advanced',
    component: SurvivalAnalysis,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: false,
    metadata: {
      title: 'Advanced (Legacy)',
      description: 'Legacy advanced analysis route',
      category: 'analysis',
      hidden: true
    }
  },
  {
    path: 'survival',
    component: SurvivalAnalysis,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: false,
    metadata: {
      title: 'Survival Analysis (Legacy)',
      description: 'Legacy survival analysis route',
      category: 'analysis',
      hidden: true
    }
  },
  {
    path: 'reliability',
    component: ReliabilityAnalysis,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: false,
    metadata: {
      title: 'Reliability Analysis',
      description: 'Assess reliability and internal consistency',
      category: 'analysis',
      icon: 'VerifiedUser'
    }
  },

  // Publication Ready Tools
  {
    path: 'publication-ready',
    component: PublicationReadyPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access for publication tools
    metadata: {
      title: 'Publication Ready',
      description: 'Generate publication-ready tables and results',
      category: 'tools',
      icon: 'Article',
      order: 10
    },
    children: [
      {
        path: 'publication-ready/table1',
        component: Table1Generator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        metadata: {
          title: 'Table 1 Generator',
          description: 'Generate descriptive statistics tables',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/apa',
        component: APAFormatter,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        metadata: {
          title: 'APA Formatter',
          description: 'Format results in APA style',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/smd-table',
        component: SMDTable,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        metadata: {
          title: 'SMD Table',
          description: 'Standardized mean difference tables',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/table1a',
        component: Table1a,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        metadata: {
          title: 'Table 1a',
          description: 'Advanced descriptive statistics table',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/table1b',
        component: Table1b,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        metadata: {
          title: 'Table 1b',
          description: 'Comprehensive descriptive statistics for numerical variables',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/flow-diagram',
        component: FlowDiagram,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        metadata: {
          title: 'Flow Diagram',
          description: 'CONSORT flow diagrams',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/regression-table',
        component: RegressionTable,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        metadata: {
          title: 'Regression Table',
          description: 'Publication-ready regression tables',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/statistical-methods',
        component: StatisticalMethodsGenerator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        metadata: {
          title: 'Statistical Methods Generator',
          description: 'Generate publication-ready methods sections',
          category: 'tools'
        }
      },

      {
        path: 'publication-ready/results-manager',
        component: ResultsManagerPage,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        metadata: {
          title: 'Results Manager',
          description: 'Manage and export analysis results',
          category: 'tools'
        }
      },

    ]
  }
];
