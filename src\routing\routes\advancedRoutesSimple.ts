// Simplified advanced analysis routes configuration (only verified components)

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

// Lazy load only verified components
const PivotAnalysisViewer = lazy(() => import('../../components/PivotAnalysis/PivotAnalysisViewer'));
const SampleSizeCalculatorsOptions = lazy(() => import('../../components/SampleSizeCalculators/SampleSizeCalculatorsOptions'));
const OneSampleCalculator = lazy(() => import('../../components/SampleSizeCalculators/OneSampleCalculator'));
const TwoSampleCalculator = lazy(() => import('../../components/SampleSizeCalculators/TwoSampleCalculator'));
const PairedSampleCalculator = lazy(() => import('../../components/SampleSizeCalculators/PairedSampleCalculator'));
const MoreThanTwoGroupsCalculator = lazy(() => import('../../components/SampleSizeCalculators/MoreThanTwoGroupsCalculator'));
const EpiCalcPage = lazy(() => import('../../pages/EpiCalcPage'));
const EpiCalc = lazy(() => import('../../components/EpiCalc/EpiCalc'));
const CaseControlCalculator = lazy(() => import('../../components/EpiCalc/CaseControlCalculator'));
const CohortCalculator = lazy(() => import('../../components/EpiCalc/CohortCalculator'));
const CrossSectionalCalculator = lazy(() => import('../../components/EpiCalc/CrossSectionalCalculator'));
const MatchedCaseControlCalculator = lazy(() => import('../../components/EpiCalc/MatchedCaseControlCalculator'));
const SampleSizePowerCalculator = lazy(() => import('../../components/EpiCalc/SampleSizePowerCalculator'));
const PublicationReadyPage = lazy(() => import('../../pages/PublicationReadyPage'));
const Table2 = lazy(() => import('../../components/PublicationReady/Table2'));
const PostHocTests = lazy(() => import('../../components/PublicationReady/PostHocTests'));
const ResultsManagerPage = lazy(() => import('../../pages/ResultsManagerPage'));

export const advancedRoutesSimple: EnhancedRouteConfig[] = [
  // Pivot Analysis
  {
    path: 'pivot',
    component: PivotAnalysisViewer,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to pivot analysis
    metadata: {
      title: 'Pivot Analysis',
      description: 'Create pivot tables and cross-tabulations',
      category: 'analysis',
      icon: 'TableChart',
      order: 6
    },
    children: [
      {
        path: 'pivot/tables',
        component: PivotAnalysisViewer,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to pivot tables
        props: { initialTab: 'tables' },
        metadata: {
          title: 'Pivot Tables',
          description: 'Create and customize pivot tables',
          category: 'analysis'
        }
      }
    ]
  },

  // Sample Size Calculators
  {
    path: 'samplesize',
    component: SampleSizeCalculatorsOptions,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to sample size calculators
    metadata: {
      title: 'Sample Size Calculators',
      description: 'Calculate required sample sizes for studies',
      category: 'tools',
      icon: 'Calculate',
      order: 7
    },
    children: [
      {
        path: 'samplesize/options',
        component: SampleSizeCalculatorsOptions,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to options
        metadata: {
          title: 'Sample Size Options',
          description: 'Choose sample size calculation method',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/one-sample',
        component: OneSampleCalculator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to one sample calculator
        metadata: {
          title: 'One Sample Calculator',
          description: 'Calculate sample size for one sample tests',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/two-sample',
        component: TwoSampleCalculator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to two sample calculator
        metadata: {
          title: 'Two Sample Calculator',
          description: 'Calculate sample size for two sample comparisons',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/paired-sample',
        component: PairedSampleCalculator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to paired sample calculator
        metadata: {
          title: 'Paired Sample Calculator',
          description: 'Calculate sample size for paired sample tests',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/more-than-two-groups',
        component: MoreThanTwoGroupsCalculator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to multiple groups calculator
        metadata: {
          title: 'More Than Two Groups Calculator',
          description: 'Calculate sample size for multiple group comparisons',
          category: 'tools'
        }
      }
    ]
  },

  // Epidemiological Calculators
  {
    path: 'epicalc',
    component: EpiCalcPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to epidemiological calculators
    metadata: {
      title: 'Epidemiological Calculators',
      description: 'Calculate epidemiological measures and statistics',
      category: 'tools',
      icon: 'LocalHospital',
      order: 8
    },
    children: [
      {
        path: 'epicalc/case-control',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to case-control studies
        props: { initialTab: 'case_control' },
        metadata: {
          title: 'Case-Control Studies',
          description: 'Calculate measures for case-control studies',
          category: 'tools'
        }
      },
      {
        path: 'epicalc/cohort',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to cohort studies
        props: { initialTab: 'cohort' },
        metadata: {
          title: 'Cohort Studies',
          description: 'Calculate measures for cohort studies',
          category: 'tools'
        }
      },
      {
        path: 'epicalc/cross-sectional',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to cross-sectional studies
        props: { initialTab: 'cross_sectional' },
        metadata: {
          title: 'Cross-Sectional Studies',
          description: 'Calculate measures for cross-sectional studies',
          category: 'tools'
        }
      },
      {
        path: 'epicalc/matched-case-control',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to matched case-control
        props: { initialTab: 'matched_case_control' },
        metadata: {
          title: 'Matched Case-Control',
          description: 'Calculate measures for matched case-control studies',
          category: 'tools'
        }
      },
      {
        path: 'epicalc/sample-size-power',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to sample size and power
        props: { initialTab: 'sample_size_power' },
        metadata: {
          title: 'Sample Size & Power',
          description: 'Calculate sample size and power for epidemiological studies',
          category: 'tools'
        }
      }
    ]
  },

  // Publication Ready Tools
  {
    path: 'publication-ready',
    component: PublicationReadyPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access for publication tools
    metadata: {
      title: 'Publication Ready',
      description: 'Generate publication-ready tables and results',
      category: 'tools',
      icon: 'Article',
      order: 10
    },
    children: [
      {
        path: 'publication-ready/table2',
        component: Table2,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to table2
        metadata: {
          title: 'Table 2 Generator',
          description: 'Generate publication-ready Table 2',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/posthoc-tests',
        component: PostHocTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to posthoc tests
        metadata: {
          title: 'Post-hoc Tests',
          description: 'Generate publication-ready post-hoc test results',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/results-manager',
        component: ResultsManagerPage,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to results manager
        metadata: {
          title: 'Results Manager',
          description: 'Manage and export analysis results',
          category: 'tools'
        }
      }
    ]
  }
];
