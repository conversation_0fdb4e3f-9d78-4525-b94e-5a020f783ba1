// Correlation and regression analysis routes

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

// Lazy load components
const CorrelationAnalysisPage = lazy(() => import('../../pages/CorrelationAnalysisPage'));
const CorrelationAnalysis = lazy(() => import('../../components/CorrelationAnalysis'));

export const correlationRoutes: EnhancedRouteConfig[] = [
  // Legacy correlation route (for backward compatibility)
  {
    path: 'correlation',
    component: CorrelationAnalysis,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to legacy correlation route
    metadata: {
      title: 'Correlation Analysis (Legacy)',
      description: 'Legacy correlation analysis interface',
      category: 'analysis',
      hidden: true // Hide from navigation
    },
    children: [
      {
        path: 'correlation/pearson',
        component: CorrelationAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to legacy Pearson correlation
        props: { initialTab: 'pearson' },
        metadata: {
          title: 'Pearson Correlation (Legacy)',
          description: 'Calculate Pearson correlation coefficients',
          category: 'analysis'
        }
      },
      {
        path: 'correlation/linear',
        component: CorrelationAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to legacy linear regression
        props: { initialTab: 'regression' },
        metadata: {
          title: 'Linear Regression (Legacy)',
          description: 'Perform linear regression analysis',
          category: 'analysis'
        }
      },
      {
        path: 'correlation/logistic',
        component: CorrelationAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to legacy logistic regression
        props: { initialTab: 'logistic' },
        metadata: {
          title: 'Logistic Regression (Legacy)',
          description: 'Perform logistic regression analysis',
          category: 'analysis'
        }
      }
    ]
  },

  // New correlation analysis page
  {
    path: 'correlation-analysis',
    component: CorrelationAnalysisPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to correlation analysis
    metadata: {
      title: 'Correlation Analysis',
      description: 'Analyze relationships between variables',
      category: 'analysis',
      icon: 'ScatterPlot',
      order: 5
    },
    children: [
      {
        path: 'correlation-analysis/pearson',
        component: CorrelationAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to Pearson correlation
        props: { initialTab: 'pearson' },
        metadata: {
          title: 'Pearson Correlation',
          description: 'Calculate Pearson correlation coefficients',
          category: 'analysis'
        }
      },
      {
        path: 'correlation-analysis/spearman',
        component: CorrelationAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to Spearman correlation
        props: { initialTab: 'spearman' },
        metadata: {
          title: 'Spearman Correlation',
          description: 'Calculate Spearman rank correlation',
          category: 'analysis'
        }
      },
      {
        path: 'correlation-analysis/partial',
        component: CorrelationAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to partial correlation
        props: { initialTab: 'partial' },
        metadata: {
          title: 'Partial Correlation',
          description: 'Calculate partial correlation coefficients',
          category: 'analysis'
        }
      },
      {
        path: 'correlation-analysis/matrix',
        component: CorrelationAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to correlation matrix
        props: { initialTab: 'matrix' },
        metadata: {
          title: 'Correlation Matrix',
          description: 'Generate correlation matrices',
          category: 'analysis'
        }
      },
      {
        path: 'correlation-analysis/regression',
        component: CorrelationAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to linear regression
        props: { initialTab: 'regression' },
        metadata: {
          title: 'Linear Regression',
          description: 'Perform linear regression analysis',
          category: 'analysis'
        }
      },
      {
        path: 'correlation-analysis/logistic',
        component: CorrelationAnalysis,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to logistic regression
        props: { initialTab: 'logistic' },
        metadata: {
          title: 'Logistic Regression',
          description: 'Perform logistic regression analysis',
          category: 'analysis'
        }
      }
    ]
  }
];
