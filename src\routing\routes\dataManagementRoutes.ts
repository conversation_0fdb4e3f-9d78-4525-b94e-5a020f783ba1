// Data Management routes configuration

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

// Lazy load components
const DataManagementPage = lazy(() => import('../../pages/DataManagementPage'));
const DataManagement = lazy(() => import('../../components/DataManagement'));

export const dataManagementRoutes: EnhancedRouteConfig[] = [
  {
    path: 'data-management',
    component: DataManagementPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to data management
    props: {},
    metadata: {
      title: 'Data Management',
      description: 'Import, export, and manage your datasets',
      category: 'data',
      icon: 'Storage',
      order: 1
    },
    children: [
      {
        path: 'data-management/import',
        component: DataManagement,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to import
        props: { initialTab: 'import' },
        metadata: {
          title: 'Import Data',
          description: 'Import data from CSV, Excel, or other formats',
          category: 'data',
          order: 1
        }
      },
      {
        path: 'data-management/datasets',
        component: DataManagement,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to datasets
        props: { initialTab: 'datasets' },
        metadata: {
          title: 'Dataset Management',
          description: 'Manage and organize your datasets',
          category: 'data',
          order: 2
        }
      },
      {
        path: 'data-management/export',
        component: DataManagement,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to export
        props: { initialTab: 'export' },
        metadata: {
          title: 'Export Data',
          description: 'Export your data in various formats',
          category: 'data',
          order: 3
        }
      },
      {
        path: 'data-management/editor',
        component: DataManagement,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to editor
        props: { initialTab: 'editor' },
        metadata: {
          title: 'Data Editor',
          description: 'Edit and transform your data',
          category: 'data',
          order: 4
        }
      },
      {
        path: 'data-management/variables',
        component: DataManagement,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to variables
        props: { initialTab: 'variables' },
        metadata: {
          title: 'Variable Editor',
          description: 'Edit variable properties and metadata',
          category: 'data',
          order: 5
        }
      },
      {
        path: 'data-management/transform',
        component: DataManagement,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to transform
        props: { initialTab: 'transform' },
        metadata: {
          title: 'Data Transformation',
          description: 'Transform and recode your variables',
          category: 'data',
          order: 6
        }
      },
      {
        path: 'data-management/sample',
        component: DataManagement,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to sample data
        props: { initialTab: 'sample' },
        metadata: {
          title: 'Sample Data',
          description: 'Generate sample datasets for learning',
          category: 'data',
          order: 7
        }
      }
    ]
  }
];
