// Statistics and analysis routes configuration

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

// Lazy load components
const DescriptiveStatsPage = lazy(() => import('../../pages/DescriptiveStatsPage'));
const DescriptiveStats = lazy(() => import('../../components/DescriptiveStats'));
const InferentialStatsPage = lazy(() => import('../../pages/InferentialStatsPage'));
const InferentialStats = lazy(() => import('../../components/InferentialStats'));
const TTests = lazy(() => import('../../components/InferentialStats/TTests'));
const NonParametricTests = lazy(() => import('../../components/InferentialStats/NonParametricTests'));
const OneWayANOVA = lazy(() => import('../../components/InferentialStats/ANOVA/OneWayANOVA'));
const RepeatedMeasuresANOVA = lazy(() => import('../../components/InferentialStats/ANOVA/RepeatedMeasuresANOVA').then(module => ({ default: module.RepeatedMeasuresANOVA })));
const TwoWayANOVA = lazy(() => import('../../components/InferentialStats/ANOVA/TwoWayANOVA'));
const ANOVA = lazy(() => import('../../components/InferentialStats/ANOVA'));
const TTestWorkflowPage = lazy(() => import('../../pages/TTestWorkflowPage'));
const ANOVAWorkflowPage = lazy(() => import('../../pages/ANOVAWorkflowPage'));

export const statisticsRoutes: EnhancedRouteConfig[] = [
  // Legacy inference route (for backward compatibility)
  {
    path: 'inference',
    component: InferentialStats,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to legacy inference route
    metadata: {
      title: 'Inferential Statistics (Legacy)',
      description: 'Legacy inferential statistics interface',
      category: 'statistics',
      hidden: true // Hide from navigation
    },
    children: [
      {
        path: 'inference/workflow',
        component: TTestWorkflowPage,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to t-test workflow
        metadata: {
          title: 'T-Test Workflow',
          description: 'Guided t-test analysis workflow',
          category: 'statistics'
        }
      },
      {
        path: 'inference/anovaworkflow',
        component: ANOVAWorkflowPage,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to ANOVA workflow
        metadata: {
          title: 'ANOVA Workflow',
          description: 'Guided ANOVA analysis workflow',
          category: 'statistics'
        }
      },
      {
        path: 'inference/ttest',
        component: TTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to legacy t-test route
        props: { initialTab: 'oneSample' },
        metadata: {
          title: 'T-Tests (Legacy)',
          description: 'Legacy t-test interface',
          category: 'statistics'
        }
      },
      {
        path: 'inference/nonparametric',
        component: NonParametricTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to legacy nonparametric route
        props: { initialTab: 'mannWhitney' },
        metadata: {
          title: 'Non-parametric Tests (Legacy)',
          description: 'Legacy non-parametric tests interface',
          category: 'statistics'
        }
      },
      {
        path: 'inference/anova',
        component: ANOVA,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to legacy ANOVA route
        metadata: {
          title: 'ANOVA (Legacy)',
          description: 'Legacy ANOVA interface',
          category: 'statistics'
        }
      },
      {
        path: 'inference/assumptions',
        component: InferentialStats,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to legacy assumptions route
        props: { initialTab: 'assumptions' },
        metadata: {
          title: 'Test Assumptions (Legacy)',
          description: 'Legacy test assumptions interface',
          category: 'statistics'
        }
      }
    ]
  },
  // Descriptive Statistics
  {
    path: 'stats',
    component: DescriptiveStatsPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to statistics
    metadata: {
      title: 'Descriptive Statistics',
      description: 'Explore and summarize your data',
      category: 'statistics',
      icon: 'Assessment',
      order: 2
    },
    children: [
      {
        path: 'stats/descriptives',
        component: DescriptiveStats,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to descriptive stats
        props: { initialTab: 'descriptives' },
        metadata: {
          title: 'Descriptive Analysis',
          description: 'Calculate means, medians, and other descriptive statistics',
          category: 'statistics',
          order: 1
        }
      },
      {
        path: 'stats/frequencies',
        component: DescriptiveStats,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to frequency tables
        props: { initialTab: 'frequencies' },
        metadata: {
          title: 'Frequency Tables',
          description: 'Generate frequency tables and distributions',
          category: 'statistics',
          order: 2
        }
      },
      {
        path: 'stats/crosstabs',
        component: DescriptiveStats,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to cross-tabulations
        props: { initialTab: 'crosstabs' },
        metadata: {
          title: 'Cross-Tabulations',
          description: 'Create cross-tabulation tables and chi-square tests',
          category: 'statistics',
          order: 3
        }
      },
      {
        path: 'stats/normality',
        component: DescriptiveStats,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to normality tests
        props: { initialTab: 'normality' },
        metadata: {
          title: 'Normality Tests',
          description: 'Test data for normal distribution',
          category: 'statistics',
          order: 4
        }
      },
      // Legacy routes for backward compatibility
      {
        path: 'stats/frequency',
        component: DescriptiveStats,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        props: { initialTab: 'frequencies' },
        metadata: {
          title: 'Frequency Tables (Legacy)',
          description: 'Generate frequency tables and cross-tabulations',
          category: 'statistics'
        }
      },
      {
        path: 'stats/descriptive',
        component: DescriptiveStats,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        props: { initialTab: 'descriptives' },
        metadata: {
          title: 'Descriptive Measures (Legacy)',
          description: 'Calculate means, medians, and other descriptive statistics',
          category: 'statistics'
        }
      }
    ]
  },

  // Inferential Statistics
  {
    path: 'inferential-stats',
    component: InferentialStatsPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to inferential statistics
    metadata: {
      title: 'Inferential Statistics',
      description: 'Perform statistical tests and hypothesis testing',
      category: 'statistics',
      icon: 'TrendingUp',
      order: 3
    },
    children: [
      // T-Tests
      {
        path: 'inferential-stats/one-sample-ttest',
        component: TTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to one-sample t-test
        props: { initialTab: 'oneSample' },
        metadata: {
          title: 'One-Sample t-test',
          description: 'Test if a sample mean differs from a population mean',
          category: 'statistics'
        }
      },
      {
        path: 'inferential-stats/independent-samples-ttest',
        component: TTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to independent samples t-test
        props: { initialTab: 'independent' },
        metadata: {
          title: 'Independent Samples t-test',
          description: 'Compare means between two independent groups',
          category: 'statistics'
        }
      },
      {
        path: 'inferential-stats/paired-samples-ttest',
        component: TTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to paired samples t-test
        props: { initialTab: 'paired' },
        metadata: {
          title: 'Paired Samples t-test',
          description: 'Compare means for paired observations',
          category: 'statistics'
        }
      },

      // Non-parametric Tests
      {
        path: 'inferential-stats/mann-whitney-u-test',
        component: NonParametricTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to Mann-Whitney U test
        props: { initialTab: 'mannWhitney' },
        metadata: {
          title: 'Mann-Whitney U Test',
          description: 'Non-parametric test for comparing two independent groups',
          category: 'statistics'
        }
      },
      {
        path: 'inferential-stats/wilcoxon-signed-rank-test',
        component: NonParametricTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to Wilcoxon signed-rank test
        props: { initialTab: 'wilcoxon' },
        metadata: {
          title: 'Wilcoxon Signed-Rank Test',
          description: 'Non-parametric test for paired samples',
          category: 'statistics'
        }
      },
      {
        path: 'inferential-stats/kruskal-wallis-test',
        component: NonParametricTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to Kruskal-Wallis test
        props: { initialTab: 'kruskalWallis' },
        metadata: {
          title: 'Kruskal-Wallis Test',
          description: 'Non-parametric alternative to one-way ANOVA',
          category: 'statistics'
        }
      },
      {
        path: 'inferential-stats/friedman-test',
        component: NonParametricTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to Friedman test
        props: { initialTab: 'friedman' },
        metadata: {
          title: 'Friedman Test',
          description: 'Non-parametric test for repeated measures',
          category: 'statistics'
        }
      },
      {
        path: 'inferential-stats/chi-square-test',
        component: NonParametricTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to chi-square test
        props: { initialTab: 'chiSquare' },
        metadata: {
          title: 'Chi-Square Test',
          description: 'Test for independence and goodness of fit',
          category: 'statistics'
        }
      },

      // ANOVA Tests
      {
        path: 'inferential-stats/one-way-anova',
        component: OneWayANOVA,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to one-way ANOVA
        metadata: {
          title: 'One-Way ANOVA',
          description: 'Compare means across multiple groups',
          category: 'statistics'
        }
      },
      {
        path: 'inferential-stats/repeated-measures-anova',
        component: RepeatedMeasuresANOVA,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to repeated measures ANOVA
        metadata: {
          title: 'Repeated Measures ANOVA',
          description: 'ANOVA for repeated measurements',
          category: 'statistics'
        }
      },
      {
        path: 'inferential-stats/two-way-anova',
        component: TwoWayANOVA,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to two-way ANOVA
        metadata: {
          title: 'Two-Way ANOVA',
          description: 'ANOVA with two factors',
          category: 'statistics'
        }
      }
    ]
  }
];
