// Visualization and charting routes configuration

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

// Lazy load components
const DataVisualizationPage = lazy(() => import('../../pages/DataVisualizationPage'));
const Visualization = lazy(() => import('../../components/Visualization'));

export const visualizationRoutes: EnhancedRouteConfig[] = [
  {
    path: 'charts',
    component: DataVisualizationPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to visualization
    metadata: {
      title: 'Data Visualization',
      description: 'Create charts and graphs from your data',
      category: 'visualization',
      icon: 'BarChart',
      order: 4
    },
    children: [
      {
        path: 'charts/bar',
        component: Visualization,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to bar charts
        props: { initialTab: 'bar' },
        metadata: {
          title: 'Bar Charts',
          description: 'Create bar charts and column charts',
          category: 'visualization'
        }
      },
      {
        path: 'charts/pie',
        component: Visualization,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to pie charts
        props: { initialTab: 'pie' },
        metadata: {
          title: 'Pie Charts',
          description: 'Create pie charts and donut charts',
          category: 'visualization'
        }
      },
      {
        path: 'charts/histogram',
        component: Visualization,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to histograms
        props: { initialTab: 'histogram' },
        metadata: {
          title: 'Histograms',
          description: 'Create histograms for distribution analysis',
          category: 'visualization'
        }
      },
      {
        path: 'charts/boxplot',
        component: Visualization,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to box plots
        props: { initialTab: 'boxplot' },
        metadata: {
          title: 'Box Plots',
          description: 'Create box plots for distribution comparison',
          category: 'visualization'
        }
      },
      {
        path: 'charts/scatter',
        component: Visualization,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to scatter plots
        props: { initialTab: 'scatter' },
        metadata: {
          title: 'Scatter Plots',
          description: 'Create scatter plots for correlation analysis',
          category: 'visualization'
        }
      },
      {
        path: 'charts/line',
        component: Visualization,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to line charts
        props: { initialTab: 'line' },
        metadata: {
          title: 'Line Charts',
          description: 'Create line charts for time series data',
          category: 'visualization'
        }
      },
      {
        path: 'charts/raincloud',
        component: Visualization,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to rain cloud plots
        props: { initialTab: 'raincloud' },
        metadata: {
          title: 'Rain Cloud Plots',
          description: 'Combine box plot, scatter plot, and density visualization',
          category: 'visualization'
        }
      },
      {
        path: 'charts/sankey',
        component: Visualization,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to Sankey diagrams
        props: { initialTab: 'sankey' },
        metadata: {
          title: 'Sankey Diagrams',
          description: 'Visualize flow relationships between categorical variables',
          category: 'visualization'
        }
      },
      {
        path: 'charts/errorbar',
        component: Visualization,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to error bar charts
        props: { initialTab: 'errorbar' },
        metadata: {
          title: 'Error Bar Charts',
          description: 'Display means with error bars showing variability or uncertainty',
          category: 'visualization'
        }
      }
    ]
  }
];
