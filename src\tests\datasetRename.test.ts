/**
 * Test file for dataset rename functionality
 * This file contains tests to verify that dataset renaming works correctly
 * for both local and cloud datasets with proper access control.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renameDataset } from '../utils/services/datasetService';

// Mock Supabase
vi.mock('../utils/supabaseClient', () => ({
  supabase: {
    auth: {
      getUser: vi.fn(() => ({
        data: {
          user: {
            id: 'test-user-id',
            email: '<EMAIL>'
          }
        }
      }))
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          neq: vi.fn(() => ({
            single: vi.fn(() => ({
              data: null,
              error: { code: 'PGRST116' } // No rows found
            }))
          }))
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => ({
              data: {
                id: 'test-dataset-id',
                dataset_name: 'New Dataset Name',
                updated_at: new Date().toISOString()
              },
              error: null
            }))
          }))
        }))
      }))
    }))
  }
}));

describe('Dataset Rename Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('renameDataset service function', () => {
    it('should successfully rename a dataset', async () => {
      const result = await renameDataset('test-dataset-id', 'New Dataset Name');
      
      expect(result.error).toBeNull();
      expect(result.data).toBeDefined();
      expect(result.data?.dataset_name).toBe('New Dataset Name');
    });

    it('should reject empty dataset names', async () => {
      const result = await renameDataset('test-dataset-id', '');
      
      expect(result.error).toBeDefined();
      expect(result.error.message).toBe('Dataset name cannot be empty.');
      expect(result.data).toBeNull();
    });

    it('should reject whitespace-only dataset names', async () => {
      const result = await renameDataset('test-dataset-id', '   ');
      
      expect(result.error).toBeDefined();
      expect(result.error.message).toBe('Dataset name cannot be empty.');
      expect(result.data).toBeNull();
    });
  });

  describe('Access Control', () => {
    it('should allow Standard users to rename their imported datasets', () => {
      // This would be tested in integration tests with the DataContext
      // The logic is: canImportData && !dataset.userId (local dataset)
      const canImportData = true;
      const isLocalDataset = true; // !dataset.userId
      
      expect(canImportData && isLocalDataset).toBe(true);
    });

    it('should allow Pro users to rename their cloud datasets', () => {
      // This would be tested in integration tests with the DataContext
      // The logic is: dataset.userId === user.id (cloud dataset owned by user)
      const datasetUserId = 'test-user-id';
      const currentUserId = 'test-user-id';
      
      expect(datasetUserId === currentUserId).toBe(true);
    });

    it('should prevent Guest users from renaming sample datasets', () => {
      // This would be tested in integration tests with the DataContext
      // The logic is: !canImportData (Guest user)
      const canImportData = false;
      const isLocalDataset = true; // Sample datasets are local
      
      expect(canImportData || !isLocalDataset).toBe(false);
    });
  });
});

/**
 * Integration Test Scenarios (to be tested manually):
 * 
 * 1. Guest User:
 *    - Load sample dataset
 *    - Try to rename -> Should show error message
 *    - Rename option should not appear in menu or be disabled
 * 
 * 2. Standard User:
 *    - Import a dataset
 *    - Rename the imported dataset -> Should work
 *    - Try to rename sample dataset -> Should show error
 * 
 * 3. Pro User:
 *    - Import and save dataset to cloud
 *    - Rename cloud dataset -> Should work
 *    - Rename local dataset -> Should work
 * 
 * 4. Edge Cases:
 *    - Try to rename to existing dataset name -> Should show error
 *    - Try to rename with empty name -> Should show error
 *    - Try to rename while another operation is in progress -> Should be disabled
 *    - Rename current dataset -> Should update current dataset reference
 */
