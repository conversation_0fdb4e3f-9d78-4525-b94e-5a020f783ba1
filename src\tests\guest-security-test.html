<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guest Security Test - DataStatPro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-step {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #2196F3;
            background-color: #f8f9fa;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1976D2;
        }
        .code {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔒 Guest Security Vulnerability Test</h1>
    <p>This test verifies that the security vulnerability where Standard users could import datasets then switch to Guest login to bypass Pro feature restrictions has been fixed.</p>

    <div class="test-container">
        <h2>Test Scenario</h2>
        <p>This test simulates the vulnerability scenario:</p>
        <ol>
            <li>Simulate a Standard user importing datasets (stored in localStorage)</li>
            <li>Simulate switching to Guest login</li>
            <li>Verify that Guest users cannot access the previously stored datasets</li>
        </ol>
    </div>

    <div class="test-container">
        <h2>Test Steps</h2>
        
        <div class="test-step">
            <h3>Step 1: Setup - Clear existing data</h3>
            <button onclick="clearAllData()">Clear All Data</button>
            <div id="step1-result"></div>
        </div>

        <div class="test-step">
            <h3>Step 2: Simulate Standard User with Datasets</h3>
            <button onclick="simulateStandardUserWithDatasets()">Create Standard User Datasets</button>
            <div id="step2-result"></div>
        </div>

        <div class="test-step">
            <h3>Step 3: Verify Datasets Exist</h3>
            <button onclick="verifyDatasetsExist()">Check Datasets</button>
            <div id="step3-result"></div>
        </div>

        <div class="test-step">
            <h3>Step 4: Simulate Guest Login (Security Fix)</h3>
            <button onclick="simulateGuestLogin()">Switch to Guest Login</button>
            <div id="step4-result"></div>
        </div>

        <div class="test-step">
            <h3>Step 5: Verify Security Fix</h3>
            <button onclick="verifySecurityFix()">Verify Datasets Cleared</button>
            <div id="step5-result"></div>
        </div>

        <div class="test-step">
            <h3>Step 6: Run Complete Test</h3>
            <button onclick="runCompleteTest()">Run All Tests</button>
            <div id="complete-test-result"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>Current Storage State</h2>
        <button onclick="showCurrentState()">Show Current State</button>
        <div id="current-state"></div>
    </div>

    <script>
        // Simulate the clearDatasetStorage function from AuthContext
        function clearDatasetStorage() {
            try {
                // Clear main dataset storage (both new and legacy keys)
                localStorage.removeItem('datastatpro_datasets');
                localStorage.removeItem('statistica_datasets');

                // Clear results and projects storage (both new and legacy keys)
                localStorage.removeItem('datastatpro_results');
                localStorage.removeItem('statistica_results');
                localStorage.removeItem('datastatpro_projects');
                localStorage.removeItem('statistica_projects');
                
                // Clear analysis configuration and results storage
                const analysisKeys = [
                    'descriptive_analysis_config',
                    'descriptive_analysis_results',
                    'cross_tabulation_config',
                    'cross_tabulation_results',
                    'linear_regression_results',
                    'posthoc_test_results',
                    'mediation_results',
                    'moderation_results',
                    'cohort_calculator_cell_values',
                    'cohort_calculator_strata',
                    'cohort_calculator_results',
                    'cohort_calculator_current_stratum_index',
                    'analysisAssistantTrainingData'
                ];
                
                // Clear t-test results for all test types
                const ttestTypes = ['one_sample', 'independent', 'paired'];
                ttestTypes.forEach(type => {
                    analysisKeys.push(`ttest_results_${type}`);
                    analysisKeys.push(`ttest_assumptions_${type}`);
                });
                
                // Clear guided workflow progress data
                Object.keys(localStorage).forEach(key => {
                    if (key.endsWith('-progress')) {
                        analysisKeys.push(key);
                    }
                });
                
                // Remove all analysis-related keys
                analysisKeys.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                console.log('🧹 Cleared all dataset-related storage for Guest login');
                return true;
            } catch (error) {
                console.error('Error clearing dataset storage:', error);
                return false;
            }
        }

        function clearAllData() {
            localStorage.clear();
            sessionStorage.clear();
            document.getElementById('step1-result').innerHTML = 
                '<div class="test-result success">✅ All data cleared successfully</div>';
        }

        function simulateStandardUserWithDatasets() {
            // Simulate datasets that a Standard user might have
            const mockDatasets = [
                {
                    id: 'dataset-1',
                    name: 'My Research Data',
                    description: 'Important research dataset',
                    data: [
                        { id: 1, name: 'John', age: 25, score: 85 },
                        { id: 2, name: 'Jane', age: 30, score: 92 }
                    ],
                    columns: [
                        { id: 'id', name: 'ID', type: 'NUMERIC' },
                        { id: 'name', name: 'Name', type: 'CATEGORICAL' },
                        { id: 'age', name: 'Age', type: 'NUMERIC' },
                        { id: 'score', name: 'Score', type: 'NUMERIC' }
                    ],
                    dateCreated: new Date().toISOString(),
                    dateModified: new Date().toISOString()
                },
                {
                    id: 'dataset-2',
                    name: 'Survey Results',
                    description: 'Customer satisfaction survey',
                    data: [
                        { id: 1, satisfaction: 'High', rating: 4.5 },
                        { id: 2, satisfaction: 'Medium', rating: 3.2 }
                    ],
                    columns: [
                        { id: 'id', name: 'ID', type: 'NUMERIC' },
                        { id: 'satisfaction', name: 'Satisfaction', type: 'CATEGORICAL' },
                        { id: 'rating', name: 'Rating', type: 'NUMERIC' }
                    ],
                    dateCreated: new Date().toISOString(),
                    dateModified: new Date().toISOString()
                }
            ];

            // Store datasets and other data (using new keys)
            localStorage.setItem('datastatpro_datasets', JSON.stringify(mockDatasets));
            localStorage.setItem('datastatpro_results', JSON.stringify([
                { id: 'result-1', title: 'Analysis Result 1', content: 'Some analysis' }
            ]));
            localStorage.setItem('descriptive_analysis_config', JSON.stringify({
                includeShape: true,
                includeOutliers: false
            }));
            localStorage.setItem('analysisAssistantTrainingData', JSON.stringify({
                questions: [],
                answers: []
            }));

            // Set session storage to simulate non-guest user
            sessionStorage.removeItem('isGuest');

            document.getElementById('step2-result').innerHTML = 
                '<div class="test-result success">✅ Created mock Standard user datasets and analysis data</div>';
        }

        function verifyDatasetsExist() {
            const datasets = localStorage.getItem('datastatpro_datasets');
            const results = localStorage.getItem('datastatpro_results');
            const config = localStorage.getItem('descriptive_analysis_config');
            
            if (datasets && results && config) {
                const datasetCount = JSON.parse(datasets).length;
                document.getElementById('step3-result').innerHTML = 
                    `<div class="test-result success">✅ Verified: ${datasetCount} datasets and analysis data exist in localStorage</div>`;
            } else {
                document.getElementById('step3-result').innerHTML = 
                    '<div class="test-result error">❌ No datasets found in localStorage</div>';
            }
        }

        function simulateGuestLogin() {
            // Simulate the guest login process with security fix
            sessionStorage.setItem('isGuest', 'true');
            
            // Apply the security fix - clear all dataset-related storage
            const cleared = clearDatasetStorage();
            
            if (cleared) {
                document.getElementById('step4-result').innerHTML = 
                    '<div class="test-result success">✅ Guest login simulated - security fix applied (dataset storage cleared)</div>';
            } else {
                document.getElementById('step4-result').innerHTML = 
                    '<div class="test-result error">❌ Failed to apply security fix</div>';
            }
        }

        function verifySecurityFix() {
            const datasets = localStorage.getItem('datastatpro_datasets');
            const results = localStorage.getItem('datastatpro_results');
            const config = localStorage.getItem('descriptive_analysis_config');
            const trainingData = localStorage.getItem('analysisAssistantTrainingData');
            const isGuest = sessionStorage.getItem('isGuest');
            
            let securityTestPassed = true;
            let messages = [];
            
            if (isGuest !== 'true') {
                securityTestPassed = false;
                messages.push('❌ Guest status not set correctly');
            } else {
                messages.push('✅ Guest status set correctly');
            }
            
            if (datasets) {
                securityTestPassed = false;
                messages.push('❌ SECURITY VULNERABILITY: Datasets still accessible to Guest user');
            } else {
                messages.push('✅ Datasets properly cleared');
            }
            
            if (results) {
                securityTestPassed = false;
                messages.push('❌ SECURITY VULNERABILITY: Results still accessible to Guest user');
            } else {
                messages.push('✅ Results properly cleared');
            }
            
            if (config) {
                securityTestPassed = false;
                messages.push('❌ SECURITY VULNERABILITY: Analysis config still accessible to Guest user');
            } else {
                messages.push('✅ Analysis config properly cleared');
            }
            
            if (trainingData) {
                securityTestPassed = false;
                messages.push('❌ SECURITY VULNERABILITY: Training data still accessible to Guest user');
            } else {
                messages.push('✅ Training data properly cleared');
            }
            
            const resultClass = securityTestPassed ? 'success' : 'error';
            const resultIcon = securityTestPassed ? '✅' : '❌';
            const resultText = securityTestPassed ? 'SECURITY FIX VERIFIED' : 'SECURITY VULNERABILITY DETECTED';
            
            document.getElementById('step5-result').innerHTML = 
                `<div class="test-result ${resultClass}">
                    <strong>${resultIcon} ${resultText}</strong><br>
                    ${messages.join('<br>')}
                </div>`;
        }

        function runCompleteTest() {
            document.getElementById('complete-test-result').innerHTML = 
                '<div class="test-result warning">🔄 Running complete test...</div>';
            
            setTimeout(() => {
                clearAllData();
                setTimeout(() => {
                    simulateStandardUserWithDatasets();
                    setTimeout(() => {
                        verifyDatasetsExist();
                        setTimeout(() => {
                            simulateGuestLogin();
                            setTimeout(() => {
                                verifySecurityFix();
                                document.getElementById('complete-test-result').innerHTML = 
                                    '<div class="test-result success">✅ Complete test finished - check individual step results above</div>';
                            }, 100);
                        }, 100);
                    }, 100);
                }, 100);
            }, 100);
        }

        function showCurrentState() {
            const allKeys = Object.keys(localStorage);
            const datasetKeys = allKeys.filter(key => 
                key.includes('dataset') || 
                key.includes('statistica') || 
                key.includes('analysis') || 
                key.includes('ttest') || 
                key.includes('regression') || 
                key.includes('cohort')
            );
            
            const isGuest = sessionStorage.getItem('isGuest');
            
            let html = `<div class="code">
                <strong>Session Storage:</strong><br>
                isGuest: ${isGuest || 'null'}<br><br>
                <strong>Dataset-related localStorage keys (${datasetKeys.length}):</strong><br>`;
            
            if (datasetKeys.length === 0) {
                html += 'None (✅ Clean state)';
            } else {
                datasetKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    const preview = value ? (value.length > 100 ? value.substring(0, 100) + '...' : value) : 'null';
                    html += `${key}: ${preview}<br>`;
                });
            }
            
            html += '</div>';
            document.getElementById('current-state').innerHTML = html;
        }

        // Show initial state
        showCurrentState();
    </script>
</body>
</html>
