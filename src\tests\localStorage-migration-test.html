<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>localStorage Migration Test - DataStatPro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-step {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #2196F3;
            background-color: #f8f9fa;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1976D2;
        }
        .code {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>📦 localStorage Migration Test</h1>
    <p>This test verifies that the migration from legacy 'statistica_*' keys to new 'datastatpro_*' keys works correctly.</p>

    <div class="test-container">
        <h2>Test Scenario</h2>
        <p>This test simulates the migration process:</p>
        <ol>
            <li>Create legacy localStorage data with 'statistica_*' keys</li>
            <li>Simulate the migration process</li>
            <li>Verify data is moved to new 'datastatpro_*' keys</li>
            <li>Verify legacy keys are removed</li>
        </ol>
    </div>

    <div class="test-container">
        <h2>Test Steps</h2>
        
        <div class="test-step">
            <h3>Step 1: Clear all storage</h3>
            <button onclick="clearAllStorage()">Clear All Storage</button>
            <div id="step1-result"></div>
        </div>

        <div class="test-step">
            <h3>Step 2: Create legacy data</h3>
            <button onclick="createLegacyData()">Create Legacy Data</button>
            <div id="step2-result"></div>
        </div>

        <div class="test-step">
            <h3>Step 3: Verify legacy data exists</h3>
            <button onclick="verifyLegacyData()">Verify Legacy Data</button>
            <div id="step3-result"></div>
        </div>

        <div class="test-step">
            <h3>Step 4: Run migration</h3>
            <button onclick="runMigration()">Run Migration</button>
            <div id="step4-result"></div>
        </div>

        <div class="test-step">
            <h3>Step 5: Verify migration</h3>
            <button onclick="verifyMigration()">Verify Migration</button>
            <div id="step5-result"></div>
        </div>

        <div class="test-step">
            <h3>Step 6: Run complete test</h3>
            <button onclick="runCompleteTest()">Run All Tests</button>
            <div id="complete-test-result"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>Current Storage State</h2>
        <button onclick="showCurrentState()">Show Current State</button>
        <div id="current-state"></div>
    </div>

    <script>
        // Migration functions (copied from the actual implementation)
        const STORAGE_KEY = 'datastatpro_datasets';
        const LEGACY_STORAGE_KEY = 'statistica_datasets';
        const RESULTS_STORAGE_KEY = 'datastatpro_results';
        const PROJECTS_STORAGE_KEY = 'datastatpro_projects';
        const LEGACY_RESULTS_KEY = 'statistica_results';
        const LEGACY_PROJECTS_KEY = 'statistica_projects';

        function migrateDatasetStorage() {
            // First check for new key
            const newData = localStorage.getItem(STORAGE_KEY);
            if (newData) {
                try {
                    const parsedData = JSON.parse(newData);
                    return parsedData.map((dataset) => ({
                        ...dataset,
                        dateCreated: new Date(dataset.dateCreated),
                        dateModified: new Date(dataset.dateModified),
                        userId: dataset.userId || null
                    }));
                } catch (e) {
                    console.error('Error parsing new datasets:', e);
                }
            }

            // Check for legacy key and migrate
            const legacyData = localStorage.getItem(LEGACY_STORAGE_KEY);
            if (legacyData) {
                try {
                    const parsedData = JSON.parse(legacyData);
                    const migratedData = parsedData.map((dataset) => ({
                        ...dataset,
                        dateCreated: new Date(dataset.dateCreated),
                        dateModified: new Date(dataset.dateModified),
                        userId: dataset.userId || null
                    }));
                    
                    // Save to new key and remove legacy key
                    localStorage.setItem(STORAGE_KEY, JSON.stringify(migratedData));
                    localStorage.removeItem(LEGACY_STORAGE_KEY);
                    console.log('📦 Migrated datasets from legacy storage key');
                    
                    return migratedData;
                } catch (e) {
                    console.error('Error parsing legacy datasets:', e);
                    localStorage.removeItem(LEGACY_STORAGE_KEY);
                }
            }

            return [];
        }

        function migrateResultsStorage() {
            const newData = localStorage.getItem(RESULTS_STORAGE_KEY);
            if (newData) {
                try {
                    return JSON.parse(newData);
                } catch (e) {
                    console.error('Error parsing new results:', e);
                }
            }

            const legacyData = localStorage.getItem(LEGACY_RESULTS_KEY);
            if (legacyData) {
                try {
                    const parsedData = JSON.parse(legacyData);
                    localStorage.setItem(RESULTS_STORAGE_KEY, legacyData);
                    localStorage.removeItem(LEGACY_RESULTS_KEY);
                    console.log('📦 Migrated results from legacy storage key');
                    return parsedData;
                } catch (e) {
                    console.error('Error parsing legacy results:', e);
                    localStorage.removeItem(LEGACY_RESULTS_KEY);
                }
            }

            return [];
        }

        function migrateProjectsStorage() {
            const newData = localStorage.getItem(PROJECTS_STORAGE_KEY);
            if (newData) {
                try {
                    return JSON.parse(newData);
                } catch (e) {
                    console.error('Error parsing new projects:', e);
                }
            }

            const legacyData = localStorage.getItem(LEGACY_PROJECTS_KEY);
            if (legacyData) {
                try {
                    const parsedData = JSON.parse(legacyData);
                    localStorage.setItem(PROJECTS_STORAGE_KEY, legacyData);
                    localStorage.removeItem(LEGACY_PROJECTS_KEY);
                    console.log('📦 Migrated projects from legacy storage key');
                    return parsedData;
                } catch (e) {
                    console.error('Error parsing legacy projects:', e);
                    localStorage.removeItem(LEGACY_PROJECTS_KEY);
                }
            }

            return [{ id: 'default', name: 'Default Project', isLocal: true, resultCount: 0, lastModified: new Date() }];
        }

        // Test functions
        function clearAllStorage() {
            localStorage.clear();
            document.getElementById('step1-result').innerHTML = 
                '<div class="test-result success">✅ All storage cleared</div>';
        }

        function createLegacyData() {
            // Create mock legacy data
            const legacyDatasets = [
                {
                    id: 'dataset-1',
                    name: 'Legacy Dataset 1',
                    description: 'Test dataset from old system',
                    data: [{ id: 1, value: 'test' }],
                    columns: [{ id: 'id', name: 'ID', type: 'NUMERIC' }],
                    dateCreated: new Date().toISOString(),
                    dateModified: new Date().toISOString()
                }
            ];

            const legacyResults = [
                { id: 'result-1', title: 'Legacy Result', type: 'descriptive', timestamp: Date.now() }
            ];

            const legacyProjects = [
                { id: 'project-1', name: 'Legacy Project', isLocal: true, resultCount: 1, lastModified: new Date() }
            ];

            localStorage.setItem(LEGACY_STORAGE_KEY, JSON.stringify(legacyDatasets));
            localStorage.setItem(LEGACY_RESULTS_KEY, JSON.stringify(legacyResults));
            localStorage.setItem(LEGACY_PROJECTS_KEY, JSON.stringify(legacyProjects));

            document.getElementById('step2-result').innerHTML = 
                '<div class="test-result success">✅ Legacy data created</div>';
        }

        function verifyLegacyData() {
            const datasets = localStorage.getItem(LEGACY_STORAGE_KEY);
            const results = localStorage.getItem(LEGACY_RESULTS_KEY);
            const projects = localStorage.getItem(LEGACY_PROJECTS_KEY);

            if (datasets && results && projects) {
                document.getElementById('step3-result').innerHTML = 
                    '<div class="test-result success">✅ Legacy data verified - all keys exist</div>';
            } else {
                document.getElementById('step3-result').innerHTML = 
                    '<div class="test-result error">❌ Legacy data missing</div>';
            }
        }

        function runMigration() {
            try {
                const migratedDatasets = migrateDatasetStorage();
                const migratedResults = migrateResultsStorage();
                const migratedProjects = migrateProjectsStorage();

                document.getElementById('step4-result').innerHTML = 
                    `<div class="test-result success">✅ Migration completed<br>
                    - Datasets: ${migratedDatasets.length}<br>
                    - Results: ${migratedResults.length}<br>
                    - Projects: ${migratedProjects.length}</div>`;
            } catch (error) {
                document.getElementById('step4-result').innerHTML = 
                    `<div class="test-result error">❌ Migration failed: ${error.message}</div>`;
            }
        }

        function verifyMigration() {
            const newDatasets = localStorage.getItem(STORAGE_KEY);
            const newResults = localStorage.getItem(RESULTS_STORAGE_KEY);
            const newProjects = localStorage.getItem(PROJECTS_STORAGE_KEY);

            const legacyDatasets = localStorage.getItem(LEGACY_STORAGE_KEY);
            const legacyResults = localStorage.getItem(LEGACY_RESULTS_KEY);
            const legacyProjects = localStorage.getItem(LEGACY_PROJECTS_KEY);

            let success = true;
            let messages = [];

            if (!newDatasets) {
                success = false;
                messages.push('❌ New datasets key missing');
            } else {
                messages.push('✅ New datasets key exists');
            }

            if (!newResults) {
                success = false;
                messages.push('❌ New results key missing');
            } else {
                messages.push('✅ New results key exists');
            }

            if (!newProjects) {
                success = false;
                messages.push('❌ New projects key missing');
            } else {
                messages.push('✅ New projects key exists');
            }

            if (legacyDatasets) {
                success = false;
                messages.push('❌ Legacy datasets key still exists');
            } else {
                messages.push('✅ Legacy datasets key removed');
            }

            if (legacyResults) {
                success = false;
                messages.push('❌ Legacy results key still exists');
            } else {
                messages.push('✅ Legacy results key removed');
            }

            if (legacyProjects) {
                success = false;
                messages.push('❌ Legacy projects key still exists');
            } else {
                messages.push('✅ Legacy projects key removed');
            }

            const resultClass = success ? 'success' : 'error';
            const resultIcon = success ? '✅' : '❌';
            const resultText = success ? 'MIGRATION SUCCESSFUL' : 'MIGRATION FAILED';

            document.getElementById('step5-result').innerHTML = 
                `<div class="test-result ${resultClass}">
                    <strong>${resultIcon} ${resultText}</strong><br>
                    ${messages.join('<br>')}
                </div>`;
        }

        function runCompleteTest() {
            document.getElementById('complete-test-result').innerHTML = 
                '<div class="test-result warning">🔄 Running complete test...</div>';
            
            setTimeout(() => {
                clearAllStorage();
                setTimeout(() => {
                    createLegacyData();
                    setTimeout(() => {
                        verifyLegacyData();
                        setTimeout(() => {
                            runMigration();
                            setTimeout(() => {
                                verifyMigration();
                                document.getElementById('complete-test-result').innerHTML = 
                                    '<div class="test-result success">✅ Complete test finished - check individual step results above</div>';
                            }, 100);
                        }, 100);
                    }, 100);
                }, 100);
            }, 100);
        }

        function showCurrentState() {
            const allKeys = Object.keys(localStorage);
            const datastatproKeys = allKeys.filter(key => key.startsWith('datastatpro_'));
            const statisticaKeys = allKeys.filter(key => key.startsWith('statistica_'));
            
            let html = `<div class="code">
                <strong>DataStatPro keys (${datastatproKeys.length}):</strong><br>`;
            
            if (datastatproKeys.length === 0) {
                html += 'None<br>';
            } else {
                datastatproKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    const preview = value ? (value.length > 50 ? value.substring(0, 50) + '...' : value) : 'null';
                    html += `${key}: ${preview}<br>`;
                });
            }
            
            html += `<br><strong>Legacy Statistica keys (${statisticaKeys.length}):</strong><br>`;
            
            if (statisticaKeys.length === 0) {
                html += 'None (✅ Clean state)<br>';
            } else {
                statisticaKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    const preview = value ? (value.length > 50 ? value.substring(0, 50) + '...' : value) : 'null';
                    html += `${key}: ${preview}<br>`;
                });
            }
            
            html += '</div>';
            document.getElementById('current-state').innerHTML = html;
        }

        // Show initial state
        showCurrentState();
    </script>
</body>
</html>
