<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Update Test - DataStatPro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .success { border-color: #4caf50; background-color: #f1f8e9; }
        .error { border-color: #f44336; background-color: #ffebee; }
        .warning { border-color: #ff9800; background-color: #fff3e0; }
        .info { border-color: #2196f3; background-color: #e3f2fd; }
        button {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #1565c0; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>PWA Update System Test</h1>
    <p>This page tests the PWA update mechanism for DataStatPro.</p>

    <div class="test-section info">
        <h2>Service Worker Status</h2>
        <p id="sw-status">Checking...</p>
        <button onclick="checkServiceWorker()">Refresh Status</button>
    </div>

    <div class="test-section">
        <h2>Update Detection</h2>
        <p id="update-status">No updates detected</p>
        <button onclick="checkForUpdates()">Check for Updates</button>
        <button onclick="simulateUpdate()">Simulate Update Available</button>
    </div>

    <div class="test-section">
        <h2>Cache Management</h2>
        <p id="cache-status">Checking cache...</p>
        <button onclick="listCaches()">List Caches</button>
        <button onclick="clearCaches()">Clear All Caches</button>
    </div>

    <div class="test-section">
        <h2>Version Information</h2>
        <p id="version-info">Loading...</p>
        <button onclick="getVersionInfo()">Get Version Info</button>
    </div>

    <div class="test-section">
        <h2>Offline Ready Notification Test</h2>
        <p id="offline-status">Testing offline ready notification behavior...</p>
        <button onclick="testOfflineNotification()">Test Offline Notification</button>
        <button onclick="resetOfflineNotification()">Reset Offline Notification</button>
    </div>

    <div class="test-section">
        <h2>Cache Diagnostics</h2>
        <p id="cache-diagnosis">Click to run cache diagnostics...</p>
        <button onclick="runCacheDiagnostics()">Run Diagnostics</button>
        <button onclick="recoverCaches()">Recover Caches</button>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        let registration = null;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function checkServiceWorker() {
            const statusElement = document.getElementById('sw-status');
            
            if ('serviceWorker' in navigator) {
                try {
                    registration = await navigator.serviceWorker.getRegistration();
                    
                    if (registration) {
                        statusElement.innerHTML = `
                            <strong>✅ Service Worker Registered</strong><br>
                            Scope: ${registration.scope}<br>
                            State: ${registration.active ? registration.active.state : 'No active worker'}<br>
                            Update Found: ${registration.waiting ? 'Yes' : 'No'}
                        `;
                        statusElement.parentElement.className = 'test-section success';
                        log('Service Worker is registered and active');
                    } else {
                        statusElement.innerHTML = '<strong>❌ Service Worker Not Registered</strong>';
                        statusElement.parentElement.className = 'test-section error';
                        log('Service Worker is not registered');
                    }
                } catch (error) {
                    statusElement.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                    statusElement.parentElement.className = 'test-section error';
                    log(`Service Worker check error: ${error.message}`);
                }
            } else {
                statusElement.innerHTML = '<strong>❌ Service Workers Not Supported</strong>';
                statusElement.parentElement.className = 'test-section error';
                log('Service Workers are not supported in this browser');
            }
        }

        async function checkForUpdates() {
            const statusElement = document.getElementById('update-status');
            
            if (!registration) {
                statusElement.textContent = 'Service Worker not registered';
                return;
            }

            try {
                log('Checking for updates...');
                await registration.update();
                
                if (registration.waiting) {
                    statusElement.innerHTML = '<strong>🔄 Update Available!</strong> New version is ready to install.';
                    statusElement.parentElement.className = 'test-section warning';
                    log('Update detected - new service worker is waiting');
                } else {
                    statusElement.innerHTML = '<strong>✅ Up to Date</strong> No updates available.';
                    statusElement.parentElement.className = 'test-section success';
                    log('No updates found - app is up to date');
                }
            } catch (error) {
                statusElement.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                statusElement.parentElement.className = 'test-section error';
                log(`Update check error: ${error.message}`);
            }
        }

        function simulateUpdate() {
            const statusElement = document.getElementById('update-status');
            statusElement.innerHTML = '<strong>🔄 Simulated Update Available!</strong> This is a test simulation.';
            statusElement.parentElement.className = 'test-section warning';
            log('Simulated update availability for testing');
        }

        async function listCaches() {
            const statusElement = document.getElementById('cache-status');
            
            if ('caches' in window) {
                try {
                    const cacheNames = await caches.keys();
                    statusElement.innerHTML = `
                        <strong>Cache Information:</strong><br>
                        Found ${cacheNames.length} cache(s):<br>
                        ${cacheNames.map(name => `• ${name}`).join('<br>')}
                    `;
                    log(`Found ${cacheNames.length} caches: ${cacheNames.join(', ')}`);
                } catch (error) {
                    statusElement.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                    log(`Cache list error: ${error.message}`);
                }
            } else {
                statusElement.innerHTML = '<strong>❌ Cache API Not Supported</strong>';
                log('Cache API is not supported in this browser');
            }
        }

        async function clearCaches() {
            if ('caches' in window) {
                try {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                    document.getElementById('cache-status').innerHTML = '<strong>✅ All Caches Cleared</strong>';
                    log(`Cleared ${cacheNames.length} caches`);
                } catch (error) {
                    document.getElementById('cache-status').innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                    log(`Cache clear error: ${error.message}`);
                }
            }
        }

        function getVersionInfo() {
            const versionElement = document.getElementById('version-info');
            
            // Try to get version from global constants or environment
            let version = 'Unknown';
            let buildDate = 'Unknown';
            
            try {
                // These would be available if the app is loaded
                if (typeof __APP_VERSION__ !== 'undefined') {
                    version = __APP_VERSION__;
                }
                if (typeof __BUILD_DATE__ !== 'undefined') {
                    buildDate = __BUILD_DATE__;
                }
            } catch (e) {
                // Fallback to checking if we can access the main app
                version = '1.0.0 (Test Environment)';
                buildDate = new Date().toISOString();
            }
            
            versionElement.innerHTML = `
                <strong>Version:</strong> ${version}<br>
                <strong>Build Date:</strong> ${buildDate}<br>
                <strong>User Agent:</strong> ${navigator.userAgent.substring(0, 100)}...
            `;
            
            log(`Version info - Version: ${version}, Build: ${buildDate}`);
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            log('PWA Update Test initialized');
            checkServiceWorker();
            listCaches();
            getVersionInfo();
        });

        // Listen for service worker updates
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                log('Service Worker controller changed - page will reload');
                window.location.reload();
            });
        }

        function testOfflineNotification() {
            const statusElement = document.getElementById('offline-status');

            // Check session storage for offline notification state
            const sessionId = sessionStorage.getItem('pwa-session-id') || 'test-session';
            const offlineShownKey = `pwa-offline-ready-shown_${sessionId}`;
            const hasBeenShownThisSession = sessionStorage.getItem(offlineShownKey) === 'true';
            const hasBeenShownBefore = localStorage.getItem('pwa-offline-ready-shown') === 'true';

            statusElement.innerHTML = `
                <strong>Offline Notification State:</strong><br>
                Session ID: ${sessionId}<br>
                Shown this session: ${hasBeenShownThisSession ? 'Yes' : 'No'}<br>
                Shown before: ${hasBeenShownBefore ? 'Yes' : 'No'}<br>
                Should show notification: ${(!hasBeenShownThisSession && !hasBeenShownBefore) ? 'Yes' : 'No'}
            `;

            log(`Offline notification test - Session: ${hasBeenShownThisSession}, Before: ${hasBeenShownBefore}`);
        }

        function resetOfflineNotification() {
            const sessionId = sessionStorage.getItem('pwa-session-id') || 'test-session';
            const offlineShownKey = `pwa-offline-ready-shown_${sessionId}`;

            sessionStorage.removeItem(offlineShownKey);
            localStorage.removeItem('pwa-offline-ready-shown');

            document.getElementById('offline-status').innerHTML = '<strong>✅ Offline notification state reset</strong>';
            log('Offline notification state has been reset');
        }

        async function runCacheDiagnostics() {
            const diagnosisElement = document.getElementById('cache-diagnosis');
            diagnosisElement.innerHTML = 'Running cache diagnostics...';

            try {
                // Basic cache information
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    let totalSize = 0;
                    let totalEntries = 0;

                    for (const name of cacheNames) {
                        const cache = await caches.open(name);
                        const keys = await cache.keys();
                        totalEntries += keys.length;

                        // Estimate size (approximate)
                        for (const request of keys.slice(0, 10)) { // Sample first 10 for performance
                            try {
                                const response = await cache.match(request);
                                if (response) {
                                    const blob = await response.blob();
                                    totalSize += blob.size;
                                }
                            } catch (e) {
                                // Ignore individual errors
                            }
                        }
                    }

                    const estimatedTotalSize = totalEntries > 10 ? (totalSize * totalEntries / 10) : totalSize;

                    diagnosisElement.innerHTML = `
                        <strong>Cache Diagnostics Results:</strong><br>
                        Total caches: ${cacheNames.length}<br>
                        Total entries: ${totalEntries}<br>
                        Estimated size: ${Math.round(estimatedTotalSize / (1024 * 1024))}MB<br>
                        Cache names: ${cacheNames.join(', ')}<br>
                        <br>
                        <strong>Potential Issues:</strong><br>
                        ${totalEntries > 1000 ? '⚠️ Too many cache entries (>1000)' : '✅ Cache entry count OK'}<br>
                        ${estimatedTotalSize > 100 * 1024 * 1024 ? '⚠️ Cache size too large (>100MB)' : '✅ Cache size OK'}<br>
                        ${cacheNames.length > 10 ? '⚠️ Too many caches (>10)' : '✅ Cache count OK'}
                    `;

                    log(`Cache diagnostics - ${cacheNames.length} caches, ${totalEntries} entries, ~${Math.round(estimatedTotalSize / (1024 * 1024))}MB`);
                } else {
                    diagnosisElement.innerHTML = '<strong>❌ Cache API not supported</strong>';
                }
            } catch (error) {
                diagnosisElement.innerHTML = `<strong>❌ Diagnostics failed:</strong> ${error.message}`;
                log(`Cache diagnostics failed: ${error.message}`);
            }
        }

        async function recoverCaches() {
            try {
                log('Starting cache recovery...');

                // Clear all caches
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                    log(`Cleared ${cacheNames.length} caches`);
                }

                // Clear cache-related localStorage
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.includes('cache') || key.includes('pwa'))) {
                        keysToRemove.push(key);
                    }
                }
                keysToRemove.forEach(key => localStorage.removeItem(key));

                document.getElementById('cache-diagnosis').innerHTML = '<strong>✅ Cache recovery completed</strong>';
                log('Cache recovery completed successfully');

                // Suggest reload
                if (confirm('Cache recovery completed. Reload the page to test with fresh caches?')) {
                    window.location.reload();
                }
            } catch (error) {
                document.getElementById('cache-diagnosis').innerHTML = `<strong>❌ Recovery failed:</strong> ${error.message}`;
                log(`Cache recovery failed: ${error.message}`);
            }
        }
    </script>
</body>
</html>
