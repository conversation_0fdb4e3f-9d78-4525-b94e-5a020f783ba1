// Type definitions for the application

// Data value types
export type DataValue = string | number | boolean | Date | null | undefined;

// Row data type for datasets
export type DataRow = Record<string, DataValue>;

// Main data type for storing datasets
export interface Dataset {
  id: string;
  name: string;
  description?: string;
  dateCreated: Date;
  dateModified: Date;
  columns: Column[];
  data: DataRow[];
  userId?: string; // ID of the user who owns this dataset
}

// Column definition
export interface Column {
  id: string;
  name: string;
  description?: string;
  type: DataType;
  role: VariableRole;
  statistics?: ColumnStatistics;
  transformations?: Transformation[];
  categoryOrder?: string[]; // For categorical/ordinal variables: defines custom category ordering
  missingValueCodes?: string[]; // User-defined missing value representations (e.g., ["", "na", "N.A.", "-", "999"])
}

// Data types supported by the application
export enum DataType {
  NUMERIC = 'numeric',
  CATEGORICAL = 'categorical',
  ORDINAL = 'ordinal',
  DATE = 'date',
  TEXT = 'text',
  BOOLEAN = 'boolean'
}

// Variable roles in analysis
export enum VariableRole {
  INDEPENDENT = 'independent',
  DEPENDENT = 'dependent',
  COVARIATE = 'covariate',
  NONE = 'none'
}

// Basic statistics for a column
export interface ColumnStatistics {
  // Common
  count: number;
  missing: number;
  
  // Numeric
  mean?: number;
  median?: number;
  mode?: number[];
  variance?: number;
  standardDeviation?: number;
  min?: number;
  max?: number;
  range?: number;
  quartiles?: [number, number, number];
  skewness?: number;
  kurtosis?: number;
  
  // Categorical
  frequencies?: Record<string, number>;
  proportions?: Record<string, number>;
}

// Transformation parameter types
export interface RecodeParameters {
  mappings: Record<string, DataValue>;
}

export interface StandardizeParameters {
  method: 'zscore' | 'minmax' | 'robust';
}

export interface LogTransformParameters {
  base: number;
}

export interface BinningParameters {
  numBins: number;
  method: 'equal-width' | 'equal-frequency';
}

export interface DummyCodingParameters {
  referenceCategory?: string;
}

export interface RobustScaleParameters {
  withCentering: boolean;
  withScaling: boolean;
}

export interface ComputeVariableParameters {
  columns: string[];
  method: 'sum' | 'mean' | 'count' | 'std' | 'min' | 'max';
}

export type TransformationParameters =
  | RecodeParameters
  | StandardizeParameters
  | LogTransformParameters
  | BinningParameters
  | DummyCodingParameters
  | RobustScaleParameters
  | ComputeVariableParameters;

// Data transformation
export interface Transformation {
  id: string;
  name: string;
  description?: string;
  type: TransformationType;
  parameters: TransformationParameters;
  original: Column;
}

// Types of transformations
export enum TransformationType {
  RECODE = 'recode',
  STANDARDIZE = 'standardize',
  LOG = 'log',
  SQUARE_ROOT = 'squareRoot',
  BINNING = 'binning',
  DUMMY_CODING = 'dummyCoding',
  ROBUST_SCALE = 'robustScale',
  COMPUTE_VARIABLE = 'computeVariable'
}

// Statistical test result
export interface StatisticalTestResult {
  testName: string;
  statistic: number;
  pValue: number;
  degreesOfFreedom?: number;
  criticalValue?: number;
  confidenceInterval?: [number, number];
  effectSize?: number;
  interpretation?: string;
}

// Descriptive statistics
export interface DescriptiveStatistics {
  count: number;
  mean: number;
  median: number;
  mode?: number | string;
  standardDeviation: number;
  variance: number;
  minimum: number;
  maximum: number;
  range: number;
  quartiles: {
    q1: number;
    q3: number;
    iqr: number;
  };
  skewness?: number;
  kurtosis?: number;
}

// ANOVA results
export interface ANOVAResult {
  fStatistic: number;
  pValue: number;
  degreesOfFreedomBetween: number;
  degreesOfFreedomWithin: number;
  meanSquareBetween: number;
  meanSquareWithin: number;
  sumOfSquaresBetween: number;
  sumOfSquaresWithin: number;
  etaSquared?: number;
  partialEtaSquared?: number;
}

// Correlation results
export interface CorrelationResult {
  coefficient: number;
  pValue: number;
  confidenceInterval?: [number, number];
  method: 'pearson' | 'spearman' | 'kendall';
}

// Regression results
export interface RegressionResult {
  coefficients: Array<{
    variable: string;
    coefficient: number;
    standardError: number;
    tStatistic: number;
    pValue: number;
    confidenceInterval: [number, number];
  }>;
  intercept?: number;
  p_values?: Record<string, number>;
  rSquared: number;
  adjustedRSquared: number;
  fStatistic: number;
  fPValue: number;
  pValue?: number; // Overall model p-value
  dfModel?: number;
  dfError?: number;
  residualStandardError: number;
  degreesOfFreedom: number;
}

// Analysis parameter types
export interface TTestParameters {
  alpha: number;
  alternative: 'two-sided' | 'less' | 'greater';
  equalVariances: boolean;
}

export interface ANOVAParameters {
  alpha: number;
  postHocTest?: 'tukey' | 'bonferroni' | 'scheffe';
}

export interface CorrelationParameters {
  method: 'pearson' | 'spearman' | 'kendall';
  alpha: number;
}

export interface RegressionParameters {
  alpha: number;
  includeIntercept: boolean;
  method: 'ols' | 'ridge' | 'lasso';
}

export type AnalysisParameters =
  | TTestParameters
  | ANOVAParameters
  | CorrelationParameters
  | RegressionParameters;

export type AnalysisResults =
  | DescriptiveStatistics
  | StatisticalTestResult
  | ANOVAResult
  | CorrelationResult
  | RegressionResult;

// Analysis results
export interface AnalysisResult {
  id: string;
  name: string;
  description?: string;
  type: AnalysisType;
  dateCreated: Date;
  parameters: AnalysisParameters;
  results: AnalysisResults;
  dataset: string; // ID of the dataset
  columns: string[]; // IDs of the columns used
}

// Types of analyses
export enum AnalysisType {
  DESCRIPTIVE = 'descriptive',
  T_TEST = 'tTest',
  PAIRED_T_TEST = 'pairedTTest',
  ONE_WAY_ANOVA = 'oneWayAnova',
  CORRELATION = 'correlation',
  LINEAR_REGRESSION = 'linearRegression',
  LOGISTIC_REGRESSION = 'logisticRegression',
  CHI_SQUARE = 'chiSquare',
  MANN_WHITNEY = 'mannWhitney',
  WILCOXON = 'wilcoxon',
  KRUSKAL_WALLIS = 'kruskalWallis'
}

// Chart styling options
export interface ChartStyling {
  colors?: string[];
  theme?: 'light' | 'dark';
  showLegend?: boolean;
  showGrid?: boolean;
  showAxes?: boolean;
  fontSize?: number;
  fontFamily?: string;
}

// Chart axis configuration
export interface AxisConfig {
  title?: string;
  min?: number;
  max?: number;
  scale?: 'linear' | 'log' | 'category';
  tickFormat?: string;
  showTicks?: boolean;
}

// Chart options by type
export interface BarChartOptions extends ChartStyling {
  orientation?: 'vertical' | 'horizontal';
  stacked?: boolean;
  grouped?: boolean;
  xAxis?: AxisConfig;
  yAxis?: AxisConfig;
}

export interface LineChartOptions extends ChartStyling {
  smooth?: boolean;
  showPoints?: boolean;
  lineWidth?: number;
  xAxis?: AxisConfig;
  yAxis?: AxisConfig;
}

export interface ScatterPlotOptions extends ChartStyling {
  showTrendline?: boolean;
  pointSize?: number;
  opacity?: number;
  xAxis?: AxisConfig;
  yAxis?: AxisConfig;
}

export interface HistogramOptions extends ChartStyling {
  bins?: number;
  density?: boolean;
  cumulative?: boolean;
  xAxis?: AxisConfig;
  yAxis?: AxisConfig;
}

export interface BoxPlotOptions extends ChartStyling {
  orientation?: 'vertical' | 'horizontal';
  showOutliers?: boolean;
  notched?: boolean;
  xAxis?: AxisConfig;
  yAxis?: AxisConfig;
}

export interface PieChartOptions extends ChartStyling {
  showLabels?: boolean;
  showPercentages?: boolean;
  donut?: boolean;
  donutRatio?: number;
}

export interface HeatmapOptions extends ChartStyling {
  colorScale?: string;
  showValues?: boolean;
  xAxis?: AxisConfig;
  yAxis?: AxisConfig;
}

export type ChartOptions =
  | BarChartOptions
  | LineChartOptions
  | ScatterPlotOptions
  | HistogramOptions
  | BoxPlotOptions
  | PieChartOptions
  | HeatmapOptions;

// Chart configuration
export interface ChartConfig {
  id: string;
  name: string;
  type: ChartType;
  dataset: string; // ID of the dataset
  columns: {
    x?: string; // ID of the x-axis column
    y?: string[]; // IDs of the y-axis columns
    group?: string; // ID of the grouping column
    size?: string; // ID of the size column (for bubble charts)
  };
  options: ChartOptions;
}

// Types of charts
export enum ChartType {
  BAR = 'bar',
  PIE = 'pie',
  LINE = 'line',
  SCATTER = 'scatter',
  HISTOGRAM = 'histogram',
  BOX_PLOT = 'boxPlot',
  HEATMAP = 'heatmap'
}

// Error handling types
export interface AppError {
  code: string;
  message: string;
  details?: string;
  timestamp: Date;
  context?: Record<string, DataValue>;
}

export interface ValidationError extends AppError {
  field: string;
  value: DataValue;
  constraint: string;
}

export interface DataProcessingError extends AppError {
  operation: string;
  datasetId?: string;
  columnId?: string;
}

export interface AnalysisError extends AppError {
  analysisType: AnalysisType;
  parameters?: AnalysisParameters;
}

// API response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: AppError;
  timestamp: Date;
}

// Data import/export types
export interface ImportOptions {
  header?: boolean;
  delimiter?: string;
  encoding?: string;
  sheetName?: string;
  sheetIndex?: number;
}

export interface ExportOptions {
  format: 'csv' | 'excel' | 'json';
  includeHeaders?: boolean;
  selectedColumns?: string[];
  filename?: string;
}

// Data filtering types
export interface FilterCondition {
  column: string;
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith' | 'isEmpty' | 'isNotEmpty' | 'between' | 'notBetween' | 'in' | 'notIn';
  value?: DataValue;
  value2?: DataValue; // For between operations
  values?: DataValue[]; // For in/notIn operations
}

export interface DataFilter {
  id: string;
  conditions: FilterCondition[];
  logic: 'AND' | 'OR';
  name?: string;
  isActive: boolean;
}

// Missing data handling types
export interface MissingValueInfo {
  originalValue: DataValue;
  isMissing: boolean;
  matchedCode?: string; // Which missing value code was matched
}

export interface CleanedDataResult {
  cleanedData: DataRow[];
  missingValueSummary: {
    totalMissing: number;
    missingByColumn: Record<string, number>;
    missingByRow: number[];
  };
}

export interface SortCondition {
  column: string;
  direction: 'asc' | 'desc';
}

// Data validation types
export interface MissingValueReport {
  total: number;
  byColumn: Record<string, number>;
  byRow: number[];
}

export interface OutlierReport {
  outliers: Array<{ value: DataValue; rowIndex: number }>;
  lowerBound: number;
  upperBound: number;
  q1: number;
  q3: number;
  iqr: number;
}

// Utility types for better type safety
export type NonEmptyArray<T> = [T, ...T[]];
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
