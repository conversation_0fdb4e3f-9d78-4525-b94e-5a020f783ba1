declare module 'jstat' {
  interface JStatDistribution {
    cdf: (...args: number[]) => number;
    inv: (...args: number[]) => number;
    pdf: (...args: number[]) => number;
    mean: (...args: number[]) => number;
    median: (...args: number[]) => number;
    mode: (...args: number[]) => number;
    variance: (...args: number[]) => number;
    stdev: (...args: number[]) => number;
  }

  interface JStat {
    // Statistical distributions
    studentt: JStatDistribution;
    tukey: JStatDistribution;
    centralF: JStatDistribution;
    chisquare: JStatDistribution;
    normal: JStatDistribution;

    // Statistical functions
    mean: (data: number[]) => number;
    median: (data: number[]) => number;
    mode: (data: number[]) => number | number[];
    variance: (data: number[], sample?: boolean) => number;
    stdev: (data: number[], sample?: boolean) => number;
    skewness: (data: number[]) => number;
    kurtosis: (data: number[]) => number;
    corrcoeff: (x: number[], y: number[]) => number;
    quantiles: (data: number[], percentiles: number[]) => number[];
    percentile: (data: number[], percentile: number) => number;

    // Additional utility functions
    sum: (data: number[]) => number;
    min: (data: number[]) => number;
    max: (data: number[]) => number;
    range: (data: number[]) => number;
  }

  const jStat: JStat;
  export = jStat;
}
