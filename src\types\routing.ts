// Type definitions for the new routing system

// User type for routing context
export interface User {
  id: string;
  email: string;
  name?: string;
  role?: 'user' | 'admin' | 'guest';
  preferences?: Record<string, unknown>;
}

// Route component props
export interface RouteComponentProps {
  params?: RouteParams;
  query?: Record<string, string>;
  user?: User;
}

export interface RouteConfig {
  path: string;
  component: React.ComponentType<any>; // Keep flexible for now to maintain compatibility
  exact?: boolean;
  requiresAuth?: boolean;
  requiresAdmin?: boolean; // New: Requires admin privileges
  allowGuest?: boolean;
  allowPublic?: boolean;
  props?: Record<string, unknown>;
  children?: RouteConfig[];
}

export interface RouteParams {
  page: string;
  subPage?: string;
  [key: string]: string | undefined;
}

export interface NavigationContext {
  navigateToPage: (path: string) => void;
  currentPage: string;
  currentSubPage: string;
  isAuthenticated: boolean;
  isGuest: boolean;
  isAdmin?: boolean; // New: Admin status
  user?: User;
}

export interface RouteGuardResult {
  allowed: boolean;
  redirectTo?: string;
  reason?: string;
}

export interface RouteGuard {
  name: string;
  check: (route: RouteConfig, context: NavigationContext) => RouteGuardResult;
}

// Route component props interface
export interface RouteComponentProps {
  onNavigate?: (path: string) => void;
  initialTab?: string;
  initialSubPage?: string;
  [key: string]: any;
}

// Route registry for dynamic route management
export interface RouteRegistry {
  register: (routes: RouteConfig[]) => void;
  unregister: (paths: string[]) => void;
  getRoute: (path: string) => RouteConfig | undefined;
  getAllRoutes: () => RouteConfig[];
  findMatchingRoute: (page: string, subPage?: string) => RouteConfig | undefined;
}

// Route metadata for better organization
export interface RouteMetadata {
  title: string;
  description?: string;
  category: string;
  icon?: string;
  order?: number;
  hidden?: boolean;
  beta?: boolean;
  deprecated?: boolean;
  adminOnly?: boolean; // New: Indicates admin-only routes
}

export interface EnhancedRouteConfig extends RouteConfig {
  metadata?: RouteMetadata;
}
