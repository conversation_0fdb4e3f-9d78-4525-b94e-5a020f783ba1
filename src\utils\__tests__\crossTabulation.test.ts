import { extractCategoricalValuesWithMissingCodes } from '../missingDataUtils';
import { getOrderedCategories, createOrderedCrossTabulation } from '../dataUtilities';
import { Dataset, Column, DataType, VariableRole } from '../../types';

describe('CrossTabulation Missing Data Integration', () => {
  // Test data setup with missing values
  const genderColumn: Column = {
    id: 'gender-col',
    name: 'gender',
    type: DataType.CATEGORICAL,
    role: VariableRole.INDEPENDENT,
    missingValueCodes: ['-', 'na'] // User-defined missing codes
  };

  const educationColumn: Column = {
    id: 'education-col',
    name: 'education',
    type: DataType.CATEGORICAL,
    role: VariableRole.INDEPENDENT,
    missingValueCodes: ['unknown', 'N/A']
  };

  const testData = [
    { gender: 'Male', education: 'College' },
    { gender: 'Female', education: 'Graduate' },
    { gender: '-', education: 'High School' }, // Missing gender
    { gender: 'Male', education: 'unknown' }, // Missing education
    { gender: 'na', education: 'College' }, // Missing gender
    { gender: 'Female', education: 'N/A' }, // Missing education
    { gender: 'Male', education: 'PhD' },
    { gender: 'Female', education: 'College' },
    { gender: null, education: 'Graduate' }, // Null gender
    { gender: 'Male', education: '' }, // Empty education
  ];

  const testDataset: Dataset = {
    id: 'test-dataset',
    name: 'Test Dataset',
    dateCreated: new Date(),
    dateModified: new Date(),
    columns: [genderColumn, educationColumn],
    data: testData
  };

  describe('extractCategoricalValuesWithMissingCodes', () => {
    it('should exclude user-defined missing codes from gender', () => {
      const validGenders = extractCategoricalValuesWithMissingCodes(testData, genderColumn);
      
      // Should exclude '-', 'na', null
      expect(validGenders).toEqual(['Male', 'Female', 'Male', 'Male', 'Female', 'Male']);
      expect(validGenders).not.toContain('-');
      expect(validGenders).not.toContain('na');
      expect(validGenders).not.toContain(null);
    });

    it('should exclude user-defined missing codes from education', () => {
      const validEducation = extractCategoricalValuesWithMissingCodes(testData, educationColumn);
      
      // Should exclude 'unknown', 'N/A', empty string
      expect(validEducation).toEqual(['College', 'Graduate', 'High School', 'PhD', 'College', 'Graduate']);
      expect(validEducation).not.toContain('unknown');
      expect(validEducation).not.toContain('N/A');
      expect(validEducation).not.toContain('');
    });
  });

  describe('getOrderedCategories', () => {
    it('should return only valid categories for gender', () => {
      const categories = getOrderedCategories(genderColumn, testData);
      
      // Should only include valid categories, excluding missing codes
      expect(categories).toEqual(['Female', 'Male']); // Alphabetical order
      expect(categories).not.toContain('-');
      expect(categories).not.toContain('na');
    });

    it('should return only valid categories for education', () => {
      const categories = getOrderedCategories(educationColumn, testData);
      
      // Should only include valid categories, excluding missing codes
      expect(categories.sort()).toEqual(['College', 'Graduate', 'High School', 'PhD']);
      expect(categories).not.toContain('unknown');
      expect(categories).not.toContain('N/A');
    });
  });

  describe('createOrderedCrossTabulation', () => {
    it('should create cross-tabulation excluding missing values', () => {
      const result = createOrderedCrossTabulation(testDataset, 'gender-col', 'education-col');
      
      // Should only include valid categories
      expect(result.rowCategories).toEqual(['Female', 'Male']);
      expect(result.columnCategories.sort()).toEqual(['College', 'Graduate', 'High School', 'PhD']);
      
      // Check frequencies - should only count valid combinations
      expect(result.frequencies['Male']['College']).toBe(1);
      expect(result.frequencies['Female']['Graduate']).toBe(1);
      expect(result.frequencies['Male']['PhD']).toBe(1);
      expect(result.frequencies['Female']['College']).toBe(1);
      
      // Missing value combinations should not appear
      expect(result.frequencies['-']).toBeUndefined();
      expect(result.frequencies['na']).toBeUndefined();
    });
  });

  describe('integration with custom category order', () => {
    const orderedGenderColumn: Column = {
      ...genderColumn,
      categoryOrder: ['Male', 'Female'] // Custom order
    };

    const orderedEducationColumn: Column = {
      ...educationColumn,
      categoryOrder: ['High School', 'College', 'Graduate', 'PhD'] // Custom order
    };

    const orderedDataset: Dataset = {
      ...testDataset,
      columns: [orderedGenderColumn, orderedEducationColumn]
    };

    it('should respect custom category order while excluding missing values', () => {
      const result = createOrderedCrossTabulation(orderedDataset, 'gender-col', 'education-col');
      
      // Should follow custom order
      expect(result.rowCategories).toEqual(['Male', 'Female']);
      expect(result.columnCategories).toEqual(['High School', 'College', 'Graduate', 'PhD']);
      
      // Should still exclude missing values
      expect(result.rowCategories).not.toContain('-');
      expect(result.rowCategories).not.toContain('na');
      expect(result.columnCategories).not.toContain('unknown');
      expect(result.columnCategories).not.toContain('N/A');
    });
  });

  describe('backward compatibility', () => {
    const legacyGenderColumn: Column = {
      id: 'legacy-gender',
      name: 'gender',
      type: DataType.CATEGORICAL,
      role: VariableRole.INDEPENDENT
      // No missingValueCodes property
    };

    it('should work with columns that have no missing value codes defined', () => {
      const validGenders = extractCategoricalValuesWithMissingCodes(testData, legacyGenderColumn);
      
      // Should still exclude null and empty, but include '-' and 'na' as valid values
      expect(validGenders).toContain('-');
      expect(validGenders).toContain('na');
      expect(validGenders).not.toContain(null);
    });
  });
});
