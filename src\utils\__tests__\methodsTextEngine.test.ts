// Comprehensive Test Suite for Methods Text Engine
import { generateMethodsText, validateMethodsText, formatMethodsText, clearTemplateCache, getCacheStats } from '../methodsTextEngine';
import { ResultItem } from '../../context/ResultsContext';

describe('Methods Text Engine', () => {
  const sampleResults: ResultItem[] = [
    {
      id: 'test1',
      title: 'Descriptive Statistics Test',
      type: 'descriptive',
      component: 'Table1',
      timestamp: Date.now(),
      data: {
        dataset: 'Test Dataset',
        variables: ['age', 'height'],
        results: [
          {
            variableName: 'age',
            column: { type: 'numeric' },
            statistics: { mean: 35.2, standardDeviation: 12.1, n: 100 }
          }
        ],
        totalSampleSize: 100
      }
    },
    {
      id: 'test2',
      title: 'T-Test Analysis',
      type: 'ttest',
      component: 'TTest',
      timestamp: Date.now(),
      data: {
        testType: 'independent',
        testResult: {
          testName: 'Independent Samples t-test',
          pValue: 0.023,
          statistic: 2.45
        }
      }
    }
  ];

  test('should generate methods text from sample results', () => {
    const result = generateMethodsText(sampleResults);
    
    expect(result).toBeDefined();
    expect(result.fullText).toBeTruthy();
    expect(result.fullText.length).toBeGreaterThan(0);
    expect(result.analysesIncluded).toHaveLength(2);
    expect(result.wordCount).toBeGreaterThan(0);
  });

  test('should handle empty results array', () => {
    const result = generateMethodsText([]);
    
    expect(result.fullText).toBe('No analyses were selected for methods generation.');
    expect(result.analysesIncluded).toHaveLength(0);
    expect(result.wordCount).toBe(0);
  });

  test('should include software citation', () => {
    const result = generateMethodsText(sampleResults);
    
    expect(result.fullText).toContain('DataStatPro');
    expect(result.sections.software).toContain('DataStatPro');
  });

  test('should include significance criteria', () => {
    const result = generateMethodsText(sampleResults);

    expect(result.fullText).toContain('p < 0.05');
    expect(result.sections.significance).toContain('p < 0.05');
  });

  test('should handle invalid input gracefully', () => {
    const invalidResults = [
      null,
      undefined,
      { id: '', type: '', component: '', timestamp: 0, data: null }
    ] as any;

    const result = generateMethodsText(invalidResults);
    expect(result).toBeDefined();
    expect(result.fullText).toContain('Error generating');
  });

  test('should validate methods text quality', () => {
    const result = generateMethodsText(sampleResults);
    const validation = validateMethodsText(result);

    expect(validation).toBeDefined();
    expect(validation.isValid).toBeDefined();
    expect(Array.isArray(validation.warnings)).toBe(true);
    expect(Array.isArray(validation.suggestions)).toBe(true);
  });

  test('should format text in different formats', () => {
    const result = generateMethodsText(sampleResults);

    const paragraph = formatMethodsText(result, 'paragraph');
    const structured = formatMethodsText(result, 'structured');
    const html = formatMethodsText(result, 'html');

    expect(paragraph).toBeTruthy();
    expect(structured).toContain('Descriptive Analysis:');
    expect(html).toContain('<div class="statistical-methods">');
  });

  test('should handle large datasets efficiently', () => {
    const largeDataset = Array.from({ length: 50 }, (_, i) => ({
      id: `test-${i}`,
      title: `Analysis ${i}`,
      type: 'descriptive' as const,
      component: 'Table1',
      timestamp: Date.now(),
      data: { results: [], totalSampleSize: 100 }
    }));

    const startTime = performance.now();
    const result = generateMethodsText(largeDataset);
    const endTime = performance.now();

    expect(result).toBeDefined();
    expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
  });

  test('should manage cache properly', () => {
    clearTemplateCache();
    const initialStats = getCacheStats();
    expect(initialStats.size).toBe(0);

    generateMethodsText(sampleResults);
    const afterStats = getCacheStats();
    expect(afterStats.size).toBeGreaterThan(0);
  });
});

// Manual test function for development
export const runManualTest = () => {
  console.log('Running manual test of Methods Text Engine...');
  
  const sampleResults: ResultItem[] = [
    {
      id: 'manual-test-1',
      title: 'Sample Descriptive Analysis',
      type: 'descriptive',
      component: 'Table1',
      timestamp: Date.now(),
      data: {
        dataset: 'Sample Dataset',
        variables: ['age', 'height', 'weight'],
        results: [
          {
            variableName: 'age',
            column: { type: 'numeric' },
            statistics: { mean: 35.2, standardDeviation: 12.1, n: 100 }
          }
        ],
        totalSampleSize: 100
      }
    },
    {
      id: 'manual-test-2',
      title: 'Sample t-test',
      type: 'ttest',
      component: 'IndependentTTest',
      timestamp: Date.now(),
      data: {
        testType: 'independent',
        testResult: {
          testName: 'Independent Samples t-test',
          pValue: 0.023,
          statistic: 2.45
        }
      }
    },
    {
      id: 'manual-test-3',
      title: 'Sample ANOVA',
      type: 'anova',
      component: 'OneWayANOVA',
      timestamp: Date.now(),
      data: {
        anovaType: 'OneWay',
        factors: ['treatment_group'],
        testResult: {
          testName: 'One-Way ANOVA',
          pValue: 0.001,
          statistic: 8.92
        }
      }
    }
  ];

  const result = generateMethodsText(sampleResults);
  
  console.log('Generated Methods Text:');
  console.log('='.repeat(50));
  console.log(result.fullText);
  console.log('='.repeat(50));
  console.log('Word Count:', result.wordCount);
  console.log('Analyses Included:', result.analysesIncluded);
  console.log('Sections:', result.sections);
  
  return result;
};
