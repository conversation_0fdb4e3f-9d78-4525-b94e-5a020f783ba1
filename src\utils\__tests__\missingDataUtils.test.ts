import {
  isMissingValue,
  cleanDataset,
  extractNumericValuesWithMissingCodes,
  extractCategoricalValuesWithMissingCodes,
  getValidSampleSize,
  getColumnMissingSummary,
  validateMissingValueCodes,
  suggestMissingValueCodes
} from '../missingDataUtils';
import { Dataset, Column, DataType, VariableRole } from '../../types';

describe('Missing Data Utils', () => {
  // Test data setup
  const testColumn: Column = {
    id: 'test-col',
    name: 'testVar',
    type: DataType.NUMERIC,
    role: VariableRole.NONE,
    missingValueCodes: ['na', 'N.A.', '-', '999']
  };

  const testData = [
    { testVar: 10 },
    { testVar: 'na' },
    { testVar: 20 },
    { testVar: 'N.A.' },
    { testVar: 30 },
    { testVar: '-' },
    { testVar: 40 },
    { testVar: '999' },
    { testVar: null },
    { testVar: undefined },
    { testVar: '' },
    { testVar: 50 }
  ];

  const testDataset: Dataset = {
    id: 'test-dataset',
    name: 'Test Dataset',
    dateCreated: new Date(),
    dateModified: new Date(),
    columns: [testColumn],
    data: testData
  };

  describe('isMissingValue', () => {
    it('should identify null and undefined as missing', () => {
      expect(isMissingValue(null, testColumn).isMissing).toBe(true);
      expect(isMissingValue(undefined, testColumn).isMissing).toBe(true);
    });

    it('should identify empty string as missing', () => {
      expect(isMissingValue('', testColumn).isMissing).toBe(true);
      expect(isMissingValue('   ', testColumn).isMissing).toBe(true);
    });

    it('should identify user-defined missing codes as missing', () => {
      expect(isMissingValue('na', testColumn).isMissing).toBe(true);
      expect(isMissingValue('N.A.', testColumn).isMissing).toBe(true);
      expect(isMissingValue('-', testColumn).isMissing).toBe(true);
      expect(isMissingValue('999', testColumn).isMissing).toBe(true);
    });

    it('should not identify valid values as missing', () => {
      expect(isMissingValue(10, testColumn).isMissing).toBe(false);
      expect(isMissingValue('valid', testColumn).isMissing).toBe(false);
      expect(isMissingValue(0, testColumn).isMissing).toBe(false);
    });

    it('should return correct matched code', () => {
      const result = isMissingValue('na', testColumn);
      expect(result.matchedCode).toBe('na');
    });
  });

  describe('extractNumericValuesWithMissingCodes', () => {
    it('should extract only valid numeric values', () => {
      const result = extractNumericValuesWithMissingCodes(testData, testColumn);
      expect(result).toEqual([10, 20, 30, 40, 50]);
    });

    it('should exclude all missing value codes', () => {
      const result = extractNumericValuesWithMissingCodes(testData, testColumn);
      expect(result.length).toBe(5); // Only 5 valid numeric values
    });
  });

  describe('getColumnMissingSummary', () => {
    it('should provide accurate missing value statistics', () => {
      const summary = getColumnMissingSummary(testData, testColumn);
      
      expect(summary.totalValues).toBe(12);
      expect(summary.missingCount).toBe(7); // na, N.A., -, 999, null, undefined, empty
      expect(summary.validCount).toBe(5);
      expect(summary.missingPercentage).toBeCloseTo(58.33, 1);
    });

    it('should provide breakdown of missing value types', () => {
      const summary = getColumnMissingSummary(testData, testColumn);
      
      expect(summary.missingValueBreakdown).toHaveProperty('na');
      expect(summary.missingValueBreakdown).toHaveProperty('N.A.');
      expect(summary.missingValueBreakdown).toHaveProperty('-');
      expect(summary.missingValueBreakdown).toHaveProperty('999');
      expect(summary.missingValueBreakdown).toHaveProperty('null');
      expect(summary.missingValueBreakdown).toHaveProperty('undefined');
      expect(summary.missingValueBreakdown).toHaveProperty('empty string');
    });
  });

  describe('validateMissingValueCodes', () => {
    it('should validate correct missing value codes', () => {
      const result = validateMissingValueCodes(['na', 'N.A.', '-'], DataType.NUMERIC);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect duplicate codes', () => {
      const result = validateMissingValueCodes(['na', 'na', 'N.A.'], DataType.NUMERIC);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Duplicate missing value codes are not allowed');
    });

    it('should warn about potentially problematic codes', () => {
      const result = validateMissingValueCodes(['0'], DataType.NUMERIC);
      expect(result.warnings).toContain('Using "0" as a missing value code for numeric data may cause confusion');
    });
  });

  describe('cleanDataset', () => {
    it('should convert missing values to null', () => {
      const result = cleanDataset(testDataset);
      
      // Check that missing values are converted to null
      const cleanedValues = result.cleanedData.map(row => row.testVar);
      const nullCount = cleanedValues.filter(val => val === null).length;
      
      expect(nullCount).toBe(7); // All missing values should be null
    });

    it('should preserve valid values', () => {
      const result = cleanDataset(testDataset);
      
      const validValues = result.cleanedData
        .map(row => row.testVar)
        .filter(val => val !== null);
      
      expect(validValues).toEqual([10, 20, 30, 40, 50]);
    });

    it('should provide accurate missing value summary', () => {
      const result = cleanDataset(testDataset);
      
      expect(result.missingValueSummary.totalMissing).toBe(7);
      expect(result.missingValueSummary.missingByColumn.testVar).toBe(7);
    });
  });

  describe('suggestMissingValueCodes', () => {
    const suggestionTestData = [
      { testVar: 'valid1' },
      { testVar: 'valid2' },
      { testVar: 'na' },
      { testVar: 'N/A' },
      { testVar: 'missing' },
      { testVar: 'valid3' },
      { testVar: 'valid4' },
      { testVar: 'valid5' }
    ];

    it('should suggest common missing patterns found in data', () => {
      const suggestions = suggestMissingValueCodes(suggestionTestData, 'testVar');
      
      expect(suggestions).toContain('na');
      expect(suggestions).toContain('N/A');
      expect(suggestions).toContain('missing');
    });

    it('should not suggest values that appear frequently', () => {
      const frequentData = Array(10).fill({ testVar: 'common' });
      const suggestions = suggestMissingValueCodes(frequentData, 'testVar');
      
      expect(suggestions).not.toContain('common');
    });
  });

  describe('backward compatibility', () => {
    const columnWithoutMissingCodes: Column = {
      id: 'legacy-col',
      name: 'legacyVar',
      type: DataType.NUMERIC,
      role: VariableRole.NONE
      // No missingValueCodes property
    };

    it('should handle columns without missing value codes', () => {
      const result = isMissingValue('some value', columnWithoutMissingCodes);
      expect(result.isMissing).toBe(false);
    });

    it('should still detect null/undefined/empty for legacy columns', () => {
      expect(isMissingValue(null, columnWithoutMissingCodes).isMissing).toBe(true);
      expect(isMissingValue(undefined, columnWithoutMissingCodes).isMissing).toBe(true);
      expect(isMissingValue('', columnWithoutMissingCodes).isMissing).toBe(true);
    });
  });
});
