// Integration Tests for Statistical Methods Generator
import { generateMethodsText, validateMethodsText, formatMethodsText } from '../methodsTextEngine';
import { mapResultToAnalysisType, mapResultsToMappedAnalyses } from '../analysisMapper';
import { getTemplateByType } from '../methodsTemplates';
import { ResultItem } from '../../context/ResultsContext';

describe('Statistical Methods Generator Integration Tests', () => {
  
  // Comprehensive test dataset covering all analysis types
  const comprehensiveResults: ResultItem[] = [
    // Descriptive Statistics
    {
      id: 'desc-1',
      title: 'Baseline Characteristics',
      type: 'descriptive',
      component: 'Table1',
      timestamp: Date.now(),
      data: {
        dataset: 'Clinical Trial Dataset',
        variables: ['age', 'gender', 'bmi', 'blood_pressure'],
        results: [
          {
            variableName: 'age',
            column: { type: 'numeric' },
            statistics: { mean: 45.2, standardDeviation: 12.8, n: 200 }
          },
          {
            variableName: 'gender',
            column: { type: 'categorical' },
            statistics: { frequencies: { male: 120, female: 80 } }
          }
        ],
        totalSampleSize: 200
      }
    },
    
    // Independent t-test
    {
      id: 'ttest-1',
      title: 'Group Comparison - Primary Outcome',
      type: 'ttest',
      component: 'IndependentTTest',
      timestamp: Date.now(),
      data: {
        testType: 'independent',
        equalVariances: false,
        testResult: {
          testName: 'Independent Samples t-test',
          pValue: 0.023,
          statistic: 2.45,
          df: 198
        }
      }
    },
    
    // One-way ANOVA
    {
      id: 'anova-1',
      title: 'Treatment Group Analysis',
      type: 'anova',
      component: 'OneWayANOVA',
      timestamp: Date.now(),
      data: {
        anovaType: 'OneWay',
        factors: ['treatment_group'],
        testResult: {
          testName: 'One-Way ANOVA',
          pValue: 0.001,
          statistic: 8.92,
          df: [2, 197]
        }
      }
    },
    
    // Post-hoc tests
    {
      id: 'posthoc-1',
      title: 'Post-hoc Multiple Comparisons',
      type: 'other',
      component: 'PostHocTests',
      timestamp: Date.now(),
      data: {
        method: 'tukey',
        postHoc: 'tukey',
        testResult: {
          testName: 'Tukey HSD',
          pValue: 0.015
        }
      }
    },
    
    // Linear Regression
    {
      id: 'regression-1',
      title: 'Predictive Model Analysis',
      type: 'regression',
      component: 'LinearRegression',
      timestamp: Date.now(),
      data: {
        regressionType: 'linear',
        dependentVariable: 'outcome_score',
        independentVariables: ['age', 'bmi', 'treatment'],
        results: {
          rSquared: 0.45,
          adjustedRSquared: 0.42,
          fStatistic: 15.6,
          fPValue: 0.001
        }
      }
    },
    
    // Correlation Analysis
    {
      id: 'correlation-1',
      title: 'Variable Relationships',
      type: 'correlation',
      component: 'CorrelationAnalysis',
      timestamp: Date.now(),
      data: {
        method: 'pearson',
        testResult: {
          testName: 'Pearson Correlation',
          pValue: 0.032,
          statistic: 0.67
        }
      }
    },
    
    // Non-parametric test
    {
      id: 'nonparam-1',
      title: 'Non-parametric Group Comparison',
      type: 'nonparametric',
      component: 'MannWhitneyU',
      timestamp: Date.now(),
      data: {
        testType: 'mannWhitney',
        testResult: {
          testName: 'Mann-Whitney U Test',
          pValue: 0.045,
          statistic: 1250
        }
      }
    },
    
    // Chi-square test
    {
      id: 'chi-1',
      title: 'Categorical Association Analysis',
      type: 'other',
      component: 'CrossTabulation',
      timestamp: Date.now(),
      data: {
        chiSquare: {
          chiSquare: 12.45,
          df: 2,
          pValue: 0.002,
          cramersV: 0.25
        }
      }
    }
  ];

  test('should generate comprehensive methods text for all analysis types', () => {
    const result = generateMethodsText(comprehensiveResults);
    
    expect(result).toBeDefined();
    expect(result.fullText).toBeTruthy();
    expect(result.fullText.length).toBeGreaterThan(100);
    expect(result.analysesIncluded.length).toBeGreaterThan(0);
    expect(result.wordCount).toBeGreaterThan(50);
    
    // Check that all major analysis types are represented
    expect(result.fullText).toContain('Descriptive statistics');
    expect(result.fullText).toContain('t-test');
    expect(result.fullText).toContain('ANOVA');
    expect(result.fullText).toContain('regression');
    expect(result.fullText).toContain('correlation');
    expect(result.fullText).toContain('Mann-Whitney');
    expect(result.fullText).toContain('Chi-square');
    
    // Check for required elements
    expect(result.fullText).toContain('DataStatPro');
    expect(result.fullText).toContain('p < 0.05');
  });

  test('should validate comprehensive methods text', () => {
    const result = generateMethodsText(comprehensiveResults);
    const validation = validateMethodsText(result);
    
    expect(validation.isValid).toBe(true);
    expect(validation.warnings.length).toBeLessThanOrEqual(1); // Allow for minor warnings
    expect(validation.suggestions.length).toBeGreaterThanOrEqual(0);
  });

  test('should format comprehensive text in all formats', () => {
    const result = generateMethodsText(comprehensiveResults);
    
    const paragraph = formatMethodsText(result, 'paragraph');
    const structured = formatMethodsText(result, 'structured');
    const html = formatMethodsText(result, 'html');
    
    // Paragraph format
    expect(paragraph).toBeTruthy();
    expect(paragraph.split('\n').length).toBe(1); // Should be single paragraph
    
    // Structured format
    expect(structured).toContain('Descriptive Analysis:');
    expect(structured).toContain('Inferential Analysis:');
    expect(structured).toContain('Statistical Criteria:');
    expect(structured).toContain('Software:');
    
    // HTML format
    expect(html).toContain('<div class="statistical-methods">');
    expect(html).toContain('<h3>Statistical Methods</h3>');
    expect(html).toContain('<p>');
  });

  test('should handle edge cases gracefully', () => {
    // Empty array
    const emptyResult = generateMethodsText([]);
    expect(emptyResult.fullText).toContain('No analyses were selected');
    
    // Single analysis
    const singleResult = generateMethodsText([comprehensiveResults[0]]);
    expect(singleResult.fullText).toBeTruthy();
    expect(singleResult.analysesIncluded.length).toBe(1);
    
    // Duplicate analyses
    const duplicateResults = [comprehensiveResults[0], comprehensiveResults[0]];
    const duplicateResult = generateMethodsText(duplicateResults);
    expect(duplicateResult.fullText).toBeTruthy();
  });

  test('should maintain consistency across multiple generations', () => {
    const result1 = generateMethodsText(comprehensiveResults);
    const result2 = generateMethodsText(comprehensiveResults);
    
    // Results should be identical for same input
    expect(result1.fullText).toBe(result2.fullText);
    expect(result1.wordCount).toBe(result2.wordCount);
    expect(result1.analysesIncluded.length).toBe(result2.analysesIncluded.length);
  });

  test('should handle malformed data gracefully', () => {
    const malformedResults: ResultItem[] = [
      {
        id: 'malformed-1',
        title: '',
        type: 'descriptive',
        component: '',
        timestamp: Date.now(),
        data: null
      },
      {
        id: 'malformed-2',
        title: 'Valid Analysis',
        type: 'ttest',
        component: 'TTest',
        timestamp: Date.now(),
        data: {
          testType: 'independent',
          testResult: {
            testName: 'Independent Samples t-test',
            pValue: 0.05
          }
        }
      }
    ];
    
    const result = generateMethodsText(malformedResults);
    expect(result).toBeDefined();
    // Should still generate text from valid analyses
    expect(result.fullText).toBeTruthy();
  });

  test('should perform well with realistic dataset sizes', () => {
    // Test with 25 analyses (realistic for a comprehensive study)
    const realisticDataset = Array.from({ length: 25 }, (_, i) => ({
      ...comprehensiveResults[i % comprehensiveResults.length],
      id: `realistic-${i}`,
      title: `Analysis ${i + 1}`
    }));
    
    const startTime = performance.now();
    const result = generateMethodsText(realisticDataset);
    const endTime = performance.now();
    
    expect(result).toBeDefined();
    expect(result.fullText).toBeTruthy();
    expect(endTime - startTime).toBeLessThan(2000); // Should complete within 2 seconds
    expect(result.analysesIncluded.length).toBeGreaterThan(0);
  });

  test('should integrate properly with analysis mapper', () => {
    comprehensiveResults.forEach(result => {
      const analysisType = mapResultToAnalysisType(result);
      expect(analysisType).toBeTruthy();
      expect(typeof analysisType).toBe('string');
      
      const template = getTemplateByType(analysisType);
      if (analysisType !== 'other') {
        expect(template).toBeDefined();
      }
    });
    
    const mappedAnalyses = mapResultsToMappedAnalyses(comprehensiveResults);
    expect(mappedAnalyses.length).toBeGreaterThan(0);
    expect(mappedAnalyses.length).toBeLessThanOrEqual(comprehensiveResults.length);
  });
});
