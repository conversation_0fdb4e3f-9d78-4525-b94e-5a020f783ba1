// Analysis Mapper for Statistical Methods Generator
import { ResultItem } from '../context/ResultsContext';
import { getTemplateByType, MethodsTemplate } from './methodsTemplates';

export interface MappedAnalysis {
  resultId: string;
  analysisType: string;
  template: MethodsTemplate;
  data: any;
  title: string;
  component: string;
}

/**
 * Maps a ResultItem to its corresponding analysis type for methods generation
 */
export const mapResultToAnalysisType = (result: ResultItem): string => {
  if (!result || !result.type) {
    console.warn('Invalid result item provided to mapResultToAnalysisType:', result);
    return 'other';
  }

  // Direct mapping from result type
  const directMappings: Record<string, string> = {
    'descriptive': 'descriptive',
    'ttest': 'ttest',
    'anova': 'anova',
    'regression': 'regression',
    'correlation': 'correlation',
    'nonparametric': 'nonparametric',
    'other': 'other'
  };

  // Check direct mapping first
  if (directMappings[result.type]) {
    return directMappings[result.type];
  }

  // Component-based mapping for more specific analysis types
  const component = result.component?.toLowerCase() || '';
  
  // T-test variations
  if (component.includes('ttest') || component.includes('t-test')) {
    return 'ttest';
  }
  
  // ANOVA variations
  if (component.includes('anova') || component.includes('oneway') || component.includes('twoway') || component.includes('repeated')) {
    return 'anova';
  }
  
  // Regression variations
  if (component.includes('regression') || component.includes('linear') || component.includes('logistic') || component.includes('cox')) {
    return 'regression';
  }
  
  // Correlation
  if (component.includes('correlation') || component.includes('pearson') || component.includes('spearman')) {
    return 'correlation';
  }
  
  // Non-parametric tests
  if (component.includes('mannwhitney') || component.includes('wilcoxon') || 
      component.includes('kruskal') || component.includes('friedman') || 
      component.includes('nonparametric')) {
    return 'nonparametric';
  }
  
  // Chi-square tests
  if (component.includes('chi') || component.includes('crosstab') || component.includes('contingency')) {
    return 'chiSquare';
  }
  
  // Survival analysis
  if (component.includes('kaplan') || component.includes('cox') || component.includes('survival')) {
    return 'survival';
  }
  
  // Meta-analysis
  if (component.includes('meta')) {
    return 'meta';
  }
  
  // Reliability analysis
  if (component.includes('reliability') || component.includes('cronbach')) {
    return 'reliability';
  }
  
  // Normality tests
  if (component.includes('normality') || component.includes('shapiro')) {
    return 'normality';
  }
  
  // Post-hoc tests
  if (component.includes('posthoc') || component.includes('tukey') || component.includes('bonferroni')) {
    return 'posthoc';
  }

  // Factor analysis
  if (component.includes('factor') || component.includes('pca') || component.includes('principal')) {
    return 'factor';
  }

  // Cluster analysis
  if (component.includes('cluster') || component.includes('kmeans') || component.includes('hierarchical')) {
    return 'cluster';
  }

  // Check data content for additional clues
  const data = result.data || {};
  
  // Check for specific test names in data
  if (data.testName) {
    const testName = data.testName.toLowerCase();
    if (testName.includes('t-test') || testName.includes('ttest')) return 'ttest';
    if (testName.includes('anova')) return 'anova';
    if (testName.includes('chi')) return 'chiSquare';
    if (testName.includes('mann') || testName.includes('wilcoxon') || testName.includes('kruskal')) return 'nonparametric';
  }
  
  // Check for regression type
  if (data.regressionType) {
    return 'regression';
  }
  
  // Check for correlation method
  if (data.method && ['pearson', 'spearman', 'kendall'].includes(data.method)) {
    return 'correlation';
  }
  
  // Check for ANOVA type
  if (data.anovaType || data.factors) {
    return 'anova';
  }
  
  // Check for survival analysis indicators
  if (data.survivalTable || data.hazardRatios || data.medianSurvival) {
    return 'survival';
  }
  
  // Check for meta-analysis indicators
  if (data.pooledEffect || data.heterogeneity || data.forestPlotData) {
    return 'meta';
  }

  // Default fallback based on result type
  return result.type || 'other';
};

/**
 * Maps a ResultItem to a MappedAnalysis object
 */
export const mapResultToMappedAnalysis = (result: ResultItem): MappedAnalysis | null => {
  try {
    const analysisType = mapResultToAnalysisType(result);
    const template = getTemplateByType(analysisType);

    if (!template) {
      console.warn(`No template found for analysis type: ${analysisType} (result: ${result.title})`);
      return null;
    }

    return {
      resultId: result.id,
      analysisType,
      template,
      data: result.data || {},
      title: result.title || 'Untitled Analysis',
      component: result.component || 'Unknown'
    };
  } catch (error) {
    console.error('Error mapping result to analysis:', error, result);
    return null;
  }
};

/**
 * Maps multiple ResultItems to MappedAnalysis objects, filtering out unmappable ones
 */
export const mapResultsToMappedAnalyses = (results: ResultItem[]): MappedAnalysis[] => {
  return results
    .map(mapResultToMappedAnalysis)
    .filter((mapped): mapped is MappedAnalysis => mapped !== null);
};

/**
 * Groups mapped analyses by analysis type
 */
export const groupMappedAnalysesByType = (mappedAnalyses: MappedAnalysis[]): Record<string, MappedAnalysis[]> => {
  return mappedAnalyses.reduce((groups, analysis) => {
    if (!groups[analysis.analysisType]) {
      groups[analysis.analysisType] = [];
    }
    groups[analysis.analysisType].push(analysis);
    return groups;
  }, {} as Record<string, MappedAnalysis[]>);
};

/**
 * Determines if a result contains chi-square test data
 */
export const hasChiSquareData = (data: any): boolean => {
  return !!(data.chiSquare || data.testResult?.testName?.toLowerCase().includes('chi'));
};

/**
 * Determines if a result contains post-hoc test data
 */
export const hasPostHocData = (data: any): boolean => {
  return !!(data.postHoc || data.postHocResults || data.method?.toLowerCase().includes('tukey'));
};

/**
 * Extracts specific analysis details for template generation
 */
export const extractAnalysisDetails = (result: ResultItem): any => {
  const data = result.data || {};
  const component = result.component?.toLowerCase() || '';
  
  // Extract common details
  const details: any = {
    component: result.component,
    title: result.title,
    dataset: data.dataset,
    variables: data.variables,
    totalSampleSize: data.totalSampleSize
  };
  
  // Extract type-specific details
  if (component.includes('ttest')) {
    details.testType = data.testType || (component.includes('paired') ? 'paired' : 'independent');
  }
  
  if (component.includes('anova')) {
    details.anovaType = component;
    details.factors = data.factors || data.groupingVariable ? [data.groupingVariable] : [];
  }
  
  if (component.includes('regression')) {
    details.regressionType = data.regressionType || (component.includes('logistic') ? 'logistic' : 'linear');
    details.dependentVariable = data.dependentVariable;
    details.independentVariables = data.independentVariables;
  }
  
  if (component.includes('correlation')) {
    details.method = data.method || 'pearson';
  }
  
  // Extract test results if available
  if (data.testResult) {
    details.testName = data.testResult.testName;
    details.testType = data.testResult.testName;
  }
  
  // Extract chi-square specific data
  if (hasChiSquareData(data)) {
    details.testType = 'chiSquare';
  }
  
  // Extract post-hoc specific data
  if (hasPostHocData(data)) {
    details.method = data.method || data.postHocMethod || 'tukey';
  }
  
  return details;
};
