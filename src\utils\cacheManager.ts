/**
 * Cache Management Utilities for DataStatPro PWA
 * Handles cache-related issues and provides recovery mechanisms
 */

export interface CacheInfo {
  name: string;
  size: number;
  entries: number;
}

export interface CacheManagerOptions {
  maxCacheAge?: number; // Maximum cache age in milliseconds
  maxCacheSize?: number; // Maximum total cache size in bytes
  debugMode?: boolean;
}

class CacheManager {
  private options: CacheManagerOptions;
  private readonly CACHE_METADATA_KEY = 'datastatpro-cache-metadata';
  private readonly CACHE_VERSION_KEY = 'datastatpro-cache-version';

  constructor(options: CacheManagerOptions = {}) {
    this.options = {
      maxCacheAge: 7 * 24 * 60 * 60 * 1000, // 7 days default
      maxCacheSize: 100 * 1024 * 1024, // 100MB default
      debugMode: false,
      ...options
    };
  }

  /**
   * Get information about all caches
   */
  async getCacheInfo(): Promise<CacheInfo[]> {
    if (!('caches' in window)) {
      return [];
    }

    try {
      const cacheNames = await caches.keys();
      const cacheInfos: CacheInfo[] = [];

      for (const name of cacheNames) {
        const cache = await caches.open(name);
        const keys = await cache.keys();
        let totalSize = 0;

        // Estimate cache size (this is approximate)
        for (const request of keys) {
          try {
            const response = await cache.match(request);
            if (response) {
              const blob = await response.blob();
              totalSize += blob.size;
            }
          } catch (error) {
            if (this.options.debugMode) {
              console.warn(`Failed to get size for ${request.url}:`, error);
            }
          }
        }

        cacheInfos.push({
          name,
          size: totalSize,
          entries: keys.length
        });
      }

      return cacheInfos;
    } catch (error) {
      console.error('Failed to get cache info:', error);
      return [];
    }
  }

  /**
   * Clear all caches
   */
  async clearAllCaches(): Promise<boolean> {
    if (!('caches' in window)) {
      return false;
    }

    try {
      const cacheNames = await caches.keys();
      const deletePromises = cacheNames.map(name => caches.delete(name));
      await Promise.all(deletePromises);
      
      // Clear cache metadata
      localStorage.removeItem(this.CACHE_METADATA_KEY);
      localStorage.removeItem(this.CACHE_VERSION_KEY);
      
      if (this.options.debugMode) {
        console.log(`Cleared ${cacheNames.length} caches`);
      }
      
      return true;
    } catch (error) {
      console.error('Failed to clear caches:', error);
      return false;
    }
  }

  /**
   * Clear old/stale caches based on age
   */
  async clearStaleCaches(): Promise<number> {
    if (!('caches' in window)) {
      return 0;
    }

    try {
      const cacheNames = await caches.keys();
      const metadata = this.getCacheMetadata();
      const now = Date.now();
      let clearedCount = 0;

      for (const name of cacheNames) {
        const cacheAge = metadata[name]?.created || 0;
        const age = now - cacheAge;

        if (age > this.options.maxCacheAge!) {
          await caches.delete(name);
          delete metadata[name];
          clearedCount++;
          
          if (this.options.debugMode) {
            console.log(`Cleared stale cache: ${name} (age: ${Math.round(age / (1000 * 60 * 60))} hours)`);
          }
        }
      }

      this.setCacheMetadata(metadata);
      return clearedCount;
    } catch (error) {
      console.error('Failed to clear stale caches:', error);
      return 0;
    }
  }

  /**
   * Check if caches are causing loading issues
   */
  async diagnoseCacheIssues(): Promise<{
    hasIssues: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      const cacheInfos = await this.getCacheInfo();
      const totalSize = cacheInfos.reduce((sum, info) => sum + info.size, 0);
      const totalEntries = cacheInfos.reduce((sum, info) => sum + info.entries, 0);

      // Check for oversized caches
      if (totalSize > this.options.maxCacheSize!) {
        issues.push(`Total cache size (${Math.round(totalSize / (1024 * 1024))}MB) exceeds recommended limit`);
        recommendations.push('Clear old caches or reduce cache size limits');
      }

      // Check for too many cache entries
      if (totalEntries > 1000) {
        issues.push(`Too many cached entries (${totalEntries})`);
        recommendations.push('Clear unnecessary cached resources');
      }

      // Check for corrupted caches
      for (const info of cacheInfos) {
        if (info.entries === 0 && info.size > 0) {
          issues.push(`Potentially corrupted cache: ${info.name}`);
          recommendations.push(`Delete and recreate cache: ${info.name}`);
        }
      }

      // Check cache metadata consistency
      const metadata = this.getCacheMetadata();
      const metadataCaches = Object.keys(metadata);
      const actualCaches = cacheInfos.map(info => info.name);
      
      const orphanedMetadata = metadataCaches.filter(name => !actualCaches.includes(name));
      if (orphanedMetadata.length > 0) {
        issues.push(`Orphaned cache metadata found for: ${orphanedMetadata.join(', ')}`);
        recommendations.push('Clean up cache metadata');
      }

      return {
        hasIssues: issues.length > 0,
        issues,
        recommendations
      };
    } catch (error) {
      return {
        hasIssues: true,
        issues: [`Cache diagnosis failed: ${error.message}`],
        recommendations: ['Try clearing all caches and reloading the app']
      };
    }
  }

  /**
   * Perform cache recovery operations
   */
  async recoverFromCacheIssues(): Promise<boolean> {
    try {
      // Clear stale caches
      await this.clearStaleCaches();
      
      // Diagnose remaining issues
      const diagnosis = await this.diagnoseCacheIssues();
      
      if (diagnosis.hasIssues) {
        // If issues persist, clear all caches
        await this.clearAllCaches();
        
        if (this.options.debugMode) {
          console.log('Performed full cache recovery');
        }
      }
      
      return true;
    } catch (error) {
      console.error('Cache recovery failed:', error);
      return false;
    }
  }

  /**
   * Get cache metadata from localStorage
   */
  private getCacheMetadata(): Record<string, { created: number; version?: string }> {
    try {
      const metadata = localStorage.getItem(this.CACHE_METADATA_KEY);
      return metadata ? JSON.parse(metadata) : {};
    } catch {
      return {};
    }
  }

  /**
   * Set cache metadata in localStorage
   */
  private setCacheMetadata(metadata: Record<string, { created: number; version?: string }>): void {
    try {
      localStorage.setItem(this.CACHE_METADATA_KEY, JSON.stringify(metadata));
    } catch (error) {
      console.warn('Failed to save cache metadata:', error);
    }
  }

  /**
   * Record cache creation
   */
  recordCacheCreation(cacheName: string, version?: string): void {
    const metadata = this.getCacheMetadata();
    metadata[cacheName] = {
      created: Date.now(),
      version
    };
    this.setCacheMetadata(metadata);
  }

  /**
   * Check if app should force refresh due to cache issues
   */
  shouldForceRefresh(): boolean {
    try {
      const lastRefresh = localStorage.getItem('datastatpro-last-force-refresh');
      const now = Date.now();

      // Only allow force refresh once per hour to prevent loops
      if (lastRefresh && (now - parseInt(lastRefresh)) < 60 * 60 * 1000) {
        return false;
      }

      // Check for indicators of cache corruption
      const hasServiceWorker = 'serviceWorker' in navigator;
      const hasCache = 'caches' in window;

      if (!hasServiceWorker || !hasCache) {
        return false;
      }

      // Check for cache corruption indicators
      const cacheCorruptionIndicators = [
        // Check if app failed to load properly (no React root)
        !document.getElementById('root')?.hasChildNodes(),
        // Check if critical resources are missing from cache
        localStorage.getItem('datastatpro-cache-corruption-detected') === 'true',
        // Check if there are persistent loading issues
        localStorage.getItem('datastatpro-loading-failures') &&
        parseInt(localStorage.getItem('datastatpro-loading-failures') || '0') > 3,
        // Check for authentication-related loading issues
        localStorage.getItem('datastatpro-auth-loading-stuck') === 'true'
      ];

      return cacheCorruptionIndicators.some(indicator => indicator);
    } catch {
      return false;
    }
  }

  /**
   * Mark that a force refresh was performed
   */
  markForceRefresh(): void {
    localStorage.setItem('datastatpro-last-force-refresh', Date.now().toString());
  }
}

// Export singleton instance
export const cacheManager = new CacheManager({
  debugMode: import.meta.env.DEV
});

// Export utility functions
export const clearAllCaches = () => cacheManager.clearAllCaches();
export const getCacheInfo = () => cacheManager.getCacheInfo();
export const diagnoseCacheIssues = () => cacheManager.diagnoseCacheIssues();
export const recoverFromCacheIssues = () => cacheManager.recoverFromCacheIssues();
