/**
 * Categorical Ordering Helper Utilities
 * 
 * This file provides additional helper functions and type definitions
 * to support consistent categorical variable ordering across the application.
 * 
 * These utilities complement the core functions in dataUtilities.ts and
 * provide specialized helpers for common use cases.
 */

import { Dataset, Column, DataType, DataRow } from '../types';
import { 
  getOrderedCategories, 
  getOrderedCategoriesByColumnId,
  getOrderedCategoriesByColumnName 
} from './dataUtilities';

// Type definitions for categorical ordering
export interface OrderedCategoryData {
  category: string;
  value: number;
  order: number;
}

export interface CategoryOrderingOptions {
  respectCustomOrder: boolean;
  fallbackToAlphabetical: boolean;
  includeEmptyCategories: boolean;
}

export interface GroupedCategoricalData {
  [groupName: string]: {
    categories: string[];
    data: DataRow[];
    count: number;
  };
}

/**
 * Default options for category ordering operations
 */
export const DEFAULT_CATEGORY_OPTIONS: CategoryOrderingOptions = {
  respectCustomOrder: true,
  fallbackToAlphabetical: true,
  includeEmptyCategories: false
};

/**
 * Check if a column has custom category ordering defined
 */
export const hasCustomCategoryOrder = (column: Column): boolean => {
  return (column.type === DataType.CATEGORICAL || column.type === DataType.ORDINAL) &&
         !!column.categoryOrder && 
         column.categoryOrder.length > 0;
};

/**
 * Get category order information for a column
 */
export const getCategoryOrderInfo = (column: Column, dataset: Dataset) => {
  const hasCustomOrder = hasCustomCategoryOrder(column);
  const orderedCategories = getOrderedCategories(column, dataset.data);
  const totalCategories = orderedCategories.length;
  
  return {
    hasCustomOrder,
    orderedCategories,
    totalCategories,
    orderType: hasCustomOrder ? 'custom' : 'alphabetical'
  };
};

/**
 * Create ordered categorical data with position information
 */
export const createOrderedCategoryData = (
  columnId: string,
  dataset: Dataset,
  values?: Record<string, number>
): OrderedCategoryData[] => {
  const orderedCategories = getOrderedCategoriesByColumnId(columnId, dataset);
  
  return orderedCategories.map((category, index) => ({
    category,
    value: values?.[category] || 0,
    order: index
  }));
};

/**
 * Group data by categorical variable while maintaining order
 */
export const groupDataByCategoricalVariable = (
  columnId: string,
  dataset: Dataset,
  options: Partial<CategoryOrderingOptions> = {}
): GroupedCategoricalData => {
  const opts = { ...DEFAULT_CATEGORY_OPTIONS, ...options };
  const column = dataset.columns.find(col => col.id === columnId);
  
  if (!column) {
    throw new Error(`Column with ID ${columnId} not found`);
  }
  
  const orderedCategories = opts.respectCustomOrder 
    ? getOrderedCategoriesByColumnId(columnId, dataset)
    : [...new Set(dataset.data.map(row => String(row[column.name])))].sort();
  
  const grouped: GroupedCategoricalData = {};
  
  // Initialize groups in the correct order
  orderedCategories.forEach(category => {
    grouped[category] = {
      categories: [category],
      data: [],
      count: 0
    };
  });
  
  // Populate groups with data
  dataset.data.forEach(row => {
    const categoryValue = String(row[column.name]);
    if (grouped[categoryValue]) {
      grouped[categoryValue].data.push(row);
      grouped[categoryValue].count++;
    }
  });
  
  // Remove empty categories if not requested
  if (!opts.includeEmptyCategories) {
    Object.keys(grouped).forEach(category => {
      if (grouped[category].count === 0) {
        delete grouped[category];
      }
    });
  }
  
  return grouped;
};

/**
 * Validate that all categories in data exist in the defined order
 */
export const validateCategoryOrder = (
  columnId: string,
  dataset: Dataset
): { isValid: boolean; missingCategories: string[]; extraCategories: string[] } => {
  const column = dataset.columns.find(col => col.id === columnId);
  
  if (!column || !hasCustomCategoryOrder(column)) {
    return { isValid: true, missingCategories: [], extraCategories: [] };
  }
  
  const definedCategories = new Set(column.categoryOrder || []);
  const dataCategories = new Set(
    dataset.data
      .map(row => String(row[column.name]))
      .filter(val => val !== null && val !== undefined && val !== '')
  );
  
  const missingCategories = Array.from(dataCategories).filter(cat => !definedCategories.has(cat));
  const extraCategories = Array.from(definedCategories).filter(cat => !dataCategories.has(cat));
  
  return {
    isValid: missingCategories.length === 0,
    missingCategories,
    extraCategories
  };
};

/**
 * Create a mapping from category to its order position
 */
export const createCategoryOrderMap = (
  columnId: string,
  dataset: Dataset
): Map<string, number> => {
  const orderedCategories = getOrderedCategoriesByColumnId(columnId, dataset);
  const orderMap = new Map<string, number>();
  
  orderedCategories.forEach((category, index) => {
    orderMap.set(category, index);
  });
  
  return orderMap;
};

/**
 * Sort an array of objects by a categorical property using defined order
 */
export const sortByCategoricalProperty = <T>(
  items: T[],
  propertyAccessor: (item: T) => string,
  columnId: string,
  dataset: Dataset
): T[] => {
  const orderMap = createCategoryOrderMap(columnId, dataset);
  
  return items.sort((a, b) => {
    const valueA = propertyAccessor(a);
    const valueB = propertyAccessor(b);
    const orderA = orderMap.get(valueA) ?? Number.MAX_SAFE_INTEGER;
    const orderB = orderMap.get(valueB) ?? Number.MAX_SAFE_INTEGER;
    
    return orderA - orderB;
  });
};

/**
 * Get category statistics with ordering information
 */
export const getCategoryStatistics = (
  columnId: string,
  dataset: Dataset
) => {
  const column = dataset.columns.find(col => col.id === columnId);
  
  if (!column) {
    throw new Error(`Column with ID ${columnId} not found`);
  }
  
  const orderedCategories = getOrderedCategoriesByColumnId(columnId, dataset);
  const frequencies: Record<string, number> = {};
  const totalCount = dataset.data.length;
  
  // Initialize frequencies
  orderedCategories.forEach(category => {
    frequencies[category] = 0;
  });
  
  // Count frequencies
  dataset.data.forEach(row => {
    const value = String(row[column.name]);
    if (frequencies.hasOwnProperty(value)) {
      frequencies[value]++;
    }
  });
  
  // Calculate statistics
  const statistics = orderedCategories.map((category, index) => {
    const count = frequencies[category];
    const percentage = totalCount > 0 ? (count / totalCount) * 100 : 0;
    
    return {
      category,
      count,
      percentage,
      order: index,
      isCustomOrdered: hasCustomCategoryOrder(column)
    };
  });
  
  return {
    categories: orderedCategories,
    statistics,
    totalCount,
    hasCustomOrder: hasCustomCategoryOrder(column),
    orderType: hasCustomCategoryOrder(column) ? 'custom' : 'alphabetical'
  };
};

/**
 * Utility to ensure consistent category ordering in component state
 */
export const useCategoricalOrdering = (columnId: string, dataset: Dataset | null) => {
  if (!dataset || !columnId) {
    return {
      orderedCategories: [],
      hasCustomOrder: false,
      orderMap: new Map(),
      isValid: true
    };
  }
  
  const column = dataset.columns.find(col => col.id === columnId);
  
  if (!column) {
    return {
      orderedCategories: [],
      hasCustomOrder: false,
      orderMap: new Map(),
      isValid: false
    };
  }
  
  const orderedCategories = getOrderedCategoriesByColumnId(columnId, dataset);
  const hasCustomOrder = hasCustomCategoryOrder(column);
  const orderMap = createCategoryOrderMap(columnId, dataset);
  const validation = validateCategoryOrder(columnId, dataset);
  
  return {
    orderedCategories,
    hasCustomOrder,
    orderMap,
    isValid: validation.isValid,
    validation
  };
};

/**
 * Export all utilities for easy importing
 */
export {
  getOrderedCategories,
  getOrderedCategoriesByColumnId,
  getOrderedCategoriesByColumnName
} from './dataUtilities';
