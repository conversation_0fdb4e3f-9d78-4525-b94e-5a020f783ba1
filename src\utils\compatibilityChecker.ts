/**
 * Utility to detect browser compatibility issues
 * This script checks for features needed by the application
 * and reports any potential compatibility issues.
 */

interface CompatibilityCheckResult {
  feature: string;
  supported: boolean;
  critical: boolean;
  fallbackAvailable: boolean;
  message: string;
}

interface CompatibilityReport {
  browser: string;
  browserVersion: string;
  os: string;
  mobile: boolean;
  passedChecks: CompatibilityCheckResult[];
  failedChecks: CompatibilityCheckResult[];
  overallCompatible: boolean;
}

/**
 * Detects browser and version
 */
function detectBrowser(): { name: string; version: string } {
  const userAgent = navigator.userAgent;
  let browser = 'Unknown';
  let version = 'Unknown';
  
  // Detect Chrome
  if (/Chrome/.test(userAgent) && !/Chromium|Edge|Edg|OPR|Opera/.test(userAgent)) {
    browser = 'Chrome';
    version = userAgent.match(/Chrome\/(\d+\.\d+)/)?.[1] || 'Unknown';
  } 
  // Detect Firefox
  else if (/Firefox/.test(userAgent)) {
    browser = 'Firefox';
    version = userAgent.match(/Firefox\/(\d+\.\d+)/)?.[1] || 'Unknown';
  } 
  // Detect Safari (not Chrome)
  else if (/Safari/.test(userAgent) && !/Chrome/.test(userAgent)) {
    browser = 'Safari';
    version = userAgent.match(/Version\/(\d+\.\d+)/)?.[1] || 'Unknown';
  } 
  // Detect Edge
  else if (/Edg/.test(userAgent)) {
    browser = 'Edge';
    version = userAgent.match(/Edg\/(\d+\.\d+)/)?.[1] || 'Unknown';
  } 
  // Detect Opera
  else if (/OPR|Opera/.test(userAgent)) {
    browser = 'Opera';
    version = userAgent.match(/(?:OPR|Opera)\/(\d+\.\d+)/)?.[1] || 'Unknown';
  }
  
  return { name: browser, version };
}

/**
 * Detects operating system
 */
function detectOS(): string {
  const userAgent = navigator.userAgent;
  
  if (/Windows/.test(userAgent)) return 'Windows';
  if (/Macintosh|Mac OS X/.test(userAgent)) return 'macOS';
  if (/Linux/.test(userAgent)) return 'Linux';
  if (/Android/.test(userAgent)) return 'Android';
  if (/iPhone|iPad|iPod/.test(userAgent)) return 'iOS';
  
  return 'Unknown';
}

/**
 * Check if device is mobile
 */
function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         (window.innerWidth <= 800);
}

/**
 * Run all compatibility checks
 */
export function checkCompatibility(): CompatibilityReport {
  const { name: browserName, version: browserVersion } = detectBrowser();
  const os = detectOS();
  const mobile = isMobileDevice();
  
  const checks: CompatibilityCheckResult[] = [
    // Check for ES6 support
    {
      feature: 'ES6 Features',
      supported: typeof Promise !== 'undefined',
      critical: true,
      fallbackAvailable: false,
      message: 'Modern JavaScript features required for application functionality.'
    },
    
    // Check for Flexbox support
    {
      feature: 'CSS Flexbox',
      supported: CSS.supports('display', 'flex'),
      critical: true,
      fallbackAvailable: false,
      message: 'Flexbox is essential for the application layout.'
    },
    
    // Check for Grid support
    {
      feature: 'CSS Grid',
      supported: CSS.supports('display', 'grid'),
      critical: false,
      fallbackAvailable: true,
      message: 'Grid enhances layout on data visualization pages.'
    },
    
    // Check for localStorage support
    {
      feature: 'Local Storage',
      supported: !!window.localStorage,
      critical: false,
      fallbackAvailable: true,
      message: 'Used for saving user preferences and analysis settings.'
    },
    
    // Check for canvas support (for charts)
    {
      feature: 'Canvas API',
      supported: !!document.createElement('canvas').getContext,
      critical: true,
      fallbackAvailable: false,
      message: 'Required for chart rendering and data visualization.'
    },
    
    // Check for fetch API
    {
      feature: 'Fetch API',
      supported: typeof fetch !== 'undefined',
      critical: false,
      fallbackAvailable: true,
      message: 'Used for data import/export functionality.'
    },
    
    // Check for Web Workers
    {
      feature: 'Web Workers',
      supported: typeof Worker !== 'undefined',
      critical: false,
      fallbackAvailable: true,
      message: 'Improves performance for complex statistical calculations.'
    },
    
    // Check for IndexedDB (for offline functionality)
    {
      feature: 'IndexedDB',
      supported: !!window.indexedDB,
      critical: false,
      fallbackAvailable: true,
      message: 'Enables offline data storage for larger datasets.'
    },
    
    // Check for CSS Variables
    {
      feature: 'CSS Variables',
      supported: CSS.supports('--test', '0'),
      critical: false,
      fallbackAvailable: true,
      message: 'Used for theme customization and consistent styling.'
    },
    
    // Check for WebGL (for advanced visualizations)
    {
      feature: 'WebGL',
      supported: (() => {
        try {
          const canvas = document.createElement('canvas');
          return !!(window.WebGLRenderingContext && (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
        } catch (e) {
          return false;
        }
      })(),
      critical: false,
      fallbackAvailable: true,
      message: 'Enhances performance of complex data visualizations.'
    }
  ];
  
  const passedChecks: CompatibilityCheckResult[] = checks.filter(check => check.supported);
  const failedChecks: CompatibilityCheckResult[] = checks.filter(check => !check.supported);
  const criticalFailures = failedChecks.filter(check => check.critical);
  
  return {
    browser: browserName,
    browserVersion,
    os,
    mobile,
    passedChecks,
    failedChecks,
    overallCompatible: criticalFailures.length === 0
  };
}

/**
 * Display compatibility warnings if needed
 */
export function checkAndDisplayCompatibilityWarnings(): void {
  const report = checkCompatibility();
  const criticalFailures = report.failedChecks.filter(check => check.critical);
  
  // Log full report to console for debugging
  console.info('Browser Compatibility Report:', report);
  
  // If there are critical failures, show a warning
  if (criticalFailures.length > 0) {
    console.error('Critical browser compatibility issues detected:', criticalFailures);
    
    // Create a warning element
    const warningElement = document.createElement('div');
    warningElement.style.position = 'fixed';
    warningElement.style.top = '0';
    warningElement.style.left = '0';
    warningElement.style.right = '0';
    warningElement.style.padding = '10px 15px';
    warningElement.style.background = '#f44336';
    warningElement.style.color = 'white';
    warningElement.style.fontFamily = 'Arial, sans-serif';
    warningElement.style.fontSize = '14px';
    warningElement.style.textAlign = 'center';
    warningElement.style.zIndex = '9999';
    warningElement.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
    
    warningElement.textContent = `Your browser (${report.browser} ${report.browserVersion}) is missing features required for this application. Please update your browser or try another one like Chrome, Firefox, or Edge.`;
    
    // Add close button
    const closeButton = document.createElement('button');
    closeButton.textContent = '×';
    closeButton.style.background = 'none';
    closeButton.style.border = 'none';
    closeButton.style.color = 'white';
    closeButton.style.fontSize = '20px';
    closeButton.style.fontWeight = 'bold';
    closeButton.style.cursor = 'pointer';
    closeButton.style.position = 'absolute';
    closeButton.style.right = '10px';
    closeButton.style.top = '8px';
    closeButton.addEventListener('click', () => {
      document.body.removeChild(warningElement);
    });
    
    warningElement.appendChild(closeButton);
    document.body.appendChild(warningElement);
  } 
  // Show minor warnings in console only
  else if (report.failedChecks.length > 0) {
    console.warn('Some non-critical browser features are not supported:', report.failedChecks);
  }
  
  return report;
}

// API to get current device information
export function getDeviceInfo() {
  const { name: browserName, version: browserVersion } = detectBrowser();
  return {
    browser: browserName,
    browserVersion,
    os: detectOS(),
    mobile: isMobileDevice(),
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
    pixelRatio: window.devicePixelRatio || 1,
    touchScreen: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
    highContrast: window.matchMedia('(prefers-contrast: more)').matches,
    darkMode: window.matchMedia('(prefers-color-scheme: dark)').matches,
    connectionType: (navigator as any).connection ? (navigator as any).connection.effectiveType : 'unknown',
    language: navigator.language
  };
}

export default {
  checkCompatibility,
  checkAndDisplayCompatibilityWarnings,
  getDeviceInfo
};