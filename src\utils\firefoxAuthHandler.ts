/**
 * Firefox-specific authentication handler
 * Addresses Firefox PWA and service worker compatibility issues
 */

import { isFirefox, shouldUseFirefoxAuthFallback } from './compatibilityChecker';
import { productionFirefoxFix } from './productionFirefoxFix';

export interface FirefoxAuthOptions {
  disableServiceWorker?: boolean;
  clearCacheOnAuth?: boolean;
  useAlternativeStorage?: boolean;
  enableDebugLogging?: boolean;
  isProduction?: boolean;
}

class FirefoxAuthHandler {
  private options: FirefoxAuthOptions;
  private debugLog: string[] = [];

  constructor(options: FirefoxAuthOptions = {}) {
    // Detect if we're in production environment
    const isProduction = import.meta.env.PROD || window.location.protocol === 'https:';

    this.options = {
      disableServiceWorker: true, // Disable SW for Firefox by default
      clearCacheOnAuth: true,     // Clear cache before auth attempts
      useAlternativeStorage: true, // Use sessionStorage instead of localStorage for some data
      enableDebugLogging: true,   // Enable detailed logging
      isProduction,               // Auto-detect production environment
      ...options
    };

    if (this.options.enableDebugLogging) {
      console.log('🦊 Firefox Auth Handler initialized with options:', this.options);
      console.log('🦊 Environment detected:', isProduction ? 'Production' : 'Development');
    }
  }

  /**
   * Checks if Firefox-specific handling is needed
   */
  public shouldUseFirefoxHandling(): boolean {
    return shouldUseFirefoxAuthFallback();
  }

  /**
   * Prepares Firefox for authentication
   */
  public async prepareForAuthentication(): Promise<void> {
    if (!this.shouldUseFirefoxHandling()) return;

    this.log('🔧 Preparing Firefox for authentication...');

    try {
      // Set authentication in progress flag
      localStorage.setItem('datastatpro-auth-in-progress', 'true');

      // Clear problematic caches
      if (this.options.clearCacheOnAuth) {
        await this.clearProblematicCaches();
      }

      // Disable service worker if needed
      if (this.options.disableServiceWorker) {
        await this.disableServiceWorkerForAuth();
      }

      // Clear authentication-related storage
      this.clearAuthStorage();

      this.log('✅ Firefox preparation completed');
    } catch (error) {
      this.log(`❌ Firefox preparation failed: ${error}`);
      throw error;
    }
  }

  /**
   * Clears caches that may interfere with authentication
   */
  private async clearProblematicCaches(): Promise<void> {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();

        if (this.options.isProduction) {
          // In production, clear ALL caches for Firefox to prevent conflicts
          this.log('🦊 Production environment - clearing ALL caches for Firefox');

          for (const cacheName of cacheNames) {
            await caches.delete(cacheName);
            this.log(`🗑️ Cleared production cache: ${cacheName}`);
          }
        } else {
          // In development, clear specific caches that may cause issues
          const problematicCaches = cacheNames.filter(name =>
            name.includes('api-cache') ||
            name.includes('pages-cache') ||
            name.includes('workbox')
          );

          for (const cacheName of problematicCaches) {
            await caches.delete(cacheName);
            this.log(`🗑️ Cleared development cache: ${cacheName}`);
          }
        }
      }
    } catch (error) {
      this.log(`⚠️ Cache clearing failed: ${error}`);
    }
  }

  /**
   * Temporarily disables service worker for authentication
   */
  private async disableServiceWorkerForAuth(): Promise<void> {
    try {
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();

        if (this.options.isProduction) {
          // In production, completely unregister service workers for Firefox
          this.log('🦊 Production environment detected - unregistering service workers');

          for (const registration of registrations) {
            await registration.unregister();
            this.log(`🚫 Unregistered service worker: ${registration.scope}`);
          }

          // Mark that we unregistered for production
          localStorage.setItem('firefox-sw-unregistered-for-auth', 'true');
        } else {
          // In development, just mark for temporary disable
          for (const registration of registrations) {
            localStorage.setItem('firefox-sw-disabled-for-auth', 'true');
            this.log('🚫 Service worker marked for temporary disable');
          }
        }
      }
    } catch (error) {
      this.log(`⚠️ Service worker disable failed: ${error}`);
    }
  }

  /**
   * Clears authentication-related storage
   */
  private clearAuthStorage(): void {
    try {
      // Clear specific localStorage keys that may cause issues
      const authKeys = [
        'datastatpro-auth-loading-stuck',
        'datastatpro-cache-corruption-detected',
        'datastatpro-loading-failures'
      ];

      authKeys.forEach(key => {
        localStorage.removeItem(key);
        this.log(`🗑️ Cleared storage key: ${key}`);
      });

      // Clear session storage auth flags
      sessionStorage.removeItem('isGuest');
      sessionStorage.removeItem('showSignupSuccess');

    } catch (error) {
      this.log(`⚠️ Auth storage clearing failed: ${error}`);
    }
  }

  /**
   * Handles post-authentication cleanup
   */
  public async handlePostAuthentication(): Promise<void> {
    if (!this.shouldUseFirefoxHandling()) return;

    this.log('🔧 Handling post-authentication cleanup...');

    try {
      // Clear authentication in progress flag
      localStorage.removeItem('datastatpro-auth-in-progress');

      if (this.options.isProduction) {
        // In production, check if we unregistered service workers
        if (localStorage.getItem('firefox-sw-unregistered-for-auth') === 'true') {
          localStorage.removeItem('firefox-sw-unregistered-for-auth');
          this.log('✅ Production service worker cleanup completed');

          // Don't re-register service workers in production for Firefox
          // Let the user navigate normally without PWA interference
          this.log('🦊 Service workers will remain disabled for Firefox in production');
        }

        // Signal authentication success to other tabs
        productionFirefoxFix.signalAuthenticationSuccess();
      } else {
        // In development, re-enable service worker if it was disabled
        if (localStorage.getItem('firefox-sw-disabled-for-auth') === 'true') {
          localStorage.removeItem('firefox-sw-disabled-for-auth');
          this.log('✅ Development service worker re-enabled');
        }
      }

      // Clear debug logs after successful auth
      this.clearDebugLogs();

    } catch (error) {
      this.log(`❌ Post-auth cleanup failed: ${error}`);
    }
  }

  /**
   * Handles authentication errors specific to Firefox
   */
  public handleAuthenticationError(error: any): { handled: boolean; message?: string } {
    if (!this.shouldUseFirefoxHandling()) {
      return { handled: false };
    }

    this.log(`🚨 Handling Firefox auth error: ${error?.message || error}`);

    // Check for Firefox-specific error patterns
    const firefoxErrorPatterns = [
      /service worker/i,
      /cache/i,
      /network/i,
      /cors/i,
      /security/i,
      /blocked/i
    ];

    const errorMessage = error?.message || String(error);
    const isFirefoxSpecific = firefoxErrorPatterns.some(pattern => 
      pattern.test(errorMessage)
    );

    if (isFirefoxSpecific) {
      return {
        handled: true,
        message: 'Firefox detected a security or caching issue. Please try refreshing the page or clearing your browser cache.'
      };
    }

    return { handled: false };
  }

  /**
   * Gets Firefox-specific debugging information
   */
  public getDebugInfo(): object {
    return {
      isFirefox: isFirefox(),
      shouldUseFirefoxHandling: this.shouldUseFirefoxHandling(),
      options: this.options,
      debugLogs: this.debugLog.slice(-10), // Last 10 log entries
      serviceWorkerDisabled: localStorage.getItem('firefox-sw-disabled-for-auth') === 'true',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Logs debug information
   */
  private log(message: string): void {
    if (this.options.enableDebugLogging) {
      console.log(`[Firefox Auth Handler] ${message}`);
      this.debugLog.push(`${new Date().toISOString()}: ${message}`);
      
      // Keep only last 50 log entries
      if (this.debugLog.length > 50) {
        this.debugLog = this.debugLog.slice(-50);
      }
    }
  }

  /**
   * Clears debug logs
   */
  private clearDebugLogs(): void {
    this.debugLog = [];
  }

  /**
   * Forces a clean reload for Firefox
   */
  public async forceCleanReload(): Promise<void> {
    if (!this.shouldUseFirefoxHandling()) return;

    this.log('🔄 Forcing clean reload for Firefox...');

    try {
      // Clear all caches
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      }

      // Clear all storage
      localStorage.clear();
      sessionStorage.clear();

      // Unregister service workers
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        await Promise.all(registrations.map(reg => reg.unregister()));
      }

      // Force reload
      window.location.reload();
    } catch (error) {
      this.log(`❌ Clean reload failed: ${error}`);
      // Fallback to simple reload
      window.location.reload();
    }
  }
}

// Export singleton instance
export const firefoxAuthHandler = new FirefoxAuthHandler();

// Export class for custom instances
export { FirefoxAuthHandler };
