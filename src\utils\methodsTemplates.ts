// Statistical Methods Templates for Publication-Ready Methods Generation
import { ResultItem } from '../context/ResultsContext';

export interface MethodsTemplate {
  analysisType: string;
  template: (data: any) => string;
  priority: number; // Order in which methods should appear in final text
}

// Software citation template
export const SOFTWARE_CITATION = "All statistical analyses were performed using DataStatPro statistical software (DataStatPro, 2024).";

// Significance criteria template
export const SIGNIFICANCE_CRITERIA = "Statistical significance was set at p < 0.05 for all analyses unless otherwise specified.";

// Additional methodological statements
export const ASSUMPTION_CHECKING = "Relevant statistical assumptions were assessed prior to conducting each analysis, with appropriate alternative methods employed when assumptions were violated.";

export const MISSING_DATA_STATEMENT = "Missing data patterns were examined and handled appropriately based on the mechanism of missingness.";

export const EFFECT_SIZE_STATEMENT = "Effect sizes were calculated and reported alongside statistical significance to assess practical significance of findings.";

// Descriptive statistics templates
export const descriptiveTemplates: MethodsTemplate[] = [
  {
    analysisType: 'descriptive',
    priority: 1,
    template: (data: any) => {
      const hasQuantitative = data.results?.some((r: any) =>
        r.statistics?.mean !== undefined || r.column?.type === 'numeric'
      );
      const hasCategorical = data.results?.some((r: any) =>
        r.statistics?.frequencies !== undefined || r.column?.type === 'categorical'
      );
      const sampleSize = data.totalSampleSize;

      let text = "Descriptive statistics were calculated for all study variables";
      if (sampleSize) {
        text += ` (N = ${sampleSize})`;
      }
      text += ". ";

      if (hasQuantitative && hasCategorical) {
        text += "Continuous variables were summarized using means and standard deviations (or medians and interquartile ranges for non-normally distributed data), while categorical variables were described using frequencies and percentages.";
      } else if (hasQuantitative) {
        text += "Continuous variables were summarized using measures of central tendency and dispersion, including means and standard deviations for normally distributed variables, and medians and interquartile ranges for non-normally distributed variables.";
      } else if (hasCategorical) {
        text += "Categorical variables were described using frequencies and percentages, with 95% confidence intervals calculated for proportions where appropriate.";
      } else {
        text += "Appropriate measures of central tendency and variability were calculated based on the distribution and measurement level of each variable.";
      }

      // Add information about missing data handling if available
      if (data.missingDataHandling) {
        text += ` Missing data were handled using ${data.missingDataHandling}.`;
      }

      return text;
    }
  }
];

// Inferential statistics templates
export const inferentialTemplates: MethodsTemplate[] = [
  {
    analysisType: 'ttest',
    priority: 2,
    template: (data: any) => {
      const testType = data.testType || 'independent';
      const equalVariances = data.equalVariances;
      const alpha = data.alpha || 0.05;

      let text = "";

      switch (testType) {
        case 'independent':
          text = "Independent samples t-tests were conducted to compare means between groups.";
          if (equalVariances === false) {
            text += " Welch's t-test was used when the assumption of equal variances was violated.";
          } else if (equalVariances === true) {
            text += " Student's t-test was used assuming equal variances.";
          } else {
            text += " Levene's test was used to assess homogeneity of variance, with Welch's t-test applied when assumptions were violated.";
          }
          break;
        case 'paired':
          text = "Paired samples t-tests were performed to compare related measurements, accounting for the dependency between observations.";
          break;
        case 'one-sample':
          text = "One-sample t-tests were used to compare sample means against hypothesized population values.";
          break;
        default:
          text = "Student's t-tests were performed for group comparisons.";
      }

      if (alpha !== 0.05) {
        text += ` The significance level was set at α = ${alpha}.`;
      }

      return text;
    }
  },
  {
    analysisType: 'anova',
    priority: 3,
    template: (data: any) => {
      const anovaType = data.anovaType || data.component;
      const factors = data.factors || [];
      const sphericity = data.sphericity;
      const postHoc = data.postHoc;

      let text = "";

      if (anovaType?.includes('TwoWay') || factors.length > 1) {
        text = `${factors.length > 2 ? 'Factorial' : 'Two-way'} analysis of variance (ANOVA) was conducted to examine main effects and interactions between factors.`;
        if (factors.length > 0) {
          text += ` The factors included ${factors.join(', ')}.`;
        }
      } else if (anovaType?.includes('RepeatedMeasures')) {
        text = "Repeated measures ANOVA was performed to analyze within-subjects effects across time points or conditions.";
        if (sphericity === false) {
          text += " Greenhouse-Geisser corrections were applied when the assumption of sphericity was violated.";
        } else if (sphericity === true) {
          text += " The assumption of sphericity was met as assessed by Mauchly's test.";
        } else {
          text += " Sphericity was assessed using Mauchly's test, with Greenhouse-Geisser corrections applied when necessary.";
        }
      } else {
        text = "One-way analysis of variance (ANOVA) was used to compare means across multiple groups.";
      }

      // Add assumption checking
      text += " Assumptions of normality and homogeneity of variance were assessed prior to analysis.";

      if (postHoc) {
        text += ` Post-hoc comparisons were planned using ${postHoc} corrections.`;
      }

      return text;
    }
  },
  {
    analysisType: 'regression',
    priority: 4,
    template: (data: any) => {
      const regressionType = data.regressionType || 'linear';
      
      switch (regressionType) {
        case 'linear':
          return "Multiple linear regression analysis was performed to examine relationships between predictor variables and the continuous outcome variable.";
        case 'logistic':
          return "Logistic regression analysis was conducted to model the relationship between predictor variables and the binary outcome variable.";
        case 'cox':
          return "Cox proportional hazards regression was used to analyze time-to-event data and identify prognostic factors.";
        default:
          return "Regression analysis was performed to examine relationships between variables.";
      }
    }
  },
  {
    analysisType: 'correlation',
    priority: 5,
    template: (data: any) => {
      const method = data.method || 'pearson';
      
      switch (method) {
        case 'pearson':
          return "Pearson product-moment correlations were calculated to assess linear relationships between continuous variables.";
        case 'spearman':
          return "Spearman rank-order correlations were computed to evaluate monotonic relationships between variables.";
        case 'kendall':
          return "Kendall's tau correlations were used to assess associations between variables.";
        default:
          return "Correlation analyses were performed to examine relationships between variables.";
      }
    }
  },
  {
    analysisType: 'nonparametric',
    priority: 6,
    template: (data: any) => {
      const testType = data.testType || data.testName;
      
      if (testType?.includes('Mann-Whitney') || testType?.includes('mannWhitney')) {
        return "Mann-Whitney U tests were conducted as non-parametric alternatives to independent samples t-tests.";
      } else if (testType?.includes('Wilcoxon') || testType?.includes('wilcoxon')) {
        return "Wilcoxon signed-rank tests were performed as non-parametric alternatives to paired samples t-tests.";
      } else if (testType?.includes('Kruskal-Wallis') || testType?.includes('kruskalWallis')) {
        return "Kruskal-Wallis tests were used as non-parametric alternatives to one-way ANOVA.";
      } else if (testType?.includes('Friedman') || testType?.includes('friedman')) {
        return "Friedman tests were conducted as non-parametric alternatives to repeated measures ANOVA.";
      } else {
        return "Non-parametric statistical tests were employed when distributional assumptions were not met.";
      }
    }
  },
  {
    analysisType: 'chiSquare',
    priority: 7,
    template: (data: any) => {
      return "Chi-square tests of independence were performed to examine associations between categorical variables.";
    }
  },
  {
    analysisType: 'survival',
    priority: 8,
    template: (data: any) => {
      const analysisType = data.component || data.analysisType;
      
      if (analysisType?.includes('KaplanMeier')) {
        return "Kaplan-Meier survival analysis was conducted to estimate survival probabilities and generate survival curves.";
      } else if (analysisType?.includes('Cox')) {
        return "Cox proportional hazards regression was used to analyze time-to-event data and identify prognostic factors.";
      } else {
        return "Survival analysis methods were employed to analyze time-to-event data.";
      }
    }
  },
  {
    analysisType: 'meta',
    priority: 9,
    template: (data: any) => {
      const model = data.model || 'random';
      return `Meta-analysis was conducted using a ${model}-effects model to pool effect sizes across studies.`;
    }
  }
];

// Post-hoc and additional analysis templates
export const additionalTemplates: MethodsTemplate[] = [
  {
    analysisType: 'posthoc',
    priority: 10,
    template: (data: any) => {
      const method = data.method || 'tukey';

      switch (method) {
        case 'tukey':
          return "Post-hoc comparisons were conducted using Tukey's HSD test to control for multiple comparisons.";
        case 'bonferroni':
          return "Bonferroni corrections were applied for multiple post-hoc comparisons.";
        case 'scheffe':
          return "Scheffé's method was used for post-hoc multiple comparisons.";
        case 'holm':
          return "Holm's sequential Bonferroni method was used for post-hoc multiple comparisons.";
        case 'fdr':
          return "False discovery rate (FDR) corrections were applied for multiple comparisons.";
        default:
          return "Post-hoc tests were performed to identify specific group differences.";
      }
    }
  },
  {
    analysisType: 'normality',
    priority: 11,
    template: (data: any) => {
      const tests = [];
      if (data.shapiroWilk) tests.push("Shapiro-Wilk tests");
      if (data.kolmogorovSmirnov) tests.push("Kolmogorov-Smirnov tests");
      if (data.andersonDarling) tests.push("Anderson-Darling tests");

      const testString = tests.length > 0 ? tests.join(" and ") : "Shapiro-Wilk tests";
      return `Normality of distributions was assessed using ${testString} and visual inspection of Q-Q plots and histograms.`;
    }
  },
  {
    analysisType: 'reliability',
    priority: 12,
    template: (data: any) => {
      const alpha = data.cronbachAlpha;
      let text = "Internal consistency reliability was evaluated using Cronbach's alpha coefficients.";

      if (alpha !== undefined) {
        if (alpha >= 0.9) {
          text += " Excellent internal consistency was observed.";
        } else if (alpha >= 0.8) {
          text += " Good internal consistency was observed.";
        } else if (alpha >= 0.7) {
          text += " Acceptable internal consistency was observed.";
        } else {
          text += " Internal consistency was below acceptable levels.";
        }
      }

      return text;
    }
  },
  {
    analysisType: 'factor',
    priority: 13,
    template: (data: any) => {
      const method = data.method || 'principal';
      const rotation = data.rotation || 'varimax';

      let text = "";
      if (method === 'principal') {
        text = "Principal component analysis (PCA) was conducted to identify underlying factor structures.";
      } else if (method === 'maximum_likelihood') {
        text = "Maximum likelihood factor analysis was performed to extract latent factors.";
      } else {
        text = "Exploratory factor analysis was conducted to identify underlying factor structures.";
      }

      if (rotation !== 'none') {
        text += ` ${rotation.charAt(0).toUpperCase() + rotation.slice(1)} rotation was applied to improve interpretability of the factor solution.`;
      }

      return text;
    }
  },
  {
    analysisType: 'cluster',
    priority: 14,
    template: (data: any) => {
      const method = data.method || 'kmeans';

      switch (method) {
        case 'kmeans':
          return "K-means clustering analysis was performed to identify distinct groups within the data.";
        case 'hierarchical':
          return "Hierarchical cluster analysis was conducted using Ward's linkage method to identify natural groupings.";
        case 'dbscan':
          return "Density-based spatial clustering (DBSCAN) was applied to identify clusters of varying shapes and sizes.";
        default:
          return "Cluster analysis was performed to identify distinct groups within the data.";
      }
    }
  }
];

// Combine all templates
export const allMethodsTemplates = [
  ...descriptiveTemplates,
  ...inferentialTemplates,
  ...additionalTemplates
];

// Helper function to get template by analysis type
export const getTemplateByType = (analysisType: string): MethodsTemplate | undefined => {
  return allMethodsTemplates.find(template => template.analysisType === analysisType);
};

// Helper function to sort templates by priority
export const sortTemplatesByPriority = (templates: MethodsTemplate[]): MethodsTemplate[] => {
  return templates.sort((a, b) => a.priority - b.priority);
};
