// Methods Text Generation Engine for Statistical Methods Generator
import { ResultItem } from '../context/ResultsContext';
import {
  mapResultsToMappedAnalyses,
  groupMappedAnalysesByType,
  extractAnalysisDetails,
  MappedAnalysis
} from './analysisMapper';

// Cache for template results to improve performance
const templateCache = new Map<string, string>();

// Performance monitoring
interface PerformanceMetrics {
  startTime: number;
  mappingTime: number;
  generationTime: number;
  totalTime: number;
  resultCount: number;
}
import {
  sortTemplatesByPriority,
  SOFTWARE_CITATION,
  SIGNIFICANCE_CRITERIA,
  ASSUMPTION_CHECKING,
  EFFECT_SIZE_STATEMENT,
  MethodsTemplate
} from './methodsTemplates';

export interface GeneratedMethods {
  fullText: string;
  sections: {
    descriptive: string;
    inferential: string;
    additional: string;
    assumptions: string;
    software: string;
    significance: string;
  };
  analysesIncluded: {
    analysisType: string;
    count: number;
    titles: string[];
  }[];
  wordCount: number;
}

/**
 * Main function to generate statistical methods text from selected results
 */
export const generateMethodsText = (selectedResults: ResultItem[]): GeneratedMethods => {
  const metrics: PerformanceMetrics = {
    startTime: performance.now(),
    mappingTime: 0,
    generationTime: 0,
    totalTime: 0,
    resultCount: selectedResults?.length || 0
  };

  try {
    if (!selectedResults || selectedResults.length === 0) {
      return {
        fullText: "No analyses were selected for methods generation.",
        sections: {
          descriptive: "",
          inferential: "",
          additional: "",
          assumptions: "",
          software: "",
          significance: ""
        },
        analysesIncluded: [],
        wordCount: 0
      };
    }

    // Validate input data
    const validResults = selectedResults.filter(result => {
      if (!result || !result.id || !result.type) {
        console.warn('Invalid result item found and skipped:', result);
        return false;
      }
      return true;
    });

    if (validResults.length === 0) {
      throw new Error('No valid analyses found in selection');
    }

    // Map results to analysis types with error handling and performance tracking
    const mappingStart = performance.now();
    const mappedAnalyses = mapResultsToMappedAnalyses(validResults);
    metrics.mappingTime = performance.now() - mappingStart;

    if (mappedAnalyses.length === 0) {
      throw new Error('No analyses could be mapped to templates');
    }

    const groupedAnalyses = groupMappedAnalysesByType(mappedAnalyses);

    // Log performance warning for large datasets
    if (validResults.length > 50) {
      console.warn(`Processing large dataset with ${validResults.length} analyses. This may take a moment.`);
    }

    // Generate text sections with error handling and performance tracking
    const generationStart = performance.now();
    const sections = generateMethodsSections(groupedAnalyses);

    // Combine sections into full text
    const fullText = combineMethodsSections(sections);
    metrics.generationTime = performance.now() - generationStart;

    if (!fullText || fullText.trim().length === 0) {
      throw new Error('Failed to generate methods text');
    }

    // Generate analysis summary
    const analysesIncluded = generateAnalysesSummary(groupedAnalyses);

    // Count words
    const wordCount = countWords(fullText);

    // Calculate total time and log performance metrics
    metrics.totalTime = performance.now() - metrics.startTime;

    if (process.env.NODE_ENV === 'development') {
      console.log('Methods generation performance:', {
        totalTime: `${metrics.totalTime.toFixed(2)}ms`,
        mappingTime: `${metrics.mappingTime.toFixed(2)}ms`,
        generationTime: `${metrics.generationTime.toFixed(2)}ms`,
        resultCount: metrics.resultCount,
        avgTimePerResult: `${(metrics.totalTime / metrics.resultCount).toFixed(2)}ms`
      });
    }

    return {
      fullText,
      sections,
      analysesIncluded,
      wordCount
    };

  } catch (error) {
    console.error('Error in generateMethodsText:', error);

    // Return error state with helpful message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return {
      fullText: `Error generating methods text: ${errorMessage}. Please check your selected analyses and try again.`,
      sections: {
        descriptive: "",
        inferential: "",
        additional: "",
        assumptions: "",
        software: "",
        significance: ""
      },
      analysesIncluded: [],
      wordCount: 0
    };
  }
};

/**
 * Generates individual sections of the methods text
 */
const generateMethodsSections = (groupedAnalyses: Record<string, MappedAnalysis[]>) => {
  const sections = {
    descriptive: "",
    inferential: "",
    additional: "",
    assumptions: "",
    software: SOFTWARE_CITATION,
    significance: SIGNIFICANCE_CRITERIA
  };

  // Define section categories
  const descriptiveTypes = ['descriptive'];
  const inferentialTypes = ['ttest', 'anova', 'regression', 'correlation', 'nonparametric', 'chiSquare', 'survival', 'meta'];
  const additionalTypes = ['posthoc', 'normality', 'reliability'];

  // Generate descriptive section
  sections.descriptive = generateSectionText(groupedAnalyses, descriptiveTypes);
  
  // Generate inferential section
  sections.inferential = generateSectionText(groupedAnalyses, inferentialTypes);
  
  // Generate additional section
  sections.additional = generateSectionText(groupedAnalyses, additionalTypes);

  // Add assumption checking if we have inferential tests
  if (sections.inferential) {
    sections.assumptions = ASSUMPTION_CHECKING;
  }

  return sections;
};

/**
 * Generates text for a specific section based on analysis types
 */
const generateSectionText = (
  groupedAnalyses: Record<string, MappedAnalysis[]>,
  analysisTypes: string[]
): string => {
  try {
    const relevantAnalyses: MappedAnalysis[] = [];

    // Collect all relevant analyses
    analysisTypes.forEach(type => {
      if (groupedAnalyses[type]) {
        relevantAnalyses.push(...groupedAnalyses[type]);
      }
    });

    if (relevantAnalyses.length === 0) {
      return "";
    }

    // Sort by template priority
    const sortedTemplates = sortTemplatesByPriority(
      relevantAnalyses.map(analysis => analysis.template)
    );

    // Generate text for each unique template
    const uniqueTemplates = Array.from(
      new Map(sortedTemplates.map(template => [template.analysisType, template])).values()
    );

    const sentences: string[] = [];

    uniqueTemplates.forEach(template => {
      const analysesOfType = relevantAnalyses.filter(
        analysis => analysis.analysisType === template.analysisType
      );

      if (analysesOfType.length > 0) {
        // Create cache key for this template and data combination
        const representativeAnalysis = analysesOfType[0];
        const cacheKey = `${template.analysisType}_${JSON.stringify(representativeAnalysis.data)}`;

        let generatedText = templateCache.get(cacheKey);

        if (!generatedText) {
          // Generate text if not cached
          const analysisDetails = extractAnalysisDetails({
            id: representativeAnalysis.resultId,
            title: representativeAnalysis.title,
            type: representativeAnalysis.analysisType as any,
            component: representativeAnalysis.component,
            data: representativeAnalysis.data,
            timestamp: Date.now()
          });

          generatedText = template.template(analysisDetails);

          // Cache the result (limit cache size to prevent memory issues)
          if (templateCache.size < 100) {
            templateCache.set(cacheKey, generatedText);
          }
        }

        if (generatedText && generatedText.trim()) {
          sentences.push(generatedText.trim());
        }
      }
    });

    return sentences.join(' ');

  } catch (error) {
    console.error('Error in generateSectionText:', error);
    return "";
  }
};

/**
 * Combines all sections into a cohesive methods paragraph
 */
const combineMethodsSections = (sections: {
  descriptive: string;
  inferential: string;
  additional: string;
  assumptions: string;
  software: string;
  significance: string;
}): string => {
  const parts: string[] = [];
  
  // Add descriptive section
  if (sections.descriptive) {
    parts.push(sections.descriptive);
  }
  
  // Add inferential section
  if (sections.inferential) {
    parts.push(sections.inferential);
  }
  
  // Add additional analyses section
  if (sections.additional) {
    parts.push(sections.additional);
  }

  // Add assumption checking
  if (sections.assumptions) {
    parts.push(sections.assumptions);
  }

  // Add significance criteria
  if (sections.significance) {
    parts.push(sections.significance);
  }

  // Add software citation
  if (sections.software) {
    parts.push(sections.software);
  }
  
  return parts.join(' ');
};

/**
 * Generates summary of included analyses
 */
const generateAnalysesSummary = (groupedAnalyses: Record<string, MappedAnalysis[]>) => {
  return Object.entries(groupedAnalyses).map(([analysisType, analyses]) => ({
    analysisType,
    count: analyses.length,
    titles: analyses.map(analysis => analysis.title)
  }));
};

/**
 * Counts words in text
 */
const countWords = (text: string): number => {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
};

/**
 * Validates that the generated methods text meets quality standards
 */
export const validateMethodsText = (generatedMethods: GeneratedMethods): {
  isValid: boolean;
  warnings: string[];
  suggestions: string[];
} => {
  const warnings: string[] = [];
  const suggestions: string[] = [];
  
  // Check minimum word count
  if (generatedMethods.wordCount < 20) {
    warnings.push("Methods text is very short. Consider adding more detail about your analyses.");
  }
  
  // Check maximum word count
  if (generatedMethods.wordCount > 500) {
    warnings.push("Methods text is quite long. Consider condensing or splitting into subsections.");
  }
  
  // Check for missing sections
  if (!generatedMethods.sections.descriptive && !generatedMethods.sections.inferential) {
    warnings.push("No statistical analyses detected. Ensure you have selected completed analyses.");
  }
  
  // Check for software citation
  if (!generatedMethods.fullText.includes("DataStatPro")) {
    warnings.push("Software citation may be missing.");
  }
  
  // Check for significance criteria
  if (!generatedMethods.fullText.includes("p < 0.05")) {
    suggestions.push("Consider adding statistical significance criteria (e.g., p < 0.05).");
  }
  
  // Provide suggestions based on analysis types
  const analysisTypes = generatedMethods.analysesIncluded.map(a => a.analysisType);
  
  if (analysisTypes.includes('anova') && !analysisTypes.includes('posthoc')) {
    suggestions.push("Consider adding post-hoc test information if multiple comparisons were performed.");
  }
  
  if (analysisTypes.includes('regression') && !analysisTypes.includes('descriptive')) {
    suggestions.push("Consider including descriptive statistics for regression variables.");
  }
  
  return {
    isValid: warnings.length === 0,
    warnings,
    suggestions
  };
};

/**
 * Formats methods text for different output formats
 */
/**
 * Clears the template cache to free memory
 */
export const clearTemplateCache = (): void => {
  templateCache.clear();
  console.log('Template cache cleared');
};

/**
 * Gets cache statistics for monitoring
 */
export const getCacheStats = () => {
  return {
    size: templateCache.size,
    maxSize: 100,
    memoryUsage: `${(JSON.stringify([...templateCache.entries()]).length / 1024).toFixed(2)} KB`
  };
};

export const formatMethodsText = (
  generatedMethods: GeneratedMethods,
  format: 'paragraph' | 'structured' | 'html'
): string => {
  switch (format) {
    case 'paragraph':
      return generatedMethods.fullText;
      
    case 'structured':
      const sections = generatedMethods.sections;
      let structured = "";

      if (sections.descriptive) {
        structured += "Descriptive Analysis:\n" + sections.descriptive + "\n\n";
      }
      if (sections.inferential) {
        structured += "Inferential Analysis:\n" + sections.inferential + "\n\n";
      }
      if (sections.additional) {
        structured += "Additional Analyses:\n" + sections.additional + "\n\n";
      }
      if (sections.assumptions) {
        structured += "Statistical Assumptions:\n" + sections.assumptions + "\n\n";
      }
      if (sections.significance) {
        structured += "Statistical Criteria:\n" + sections.significance + "\n\n";
      }
      if (sections.software) {
        structured += "Software:\n" + sections.software + "\n";
      }

      return structured.trim();
      
    case 'html':
      return `<div class="statistical-methods">
        <h3>Statistical Methods</h3>
        <p>${generatedMethods.fullText}</p>
      </div>`;
      
    default:
      return generatedMethods.fullText;
  }
};
