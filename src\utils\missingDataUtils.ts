import { DataValue, DataRow, Column, Dataset, MissingValueInfo, CleanedDataResult } from '../types';

/**
 * Centralized missing data handling utilities
 * Provides consistent missing value detection and processing across all analysis components
 */

/**
 * Default missing value codes that are always considered missing
 */
export const DEFAULT_MISSING_CODES = ['', null, undefined];

/**
 * Checks if a value is considered missing based on column-specific missing value codes
 * @param value The value to check
 * @param column The column definition containing missing value codes
 * @returns MissingValueInfo object with details about the missing status
 */
export const isMissingValue = (value: DataValue, column: Column): MissingValueInfo => {
  // Always consider null and undefined as missing
  if (value === null || value === undefined) {
    return {
      originalValue: value,
      isMissing: true,
      matchedCode: value === null ? 'null' : 'undefined'
    };
  }

  // Convert value to string for comparison
  const stringValue = String(value);

  // Check for empty string (always missing)
  if (stringValue.trim() === '') {
    return {
      originalValue: value,
      isMissing: true,
      matchedCode: 'empty string'
    };
  }

  // Check user-defined missing value codes
  if (column.missingValueCodes && column.missingValueCodes.length > 0) {
    for (const code of column.missingValueCodes) {
      // Exact match comparison (case-sensitive)
      if (stringValue === code) {
        return {
          originalValue: value,
          isMissing: true,
          matchedCode: code
        };
      }
    }
  }

  return {
    originalValue: value,
    isMissing: false
  };
};

/**
 * Cleans a dataset by converting user-defined missing values to null
 * @param dataset The dataset to clean
 * @returns CleanedDataResult with cleaned data and missing value summary
 */
export const cleanDataset = (dataset: Dataset): CleanedDataResult => {
  const cleanedData: DataRow[] = [];
  const missingByColumn: Record<string, number> = {};
  const missingByRow: number[] = new Array(dataset.data.length).fill(0);
  let totalMissing = 0;

  // Initialize column counters
  dataset.columns.forEach(column => {
    missingByColumn[column.name] = 0;
  });

  // Process each row
  dataset.data.forEach((row, rowIndex) => {
    const cleanedRow: DataRow = {};

    dataset.columns.forEach(column => {
      const originalValue = row[column.name];
      const missingInfo = isMissingValue(originalValue, column);

      if (missingInfo.isMissing) {
        cleanedRow[column.name] = null;
        missingByColumn[column.name]++;
        missingByRow[rowIndex]++;
        totalMissing++;
      } else {
        cleanedRow[column.name] = originalValue;
      }
    });

    cleanedData.push(cleanedRow);
  });

  return {
    cleanedData,
    missingValueSummary: {
      totalMissing,
      missingByColumn,
      missingByRow
    }
  };
};

/**
 * Extracts numeric values from a column, respecting user-defined missing value codes
 * @param data Array of data rows
 * @param column Column definition
 * @returns Array of valid numeric values with missing values excluded
 */
export const extractNumericValuesWithMissingCodes = (
  data: DataRow[],
  column: Column
): number[] => {
  const numericValues: number[] = [];

  data.forEach(row => {
    const value = row[column.name];
    const missingInfo = isMissingValue(value, column);

    if (!missingInfo.isMissing) {
      // Try to convert to number
      if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
        numericValues.push(value);
      } else if (typeof value === 'string') {
        const parsed = Number(value);
        if (!isNaN(parsed) && isFinite(parsed)) {
          numericValues.push(parsed);
        }
      }
    }
  });

  return numericValues;
};

/**
 * Extracts categorical values from a column, respecting user-defined missing value codes
 * @param data Array of data rows
 * @param column Column definition
 * @returns Array of valid categorical values with missing values excluded
 */
export const extractCategoricalValuesWithMissingCodes = (
  data: DataRow[],
  column: Column
): string[] => {
  const categoricalValues: string[] = [];

  data.forEach(row => {
    const value = row[column.name];
    const missingInfo = isMissingValue(value, column);

    if (!missingInfo.isMissing) {
      categoricalValues.push(String(value));
    }
  });

  return categoricalValues;
};

/**
 * Gets valid sample size for a column after excluding missing values
 * @param data Array of data rows
 * @param column Column definition
 * @returns Number of valid (non-missing) values
 */
export const getValidSampleSize = (data: DataRow[], column: Column): number => {
  let validCount = 0;

  data.forEach(row => {
    const value = row[column.name];
    const missingInfo = isMissingValue(value, column);

    if (!missingInfo.isMissing) {
      validCount++;
    }
  });

  return validCount;
};

/**
 * Gets missing value summary for a specific column
 * @param data Array of data rows
 * @param column Column definition
 * @returns Object with missing value statistics
 */
export const getColumnMissingSummary = (
  data: DataRow[],
  column: Column
): {
  totalValues: number;
  missingCount: number;
  missingPercentage: number;
  validCount: number;
  missingValueBreakdown: Record<string, number>;
} => {
  const totalValues = data.length;
  let missingCount = 0;
  const missingValueBreakdown: Record<string, number> = {};

  data.forEach(row => {
    const value = row[column.name];
    const missingInfo = isMissingValue(value, column);

    if (missingInfo.isMissing) {
      missingCount++;
      const code = missingInfo.matchedCode || 'unknown';
      missingValueBreakdown[code] = (missingValueBreakdown[code] || 0) + 1;
    }
  });

  return {
    totalValues,
    missingCount,
    missingPercentage: totalValues > 0 ? (missingCount / totalValues) * 100 : 0,
    validCount: totalValues - missingCount,
    missingValueBreakdown
  };
};

/**
 * Validates missing value codes for a column
 * @param codes Array of missing value codes to validate
 * @param columnType The data type of the column
 * @returns Object with validation results
 */
export const validateMissingValueCodes = (
  codes: string[],
  columnType: string
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check for duplicates
  const uniqueCodes = new Set(codes);
  if (uniqueCodes.size !== codes.length) {
    errors.push('Duplicate missing value codes are not allowed');
  }

  // Check for empty codes (except empty string which is valid)
  codes.forEach((code, index) => {
    if (code === null || code === undefined) {
      errors.push(`Missing value code at position ${index + 1} cannot be null or undefined`);
    }
  });

  // Warn about potentially problematic codes
  codes.forEach(code => {
    if (code === '0' && columnType === 'NUMERIC') {
      warnings.push('Using "0" as a missing value code for numeric data may cause confusion');
    }
    if (code.toLowerCase() === 'false' && columnType === 'BOOLEAN') {
      warnings.push('Using "false" as a missing value code for boolean data may cause confusion');
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Suggests common missing value codes based on data analysis
 * @param data Array of data rows
 * @param columnName Name of the column to analyze
 * @returns Array of suggested missing value codes
 */
export const suggestMissingValueCodes = (
  data: DataRow[],
  columnName: string
): string[] => {
  const valueCounts: Record<string, number> = {};
  const totalRows = data.length;

  // Count all unique values
  data.forEach(row => {
    const value = row[columnName];
    if (value !== null && value !== undefined) {
      const stringValue = String(value);
      valueCounts[stringValue] = (valueCounts[stringValue] || 0) + 1;
    }
  });

  const suggestions: string[] = [];
  const commonMissingPatterns = [
    'na', 'n/a', 'n.a.', 'NA', 'N/A', 'N.A.',
    'null', 'NULL', 'nil', 'NIL',
    'missing', 'MISSING', 'miss', 'MISS',
    '-', '--', '---', '.',
    '999', '-999', '9999', '-9999',
    'unknown', 'UNKNOWN', 'unk', 'UNK'
  ];

  // Check which common patterns exist in the data
  commonMissingPatterns.forEach(pattern => {
    if (valueCounts[pattern]) {
      const frequency = valueCounts[pattern] / totalRows;
      // Suggest if it appears in less than 50% of rows (likely missing indicator)
      if (frequency < 0.5) {
        suggestions.push(pattern);
      }
    }
  });

  return suggestions;
};
