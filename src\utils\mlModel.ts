import * as tf from '@tensorflow/tfjs';
import { trainingData, statisticalTests } from '../data/statisticalTestTrainingData';
import { buildVocabulary, calculateIdf, calculateTfIdf } from './nlpUtils';

let vocabulary: string[] = [];
let idfMap: Map<string, number>;
let model: tf.Sequential | null = null;

export async function initializeModel() {
  if (model) {
    console.log("Model already initialized.");
    return model;
  }

  console.log("Initializing model...");
  vocabulary = buildVocabulary(trainingData);
  idfMap = calculateIdf(trainingData, vocabulary);

  const xs = tf.stack(trainingData.map(d => calculateTfIdf(d.query, vocabulary, idfMap)));
  const ys = tf.oneHot(trainingData.map(d => statisticalTests.indexOf(d.test)), statisticalTests.length);

  model = tf.sequential();
  model.add(tf.layers.dense({ units: 128, activation: 'relu', inputShape: [vocabulary.length] }));
  model.add(tf.layers.dense({ units: 64, activation: 'relu' })); // Added another hidden layer
  model.add(tf.layers.dense({ units: statisticalTests.length, activation: 'softmax' }));

  model.compile({
    optimizer: tf.train.adam(0.001), // Corrected learning rate syntax
    loss: 'categoricalCrossentropy',
    metrics: ['accuracy'],
  });

  await model.fit(xs, ys, {
    epochs: 100, // Increased epochs
    batchSize: 32,
    shuffle: true,
    callbacks: {
      onEpochEnd: (epoch, logs) => {
        console.log(`Epoch ${epoch + 1}: loss = ${logs?.loss.toFixed(4)}, accuracy = ${logs?.acc.toFixed(4)}`);
      }
    }
  });

  console.log("Model initialized and trained.");
  return model;
}

export function predictTest(query: string, topN: number = 3): { test: string; confidence: number }[] | null {
  if (!model) {
    console.error("Model not initialized. Call initializeModel() first.");
    return null;
  }

  const inputVector = calculateTfIdf(query, vocabulary, idfMap);
  const prediction = model.predict(inputVector.expandDims(0)) as tf.Tensor;
  const probabilities = prediction.dataSync();

  const results = Array.from(probabilities)
    .map((confidence, index) => ({
      test: statisticalTests[index],
      confidence: confidence,
    }))
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, topN);

  return results;
}

// Export for testing or direct access if needed
export { vocabulary, idfMap };
