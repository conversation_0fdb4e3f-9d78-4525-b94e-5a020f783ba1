import * as tf from '@tensorflow/tfjs';

// A very basic list of English stop words
const stopWords = new Set([
  "a", "an", "and", "are", "as", "at", "be", "but", "by", "for", "if", "in",
  "into", "is", "it", "no", "not", "of", "on", "or", "such", "that", "the",
  "their", "then", "there", "these", "they", "this", "to", "was", "will",
  "with", "how", "what", "when", "where", "which", "who", "whom", "why",
  "can", "could", "would", "should", "may", "might", "must", "have", "has",
  "had", "do", "does", "did", "am", "is", "are", "was", "were", "be", "been",
  "being", "from", "here", "there", "when", "where", "why", "how", "all",
  "any", "both", "each", "few", "more", "most", "other", "some", "such",
  "no", "nor", "not", "only", "own", "same", "so", "than", "too", "very",
  "s", "t", "can", "will", "just", "don", "should", "now", "d", "ll", "m",
  "o", "re", "ve", "y", "ain", "aren", "couldn", "didn", "doesn", "hadn",
  "hasn", "haven", "isn", "ma", "mightn", "mustn", "needn", "shan", "shouldn",
  "wasn", "weren", "won", "wouldn", "about", "above", "after", "again",
  "against", "all", "am", "an", "and", "any", "are", "aren't", "as", "at",
  "be", "because", "been", "before", "being", "below", "between", "both",
  "but", "by", "can't", "cannot", "could", "couldn't", "did", "didn't", "do",
  "does", "doesn't", "doing", "don't", "down", "during", "each", "few", "for",
  "from", "further", "had", "hadn't", "has", "hasn't", "have", "haven't",
  "having", "he", "he'd", "he'll", "he's", "her", "here", "here's", "hers",
  "herself", "him", "himself", "his", "how", "how's", "i", "i'd", "i'll",
  "i'm", "i've", "if", "in", "into", "is", "isn't", "it", "it's", "its",
  "itself", "let's", "me", "more", "most", "mustn't", "my", "myself", "no",
  "nor", "not", "of", "off", "on", "once", "only", "or", "other", "ought",
  "our", "ours", "ourselves", "out", "over", "own", "same", "shan't", "she",
  "she'd", "she'll", "she's", "should", "shouldn't", "so", "some", "such",
  "than", "that", "that's", "the", "their", "theirs", "them", "themselves",
  "then", "there", "there's", "these", "they", "they'd", "they'll", "they're",
  "they've", "this", "those", "through", "to", "too", "under", "until", "up",
  "very", "was", "wasn't", "we", "we'd", "we'll", "we're", "we've", "were",
  "weren't", "what", "what's", "when", "when's", "where", "where's", "which",
  "while", "who", "who's", "whom", "why", "why's", "with", "won't", "would",
  "wouldn't", "you", "you'd", "you'll", "you're", "you've", "your", "yours",
  "yourself", "yourselves"
]);

export function tokenize(text: string): string[] {
  return text.toLowerCase().split(/\W+/).filter(word => word.length > 0);
}

export function removeStopWords(tokens: string[]): string[] {
  return tokens.filter(token => !stopWords.has(token));
}

// A very simple stemming function (Porter Stemmer is more complex)
export function stem(word: string): string {
  if (word.length > 2) {
    if (word.endsWith('es')) return word.slice(0, -2);
    if (word.endsWith('s')) return word.slice(0, -1);
    if (word.endsWith('ing')) return word.slice(0, -3);
    if (word.endsWith('ed')) return word.slice(0, -2);
  }
  return word;
}

export function preprocessText(text: string): string[] {
  let tokens = tokenize(text);
  tokens = removeStopWords(tokens);
  tokens = tokens.map(token => stem(token));
  return tokens;
}

export function buildVocabulary(data: { query: string; test: string }[]): string[] {
  const vocabulary = new Set<string>();
  data.forEach(item => {
    const preprocessed = preprocessText(item.query);
    preprocessed.forEach(word => vocabulary.add(word));
  });
  return Array.from(vocabulary).sort();
}

export function calculateTf(tokens: string[], vocabulary: string[]): number[] {
  const tfVector = new Array(vocabulary.length).fill(0);
  const tokenCounts: { [key: string]: number } = {};
  tokens.forEach(token => {
    tokenCounts[token] = (tokenCounts[token] || 0) + 1;
  });

  vocabulary.forEach((word, index) => {
    if (tokenCounts[word]) {
      tfVector[index] = tokenCounts[word] / tokens.length;
    }
  });
  return tfVector;
}

export function calculateIdf(data: { query: string; test: string }[], vocabulary: string[]): Map<string, number> {
  const idfMap = new Map<string, number>();
  const numDocuments = data.length;

  vocabulary.forEach(word => {
    let docCount = 0;
    data.forEach(item => {
      const preprocessed = preprocessText(item.query);
      if (new Set(preprocessed).has(word)) {
        docCount++;
      }
    });
    idfMap.set(word, Math.log(numDocuments / (docCount + 1))); // Add 1 to avoid division by zero
  });
  return idfMap;
}

export function calculateTfIdf(text: string, vocabulary: string[], idfMap: Map<string, number>): tf.Tensor1D {
  const preprocessed = preprocessText(text);
  const tfVector = calculateTf(preprocessed, vocabulary);
  const tfidfVector = tfVector.map((tfVal, index) => {
    const word = vocabulary[index];
    const idfVal = idfMap.get(word) || 0;
    return tfVal * idfVal;
  });
  return tf.tensor1d(tfidfVector);
}
