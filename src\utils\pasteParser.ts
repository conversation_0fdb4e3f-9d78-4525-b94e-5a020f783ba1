import { DataType } from '../types';

export interface ParsedData {
  headers: string[];
  rows: string[][];
}

export function parsePastedData(text: string): ParsedData {
  // Remove any trailing newlines
  text = text.trim();
  
  // Split into lines
  const lines = text.split(/\r?\n/);
  
  if (lines.length === 0) {
    return { headers: [], rows: [] };
  }
  
  // Detect delimiter (tab for Excel, comma for CSV)
  const firstLine = lines[0];
  const tabCount = (firstLine.match(/\t/g) || []).length;
  const commaCount = (firstLine.match(/,/g) || []).length;
  const delimiter = tabCount > commaCount ? '\t' : ',';
  
  // Parse lines
  const parsedLines = lines.map(line => {
    // Simple parsing - for more robust CSV parsing, consider using a library
    return line.split(delimiter).map(cell => cell.trim());
  });
  
  // Assume first row contains headers if it has non-numeric values
  const firstRow = parsedLines[0];
  const hasHeaders = firstRow.some(cell => isNaN(Number(cell)) && cell !== '');
  
  if (hasHeaders) {
    return {
      headers: firstRow,
      rows: parsedLines.slice(1)
    };
  } else {
    // Generate generic headers if none provided
    const headers = firstRow.map((_, index) => `Column${index + 1}`);
    return {
      headers,
      rows: parsedLines
    };
  }
}

export function detectDataTypes(rows: string[][]): DataType[] {
  if (rows.length === 0) return [];
  
  const columnCount = rows[0].length;
  const types: DataType[] = [];
  
  for (let col = 0; col < columnCount; col++) {
    const values = rows.map(row => row[col]).filter(v => v !== '' && v !== null);
    
    if (values.length === 0) {
      types.push(DataType.TEXT);
      continue;
    }
    
    // Check if values are strictly 0 or 1 (should be numeric)
    const zeroOneValues = ['0', '1'];
    if (values.length > 0 && values.every(v => zeroOneValues.includes(v))) {
       types.push(DataType.NUMERIC);
       continue;
    }

    // Check if all values are boolean
    const booleanValues = ['true', 'false', '1', '0', 'yes', 'no'];
    if (values.every(v => booleanValues.includes(v.toLowerCase()))) {
      types.push(DataType.BOOLEAN);
      continue;
    }
    
    // Check if all values are numeric
    // Check if all values are dates (more robust check)
    if (values.every(v => {
      // Check if Date.parse is valid AND if the string contains common date separators or is a recognized date format
      const isParsableDate = !isNaN(Date.parse(v));
      const hasDateSeparators = /[/.-]/.test(v); // Check for /, -, or .
      const isLikelyDateFormat = isParsableDate && (hasDateSeparators || /^\d{4}-\d{2}-\d{2}$/.test(v) || /^\d{2}\/\d{2}\/\d{4}$/.test(v)); // Add more format checks if needed

      return isLikelyDateFormat;
    })) {
      types.push(DataType.DATE);
      continue;
    }

    // Check if all values are numeric
    if (values.every(v => !isNaN(Number(v)))) {
      types.push(DataType.NUMERIC);
      continue;
    }
    
    // Check if the number of unique values is below a threshold for categorical
    const uniqueValues = new Set(values.map(v => v.toLowerCase()));
    // Define a threshold for categorical variables (e.g., less than 20 unique values)
    const CATEGORICAL_THRESHOLD = 20; 

    if (uniqueValues.size > 0 && uniqueValues.size <= CATEGORICAL_THRESHOLD) {
      types.push(DataType.CATEGORICAL);
      continue;
    }

    // Default to text
    types.push(DataType.TEXT);
  }
  
  return types;
}
