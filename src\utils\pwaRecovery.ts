/**
 * PWA Recovery Utilities for DataStatPro
 * Handles critical PWA loading failures and cache corruption recovery
 */

import { cacheManager, recoverFromCacheIssues } from './cacheManager';

export interface PWARecoveryOptions {
  maxRetries?: number;
  retryDelay?: number;
  forceReload?: boolean;
  clearAllData?: boolean;
}

export interface PWARecoveryResult {
  success: boolean;
  action: string;
  message: string;
  requiresReload: boolean;
}

class PWARecovery {
  private readonly RECOVERY_KEY = 'datastatpro-recovery-attempts';
  private readonly MAX_RECOVERY_ATTEMPTS = 3;
  private readonly RECOVERY_COOLDOWN = 5 * 60 * 1000; // 5 minutes

  /**
   * Perform comprehensive PWA recovery
   */
  async performRecovery(options: PWARecoveryOptions = {}): Promise<PWARecoveryResult> {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      forceReload = false,
      clearAllData = false
    } = options;

    try {
      // Check if we're in a recovery loop
      if (this.isInRecoveryLoop()) {
        return {
          success: false,
          action: 'recovery_loop_detected',
          message: 'Recovery loop detected, manual intervention required',
          requiresReload: false
        };
      }

      // Mark recovery attempt
      this.markRecoveryAttempt();

      // Step 1: Clear corrupted caches
      console.log('🔧 Step 1: Clearing corrupted caches...');
      const cacheCleared = await cacheManager.clearAllCaches();
      
      if (!cacheCleared) {
        console.warn('Failed to clear caches, attempting alternative recovery');
      }

      // Step 2: Clear problematic localStorage entries
      console.log('🔧 Step 2: Clearing problematic storage...');
      this.clearProblematicStorage(clearAllData);

      // Step 3: Unregister and re-register service worker
      console.log('🔧 Step 3: Refreshing service worker...');
      await this.refreshServiceWorker();

      // Step 4: Validate recovery
      console.log('🔧 Step 4: Validating recovery...');
      const isRecovered = await this.validateRecovery();

      if (isRecovered || forceReload) {
        // Clear recovery tracking on success
        localStorage.removeItem(this.RECOVERY_KEY);
        localStorage.removeItem('datastatpro-cache-corruption-detected');
        localStorage.removeItem('datastatpro-loading-failures');

        return {
          success: true,
          action: 'recovery_complete',
          message: 'PWA recovery completed successfully',
          requiresReload: true
        };
      } else {
        return {
          success: false,
          action: 'recovery_failed',
          message: 'PWA recovery failed, manual cache clearing may be required',
          requiresReload: false
        };
      }
    } catch (error) {
      console.error('PWA recovery failed:', error);
      return {
        success: false,
        action: 'recovery_error',
        message: `Recovery error: ${error.message}`,
        requiresReload: false
      };
    }
  }

  /**
   * Check if we're in a recovery loop
   */
  private isInRecoveryLoop(): boolean {
    try {
      const recoveryData = localStorage.getItem(this.RECOVERY_KEY);
      if (!recoveryData) return false;

      const { attempts, lastAttempt } = JSON.parse(recoveryData);
      const now = Date.now();

      // Check if too many attempts in short time
      if (attempts >= this.MAX_RECOVERY_ATTEMPTS && 
          (now - lastAttempt) < this.RECOVERY_COOLDOWN) {
        return true;
      }

      return false;
    } catch {
      return false;
    }
  }

  /**
   * Mark a recovery attempt
   */
  private markRecoveryAttempt(): void {
    try {
      const now = Date.now();
      const existing = localStorage.getItem(this.RECOVERY_KEY);
      
      let recoveryData = { attempts: 1, lastAttempt: now };
      
      if (existing) {
        const parsed = JSON.parse(existing);
        // Reset if last attempt was more than cooldown period ago
        if ((now - parsed.lastAttempt) > this.RECOVERY_COOLDOWN) {
          recoveryData = { attempts: 1, lastAttempt: now };
        } else {
          recoveryData = { attempts: parsed.attempts + 1, lastAttempt: now };
        }
      }

      localStorage.setItem(this.RECOVERY_KEY, JSON.stringify(recoveryData));
    } catch (error) {
      console.warn('Failed to mark recovery attempt:', error);
    }
  }

  /**
   * Clear problematic localStorage entries
   */
  private clearProblematicStorage(clearAll: boolean = false): void {
    try {
      const keysToRemove = [
        'datastatpro-cache-metadata',
        'datastatpro-cache-version',
        'datastatpro-last-force-refresh',
        'datastatpro-cache-corruption-detected',
        'datastatpro-loading-failures'
      ];

      if (clearAll) {
        // Clear all DataStatPro related storage
        const allKeys = Object.keys(localStorage);
        keysToRemove.push(...allKeys.filter(key => key.startsWith('datastatpro-')));
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });

      console.log(`Cleared ${keysToRemove.length} storage entries`);
    } catch (error) {
      console.warn('Failed to clear storage:', error);
    }
  }

  /**
   * Refresh service worker registration
   */
  private async refreshServiceWorker(): Promise<void> {
    try {
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        
        // Unregister all service workers
        for (const registration of registrations) {
          await registration.unregister();
          console.log('Unregistered service worker:', registration.scope);
        }

        // Small delay to ensure cleanup
        await new Promise(resolve => setTimeout(resolve, 500));

        // The service worker will be re-registered automatically by the PWA plugin
        console.log('Service worker refresh completed');
      }
    } catch (error) {
      console.warn('Service worker refresh failed:', error);
    }
  }

  /**
   * Validate that recovery was successful
   */
  private async validateRecovery(): Promise<boolean> {
    try {
      // Check if caches are accessible
      if ('caches' in window) {
        await caches.keys();
      }

      // Check if service worker is available
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.getRegistration();
        // It's okay if no registration exists yet, it will be created
      }

      // Check if localStorage is working
      const testKey = 'datastatpro-recovery-test';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);

      return true;
    } catch (error) {
      console.warn('Recovery validation failed:', error);
      return false;
    }
  }

  /**
   * Emergency recovery - clears everything and forces reload
   */
  async emergencyRecovery(): Promise<void> {
    try {
      console.log('🚨 Performing emergency PWA recovery...');
      
      // Clear all caches
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      }

      // Clear all localStorage
      localStorage.clear();

      // Clear sessionStorage
      sessionStorage.clear();

      // Unregister all service workers
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        await Promise.all(registrations.map(reg => reg.unregister()));
      }

      // Force reload
      window.location.reload();
    } catch (error) {
      console.error('Emergency recovery failed:', error);
      // Last resort - force reload anyway
      window.location.reload();
    }
  }
}

// Export singleton instance
export const pwaRecovery = new PWARecovery();

// Export utility functions
export const performPWARecovery = (options?: PWARecoveryOptions) => 
  pwaRecovery.performRecovery(options);

export const emergencyPWARecovery = () => 
  pwaRecovery.emergencyRecovery();
