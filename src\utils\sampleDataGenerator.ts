import { v4 as uuidv4 } from 'uuid';
import { DataType, VariableRole } from '../types';

// Simple seeded random number generator (Linear Congruential Generator)
// Based on: https://stackoverflow.com/questions/521295/seeding-the-random-number-generator-in-javascript
function mulberry32(seed: number) {
  return function() {
    let t = seed += 0x6D2B79F5;
    t = Math.imul(t ^ t >>> 15, t | 1);
    t ^= t + Math.imul(t ^ t >>> 7, t | 61);
    return ((t ^ t >>> 14) >>> 0) / **********;
  }
}

// Function to generate sample data for testing correlation and regression features
export const generateHealthDataset = (seed?: number) => {
  const random = seed !== undefined ? mulberry32(seed) : Math.random;

  const sampleData = {
    name: 'Health Dataset',
    columns: [
      { id: `col-${uuidv4()}`, name: 'Age', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Gender', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Income', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Education', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Satisfaction', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Height', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Weight', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'BloodPressure', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Cholesterol', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Diabetic', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Smoker', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'HeartDisease', type: DataType.NUMERIC, role: VariableRole.NONE }
    ],
    data: [] as Record<string, any>[]
  };

  // Generate 100 random data points
  for (let i = 0; i < 100; i++) {
    const row: Record<string, any> = {};
    const age = Math.floor(random() * 50) + 18; // Age between 18-67
    const gender = random() > 0.5 ? 'Male' : 'Female';
    row[sampleData.columns[0].name] = age;
    row[sampleData.columns[1].name] = gender;
    row[sampleData.columns[2].name] = Math.floor(random() * 100000) + 20000; // Income between 20k-120k
    row[sampleData.columns[3].name] = ['High School', 'College', 'Graduate', 'PhD'][Math.floor(random() * 4)];
    row[sampleData.columns[4].name] = Math.floor(random() * 10) + 1; // Satisfaction 1-10
    
    // Height in cm, with some correlation to gender
    const baseHeight = gender === 'Male' ? 175 : 162;
    row[sampleData.columns[5].name] = baseHeight + Math.floor(random() * 20) - 10;
    
    // Weight in kg, with correlation to height and age
    const height = row[sampleData.columns[5].name];
    const idealWeight = (height - 100) + (age / 10);
    row[sampleData.columns[6].name] = idealWeight + Math.floor(random() * 30) - 10;
    
    // Blood pressure, correlated with age and weight
    const weight = row[sampleData.columns[6].name];
    const bpBase = 110 + (age / 10) + (weight - idealWeight) / 2;
    row[sampleData.columns[7].name] = Math.max(100, Math.min(200, Math.floor(bpBase + random() * 20 - 10)));
    
    // Cholesterol, correlated with age, weight and blood pressure
    const bp = row[sampleData.columns[7].name];
    const cholBase = 150 + (age / 5) + (weight - idealWeight) / 2 + (bp - 120) / 2;
    row[sampleData.columns[8].name] = Math.max(120, Math.min(300, Math.floor(cholBase + random() * 30 - 15)));
    
    // Diabetic status - higher chance with age and weight
    const diabeticChance = Math.min(0.5, (age - 30) / 100 + (weight - idealWeight) / 100);
    row[sampleData.columns[9].name] = random() < diabeticChance ? 1 : 0;
    
    // Smoker status - random
    row[sampleData.columns[10].name] = random() < 0.3 ? 1 : 0;
    
    // Heart disease - correlated with age, blood pressure, cholesterol, diabetes and smoking
    const heartDiseaseRisk = 
      (age / 200) + 
      ((bp - 120) / 200) + 
      ((row[sampleData.columns[8].name] - 150) / 200) + 
      (row[sampleData.columns[9].name] * 0.2) + 
      (row[sampleData.columns[10].name] * 0.2);
    
    row[sampleData.columns[11].name] = random() < heartDiseaseRisk ? 1 : 0;
    
    sampleData.data.push(row);
  }

  return {
    id: `dataset-${uuidv4()}`,
    ...sampleData
  };
};

// Function to generate data more suitable for logistic regression testing
export const generateLogisticRegressionDataset = (seed?: number) => {
  const random = seed !== undefined ? mulberry32(seed) : Math.random;

  const sampleData = {
    name: 'Credit Risk Dataset',
    columns: [
      { id: `col-${uuidv4()}`, name: 'Income', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Age', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'LoanAmount', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'CreditScore', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'PreviousDefault', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'EmploymentYears', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'HasProperty', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'DefaultRisk', type: DataType.NUMERIC, role: VariableRole.NONE }
    ],
    data: [] as Record<string, any>[]
  };

  // Generate 100 random data points
  for (let i = 0; i < 150; i++) {
    const row: Record<string, any> = {};
    
    // Income (annual in dollars)
    row[sampleData.columns[0].name] = Math.floor(random() * 80000) + 20000;
    
    // Age
    row[sampleData.columns[1].name] = Math.floor(random() * 50) + 20;
    
    // Loan amount
    row[sampleData.columns[2].name] = Math.floor(random() * 50000) + 5000;
    
    // Credit score (300-850)
    row[sampleData.columns[3].name] = Math.floor(random() * 550) + 300;
    
    // Previous default (0/1)
    row[sampleData.columns[4].name] = random() < 0.2 ? 1 : 0;
    
    // Employment years
    row[sampleData.columns[5].name] = Math.floor(random() * 20);
    
    // Has property (0/1)
    row[sampleData.columns[6].name] = random() < 0.4 ? 1 : 0;
    
    // Calculate default risk based on other factors
    const income = row[sampleData.columns[0].name];
    const age = row[sampleData.columns[1].name];
    const loanAmount = row[sampleData.columns[2].name];
    const creditScore = row[sampleData.columns[3].name];
    const prevDefault = row[sampleData.columns[4].name];
    const employYears = row[sampleData.columns[5].name];
    const hasProperty = row[sampleData.columns[6].name];
    
    // Model default risk
    const scoreComponent = (creditScore - 500) / 350; // Higher score reduces risk
    const incomeComponent = (income / 100000); // Higher income reduces risk
    const loanRatio = loanAmount / income; // Higher ratio increases risk
    const ageStability = Math.min(1, age / 60); // Older more stable up to a point
    const employmentStability = Math.min(1, employYears / 10); // Longer employment more stable
    
    // Calculate risk score (lower is riskier)
    const riskScore = 
      scoreComponent * 0.4 + 
      incomeComponent * 0.2 +
      (1 - loanRatio) * 0.2 +
      ageStability * 0.1 +
      employmentStability * 0.1 -
      (prevDefault * 0.3) + // Previous default is a big negative
      (hasProperty * 0.1);  // Having property is a small positive
    
    // Convert to default (0/1) with some randomness
    const defaultProb = Math.max(0, Math.min(1, 1 - riskScore)) + (random() * 0.2 - 0.1);
    row[sampleData.columns[7].name] = random() < defaultProb ? 1 : 0;
    
    sampleData.data.push(row);
  }

  return {
    id: `dataset-${uuidv4()}`,
    ...sampleData
  };
};

// Function to generate Employee Satisfaction Dataset
export const generateEmployeeSatisfactionDataset = (seed?: number) => {
  const random = seed !== undefined ? mulberry32(seed) : Math.random;

  const sampleData = {
    name: 'Employee Satisfaction Dataset',
    columns: [
      { id: `col-${uuidv4()}`, name: 'EmployeeID', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Age', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Department', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'JobLevel', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'YearsAtCompany', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'MonthlyIncome', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'WorkLifeBalance', type: DataType.NUMERIC, role: VariableRole.NONE }, // Scale 1-4
      { id: `col-${uuidv4()}`, name: 'JobSatisfaction', type: DataType.NUMERIC, role: VariableRole.NONE }  // Scale 1-5
    ],
    data: [] as Record<string, any>[]
  };

  const departments = ['HR', 'Engineering', 'Marketing', 'Sales', 'Finance'];
  const jobLevels = ['Entry', 'Junior', 'Mid-Level', 'Senior', 'Manager'];

  for (let i = 0; i < 120; i++) {
    const row: Record<string, any> = {};
    const age = Math.floor(random() * 40) + 22; // Age 22-61
    const department = departments[Math.floor(random() * departments.length)];
    const jobLevel = jobLevels[Math.floor(random() * jobLevels.length)];
    const yearsAtCompany = Math.floor(random() * (age - 21)); // Max years based on age
    const monthlyIncome = 3000 + (jobLevels.indexOf(jobLevel) * 1500) + (yearsAtCompany * 100) + Math.floor(random() * 1000);
    const workLifeBalance = Math.floor(random() * 4) + 1; // 1-4
    
    // Job satisfaction influenced by income, work-life balance, and job level
    let satisfactionScore = 2; // Base satisfaction
    satisfactionScore += (monthlyIncome / 10000); // Max +1 for income
    satisfactionScore += (workLifeBalance / 4);    // Max +1 for WLB
    satisfactionScore += (jobLevels.indexOf(jobLevel) / 4); // Max +1 for job level
    
    row[sampleData.columns[0].name] = `EMP-${1001 + i}`;
    row[sampleData.columns[1].name] = age;
    row[sampleData.columns[2].name] = department;
    row[sampleData.columns[3].name] = jobLevel;
    row[sampleData.columns[4].name] = Math.max(0, yearsAtCompany);
    row[sampleData.columns[5].name] = Math.floor(monthlyIncome);
    row[sampleData.columns[6].name] = workLifeBalance;
    row[sampleData.columns[7].name] = Math.max(1, Math.min(5, Math.round(satisfactionScore + (random() * 1 - 0.5) ))); // 1-5 with some noise

    sampleData.data.push(row);
  }

  return {
    id: `dataset-${uuidv4()}`,
    ...sampleData
  };
};

// Function to generate Student Performance Dataset
export const generateStudentPerformanceDataset = (seed?: number) => {
  const random = seed !== undefined ? mulberry32(seed) : Math.random;

  const sampleData = {
    name: 'Student Performance Dataset',
    columns: [
      { id: `col-${uuidv4()}`, name: 'StudentID', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Gender', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'PreviousGrade', type: DataType.NUMERIC, role: VariableRole.NONE }, // 0-100
      { id: `col-${uuidv4()}`, name: 'StudyHoursWeekly', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'AttendancePercentage', type: DataType.NUMERIC, role: VariableRole.NONE }, // 0-100
      { id: `col-${uuidv4()}`, name: 'HasTutor', type: DataType.NUMERIC, role: VariableRole.NONE }, // 0 or 1
      { id: `col-${uuidv4()}`, name: 'FinalScore', type: DataType.NUMERIC, role: VariableRole.NONE } // 0-100
    ],
    data: [] as Record<string, any>[]
  };

  const genders = ['Male', 'Female', 'Other'];

  for (let i = 0; i < 150; i++) {
    const row: Record<string, any> = {};
    const gender = genders[Math.floor(random() * genders.length)];
    const previousGrade = Math.floor(random() * 60) + 40; // 40-99
    const studyHoursWeekly = Math.floor(random() * 20) + 1; // 1-20
    const attendancePercentage = Math.floor(random() * 50) + 50; // 50-99
    const hasTutor = random() < 0.3 ? 1 : 0; // 30% have tutors

    // Final score influenced by previous grade, study hours, attendance, and tutor
    let finalScoreCalc = previousGrade * 0.4;
    finalScoreCalc += studyHoursWeekly * 1.5; // Max 30 points
    finalScoreCalc += (attendancePercentage / 100) * 20; // Max 20 points
    finalScoreCalc += hasTutor * 10; // Max 10 points
    
    row[sampleData.columns[0].name] = `STU-${2001 + i}`;
    row[sampleData.columns[1].name] = gender;
    row[sampleData.columns[2].name] = previousGrade;
    row[sampleData.columns[3].name] = studyHoursWeekly;
    row[sampleData.columns[4].name] = attendancePercentage;
    row[sampleData.columns[5].name] = hasTutor;
    row[sampleData.columns[6].name] = Math.max(0, Math.min(100, Math.floor(finalScoreCalc + (random() * 10 - 5)))); // 0-100 with noise

    sampleData.data.push(row);
  }
  
  return {
    id: `dataset-${uuidv4()}`,
    ...sampleData
  };
};

// Function to generate data for Reliability Analysis (e.g., Cronbach's Alpha)
export const generateReliabilityDataset = (seed?: number) => {
  const random = seed !== undefined ? mulberry32(seed) : Math.random;

  const sampleData = {
    name: 'Reliability Analysis Dataset',
    columns: [
      { id: `col-${uuidv4()}`, name: 'ParticipantID', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Item1', type: DataType.NUMERIC, role: VariableRole.NONE }, // Scale 1-5
      { id: `col-${uuidv4()}`, name: 'Item2', type: DataType.NUMERIC, role: VariableRole.NONE }, // Scale 1-5
      { id: `col-${uuidv4()}`, name: 'Item3', type: DataType.NUMERIC, role: VariableRole.NONE }, // Scale 1-5
      { id: `col-${uuidv4()}`, name: 'Item4', type: DataType.NUMERIC, role: VariableRole.NONE }, // Scale 1-5
      { id: `col-${uuidv4()}`, name: 'Item5', type: DataType.NUMERIC, role: VariableRole.NONE }  // Scale 1-5
    ],
    data: [] as Record<string, any>[]
  };

  for (let i = 0; i < 80; i++) {
    const row: Record<string, any> = {};
    row[sampleData.columns[0].name] = `PART-${3001 + i}`;
    
    // Generate scores for items with some correlation
    const baseScore = Math.floor(random() * 3) + 1; // Base score 1-3
    row[sampleData.columns[1].name] = Math.max(1, Math.min(5, baseScore + Math.floor(random() * 3) - 1));
    row[sampleData.columns[2].name] = Math.max(1, Math.min(5, baseScore + Math.floor(random() * 3) - 1));
    row[sampleData.columns[3].name] = Math.max(1, Math.min(5, baseScore + Math.floor(random() * 3) - 1));
    row[sampleData.columns[4].name] = Math.max(1, Math.min(5, baseScore + Math.floor(random() * 3) - 1));
    row[sampleData.columns[5].name] = Math.max(1, Math.min(5, baseScore + Math.floor(random() * 3) - 1));

    sampleData.data.push(row);
  }

  return {
    id: `dataset-${uuidv4()}`,
    ...sampleData
  };
};

// Function to generate data for Factor Analysis
export const generateFactorAnalysisDataset = (seed?: number) => {
  const random = seed !== undefined ? mulberry32(seed) : Math.random;

  const sampleData = {
    name: 'Factor Analysis Dataset',
    columns: [
      { id: `col-${uuidv4()}`, name: 'SubjectID', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q1_Math', type: DataType.NUMERIC, role: VariableRole.NONE }, // Math aptitude
      { id: `col-${uuidv4()}`, name: 'Q2_Math', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q3_Math', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q4_Math', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q5_Math', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q1_Verbal', type: DataType.NUMERIC, role: VariableRole.NONE }, // Verbal aptitude
      { id: `col-${uuidv4()}`, name: 'Q2_Verbal', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q3_Verbal', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q4_Verbal', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q5_Verbal', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q1_Spatial', type: DataType.NUMERIC, role: VariableRole.NONE }, // Spatial reasoning
      { id: `col-${uuidv4()}`, name: 'Q2_Spatial', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q3_Spatial', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q4_Spatial', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Q5_Spatial', type: DataType.NUMERIC, role: VariableRole.NONE }
    ],
    data: [] as Record<string, any>[]
  };

  for (let i = 0; i < 100; i++) {
    const row: Record<string, any> = {};
    row[sampleData.columns[0].name] = `SUB-${4001 + i}`;

    // Simulate underlying factors (Math, Verbal, Spatial)
    const mathFactor = random() * 5 + 1; // 1-6
    const verbalFactor = random() * 5 + 1; // 1-6
    const spatialFactor = random() * 5 + 1; // 1-6

    // Generate item scores based on factors with some noise
    for (let j = 1; j <= 5; j++) {
      row[`Q${j}_Math`] = Math.max(1, Math.min(7, Math.round(mathFactor * (0.6 + random() * 0.3) + random() * 2)));
      row[`Q${j}_Verbal`] = Math.max(1, Math.min(7, Math.round(verbalFactor * (0.6 + random() * 0.3) + random() * 2)));
      row[`Q${j}_Spatial`] = Math.max(1, Math.min(7, Math.round(spatialFactor * (0.6 + random() * 0.3) + random() * 2)));
    }

    sampleData.data.push(row);
  }

  return {
    id: `dataset-${uuidv4()}`,
    ...sampleData
  };
};

// Function to generate data for Survival Analysis (e.g., time to event)
export const generateSurvivalAnalysisDataset = (seed?: number) => {
  const random = seed !== undefined ? mulberry32(seed) : Math.random;

  const sampleData = {
    name: 'Survival Analysis Dataset',
    columns: [
      { id: `col-${uuidv4()}`, name: 'PatientID', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Time', type: DataType.NUMERIC, role: VariableRole.NONE }, // Time to event or censoring
      { id: `col-${uuidv4()}`, name: 'Event', type: DataType.NUMERIC, role: VariableRole.NONE }, // 1 if event occurred, 0 if censored
      { id: `col-${uuidv4()}`, name: 'Age', type: DataType.NUMERIC, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'Treatment', type: DataType.CATEGORICAL, role: VariableRole.NONE } // e.g., 'A', 'B', 'C'
    ],
    data: [] as Record<string, any>[]
  };

  const treatments = ['A', 'B', 'C'];

  for (let i = 0; i < 100; i++) {
    const row: Record<string, any> = {};
    const age = Math.floor(random() * 40) + 30; // Age 30-69
    const treatment = treatments[Math.floor(random() * treatments.length)];

    // Simulate survival time based on age and treatment
    let baseTime = 100 + (70 - age) * 2; // Older age reduces time
    if (treatment === 'B') baseTime += 30; // Treatment B improves survival
    if (treatment === 'C') baseTime -= 20; // Treatment C worsens survival

    const time = Math.max(10, Math.min(200, Math.floor(baseTime + random() * 40 - 20))); // Add some noise

    // Simulate event occurrence (higher chance with shorter time)
    const event = random() < (200 - time) / 150 ? 1 : 0;

    row[sampleData.columns[0].name] = `PAT-${5001 + i}`;
    row[sampleData.columns[1].name] = time;
    row[sampleData.columns[2].name] = event;
    row[sampleData.columns[3].name] = age;
    row[sampleData.columns[4].name] = treatment;

    sampleData.data.push(row);
  }

  return {
    id: `dataset-${uuidv4()}`,
    ...sampleData
  };
};

// Function to generate data for Mediation/Moderation Analysis
export const generateMediationModerationDataset = (seed?: number) => {
  const random = seed !== undefined ? mulberry32(seed) : Math.random;

  const sampleData = {
    name: 'Mediation/Moderation Dataset',
    columns: [
      { id: `col-${uuidv4()}`, name: 'SubjectID', type: DataType.CATEGORICAL, role: VariableRole.NONE },
      { id: `col-${uuidv4()}`, name: 'IndependentVar', type: DataType.NUMERIC, role: VariableRole.NONE }, // X
      { id: `col-${uuidv4()}`, name: 'MediatorVar', type: DataType.NUMERIC, role: VariableRole.NONE },    // M
      { id: `col-${uuidv4()}`, name: 'DependentVar', type: DataType.NUMERIC, role: VariableRole.NONE },   // Y
      { id: `col-${uuidv4()}`, name: 'ModeratorVar', type: DataType.NUMERIC, role: VariableRole.NONE }   // W (for moderation)
    ],
    data: [] as Record<string, any>[]
  };

  for (let i = 0; i < 100; i++) {
    const row: Record<string, any> = {};
    const independentVar = random() * 10; // X (0-10)
    const moderatorVar = random() * 5; // W (0-5)

    // Simulate relationships: X -> M, M -> Y, X -> Y (direct effect), X*W -> Y (moderation)
    const mediatorVar = independentVar * 0.6 + random() * 3; // M = f(X) + noise
    const dependentVar = independentVar * 0.3 + mediatorVar * 0.5 + (independentVar * moderatorVar * 0.2) + random() * 5; // Y = f(X, M, X*W) + noise

    row[sampleData.columns[0].name] = `SUB-${6001 + i}`;
    row[sampleData.columns[1].name] = parseFloat(independentVar.toFixed(2));
    row[sampleData.columns[2].name] = parseFloat(mediatorVar.toFixed(2));
    row[sampleData.columns[3].name] = parseFloat(dependentVar.toFixed(2));
    row[sampleData.columns[4].name] = parseFloat(moderatorVar.toFixed(2));

    sampleData.data.push(row);
  }

  return {
    id: `dataset-${uuidv4()}`,
    ...sampleData
  };
};


// Function to generate data for Meta Analysis
export const generateMetaAnalysisDataset = (seed?: number) => {
  const random = seed !== undefined ? mulberry32(seed) : Math.random;

  const sampleData = {
    name: 'Meta Analysis Dataset',
    description: 'Study-level data for meta-analysis with effect sizes, confidence intervals, and study characteristics',
    columns: [
      { id: uuidv4(), name: 'Study_ID', type: DataType.TEXT, role: VariableRole.NONE, description: 'Unique study identifier' },
      { id: uuidv4(), name: 'Study_Name', type: DataType.TEXT, role: VariableRole.NONE, description: 'Name of the study' },
      { id: uuidv4(), name: 'Publication_Year', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Year of publication' },
      { id: uuidv4(), name: 'Sample_Size', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Total sample size' },
      { id: uuidv4(), name: 'Effect_Size', type: DataType.NUMERIC, role: VariableRole.DEPENDENT, description: 'Standardized effect size (Cohen\'s d)' },
      { id: uuidv4(), name: 'Standard_Error', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Standard error of effect size' },
      { id: uuidv4(), name: 'CI_Lower', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: '95% confidence interval lower bound' },
      { id: uuidv4(), name: 'CI_Upper', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: '95% confidence interval upper bound' },
      { id: uuidv4(), name: 'Study_Quality', type: DataType.CATEGORICAL, role: VariableRole.INDEPENDENT, description: 'Study quality rating' },
      { id: uuidv4(), name: 'Country', type: DataType.CATEGORICAL, role: VariableRole.INDEPENDENT, description: 'Country where study was conducted' },
      { id: uuidv4(), name: 'Population_Type', type: DataType.CATEGORICAL, role: VariableRole.INDEPENDENT, description: 'Type of population studied' },
      { id: uuidv4(), name: 'Intervention_Duration', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Duration of intervention in weeks' }
    ],
    data: [] as any[]
  };

  const studyQualities = ['High', 'Medium', 'Low'];
  const countries = ['USA', 'UK', 'Canada', 'Germany', 'Australia', 'France', 'Netherlands', 'Sweden', 'Japan', 'Brazil'];
  const populationTypes = ['Adults', 'Children', 'Elderly', 'Students', 'Patients', 'Athletes'];
  const studyNames = [
    'Smith et al.', 'Johnson & Brown', 'Williams Study', 'Davis Research', 'Miller Investigation',
    'Wilson Analysis', 'Moore Experiment', 'Taylor Trial', 'Anderson Study', 'Thomas Research',
    'Jackson Investigation', 'White Analysis', 'Harris Experiment', 'Martin Trial', 'Thompson Study',
    'Garcia Research', 'Martinez Investigation', 'Robinson Analysis', 'Clark Experiment', 'Rodriguez Trial',
    'Lewis Study', 'Lee Research', 'Walker Investigation', 'Hall Analysis', 'Allen Experiment',
    'Young Trial', 'Hernandez Study', 'King Research', 'Wright Investigation', 'Lopez Analysis'
  ];

  // Generate 30 studies
  for (let i = 0; i < 30; i++) {
    const row: any = {};

    // Basic study information
    row[sampleData.columns[0].name] = `STUDY_${String(i + 1).padStart(3, '0')}`;
    row[sampleData.columns[1].name] = studyNames[i % studyNames.length];
    row[sampleData.columns[2].name] = Math.floor(2010 + random() * 14); // 2010-2023

    // Sample size (log-normal distribution)
    const logSampleSize = 4 + random() * 2; // log(50) to log(400) approximately
    row[sampleData.columns[3].name] = Math.floor(Math.exp(logSampleSize));

    // True effect size with some heterogeneity
    const trueEffect = 0.3 + (random() - 0.5) * 0.6; // Range from -0.3 to 0.9

    // Standard error based on sample size
    const sampleSize = row[sampleData.columns[3].name];
    const standardError = Math.sqrt(2 / sampleSize) + random() * 0.05;

    // Observed effect size with sampling error
    const observedEffect = trueEffect + (random() - 0.5) * 2 * standardError;

    row[sampleData.columns[4].name] = parseFloat(observedEffect.toFixed(3));
    row[sampleData.columns[5].name] = parseFloat(standardError.toFixed(4));

    // 95% Confidence intervals
    const ciLower = observedEffect - 1.96 * standardError;
    const ciUpper = observedEffect + 1.96 * standardError;
    row[sampleData.columns[6].name] = parseFloat(ciLower.toFixed(3));
    row[sampleData.columns[7].name] = parseFloat(ciUpper.toFixed(3));

    // Study characteristics
    row[sampleData.columns[8].name] = studyQualities[Math.floor(random() * studyQualities.length)];
    row[sampleData.columns[9].name] = countries[Math.floor(random() * countries.length)];
    row[sampleData.columns[10].name] = populationTypes[Math.floor(random() * populationTypes.length)];
    row[sampleData.columns[11].name] = Math.floor(4 + random() * 20); // 4-24 weeks

    sampleData.data.push(row);
  }

  return {
    id: `dataset-${uuidv4()}`,
    ...sampleData
  };
};

// Function to generate data for Cluster Analysis
export const generateClusterAnalysisDataset = (seed?: number) => {
  const random = seed !== undefined ? mulberry32(seed) : Math.random;

  const sampleData = {
    name: 'Cluster Analysis Dataset',
    description: 'Customer segmentation data with multiple numeric variables for clustering analysis',
    columns: [
      { id: uuidv4(), name: 'Customer_ID', type: DataType.TEXT, role: VariableRole.NONE, description: 'Unique customer identifier' },
      { id: uuidv4(), name: 'Annual_Income', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Annual income in thousands' },
      { id: uuidv4(), name: 'Spending_Score', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Spending score (1-100)' },
      { id: uuidv4(), name: 'Age', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Customer age in years' },
      { id: uuidv4(), name: 'Purchase_Frequency', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Number of purchases per month' },
      { id: uuidv4(), name: 'Online_Engagement', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Online engagement score (0-10)' },
      { id: uuidv4(), name: 'Customer_Tenure', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Years as customer' },
      { id: uuidv4(), name: 'Product_Diversity', type: DataType.NUMERIC, role: VariableRole.INDEPENDENT, description: 'Number of different product categories purchased' }
    ],
    data: [] as any[]
  };

  // Define cluster centers for realistic clustering patterns
  const clusterCenters = [
    // High income, high spending (Premium customers)
    { income: 80, spending: 85, age: 45, frequency: 8, engagement: 8.5, tenure: 5, diversity: 12 },
    // Low income, low spending (Budget customers)
    { income: 35, spending: 25, age: 35, frequency: 2, engagement: 4, tenure: 2, diversity: 4 },
    // Medium income, medium spending (Regular customers)
    { income: 55, spending: 55, age: 40, frequency: 5, engagement: 6, tenure: 3, diversity: 8 },
    // Young, tech-savvy (Digital natives)
    { income: 45, spending: 70, age: 28, frequency: 12, engagement: 9, tenure: 1.5, diversity: 6 },
    // Older, traditional (Traditional customers)
    { income: 65, spending: 40, age: 60, frequency: 3, engagement: 3, tenure: 8, diversity: 5 }
  ];

  // Generate 400 customers
  for (let i = 0; i < 400; i++) {
    const row: any = {};

    // Assign to cluster with some probability
    const clusterIndex = Math.floor(random() * clusterCenters.length);
    const center = clusterCenters[clusterIndex];

    // Add noise around cluster centers
    const noise = () => (random() - 0.5) * 0.4; // ±20% noise

    row[sampleData.columns[0].name] = `CUST_${String(i + 1).padStart(4, '0')}`;

    // Generate values around cluster centers with noise
    row[sampleData.columns[1].name] = Math.max(20, Math.min(120, Math.round(center.income * (1 + noise()))));
    row[sampleData.columns[2].name] = Math.max(1, Math.min(100, Math.round(center.spending * (1 + noise()))));
    row[sampleData.columns[3].name] = Math.max(18, Math.min(80, Math.round(center.age * (1 + noise()))));
    row[sampleData.columns[4].name] = Math.max(0, Math.round(center.frequency * (1 + noise())));
    row[sampleData.columns[5].name] = parseFloat(Math.max(0, Math.min(10, center.engagement * (1 + noise()))).toFixed(1));
    row[sampleData.columns[6].name] = parseFloat(Math.max(0.1, center.tenure * (1 + noise())).toFixed(1));
    row[sampleData.columns[7].name] = Math.max(1, Math.min(20, Math.round(center.diversity * (1 + noise()))));

    sampleData.data.push(row);
  }

  return {
    id: `dataset-${uuidv4()}`,
    ...sampleData
  };
};

export default {
  generateHealthDataset,
  generateLogisticRegressionDataset,
  generateEmployeeSatisfactionDataset,
  generateStudentPerformanceDataset,
  generateReliabilityDataset, // Added
  generateFactorAnalysisDataset, // Added
  generateSurvivalAnalysisDataset, // Added
  generateMediationModerationDataset, // Added
  generateMetaAnalysisDataset, // Added
  generateClusterAnalysisDataset // Added
};
