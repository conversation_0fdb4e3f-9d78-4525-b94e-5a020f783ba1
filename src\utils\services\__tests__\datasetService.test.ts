import { saveDataset, loadDataset, listDatasets, deleteDataset } from '../datasetService';
import { supabase } from '../../supabaseClient';

// Mock Supabase
jest.mock('../../supabaseClient', () => ({
  supabase: {
    auth: {
      getUser: jest.fn()
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    single: jest.fn(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis()
  }
}));

describe('datasetService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('saveDataset', () => {
    it('should check authentication before saving', async () => {
      // Mock user not authenticated
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ data: { user: null } });

      const result = await saveDataset('Test Dataset', { data: [], variableInfo: [] });
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('not authenticated');
    });

    it('should check dataset size before saving', async () => {
      // Mock authenticated user
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ 
        data: { user: { id: 'test-user-id' } } 
      });

      // Create a large dataset that exceeds 2MB
      const largeData = Array(100000).fill({ value: 'x'.repeat(1000) });
      
      const result = await saveDataset('Large Dataset', { 
        data: largeData, 
        variableInfo: [] 
      });
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('2MB limit');
    });

    it('should check dataset count before saving a new dataset', async () => {
      // Mock authenticated user
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ 
        data: { user: { id: 'test-user-id' } } 
      });

      // Mock listDatasets to return 2 datasets
      jest.spyOn(global, 'listDatasets').mockResolvedValue({
        data: [
          { id: 'dataset1', dataset_name: 'Dataset 1' },
          { id: 'dataset2', dataset_name: 'Dataset 2' }
        ],
        error: null
      });

      // Mock dataset not existing
      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { code: 'PGRST116' }
              })
            })
          })
        })
      });

      const result = await saveDataset('New Dataset', { 
        data: [{ value: 'test' }], 
        variableInfo: [] 
      });
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('maximum limit of 2');
    });
  });

  describe('loadDataset', () => {
    it('should check authentication before loading', async () => {
      // Mock user not authenticated
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ data: { user: null } });

      const result = await loadDataset('test-dataset-id');
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('not authenticated');
    });
  });

  describe('listDatasets', () => {
    it('should check authentication before listing', async () => {
      // Mock user not authenticated
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ data: { user: null } });

      const result = await listDatasets();
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('not authenticated');
    });
  });

  describe('deleteDataset', () => {
    it('should check authentication before deleting', async () => {
      // Mock user not authenticated
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({ data: { user: null } });

      const result = await deleteDataset('test-dataset-id');
      
      expect(result.error).toBeTruthy();
      expect(result.error.message).toContain('not authenticated');
    });
  });
});