// src/utils/services/confirmatoryFactorAnalysisService.ts

interface CFAModelSpecification {
  factors: {
    name: string;
    indicators: string[];
  }[];
  constraints: {
    allowCorrelatedErrors: boolean;
    allowFactorCorrelations: boolean;
    constrainVariances: boolean;
    meanStructure: boolean;
  };
}

interface FitIndices {
  chi_square: number;
  p_value: number;
  degrees_of_freedom: number;
  cfi: number;
  tli: number;
  rmsea: number;
  rmsea_ci_lower: number;
  rmsea_ci_upper: number;
  srmr: number;
  aic: number;
  bic: number;
  gfi?: number;
  agfi?: number;
}

interface PathCoefficient {
  from: string;
  to: string;
  parameter_type: 'loading' | 'correlation' | 'variance' | 'covariance';
  unstandardized_estimate: number;
  standardized_estimate: number;
  standard_error: number;
  z_score: number;
  p_value: number;
  confidence_interval_lower: number;
  confidence_interval_upper: number;
}

interface ModificationIndex {
  parameter: string;
  modification_index: number;
  expected_parameter_change: number;
  parameter_type: string;
}

interface CFAResults {
  fit_indices: FitIndices;
  factor_loadings: PathCoefficient[];
  factor_correlations: PathCoefficient[];
  error_correlations?: PathCoefficient[];
  modification_indices?: ModificationIndex[];
  residual_correlations?: number[][];
  standardized_residuals?: number[][];
  n_factors: number;
  n_observed_variables: number;
  n_parameters: number;
  n_observations: number;
  degrees_of_freedom: number;
  estimation_method: string;
  converged: boolean;
  iterations: number;
}

interface CFAData {
  variables: { [key: string]: number[] };
  variable_names: string[];
  model_specification: CFAModelSpecification;
  estimation_method: string;
  significance_level: number;
}

class ConfirmatoryFactorAnalysisService {
  private pyodide: any = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    if (this.initializationPromise) return this.initializationPromise;

    this.initializationPromise = this.doInitialize();
    await this.initializationPromise;
  }

  private async doInitialize(): Promise<void> {
    try {
      // Load Pyodide from CDN
      if (typeof window !== 'undefined' && !(window as any).loadPyodide) {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/pyodide/v0.25.0/full/pyodide.js';
        document.head.appendChild(script);
        
        await new Promise((resolve, reject) => {
          script.onload = resolve;
          script.onerror = reject;
        });
      }

      // Initialize Pyodide
      this.pyodide = await (window as any).loadPyodide({
        indexURL: 'https://cdn.jsdelivr.net/pyodide/v0.25.0/full/'
      });

      // Install required packages
      await this.pyodide.loadPackage(['numpy', 'scipy']);
      
      console.log('Pyodide initialized for Confirmatory Factor Analysis');
      this.setupCFAImplementation();
      
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Pyodide:', error);
      throw new Error('Failed to initialize Python environment for confirmatory factor analysis');
    }
  }

  private setupCFAImplementation(): void {
    this.pyodide.runPython(`
import numpy as np
import scipy.stats as stats
import scipy.linalg as linalg
from scipy.optimize import minimize
import json
import warnings
warnings.filterwarnings('ignore')

def create_model_matrices(model_spec, variable_names):
    """Create model matrices for CFA"""
    n_vars = len(variable_names)
    factors = model_spec['factors']
    n_factors = len(factors)
    
    # Create variable name to index mapping
    var_to_idx = {name: idx for idx, name in enumerate(variable_names)}
    
    # Factor loading matrix (Lambda)
    factor_loading_matrix = np.zeros((n_vars, n_factors))
    
    # Track which parameters are free
    free_loading_params = []
    
    for f_idx, factor in enumerate(factors):
        for indicator in factor['indicators']:
            if indicator in var_to_idx:
                var_idx = var_to_idx[indicator]
                factor_loading_matrix[var_idx, f_idx] = 1.0  # Start with 1, will be estimated
                free_loading_params.append((var_idx, f_idx))
    
    # Factor correlation matrix (Phi)
    factor_corr_matrix = np.eye(n_factors)
    free_factor_corr_params = []
    
    if model_spec['constraints']['allowFactorCorrelations'] and n_factors > 1:
        for i in range(n_factors):
            for j in range(i + 1, n_factors):
                free_factor_corr_params.append((i, j))
    
    # Error correlation matrix (Theta)
    error_corr_matrix = np.eye(n_vars)
    free_error_corr_params = []
    
    if model_spec['constraints']['allowCorrelatedErrors']:
        # For now, don't add any correlated errors by default
        # They would be added based on modification indices
        pass
    
    return {
        'factor_loading_matrix': factor_loading_matrix,
        'factor_corr_matrix': factor_corr_matrix,
        'error_corr_matrix': error_corr_matrix,
        'free_loading_params': free_loading_params,
        'free_factor_corr_params': free_factor_corr_params,
        'free_error_corr_params': free_error_corr_params,
        'n_vars': n_vars,
        'n_factors': n_factors
    }

def calculate_covariance_matrix(params, model_matrices):
    """Calculate implied covariance matrix from parameters"""
    # Unpack parameters
    param_idx = 0
    
    # Factor loadings
    lambda_matrix = model_matrices['factor_loading_matrix'].copy()
    for var_idx, factor_idx in model_matrices['free_loading_params']:
        lambda_matrix[var_idx, factor_idx] = params[param_idx]
        param_idx += 1
    
    # Factor correlations
    phi_matrix = model_matrices['factor_corr_matrix'].copy()
    for i, j in model_matrices['free_factor_corr_params']:
        phi_matrix[i, j] = phi_matrix[j, i] = params[param_idx]
        param_idx += 1
    
    # Error variances (diagonal of Theta)
    theta_matrix = np.eye(model_matrices['n_vars'])
    for i in range(model_matrices['n_vars']):
        theta_matrix[i, i] = params[param_idx]
        param_idx += 1
    
    # Error correlations
    for i, j in model_matrices['free_error_corr_params']:
        theta_matrix[i, j] = theta_matrix[j, i] = params[param_idx]
        param_idx += 1
    
    # Calculate implied covariance matrix: Sigma = Lambda * Phi * Lambda' + Theta
    sigma = lambda_matrix @ phi_matrix @ lambda_matrix.T + theta_matrix
    
    return sigma, lambda_matrix, phi_matrix, theta_matrix

def ml_fit_function(params, sample_cov, model_matrices, n_obs):
    """Maximum likelihood fit function"""
    try:
        sigma, _, _, _ = calculate_covariance_matrix(params, model_matrices)
        
        # Ensure positive definiteness
        eigenvals = np.linalg.eigvals(sigma)
        if np.any(eigenvals <= 1e-10):
            return 1e10
        
        # ML fit function: log|Sigma| + tr(S * Sigma^-1) - log|S| - p
        log_det_sigma = np.linalg.slogdet(sigma)[1]
        inv_sigma = np.linalg.inv(sigma)
        trace_term = np.trace(sample_cov @ inv_sigma)
        
        fit = log_det_sigma + trace_term
        
        return fit
        
    except:
        return 1e10

def gls_fit_function(params, sample_cov, model_matrices, n_obs):
    """Generalized least squares fit function"""
    try:
        sigma, _, _, _ = calculate_covariance_matrix(params, model_matrices)
        
        # GLS fit function: 0.5 * tr((S - Sigma) * S^-1)^2
        inv_sample_cov = np.linalg.inv(sample_cov)
        diff = sample_cov - sigma
        fit = 0.5 * np.trace((diff @ inv_sample_cov) @ (diff @ inv_sample_cov))
        
        return fit
        
    except:
        return 1e10

def wls_fit_function(params, sample_cov, model_matrices, n_obs):
    """Weighted least squares fit function"""
    try:
        sigma, _, _, _ = calculate_covariance_matrix(params, model_matrices)
        
        # WLS fit function: (s - sigma)' * W * (s - sigma)
        # where s and sigma are vectorized lower triangular parts
        s_vec = sample_cov[np.tril_indices_from(sample_cov)]
        sigma_vec = sigma[np.tril_indices_from(sigma)]
        diff = s_vec - sigma_vec
        
        # Use identity weight matrix for simplicity
        W = np.eye(len(diff))
        fit = diff.T @ W @ diff
        
        return fit
        
    except:
        return 1e10

def estimate_cfa_model(sample_cov, model_matrices, estimation_method='ml', n_obs=100):
    """Estimate CFA model parameters"""
    # Initialize parameters
    initial_params = []
    
    # Factor loadings (start with 0.7)
    for _ in model_matrices['free_loading_params']:
        initial_params.append(0.7)
    
    # Factor correlations (start with 0.3)
    for _ in model_matrices['free_factor_corr_params']:
        initial_params.append(0.3)
    
    # Error variances (start with 0.5)
    for _ in range(model_matrices['n_vars']):
        initial_params.append(0.5)
    
    # Error correlations (start with 0.0)
    for _ in model_matrices['free_error_corr_params']:
        initial_params.append(0.0)
    
    # Parameter bounds
    bounds = []
    
    # Factor loadings bounds
    for _ in model_matrices['free_loading_params']:
        bounds.append((-5.0, 5.0))
    
    # Factor correlations bounds
    for _ in model_matrices['free_factor_corr_params']:
        bounds.append((-0.99, 0.99))
    
    # Error variances bounds (must be positive)
    for _ in range(model_matrices['n_vars']):
        bounds.append((0.001, 10.0))
    
    # Error correlations bounds
    for _ in model_matrices['free_error_corr_params']:
        bounds.append((-0.99, 0.99))
    
    # Choose fit function
    if estimation_method == 'gls':
        fit_func = gls_fit_function
    elif estimation_method == 'wls':
        fit_func = wls_fit_function
    else:  # ml or mlr
        fit_func = ml_fit_function
    
    # Optimize
    try:
        result = minimize(
            fit_func,
            initial_params,
            args=(sample_cov, model_matrices, n_obs),
            method='L-BFGS-B',
            bounds=bounds,
            options={'maxiter': 1000, 'ftol': 1e-9}
        )
        
        if result.success:
            return result.x, result.fun, True, result.nit
        else:
            return initial_params, 1e10, False, 0
            
    except:
        return initial_params, 1e10, False, 0

def calculate_fit_indices(sample_cov, fitted_params, model_matrices, n_obs, estimation_method='ml'):
    """Calculate model fit indices"""
    try:
        sigma, _, _, _ = calculate_covariance_matrix(fitted_params, model_matrices)
        n_vars = model_matrices['n_vars']
        n_params = len(fitted_params)
        df = int(n_vars * (n_vars + 1) / 2 - n_params)
        
        # Chi-square test statistic
        if estimation_method == 'ml':
            log_det_sigma = np.linalg.slogdet(sigma)[1]
            log_det_sample = np.linalg.slogdet(sample_cov)[1]
            inv_sigma = np.linalg.inv(sigma)
            trace_term = np.trace(sample_cov @ inv_sigma)
            chi_square = (n_obs - 1) * (log_det_sigma + trace_term - log_det_sample - n_vars)
        else:
            # For other methods, use a simpler approximation
            diff = sample_cov - sigma
            chi_square = (n_obs - 1) * np.trace(diff @ np.linalg.inv(sample_cov) @ diff)
        
        # p-value
        p_value = 1 - stats.chi2.cdf(chi_square, df) if df > 0 else 1.0
        
        # Baseline model (independence model)
        baseline_cov = np.diag(np.diag(sample_cov))
        if estimation_method == 'ml':
            log_det_baseline = np.linalg.slogdet(baseline_cov)[1]
            inv_baseline = np.linalg.inv(baseline_cov)
            baseline_chi = (n_obs - 1) * (log_det_baseline + np.trace(sample_cov @ inv_baseline) - log_det_sample - n_vars)
        else:
            diff_baseline = sample_cov - baseline_cov
            baseline_chi = (n_obs - 1) * np.trace(diff_baseline @ np.linalg.inv(sample_cov) @ diff_baseline)
        
        baseline_df = int(n_vars * (n_vars - 1) / 2)
        
        # CFI (Comparative Fit Index)
        if baseline_df > 0 and baseline_chi > 0:
            cfi = max(0, 1 - max(0, chi_square - df) / max(0, baseline_chi - baseline_df))
        else:
            cfi = 1.0
        
        # TLI (Tucker-Lewis Index)
        if baseline_df > 0 and df > 0:
            tli = 1 - (chi_square / df) / (baseline_chi / baseline_df)
        else:
            tli = 1.0
        
        # RMSEA (Root Mean Square Error of Approximation)
        if df > 0:
            rmsea = np.sqrt(max(0, (chi_square - df) / (df * (n_obs - 1))))
            
            # RMSEA confidence interval (approximate)
            alpha = 0.1  # 90% CI
            chi_lower = stats.chi2.ppf(alpha/2, df)
            chi_upper = stats.chi2.ppf(1 - alpha/2, df)
            rmsea_ci_lower = np.sqrt(max(0, (chi_lower - df) / (df * (n_obs - 1))))
            rmsea_ci_upper = np.sqrt(max(0, (chi_upper - df) / (df * (n_obs - 1))))
        else:
            rmsea = 0.0
            rmsea_ci_lower = 0.0
            rmsea_ci_upper = 0.0
        
        # SRMR (Standardized Root Mean Square Residual)
        residuals = sample_cov - sigma
        diag_sample = np.sqrt(np.diag(sample_cov))
        standardized_residuals = residuals / np.outer(diag_sample, diag_sample)
        srmr = np.sqrt(np.mean(standardized_residuals[np.tril_indices_from(standardized_residuals, k=-1)]**2))
        
        # Information criteria
        log_likelihood = -(n_obs - 1) * chi_square / 2
        aic = -2 * log_likelihood + 2 * n_params
        bic = -2 * log_likelihood + np.log(n_obs) * n_params
        
        return {
            'chi_square': float(chi_square),
            'p_value': float(p_value),
            'degrees_of_freedom': int(df),
            'cfi': float(cfi),
            'tli': float(tli),
            'rmsea': float(rmsea),
            'rmsea_ci_lower': float(rmsea_ci_lower),
            'rmsea_ci_upper': float(rmsea_ci_upper),
            'srmr': float(srmr),
            'aic': float(aic),
            'bic': float(bic)
        }
        
    except Exception as e:
        print(f"Error calculating fit indices: {e}")
        return {
            'chi_square': 0.0,
            'p_value': 1.0,
            'degrees_of_freedom': 1,
            'cfi': 0.0,
            'tli': 0.0,
            'rmsea': 1.0,
            'rmsea_ci_lower': 0.0,
            'rmsea_ci_upper': 1.0,
            'srmr': 1.0,
            'aic': 1e10,
            'bic': 1e10
        }

def calculate_standard_errors(fitted_params, sample_cov, model_matrices, n_obs):
    """Calculate standard errors using numerical derivatives"""
    def fit_func(params):
        try:
            sigma, _, _, _ = calculate_covariance_matrix(params, model_matrices)
            log_det_sigma = np.linalg.slogdet(sigma)[1]
            log_det_sample = np.linalg.slogdet(sample_cov)[1]
            inv_sigma = np.linalg.inv(sigma)
            trace_term = np.trace(sample_cov @ inv_sigma)
            return log_det_sigma + trace_term - log_det_sample - model_matrices['n_vars']
        except:
            return 1e10
    
    # Calculate Hessian using finite differences
    h = 1e-6
    n_params = len(fitted_params)
    hessian = np.zeros((n_params, n_params))
    
    f0 = fit_func(fitted_params)
    
    for i in range(n_params):
        for j in range(i, n_params):
            params_ij = fitted_params.copy()
            params_ij[i] += h
            params_ij[j] += h
            
            params_i = fitted_params.copy()
            params_i[i] += h
            
            params_j = fitted_params.copy()
            params_j[j] += h
            
            fij = fit_func(params_ij)
            fi = fit_func(params_i)
            fj = fit_func(params_j)
            
            hessian[i, j] = (fij - fi - fj + f0) / (h * h)
            if i != j:
                hessian[j, i] = hessian[i, j]
    
    try:
        # Information matrix is (n-1) * Hessian
        info_matrix = (n_obs - 1) * hessian
        
        # Standard errors are sqrt of diagonal of inverse information matrix
        inv_info = np.linalg.inv(info_matrix)
        standard_errors = np.sqrt(np.diag(inv_info))
        
        return standard_errors, inv_info
    except:
        return np.ones(n_params) * 0.1, np.eye(n_params)

def extract_path_coefficients(fitted_params, standard_errors, model_matrices, model_spec, variable_names):
    """Extract path coefficients from fitted parameters"""
    param_idx = 0
    path_coefficients = []
    
    # Factor loadings
    sigma, lambda_matrix, phi_matrix, theta_matrix = calculate_covariance_matrix(fitted_params, model_matrices)
    
    # Calculate standardization factors
    var_names_ordered = variable_names
    implied_variances = np.diag(sigma)
    factor_variances = np.diag(phi_matrix)
    
    # Factor loadings
    for var_idx, factor_idx in model_matrices['free_loading_params']:
        unstd_estimate = fitted_params[param_idx]
        se = standard_errors[param_idx]
        
        # Standardized loading
        if factor_variances[factor_idx] > 0 and implied_variances[var_idx] > 0:
            std_estimate = unstd_estimate * np.sqrt(factor_variances[factor_idx]) / np.sqrt(implied_variances[var_idx])
        else:
            std_estimate = unstd_estimate
        
        z_score = unstd_estimate / se if se > 0 else 0
        p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
        
        # 95% confidence interval
        ci_lower = unstd_estimate - 1.96 * se
        ci_upper = unstd_estimate + 1.96 * se
        
        factor_name = model_spec['factors'][factor_idx]['name']
        var_name = var_names_ordered[var_idx]
        
        path_coefficients.append({
            'from': factor_name,
            'to': var_name,
            'parameter_type': 'loading',
            'unstandardized_estimate': float(unstd_estimate),
            'standardized_estimate': float(std_estimate),
            'standard_error': float(se),
            'z_score': float(z_score),
            'p_value': float(p_value),
            'confidence_interval_lower': float(ci_lower),
            'confidence_interval_upper': float(ci_upper)
        })
        
        param_idx += 1
    
    # Factor correlations
    factor_correlations = []
    for i, j in model_matrices['free_factor_corr_params']:
        unstd_estimate = fitted_params[param_idx]
        se = standard_errors[param_idx]
        
        # For correlations, standardized = unstandardized
        std_estimate = unstd_estimate
        
        z_score = unstd_estimate / se if se > 0 else 0
        p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
        
        ci_lower = unstd_estimate - 1.96 * se
        ci_upper = unstd_estimate + 1.96 * se
        
        factor_name_i = model_spec['factors'][i]['name']
        factor_name_j = model_spec['factors'][j]['name']
        
        factor_correlations.append({
            'from': factor_name_i,
            'to': factor_name_j,
            'parameter_type': 'correlation',
            'unstandardized_estimate': float(unstd_estimate),
            'standardized_estimate': float(std_estimate),
            'standard_error': float(se),
            'z_score': float(z_score),
            'p_value': float(p_value),
            'confidence_interval_lower': float(ci_lower),
            'confidence_interval_upper': float(ci_upper)
        })
        
        param_idx += 1
    
    return path_coefficients, factor_correlations

def calculate_modification_indices(fitted_params, sample_cov, model_matrices, n_obs):
    """Calculate modification indices for potential model improvements using Lagrange multiplier test"""
    modification_indices = []

    try:
        n_vars = model_matrices['n_vars']
        n_factors = model_matrices['n_factors']

        # Get current model implied covariance and derivatives
        sigma, lambda_matrix, phi_matrix, theta_matrix = calculate_covariance_matrix(fitted_params, model_matrices)

        # Calculate residual matrix
        residual_matrix = sample_cov - sigma

        # Calculate weight matrix (for ML estimation, use simpler approach)
        try:
            inv_sigma = np.linalg.inv(sigma)
        except:
            # If sigma is singular, use pseudo-inverse
            inv_sigma = np.linalg.pinv(sigma)



        # Current free parameters
        current_loadings = set(model_matrices['free_loading_params'])
        current_error_corrs = set(model_matrices['free_error_corr_params'])

        # Check factor loadings not currently in model
        for var_idx in range(n_vars):
            for factor_idx in range(n_factors):
                if (var_idx, factor_idx) not in current_loadings:
                    # Calculate derivative of sigma with respect to this loading
                    d_sigma = np.zeros((n_vars, n_vars))

                    # d(sigma)/d(lambda_ij) = e_i * phi_jj * lambda_j^T + lambda_j * phi_jj * e_i^T
                    # where e_i is unit vector for variable i, lambda_j is column j of lambda matrix
                    e_i = np.zeros(n_vars)
                    e_i[var_idx] = 1.0
                    lambda_j = lambda_matrix[:, factor_idx]
                    phi_jj = phi_matrix[factor_idx, factor_idx]

                    d_sigma = phi_jj * (np.outer(e_i, lambda_j) + np.outer(lambda_j, e_i))

                    # Calculate modification index using simpler approach
                    # Score function: tr(inv_sigma * residual * inv_sigma * d_sigma)
                    score_matrix = inv_sigma @ residual_matrix @ inv_sigma @ d_sigma
                    score = np.trace(score_matrix)

                    # Information matrix: tr(inv_sigma * d_sigma * inv_sigma * d_sigma)
                    info_matrix = inv_sigma @ d_sigma @ inv_sigma @ d_sigma
                    information = np.trace(info_matrix)

                    if information > 1e-10:
                        mi = (n_obs - 1) * (score ** 2) / information
                        epc = score / information if information > 0 else 0.0
                    else:
                        mi = 0.0
                        epc = 0.0

                    # Include all modification indices above a very small threshold
                    if mi > 0.01:  # Lower threshold to capture more indices
                        modification_indices.append({
                            'parameter': f'Loading: Factor{factor_idx+1} -> Var{var_idx+1}',
                            'modification_index': float(mi),
                            'expected_parameter_change': float(epc),
                            'parameter_type': 'loading'
                        })


        # Check error correlations not currently in model
        for i in range(n_vars):
            for j in range(i + 1, n_vars):
                if (i, j) not in current_error_corrs:
                    # Calculate derivative of sigma with respect to error correlation
                    d_sigma = np.zeros((n_vars, n_vars))
                    d_sigma[i, j] = 1.0
                    d_sigma[j, i] = 1.0

                    # Calculate modification index using simpler approach
                    # Score function: tr(inv_sigma * residual * inv_sigma * d_sigma)
                    score_matrix = inv_sigma @ residual_matrix @ inv_sigma @ d_sigma
                    score = np.trace(score_matrix)

                    # Information matrix: tr(inv_sigma * d_sigma * inv_sigma * d_sigma)
                    info_matrix = inv_sigma @ d_sigma @ inv_sigma @ d_sigma
                    information = np.trace(info_matrix)

                    if information > 1e-10:
                        mi = (n_obs - 1) * (score ** 2) / information
                        epc = score / information if information > 0 else 0.0
                    else:
                        mi = 0.0
                        epc = 0.0

                    # Include all modification indices above a very small threshold
                    if mi > 0.01:  # Lower threshold to capture more indices
                        modification_indices.append({
                            'parameter': f'Error correlation: Var{i+1} <-> Var{j+1}',
                            'modification_index': float(mi),
                            'expected_parameter_change': float(epc),
                            'parameter_type': 'error_correlation'
                        })


        # Sort by modification index (descending)
        modification_indices.sort(key=lambda x: x['modification_index'], reverse=True)



        return modification_indices

    except Exception as e:
        print(f"Error calculating modification indices: {e}")
        import traceback
        traceback.print_exc()
        return []

def run_cfa_analysis(data_dict):
    """Main CFA analysis function"""
    try:
        # Extract parameters
        variables = data_dict.get('variables', {})
        variable_names = data_dict.get('variable_names', list(variables.keys()))
        model_spec = data_dict.get('model_specification', {})
        estimation_method = data_dict.get('estimation_method', 'ml')
        significance_level = data_dict.get('significance_level', 0.05)
        
        print(f"Running CFA with {len(variables)} variables and {len(model_spec.get('factors', []))} factors")
        
        # Create data matrix and calculate sample covariance
        n_vars = len(variable_names)
        n_obs = len(next(iter(variables.values())))
        
        X = np.array([variables[name] for name in variable_names]).T
        
        # Standardize data
        X_std = (X - np.mean(X, axis=0)) / np.std(X, axis=0, ddof=1)
        
        # Calculate sample covariance matrix
        sample_cov = np.cov(X_std.T, ddof=1)
        
        # Create model matrices
        model_matrices = create_model_matrices(model_spec, variable_names)
        
        # Estimate model
        fitted_params, fit_value, converged, iterations = estimate_cfa_model(
            sample_cov, model_matrices, estimation_method, n_obs
        )
        
        # Calculate standard errors
        standard_errors, info_matrix = calculate_standard_errors(
            fitted_params, sample_cov, model_matrices, n_obs
        )
        
        # Calculate fit indices
        fit_indices = calculate_fit_indices(
            sample_cov, fitted_params, model_matrices, n_obs, estimation_method
        )
        
        # Extract path coefficients
        factor_loadings, factor_correlations = extract_path_coefficients(
            fitted_params, standard_errors, model_matrices, model_spec, variable_names
        )
        
        # Calculate modification indices
        modification_indices = calculate_modification_indices(
            fitted_params, sample_cov, model_matrices, n_obs
        )
        
        # Calculate residuals
        sigma, _, _, _ = calculate_covariance_matrix(fitted_params, model_matrices)
        residuals = sample_cov - sigma
        
        # Standardized residuals
        diag_sample = np.sqrt(np.diag(sample_cov))
        standardized_residuals = residuals / np.outer(diag_sample, diag_sample)
        
        # Prepare results
        results = {
            'fit_indices': fit_indices,
            'factor_loadings': factor_loadings,
            'factor_correlations': factor_correlations,
            'modification_indices': modification_indices,
            'residual_correlations': residuals.tolist(),
            'standardized_residuals': standardized_residuals.tolist(),
            'n_factors': len(model_spec['factors']),
            'n_observed_variables': n_vars,
            'n_parameters': len(fitted_params),
            'n_observations': n_obs,
            'degrees_of_freedom': fit_indices['degrees_of_freedom'],
            'estimation_method': estimation_method,
            'converged': converged,
            'iterations': iterations
        }
        
        return json.dumps(results)
        
    except Exception as e:
        error_msg = f'CFA analysis failed: {str(e)}'
        print("Error:", error_msg)
        import traceback
        traceback.print_exc()
        return json.dumps({'error': error_msg})
    `);
  }

  async runCFA(data: CFAData): Promise<CFAResults> {
    await this.initialize();

    const pythonData = {
      variables: data.variables,
      variable_names: data.variable_names,
      model_specification: data.model_specification,
      estimation_method: data.estimation_method || 'ml',
      significance_level: data.significance_level || 0.05
    };

    console.log('Running CFA with:', {
      nVariables: Object.keys(data.variables).length,
      nObservations: data.variables[Object.keys(data.variables)[0]]?.length || 0,
      nFactors: data.model_specification.factors.length,
      estimationMethod: pythonData.estimation_method
    });

    this.pyodide.globals.set('cfa_data', this.pyodide.toPy(pythonData));

    const resultJson = this.pyodide.runPython(`run_cfa_analysis(cfa_data)`);
    const result = JSON.parse(resultJson);

    if (result.error) {
      throw new Error(`CFA analysis failed: ${result.error}`);
    }

    return result;
  }

  async calculateModificationIndices(
    cfaResults: CFAResults,
    data: CFAData
  ): Promise<ModificationIndex[]> {
    await this.initialize();

    // In a real implementation, this would calculate modification indices
    // for parameters not currently in the model
    try {
      return cfaResults.modification_indices || [];
    } catch (error) {
      console.error('Error calculating modification indices:', error);
      return [];
    }
  }

  async evaluateModelFit(fitIndices: FitIndices): Promise<{
    overall: 'excellent' | 'good' | 'acceptable' | 'poor';
    details: string[];
  }> {
    const details: string[] = [];
    let excellentCount = 0;
    let acceptableCount = 0;

    // CFI evaluation
    if (fitIndices.cfi >= 0.95) {
      details.push('CFI indicates excellent fit (≥ 0.95)');
      excellentCount++;
    } else if (fitIndices.cfi >= 0.90) {
      details.push('CFI indicates acceptable fit (≥ 0.90)');
      acceptableCount++;
    } else {
      details.push('CFI indicates poor fit (< 0.90)');
    }

    // TLI evaluation
    if (fitIndices.tli >= 0.95) {
      details.push('TLI indicates excellent fit (≥ 0.95)');
      excellentCount++;
    } else if (fitIndices.tli >= 0.90) {
      details.push('TLI indicates acceptable fit (≥ 0.90)');
      acceptableCount++;
    } else {
      details.push('TLI indicates poor fit (< 0.90)');
    }

    // RMSEA evaluation
    if (fitIndices.rmsea <= 0.05) {
      details.push('RMSEA indicates excellent fit (≤ 0.05)');
      excellentCount++;
    } else if (fitIndices.rmsea <= 0.08) {
      details.push('RMSEA indicates acceptable fit (≤ 0.08)');
      acceptableCount++;
    } else if (fitIndices.rmsea <= 0.10) {
      details.push('RMSEA indicates mediocre fit (≤ 0.10)');
    } else {
      details.push('RMSEA indicates poor fit (> 0.10)');
    }

    // SRMR evaluation
    if (fitIndices.srmr <= 0.05) {
      details.push('SRMR indicates excellent fit (≤ 0.05)');
      excellentCount++;
    } else if (fitIndices.srmr <= 0.08) {
      details.push('SRMR indicates acceptable fit (≤ 0.08)');
      acceptableCount++;
    } else {
      details.push('SRMR indicates poor fit (> 0.08)');
    }

    // Chi-square evaluation
    if (fitIndices.p_value > 0.05) {
      details.push('Chi-square test indicates good fit (p > 0.05)');
      excellentCount++;
    } else {
      details.push('Chi-square test indicates poor fit (p ≤ 0.05), but may be due to large sample size');
    }

    // Overall assessment
    let overall: 'excellent' | 'good' | 'acceptable' | 'poor';
    if (excellentCount >= 3) {
      overall = 'excellent';
    } else if (excellentCount >= 2 || acceptableCount >= 3) {
      overall = 'good';
    } else if (acceptableCount >= 2) {
      overall = 'acceptable';
    } else {
      overall = 'poor';
    }

    return { overall, details };
  }

  isReady(): boolean {
    return this.isInitialized;
  }
}

export const confirmatoryFactorAnalysisService = new ConfirmatoryFactorAnalysisService();
export type { 
  CFAResults, 
  CFAData, 
  CFAModelSpecification, 
  FitIndices, 
  PathCoefficient, 
  ModificationIndex 
};
