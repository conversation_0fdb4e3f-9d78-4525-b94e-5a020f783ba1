import { supabase } from '../supabaseClient';
import { Column } from '../../types';

// Define the type for the dataset content, including data and variable info
export interface DatasetContent {
  data: any[]; // The actual dataset rows
  variableInfo: Column[]; // Column definitions with type, role, etc.
}

// Define the type for a saved dataset entry
export interface SavedDataset {
  id: string;
  user_id: string;
  dataset_name: string;
  file_path: string; // Store the file path in the storage bucket
  created_at: string;
  updated_at: string;
}

const MAX_DATASET_SIZE_BYTES = 2 * 1024 * 1024; // 2MB
const DATASET_BUCKET_NAME = 'userdatasets'; // Define your Supabase storage bucket name

/**
 * Saves a dataset for the current user to Supabase Storage.
 * Enforces a 2MB size limit per file and a limit of 2 datasets per user.
 * @param datasetName The name for the dataset.
 * @param datasetContent The dataset content including data and variable info.
 * @returns The saved dataset metadata or an error.
 */
export const saveDataset = async (datasetName: string, datasetContent: DatasetContent): Promise<{ data: SavedDataset | null; error: any }> => {
  // Get the current session and user
  const { data: sessionData } = await supabase.auth.getSession();
  const { data: userData } = await supabase.auth.getUser();

  const user = userData?.user;

  if (!user) {
    return { data: null, error: new Error('User not authenticated.') };
  }

  const contentString = JSON.stringify(datasetContent);
  const contentBlob = new Blob([contentString], { type: 'application/json' });

  // Frontend size check
  if (contentBlob.size > MAX_DATASET_SIZE_BYTES) {
    return { data: null, error: new Error('Dataset size exceeds the 2MB limit.') };
  }

  // Frontend check for dataset count
  const { data: existingDatasets, error: listError } = await listDatasets();
  if (listError) {
    return { data: null, error: listError };
  }
  if (existingDatasets && existingDatasets.length >= 2) {
    // Check if a dataset with the same name already exists for update
    const existing = existingDatasets.find(ds => ds.dataset_name === datasetName);
    if (!existing) {
       return { data: null, error: new Error('You have reached the maximum limit of 2 saved datasets.') };
    }
  }

  const filePath = `${user.id}/${datasetName}.json`;

  try {
    // Check if a dataset with the same name already exists for this user in the metadata table
    const { data: existingMetadata, error: fetchError } = await supabase
      .from('user_datasets')
      .select('id, file_path')
      .eq('user_id', user.id)
      .eq('dataset_name', datasetName)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 means no rows found
       return { data: null, error: fetchError };
    }

    if (existingMetadata) {
      // Update existing dataset file in storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(DATASET_BUCKET_NAME)
        .upload(filePath, contentBlob, {
          cacheControl: '3600',
          upsert: true, // Use upsert to overwrite if file exists
        });

      if (uploadError) {
        console.error('Error uploading dataset file for update:', uploadError);
        return { data: null, error: uploadError };
      }

      // Update metadata in the database
      const { data, error } = await supabase
        .from('user_datasets')
        .update({
          updated_at: new Date().toISOString(),
          file_path: filePath // Ensure file_path is correct in case of upsert
        })
        .eq('id', existingMetadata.id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating dataset metadata:', error);
      }

      return { data, error };

    } else {
      // Insert new dataset file into storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(DATASET_BUCKET_NAME)
        .upload(filePath, contentBlob, {
          cacheControl: '3600',
          upsert: false, // Do not upsert for new files
        });

      if (uploadError) {
        console.error('Error uploading new dataset file:', uploadError);
        return { data: null, error: uploadError };
      }

      // Insert new metadata into the database
      const { data, error } = await supabase
        .from('user_datasets')
        .insert([{
          user_id: user.id,
          dataset_name: datasetName,
          file_path: filePath // Store the file path
        }])
        .select()
        .single();

      if (error) {
        console.error('Error inserting new dataset metadata:', error);
      }

      return { data, error };
    }
  } catch (e) {
    console.error('Unexpected error in saveDataset:', e);
    return { data: null, error: e };
  }
};

/**
 * Loads a specific dataset for the current user from Supabase Storage.
 * @param datasetId The ID of the dataset to load.
 * @returns The dataset content or an error.
 */
export const loadDataset = async (datasetId: string): Promise<{ data: DatasetContent | null; error: any }> => {
  const { data: userData } = await supabase.auth.getUser();
  const user = userData?.user;

  if (!user) {
    return { data: null, error: new Error('User not authenticated.') };
  }

  try {
    // Fetch the file path from the metadata table
    const { data: metadata, error: fetchError } = await supabase
      .from('user_datasets')
      .select('file_path')
      .eq('user_id', user.id)
      .eq('id', datasetId)
      .single();

    if (fetchError) {
      console.error('Error fetching dataset metadata for loading:', fetchError);
      return { data: null, error: fetchError };
    }

    if (!metadata?.file_path) {
      return { data: null, error: new Error('Dataset file path not found.') };
    }

    // Download the file content from storage
    const { data: fileData, error: downloadError } = await supabase.storage
      .from(DATASET_BUCKET_NAME)
      .download(metadata.file_path);

    if (downloadError) {
      console.error('Error downloading dataset file:', downloadError);
      return { data: null, error: downloadError };
    }

    if (!fileData) {
        return { data: null, error: new Error('Dataset file not found in storage.') };
    }

    // Read the file content as text and parse JSON
    const contentString = await fileData.text();
    const datasetContent: DatasetContent = JSON.parse(contentString);

    return { data: datasetContent, error: null };

  } catch (e) {
    console.error('Unexpected error in loadDataset:', e);
    return { data: null, error: e };
  }
};

/**
 * Lists all saved datasets for the current user from the metadata table.
 * @returns An array of saved dataset entries (id and name) or an error.
 */
export const listDatasets = async (): Promise<{ data: { id: string; dataset_name: string }[] | null; error: any }> => {
  const { data: userData } = await supabase.auth.getUser();
  const user = userData?.user;

  if (!user) {
    return { data: null, error: new Error('User not authenticated.') };
  }

  try {
    // Query the metadata table
    const { data, error } = await supabase
      .from('user_datasets')
      .select('id, dataset_name')
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error listing datasets:', error);
    }

    return { data, error };
  } catch (e) {
    console.error('Unexpected error in listDatasets:', e);
    return { data: null, error: e };
  }
};

/**
 * Renames a specific dataset for the current user in Supabase metadata table.
 * @param datasetId The ID of the dataset to rename.
 * @param newName The new name for the dataset.
 * @returns The updated dataset metadata or an error.
 */
export const renameDataset = async (datasetId: string, newName: string): Promise<{ data: SavedDataset | null; error: any }> => {
  const { data: userData } = await supabase.auth.getUser();
  const user = userData?.user;

  if (!user) {
    return { data: null, error: new Error('User not authenticated.') };
  }

  if (!newName.trim()) {
    return { data: null, error: new Error('Dataset name cannot be empty.') };
  }

  try {
    // Check if a dataset with the new name already exists for this user
    const { data: existingDataset, error: checkError } = await supabase
      .from('user_datasets')
      .select('id')
      .eq('user_id', user.id)
      .eq('dataset_name', newName.trim())
      .neq('id', datasetId) // Exclude the current dataset being renamed
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 means no rows found
      console.error('Error checking for duplicate dataset name:', checkError);
      return { data: null, error: checkError };
    }

    if (existingDataset) {
      return { data: null, error: new Error('A dataset with this name already exists.') };
    }

    // Update the dataset name in the metadata table
    const { data, error } = await supabase
      .from('user_datasets')
      .update({
        dataset_name: newName.trim(),
        updated_at: new Date().toISOString()
      })
      .eq('id', datasetId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) {
      console.error('Error renaming dataset:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (e) {
    console.error('Unexpected error in renameDataset:', e);
    return { data: null, error: e };
  }
};

/**
 * Deletes a specific dataset for the current user from Supabase Storage and metadata table.
 * @param datasetId The ID of the dataset to delete.
 * @returns Null on success or an error.
 */
export const deleteDataset = async (datasetId: string): Promise<{ error: any }> => {
  const { data: userData } = await supabase.auth.getUser();
  const user = userData?.user;

  if (!user) {
    return { error: new Error('User not authenticated.') };
  }

  try {
    // Fetch the file path from the metadata table
    const { data: metadata, error: fetchError } = await supabase
      .from('user_datasets')
      .select('file_path')
      .eq('user_id', user.id)
      .eq('id', datasetId)
      .single();

    if (fetchError) {
      console.error('Error fetching dataset metadata for deletion:', fetchError);
      return { error: fetchError };
    }

    if (metadata?.file_path) {
        // Delete the file from storage
        const { error: deleteFileError } = await supabase.storage
            .from(DATASET_BUCKET_NAME)
            .remove([metadata.file_path]);

        if (deleteFileError) {
            console.error('Error deleting dataset file from storage:', deleteFileError);
            // Continue to delete metadata even if file deletion fails
        }
    }


    // Delete the record from the metadata table
    const { error } = await supabase
      .from('user_datasets')
      .delete()
      .eq('user_id', user.id)
      .eq('id', datasetId);

    if (error) {
      console.error('Error deleting dataset metadata:', error);
    }

    return { error };
  } catch (e) {
    console.error('Unexpected error in deleteDataset:', e);
    return { error: e };
  }
};
