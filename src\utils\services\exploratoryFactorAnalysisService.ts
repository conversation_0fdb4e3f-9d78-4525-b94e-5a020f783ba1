// src/utils/services/exploratoryFactorAnalysisService.ts

interface FactorAnalysisData {
  variables: { [key: string]: number[] };
  variable_names: string[];
}

interface FactorAnalysisResults {
  loadings: number[][];
  eigenvalues: number[];
  variance_explained: number[];
  cumulative_variance: number[];
  communalities: { [key: string]: number };
  factor_correlations?: number[][];
  kmo: number;
  bartlett_chi_square: number;
  bartlett_p_value: number;
  n_factors: number;
  rotation_method: string;
  extraction_method: string;
  rotated_loadings?: number[][];
  correlation_matrix: number[][];
  n_observations: number;
}

interface FactorScores {
  scores: number[][];
  method: string;
}

class ExploratoryFactorAnalysisService {
  private pyodide: any = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    if (this.initializationPromise) return this.initializationPromise;

    this.initializationPromise = this.doInitialize();
    await this.initializationPromise;
  }

  private async doInitialize(): Promise<void> {
    try {
      // Load Pyodide from CDN
      if (typeof window !== 'undefined' && !(window as any).loadPyodide) {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/pyodide/v0.25.0/full/pyodide.js';
        document.head.appendChild(script);
        
        await new Promise((resolve, reject) => {
          script.onload = resolve;
          script.onerror = reject;
        });
      }

      // Initialize Pyodide
      this.pyodide = await (window as any).loadPyodide({
        indexURL: 'https://cdn.jsdelivr.net/pyodide/v0.25.0/full/'
      });

      // Install required packages
      await this.pyodide.loadPackage(['numpy', 'scipy']);
      
      console.log('Pyodide initialized for Factor Analysis');
      this.setupFactorAnalysisImplementation();
      
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Pyodide:', error);
      throw new Error('Failed to initialize Python environment for factor analysis');
    }
  }

  private setupFactorAnalysisImplementation(): void {
    this.pyodide.runPython(`
import numpy as np
import scipy.stats as stats
import scipy.linalg as linalg
from scipy.optimize import minimize
import json
import warnings
warnings.filterwarnings('ignore')

def calculate_correlation_matrix(data):
    """Calculate correlation matrix from data"""
    # Convert data to numpy array
    n_vars = len(data)
    n_obs = len(next(iter(data.values())))
    
    # Create data matrix
    X = np.array([data[key] for key in sorted(data.keys())]).T
    
    # Standardize data
    X_std = (X - np.mean(X, axis=0)) / np.std(X, axis=0, ddof=1)
    
    # Calculate correlation matrix
    corr_matrix = np.corrcoef(X_std.T)
    
    return corr_matrix, n_obs, X_std

def calculate_kmo_bartlett(corr_matrix, n_obs):
    """Calculate Kaiser-Meyer-Olkin (KMO) measure and Bartlett's test"""
    n_vars = corr_matrix.shape[0]
    
    # Calculate partial correlations
    try:
        inv_corr = np.linalg.inv(corr_matrix)
        partial_corr = -inv_corr / np.sqrt(np.outer(np.diag(inv_corr), np.diag(inv_corr)))
        np.fill_diagonal(partial_corr, 0)
    except:
        # If correlation matrix is singular, return poor KMO
        return 0.0, 0.0, 1.0
    
    # KMO calculation
    corr_sq = corr_matrix ** 2
    partial_corr_sq = partial_corr ** 2
    
    # Sum of squared correlations
    sum_corr_sq = np.sum(corr_sq) - np.trace(corr_sq)
    # Sum of squared partial correlations
    sum_partial_sq = np.sum(partial_corr_sq)
    
    # Overall KMO
    if sum_corr_sq + sum_partial_sq > 0:
        kmo = sum_corr_sq / (sum_corr_sq + sum_partial_sq)
    else:
        kmo = 0.0
    
    # Bartlett's test of sphericity
    det_corr = np.linalg.det(corr_matrix)
    if det_corr <= 0:
        bartlett_chi_square = 0.0
        bartlett_p_value = 1.0
    else:
        bartlett_chi_square = -(n_obs - 1 - (2 * n_vars + 5) / 6) * np.log(det_corr)
        df = n_vars * (n_vars - 1) / 2
        bartlett_p_value = 1 - stats.chi2.cdf(bartlett_chi_square, df)
    
    return kmo, bartlett_chi_square, bartlett_p_value

def extract_factors_pca(corr_matrix, n_factors):
    """Extract factors using Principal Component Analysis"""
    # Eigenvalue decomposition
    eigenvalues, eigenvectors = np.linalg.eigh(corr_matrix)
    
    # Sort in descending order
    idx = eigenvalues.argsort()[::-1]
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]
    
    # Select first n_factors
    loadings = eigenvectors[:, :n_factors] @ np.diag(np.sqrt(eigenvalues[:n_factors]))
    
    return loadings, eigenvalues

def extract_factors_ml(corr_matrix, n_factors, max_iter=100):
    """Extract factors using Maximum Likelihood"""
    n_vars = corr_matrix.shape[0]
    
    # Initialize with PCA solution
    loadings_pca, _ = extract_factors_pca(corr_matrix, n_factors)
    
    # ML estimation using iterative approach
    uniqueness = 1 - np.sum(loadings_pca ** 2, axis=1)
    uniqueness = np.clip(uniqueness, 0.001, 0.999)
    
    for iteration in range(max_iter):
        # Update reduced correlation matrix
        psi = np.diag(uniqueness)
        reduced_corr = corr_matrix - psi
        
        # Extract factors
        try:
            eigenvalues, eigenvectors = np.linalg.eigh(reduced_corr)
            idx = eigenvalues.argsort()[::-1]
            eigenvalues = eigenvalues[idx]
            eigenvectors = eigenvectors[:, idx]
            
            # Keep only positive eigenvalues
            pos_idx = eigenvalues > 0
            if np.sum(pos_idx) < n_factors:
                break
                
            loadings = eigenvectors[:, :n_factors] @ np.diag(np.sqrt(eigenvalues[:n_factors]))
            
            # Update uniqueness
            new_uniqueness = 1 - np.sum(loadings ** 2, axis=1)
            new_uniqueness = np.clip(new_uniqueness, 0.001, 0.999)
            
            # Check convergence
            if np.max(np.abs(new_uniqueness - uniqueness)) < 0.001:
                break
                
            uniqueness = new_uniqueness
            
        except:
            break
    
    # Calculate eigenvalues for the final solution
    eigenvalues, _ = np.linalg.eigh(corr_matrix)
    eigenvalues = np.sort(eigenvalues)[::-1]
    
    return loadings, eigenvalues

def rotate_varimax(loadings, gamma=1.0, max_iter=100, tol=1e-6):
    """Varimax rotation (orthogonal)"""
    n_vars, n_factors = loadings.shape
    rotation_matrix = np.eye(n_factors)
    
    for _ in range(max_iter):
        old_rotation = rotation_matrix.copy()
        
        for i in range(n_factors - 1):
            for j in range(i + 1, n_factors):
                # Extract columns i and j
                u = loadings @ rotation_matrix[:, [i, j]]
                
                # Calculate rotation angle
                if gamma == 0:  # Quartimax
                    u_squared = u ** 2
                    numerator = 2 * np.sum(u[:, 0] * u[:, 1] * (u_squared[:, 0] - u_squared[:, 1]))
                    denominator = np.sum((u_squared[:, 0] - u_squared[:, 1]) ** 2)
                else:  # Varimax
                    u_squared = u ** 2
                    sum_u2 = np.sum(u_squared, axis=0)
                    numerator = np.sum(u[:, 0] * u[:, 1]) * n_vars - gamma * sum_u2[0] * sum_u2[1] / n_vars
                    denominator = (np.sum(u_squared[:, 0] ** 2) - gamma * sum_u2[0] ** 2 / n_vars - 
                                 np.sum(u_squared[:, 1] ** 2) + gamma * sum_u2[1] ** 2 / n_vars)
                
                # Calculate angle
                if abs(denominator) < 1e-10:
                    angle = 0
                else:
                    angle = 0.25 * np.arctan2(2 * numerator, denominator)
                
                # Apply rotation
                cos_angle = np.cos(angle)
                sin_angle = np.sin(angle)
                rotation_matrix[:, i], rotation_matrix[:, j] = (
                    cos_angle * rotation_matrix[:, i] + sin_angle * rotation_matrix[:, j],
                    -sin_angle * rotation_matrix[:, i] + cos_angle * rotation_matrix[:, j]
                )
        
        # Check convergence
        if np.max(np.abs(rotation_matrix - old_rotation)) < tol:
            break
    
    rotated_loadings = loadings @ rotation_matrix
    return rotated_loadings, rotation_matrix

def rotate_promax(loadings, power=4):
    """Promax rotation (oblique)"""
    # Start with varimax rotation
    varimax_loadings, _ = rotate_varimax(loadings)
    
    # Raise to power and normalize
    target = np.sign(varimax_loadings) * np.abs(varimax_loadings) ** power
    
    # Find transformation matrix
    try:
        transformation = np.linalg.lstsq(loadings, target, rcond=None)[0]
        inv_transformation = np.linalg.inv(transformation.T @ transformation)
        pattern_matrix = loadings @ transformation @ inv_transformation
        
        # Factor correlations
        factor_corr = inv_transformation @ transformation.T @ transformation @ inv_transformation
        
        # Normalize to get correlation matrix
        d = np.sqrt(np.diag(factor_corr))
        factor_corr = factor_corr / np.outer(d, d)
        
        return pattern_matrix, factor_corr
    except:
        # If transformation fails, return varimax solution
        return varimax_loadings, np.eye(loadings.shape[1])

def rotate_oblimin(loadings, delta=0):
    """Oblimin rotation (oblique)"""
    n_vars, n_factors = loadings.shape
    
    # Initialize with random rotation
    rotation_matrix = np.linalg.qr(np.random.randn(n_factors, n_factors))[0]
    
    max_iter = 100
    tol = 1e-6
    
    for _ in range(max_iter):
        old_rotation = rotation_matrix.copy()
        
        # Calculate gradient
        rotated = loadings @ rotation_matrix
        gradient = rotated.T @ (rotated ** 3) - delta * rotated.T @ rotated @ np.diag(np.sum(rotated ** 2, axis=0))
        
        # Update rotation matrix
        u, s, vt = np.linalg.svd(gradient)
        rotation_matrix = u @ vt
        
        # Check convergence
        if np.max(np.abs(rotation_matrix - old_rotation)) < tol:
            break
    
    # Calculate pattern matrix
    pattern_matrix = loadings @ rotation_matrix
    
    # Calculate factor correlations
    try:
        structure_matrix = loadings @ rotation_matrix
        factor_corr = np.corrcoef(structure_matrix.T)
    except:
        factor_corr = np.eye(n_factors)
    
    return pattern_matrix, factor_corr

def calculate_communalities(loadings):
    """Calculate communalities from loadings"""
    return np.sum(loadings ** 2, axis=1)

def determine_n_factors(eigenvalues, method='kaiser'):
    """Determine number of factors to extract"""
    if method == 'kaiser':
        return np.sum(eigenvalues > 1)
    else:  # scree
        # Simple scree test - look for elbow
        if len(eigenvalues) < 3:
            return 1
        
        # Calculate differences
        diffs = np.diff(eigenvalues)
        second_diffs = np.diff(diffs)
        
        # Find elbow point (where second derivative is maximum)
        elbow = np.argmax(second_diffs) + 2
        
        return min(elbow, len(eigenvalues) // 2)

def run_factor_analysis(data_dict):
    """Main factor analysis function"""
    try:
        # Extract parameters
        variables = data_dict.get('variables', {})
        variable_names = data_dict.get('variable_names', list(variables.keys()))
        n_factors = data_dict.get('n_factors', None)
        extraction_method = data_dict.get('extraction_method', 'pca')
        rotation_method = data_dict.get('rotation_method', 'varimax')
        factor_selection = data_dict.get('factor_selection', 'kaiser')
        min_loading = data_dict.get('min_loading', 0.0)
        
        print(f"Running factor analysis with {len(variables)} variables")
        
        # Calculate correlation matrix
        corr_matrix, n_obs, X_std = calculate_correlation_matrix(variables)
        
        # Calculate KMO and Bartlett's test
        kmo, bartlett_chi, bartlett_p = calculate_kmo_bartlett(corr_matrix, n_obs)
        
        # Extract factors
        if extraction_method == 'ml':
            loadings, eigenvalues = extract_factors_ml(corr_matrix, n_factors or 3)
        else:  # pca
            loadings, eigenvalues = extract_factors_pca(corr_matrix, n_factors or len(variables))
        
        # Determine number of factors if not specified
        if n_factors is None:
            n_factors = determine_n_factors(eigenvalues, factor_selection)
            loadings = loadings[:, :n_factors]
        
        # Apply rotation
        factor_correlations = None
        if n_factors > 1:
            if rotation_method == 'varimax':
                rotated_loadings, _ = rotate_varimax(loadings)
            elif rotation_method == 'promax':
                rotated_loadings, factor_correlations = rotate_promax(loadings)
            elif rotation_method == 'oblimin':
                rotated_loadings, factor_correlations = rotate_oblimin(loadings)
            else:
                rotated_loadings = loadings
        else:
            rotated_loadings = loadings
        
        # Apply minimum loading threshold
        if min_loading > 0:
            display_loadings = np.where(np.abs(rotated_loadings) < min_loading, 0, rotated_loadings)
        else:
            display_loadings = rotated_loadings
        
        # Calculate communalities
        communalities = calculate_communalities(rotated_loadings)
        communalities_dict = {name: float(comm) for name, comm in zip(variable_names, communalities)}
        
        # Calculate variance explained
        total_variance = np.sum(eigenvalues)
        variance_explained = eigenvalues / total_variance * 100
        cumulative_variance = np.cumsum(variance_explained)
        
        # Prepare results
        results = {
            'loadings': loadings.tolist(),
            'rotated_loadings': display_loadings.tolist(),
            'eigenvalues': eigenvalues.tolist(),
            'variance_explained': variance_explained.tolist(),
            'cumulative_variance': cumulative_variance.tolist(),
            'communalities': communalities_dict,
            'kmo': float(kmo),
            'bartlett_chi_square': float(bartlett_chi),
            'bartlett_p_value': float(bartlett_p),
            'n_factors': int(n_factors),
            'rotation_method': rotation_method,
            'extraction_method': extraction_method,
            'correlation_matrix': corr_matrix.tolist(),
            'n_observations': int(n_obs)
        }
        
        if factor_correlations is not None:
            results['factor_correlations'] = factor_correlations.tolist()
        
        return json.dumps(results)
        
    except Exception as e:
        error_msg = f'Factor analysis failed: {str(e)}'
        print("Error:", error_msg)
        import traceback
        traceback.print_exc()
        return json.dumps({'error': error_msg})

def calculate_factor_scores(factor_results_dict, data_dict):
    """Calculate factor scores using regression method"""
    try:
        # Extract data
        variables = data_dict.get('variables', {})
        rotated_loadings = np.array(factor_results_dict.get('rotated_loadings', []))
        corr_matrix = np.array(factor_results_dict.get('correlation_matrix', []))
        
        # Create data matrix
        X = np.array([variables[key] for key in sorted(variables.keys())]).T
        
        # Standardize data
        X_std = (X - np.mean(X, axis=0)) / np.std(X, axis=0, ddof=1)
        
        # Calculate factor score coefficients using regression method
        # F = (L'R^-1L)^-1 L'R^-1
        try:
            inv_corr = np.linalg.inv(corr_matrix)
            factor_score_coef = np.linalg.inv(rotated_loadings.T @ inv_corr @ rotated_loadings) @ rotated_loadings.T @ inv_corr
        except:
            # If matrix is singular, use pseudo-inverse
            factor_score_coef = np.linalg.pinv(rotated_loadings.T @ rotated_loadings) @ rotated_loadings.T
        
        # Calculate factor scores
        scores = X_std @ factor_score_coef.T
        
        results = {
            'scores': scores.tolist(),
            'method': 'regression'
        }
        
        return json.dumps(results)
        
    except Exception as e:
        error_msg = f'Factor score calculation failed: {str(e)}'
        print("Error:", error_msg)
        import traceback
        traceback.print_exc()
        return json.dumps({'error': error_msg})
    `);
  }

  async runFactorAnalysis(data: FactorAnalysisData & {
    n_factors?: number;
    extraction_method?: string;
    rotation_method?: string;
    factor_selection?: string;
    min_loading?: number;
  }): Promise<FactorAnalysisResults> {
    await this.initialize();

    const pythonData = {
      variables: data.variables,
      variable_names: data.variable_names,
      n_factors: data.n_factors,
      extraction_method: data.extraction_method || 'pca',
      rotation_method: data.rotation_method || 'varimax',
      factor_selection: data.factor_selection || 'kaiser',
      min_loading: data.min_loading || 0
    };

    console.log('Running factor analysis with:', {
      nVariables: Object.keys(data.variables).length,
      nObservations: data.variables[Object.keys(data.variables)[0]]?.length || 0,
      extractionMethod: pythonData.extraction_method,
      rotationMethod: pythonData.rotation_method
    });

    this.pyodide.globals.set('factor_data', this.pyodide.toPy(pythonData));

    const resultJson = this.pyodide.runPython(`run_factor_analysis(factor_data)`);
    const result = JSON.parse(resultJson);

    if (result.error) {
      throw new Error(`Factor analysis failed: ${result.error}`);
    }

    return result;
  }

  async calculateFactorScores(
    factorResults: FactorAnalysisResults,
    data: FactorAnalysisData
  ): Promise<FactorScores> {
    await this.initialize();

    this.pyodide.globals.set('factor_results', this.pyodide.toPy(factorResults));
    this.pyodide.globals.set('score_data', this.pyodide.toPy(data));

    const resultJson = this.pyodide.runPython(`calculate_factor_scores(factor_results, score_data)`);
    const result = JSON.parse(resultJson);

    if (result.error) {
      throw new Error(`Factor score calculation failed: ${result.error}`);
    }

    return result;
  }

  isReady(): boolean {
    return this.isInitialized;
  }
}

export const exploratoryFactorAnalysisService = new ExploratoryFactorAnalysisService();
export type { FactorAnalysisResults, FactorScores, FactorAnalysisData };
