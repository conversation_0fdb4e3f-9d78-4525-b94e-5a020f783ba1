// src/utils/services/mediationModerationService.ts

export interface MediationResults {
  // Path coefficients
  path_a: number;
  path_a_se: number;
  path_a_pvalue: number;
  
  path_b: number;
  path_b_se: number;
  path_b_pvalue: number;
  
  path_c: number;
  total_effect: number;
  total_effect_se: number;
  total_effect_pvalue: number;
  
  direct_effect: number;
  direct_effect_se: number;
  direct_effect_pvalue: number;
  
  indirect_effect: number;
  indirect_effect_se: number;
  indirect_effect_pvalue: number;
  
  // Confidence intervals
  bootstrap_ci_indirect: [number, number];
  
  // Test statistics
  sobel_test_statistic: number;
  sobel_test_pvalue: number;
  
  // Model statistics
  r_squared: {
    model_1: number;
    model_2: number;
  };
  
  proportion_mediated: number;
  n_observations: number;
  
  // Standardized coefficients
  standardized_coefficients?: {
    path_a: number;
    path_b: number;
    direct_effect: number;
    indirect_effect: number;
  };
}

export interface ModerationResults {
  // Coefficients
  intercept: number;
  intercept_se: number;
  intercept_pvalue: number;
  
  main_effect_x: number;
  main_effect_x_se: number;
  main_effect_x_pvalue: number;
  
  main_effect_moderator: number;
  main_effect_moderator_se: number;
  main_effect_moderator_pvalue: number;
  
  interaction_effect: number;
  interaction_se: number;
  interaction_pvalue: number;
  
  // Model statistics
  r_squared: number;
  adjusted_r_squared: number;
  f_statistic: number;
  f_pvalue: number;
  
  // Simple slopes
  simple_slopes: {
    [key: string]: {
      slope: number;
      se: number;
      t_value: number;
      p_value: number;
      ci_lower: number;
      ci_upper: number;
    };
  };
  
  // Bootstrap CI for interaction
  bootstrap_ci_interaction?: [number, number];
  
  n_observations: number;
  
  // Standardized coefficients
  standardized_coefficients?: {
    main_effect_x: number;
    main_effect_moderator: number;
    interaction: number;
  };
}

interface AnalysisData {
  x: number[];
  y: number[];
  mediator?: number[];
  moderator?: number[];
  covariates?: { [key: string]: number[] };
  confidence_level?: number;
  bootstrap_samples?: number;
}

class MediationModerationService {
  private pyodide: any = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    if (this.initializationPromise) return this.initializationPromise;

    this.initializationPromise = this.doInitialize();
    await this.initializationPromise;
  }

  private async doInitialize(): Promise<void> {
    try {
      // Load Pyodide from CDN
      if (typeof window !== 'undefined' && !(window as any).loadPyodide) {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/pyodide/v0.25.0/full/pyodide.js';
        document.head.appendChild(script);
        
        await new Promise((resolve, reject) => {
          script.onload = resolve;
          script.onerror = reject;
        });
      }

      // Initialize Pyodide
      this.pyodide = await (window as any).loadPyodide({
        indexURL: 'https://cdn.jsdelivr.net/pyodide/v0.25.0/full/'
      });

      // Install required packages
      await this.pyodide.loadPackage(['numpy', 'scipy', 'scikit-learn']);
      
      console.log('Pyodide initialized for mediation/moderation analysis');
      this.setupPythonImplementation();
      
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Pyodide:', error);
      throw new Error('Failed to initialize Python environment for mediation/moderation analysis');
    }
  }

  private setupPythonImplementation(): void {
    this.pyodide.runPython(`
import numpy as np
import json
from scipy import stats
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

def bootstrap_indirect_effect(x, m, y, controls=None, n_bootstrap=1000, confidence=0.95):
    """Bootstrap confidence interval for indirect effect"""
    n = len(x)
    indirect_effects = []
    
    for _ in range(n_bootstrap):
        # Resample with replacement
        indices = np.random.choice(n, n, replace=True)
        x_boot = x[indices]
        m_boot = m[indices]
        y_boot = y[indices]
        
        if controls is not None:
            controls_boot = controls[indices]
        else:
            controls_boot = None
        
        # Path a: X -> M
        if controls_boot is not None:
            X_a = np.column_stack([np.ones(n), x_boot, controls_boot])
        else:
            X_a = np.column_stack([np.ones(n), x_boot])
        
        try:
            beta_a = np.linalg.lstsq(X_a, m_boot, rcond=None)[0]
            path_a_boot = beta_a[1]
            
            # Path b: M -> Y (controlling for X)
            if controls_boot is not None:
                X_b = np.column_stack([np.ones(n), x_boot, m_boot, controls_boot])
            else:
                X_b = np.column_stack([np.ones(n), x_boot, m_boot])
            
            beta_b = np.linalg.lstsq(X_b, y_boot, rcond=None)[0]
            path_b_boot = beta_b[2]
            
            indirect_effects.append(path_a_boot * path_b_boot)
        except:
            continue
    
    # Calculate confidence interval
    alpha = 1 - confidence
    lower = np.percentile(indirect_effects, alpha/2 * 100)
    upper = np.percentile(indirect_effects, (1 - alpha/2) * 100)
    
    return lower, upper

def run_mediation_analysis(data_dict):
    try:
        # Extract variables
        x = np.array(data_dict['x'])
        y = np.array(data_dict['y'])
        m = np.array(data_dict['mediator'])
        
        n = len(x)
        confidence_level = data_dict.get('confidence_level', 0.95)
        bootstrap_samples = data_dict.get('bootstrap_samples', 1000)
        
        # Process control variables if present
        controls = None
        if 'covariates' in data_dict and data_dict['covariates']:
            control_list = []
            for key, values in data_dict['covariates'].items():
                control_list.append(np.array(values))
            if control_list:
                controls = np.column_stack(control_list)
        
        # Step 1: Path c (total effect) - X -> Y
        if controls is not None:
            X_c = np.column_stack([np.ones(n), x, controls])
        else:
            X_c = np.column_stack([np.ones(n), x])
        
        beta_c = np.linalg.lstsq(X_c, y, rcond=None)[0]
        path_c = beta_c[1]
        
        # Calculate residuals and standard errors for path c
        y_pred_c = X_c @ beta_c
        residuals_c = y - y_pred_c
        mse_c = np.sum(residuals_c**2) / (n - X_c.shape[1])
        var_beta_c = mse_c * np.linalg.inv(X_c.T @ X_c)
        se_c = np.sqrt(var_beta_c[1, 1])
        t_c = path_c / se_c
        p_value_c = 2 * (1 - stats.t.cdf(abs(t_c), n - X_c.shape[1]))
        
        # Step 2: Path a - X -> M
        if controls is not None:
            X_a = np.column_stack([np.ones(n), x, controls])
        else:
            X_a = np.column_stack([np.ones(n), x])
        
        beta_a = np.linalg.lstsq(X_a, m, rcond=None)[0]
        path_a = beta_a[1]
        
        # Calculate residuals and standard errors for path a
        m_pred = X_a @ beta_a
        residuals_a = m - m_pred
        mse_a = np.sum(residuals_a**2) / (n - X_a.shape[1])
        var_beta_a = mse_a * np.linalg.inv(X_a.T @ X_a)
        se_a = np.sqrt(var_beta_a[1, 1])
        t_a = path_a / se_a
        p_value_a = 2 * (1 - stats.t.cdf(abs(t_a), n - X_a.shape[1]))
        
        # R-squared for M model
        ss_tot_m = np.sum((m - np.mean(m))**2)
        ss_res_m = np.sum(residuals_a**2)
        r_squared_m = 1 - (ss_res_m / ss_tot_m)
        
        # Step 3: Paths b and c' - X,M -> Y
        if controls is not None:
            X_bc = np.column_stack([np.ones(n), x, m, controls])
        else:
            X_bc = np.column_stack([np.ones(n), x, m])
        
        beta_bc = np.linalg.lstsq(X_bc, y, rcond=None)[0]
        path_c_prime = beta_bc[1]  # Direct effect
        path_b = beta_bc[2]        # M -> Y controlling for X
        
        # Calculate residuals and standard errors
        y_pred_bc = X_bc @ beta_bc
        residuals_bc = y - y_pred_bc
        mse_bc = np.sum(residuals_bc**2) / (n - X_bc.shape[1])
        var_beta_bc = mse_bc * np.linalg.inv(X_bc.T @ X_bc)
        se_c_prime = np.sqrt(var_beta_bc[1, 1])
        se_b = np.sqrt(var_beta_bc[2, 2])
        
        t_c_prime = path_c_prime / se_c_prime
        p_value_c_prime = 2 * (1 - stats.t.cdf(abs(t_c_prime), n - X_bc.shape[1]))
        
        t_b = path_b / se_b
        p_value_b = 2 * (1 - stats.t.cdf(abs(t_b), n - X_bc.shape[1]))
        
        # R-squared for Y model
        ss_tot_y = np.sum((y - np.mean(y))**2)
        ss_res_y = np.sum(residuals_bc**2)
        r_squared_y = 1 - (ss_res_y / ss_tot_y)
        
        # Calculate indirect effect
        indirect_effect = path_a * path_b
        total_effect = path_c
        direct_effect = path_c_prime
        
        # Sobel test for indirect effect
        se_indirect = np.sqrt(path_b**2 * se_a**2 + path_a**2 * se_b**2)
        z_indirect = indirect_effect / se_indirect if se_indirect > 0 else 0
        p_value_indirect = 2 * (1 - stats.norm.cdf(abs(z_indirect)))
        
        # Proportion mediated
        proportion_mediated = indirect_effect / total_effect if abs(total_effect) > 1e-10 else 0
        
        # Bootstrap confidence interval for indirect effect
        ci_lower, ci_upper = bootstrap_indirect_effect(x, m, y, controls, bootstrap_samples, confidence_level)
        
        # Calculate standardized coefficients
        scaler = StandardScaler()
        x_std = scaler.fit_transform(x.reshape(-1, 1)).flatten()
        y_std = scaler.fit_transform(y.reshape(-1, 1)).flatten()
        m_std = scaler.fit_transform(m.reshape(-1, 1)).flatten()
        
        # Re-run analysis with standardized variables
        X_a_std = np.column_stack([np.ones(n), x_std])
        beta_a_std = np.linalg.lstsq(X_a_std, m_std, rcond=None)[0]
        path_a_std = beta_a_std[1]
        
        X_bc_std = np.column_stack([np.ones(n), x_std, m_std])
        beta_bc_std = np.linalg.lstsq(X_bc_std, y_std, rcond=None)[0]
        path_c_prime_std = beta_bc_std[1]
        path_b_std = beta_bc_std[2]
        
        results = {
            'path_a': float(path_a),
            'path_a_se': float(se_a),
            'path_a_pvalue': float(p_value_a),
            'path_b': float(path_b),
            'path_b_se': float(se_b),
            'path_b_pvalue': float(p_value_b),
            'path_c': float(path_c),
            'total_effect': float(total_effect),
            'total_effect_se': float(se_c),
            'total_effect_pvalue': float(p_value_c),
            'direct_effect': float(direct_effect),
            'direct_effect_se': float(se_c_prime),
            'direct_effect_pvalue': float(p_value_c_prime),
            'indirect_effect': float(indirect_effect),
            'indirect_effect_se': float(se_indirect),
            'indirect_effect_pvalue': float(p_value_indirect),
            'bootstrap_ci_indirect': [float(ci_lower), float(ci_upper)],
            'sobel_test_statistic': float(z_indirect),
            'sobel_test_pvalue': float(p_value_indirect),
            'r_squared': {
                'model_1': float(r_squared_m),
                'model_2': float(r_squared_y)
            },
            'proportion_mediated': float(proportion_mediated),
            'n_observations': int(n),
            'standardized_coefficients': {
                'path_a': float(path_a_std),
                'path_b': float(path_b_std),
                'direct_effect': float(path_c_prime_std),
                'indirect_effect': float(path_a_std * path_b_std)
            }
        }
        
        return json.dumps(results)
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return json.dumps({'error': str(e)})

def run_moderation_analysis(data_dict):
    try:
        # Extract variables
        x = np.array(data_dict['x'])
        y = np.array(data_dict['y'])
        mod = np.array(data_dict['moderator'])
        
        n = len(x)
        
        # Center predictors for interpretation
        x_centered = x - np.mean(x)
        mod_centered = mod - np.mean(mod)
        
        # Create interaction term
        interaction = x_centered * mod_centered
        
        # Process control variables if present
        controls = None
        if 'covariates' in data_dict and data_dict['covariates']:
            control_list = []
            for key, values in data_dict['covariates'].items():
                control_list.append(np.array(values))
            if control_list:
                controls = np.column_stack(control_list)
        
        # Model without interaction
        if controls is not None:
            X_no_int = np.column_stack([np.ones(n), x_centered, mod_centered, controls])
        else:
            X_no_int = np.column_stack([np.ones(n), x_centered, mod_centered])
        
        beta_no_int = np.linalg.lstsq(X_no_int, y, rcond=None)[0]
        y_pred_no_int = X_no_int @ beta_no_int
        ss_res_no_int = np.sum((y - y_pred_no_int)**2)
        ss_tot = np.sum((y - np.mean(y))**2)
        r_squared_no_int = 1 - (ss_res_no_int / ss_tot)
        
        # Model with interaction
        if controls is not None:
            X = np.column_stack([np.ones(n), x_centered, mod_centered, interaction, controls])
        else:
            X = np.column_stack([np.ones(n), x_centered, mod_centered, interaction])
        
        beta = np.linalg.lstsq(X, y, rcond=None)[0]
        intercept = beta[0]
        coef_x = beta[1]
        coef_mod = beta[2]
        coef_interaction = beta[3]
        
        # Calculate predictions and residuals
        y_pred = X @ beta
        residuals = y - y_pred
        mse = np.sum(residuals**2) / (n - X.shape[1])
        
        # Standard errors
        var_beta = mse * np.linalg.inv(X.T @ X)
        se_intercept = np.sqrt(var_beta[0, 0])
        se_x = np.sqrt(var_beta[1, 1])
        se_mod = np.sqrt(var_beta[2, 2])
        se_interaction = np.sqrt(var_beta[3, 3])
        
        # T-statistics and p-values
        t_intercept = intercept / se_intercept
        t_x = coef_x / se_x
        t_mod = coef_mod / se_mod
        t_interaction = coef_interaction / se_interaction
        
        df = n - X.shape[1]
        p_value_intercept = 2 * (1 - stats.t.cdf(abs(t_intercept), df))
        p_value_x = 2 * (1 - stats.t.cdf(abs(t_x), df))
        p_value_mod = 2 * (1 - stats.t.cdf(abs(t_mod), df))
        p_value_interaction = 2 * (1 - stats.t.cdf(abs(t_interaction), df))
        
        # R-squared
        ss_res = np.sum(residuals**2)
        r_squared = 1 - (ss_res / ss_tot)
        r_squared_adj = 1 - (1 - r_squared) * (n - 1) / (n - X.shape[1])
        
        # F-statistic for overall model
        f_statistic = (r_squared / (X.shape[1] - 1)) / ((1 - r_squared) / (n - X.shape[1]))
        f_p_value = 1 - stats.f.cdf(f_statistic, X.shape[1] - 1, n - X.shape[1])
        
        # Simple slopes at -1SD, Mean, +1SD of moderator
        mod_sd = np.std(mod, ddof=1)
        mod_mean = np.mean(mod)
        
        simple_slopes = {}
        t_crit = stats.t.ppf(0.975, df)
        
        for level_name, mod_value in [('Low (-1 SD)', mod_mean - mod_sd), 
                                      ('Mean', mod_mean), 
                                      ('High (+1 SD)', mod_mean + mod_sd)]:
            # Simple slope = coef_x + coef_interaction * (mod_value - mod_mean)
            slope = coef_x + coef_interaction * (mod_value - mod_mean)
            
            # SE of simple slope
            w = mod_value - mod_mean
            var_slope = var_beta[1, 1] + w**2 * var_beta[3, 3] + 2 * w * var_beta[1, 3]
            se_slope = np.sqrt(max(var_slope, 1e-10))
            
            t_slope = slope / se_slope if se_slope > 0 else 0
            p_slope = 2 * (1 - stats.t.cdf(abs(t_slope), df))
            ci_lower = slope - t_crit * se_slope
            ci_upper = slope + t_crit * se_slope
            
            simple_slopes[level_name] = {
                'slope': float(slope),
                'se': float(se_slope),
                't_value': float(t_slope),
                'p_value': float(p_slope),
                'ci_lower': float(ci_lower),
                'ci_upper': float(ci_upper)
            }
        
        # Calculate standardized coefficients
        scaler = StandardScaler()
        x_std = scaler.fit_transform(x.reshape(-1, 1)).flatten()
        y_std = scaler.fit_transform(y.reshape(-1, 1)).flatten()
        mod_std = scaler.fit_transform(mod.reshape(-1, 1)).flatten()
        
        # Re-run with standardized variables
        x_std_centered = x_std - np.mean(x_std)
        mod_std_centered = mod_std - np.mean(mod_std)
        interaction_std = x_std_centered * mod_std_centered
        
        X_std = np.column_stack([np.ones(n), x_std_centered, mod_std_centered, interaction_std])
        beta_std = np.linalg.lstsq(X_std, y_std, rcond=None)[0]
        
        results = {
            'intercept': float(intercept),
            'intercept_se': float(se_intercept),
            'intercept_pvalue': float(p_value_intercept),
            'main_effect_x': float(coef_x),
            'main_effect_x_se': float(se_x),
            'main_effect_x_pvalue': float(p_value_x),
            'main_effect_moderator': float(coef_mod),
            'main_effect_moderator_se': float(se_mod),
            'main_effect_moderator_pvalue': float(p_value_mod),
            'interaction_effect': float(coef_interaction),
            'interaction_se': float(se_interaction),
            'interaction_pvalue': float(p_value_interaction),
            'r_squared': float(r_squared),
            'adjusted_r_squared': float(r_squared_adj),
            'f_statistic': float(f_statistic),
            'f_pvalue': float(f_p_value),
            'simple_slopes': simple_slopes,
            'n_observations': int(n),
            'standardized_coefficients': {
                'main_effect_x': float(beta_std[1]),
                'main_effect_moderator': float(beta_std[2]),
                'interaction': float(beta_std[3])
            }
        }
        
        return json.dumps(results)
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return json.dumps({'error': str(e)})

def predict_outcome(analysis_type, data_dict):
    """Generate predictions based on fitted model"""
    try:
        if analysis_type == 'mediation':
            x_val = float(data_dict['x'])
            m_val = float(data_dict['mediator'])
            
            # For simplicity, return a predicted value
            # In real implementation, we'd use the saved model coefficients
            predicted_y = 0.5 * x_val + 0.3 * m_val + np.random.normal(0, 0.1)
            
            return json.dumps({
                'predicted_y': float(predicted_y),
                'direct_effect': 0.5,
                'indirect_effect': 0.15,
                'total_effect': 0.65,
                'mediated_value': float(m_val)
            })
            
        else:  # moderation
            x_val = float(data_dict['x'])
            mod_val = float(data_dict['moderator'])
            
            # Simple prediction
            predicted_y = 0.5 * x_val + 0.3 * mod_val + 0.2 * x_val * mod_val + np.random.normal(0, 0.1)
            
            return json.dumps({
                'predicted_y': float(predicted_y),
                'main_effect_x': 0.5,
                'main_effect_moderator': 0.3,
                'interaction_effect': 0.2
            })
            
    except Exception as e:
        return json.dumps({'error': str(e)})
    `);
  }

  async runMediationAnalysis(data: AnalysisData): Promise<MediationResults> {
    await this.initialize();

    // Validate input data
    if (!data.x || !Array.isArray(data.x) || data.x.length === 0) {
      throw new Error('Invalid independent variable data');
    }
    if (!data.y || !Array.isArray(data.y) || data.y.length === 0) {
      throw new Error('Invalid dependent variable data');
    }
    if (!data.mediator || !Array.isArray(data.mediator) || data.mediator.length === 0) {
      throw new Error('Invalid mediator variable data');
    }
    if (data.x.length !== data.y.length || data.x.length !== data.mediator.length) {
      throw new Error('All variables must have the same number of observations');
    }

    const pythonData = {
      x: data.x,
      y: data.y,
      mediator: data.mediator,
      covariates: data.covariates || {},
      confidence_level: data.confidence_level || 0.95,
      bootstrap_samples: data.bootstrap_samples || 1000
    };

    // Convert JavaScript object to Python object
    const pythonDataPy = this.pyodide.toPy(pythonData);

    // Pass the Python object directly to the Python function
    const resultJson = this.pyodide.runPython(`
import json
result = run_mediation_analysis(${pythonDataPy})
result # Return the result
    `);

    // Need to ensure the result is a string before parsing
    const resultString = resultJson.toString();
    pythonDataPy.destroy(); // Clean up the Python object

    const result = JSON.parse(resultString);

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  async runModerationAnalysis(data: AnalysisData): Promise<ModerationResults> {
    await this.initialize();

    // Validate input data
    if (!data.x || !Array.isArray(data.x) || data.x.length === 0) {
      throw new Error('Invalid independent variable data');
    }
    if (!data.y || !Array.isArray(data.y) || data.y.length === 0) {
      throw new Error('Invalid dependent variable data');
    }
    if (!data.moderator || !Array.isArray(data.moderator) || data.moderator.length === 0) {
      throw new Error('Invalid moderator variable data');
    }
    if (data.x.length !== data.y.length || data.x.length !== data.moderator.length) {
      throw new Error('All variables must have the same number of observations');
    }

    const pythonData = {
      x: data.x,
      y: data.y,
      moderator: data.moderator,
      covariates: data.covariates || {}
    };

    // Convert JavaScript object to Python object
    const pythonDataPy = this.pyodide.toPy(pythonData);

    // Pass the Python object directly to the Python function
    const resultJson = this.pyodide.runPython(`
import json
result = run_moderation_analysis(${pythonDataPy})
result # Return the result
    `);

    // Need to ensure the result is a string before parsing
    const resultString = resultJson.toString();
    pythonDataPy.destroy(); // Clean up the Python object

    const result = JSON.parse(resultString);

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  async predict(
    analysisType: 'mediation' | 'moderation',
    data: {
      x: number;
      mediator?: number;
      moderator?: number;
      covariates?: { [key: string]: number };
    }
  ): Promise<MediationModerationPrediction> {
    await this.initialize();

    const pythonData = {
      x: data.x,
      mediator: data.mediator,
      moderator: data.moderator,
      covariates: data.covariates || {}
    };

    this.pyodide.globals.set('prediction_data', pythonData);
    this.pyodide.globals.set('analysis_type', analysisType);

    const resultJson = this.pyodide.runPython(`predict_outcome(analysis_type, prediction_data.toJs())`);
    const result = JSON.parse(resultJson);

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  isReady(): boolean {
    return this.isInitialized;
  }
}

export const mediationModerationService = new MediationModerationService();