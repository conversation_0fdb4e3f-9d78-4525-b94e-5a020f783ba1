import { supabase } from '../supabaseClient';
import { ResultItem } from '../../context/ResultsContext';

// Define the type for project content stored in cloud
export interface ProjectContent {
  results: ResultItem[];
  metadata: {
    totalResults: number;
    lastModified: string;
    version: string;
  };
}

// Define the type for a saved project entry
export interface SavedProject {
  id: string;
  user_id: string;
  project_name: string;
  file_path: string; // Store the file path in the storage bucket
  created_at: string;
  updated_at: string;
}

const MAX_PROJECT_SIZE_BYTES = 2 * 1024 * 1024; // 2MB
const PROJECT_BUCKET_NAME = 'userprojects'; // Supabase storage bucket name
const MAX_PROJECTS_PER_USER = 2; // Limit for Pro users

/**
 * Saves a project for the current user to Supabase Storage.
 * Enforces a 2MB size limit per file and a limit of 2 projects per user.
 * @param projectName The name for the project.
 * @param projectContent The project content including results and metadata.
 * @returns The saved project metadata or an error.
 */
export const saveProject = async (projectName: string, projectContent: ProjectContent): Promise<{ data: SavedProject | null; error: any }> => {
  // Get the current session and user
  const { data: sessionData } = await supabase.auth.getSession();
  const { data: userData } = await supabase.auth.getUser();

  const user = userData?.user;

  if (!user) {
    return { data: null, error: new Error('User not authenticated.') };
  }

  try {
    // Check if user already has the maximum number of projects
    const { data: existingProjects, error: countError } = await supabase
      .from('user_projects')
      .select('id, project_name')
      .eq('user_id', user.id);

    if (countError) {
      console.error('Error checking existing projects:', countError);
      return { data: null, error: countError };
    }

    // Check if project with same name already exists
    const existingProject = existingProjects?.find(p => p.project_name === projectName);

    // If creating a new project (not updating), check the limit
    if (!existingProject && existingProjects && existingProjects.length >= MAX_PROJECTS_PER_USER) {
      return { 
        data: null, 
        error: new Error(`You can only save up to ${MAX_PROJECTS_PER_USER} projects. Please delete an existing project first.`) 
      };
    }

    // Serialize the project content
    const contentString = JSON.stringify(projectContent);
    const contentBlob = new Blob([contentString], { type: 'application/json' });

    // Check size limit
    if (contentBlob.size > MAX_PROJECT_SIZE_BYTES) {
      return { 
        data: null, 
        error: new Error(`Project size (${(contentBlob.size / 1024 / 1024).toFixed(2)}MB) exceeds the 2MB limit.`) 
      };
    }

    // Create file path: userId/projectName.json
    const filePath = `${user.id}/${projectName}.json`;

    if (existingProject) {
      // Update existing project file in storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(PROJECT_BUCKET_NAME)
        .upload(filePath, contentBlob, {
          cacheControl: '3600',
          upsert: true, // Use upsert to overwrite if file exists
        });

      if (uploadError) {
        console.error('Error uploading project file for update:', uploadError);
        return { data: null, error: uploadError };
      }

      // Update metadata in the database
      const { data, error } = await supabase
        .from('user_projects')
        .update({
          updated_at: new Date().toISOString(),
          file_path: filePath // Ensure file_path is correct in case of upsert
        })
        .eq('id', existingProject.id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating project metadata:', error);
      }

      return { data, error };

    } else {
      // Insert new project file into storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(PROJECT_BUCKET_NAME)
        .upload(filePath, contentBlob, {
          cacheControl: '3600',
          upsert: false, // Do not upsert for new files
        });

      if (uploadError) {
        console.error('Error uploading new project file:', uploadError);
        return { data: null, error: uploadError };
      }

      // Insert new metadata into the database
      const { data, error } = await supabase
        .from('user_projects')
        .insert([{
          user_id: user.id,
          project_name: projectName,
          file_path: filePath // Store the file path
        }])
        .select()
        .single();

      if (error) {
        console.error('Error inserting new project metadata:', error);
      }

      return { data, error };
    }
  } catch (error) {
    console.error('Unexpected error in saveProject:', error);
    return { data: null, error };
  }
};

/**
 * Loads a project for the current user from Supabase Storage.
 * @param projectId The ID of the project to load.
 * @returns The project content or an error.
 */
export const loadProject = async (projectId: string): Promise<{ data: ProjectContent | null; error: any }> => {
  const { data: userData } = await supabase.auth.getUser();
  const user = userData?.user;

  if (!user) {
    return { data: null, error: new Error('User not authenticated.') };
  }

  try {
    // Fetch the file path from the metadata table
    const { data: metadata, error: fetchError } = await supabase
      .from('user_projects')
      .select('file_path')
      .eq('user_id', user.id)
      .eq('id', projectId)
      .single();

    if (fetchError) {
      console.error('Error fetching project metadata for loading:', fetchError);
      return { data: null, error: fetchError };
    }

    if (!metadata?.file_path) {
      return { data: null, error: new Error('Project file path not found.') };
    }

    // Download the file content from storage
    const { data: fileData, error: downloadError } = await supabase.storage
      .from(PROJECT_BUCKET_NAME)
      .download(metadata.file_path);

    if (downloadError) {
      console.error('Error downloading project file:', downloadError);
      return { data: null, error: downloadError };
    }

    // Parse the file content
    const contentText = await fileData.text();
    const projectContent: ProjectContent = JSON.parse(contentText);

    return { data: projectContent, error: null };
  } catch (error) {
    console.error('Unexpected error in loadProject:', error);
    return { data: null, error };
  }
};

/**
 * Lists all projects for the current user.
 * @returns Array of saved projects or an error.
 */
export const listProjects = async (): Promise<{ data: SavedProject[] | null; error: any }> => {
  const { data: userData } = await supabase.auth.getUser();
  const user = userData?.user;

  if (!user) {
    return { data: null, error: new Error('User not authenticated.') };
  }

  try {
    const { data, error } = await supabase
      .from('user_projects')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error listing projects:', error);
    }

    return { data, error };
  } catch (error) {
    console.error('Unexpected error in listProjects:', error);
    return { data: null, error };
  }
};

/**
 * Deletes a project for the current user from both database and storage.
 * @param projectId The ID of the project to delete.
 * @returns Success status or an error.
 */
export const deleteProject = async (projectId: string): Promise<{ error: any }> => {
  const { data: userData } = await supabase.auth.getUser();
  const user = userData?.user;

  if (!user) {
    return { error: new Error('User not authenticated.') };
  }

  try {
    // First, get the project metadata to find the file path
    const { data: metadata, error: fetchError } = await supabase
      .from('user_projects')
      .select('file_path')
      .eq('user_id', user.id)
      .eq('id', projectId)
      .single();

    if (fetchError) {
      console.error('Error fetching project metadata for deletion:', fetchError);
      return { error: fetchError };
    }

    if (!metadata?.file_path) {
      return { error: new Error('Project file path not found.') };
    }

    // Delete the file from storage
    const { error: storageError } = await supabase.storage
      .from(PROJECT_BUCKET_NAME)
      .remove([metadata.file_path]);

    if (storageError) {
      console.error('Error deleting project file from storage:', storageError);
      return { error: storageError };
    }

    // Delete the metadata from the database
    const { error: dbError } = await supabase
      .from('user_projects')
      .delete()
      .eq('user_id', user.id)
      .eq('id', projectId);

    if (dbError) {
      console.error('Error deleting project metadata from database:', dbError);
      return { error: dbError };
    }

    return { error: null };
  } catch (error) {
    console.error('Unexpected error in deleteProject:', error);
    return { error };
  }
};
