// Use CommonJS require syntax for compatibility with ts-node
const { multipleLogisticRegression } = require('./stats');

// Simple test function to verify the logistic regression implementation
function testLogisticRegression() {
  console.log('Testing logistic regression implementation...');
  
  // Create a simple dataset
  const x = [
    [1, 2],
    [2, 3],
    [3, 4],
    [4, 5],
    [5, 6],
    [6, 7],
    [7, 8],
    [8, 9],
    [9, 10],
    [10, 11]
  ];
  
  const y = [0, 0, 0, 0, 0, 1, 1, 1, 1, 1];
  
  try {
    // Run logistic regression
    const result = multipleLogisticRegression(x, y, 1000, 0.1);
    
    // Check for NaN values in the results
    const hasNaN = (
      isNaN(result.intercept) ||
      result.coefficients.some((c: any) => isNaN(c)) ||
      result.stdErrors.some((se: any) => isNaN(se)) ||
      result.pValues.some((p: any) => isNaN(p)) ||
      isNaN(result.interceptStdError) ||
      isNaN(result.interceptPValue) ||
      isNaN(result.logLikelihood) ||
      isNaN(result.aic) ||
      isNaN(result.pseudoRSquared) ||
      isNaN(result.deviance) ||
      result.predictions.some((p: any) => isNaN(p)) ||
      isNaN(result.accuracy) ||
      isNaN(result.precision) ||
      isNaN(result.recall) ||
      isNaN(result.f1Score) ||
      isNaN(result.auc)
    );
    
    console.log('Logistic regression test completed.');
    console.log('Has NaN values:', hasNaN ? 'Yes' : 'No');
    
    // Print key results
    console.log('Intercept:', result.intercept);
    console.log('Coefficients:', result.coefficients);
    console.log('Standard Errors:', result.stdErrors);
    console.log('P-values:', result.pValues);
    console.log('Intercept Standard Error:', result.interceptStdError);
    console.log('Intercept P-value:', result.interceptPValue);
    console.log('AUC:', result.auc);
    console.log('Accuracy:', result.accuracy);
    
    return !hasNaN;
  } catch (error) {
    console.error('Error in logistic regression test:', error);
    return false;
  }
}

// Run the test
testLogisticRegression();
