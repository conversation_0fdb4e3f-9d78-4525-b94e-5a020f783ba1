import jStat from 'jstat';

// Calculate Pearson correlation coefficient
export const calculatePearsonCorrelation = (
  x: number[],
  y: number[]
): {
  r: number;
  pValue: number;
  n: number;
  rSquared: number;
} => {
  if (x.length !== y.length) {
    throw new Error('Arrays must have the same length');
  }
  
  if (x.length < 3) {
    throw new Error('Sample size must be at least 3');
  }
  
  const n = x.length;
  const r = jStat.corrcoeff(x, y);
  
  // Calculate t-statistic for correlation
  const t = r * Math.sqrt((n - 2) / (1 - r * r));
  
  // Calculate two-tailed p-value
  const pValue = 2 * (1 - jStat.studentt.cdf(Math.abs(t), n - 2));
  
  // Calculate r-squared (coefficient of determination)
  const rSquared = r * r;
  
  return { r, pValue, n, rSquared };
};
