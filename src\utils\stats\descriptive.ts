import jStat from 'jstat';
import { DataValue } from '../../types';
import { extractNumericValues, safeSortNumeric } from '../typeConversions';

/**
 * Descriptive Statistics Functions
 */

// Calculate mean of an array of numbers
export function calculateMean(data: number[]): number;
export function calculateMean(data: DataValue[]): number;
export function calculateMean(data: number[] | DataValue[]): number {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length === 0) return 0;
  return jStat.mean(numericData);
}

// Calculate median of an array of numbers
export function calculateMedian(data: number[]): number;
export function calculateMedian(data: DataValue[]): number;
export function calculateMedian(data: number[] | DataValue[]): number {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length === 0) return 0;
  return jStat.median(numericData);
}

// Calculate mode of an array of numbers
export interface ModeResult {
  mode: number | null;
  message: string;
}

export function calculateMode(data: number[]): ModeResult;
export function calculateMode(data: DataValue[]): ModeResult;
export function calculateMode(data: number[] | DataValue[]): ModeResult {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length === 0) {
    return { mode: null, message: "No mode found (empty dataset)." };
  }

  const frequency: { [key: number]: number } = {};
  let maxFreq = 0;

  // Count frequencies
  numericData.forEach(value => {
    frequency[value] = (frequency[value] || 0) + 1;
    if (frequency[value] > maxFreq) {
      maxFreq = frequency[value];
    }
  });

  // Find values with the maximum frequency
  const modes = Object.keys(frequency)
    .filter(key => frequency[Number(key)] === maxFreq)
    .map(key => Number(key))
    .sort((a, b) => a - b); // Sort modes to easily find the smallest

  if (modes.length === 0) { // Should not happen if data is not empty and maxFreq > 0
    return { mode: null, message: "No mode found." };
  }

  if (modes.length === 1) {
    return { mode: modes[0], message: "Single mode found." };
  } else {
    // Multiple modes exist, report the smallest one
    return {
      mode: modes[0],
      message: "Multiple modes exist, smallest one is being reported."
    };
  }
}

// Calculate variance of an array of numbers
export function calculateVariance(data: number[], sample?: boolean): number;
export function calculateVariance(data: DataValue[], sample?: boolean): number;
export function calculateVariance(data: number[] | DataValue[], sample = true): number {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length <= 1) return 0;
  return sample ? jStat.variance(numericData, true) : jStat.variance(numericData, false);
}

// Calculate standard deviation of an array of numbers
export function calculateStandardDeviation(data: number[], sample?: boolean): number;
export function calculateStandardDeviation(data: DataValue[], sample?: boolean): number;
export function calculateStandardDeviation(data: number[] | DataValue[], sample = true): number {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length <= 1) return 0;
  return sample ? jStat.stdev(numericData, true) : jStat.stdev(numericData, false);
}

// Calculate standard error of an array of numbers
export function calculateStandardError(data: number[]): number;
export function calculateStandardError(data: DataValue[]): number;
export function calculateStandardError(data: number[] | DataValue[]): number {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length <= 1) return 0;
  const stdDev = calculateStandardDeviation(numericData, true);
  return stdDev / Math.sqrt(numericData.length);
}

// Calculate skewness of an array of numbers
export function calculateSkewness(data: number[]): number;
export function calculateSkewness(data: DataValue[]): number;
export function calculateSkewness(data: number[] | DataValue[]): number {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length <= 2) return 0;
  return jStat.skewness(numericData);
}

// Calculate kurtosis of an array of numbers
export function calculateKurtosis(data: number[]): number;
export function calculateKurtosis(data: DataValue[]): number;
export function calculateKurtosis(data: number[] | DataValue[]): number {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length <= 3) return 0;
  return jStat.kurtosis(numericData);
}

// Calculate range of an array of numbers
export function calculateRange(data: number[]): number;
export function calculateRange(data: DataValue[]): number;
export function calculateRange(data: number[] | DataValue[]): number {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length === 0) return 0;
  return Math.max(...numericData) - Math.min(...numericData);
}

// Calculate quartiles of an array of numbers
export function calculateQuartiles(data: number[]): [number, number, number];
export function calculateQuartiles(data: DataValue[]): [number, number, number];
export function calculateQuartiles(data: number[] | DataValue[]): [number, number, number] {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length === 0) return [0, 0, 0];

  const sortedData = [...numericData].sort((a, b) => a - b);
  const q1 = jStat.quantiles(sortedData, [0.25])[0];
  const q2 = jStat.median(sortedData);
  const q3 = jStat.quantiles(sortedData, [0.75])[0];

  return [q1, q2, q3];
}

// Calculate interquartile range (IQR) of an array of numbers
export function calculateIQR(data: number[]): number;
export function calculateIQR(data: DataValue[]): number;
export function calculateIQR(data: number[] | DataValue[]): number {
  const numericData = Array.isArray(data) && data.length > 0 && typeof data[0] !== 'number'
    ? extractNumericValues(data as DataValue[])
    : data as number[];

  if (numericData.length === 0) return 0;
  const [q1, , q3] = calculateQuartiles(numericData);
  return q3 - q1;
}

// Error function approximation for normal CDF
const erf = (x: number): number => {
  const a1 = 0.254829592;
  const a2 = -0.284496736;
  const a3 = 1.421413741;
  const a4 = -1.453152027;
  const a5 = 1.061405429;
  const p = 0.3275911;
  const t = 1 / (1 + p * Math.abs(x));
  const y = 1 - ((((a5 * t + a4) * t + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);
  return Math.sign(x) * y;
};



// Calculate frequency table for categorical data
export const calculateFrequencies = (data: string[] | number[]): Record<string, number> => {
  const frequencies: Record<string, number> = {};
  
  data.forEach(value => {
    const key = String(value);
    frequencies[key] = (frequencies[key] || 0) + 1;
  });
  
  return frequencies;
};

// Calculate proportions for categorical data
export const calculateProportions = (data: string[] | number[]): Record<string, number> => {
  const frequencies = calculateFrequencies(data);
  const total = data.length;
  
  const proportions: Record<string, number> = {};
  Object.keys(frequencies).forEach(key => {
    proportions[key] = frequencies[key] / total;
  });
  
  return proportions;
};

// Create a cross-tabulation (contingency table) between two categorical variables
export const createCrossTabulation = (
  var1: string[] | number[],
  var2: string[] | number[]
): Record<string, Record<string, number>> => {
  if (var1.length !== var2.length) {
    throw new Error('Variables must have the same length');
  }
  
  // Get unique values for each variable
  const uniqueValues1 = [...new Set(var1.map(String))];
  const uniqueValues2 = [...new Set(var2.map(String))];
  
  // Initialize cross-tabulation
  const crossTab: Record<string, Record<string, number>> = {};
  uniqueValues1.forEach(val1 => {
    crossTab[val1] = {};
    uniqueValues2.forEach(val2 => {
      crossTab[val1][val2] = 0;
    });
  });
  
  // Fill cross-tabulation
  for (let i = 0; i < var1.length; i++) {
    const val1 = String(var1[i]);
    const val2 = String(var2[i]);
    crossTab[val1][val2] += 1;
  }
  
  return crossTab;
};
