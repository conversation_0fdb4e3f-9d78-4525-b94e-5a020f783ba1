/**
 * Calculates the p-value for a given chi-squared statistic and degrees of freedom.
 * This implementation uses the lower incomplete gamma function and the regularized gamma function.
 *
 * @param chiSquared The chi-squared statistic.
 * @param df Degrees of freedom.
 * @returns The p-value (right-tail probability).
 */

// Helper: Gamma function (La<PERSON><PERSON><PERSON> approximation)
function logGamma(x: number): number {
  const cof = [
    76.18009172947146,
    -86.50532032941677,
    24.01409824083091,
    -1.231739572450155,
    0.1208650973866179e-2,
    -0.5395239384953e-5,
  ];
  let y = x;
  let tmp = x + 5.5;
  tmp -= (x + 0.5) * Math.log(tmp);
  let ser = 1.000000000190015;
  for (let j = 0; j < 6; j++) {
    ser += cof[j] / ++y;
  }
  return -tmp + Math.log(2.5066282746310005 * ser / x);
}

function gamma(x: number): number {
  return Math.exp(logGamma(x));
}

// Helper: Lower incomplete gamma function P(a, x) = gamma(a, x) / Gamma(a)
// This is the regularized lower incomplete gamma function.
// Uses series expansion for small x and continued fraction for large x.
function regularizedLowerIncompleteGamma(a: number, x: number): number {
  if (x < 0 || a <= 0) {
    return 0.0;
  }
  if (x === 0) {
    return 0.0;
  }

  const logGammaA = logGamma(a);

  if (x < a + 1.0) {
    // Series expansion
    let ap = a;
    let del = 1.0 / a;
    let sum = del;
    for (let i = 0; i < 100; i++) {
      ap += 1.0;
      del *= x / ap;
      sum += del;
      if (Math.abs(del) < Math.abs(sum) * 1e-7) {
        break;
      }
    }
    return sum * Math.exp(-x + a * Math.log(x) - logGammaA);
  } else {
    // Continued fraction
    let b = x + 1.0 - a;
    let c = 1.0 / 1e-30; // A very small number
    let d = 1.0 / b;
    let h = d;
    for (let i = 0; i < 100; i++) {
      const an = -i * (i - a + 1.0);
      b += 2.0;
      d = an * d + b;
      if (Math.abs(d) < 1e-30) d = 1e-30;
      c = b + an / c;
      if (Math.abs(c) < 1e-30) c = 1e-30;
      d = 1.0 / d;
      const del = d * c;
      h *= del;
      if (Math.abs(del - 1.0) < 1e-7) {
        break;
      }
    }
    return 1.0 - (Math.exp(-x + a * Math.log(x) - logGammaA) * h);
  }
}

/**
 * Calculates the p-value for a Chi-Squared test.
 * This is the right-tail probability: P(X > chiSquared) = 1 - P(X <= chiSquared).
 *
 * @param chiSquared The observed Chi-Squared statistic.
 * @param df Degrees of freedom.
 * @returns The p-value.
 */
export function chiSquarePValue(chiSquared: number, df: number): number {
  if (isNaN(chiSquared) || df <= 0 || chiSquared < 0) {
    //throw new Error('Degrees of freedom must be positive and chiSquared must be non-negative, and chiSquared must be a number.');
    // Return NaN or handle as appropriate for invalid inputs in a statistical context
    return NaN; 
  }
  if (chiSquared === 0) return 1.0; // No deviation, p-value is 1
  if (df === 1 && chiSquared > 1000) return 0; // Avoid precision issues for large chiSquared with df=1
  if (df > 1000 && chiSquared > df + 100 * Math.sqrt(2*df)) return 0; // For very large df and chiSquared

  // The p-value is 1 - P(a, x), where a = df/2 and x = chiSquared/2
  // P(a,x) is the regularized lower incomplete gamma function.
  const pValue = 1.0 - regularizedLowerIncompleteGamma(df / 2.0, chiSquared / 2.0);
  
  // Clamp pValue to [0, 1] to handle potential floating point inaccuracies
  return Math.max(0, Math.min(1, pValue));
}