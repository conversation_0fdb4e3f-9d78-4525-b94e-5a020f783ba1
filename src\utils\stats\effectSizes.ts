import jStat from 'jstat'; // Assuming jStat is available for statistical functions

/**
 * Calculates <PERSON>'s d for independent samples.
 * @param mean1 Mean of group 1.
 * @param sd1 Standard deviation of group 1.
 * @param n1 Sample size of group 1.
 * @param mean2 Mean of group 2.
 * @param sd2 Standard deviation of group 2.
 * @param n2 Sample size of group 2.
 * @returns <PERSON>'s d.
 */
export const calculateCohenSD = (mean1: number, sd1: number, n1: number, mean2: number, sd2: number, n2: number): number => {
    // Pooled standard deviation
    const pooledSD = Math.sqrt(((n1 - 1) * Math.pow(sd1, 2) + (n2 - 1) * Math.pow(sd2, 2)) / (n1 + n2 - 2));
    // <PERSON>'s d
    return (mean1 - mean2) / pooledSD;
};

/**
 * Calculates the confidence interval for <PERSON>'s d (approximation).
 * Note: A more robust calculation would use the non-central t-distribution.
 * This implementation uses a simplified approximation based on the standard error.
 * @param cohenSD <PERSON>'s d value.
 * @param n1 Sample size of group 1.
 * @param n2 Sample size of group 2.
 * @param confidenceLevel Confidence level (e.g., 0.95 for 95% CI).
 * @returns An object with lower and upper bounds of the confidence interval.
 */
export const calculateCohenSDConfidenceInterval = (cohenSD: number, n1: number, n2: number, confidenceLevel: number = 0.95): { lower: number; upper: number } => {
    const pooledN = n1 + n2;
    // Standard error of <PERSON>'s d (approximation)
    const standardError = Math.sqrt((pooledN / (n1 * n2)) + (Math.pow(cohenSD, 2) / (2 * (pooledN - 2))));

    // Using Z-score for the specified confidence level
    // For a two-tailed interval, we need the Z-score for (1 - confidenceLevel) / 2 in each tail.
    // jStat.normal.inv(p, mean, std) gives the inverse of the normal distribution CDF.
    // For a 95% CI, we need the z-score for p = 0.975 (upper tail) and p = 0.025 (lower tail).
    const alpha = 1 - confidenceLevel;
    const z = jStat.normal.inv(1 - alpha / 2, 0, 1); // Z-score for the upper tail

    const lower = cohenSD - z * standardError;
    const upper = cohenSD + z * standardError;

    return { lower, upper };
};
