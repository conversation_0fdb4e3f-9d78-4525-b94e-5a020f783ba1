import { rankD<PERSON>, TiedRankStrategy } from './ranking';
import { chiSquarePValue } from './distributions'; // Assuming a chi-square p-value function exists

/**
 * Represents the result of a Friedman test.
 */
export interface FriedmanTestResult {
  statistic: number;       // The Friedman Chi-Squared statistic (renamed from fchiSquared for consistency)
  df: number;               // Degrees of freedom (k - 1)
  pValue: number;           // The p-value
  n: number;                // Number of subjects/blocks (rows)
  k: number;                // Number of groups/conditions (columns)
  interpretation: string;   // A textual interpretation of the results
  rankSums: number[];       // Sum of ranks for each group
  meanRanks?: number[];     // Mean ranks for each group (added for better visualization)
}

/**
 * Performs the Friedman test for k related samples.
 * This test is a non-parametric alternative to the one-way repeated measures ANOVA.
 * It is used to detect differences in treatments across multiple test attempts.
 *
 * @param data A 2D array where rows represent subjects/blocks and columns represent conditions/treatments.
 *             Each cell contains the measurement for a subject under a specific condition.
 *             Example: [[subj1_cond1, subj1_cond2, subj1_cond3],
 *                       [subj2_cond1, subj2_cond2, subj2_cond3]]
 * @returns An object containing the <PERSON> test statistics and interpretation.
 *
 * @throws Error if data is insufficient (e.g., less than 2 subjects or 3 conditions).
 */
export function calculateFriedmanTest(data: number[][]): FriedmanTestResult {
  if (!data || data.length === 0) {
    throw new Error('Input data cannot be empty.');
  }

  const n = data.length; // Number of subjects (rows)
  if (n < 2) {
    throw new Error('Friedman test requires at least 2 subjects/blocks.');
  }

  const k = data[0]?.length || 0; // Number of conditions (columns)
  if (k < 3) {
    throw new Error('Friedman test requires at least 3 conditions/groups.');
  }

  // Validate that all rows have the same number of columns
  for (let i = 0; i < n; i++) {
    if (data[i].length !== k) {
      throw new Error(`All subjects must have the same number of conditions. Subject ${i + 1} has ${data[i].length}, expected ${k}.`);
    }
    // Check for non-numeric values
    for (let j = 0; j < k; j++) {
      if (typeof data[i][j] !== 'number' || isNaN(data[i][j])) {
        throw new Error(`Data for subject ${i + 1}, condition ${j + 1} is not a valid number.`);
      }
    }
  }

  // Step 1: Rank the data for each subject (row) across conditions (columns)
  const rankedData: number[][] = [];
  for (let i = 0; i < n; i++) {
    // rankData utility expects an array of objects or simple values.
    // We pass each row (subject's scores across conditions) to be ranked.
    const subjectRanks = rankData(data[i], { strategy: TiedRankStrategy.AVERAGE });
    rankedData.push(subjectRanks.map(r => r.rank)); // Extract just the rank values
  }

  // Step 2: Calculate the sum of ranks for each condition (column)
  const rankSums: number[] = new Array(k).fill(0);
  for (let j = 0; j < k; j++) {
    for (let i = 0; i < n; i++) {
      rankSums[j] += rankedData[i][j];
    }
  }

  // Calculate mean ranks for each condition
  const meanRanks = rankSums.map(sum => sum / n);

  // Step 3: Calculate the Friedman statistic (Chi-Squared_r)
  // Formula: Q = [12 / (N * k * (k + 1))] * [Sum of (R_j)^2] - 3 * N * (k + 1)
  // Where N is number of subjects, k is number of conditions, R_j is sum of ranks for condition j.

  let sumOfSquaredRankSums = 0;
  for (let j = 0; j < k; j++) {
    sumOfSquaredRankSums += Math.pow(rankSums[j], 2);
  }

  // Calculate the Friedman statistic (Chi-Squared_r) with more precise arithmetic
  const numerator = 12 * sumOfSquaredRankSums;
  const denominator = n * k * (k + 1);
  const subtrahend = 3 * n * (k + 1);
  const chiSquared = (numerator / denominator) - subtrahend;
  
  // Ensure the result is a valid number
  const validChiSquared = Number.isFinite(chiSquared) ? chiSquared : 0;

  // Step 4: Calculate degrees of freedom
  const df = k - 1;

  // Step 5: Calculate the p-value from the Chi-Squared distribution
  const pValue = chiSquarePValue(validChiSquared, df);

  // Step 6: Interpretation
  const chiSqValueForDisplay = validChiSquared.toFixed(3);

  const pValueForDisplay = (typeof pValue === 'number' && !isNaN(pValue))
    ? (pValue < 0.001 ? '< 0.001' : pValue.toFixed(3))
    : 'undefined';

  const significanceMessage = pValue < 0.05
    ? "There was a statistically significant difference"
    : "There was no statistically significant difference";

  let generatedInterpretation = `A Friedman Test was conducted to compare the ranks of ${k} related conditions/variables for ${n} subjects. ` +
                       `${significanceMessage} among the mean ranks of the conditions/variables ` +
                       `(χ²(${df}) = ${chiSqValueForDisplay}, p = ${pValueForDisplay}).`;

  if (n < 10 || k < 4) {
    generatedInterpretation += ' Note: For small sample sizes, the Chi-Squared approximation may be less accurate. Exact p-values might be preferred if available.';
  }

  return {
    statistic: validChiSquared, // Renamed from fchiSquared to statistic for consistency
    df,
    pValue,
    n,
    k,
    interpretation: generatedInterpretation,
    rankSums,
    meanRanks, // Added mean ranks for better visualization
  };
}
