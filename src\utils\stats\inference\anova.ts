import jStat from 'jstat';
// import leveneTest from '@stdlib/stats/levene-test'; // Comment out for now
import { calculateMean, calculateVariance } from '../descriptive';

// One-way ANOVA
export const oneWayANOVA = (
  groups: number[][]
): {
  F: number;
  dfBetween: number;
  dfWithin: number;
  pValue: number;
  etaSquared: number;
  means: number[];
  grandMean: number;
} => {
  if (groups.length < 2) {
    throw new Error('At least 2 groups required for ANOVA');
  }
  
  // Calculate group statistics
  const n = groups.map(g => g.length);
  const means = groups.map(g => calculateMean(g));
  const variances = groups.map(g => calculateVariance(g, true));
  
  // Calculate total sample size
  const N = n.reduce((a, b) => a + b, 0);
  
  // Calculate grand mean
  let sumOfAllValues = 0;
  groups.forEach(group => {
    group.forEach(value => {
      sumOfAllValues += value;
    });
  });
  const grandMean = sumOfAllValues / N;
  
  // Calculate Sum of Squares Between (SSB)
  let ssb = 0;
  for (let i = 0; i < groups.length; i++) {
    ssb += n[i] * Math.pow(means[i] - grandMean, 2);
  }
  
  // Calculate Sum of Squares Within (SSW)
  let ssw = 0;
  for (let i = 0; i < groups.length; i++) {
    ssw += (n[i] - 1) * variances[i];
  }
  
  // Calculate Sum of Squares Total (SST)
  const sst = ssb + ssw;
  
  // Calculate degrees of freedom
  const dfBetween = groups.length - 1;
  const dfWithin = N - groups.length;
  
  // Calculate Mean Squares
  const msb = ssb / dfBetween;
  const msw = ssw / dfWithin;
  
  // Calculate F-statistic
  const F = msb / msw;
  
  // Calculate p-value
  const pValue = 1 - jStat.centralF.cdf(F, dfBetween, dfWithin);
  
  // Calculate effect size (eta-squared)
  const etaSquared = ssb / sst;
  
  return { F, dfBetween, dfWithin, pValue, etaSquared, means, grandMean };
};

import jStat from 'jstat';
import { calculateMean, calculateVariance } from '../descriptive';
export { twoWayANOVA } from './anova/twoWayANOVA';
export { calculateRepeatedMeasuresANOVA } from './anova/repeatedMeasuresANOVA';

// Enhanced Levene's test for equality of variances with support for both variants
export const performLeveneTest = (
  ...args: [...number[][], { variant?: 'mean' | 'median'; alpha?: number }?]
): {
  statistic: number;
  pValue: number;
  df: [number, number] | null; // Degrees of freedom [df1, df2] or null if not applicable
  rejected: boolean; // Whether the null hypothesis (equal variances) is rejected
  alpha: number; // Significance level used for the test
  method: string; // Method used (e.g., 'Levene test based on the mean')
  variant: 'mean' | 'median'; // Which variant was used
} => {
  // Parse arguments - last argument might be options object
  const lastArg = args[args.length - 1];
  let groups: number[][];
  let options: { variant?: 'mean' | 'median'; alpha?: number } = {};

  if (Array.isArray(lastArg) && typeof lastArg[0] === 'number') {
    // All arguments are groups
    groups = args as number[][];
  } else {
    // Last argument is options object
    groups = args.slice(0, -1) as number[][];
    options = lastArg as { variant?: 'mean' | 'median'; alpha?: number };
  }

  const variant = options.variant || 'median'; // Default to median (Brown-Forsythe)
  const alpha = options.alpha || 0.05;
  if (groups.length < 2) {
    throw new Error('At least 2 groups are required for Levene\'s test.');
  }

  // Calculate degrees of freedom for Levene's test:
  // df1 = k - 1 (number of groups - 1)
  // df2 = N - k (total number of observations - number of groups)
  const k = groups.length;
  const N = groups.reduce((sum, group) => sum + group.length, 0);
  
  if (k < 2) { // Should have been caught earlier, but as a safeguard for df calculation
      throw new Error("At least two groups are required to calculate Levene's test degrees of freedom.");
  }
  // Ensure each group has at least one observation for N to be meaningful in N-k.
  // Also, N must be greater than k for df2 > 0.
  if (N <= k) { 
      throw new Error("Total observations must be greater than the number of groups for valid Levene's test df.");
  }

  const df1 = k - 1;
  const df2 = N - k;
  
  // Calculate group centers (means or medians based on variant)
  const groupCenters = groups.map(group => {
    if (variant === 'mean') {
      return calculateMean(group);
    } else {
      // Calculate median (Brown-Forsythe variant)
      const sorted = [...group].sort((a, b) => a - b);
      const mid = Math.floor(sorted.length / 2);
      return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
    }
  });

  // Calculate absolute deviations from group centers
  const deviations = groups.map((group, i) =>
    group.map(value => Math.abs(value - groupCenters[i]))
  );
  
  // Calculate mean of deviations for each group
  const deviationMeans = deviations.map(group => 
    group.reduce((sum, val) => sum + val, 0) / group.length
  );
  
  // Calculate overall mean of deviations
  const allDeviations = deviations.flat();
  const overallMean = allDeviations.reduce((sum, val) => sum + val, 0) / allDeviations.length;
  
  // Calculate SSB (between groups sum of squares)
  let SSB = 0;
  for (let i = 0; i < k; i++) {
    SSB += groups[i].length * Math.pow(deviationMeans[i] - overallMean, 2);
  }
  
  // Calculate SSW (within groups sum of squares)
  let SSW = 0;
  for (let i = 0; i < k; i++) {
    for (let j = 0; j < deviations[i].length; j++) {
      SSW += Math.pow(deviations[i][j] - deviationMeans[i], 2);
    }
  }
  
  // Calculate F statistic
  const MSB = SSB / df1;
  const MSW = SSW / df2;
  const statistic = MSB / MSW;
  
  // Calculate p-value using F distribution
  // Using jStat for F distribution CDF
  const pValue = 1 - jStat.centralF.cdf(statistic, df1, df2);
  
  const methodDescription = variant === 'mean'
    ? 'Levene test based on mean'
    : 'Levene test based on median (Brown-Forsythe variant)';

  return {
    statistic,
    pValue,
    df: [df1, df2],
    rejected: pValue < alpha,
    alpha,
    method: methodDescription,
    variant
  };
};

/**
 * Levene's test based on mean (classic Levene's test)
 * Tests the null hypothesis that all input samples are from populations with equal variances
 */
export const leveneTestMean = (...groups: number[][]) => {
  return performLeveneTest(...groups, { variant: 'mean' });
};

/**
 * Levene's test based on median (Brown-Forsythe test)
 * More robust variant that uses medians instead of means
 * This is the default variant and matches SPSS "Based on Median" results
 */
export const leveneTestMedian = (...groups: number[][]) => {
  return performLeveneTest(...groups, { variant: 'median' });
};

/**
 * Brown-Forsythe test (alias for median-based Levene's test)
 * This is the same as leveneTestMedian but with a more descriptive name
 */
export const brownForsytheTest = leveneTestMedian;

import * as sm from 'statsmodels-js'; // Attempt to import statsmodels-js

// Real implementation for Repeated Measures ANOVA (One-Way RM ANOVA)

// Helper function to calculate determinant of a matrix
// This is a simplified implementation for small matrices
function calculateDeterminant(matrix: number[][]): number {
  const n = matrix.length;
  
  if (n === 1) {
    return matrix[0][0];
  }
  
  if (n === 2) {
    return matrix[0][0] * matrix[1][1] - matrix[0][1] * matrix[1][0];
  }
  
  let det = 0;
  for (let i = 0; i < n; i++) {
    const subMatrix: number[][] = [];
    for (let j = 1; j < n; j++) {
      subMatrix[j-1] = [];
      for (let k = 0; k < n; k++) {
        if (k !== i) {
          subMatrix[j-1].push(matrix[j][k]);
        }
      }
    }
    const sign = i % 2 === 0 ? 1 : -1;
    det += sign * matrix[0][i] * calculateDeterminant(subMatrix);
  }
  
  return det;
}
