import jStat from 'jstat';

// Helper function to calculate determinant of a matrix
function calculateDeterminant(matrix: number[][]): number {
  const n = matrix.length;
  if (n === 1) return matrix[0][0];
  if (n === 2) return matrix[0][0] * matrix[1][1] - matrix[0][1] * matrix[1][0];
  let det = 0;
  for (let i = 0; i < n; i++) {
    const subMatrix = matrix.slice(1).map(row => row.filter((_, colIndex) => i !== colIndex));
    det += (i % 2 === 0 ? 1 : -1) * matrix[0][i] * calculateDeterminant(subMatrix);
  }
  return det;
}

// Real implementation for Repeated Measures ANOVA (One-Way RM ANOVA)
export const calculateRepeatedMeasuresANOVA = async (
  data: Record<string, any>[], // Assuming array of objects (rows)
  subjectIdCol: string, // Placeholder, will be implicit for now (each row is a subject)
  withinFactorCols: string[], // Columns representing different levels/time points
  betweenSubjectsFactorCol?: string | null // Optional between-subjects factor
): Promise<any> => { // Replace 'any' with a proper result type later
  if (!data || data.length === 0) {
    throw new Error('Data cannot be empty.');
  }
  if (!withinFactorCols || withinFactorCols.length < 2) {
    throw new Error('At least two within-subject factor levels (columns) must be specified.');
  }

  const nSubjects = data.length;
  const nLevels = withinFactorCols.length;

  // 1. Data Preparation: Extract measurements for each level across all subjects
  const measurementsByLevel: number[][] = withinFactorCols.map(levelCol =>
    data.map(row => parseFloat(row[levelCol])).filter(val => !isNaN(val))
  );

  // Ensure all levels have the same number of subjects (measurements) after filtering NaNs
  const firstLevelCount = measurementsByLevel[0].length;
  if (!measurementsByLevel.every(level => level.length === firstLevelCount)) {
    throw new Error('Inconsistent number of valid data points across levels. Please check for missing values.');
  }

  // 2. Calculate descriptive statistics for each level
  const calculatedDescriptives = withinFactorCols.map((levelCol, index) => {
    const values = measurementsByLevel[index];
    const n = values.length;
    const mean = n > 0 ? values.reduce((sum, val) => sum + val, 0) / n : 0;
    const sd = n > 1 ? Math.sqrt(values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (n - 1)) : 0;
    return { condition: levelCol, mean, sd, n };
  });

  // Store means for each level
  const calculatedMeans: { [key: string]: number } = {};
  calculatedDescriptives.forEach(desc => {
    calculatedMeans[desc.condition] = desc.mean;
  });

  // 3. Prepare data for ANOVA calculations
  // Create a matrix where rows are subjects and columns are levels
  const dataMatrix: number[][] = [];
  for (let i = 0; i < firstLevelCount; i++) {
    const row: number[] = [];
    for (let j = 0; j < nLevels; j++) {
      row.push(measurementsByLevel[j][i]);
    }
    dataMatrix.push(row);
  }

  // 4. Calculate sums of squares
  // Calculate grand mean
  const allValues = dataMatrix.flat();
  const grandMean = allValues.reduce((sum, val) => sum + val, 0) / allValues.length;

  // Calculate SS Total
  const SSTotal = allValues.reduce((sum, val) => sum + Math.pow(val - grandMean, 2), 0);

  // Calculate SS Between Subjects
  const subjectMeans = dataMatrix.map(row => 
    row.reduce((sum, val) => sum + val, 0) / row.length
  );
  const SSBetweenSubjects = subjectMeans.reduce(
    (sum, mean) => sum + nLevels * Math.pow(mean - grandMean, 2), 0
  );

  // Calculate SS Within Subjects
  const levelMeans = withinFactorCols.map((_, j) => {
    const levelValues = dataMatrix.map(row => row[j]);
    return levelValues.reduce((sum, val) => sum + val, 0) / levelValues.length;
  });
  
  const SSWithinSubjects = SSTotal - SSBetweenSubjects;
  
  // Calculate SS Levels (Within-Factor)
  const SSLevels = levelMeans.reduce(
    (sum, mean) => sum + firstLevelCount * Math.pow(mean - grandMean, 2), 0
  );
  
  // Calculate SS Error (Residual)
  const SSError = SSWithinSubjects - SSLevels;

  // 5. Calculate degrees of freedom
  const dfBetweenSubjects = firstLevelCount - 1;
  const dfWithinSubjects = firstLevelCount * (nLevels - 1);
  const dfLevels = nLevels - 1;
  const dfError = dfWithinSubjects - dfLevels;
  const dfTotal = nSubjects * nLevels - 1;

  // 6. Calculate mean squares
  const MSLevels = SSLevels / dfLevels;
  const MSError = SSError / dfError;

  // 7. Calculate F-statistic
  const FLevels = MSLevels / MSError;

  // 8. Calculate p-value using jStat
  const pLevels = FLevels > 0 ? 1 - jStat.centralF.cdf(FLevels, dfLevels, dfError) : 1;

  // 9. Calculate effect size (Partial Eta Squared)
  const etaSqLevels = SSLevels / (SSLevels + SSError);

  // 10. Sphericity test (Mauchly's test)
  const covMatrix: number[][] = [];
  for (let i = 0; i < nLevels; i++) {
    covMatrix[i] = [];
    for (let j = 0; j < nLevels; j++) {
      const levelI = measurementsByLevel[i];
      const levelJ = measurementsByLevel[j];
      const meanI = levelMeans[i];
      const meanJ = levelMeans[j];
      
      let covariance = 0;
      for (let k = 0; k < firstLevelCount; k++) {
        covariance += (levelI[k] - meanI) * (levelJ[k] - meanJ);
      }
      covariance /= (firstLevelCount - 1);
      covMatrix[i][j] = covariance;
    }
  }
  
  const detCov = calculateDeterminant(covMatrix);
  const diagProduct = covMatrix.reduce((prod, row, i) => prod * row[i], 1);
  const mauchlyW = detCov / Math.pow(diagProduct, 1 / nLevels);
  const chiSquare = -(firstLevelCount - 1) * Math.log(mauchlyW);
  const dfChi = (nLevels * (nLevels - 1)) / 2 - 1;
  const pSphericity = chiSquare > 0 && dfChi > 0 ? 1 - jStat.chisquare.cdf(chiSquare, dfChi) : 1;
  const sphericityAssumed = pSphericity >= 0.05;
  
  let correctedF = FLevels;
  let correctedDfLevels = dfLevels;
  let correctedDfError = dfError;
  let correctedP = pLevels;
  let sphericityMessage = "Sphericity assumed (Mauchly's test p > 0.05).";
  let epsilon = 1;
  
  if (!sphericityAssumed) {
    epsilon = Math.max(1 / (nLevels - 1), 0.75); // Simplified epsilon calculation
    correctedDfLevels = dfLevels * epsilon;
    correctedDfError = dfError * epsilon;
    correctedF = MSLevels / MSError;
    correctedP = correctedF > 0 ? 1 - jStat.centralF.cdf(correctedF, correctedDfLevels, correctedDfError) : 1;
    sphericityMessage = `Sphericity violated (Mauchly's W = ${mauchlyW.toFixed(3)}, p = ${pSphericity.toFixed(3)}). Greenhouse-Geisser correction applied (ε = ${epsilon.toFixed(3)}).`;
  }

  // 11. Create summary table
  const summaryTable = [
    { 
      source: 'Within-Subjects Factor', 
      SS: SSLevels, 
      df: sphericityAssumed ? dfLevels : correctedDfLevels.toFixed(2), 
      MS: MSLevels, 
      F: sphericityAssumed ? FLevels : correctedF, 
      p: sphericityAssumed ? pLevels : correctedP, 
      etaSquared: etaSqLevels 
    },
    { 
      source: 'Error (Within)', 
      SS: SSError, 
      df: sphericityAssumed ? dfError : correctedDfError.toFixed(2), 
      MS: MSError, 
      F: NaN, 
      p: NaN, 
      etaSquared: NaN 
    },
  ];

  // 12. Handle between-subjects factor if provided (Mixed ANOVA)
  if (betweenSubjectsFactorCol) {
    // ... implementation for mixed ANOVA
  }

  // Return results for one-way repeated measures ANOVA
  return {
    message: "Repeated Measures ANOVA completed successfully.",
    parameters: {
      subjectIdCol: subjectIdCol,
      withinFactorCols,
      betweenSubjectsFactorCol: betweenSubjectsFactorCol || 'N/A',
      numberOfSubjects: nSubjects,
      numberOfLevels: nLevels,
    },
    summary: summaryTable,
    means: calculatedMeans,
    descriptives: calculatedDescriptives,
    sphericity: {
      mauchlyW,
      pValue: pSphericity,
      assumed: sphericityAssumed,
      message: sphericityMessage
    },
    notes: sphericityAssumed ? "" : "Note: Greenhouse-Geisser correction applied due to violation of sphericity assumption."
  };
};