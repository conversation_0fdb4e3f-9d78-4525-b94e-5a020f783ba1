import jStat from 'jstat';

// Helper function to calculate standard deviation
const calculateSD = (values: number[]): number => {
  if (values.length <= 1) return 0;
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1);
  return Math.sqrt(variance);
};

// Helper function to calculate standard error
const calculateSE = (sd: number, n: number): number => {
  return n > 0 ? sd / Math.sqrt(n) : 0;
};

// Interface for input data format
export interface TwoWayANOVAData {
  value: number;
  factorA: string;
  factorB: string;
}

// Interface for cell statistics
export interface CellStats {
  factor1Level: string;
  factor2Level: string;
  N: number;
  mean: number;
  sd: number;
  se: number;
  ci95Lower: number;
  ci95Upper: number;
}

// Interface for marginal means
export interface MarginalStats {
  mean: number;
  sd: number;
  n: number;
  se: number;
}

// Interface for ANOVA table row
export interface ANOVATableRow {
  source: string;
  SS: number;
  df: number;
  MS: number | null;
  F: number | null;
  p: number | null;
  etaSquared: number | null;
}

// Interface for assumptions tests
export interface AssumptionsTests {
  normality: {
    passed: boolean;
    statistic: number;
    p: number;
    test: string;
  };
  homogeneity: {
    passed: boolean;
    statistic: number;
    p: number;
    test: string;
  };
  independence: {
    passed: boolean;
    note: string;
  };
}

// Main Two-way ANOVA function with flexible input
export const twoWayANOVA = (
  data: TwoWayANOVAData[],
  factorAName: string = 'Factor A',
  factorBName: string = 'Factor B'
): {
  anovaTable: ANOVATableRow[];
  descriptiveStats: CellStats[];
  marginalMeansFactor1: { [key: string]: MarginalStats };
  marginalMeansFactor2: { [key: string]: MarginalStats };
  grandMean: number;
  totalN: number;
  assumptions: AssumptionsTests;
} => {
  // Validate input
  if (!data || data.length === 0) {
    throw new Error('Data must be a non-empty array');
  }

  // Extract unique levels for both factors
  const factorALevels = [...new Set(data.map(d => d.factorA))].sort();
  const factorBLevels = [...new Set(data.map(d => d.factorB))].sort();

  // Organize data into cells
  const cells: { [key: string]: number[] } = {};
  factorALevels.forEach(a => {
    factorBLevels.forEach(b => {
      cells[`${a}_${b}`] = [];
    });
  });

  // Populate cells with data
  data.forEach(d => {
    const key = `${d.factorA}_${d.factorB}`;
    if (cells[key]) {
      cells[key].push(d.value);
    }
  });

  // Calculate descriptive statistics for each cell
  const descriptiveStats: CellStats[] = [];
  const cellMeans: { [key: string]: number } = {};
  const cellCounts: { [key: string]: number } = {};

  factorALevels.forEach(a => {
    factorBLevels.forEach(b => {
      const key = `${a}_${b}`;
      const cellData = cells[key];
      
      if (cellData.length > 0) {
        const n = cellData.length;
        const mean = cellData.reduce((sum, val) => sum + val, 0) / n;
        const sd = calculateSD(cellData);
        const se = calculateSE(sd, n);
        const ci95Lower = mean - (1.96 * se);
        const ci95Upper = mean + (1.96 * se);

        cellMeans[key] = mean;
        cellCounts[key] = n;

        descriptiveStats.push({
          factor1Level: a,
          factor2Level: b,
          N: n,
          mean,
          sd,
          se,
          ci95Lower,
          ci95Upper
        });
      } else {
        cellMeans[key] = NaN;
        cellCounts[key] = 0;

        descriptiveStats.push({
          factor1Level: a,
          factor2Level: b,
          N: 0,
          mean: NaN,
          sd: NaN,
          se: NaN,
          ci95Lower: NaN,
          ci95Upper: NaN
        });
      }
    });
  });

  // Calculate marginal means for Factor A
  const marginalMeansFactor1: { [key: string]: MarginalStats } = {};
  factorALevels.forEach(a => {
    const levelData = data.filter(d => d.factorA === a).map(d => d.value);
    if (levelData.length > 0) {
      const mean = levelData.reduce((sum, val) => sum + val, 0) / levelData.length;
      const sd = calculateSD(levelData);
      const n = levelData.length;
      const se = calculateSE(sd, n);

      marginalMeansFactor1[a] = { mean, sd, n, se };
    }
  });

  // Calculate marginal means for Factor B
  const marginalMeansFactor2: { [key: string]: MarginalStats } = {};
  factorBLevels.forEach(b => {
    const levelData = data.filter(d => d.factorB === b).map(d => d.value);
    if (levelData.length > 0) {
      const mean = levelData.reduce((sum, val) => sum + val, 0) / levelData.length;
      const sd = calculateSD(levelData);
      const n = levelData.length;
      const se = calculateSE(sd, n);

      marginalMeansFactor2[b] = { mean, sd, n, se };
    }
  });

  // Calculate grand mean and total N
  const allValues = data.map(d => d.value);
  const totalN = allValues.length;
  const grandMean = allValues.reduce((sum, val) => sum + val, 0) / totalN;

  // Calculate sum of squares
  let SSTotal = 0;
  let SSA = 0;
  let SSB = 0;
  let SSAB = 0;
  let SSError = 0;

  // SSTotal: Total sum of squares
  SSTotal = allValues.reduce((sum, val) => sum + Math.pow(val - grandMean, 2), 0);

  // SSA: Sum of squares for Factor A
  factorALevels.forEach(a => {
    const levelData = data.filter(d => d.factorA === a);
    const levelMean = marginalMeansFactor1[a].mean;
    SSA += levelData.length * Math.pow(levelMean - grandMean, 2);
  });

  // SSB: Sum of squares for Factor B
  factorBLevels.forEach(b => {
    const levelData = data.filter(d => d.factorB === b);
    const levelMean = marginalMeansFactor2[b].mean;
    SSB += levelData.length * Math.pow(levelMean - grandMean, 2);
  });

  // SSError and SSAB
  data.forEach(d => {
    const key = `${d.factorA}_${d.factorB}`;
    const cellMean = cellMeans[key];
    if (!isNaN(cellMean)) {
      SSError += Math.pow(d.value - cellMean, 2);
    }
  });

  // SSAB: Interaction sum of squares
  factorALevels.forEach(a => {
    factorBLevels.forEach(b => {
      const key = `${a}_${b}`;
      const cellMean = cellMeans[key];
      const cellCount = cellCounts[key];
      if (!isNaN(cellMean) && cellCount > 0) {
        const factorAMean = marginalMeansFactor1[a].mean;
        const factorBMean = marginalMeansFactor2[b].mean;
        SSAB += cellCount * Math.pow(cellMean - factorAMean - factorBMean + grandMean, 2);
      }
    });
  });

  // Calculate degrees of freedom
  const dfA = factorALevels.length - 1;
  const dfB = factorBLevels.length - 1;
  const dfAB = dfA * dfB;
  const dfError = totalN - (factorALevels.length * factorBLevels.length);
  const dfTotal = totalN - 1;

  // Calculate mean squares
  const MSA = dfA > 0 ? SSA / dfA : 0;
  const MSB = dfB > 0 ? SSB / dfB : 0;
  const MSAB = dfAB > 0 ? SSAB / dfAB : 0;
  const MSError = dfError > 0 ? SSError / dfError : 0;

  // Calculate F statistics
  const FA = MSError > 0 ? MSA / MSError : 0;
  const FB = MSError > 0 ? MSB / MSError : 0;
  const FAB = MSError > 0 ? MSAB / MSError : 0;

  // Calculate p-values
  const pValueA = dfA > 0 && dfError > 0 && FA > 0 ? 1 - jStat.centralF.cdf(FA, dfA, dfError) : 1;
  const pValueB = dfB > 0 && dfError > 0 && FB > 0 ? 1 - jStat.centralF.cdf(FB, dfB, dfError) : 1;
  const pValueAB = dfAB > 0 && dfError > 0 && FAB > 0 ? 1 - jStat.centralF.cdf(FAB, dfAB, dfError) : 1;

  // Calculate effect sizes (eta squared)
  const etaSquaredA = SSTotal > 0 ? SSA / SSTotal : 0;
  const etaSquaredB = SSTotal > 0 ? SSB / SSTotal : 0;
  const etaSquaredAB = SSTotal > 0 ? SSAB / SSTotal : 0;

  // Create ANOVA table
  const anovaTable: ANOVATableRow[] = [
    {
      source: factorAName,
      SS: SSA,
      df: dfA,
      MS: MSA,
      F: FA,
      p: pValueA,
      etaSquared: etaSquaredA
    },
    {
      source: factorBName,
      SS: SSB,
      df: dfB,
      MS: MSB,
      F: FB,
      p: pValueB,
      etaSquared: etaSquaredB
    },
    {
      source: `${factorAName} × ${factorBName}`,
      SS: SSAB,
      df: dfAB,
      MS: MSAB,
      F: FAB,
      p: pValueAB,
      etaSquared: etaSquaredAB
    },
    {
      source: 'Error',
      SS: SSError,
      df: dfError,
      MS: MSError,
      F: null,
      p: null,
      etaSquared: null
    },
    {
      source: 'Total',
      SS: SSTotal,
      df: dfTotal,
      MS: null,
      F: null,
      p: null,
      etaSquared: null
    }
  ];

  // Perform assumptions tests
  const assumptions = performAssumptionsTests(data, factorALevels, factorBLevels, cells);

  return {
    anovaTable,
    descriptiveStats,
    marginalMeansFactor1,
    marginalMeansFactor2,
    grandMean,
    totalN,
    assumptions
  };
};

// Helper function to perform assumptions tests
const performAssumptionsTests = (
  data: TwoWayANOVAData[],
  factorALevels: string[],
  factorBLevels: string[],
  cells: { [key: string]: number[] }
): AssumptionsTests => {
  // Normality test (Shapiro-Wilk approximation)
  const residuals: number[] = [];
  data.forEach(d => {
    const key = `${d.factorA}_${d.factorB}`;
    const cellData = cells[key];
    if (cellData.length > 0) {
      const cellMean = cellData.reduce((sum, val) => sum + val, 0) / cellData.length;
      residuals.push(d.value - cellMean);
    }
  });

  // Simple normality check
  const normalityTest = testNormality(residuals);

  // Homogeneity of variance test (Levene's test approximation)
  const homogeneityTest = testHomogeneity(cells);

  return {
    normality: {
      passed: normalityTest.p > 0.05,
      statistic: normalityTest.statistic,
      p: normalityTest.p,
      test: "Shapiro-Wilk (approximation)"
    },
    homogeneity: {
      passed: homogeneityTest.p > 0.05,
      statistic: homogeneityTest.statistic,
      p: homogeneityTest.p,
      test: "Levene's Test"
    },
    independence: {
      passed: true,
      note: "Assumed based on study design"
    }
  };
};

// Helper function to calculate mean
const calculateMean = (values: number[]): number => {
  if (values.length === 0) return NaN;
  return values.reduce((sum, val) => sum + val, 0) / values.length;
};

// Helper function to calculate median
const calculateMedian = (values: number[]): number => {
  if (values.length === 0) return NaN;
  const sorted = [...values].sort((a, b) => a - b);
  const mid = Math.floor(sorted.length / 2);
  return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
};

// Placeholder for normality test (simplified)
const testNormality = (values: number[]): { statistic: number; p: number } => {
  if (values.length < 3) {
    return { statistic: NaN, p: 1 };
  }

  const n = values.length;
  const mean = calculateMean(values);
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (n - 1);
  const sd = Math.sqrt(variance);

  if (sd === 0) {
    return { statistic: 1, p: 0 };
  }

  // Calculate standardized moments
  const standardized = values.map(val => (val - mean) / sd);
  const m3 = standardized.reduce((sum, val) => sum + Math.pow(val, 3), 0) / n;
  const m4 = standardized.reduce((sum, val) => sum + Math.pow(val, 4), 0) / n;

  // Jarque-Bera test statistic (approximation)
  const JB = (n / 6) * (Math.pow(m3, 2) + Math.pow(m4 - 3, 2) / 4);
  
  // Chi-square approximation with 2 df
  const p = 1 - jStat.chisquare.cdf(JB, 2);

  // Return a pseudo W-statistic (normalized between 0 and 1)
  const W = Math.exp(-JB / n);

  return { statistic: W, p };
};

// Placeholder for Levene's test (simplified)
const testHomogeneity = (cells: { [key: string]: number[] }): { statistic: number; p: number } => {
  const groups = Object.values(cells).filter(cell => cell.length > 0);
  
  if (groups.length < 2) {
    return { statistic: NaN, p: 1 };
  }

  // Calculate deviations from cell medians
  const deviations: number[][] = groups.map(group => {
    const median = calculateMedian(group);
    return group.map(val => Math.abs(val - median));
  });

  // Perform one-way ANOVA on deviations
  const allDeviations = deviations.flat();
  const grandMean = calculateMean(allDeviations);
  
  let SSBetween = 0;
  let SSWithin = 0;
  
  deviations.forEach(group => {
    const groupMean = calculateMean(group);
    SSBetween += group.length * Math.pow(groupMean - grandMean, 2);
    SSWithin += group.reduce((sum, val) => sum + Math.pow(val - groupMean, 2), 0);
  });

  const dfBetween = groups.length - 1;
  const dfWithin = allDeviations.length - groups.length;
  
  const MSBetween = SSBetween / dfBetween;
  const MSWithin = SSWithin / dfWithin;
  
  const F = MSWithin > 0 ? MSBetween / MSWithin : 0;
  const p = 1 - jStat.centralF.cdf(F, dfBetween, dfWithin);

  return { statistic: F, p };
};

// Export helper function to transform component data to ANOVA format
export const prepareDataForANOVA = (
  data: any[],
  dependentVarName: string,
  factor1Name: string,
  factor2Name: string
): TwoWayANOVAData[] => {
  return data.map(row => ({
    value: Number(row[dependentVarName]),
    factorA: String(row[factor1Name]),
    factorB: String(row[factor2Name])
  })).filter(d => !isNaN(d.value));
};
