import jStat from 'jstat';
import { calculateMean, calculateStandardDeviation, calculateVariance } from '../descriptive';

// One-sample t-test
export const oneSampleTTest = (
  data: number[],
  mu: number
): { 
  t: number; 
  df: number; 
  pValue: number; 
  mean: number; 
  se: number; 
  ci95: [number, number];
} => {
  if (data.length < 2) {
    throw new Error('Sample size must be at least 2');
  }
  
  const n = data.length;
  const mean = calculateMean(data);
  const sd = calculateStandardDeviation(data, true);
  const se = sd / Math.sqrt(n);
  const t = (mean - mu) / se;
  const df = n - 1;
  
  // Two-tailed p-value
  const pValue = 2 * (1 - jStat.studentt.cdf(Math.abs(t), df));
  
  // 95% confidence interval
  const criticalT = jStat.studentt.inv(0.975, df);
  const ci95: [number, number] = [mean - criticalT * se, mean + criticalT * se];
  
  return { t, df, pValue, mean, se, ci95 };
};

// Independent samples t-test
interface TTestOptions {
  equalVariances?: boolean;
  alpha?: number;
  alternative?: 'two-sided' | 'less' | 'greater';
}

export const independentSamplesTTest = (
  group1: number[],
  group2: number[],
  options?: TTestOptions
): {
  t: number;
  df: number;
  pValue: number;
  meanDifference: number;
  se: number;
  ci: [number, number]; // Confidence interval based on alpha
  cohensD: number;
  testType: string; // 'Student' or 'Welch'
} => {
  if (group1.length < 2 || group2.length < 2) {
    throw new Error('Each group must have at least 2 observations');
  }

  const n1 = group1.length;
  const n2 = group2.length;
  const mean1 = calculateMean(group1);
  const mean2 = calculateMean(group2);
  const var1 = calculateVariance(group1, true); // Sample variance (n-1 denominator)
  const var2 = calculateVariance(group2, true); // Sample variance (n-1 denominator)

  const meanDifference = mean1 - mean2;

  // Default options
  const { 
    equalVariances = true, 
    alpha = 0.05, 
    alternative = 'two-sided' 
  } = options || {};

  let t: number;
  let df: number;
  let se: number;
  let testType: string;

  if (equalVariances) {
    // Student's t-test (assuming equal variances)
    testType = "Student's t-test";
    const pooledVar = ((n1 - 1) * var1 + (n2 - 1) * var2) / (n1 + n2 - 2);
    se = Math.sqrt(pooledVar * (1 / n1 + 1 / n2));
    t = meanDifference / se;
    df = n1 + n2 - 2;
  } else {
    // Welch's t-test (variances not assumed equal)
    testType = "Welch's t-test";
    se = Math.sqrt(var1 / n1 + var2 / n2);
    t = meanDifference / se;
    // Welch-Satterthwaite equation for degrees of freedom
    const term1 = Math.pow(var1 / n1, 2) / (n1 - 1);
    const term2 = Math.pow(var2 / n2, 2) / (n2 - 1);
    df = Math.pow(var1 / n1 + var2 / n2, 2) / (term1 + term2);
  }

  let pValue: number;
  if (alternative === 'less') { // H1: mean1 < mean2  => meanDifference < 0
    pValue = jStat.studentt.cdf(t, df);
  } else if (alternative === 'greater') { // H1: mean1 > mean2 => meanDifference > 0
    pValue = 1 - jStat.studentt.cdf(t, df);
  } else { // two-sided (default)
    pValue = 2 * (1 - jStat.studentt.cdf(Math.abs(t), df));
  }
  
  // Confidence interval
  const criticalT = jStat.studentt.inv(1 - alpha / 2, df); // For two-sided CI
  const ci: [number, number] = [meanDifference - criticalT * se, meanDifference + criticalT * se];
  
  // Effect size (Cohen's d)
  // For Student's, pooled SD is sqrt(pooledVar). For Welch's, a common approach is to use an average SD or control group's SD.
  // Here, we'll use the pooled SD for simplicity, acknowledging it's an approximation if Welch's is used.
  const pooledSdForCohensD = Math.sqrt(((n1 - 1) * var1 + (n2 - 1) * var2) / (n1 + n2 - 2));
  const cohensD = Math.abs(meanDifference) / pooledSdForCohensD;
  
  return { t, df, pValue, meanDifference, se, ci, cohensD, testType };
};

// Paired samples t-test
export const pairedSamplesTTest = (
  before: number[],
  after: number[]
): {
  t: number;
  df: number;
  pValue: number;
  meanDifference: number;
  se: number;
  ci95: [number, number];
  cohensD: number;
} => {
  if (before.length !== after.length) {
    throw new Error('Paired samples must have the same length');
  }
  
  if (before.length < 2) {
    throw new Error('Sample size must be at least 2');
  }
  
  // Calculate differences
  const differences = before.map((val, i) => val - after[i]);
  
  const n = differences.length;
  const meanDifference = calculateMean(differences);
  const sd = calculateStandardDeviation(differences, true);
  const se = sd / Math.sqrt(n);
  const t = meanDifference / se;
  const df = n - 1;
  
  // Two-tailed p-value
  const pValue = 2 * (1 - jStat.studentt.cdf(Math.abs(t), df));
  
  // 95% confidence interval
  const criticalT = jStat.studentt.inv(0.975, df);
  const ci95: [number, number] = [meanDifference - criticalT * se, meanDifference + criticalT * se];
  
  // Effect size (Cohen's d for paired samples)
  const cohensD = Math.abs(meanDifference) / sd;
  
  return { t, df, pValue, meanDifference, se, ci95, cohensD };
};
