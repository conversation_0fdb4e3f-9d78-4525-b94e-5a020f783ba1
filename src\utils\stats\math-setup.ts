import { create, all } from 'mathjs';

// Initialize math.js with all functions including matrix operations
const config: any = { // Cast to any to bypass strict type checking for config
  matrix: 'Matrix',
  number: 'number',
  // Include all matrix functions
  identity: true
};
export const math = create(all, config) as any; // Cast math to any to handle dynamic properties like eye

// Create an identity matrix function if not available
if (!math.eye) {
  math.eye = (size: number): math.Matrix => { // Ensure it's added to math and returns math.Matrix
    const arr = Array(size).fill(0).map(() => Array(size).fill(0));
    for (let i = 0; i < size; i++) {
      arr[i][i] = 1;
    }
    return math.matrix(arr); // Return as a math.js Matrix object
  };
}
