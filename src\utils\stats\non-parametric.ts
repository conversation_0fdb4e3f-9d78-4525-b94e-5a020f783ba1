import jStat from 'jstat'; // Keep for other functions if they still use it
import kstest from '@stdlib/stats/kstest';
import dmean from '@stdlib/stats/base/dmean'; // For sample mean
import dstdev from '@stdlib/stats/base/dstdev'; // For sample standard deviation

// Interface for items with ranks used in non-parametric tests
interface RankedItem {
  value: number;
  group?: number; // Optional group identifier
  rank?: number; // Assigned rank
}

interface RankedDiffItem {
  diff: number;
  abs: number;
  rank?: number; // Assigned rank
}

// Mann-Whitney U test (Wilcoxon rank-sum test)
export const mannWhitneyUTest = (
  group1: number[],
  group2: number[]
): {
  U: number; // U statistic
  z: number; // Z-score approximation
  pValue: number; // Two-tailed p-value
  n1: number; // Size of group 1
  n2: number; // Size of group 2
} => {
   if (group1.length < 1 || group2.length < 1) {
     throw new Error('Each group must have at least 1 observation');
   }

   const n1 = group1.length;
   const n2 = group2.length;
   const N = n1 + n2;

   // Combine groups, assign ranks, handle ties
   const combined: RankedItem[] = [
     ...group1.map(value => ({ value, group: 1 })),
     ...group2.map(value => ({ value, group: 2 }))
   ].sort((a, b) => a.value - b.value);

   // Assign ranks, handling ties by averaging ranks
   let i = 0;
   while (i < N) {
     let j = i;
     // Find end of tied group
     while (j < N - 1 && combined[j].value === combined[j + 1].value) {
       j++;
     }
     // Calculate average rank for the tied group
     const rank = (i + 1 + j + 1) / 2;
     // Assign average rank to all items in the tied group
     for (let k = i; k <= j; k++) {
       combined[k].rank = rank;
     }
     i = j + 1; // Move to the next item after the tied group
   }

   // Calculate sum of ranks for group 1
   const sumRanks1 = combined
     .filter(item => item.group === 1)
     .reduce((sum, item) => sum + (item.rank || 0), 0); // Use item.rank

   // Calculate U statistics
   const U1 = sumRanks1 - (n1 * (n1 + 1)) / 2;
   const U2 = n1 * n2 - U1;
   const U = Math.min(U1, U2);

   // Calculate expected mean and standard deviation of U
   const meanU = (n1 * n2) / 2;

   // Calculate standard deviation, correcting for ties
   const ties: Record<string, number> = {};
   combined.forEach(item => {
     const key = String(item.value);
     ties[key] = (ties[key] || 0) + 1;
   });

   let tieCorrection = 0;
   Object.values(ties).forEach((count: number) => { // Explicitly type count
     if (count > 1) {
       tieCorrection += (Math.pow(count, 3) - count);
     }
   });

   const varianceU = (n1 * n2 / (N * (N - 1))) * ((Math.pow(N, 3) - N) / 12 - tieCorrection / 12);
   const stdU = Math.sqrt(varianceU);

   // Calculate z-score (with continuity correction)
   const z = stdU > 0 ? (U - meanU) / stdU : 0; // Avoid division by zero

   // Calculate two-tailed p-value using normal approximation
   const pValue = 2 * (1 - jStat.normal.cdf(Math.abs(z), 0, 1));

   return { U, z, pValue, n1, n2 };
 };

// Wilcoxon signed-rank test (for paired samples)
export const wilcoxonSignedRankTest = (
  before: number[],
  after: number[]
): {
  W: number; // W statistic (sum of ranks for the smaller group)
  z: number; // Z-score approximation
  pValue: number; // Two-tailed p-value
  n: number; // Number of non-zero differences
} => {
   if (before.length !== after.length) {
     throw new Error('Paired samples must have the same length');
   }

   // Calculate differences, filter out zeros
   const differences = before.map((val, i) => val - after[i]);
   const nonZeroDifferences = differences.filter(diff => diff !== 0);
   const n = nonZeroDifferences.length; // Effective sample size

   if (n === 0) {
     // Handle case with no non-zero differences
     return { W: NaN, z: NaN, pValue: NaN, n: 0 };
   }
    if (n < 6) {
     console.warn('Sample size (non-zero differences) < 6 for Wilcoxon Signed-Rank test, normal approximation may be inaccurate.');
   }

   // Create objects with difference, absolute difference, and sign
   const rankedDiffs: RankedDiffItem[] = nonZeroDifferences.map(diff => ({
     diff,
     abs: Math.abs(diff),
   })).sort((a, b) => a.abs - b.abs); // Sort by absolute difference

   // Assign ranks to absolute differences, handling ties
   let i = 0;
   while (i < n) {
     let j = i;
     while (j < n - 1 && rankedDiffs[j].abs === rankedDiffs[j + 1].abs) {
       j++;
     }
     const rank = (i + 1 + j + 1) / 2;
     for (let k = i; k <= j; k++) {
       rankedDiffs[k].rank = rank;
     }
     i = j + 1;
   }

   // Calculate sum of ranks for positive and negative differences
   let positiveRankSum = 0;
   let negativeRankSum = 0;
   rankedDiffs.forEach(item => {
     if (item.diff > 0) {
       positiveRankSum += item.rank || 0;
     } else {
       negativeRankSum += item.rank || 0;
     }
   });

   // W statistic is the minimum of the two sums
   const W = Math.min(positiveRankSum, negativeRankSum);

   // Calculate expected mean and standard deviation of W
   const meanW = (n * (n + 1)) / 4;

   // Calculate standard deviation, correcting for ties
   const ties: Record<string, number> = {};
   rankedDiffs.forEach(item => {
     const key = String(item.abs);
     ties[key] = (ties[key] || 0) + 1;
   });

   let tieCorrection = 0;
   Object.values(ties).forEach((count: number) => { // Explicitly type count
     if (count > 1) {
       tieCorrection += (Math.pow(count, 3) - count);
     }
   });

   const varianceW = (n * (n + 1) * (2 * n + 1)) / 24 - tieCorrection / 48;
   const stdW = Math.sqrt(Math.max(0, varianceW)); // Ensure non-negative variance

   // Calculate z-score (with continuity correction)
   const z = stdW > 0 ? (W - meanW) / stdW : 0; // Avoid division by zero

   // Calculate two-tailed p-value
   const pValue = 2 * (1 - jStat.normal.cdf(Math.abs(z), 0, 1));

   return { W, z, pValue, n };
 };

// Kruskal-Wallis test (non-parametric one-way ANOVA)
export const kruskalWallisTest = (
  groups: number[][] // Array of arrays, each inner array is a group
): {
  H: number; // H statistic
  df: number; // Degrees of freedom
  pValue: number; // p-value from Chi-Square distribution
  n: number; // Total sample size
} => {
   const numGroups = groups.length;
   if (numGroups < 2) {
     throw new Error('At least 2 groups required for Kruskal-Wallis test');
   }
   if (groups.some(group => group.length === 0)) {
     throw new Error('All groups must contain at least one observation');
   }

   // Combine all values, keeping track of group index
   let allValues: RankedItem[] = [];
   groups.forEach((group, index) => {
     allValues = [...allValues, ...group.map(value => ({ value, group: index }))];
   });
   const n = allValues.length; // Total sample size

   // Sort all values
   allValues.sort((a, b) => a.value - b.value);

   // Assign ranks, handling ties
   let i = 0;
   while (i < n) {
     let j = i;
     while (j < n - 1 && allValues[j].value === allValues[j + 1].value) {
       j++;
     }
     const rank = (i + 1 + j + 1) / 2;
     for (let k = i; k <= j; k++) {
       allValues[k].rank = rank;
     }
     i = j + 1;
   }

   // Calculate sum of ranks (R_i) and size (n_i) for each group
   const groupStats = groups.map((_, groupIndex) => {
     const groupItems = allValues.filter(item => item.group === groupIndex);
     const rankSum = groupItems.reduce((sum, item) => sum + (item.rank || 0), 0);
     return { n: groupItems.length, rankSum };
   });

   // Calculate Kruskal-Wallis H statistic
   let H = 0;
   groupStats.forEach(stat => {
     if (stat.n > 0) { 
       H += Math.pow(stat.rankSum, 2) / stat.n;
     }
   });
   H = (12 / (n * (n + 1))) * H - 3 * (n + 1);

   // Correction for ties
   const ties: Record<string, number> = {};
   allValues.forEach(item => {
     const key = String(item.value);
     ties[key] = (ties[key] || 0) + 1;
   });

   let tieCorrectionFactor = 0;
   Object.values(ties).forEach((count: number) => { 
     if (count > 1) {
       tieCorrectionFactor += (Math.pow(count, 3) - count);
     }
   });

   const C = tieCorrectionFactor > 0 ? 1 - (tieCorrectionFactor / (Math.pow(n, 3) - n)) : 1;
   const H_corrected = C > 0 ? H / C : H; 

   // Degrees of freedom
   const df = numGroups - 1;

   // Calculate p-value using Chi-Square distribution
   const pValue = 1 - jStat.chisquare.cdf(H_corrected, df);

   return { H: H_corrected, df, pValue, n };
 };

// Chi-square test of independence
export const chiSquareTest = (
  contingencyTable: number[][] // 2D array representing observed frequencies
): {
  chiSquare: number; // Chi-Square statistic
  df: number; // Degrees of freedom
  pValue: number; // p-value
  cramersV: number; // Cramer's V effect size
  hasLowExpectedFrequencies?: boolean; 
  lowFrequencyCells?: Array<{row: number, col: number, expected: number}>; 
} => {
   const numRows = contingencyTable.length;
   if (numRows === 0) {
     throw new Error('Contingency table cannot be empty');
   }
   const numCols = contingencyTable[0]?.length || 0;
   if (numCols === 0) {
     throw new Error('Contingency table must have at least one column');
   }
   if (contingencyTable.some(row => row.length !== numCols)) {
     throw new Error('All rows in the contingency table must have the same number of columns');
   }
   if (contingencyTable.flat().some(cell => cell < 0)) {
     throw new Error('Observed frequencies in the contingency table cannot be negative');
   }

   const rowTotals = contingencyTable.map(row => row.reduce((sum, cell) => sum + cell, 0));
   const columnTotals = Array(numCols).fill(0).map((_, colIndex) =>
     contingencyTable.reduce((sum, row) => sum + row[colIndex], 0)
   );
   const grandTotal = rowTotals.reduce((sum, total) => sum + total, 0);

   if (grandTotal === 0) {
     return { chiSquare: 0, df: (numRows - 1) * (numCols - 1), pValue: 1, cramersV: NaN };
   }

   let chiSquare = 0;
   let hasLowExpectedFrequencies = false;
   const lowFrequencyCells: Array<{row: number, col: number, expected: number}> = [];
   
   for (let i = 0; i < numRows; i++) {
     for (let j = 0; j < numCols; j++) {
       const expectedValue = (rowTotals[i] * columnTotals[j]) / grandTotal;
       
       if (expectedValue < 5) {
          hasLowExpectedFrequencies = true;
          lowFrequencyCells.push({ row: i, col: j, expected: expectedValue });
          console.warn(`Expected frequency in cell (${i}, ${j}) is ${expectedValue.toFixed(2)}, which is less than 5. Chi-Square approximation may be inaccurate.`);
       }
       
       if (expectedValue > 0) { 
         const observed = contingencyTable[i][j];
         chiSquare += Math.pow(observed - expectedValue, 2) / expectedValue;
       }
     }
   }

   const df = (numRows - 1) * (numCols - 1);
   const pValue = df > 0 ? 1 - jStat.chisquare.cdf(chiSquare, df) : NaN; 
   const minDimension = Math.min(numRows, numCols);
   const cramersV = df > 0 ? Math.sqrt(chiSquare / (grandTotal * (minDimension - 1))) : NaN;

   return { 
     chiSquare, 
     df, 
     pValue, 
     cramersV,
     hasLowExpectedFrequencies,
     lowFrequencyCells: lowFrequencyCells.length > 0 ? lowFrequencyCells : undefined
   };
 };
