import jStat from 'jstat';

/**
 * Comprehensive Normality Testing Module
 * 
 * This module provides multiple normality tests commonly used in statistical analysis:
 * - Shapiro-<PERSON>ilk Test (primary for n < 50)
 * - Kolmogorov-Smirnov Test (good for larger samples)
 * - Jarque-Bera Test (based on skewness and kurtosis)
 * - Anderson-Darling Test (sensitive to tail deviations)
 */

export interface NormalityTestResult {
  testName: string;
  statistic: number;
  pValue: number;
  isNormal: boolean;
  alpha: number;
  sampleSize: number;
  interpretation: string;
  recommendation?: string;
}

export interface ComprehensiveNormalityResult {
  sampleSize: number;
  recommendedTest: string;
  tests: {
    shapiroWilk?: NormalityTestResult;
    kolmogorovSmirnov?: NormalityTestResult;
    jarqueBera?: NormalityTestResult;
    andersonDarling?: NormalityTestResult;
  };
  overallAssessment: {
    isNormal: boolean;
    confidence: 'high' | 'medium' | 'low';
    summary: string;
  };
}

/**
 * Shapiro-Wilk Test for Normality
 * Most powerful test for small to medium sample sizes (n ≤ 50)
 */
export const shapiroWilkTest = (data: number[], alpha: number = 0.05): NormalityTestResult => {
  const n = data.length;
  
  if (n < 3) {
    return {
      testName: 'Shapiro-Wilk',
      statistic: NaN,
      pValue: NaN,
      isNormal: false,
      alpha,
      sampleSize: n,
      interpretation: 'Insufficient data for Shapiro-Wilk test (minimum 3 observations required)',
      recommendation: 'Collect more data points'
    };
  }

  if (n > 5000) {
    return {
      testName: 'Shapiro-Wilk',
      statistic: NaN,
      pValue: NaN,
      isNormal: false,
      alpha,
      sampleSize: n,
      interpretation: 'Sample size too large for Shapiro-Wilk test (maximum 5000 observations)',
      recommendation: 'Use Kolmogorov-Smirnov or Jarque-Bera test for large samples'
    };
  }

  const sortedData = [...data].sort((a, b) => a - b);
  
  // Calculate Shapiro-Wilk statistic using the algorithm
  const { W, pValue } = calculateShapiroWilkStatistic(sortedData);
  
  const isNormal = pValue >= alpha;
  
  return {
    testName: 'Shapiro-Wilk',
    statistic: W,
    pValue,
    isNormal,
    alpha,
    sampleSize: n,
    interpretation: isNormal 
      ? `Data appears normally distributed (p = ${pValue.toFixed(4)} ≥ ${alpha})`
      : `Data significantly deviates from normal distribution (p = ${pValue.toFixed(4)} < ${alpha})`,
    recommendation: n <= 50 ? 'Shapiro-Wilk is the recommended test for this sample size' : 'Consider using other tests for larger samples'
  };
};

/**
 * Kolmogorov-Smirnov Test for Normality
 * Good for larger sample sizes, tests overall distribution shape
 */
export const kolmogorovSmirnovTest = (data: number[], alpha: number = 0.05): NormalityTestResult => {
  const n = data.length;
  
  if (n < 3) {
    return {
      testName: 'Kolmogorov-Smirnov',
      statistic: NaN,
      pValue: NaN,
      isNormal: false,
      alpha,
      sampleSize: n,
      interpretation: 'Insufficient data for KS test (minimum 3 observations required)'
    };
  }

  const mean = data.reduce((sum, val) => sum + val, 0) / n;
  const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / n;
  const stdDev = Math.sqrt(variance);

  if (stdDev === 0) {
    return {
      testName: 'Kolmogorov-Smirnov',
      statistic: 0,
      pValue: 1,
      isNormal: true,
      alpha,
      sampleSize: n,
      interpretation: 'All data points are identical - cannot assess normality'
    };
  }

  const sortedData = [...data].sort((a, b) => a - b);
  let maxDifference = 0;
  
  for (let i = 0; i < n; i++) {
    const z = (sortedData[i] - mean) / stdDev;
    const theoreticalCDF = jStat.normal.cdf(z, 0, 1);
    const empiricalCDF = (i + 1) / n;
    const empiricalCDFPrev = i / n;
    
    const diff1 = Math.abs(empiricalCDF - theoreticalCDF);
    const diff2 = Math.abs(empiricalCDFPrev - theoreticalCDF);
    
    maxDifference = Math.max(maxDifference, diff1, diff2);
  }
  
  const pValue = kolmogorovPValue(maxDifference, n);
  const isNormal = pValue >= alpha;
  
  return {
    testName: 'Kolmogorov-Smirnov',
    statistic: maxDifference,
    pValue,
    isNormal,
    alpha,
    sampleSize: n,
    interpretation: isNormal 
      ? `Data appears normally distributed (p = ${pValue.toFixed(4)} ≥ ${alpha})`
      : `Data significantly deviates from normal distribution (p = ${pValue.toFixed(4)} < ${alpha})`,
    recommendation: n >= 50 ? 'KS test is suitable for this sample size' : 'Consider Shapiro-Wilk for smaller samples'
  };
};

/**
 * Jarque-Bera Test for Normality
 * Based on skewness and kurtosis, good for detecting specific types of non-normality
 */
export const jarqueBeraTest = (data: number[], alpha: number = 0.05): NormalityTestResult => {
  const n = data.length;
  
  if (n < 4) {
    return {
      testName: 'Jarque-Bera',
      statistic: NaN,
      pValue: NaN,
      isNormal: false,
      alpha,
      sampleSize: n,
      interpretation: 'Insufficient data for Jarque-Bera test (minimum 4 observations required)'
    };
  }

  const mean = data.reduce((sum, val) => sum + val, 0) / n;
  const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (n - 1);
  const stdDev = Math.sqrt(variance);

  if (stdDev === 0) {
    return {
      testName: 'Jarque-Bera',
      statistic: 0,
      pValue: 1,
      isNormal: true,
      alpha,
      sampleSize: n,
      interpretation: 'All data points are identical - cannot assess normality'
    };
  }

  // Calculate standardized moments
  const standardized = data.map(val => (val - mean) / stdDev);
  const m3 = standardized.reduce((sum, val) => sum + Math.pow(val, 3), 0) / n;
  const m4 = standardized.reduce((sum, val) => sum + Math.pow(val, 4), 0) / n;

  // Calculate skewness and kurtosis
  const skewness = m3;
  const kurtosis = m4 - 3; // Excess kurtosis

  // Jarque-Bera test statistic
  const JB = (n / 6) * (Math.pow(skewness, 2) + Math.pow(kurtosis, 2) / 4);
  
  // Chi-square distribution with 2 degrees of freedom
  const pValue = 1 - jStat.chisquare.cdf(JB, 2);
  const isNormal = pValue >= alpha;
  
  return {
    testName: 'Jarque-Bera',
    statistic: JB,
    pValue,
    isNormal,
    alpha,
    sampleSize: n,
    interpretation: isNormal 
      ? `Data appears normally distributed (p = ${pValue.toFixed(4)} ≥ ${alpha}). Skewness: ${skewness.toFixed(3)}, Kurtosis: ${kurtosis.toFixed(3)}`
      : `Data significantly deviates from normal distribution (p = ${pValue.toFixed(4)} < ${alpha}). Skewness: ${skewness.toFixed(3)}, Kurtosis: ${kurtosis.toFixed(3)}`,
    recommendation: 'Jarque-Bera is particularly sensitive to skewness and kurtosis deviations'
  };
};

// Helper function for Kolmogorov-Smirnov p-value calculation
const kolmogorovPValue = (ksStatistic: number, n: number): number => {
  const sqrtN = Math.sqrt(n);
  const lambda = sqrtN * ksStatistic;
  
  if (lambda < 0.27) return 1.0;
  if (lambda > 3.1) return 0.0;
  
  let sum = 0.0;
  let term: number;
  let j = 1;
  
  do {
    term = Math.exp(-2.0 * j * j * lambda * lambda);
    if (j % 2 === 0) {
      sum -= term;
    } else {
      sum += term;
    }
    j++;
  } while (term > 1e-10 && j <= 100);
  
  return Math.min(1.0, Math.max(0.0, 2.0 * sum));
};

// Improved Shapiro-Wilk calculation based on Royston (1995) algorithm
const calculateShapiroWilkStatistic = (sortedData: number[]): { W: number; pValue: number } => {
  const n = sortedData.length;

  // For very small samples, return conservative results
  if (n <= 3) {
    return { W: 1.0, pValue: 1.0 };
  }

  // Calculate mean
  const mean = sortedData.reduce((sum, val) => sum + val, 0) / n;

  // Calculate sum of squares
  const ss = sortedData.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0);

  if (ss === 0) {
    return { W: 1.0, pValue: 1.0 };
  }

  // Calculate Shapiro-Wilk coefficients (approximation for a_i)
  const coefficients = calculateShapiroWilkCoefficients(n);

  // Calculate the numerator of W statistic
  let numerator = 0;
  for (let i = 0; i < n; i++) {
    numerator += coefficients[i] * sortedData[i];
  }

  // Calculate W statistic
  const W = (numerator * numerator) / ss;

  // Calculate p-value using improved approximation
  const pValue = calculateShapiroWilkPValue(W, n);

  return { W: Math.max(0, Math.min(1, W)), pValue: Math.max(0, Math.min(1, pValue)) };
};

// Calculate Shapiro-Wilk coefficients (a_i) - approximation based on expected order statistics
const calculateShapiroWilkCoefficients = (n: number): number[] => {
  const coefficients = new Array(n).fill(0);

  // Calculate expected order statistics for standard normal distribution
  const expectedValues = new Array(n);
  for (let i = 0; i < n; i++) {
    const p = (i + 1 - 0.375) / (n + 0.25);
    expectedValues[i] = jStat.normal.inv(p, 0, 1);
  }

  // Calculate the sum of squares of expected values
  const sumSquares = expectedValues.reduce((sum, val) => sum + val * val, 0);

  // For the Shapiro-Wilk test, we need to calculate the coefficients
  // This is a simplified approach based on the symmetry of the test
  const c = 1 / Math.sqrt(sumSquares);

  for (let i = 0; i < n; i++) {
    coefficients[i] = c * expectedValues[i];
  }

  return coefficients;
};

// Calculate p-value for Shapiro-Wilk test using improved approximation
const calculateShapiroWilkPValue = (W: number, n: number): number => {
  // Handle edge cases
  if (W >= 1.0) return 1.0;
  if (W <= 0.0) return 0.0;

  // Use different approximations based on sample size
  if (n <= 11) {
    // For small samples, use polynomial approximation
    return calculateSmallSamplePValue(W, n);
  } else {
    // For larger samples, use normal approximation with better transformation
    return calculateLargeSamplePValue(W, n);
  }
};

// P-value calculation for small samples (n <= 11)
const calculateSmallSamplePValue = (W: number, n: number): number => {
  // This is based on tabulated critical values and polynomial approximation
  // The exact implementation would require extensive tables, so we use an approximation

  const logW = Math.log(W);

  // Polynomial coefficients for small sample approximation (corrected)
  let gamma = 0;
  let mu = 0;
  let sigma = 1;

  if (n === 3) {
    gamma = 0; mu = -0.767; sigma = 0.767;
  } else if (n <= 5) {
    gamma = -0.715; mu = -1.038; sigma = 0.715;
  } else if (n <= 7) {
    gamma = -0.480; mu = -0.992; sigma = 0.662;
  } else if (n <= 11) {
    gamma = -0.284; mu = -0.955; sigma = 0.623;
  }

  const y = logW;  // Use logW directly, not -logW
  const z = (y - mu) / sigma;

  // Apply gamma correction for skewness
  if (gamma !== 0) {
    const h = 2 / (gamma * gamma);
    const zp = Math.sqrt(h) * Math.asinh(gamma * z / Math.sqrt(2));
    return 1 - jStat.normal.cdf(zp, 0, 1);
  } else {
    return 1 - jStat.normal.cdf(z, 0, 1);
  }
};

// P-value calculation for larger samples (n > 11)
const calculateLargeSamplePValue = (W: number, n: number): number => {
  // Royston (1995) approximation for larger samples
  const logW = Math.log(W);
  const logN = Math.log(n);

  // Calculate mu (mean of log(W)) - corrected formula
  const mu = -1.2725 + 1.0521 * (logN - Math.log(Math.PI)) - 0.11135 * logN;

  // Calculate sigma (standard deviation of log(W))
  const sigma = Math.exp(-0.4803 - 0.082676 * logN + 0.0030302 * logN * logN);

  // Calculate gamma (skewness parameter) - corrected coefficients
  const gamma = -0.37542 - 0.47145 / n + 1.9279 / (n * n) - 2.1154 / (n * n * n);

  // Normalize
  const z = (logW - mu) / sigma;

  // Apply Johnson transformation for skewness correction
  if (Math.abs(gamma) > 1e-8) {
    // Corrected Johnson transformation
    const h = 2 / (gamma * gamma);
    const zp = Math.sqrt(h) * Math.asinh(gamma * z / Math.sqrt(2));
    return 1 - jStat.normal.cdf(zp, 0, 1);
  } else {
    // For the Shapiro-Wilk test, we want P(W < observed W)
    // Since larger W values indicate more normality, we use 1 - CDF
    return 1 - jStat.normal.cdf(z, 0, 1);
  }
};

/**
 * Anderson-Darling Test for Normality
 * More sensitive to deviations in the tails compared to KS test
 */
export const andersonDarlingTest = (data: number[], alpha: number = 0.05): NormalityTestResult => {
  const n = data.length;

  if (n < 5) {
    return {
      testName: 'Anderson-Darling',
      statistic: NaN,
      pValue: NaN,
      isNormal: false,
      alpha,
      sampleSize: n,
      interpretation: 'Insufficient data for Anderson-Darling test (minimum 5 observations required)'
    };
  }

  const mean = data.reduce((sum, val) => sum + val, 0) / n;
  const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (n - 1);
  const stdDev = Math.sqrt(variance);

  if (stdDev === 0) {
    return {
      testName: 'Anderson-Darling',
      statistic: 0,
      pValue: 1,
      isNormal: true,
      alpha,
      sampleSize: n,
      interpretation: 'All data points are identical - cannot assess normality'
    };
  }

  const sortedData = [...data].sort((a, b) => a - b);

  // Standardize data
  const standardizedData = sortedData.map(val => (val - mean) / stdDev);

  // Calculate Anderson-Darling statistic
  let A2 = 0;

  for (let i = 0; i < n; i++) {
    const zi = standardizedData[i];
    const Fi = jStat.normal.cdf(zi, 0, 1);
    const term1 = (2 * (i + 1) - 1) * Math.log(Fi);
    const term2 = (2 * (n - i) - 1) * Math.log(1 - Fi);

    // Handle edge cases where Fi is very close to 0 or 1
    if (Fi > 1e-10 && Fi < (1 - 1e-10)) {
      A2 += term1 + term2;
    }
  }

  A2 = -n - (A2 / n);

  // Adjust for sample size
  const A2_adjusted = A2 * (1 + 0.75/n + 2.25/(n*n));

  // Calculate approximate p-value using critical values
  const pValue = andersonDarlingPValue(A2_adjusted);
  const isNormal = pValue >= alpha;

  return {
    testName: 'Anderson-Darling',
    statistic: A2_adjusted,
    pValue,
    isNormal,
    alpha,
    sampleSize: n,
    interpretation: isNormal
      ? `Data appears normally distributed (p = ${pValue.toFixed(4)} ≥ ${alpha})`
      : `Data significantly deviates from normal distribution (p = ${pValue.toFixed(4)} < ${alpha})`,
    recommendation: 'Anderson-Darling is particularly sensitive to deviations in the distribution tails'
  };
};

// Helper function for Anderson-Darling p-value approximation
const andersonDarlingPValue = (A2: number): number => {
  // Approximate p-value calculation based on critical values
  // These are approximations based on statistical tables

  if (A2 >= 13.8) return 0.001;
  if (A2 >= 9.5) return 0.005;
  if (A2 >= 6.0) return 0.01;
  if (A2 >= 4.0) return 0.025;
  if (A2 >= 2.5) return 0.05;
  if (A2 >= 1.9) return 0.10;
  if (A2 >= 1.6) return 0.15;
  if (A2 >= 1.3) return 0.25;
  if (A2 >= 1.0) return 0.50;
  if (A2 >= 0.6) return 0.75;
  if (A2 >= 0.3) return 0.90;
  if (A2 >= 0.2) return 0.95;

  return 0.99;
};

/**
 * Comprehensive Normality Testing
 * Runs multiple normality tests and provides an overall assessment
 */
export const comprehensiveNormalityTest = (
  data: number[],
  alpha: number = 0.05,
  testsToRun: string[] = ['auto']
): ComprehensiveNormalityResult => {
  const n = data.length;

  // Determine which tests to run
  let selectedTests: string[];
  if (testsToRun.includes('auto')) {
    if (n <= 50) {
      selectedTests = ['shapiroWilk', 'jarqueBera'];
    } else if (n <= 5000) {
      selectedTests = ['kolmogorovSmirnov', 'jarqueBera', 'andersonDarling'];
    } else {
      selectedTests = ['jarqueBera'];
    }
  } else {
    selectedTests = testsToRun;
  }

  // Determine recommended test
  let recommendedTest: string;
  if (n <= 50) {
    recommendedTest = 'Shapiro-Wilk';
  } else if (n <= 5000) {
    recommendedTest = 'Kolmogorov-Smirnov';
  } else {
    recommendedTest = 'Jarque-Bera';
  }

  // Run selected tests
  const tests: any = {};

  if (selectedTests.includes('shapiroWilk')) {
    tests.shapiroWilk = shapiroWilkTest(data, alpha);
  }

  if (selectedTests.includes('kolmogorovSmirnov')) {
    tests.kolmogorovSmirnov = kolmogorovSmirnovTest(data, alpha);
  }

  if (selectedTests.includes('jarqueBera')) {
    tests.jarqueBera = jarqueBeraTest(data, alpha);
  }

  if (selectedTests.includes('andersonDarling')) {
    tests.andersonDarling = andersonDarlingTest(data, alpha);
  }

  // Overall assessment
  const testResults = Object.values(tests) as NormalityTestResult[];
  const validTests = testResults.filter(test => !isNaN(test.pValue));

  if (validTests.length === 0) {
    return {
      sampleSize: n,
      recommendedTest,
      tests,
      overallAssessment: {
        isNormal: false,
        confidence: 'low',
        summary: 'Unable to assess normality - insufficient data or all tests failed'
      }
    };
  }

  const normalTests = validTests.filter(test => test.isNormal);
  const normalRatio = normalTests.length / validTests.length;

  let isNormal: boolean;
  let confidence: 'high' | 'medium' | 'low';
  let summary: string;

  if (normalRatio === 1) {
    isNormal = true;
    confidence = validTests.length >= 2 ? 'high' : 'medium';
    summary = `All ${validTests.length} test(s) indicate normal distribution`;
  } else if (normalRatio >= 0.5) {
    isNormal = true;
    confidence = 'medium';
    summary = `${normalTests.length} of ${validTests.length} tests indicate normal distribution`;
  } else {
    isNormal = false;
    confidence = validTests.length >= 2 ? 'high' : 'medium';
    summary = `${validTests.length - normalTests.length} of ${validTests.length} tests indicate non-normal distribution`;
  }

  return {
    sampleSize: n,
    recommendedTest,
    tests,
    overallAssessment: {
      isNormal,
      confidence,
      summary
    }
  };
};

/**
 * Legacy function for backward compatibility
 * Maps to the comprehensive testing with automatic test selection
 */
export const isNormallyDistributed = (data: number[]): {
  isNormal: boolean;
  pValue: number;
  statistic: number;
} => {
  const result = comprehensiveNormalityTest(data, 0.05, ['auto']);

  // Return the result from the recommended test
  const recommendedTestName = result.recommendedTest.toLowerCase().replace(/[^a-z]/g, '');
  let testResult: NormalityTestResult | undefined;

  if (recommendedTestName.includes('shapiro')) {
    testResult = result.tests.shapiroWilk;
  } else if (recommendedTestName.includes('kolmogorov')) {
    testResult = result.tests.kolmogorovSmirnov;
  } else if (recommendedTestName.includes('jarque')) {
    testResult = result.tests.jarqueBera;
  }

  if (testResult) {
    return {
      isNormal: testResult.isNormal,
      pValue: testResult.pValue,
      statistic: testResult.statistic
    };
  }

  // Fallback to overall assessment
  return {
    isNormal: result.overallAssessment.isNormal,
    pValue: NaN,
    statistic: NaN
  };
};
