/**
 * Defines strategies for handling ties when ranking data.
 */
export enum TiedRankStrategy {
  AVERAGE = 'average', // Assign the average rank to tied items
  MIN = 'min',       // Assign the minimum rank to tied items (e.g., 1, 2, 2, 4)
  MAX = 'max',       // Assign the maximum rank to tied items (e.g., 1, 3, 3, 4)
  ORDINAL = 'ordinal', // Assign ranks sequentially based on order of appearance (e.g., 1, 2, 3, 4 for ties)
  // DENSE is like MIN but ranks are consecutive integers (e.g., 1, 2, 2, 3)
}

/**
 * Represents a ranked item, including its original value, original index, and calculated rank.
 */
export interface RankedItem<T> {
  originalValue: T;
  originalIndex: number;
  rank: number;
}

interface RankOptions {
  strategy?: TiedRankStrategy;
  ascending?: boolean;
}

const defaultRankOptions: RankOptions = {
  strategy: TiedRankStrategy.AVERAGE,
  ascending: true,
};

/**
 * Ranks an array of numbers or objects with a numeric property.
 *
 * @param data An array of numbers or objects.
 * @param options Configuration for ranking, including tie-breaking strategy and sort order.
 * @param valueAccessor An optional function to extract the numeric value from objects in the data array.
 *                      If data is an array of numbers, this can be omitted.
 * @returns An array of RankedItem objects, sorted by their original index.
 */
export function rankData<T>(
  data: T[],
  options?: RankOptions,
  valueAccessor?: (item: T) => number
): RankedItem<T>[] { // Corrected return type to array
  const opts = { ...defaultRankOptions, ...options };
  const getValue = (item: T, index: number): number => {
    if (valueAccessor) {
      return valueAccessor(item);
    }
    if (typeof item === 'number') {
      return item;
    }
    throw new Error(`Data at index ${index} is not a number and no valueAccessor was provided.`);
  };

  // Create an array of objects with original values and indices to preserve original order later
  const itemsWithIndices = data.map((value, index) => ({
    originalValue: value,
    originalIndex: index,
    numericValue: getValue(value, index),
  }));

  // Sort based on numeric value (and original index for stable sort if values are equal)
  itemsWithIndices.sort((a, b) => {
    const diff = opts.ascending ? a.numericValue - b.numericValue : b.numericValue - a.numericValue;
    if (diff === 0) {
      return a.originalIndex - b.originalIndex; // Stable sort for ties before ranking
    }
    return diff;
  });

  // Assign ranks
  const rankedItems: RankedItem<T>[] = new Array(data.length);
  let i = 0;
  while (i < itemsWithIndices.length) {
    let j = i;
    // Find all items with the same value (ties)
    while (j < itemsWithIndices.length && itemsWithIndices[j].numericValue === itemsWithIndices[i].numericValue) {
      j++;
    }

    const numTies = j - i;
    const firstRankInTieGroup = i + 1;

    let rankToAssign: number | undefined; // Make it possibly undefined initially

    switch (opts.strategy) {
      case TiedRankStrategy.AVERAGE:
        let sumOfRanksInTieGroup = 0;
        for (let k = 0; k < numTies; k++) {
          sumOfRanksInTieGroup += firstRankInTieGroup + k;
        }
        rankToAssign = sumOfRanksInTieGroup / numTies;
        break;
      case TiedRankStrategy.MIN:
        rankToAssign = firstRankInTieGroup;
        break;
      case TiedRankStrategy.MAX:
        rankToAssign = firstRankInTieGroup + numTies - 1;
        break;
      case TiedRankStrategy.ORDINAL:
        // Ranks assigned sequentially in the loop below
        break;
      default:
        throw new Error(`Unknown tie strategy: ${opts.strategy}`);
    }

    for (let k = 0; k < numTies; k++) {
      const currentItem = itemsWithIndices[i + k];
      const assignedRank = opts.strategy === TiedRankStrategy.ORDINAL
        ? firstRankInTieGroup + k // Sequential rank for ORDINAL
        : rankToAssign!; // Use the calculated rank for other strategies (non-null assertion as it's assigned in switch)

      rankedItems[currentItem.originalIndex] = {
        originalValue: currentItem.originalValue,
        originalIndex: currentItem.originalIndex,
        rank: assignedRank,
      };
    }
    i = j;
  }

  return rankedItems;
}
