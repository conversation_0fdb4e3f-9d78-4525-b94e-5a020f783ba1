import jStat from 'jstat';

// Helper function to get Z-score for a given probability (using <PERSON><PERSON><PERSON><PERSON> and Stegun 26.2.23)
export const getZScore = (p: number): number => {
  if (p <= 0.0) return -Infinity;
  if (p >= 1.0) return Infinity;
  if (p === 0.5) return 0;

  // Coefficients for <PERSON>bra<PERSON><PERSON> and Stegun formula 26.2.23
  const c0 = 2.515517;
  const c1 = 0.802853;
  const c2 = 0.010328;

  const d1 = 1.432788;
  const d2 = 0.189269;
  const d3 = 0.001308;

  // p_tail is the probability in the tail
  const p_tail = p < 0.5 ? p : 1 - p;
  const t = Math.sqrt(-2 * Math.log(p_tail));

  const z_approx = t - (c0 + c1 * t + c2 * t * t) / (1 + d1 * t + d2 * t * t + d3 * t * t * t);

  // if original p < 0.5, z is negative
  return p < 0.5 ? -z_approx : z_approx;
};


// One-way ANOVA Sample Size Calculation
export const calculateOneWayAnovaSampleSize = (
  k: number, // Number of groups
  delta: number, // Minimum detectable effect size (difference between group means)
  sigma: number, // Common standard deviation across groups
  alpha: number, // Significance level (e.g., 0.05)
  power: number // Power (e.g., 0.80)
): number => {
  const zAlpha = getZScore(1 - alpha / 2); // Two-tailed alpha
  const zBeta = getZScore(power);

  if (delta === 0 || sigma === 0) {
    return Infinity; // Cannot calculate if no effect or no variability
  }

  const numerator = Math.pow(zAlpha + zBeta, 2) * Math.pow(sigma, 2) * k;
  const denominator = Math.pow(delta, 2);

  return Math.ceil(numerator / denominator);
};

// Factorial ANOVA Sample Size Calculation
export const calculateFactorialAnovaSampleSize = (
  m: number, // Number of experimental conditions (e.g., 2x2 = 4)
  delta: number, // Effect size for the interaction or main effect
  sigma: number, // Common standard deviation
  rho: number, // Expected correlation between factors (if applicable)
  alpha: number, // Significance level
  power: number // Power
): number => {
  const zAlpha = getZScore(1 - alpha / 2);
  const zBeta = getZScore(power);

  if (delta === 0 || sigma === 0) {
    return Infinity;
  }

  const numerator = Math.pow(zAlpha + zBeta, 2) * Math.pow(sigma, 2) * (1 + (m - 1) * rho);
  const denominator = Math.pow(delta, 2) * m;

  return Math.ceil(numerator / denominator);
};

// Repeated Measures ANOVA Sample Size Calculation
export const calculateRepeatedMeasuresAnovaSampleSize = (
  r: number, // Number of repeated measurements
  delta: number, // Effect size across time or conditions
  sigma: number, // Common standard deviation
  rho: number, // Correlation between repeated measures
  alpha: number, // Significance level
  power: number // Power
): number => {
  const zAlpha = getZScore(1 - alpha / 2);
  const zBeta = getZScore(power);

  if (delta === 0 || sigma === 0) {
    return Infinity;
  }

  const numerator = Math.pow(zAlpha + zBeta, 2) * Math.pow(sigma, 2) * (1 - rho);
  const denominator = Math.pow(delta, 2) * r;

  return Math.ceil(numerator / denominator);
};

// One-way ANOVA Power Calculation
export const calculateOneWayAnovaPower = (
  n: number, // Sample size per group
  k: number, // Number of groups
  delta: number, // Minimum detectable effect size (difference between group means)
  sigma: number, // Common standard deviation across groups
  alpha: number // Significance level (e.g., 0.05)
): number => {
  if (n <= 1 || k < 2 || sigma === 0) {
    return 0; // Invalid parameters for power calculation
  }

  // Calculate Cohen's f (effect size)
  const f = delta / sigma;

  // Calculate non-centrality parameter (lambda)
  const lambda = n * f * f * k;

  // Degrees of freedom
  const df1 = k - 1;
  const df2 = k * (n - 1);

  // Critical F-value from central F-distribution
  const fCritical = jStat.centralF.inv(1 - alpha, df1, df2);

  // Power is 1 - CDF of non-central F-distribution at F_critical
  // Check if noncentralF is available, otherwise return a placeholder (e.g., NaN or 0)
  if (jStat.noncentralF && typeof jStat.noncentralF.cdf === 'function') {
    const power = 1 - jStat.noncentralF.cdf(fCritical, df1, df2, lambda);
    return power;
  } else {
    console.warn('jStat.noncentralF.cdf is not available. Power calculation for One-Way ANOVA will be inaccurate.');
    return NaN; // Or return 0, or handle as appropriate for your application
  }
};
