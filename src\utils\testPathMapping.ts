export const testPathMapping: { [key: string]: string } = {
  "Independent Samples t-test": "inference/ttest/independent",
  "Linear Regression": "correlation/linear/simple", // Assuming simple linear regression is the primary entry
  "Paired Samples t-test": "inference/ttest/paired",
  "One-Way ANOVA": "inference/anova/oneway",
  "Logistic Regression": "correlation/logistic",
  "Correlation Analysis": "correlation/matrix", // Or "correlation/linear/simple" for Pearson
  "One-Sample t-test": "inference/ttest/onesample",
  "Kruskal-Wallis Test": "inference/nonparametric/kruskal_wallis",
  "Mann-Whitney U Test": "inference/nonparametric/mann_whitney",
  "Wilcoxon Signed-Rank Test": "inference/nonparametric/wilcoxon_signed_rank", // Assuming general Wilcoxon
  "Two-Way ANOVA": "inference/anova/twoway",
  "Repeated Measures ANOVA": "inference/anova/repeated",
  "Friedman Test": "inference/nonparametric/friedman",
  "Descriptive Statistics": "stats/summary", // Assuming summary statistics
  "Normality Test": "inference/assumptions/normality",
  "Histogram": "charts/histogram",
  "Bar Chart": "charts/barchart",
  "Pie Chart": "charts/piechart",
  "Scatter Plot": "charts/scatterplot",
  "Box Plot": "charts/boxplot",
  "Cross-Tabulation": "stats/crosstabulation",
  "Frequency Tables": "stats/frequency",
  "Sample Size": "samplesize",
  "Power Analysis": "samplesize",
  "Epi-Calculator": "epicalc/main",
  "Cross-Sectional Study": "epicalc/cross_sectional",
  "Cohort Study": "epicalc/cohort",
  "Case-Control": "epicalc/case_control",
  "Matched Case-Control": "epicalc/matched_case_control",
  "Odds Ratio": "epicalc/case_control",
  "Risk Ratio": "epicalc/cohort",
  "Sample Size Calculator": "samplesize",
  "Adjusted OR": "correlation/logistic",
  "Adjusted RR": "correlation/logistic"
};
