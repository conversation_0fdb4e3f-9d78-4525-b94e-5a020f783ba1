/**
 * Test script to validate the training system works correctly
 * Tests the problematic queries identified by the user
 */

import { trainingDB } from './trainingDatabase';
import { initializeCompleteTrainingSystem } from './trainingDataInitializer';

interface TestResult {
  query: string;
  expectedAnalyses: string[];
  actualSuggestions: any[];
  passed: boolean;
  score: number;
}

/**
 * Test the training system with problematic queries
 */
export const testTrainingSystem = (): TestResult[] => {
  // Initialize the training system
  initializeCompleteTrainingSystem();
  
  const testCases = [
    // Original problematic queries
    {
      query: "How do I test if age is correlated with test scores?",
      expectedAnalyses: ["CORR1", "VIZ4", "REG1"], // Correlation Matrix, Scatter Plot, Linear Regression
      description: "Should suggest correlation analysis, not normality tests"
    },
    {
      query: "What test for categorical data?",
      expectedAnalyses: ["CAT1", "DESC2", "DESC3"], // Chi-Square, Frequency Tables, Cross-Tabulation
      description: "Should suggest categorical data analyses"
    },
    {
      query: "Test correlation between variables",
      expectedAnalyses: ["CORR1", "VIZ4"], // Correlation Matrix, Scatter Plot
      description: "Should suggest correlation analysis tools"
    },
    {
      query: "Compare means between two groups",
      expectedAnalyses: ["TTEST2", "NONPAR1"], // Independent T-Test, Mann-Whitney U
      description: "Should suggest group comparison tests"
    },
    {
      query: "Is my data normally distributed?",
      expectedAnalyses: ["NORM1", "VIZ2", "DESC1"], // Normality Tests, Histograms, Descriptive Analysis
      description: "Should suggest normality assessment tools"
    },

    // Data quality assessment queries
    {
      query: "Check data quality and missing values",
      expectedAnalyses: ["DESC1"], // Descriptive Analysis
      description: "Should suggest comprehensive data quality assessment"
    },
    {
      query: "Detect outliers in my data",
      expectedAnalyses: ["DESC1", "VIZ3", "VIZ4"], // Descriptive Analysis, Box Plots, Scatter Plots
      description: "Should suggest outlier detection methods"
    },
    {
      query: "Assess overall data quality",
      expectedAnalyses: ["DESC1"], // Descriptive Analysis
      description: "Should suggest comprehensive quality assessment"
    },

    // Variable pattern recognition queries
    {
      query: "I have variables with similar names like item1, item2, item3",
      expectedAnalyses: ["REL1", "FACTOR1", "DESC1"], // Reliability Analysis, Factor Analysis, Descriptive Analysis
      description: "Should recognize scale items and suggest reliability analysis"
    },
    {
      query: "I have pre/post or before/after variables",
      expectedAnalyses: ["TTEST3", "NONPAR2"], // Paired T-Test, Wilcoxon Signed-Rank Test
      description: "Should recognize paired data structure"
    },

    // Context-aware recommendations
    {
      query: "I have survey data with Likert scales",
      expectedAnalyses: ["DESC1", "REL1", "NONPAR1"], // Descriptive Analysis, Reliability Analysis, Non-parametric tests
      description: "Should recognize survey context and suggest appropriate analyses"
    },
    {
      query: "I have experimental data with treatment groups",
      expectedAnalyses: ["TTEST2", "ANOVA1", "DESC1"], // Independent T-Test, One-Way ANOVA, Descriptive Analysis
      description: "Should recognize experimental design"
    },

    // Advanced analysis scenarios
    {
      query: "Analyze longitudinal data with repeated measures",
      expectedAnalyses: ["ANOVA3", "TTEST3"], // Repeated Measures ANOVA, Paired T-Test
      description: "Should suggest repeated measures analysis"
    },
    {
      query: "Compare multiple treatments with control group",
      expectedAnalyses: ["ANOVA1"], // One-Way ANOVA
      description: "Should suggest ANOVA with post-hoc comparisons"
    },

    // Advanced statistical methods
    {
      query: "Analyze survival data",
      expectedAnalyses: ["ADV4"], // Survival Analysis
      description: "Should suggest survival analysis for time-to-event data"
    },
    {
      query: "Perform factor analysis on my questionnaire data",
      expectedAnalyses: ["ADV1", "ADV3"], // Factor Analysis, Reliability Analysis
      description: "Should suggest factor analysis and reliability assessment"
    },
    {
      query: "Test reliability of my scale",
      expectedAnalyses: ["ADV3"], // Reliability Analysis
      description: "Should suggest reliability analysis for scale assessment"
    },
    {
      query: "Calculate odds ratio for case-control study",
      expectedAnalyses: ["EPI1"], // Case-Control Calculator
      description: "Should suggest epidemiological case-control analysis"
    },
    {
      query: "Calculate sample size for my study",
      expectedAnalyses: ["SS1", "SS2"], // Sample Size Calculators
      description: "Should suggest sample size calculation tools"
    },
    {
      query: "Create Table 1 for my manuscript",
      expectedAnalyses: ["PUB1"], // Table 1 Generator
      description: "Should suggest publication-ready table generation"
    },

    // Critical assumption-aware tests
    {
      query: "How do I compare means between two groups? My data is non normal",
      expectedAnalyses: ["NONPAR1"], // Mann-Whitney U Test
      description: "Should prioritize non-parametric test over t-test when data is non-normal"
    },
    {
      query: "Compare paired data but my data is not normally distributed",
      expectedAnalyses: ["NONPAR2"], // Wilcoxon Signed-Rank Test
      description: "Should suggest non-parametric alternative for paired non-normal data"
    },
    {
      query: "Compare multiple groups but data is not normal",
      expectedAnalyses: ["NONPAR3"], // Kruskal-Wallis Test
      description: "Should suggest non-parametric alternative for multiple group comparison"
    }
  ];

  const results: TestResult[] = [];

  testCases.forEach(testCase => {
    console.log(`\nTesting query: "${testCase.query}"`);
    
    // Get suggestions from the curated database
    const curatedSuggestions = trainingDB.findMatchingSuggestions(testCase.query);
    
    // Extract analysis IDs from suggestions
    const actualAnalysisIds: string[] = [];
    curatedSuggestions.forEach(suggestion => {
      suggestion.suggestions.forEach(sug => {
        if (!actualAnalysisIds.includes(sug.analysisId)) {
          actualAnalysisIds.push(sug.analysisId);
        }
      });
    });

    // Calculate score based on how many expected analyses were found
    const foundExpected = testCase.expectedAnalyses.filter(expected => 
      actualAnalysisIds.includes(expected)
    );
    const score = foundExpected.length / testCase.expectedAnalyses.length;
    const passed = score >= 0.5; // Pass if at least 50% of expected analyses are found

    console.log(`Expected: ${testCase.expectedAnalyses.join(', ')}`);
    console.log(`Found: ${actualAnalysisIds.join(', ')}`);
    console.log(`Score: ${(score * 100).toFixed(1)}% (${foundExpected.length}/${testCase.expectedAnalyses.length})`);
    console.log(`Status: ${passed ? 'PASS' : 'FAIL'}`);

    results.push({
      query: testCase.query,
      expectedAnalyses: testCase.expectedAnalyses,
      actualSuggestions: curatedSuggestions,
      passed,
      score
    });
  });

  return results;
};

/**
 * Generate a test report
 */
export const generateTestReport = (results: TestResult[]): string => {
  const totalTests = results.length;
  const passedTests = results.filter(r => r.passed).length;
  const averageScore = results.reduce((sum, r) => sum + r.score, 0) / totalTests;

  let report = `
# Training System Test Report

## Summary
- Total Tests: ${totalTests}
- Passed: ${passedTests}
- Failed: ${totalTests - passedTests}
- Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%
- Average Score: ${(averageScore * 100).toFixed(1)}%

## Detailed Results

`;

  results.forEach((result, index) => {
    report += `### Test ${index + 1}: ${result.passed ? '✅ PASS' : '❌ FAIL'}
**Query:** "${result.query}"
**Expected:** ${result.expectedAnalyses.join(', ')}
**Found:** ${result.actualSuggestions.length} suggestions
**Score:** ${(result.score * 100).toFixed(1)}%

`;
  });

  return report;
};

/**
 * Run a comprehensive test of the training system
 */
export const runComprehensiveTest = (): void => {
  console.log('🧪 Starting Training System Test...\n');
  
  const results = testTrainingSystem();
  const report = generateTestReport(results);
  
  console.log(report);
  
  // Test database functionality
  console.log('\n🔧 Testing Database Functionality...');
  
  const stats = trainingDB.getStats();
  console.log(`Database Stats:
- Questions: ${stats.totalQuestions}
- Answers: ${stats.totalAnswers}
- Validated Answers: ${stats.validatedAnswers}
- Accuracy Score: ${stats.accuracyScore.toFixed(1)}%
`);

  // Test curated suggestions
  const curatedSuggestions = trainingDB.getCuratedSuggestions();
  console.log(`Curated Suggestions: ${curatedSuggestions.length}`);
  
  console.log('\n✅ Training System Test Complete!');
};

/**
 * Test specific query and show detailed results
 */
export const testSpecificQuery = (query: string): void => {
  console.log(`\n🔍 Testing Query: "${query}"`);
  
  const curatedSuggestions = trainingDB.findMatchingSuggestions(query);
  
  console.log(`Found ${curatedSuggestions.length} curated suggestions:`);
  
  curatedSuggestions.forEach((suggestion, index) => {
    console.log(`\n${index + 1}. Pattern: "${suggestion.questionPattern}"`);
    console.log(`   Keywords: ${suggestion.keywords.join(', ')}`);
    console.log(`   Category: ${suggestion.category}`);
    console.log(`   Validated: ${suggestion.validated ? 'Yes' : 'No'}`);
    console.log(`   Usage: ${suggestion.usage_count} times, Success: ${(suggestion.success_rate * 100).toFixed(1)}%`);
    
    suggestion.suggestions.forEach((sug, sugIndex) => {
      console.log(`   ${sugIndex + 1}. ${sug.analysisId} (Priority: ${sug.priority}, Confidence: ${(sug.confidence * 100).toFixed(0)}%)`);
      console.log(`      Reason: ${sug.reason}`);
    });
  });
};

// Export for use in browser console or testing
if (typeof window !== 'undefined') {
  (window as any).testTrainingSystem = {
    runComprehensiveTest,
    testSpecificQuery,
    testTrainingSystem,
    generateTestReport
  };
}
