/**
 * Training Data Initializer
 * Pre-populates the training database with common statistical questions and correct answers
 */

import { trainingDB, TrainingQuestion, TrainingAnswer } from './trainingDatabase';

interface InitialTrainingData {
  question: Omit<TrainingQuestion, 'id' | 'createdAt' | 'updatedAt'>;
  answers: Omit<TrainingAnswer, 'id' | 'questionId' | 'createdAt'>[];
}

const initialTrainingData: InitialTrainingData[] = [
  // === CORRELATION AND RELATIONSHIPS ===
  {
    question: {
      question: "How do I test if age is correlated with test scores?",
      keywords: ["correlation", "age", "test scores", "relationship", "association"],
      patterns: ["correlat.*age.*test.*scores", "age.*correlat.*test", "test.*scores.*correlat.*age"],
      category: "correlation",
      difficulty: "basic",
      context: "Testing correlation between two continuous variables"
    },
    answers: [
      {
        analysisId: "CORR1",
        priority: "high",
        reason: "Correlation Matrix is the primary tool for examining relationships between continuous variables like age and test scores. Use Pearson correlation for linear relationships.",
        validated: true
      },
      {
        analysisId: "VIZ4",
        priority: "high",
        reason: "Scatter Plot provides visual representation of the relationship between age and test scores, helping identify linear or non-linear patterns.",
        validated: true
      },
      {
        analysisId: "REG1",
        priority: "medium",
        reason: "Linear Regression can model the relationship and predict test scores based on age, providing additional insights beyond correlation.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Test correlation between variables",
      keywords: ["correlation", "variables", "relationship", "association", "pearson", "spearman"],
      patterns: ["test.*correlation", "correlation.*test", "correlation.*between.*variables"],
      category: "correlation",
      difficulty: "basic",
      context: "General correlation analysis between variables"
    },
    answers: [
      {
        analysisId: "CORR1",
        priority: "high",
        reason: "Correlation Matrix calculates Pearson, Spearman, or Kendall correlations between variables, providing comprehensive correlation analysis.",
        validated: true
      },
      {
        analysisId: "VIZ4",
        priority: "high",
        reason: "Scatter Plot visualizes the relationship between two variables, helping identify the nature and strength of correlation.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Which variables are related to each other?",
      keywords: ["related", "variables", "relationships", "associations", "connected", "linked"],
      patterns: ["which.*variables.*related", "variables.*related.*each.*other", "find.*relationships.*variables"],
      category: "correlation",
      difficulty: "basic",
      context: "Exploring relationships between multiple variables"
    },
    answers: [
      {
        analysisId: "CORR1",
        priority: "high",
        reason: "Correlation Matrix shows relationships between all variables simultaneously, helping identify which variables are related.",
        validated: true
      },
      {
        analysisId: "VIZ5",
        priority: "medium",
        reason: "Correlation Heatmap provides visual representation of relationships between multiple variables.",
        validated: true
      }
    ]
  },

  // === CATEGORICAL DATA ANALYSIS ===
  {
    question: {
      question: "What test for categorical data?",
      keywords: ["categorical", "test", "nominal", "ordinal", "chi-square"],
      patterns: ["categorical.*data.*test", "test.*categorical", "nominal.*test", "ordinal.*test"],
      category: "categorical",
      difficulty: "basic",
      context: "Analyzing categorical variables and their relationships"
    },
    answers: [
      {
        analysisId: "CAT1",
        priority: "high",
        reason: "Chi-Square Test is the primary test for categorical data, used to test independence between categorical variables or goodness of fit.",
        validated: true
      },
      {
        analysisId: "DESC2",
        priority: "high",
        reason: "Frequency Tables provide descriptive analysis of categorical data, showing counts and percentages for each category.",
        validated: true
      },
      {
        analysisId: "DESC3",
        priority: "medium",
        reason: "Cross-Tabulation examines relationships between two categorical variables, often used before Chi-Square testing.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "How to test association between categorical variables?",
      keywords: ["association", "categorical", "contingency", "chi-square", "independence"],
      patterns: ["association.*categorical", "categorical.*association", "contingency.*table"],
      category: "categorical",
      difficulty: "intermediate",
      context: "Testing relationships between categorical variables"
    },
    answers: [
      {
        analysisId: "CAT1",
        priority: "high",
        reason: "Chi-Square Test of Independence tests whether two categorical variables are associated.",
        validated: true
      },
      {
        analysisId: "DESC3",
        priority: "high",
        reason: "Cross-Tabulation creates contingency tables to examine the relationship between categorical variables.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Analyze frequency distribution of categories",
      keywords: ["frequency", "distribution", "categories", "counts", "percentages"],
      patterns: ["frequency.*distribution", "analyze.*frequency", "category.*counts"],
      category: "categorical",
      difficulty: "basic",
      context: "Examining distribution of categorical variables"
    },
    answers: [
      {
        analysisId: "DESC2",
        priority: "high",
        reason: "Frequency Tables show the distribution of categorical variables with counts and percentages.",
        validated: true
      },
      {
        analysisId: "VIZ1",
        priority: "high",
        reason: "Bar Charts provide visual representation of frequency distributions for categorical data.",
        validated: true
      }
    ]
  },

  // === ASSUMPTION-AWARE GROUP COMPARISONS ===
  {
    question: {
      question: "How do I compare means between two groups? My data is non normal",
      keywords: ["compare", "means", "two groups", "non normal", "not normal", "skewed", "non-parametric"],
      patterns: ["compare.*means.*two.*groups.*non.*normal", "two.*groups.*not.*normal", "compare.*groups.*skewed", "non.*normal.*two.*groups"],
      category: "comparison",
      difficulty: "intermediate",
      context: "Comparing two groups when normality assumption is violated"
    },
    answers: [
      {
        analysisId: "NONPAR1",
        priority: "high",
        reason: "Mann-Whitney U Test is the non-parametric alternative to independent t-test when data is not normally distributed.",
        validated: true
      },
      {
        analysisId: "VIZ2",
        priority: "medium",
        reason: "Box Plots help visualize the distribution differences between groups when data is non-normal.",
        validated: true
      },
      {
        analysisId: "DESC4",
        priority: "low",
        reason: "Normality Test can confirm the non-normal distribution before choosing non-parametric alternatives.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Compare paired data but my data is not normally distributed",
      keywords: ["paired", "pre post", "before after", "non normal", "not normal", "skewed", "non-parametric"],
      patterns: ["paired.*non.*normal", "pre.*post.*not.*normal", "before.*after.*skewed", "paired.*skewed"],
      category: "comparison",
      difficulty: "intermediate",
      context: "Comparing paired measurements when normality assumption is violated"
    },
    answers: [
      {
        analysisId: "NONPAR2",
        priority: "high",
        reason: "Wilcoxon Signed-Rank Test is the non-parametric alternative to paired t-test when data is not normally distributed.",
        validated: true
      },
      {
        analysisId: "VIZ2",
        priority: "medium",
        reason: "Box Plots can visualize the distribution of differences in paired data.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Compare multiple groups but data is not normal",
      keywords: ["multiple groups", "three groups", "non normal", "not normal", "skewed", "kruskal wallis"],
      patterns: ["multiple.*groups.*non.*normal", "three.*groups.*not.*normal", "groups.*skewed", "non.*normal.*anova"],
      category: "comparison",
      difficulty: "intermediate",
      context: "Comparing multiple groups when normality assumption is violated"
    },
    answers: [
      {
        analysisId: "NONPAR3",
        priority: "high",
        reason: "Kruskal-Wallis Test is the non-parametric alternative to one-way ANOVA when data is not normally distributed.",
        validated: true
      },
      {
        analysisId: "VIZ2",
        priority: "medium",
        reason: "Box Plots help visualize distribution differences across multiple groups when data is non-normal.",
        validated: true
      }
    ]
  },

  // === GROUP COMPARISONS ===
  {
    question: {
      question: "How to compare means between two groups?",
      keywords: ["compare", "means", "two groups", "t-test", "independent"],
      patterns: ["compare.*means.*two.*groups", "two.*groups.*means", "means.*between.*groups"],
      category: "comparison",
      difficulty: "basic",
      context: "Comparing continuous outcomes between two independent groups"
    },
    answers: [
      {
        analysisId: "TTEST2",
        priority: "high",
        reason: "Independent T-Test is the standard test for comparing means between two independent groups when data is normally distributed.",
        validated: true
      },
      {
        analysisId: "NONPAR1",
        priority: "medium",
        reason: "Mann-Whitney U Test is the non-parametric alternative when data is not normally distributed or when dealing with ordinal data.",
        validated: true
      },
      {
        analysisId: "DESC1",
        priority: "low",
        reason: "Descriptive Analysis should be performed first to examine the distribution and characteristics of each group.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Compare pre and post measurements",
      keywords: ["pre", "post", "measurements", "paired", "before", "after"],
      patterns: ["pre.*post", "before.*after", "paired.*measurements"],
      category: "comparison",
      difficulty: "basic",
      context: "Comparing measurements from the same subjects at different time points"
    },
    answers: [
      {
        analysisId: "TTEST3",
        priority: "high",
        reason: "Paired T-Test is designed specifically for comparing measurements from the same subjects at two different time points.",
        validated: true
      },
      {
        analysisId: "NONPAR2",
        priority: "medium",
        reason: "Wilcoxon Signed-Rank Test is the non-parametric alternative for paired data when normality assumptions are violated.",
        validated: true
      }
    ]
  },

  // === VARIABLE ROLE AND DATA STRUCTURE ANALYSIS ===
  {
    question: {
      question: "Which variables should be predictors and which is the outcome?",
      keywords: ["predictors", "outcome", "dependent", "independent", "variables", "target"],
      patterns: ["predictors.*outcome", "dependent.*independent", "target.*variable"],
      category: "other",
      difficulty: "intermediate",
      context: "Identifying variable roles for analysis"
    },
    answers: [
      {
        analysisId: "DESC1",
        priority: "high",
        reason: "Descriptive Analysis helps understand variable characteristics to identify potential predictors and outcomes.",
        validated: true
      },
      {
        analysisId: "CORR1",
        priority: "high",
        reason: "Correlation Matrix shows relationships between variables, helping identify which variables might predict others.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "What analysis is appropriate for my data structure?",
      keywords: ["data structure", "appropriate analysis", "what analysis", "data type"],
      patterns: ["appropriate.*analysis", "what.*analysis.*data", "data.*structure.*analysis"],
      category: "other",
      difficulty: "intermediate",
      context: "Selecting analysis based on data characteristics"
    },
    answers: [
      {
        analysisId: "DESC1",
        priority: "high",
        reason: "Start with Descriptive Analysis to understand your data structure, variable types, and distributions.",
        validated: true
      },
      {
        analysisId: "CORR1",
        priority: "medium",
        reason: "Correlation Matrix helps understand relationships between variables in your dataset.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Analyze scale reliability and factor structure",
      keywords: ["scale", "reliability", "factor", "cronbach", "alpha", "factor analysis"],
      patterns: ["scale.*reliability", "factor.*analysis", "cronbach.*alpha"],
      category: "other",
      difficulty: "advanced",
      context: "Psychometric analysis of scales and questionnaires"
    },
    answers: [
      {
        analysisId: "REL1",
        priority: "high",
        reason: "Reliability Analysis calculates Cronbach's alpha and other reliability measures for scales.",
        validated: true
      },
      {
        analysisId: "FACTOR1",
        priority: "high",
        reason: "Factor Analysis explores the underlying structure of scales and questionnaires.",
        validated: true
      }
    ]
  },

  // === CONTEXT-AWARE RECOMMENDATIONS ===
  {
    question: {
      question: "I have survey data with Likert scales",
      keywords: ["survey", "likert", "scales", "questionnaire", "rating"],
      patterns: ["survey.*data", "likert.*scales", "questionnaire.*data"],
      category: "other",
      difficulty: "intermediate",
      context: "Analyzing survey and questionnaire data"
    },
    answers: [
      {
        analysisId: "DESC1",
        priority: "high",
        reason: "Descriptive Analysis provides appropriate statistics for Likert scale data including medians and frequency distributions.",
        validated: true
      },
      {
        analysisId: "REL1",
        priority: "high",
        reason: "Reliability Analysis assesses internal consistency of Likert scales using Cronbach's alpha.",
        validated: true
      },
      {
        analysisId: "NONPAR1",
        priority: "medium",
        reason: "Non-parametric tests are often more appropriate for ordinal Likert scale data.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "I have experimental data with treatment groups",
      keywords: ["experimental", "treatment", "groups", "intervention", "control"],
      patterns: ["experimental.*data", "treatment.*groups", "intervention.*control"],
      category: "comparison",
      difficulty: "intermediate",
      context: "Analyzing experimental and intervention studies"
    },
    answers: [
      {
        analysisId: "TTEST2",
        priority: "high",
        reason: "Independent T-Test compares outcomes between treatment and control groups in experimental studies.",
        validated: true
      },
      {
        analysisId: "ANOVA1",
        priority: "high",
        reason: "One-Way ANOVA compares outcomes across multiple treatment groups.",
        validated: true
      },
      {
        analysisId: "DESC1",
        priority: "medium",
        reason: "Descriptive Analysis should be performed first to examine baseline characteristics of treatment groups.",
        validated: true
      }
    ]
  },

  // === DATA QUALITY AND NORMALITY TESTING ===
  {
    question: {
      question: "Is my data normally distributed?",
      keywords: ["normal", "distribution", "normality", "shapiro", "kolmogorov", "gaussian"],
      patterns: ["normal.*distribut", "normality.*test", "data.*normal", "gaussian.*distribut"],
      category: "test",
      difficulty: "basic",
      context: "Testing assumptions for parametric tests"
    },
    answers: [
      {
        analysisId: "DESC4",
        priority: "high",
        reason: "Normality Test (Shapiro-Wilk, Kolmogorov-Smirnov) formally tests whether data follows a normal distribution.",
        validated: true
      },
      {
        analysisId: "VIZ3",
        priority: "high",
        reason: "Histograms provide visual assessment of distribution shape and normality.",
        validated: true
      },
      {
        analysisId: "DESC1",
        priority: "medium",
        reason: "Descriptive statistics including skewness and kurtosis provide numerical indicators of distribution shape.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Test assumptions for statistical tests",
      keywords: ["assumptions", "statistical tests", "normality", "homogeneity", "independence"],
      patterns: ["test.*assumptions", "assumptions.*statistical", "check.*assumptions", "validate.*assumptions"],
      category: "test",
      difficulty: "intermediate",
      context: "Verifying statistical assumptions before analysis"
    },
    answers: [
      {
        analysisId: "DESC4",
        priority: "high",
        reason: "Normality Test checks if data meets the normality assumption required for parametric tests.",
        validated: true
      },
      {
        analysisId: "DESC1",
        priority: "high",
        reason: "Descriptive Analysis provides comprehensive assumption checking including normality, outliers, and distribution characteristics.",
        validated: true
      },
      {
        analysisId: "VIZ3",
        priority: "medium",
        reason: "Histograms help visually assess normality and other distributional assumptions.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Check data quality and missing values",
      keywords: ["data quality", "missing values", "missing data", "quality assessment", "data cleaning"],
      patterns: ["data.*quality", "missing.*values", "missing.*data", "quality.*assessment"],
      category: "descriptive",
      difficulty: "basic",
      context: "Assessing data quality and completeness"
    },
    answers: [
      {
        analysisId: "DESC1",
        priority: "high",
        reason: "Descriptive Analysis provides comprehensive data quality assessment including missing value patterns and basic statistics.",
        validated: true
      },
      {
        analysisId: "VIZ2",
        priority: "medium",
        reason: "Histograms help identify data distribution issues and potential quality problems.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Detect outliers in my data",
      keywords: ["outliers", "outlier detection", "extreme values", "anomalies"],
      patterns: ["detect.*outliers", "outlier.*detection", "extreme.*values", "find.*outliers"],
      category: "descriptive",
      difficulty: "intermediate",
      context: "Identifying unusual or extreme values in the dataset"
    },
    answers: [
      {
        analysisId: "DESC1",
        priority: "high",
        reason: "Descriptive Analysis includes outlier detection using IQR method and z-scores to identify extreme values.",
        validated: true
      },
      {
        analysisId: "VIZ3",
        priority: "high",
        reason: "Box Plots are excellent for visualizing outliers and understanding data distribution.",
        validated: true
      },
      {
        analysisId: "VIZ4",
        priority: "medium",
        reason: "Scatter Plots can help identify outliers in bivariate relationships.",
        validated: true
      }
    ]
  },

  // === DESCRIPTIVE ANALYSIS AND EXPLORATION ===
  {
    question: {
      question: "Summarize my data",
      keywords: ["summarize", "summary", "descriptive", "statistics", "overview"],
      patterns: ["summarize.*data", "data.*summary", "descriptive.*statistics"],
      category: "descriptive",
      difficulty: "basic",
      context: "Getting an overview of dataset characteristics"
    },
    answers: [
      {
        analysisId: "DESC1",
        priority: "high",
        reason: "Descriptive Analysis provides comprehensive summary statistics including central tendency, variability, and distribution shape.",
        validated: true
      },
      {
        analysisId: "DESC2",
        priority: "medium",
        reason: "Frequency Tables summarize categorical variables with counts and percentages.",
        validated: true
      },
      {
        analysisId: "VIZ2",
        priority: "medium",
        reason: "Histograms provide visual summary of data distributions.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Explore data patterns and distributions",
      keywords: ["explore", "patterns", "distributions", "data exploration", "understand data"],
      patterns: ["explore.*data", "data.*patterns", "understand.*data", "data.*exploration"],
      category: "descriptive",
      difficulty: "basic",
      context: "Initial data exploration and pattern identification"
    },
    answers: [
      {
        analysisId: "DESC1",
        priority: "high",
        reason: "Descriptive Analysis reveals data patterns, distributions, and potential issues in your dataset.",
        validated: true
      },
      {
        analysisId: "VIZ2",
        priority: "high",
        reason: "Histograms show the shape and distribution of continuous variables.",
        validated: true
      },
      {
        analysisId: "CORR1",
        priority: "medium",
        reason: "Correlation Matrix reveals relationships and patterns between variables.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Compare pre and post measurements",
      keywords: ["pre", "post", "measurements", "paired", "before", "after"],
      patterns: ["pre.*post", "before.*after", "paired.*measurements"],
      category: "comparison",
      difficulty: "basic",
      context: "Comparing measurements from the same subjects at different time points"
    },
    answers: [
      {
        analysisId: "TTEST3",
        priority: "high",
        reason: "Paired T-Test is designed specifically for comparing measurements from the same subjects at two different time points.",
        validated: true
      },
      {
        analysisId: "NONPAR2",
        priority: "medium",
        reason: "Wilcoxon Signed-Rank Test is the non-parametric alternative for paired data when normality assumptions are violated.",
        validated: true
      }
    ]
  },

  // === PREDICTION AND REGRESSION ===
  {
    question: {
      question: "Predict binary outcome",
      keywords: ["predict", "binary", "logistic", "classification", "yes no"],
      patterns: ["predict.*binary", "binary.*outcome", "logistic.*regression"],
      category: "prediction",
      difficulty: "intermediate",
      context: "Predicting binary/dichotomous outcomes"
    },
    answers: [
      {
        analysisId: "REG2",
        priority: "high",
        reason: "Logistic Regression is specifically designed for predicting binary outcomes and provides odds ratios.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Predict continuous outcome from multiple variables",
      keywords: ["predict", "continuous", "multiple variables", "linear regression", "model"],
      patterns: ["predict.*continuous", "multiple.*variables.*predict", "linear.*regression"],
      category: "prediction",
      difficulty: "intermediate",
      context: "Predicting continuous outcomes using multiple predictors"
    },
    answers: [
      {
        analysisId: "REG1",
        priority: "high",
        reason: "Linear Regression models continuous outcomes using multiple predictor variables.",
        validated: true
      },
      {
        analysisId: "CORR1",
        priority: "medium",
        reason: "Correlation Matrix helps identify which variables are most strongly related to the outcome.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Build predictive model",
      keywords: ["build", "predictive model", "modeling", "prediction", "machine learning"],
      patterns: ["build.*model", "predictive.*model", "machine.*learning"],
      category: "prediction",
      difficulty: "advanced",
      context: "Creating predictive models from data"
    },
    answers: [
      {
        analysisId: "REG1",
        priority: "high",
        reason: "Linear Regression is a fundamental predictive modeling technique for continuous outcomes.",
        validated: true
      },
      {
        analysisId: "REG2",
        priority: "high",
        reason: "Logistic Regression is used for binary classification and prediction problems.",
        validated: true
      }
    ]
  },

  // === ADVANCED AND SPECIALIZED ANALYSIS ===
  {
    question: {
      question: "Analyze survival data",
      keywords: ["survival", "time to event", "kaplan", "meier", "hazard", "cox regression", "censored"],
      patterns: ["survival.*data", "time.*to.*event", "kaplan.*meier", "cox.*regression", "hazard.*ratio"],
      category: "other",
      difficulty: "advanced",
      context: "Time-to-event analysis with censoring"
    },
    answers: [
      {
        analysisId: "ADV4",
        priority: "high",
        reason: "Survival Analysis provides comprehensive time-to-event analysis including Kaplan-Meier curves and Cox proportional hazards regression.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Perform factor analysis on my questionnaire data",
      keywords: ["factor analysis", "questionnaire", "scale", "construct", "latent variables", "EFA"],
      patterns: ["factor.*analysis", "questionnaire.*factor", "scale.*factor", "construct.*validity"],
      category: "other",
      difficulty: "advanced",
      context: "Exploring underlying factor structure in questionnaire data"
    },
    answers: [
      {
        analysisId: "ADV1",
        priority: "high",
        reason: "Exploratory Factor Analysis identifies underlying factors and constructs in questionnaire or scale data.",
        validated: true
      },
      {
        analysisId: "ADV3",
        priority: "medium",
        reason: "Reliability Analysis should be performed first to assess internal consistency of scales before factor analysis.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Test reliability of my scale",
      keywords: ["reliability", "cronbach", "alpha", "internal consistency", "scale reliability"],
      patterns: ["reliability.*analysis", "cronbach.*alpha", "internal.*consistency", "scale.*reliability"],
      category: "other",
      difficulty: "intermediate",
      context: "Assessing internal consistency and reliability of measurement scales"
    },
    answers: [
      {
        analysisId: "ADV3",
        priority: "high",
        reason: "Reliability Analysis calculates Cronbach's alpha and other reliability measures to assess internal consistency of scales.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Perform cluster analysis to group my data",
      keywords: ["cluster analysis", "grouping", "k-means", "hierarchical", "segmentation"],
      patterns: ["cluster.*analysis", "group.*data", "k.*means", "hierarchical.*cluster"],
      category: "other",
      difficulty: "advanced",
      context: "Grouping similar observations or cases"
    },
    answers: [
      {
        analysisId: "ADV5",
        priority: "high",
        reason: "Cluster Analysis groups similar data points using methods like K-means or hierarchical clustering.",
        validated: true
      },
      {
        analysisId: "DESC1",
        priority: "medium",
        reason: "Descriptive Analysis should be performed first to understand variable distributions before clustering.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Conduct meta-analysis of multiple studies",
      keywords: ["meta analysis", "meta-analysis", "systematic review", "effect size", "forest plot"],
      patterns: ["meta.*analysis", "systematic.*review", "effect.*size.*synthesis", "forest.*plot"],
      category: "other",
      difficulty: "advanced",
      context: "Synthesizing findings from multiple research studies"
    },
    answers: [
      {
        analysisId: "ADV6",
        priority: "high",
        reason: "Meta Analysis synthesizes findings from multiple studies and provides pooled effect size estimates.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "What test should I use for multiple groups?",
      keywords: ["multiple groups", "anova", "kruskal", "wallis", "three groups"],
      patterns: ["multiple.*groups", "three.*groups", "more.*than.*two.*groups"],
      category: "comparison",
      difficulty: "intermediate",
      context: "Comparing outcomes across three or more groups"
    },
    answers: [
      {
        analysisId: "ANOVA1",
        priority: "high",
        reason: "One-Way ANOVA is used to compare means across three or more independent groups when data is normally distributed.",
        validated: true
      },
      {
        analysisId: "NONPAR3",
        priority: "medium",
        reason: "Kruskal-Wallis Test is the non-parametric alternative for comparing multiple groups when normality assumptions are violated.",
        validated: true
      },
      {
        analysisId: "POST1",
        priority: "medium",
        reason: "Post-hoc tests are needed after ANOVA to determine which specific groups differ from each other.",
        validated: true
      }
    ]
  },

  // === EPIDEMIOLOGY AND STUDY DESIGN ===
  {
    question: {
      question: "Calculate odds ratio for case-control study",
      keywords: ["odds ratio", "case control", "case-control", "epidemiology", "retrospective"],
      patterns: ["odds.*ratio", "case.*control", "epidemiolog", "retrospective.*study"],
      category: "other",
      difficulty: "intermediate",
      context: "Epidemiological analysis for case-control studies"
    },
    answers: [
      {
        analysisId: "EPI1",
        priority: "high",
        reason: "Case-Control Calculator computes odds ratios and confidence intervals for case-control study designs.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Calculate risk ratio for cohort study",
      keywords: ["risk ratio", "cohort", "prospective", "incidence", "follow-up"],
      patterns: ["risk.*ratio", "cohort.*study", "prospective.*study", "incidence.*rate"],
      category: "other",
      difficulty: "intermediate",
      context: "Epidemiological analysis for cohort studies"
    },
    answers: [
      {
        analysisId: "EPI2",
        priority: "high",
        reason: "Cohort Calculator computes risk ratios and rate ratios for prospective cohort studies.",
        validated: true
      }
    ]
  },

  // === SAMPLE SIZE AND POWER ANALYSIS ===
  {
    question: {
      question: "Calculate sample size for my study",
      keywords: ["sample size", "power analysis", "study planning", "power calculation"],
      patterns: ["sample.*size", "power.*analysis", "study.*planning", "power.*calculation"],
      category: "other",
      difficulty: "intermediate",
      context: "Planning study sample size and statistical power"
    },
    answers: [
      {
        analysisId: "SS1",
        priority: "high",
        reason: "One Sample Calculator determines appropriate sample size for single group studies.",
        validated: true
      },
      {
        analysisId: "SS2",
        priority: "high",
        reason: "Two Sample Calculator determines sample size for comparing two groups.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Power analysis for comparing two groups",
      keywords: ["power analysis", "two groups", "sample size", "effect size", "comparison"],
      patterns: ["power.*analysis.*two.*groups", "sample.*size.*comparison", "effect.*size.*power"],
      category: "other",
      difficulty: "intermediate",
      context: "Power analysis for group comparison studies"
    },
    answers: [
      {
        analysisId: "SS2",
        priority: "high",
        reason: "Two Sample Calculator provides power analysis and sample size calculations for comparing two groups.",
        validated: true
      }
    ]
  },

  // === VISUALIZATION AND PRESENTATION ===
  {
    question: {
      question: "How to visualize my data?",
      keywords: ["visualize", "plot", "chart", "graph", "visualization"],
      patterns: ["visualize.*data", "plot.*data", "chart.*data", "graph.*data"],
      category: "visualization",
      difficulty: "basic",
      context: "Creating appropriate visualizations for data"
    },
    answers: [
      {
        analysisId: "VIZ2",
        priority: "high",
        reason: "Histograms are excellent for visualizing the distribution of continuous variables.",
        validated: true
      },
      {
        analysisId: "VIZ1",
        priority: "high",
        reason: "Bar Charts are ideal for displaying categorical data and frequency distributions.",
        validated: true
      },
      {
        analysisId: "VIZ4",
        priority: "medium",
        reason: "Scatter Plots show relationships between two continuous variables.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Create publication-ready tables and figures",
      keywords: ["publication", "tables", "figures", "manuscript", "journal"],
      patterns: ["publication.*ready", "manuscript.*tables", "journal.*figures"],
      category: "visualization",
      difficulty: "advanced",
      context: "Preparing results for academic publication"
    },
    answers: [
      {
        analysisId: "PUB1",
        priority: "high",
        reason: "Table 1 Generator creates publication-ready descriptive statistics tables.",
        validated: true
      },
      {
        analysisId: "PUB2",
        priority: "high",
        reason: "Results tables provide formatted output suitable for manuscripts.",
        validated: true
      }
    ]
  },

  // === PUBLICATION READY OUTPUTS ===
  {
    question: {
      question: "Create Table 1 for my manuscript",
      keywords: ["table 1", "baseline characteristics", "demographics", "manuscript", "publication"],
      patterns: ["table.*1", "baseline.*characteristics", "demographics.*table", "manuscript.*table"],
      category: "other",
      difficulty: "basic",
      context: "Creating publication-ready baseline characteristics table"
    },
    answers: [
      {
        analysisId: "PUB1",
        priority: "high",
        reason: "Table 1 Generator creates publication-ready baseline characteristics tables with proper formatting.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Format regression results for publication",
      keywords: ["regression table", "publication", "manuscript", "coefficients", "odds ratios"],
      patterns: ["regression.*table", "format.*regression", "publication.*regression", "manuscript.*results"],
      category: "other",
      difficulty: "intermediate",
      context: "Creating publication-ready regression results tables"
    },
    answers: [
      {
        analysisId: "PUB4",
        priority: "high",
        reason: "Regression Table formats regression coefficients, odds ratios, and confidence intervals for publication.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Calculate standardized mean differences",
      keywords: ["SMD", "standardized mean difference", "effect size", "cohen's d", "balance"],
      patterns: ["standardized.*mean.*difference", "SMD", "effect.*size", "cohen.*d"],
      category: "other",
      difficulty: "intermediate",
      context: "Calculating effect sizes and standardized mean differences"
    },
    answers: [
      {
        analysisId: "PUB3",
        priority: "high",
        reason: "SMD Table calculates standardized mean differences and effect sizes for group comparisons.",
        validated: true
      }
    ]
  },

  // === SPECIFIC DATA PATTERNS AND NAMING CONVENTIONS ===
  {
    question: {
      question: "I have variables with similar names like item1, item2, item3",
      keywords: ["similar names", "item1", "item2", "scale items", "numbered variables"],
      patterns: ["item\\d+", "similar.*names", "numbered.*variables", "scale.*items"],
      category: "other",
      difficulty: "intermediate",
      context: "Analyzing variables with systematic naming patterns"
    },
    answers: [
      {
        analysisId: "REL1",
        priority: "high",
        reason: "Variables with similar names often represent scale items that should be analyzed for reliability using Cronbach's alpha.",
        validated: true
      },
      {
        analysisId: "FACTOR1",
        priority: "medium",
        reason: "Factor Analysis can explore whether similarly named items measure the same underlying construct.",
        validated: true
      },
      {
        analysisId: "DESC1",
        priority: "medium",
        reason: "Descriptive Analysis helps understand the distribution and characteristics of scale items.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "I have pre/post or before/after variables",
      keywords: ["pre", "post", "before", "after", "time1", "time2", "baseline", "followup"],
      patterns: ["pre.*post", "before.*after", "time\\d+", "baseline.*followup"],
      category: "comparison",
      difficulty: "basic",
      context: "Analyzing repeated measures or longitudinal data"
    },
    answers: [
      {
        analysisId: "TTEST3",
        priority: "high",
        reason: "Paired T-Test is specifically designed for comparing pre/post or before/after measurements.",
        validated: true
      },
      {
        analysisId: "NONPAR2",
        priority: "medium",
        reason: "Wilcoxon Signed-Rank Test is the non-parametric alternative for paired comparisons.",
        validated: true
      }
    ]
  },

  // === ADVANCED DATA QUALITY SCENARIOS ===
  {
    question: {
      question: "My data has many missing values",
      keywords: ["missing values", "missing data", "incomplete data", "na values"],
      patterns: ["missing.*values", "missing.*data", "incomplete.*data", "na.*values"],
      category: "descriptive",
      difficulty: "intermediate",
      context: "Handling datasets with substantial missing data"
    },
    answers: [
      {
        analysisId: "DESC1",
        priority: "high",
        reason: "Descriptive Analysis provides comprehensive missing data analysis including patterns and percentages.",
        validated: true
      }
    ]
  },
  {
    question: {
      question: "Check if my data meets assumptions for statistical tests",
      keywords: ["assumptions", "statistical tests", "normality", "homogeneity", "independence"],
      patterns: ["assumptions.*tests", "test.*assumptions", "normality.*assumptions"],
      category: "test",
      difficulty: "intermediate",
      context: "Verifying statistical assumptions before analysis"
    },
    answers: [
      {
        analysisId: "NORM1",
        priority: "high",
        reason: "Normality Tests check if data meets the normality assumption required for parametric tests.",
        validated: true
      },
      {
        analysisId: "DESC1",
        priority: "high",
        reason: "Descriptive Analysis provides information about data distribution and potential assumption violations.",
        validated: true
      },
      {
        analysisId: "VIZ2",
        priority: "medium",
        reason: "Histograms and Q-Q plots help visually assess normality assumptions.",
        validated: true
      }
    ]
  }
];

/**
 * Initialize the training database with pre-defined questions and answers
 */
export const initializeTrainingData = (): void => {
  const existingQuestions = trainingDB.getAllQuestions();
  
  // Only initialize if database is empty
  if (existingQuestions.length === 0) {
    console.log('Initializing Analysis Assistant training data...');
    
    initialTrainingData.forEach(data => {
      // Add the question
      const questionId = trainingDB.addQuestion(data.question);
      
      // Add all answers for this question
      data.answers.forEach(answer => {
        trainingDB.addAnswer({
          ...answer,
          questionId
        });
      });
    });
    
    console.log(`Initialized ${initialTrainingData.length} training questions with answers`);
  }
};

/**
 * Add additional training questions for specific scenarios
 */
export const addSpecificTrainingQuestions = (): void => {
  // Check if specific questions already exist
  const existingQuestions = trainingDB.getAllQuestions();
  const hasSpecificQuestions = existingQuestions.some(q =>
    q.question === "Analyze longitudinal data with repeated measures" ||
    q.question === "Compare multiple treatments with control group"
  );

  if (hasSpecificQuestions) {
    console.log('Specific training questions already exist, skipping addition');
    return;
  }

  console.log('Adding specific training questions...');
  const additionalQuestions: InitialTrainingData[] = [
    {
      question: {
        question: "Analyze longitudinal data with repeated measures",
        keywords: ["longitudinal", "repeated measures", "time series", "panel data", "within subjects"],
        patterns: ["longitudinal.*data", "repeated.*measures", "within.*subjects", "time.*series"],
        category: "comparison",
        difficulty: "advanced",
        context: "Analyzing data with multiple time points or repeated observations"
      },
      answers: [
        {
          analysisId: "ANOVA3",
          priority: "high",
          reason: "Repeated Measures ANOVA is designed for analyzing data with multiple measurements from the same subjects over time.",
          validated: true
        },
        {
          analysisId: "TTEST3",
          priority: "medium",
          reason: "Paired T-Test can be used for simple before/after comparisons within the longitudinal design.",
          validated: true
        }
      ]
    },
    {
      question: {
        question: "Compare multiple treatments with control group",
        keywords: ["multiple treatments", "control group", "treatment comparison", "intervention study"],
        patterns: ["multiple.*treatments", "treatment.*control", "intervention.*study"],
        category: "comparison",
        difficulty: "intermediate",
        context: "Comparing several treatment conditions against a control"
      },
      answers: [
        {
          analysisId: "ANOVA1",
          priority: "high",
          reason: "One-Way ANOVA compares means across multiple treatment groups including control.",
          validated: true
        },
        {
          analysisId: "POST1",
          priority: "high",
          reason: "Post-hoc tests identify which specific treatments differ from control and from each other.",
          validated: true
        }
      ]
    },
    {
      question: {
        question: "Analyze dose-response relationships",
        keywords: ["dose response", "dose effect", "concentration", "dosage", "gradient"],
        patterns: ["dose.*response", "dose.*effect", "concentration.*effect"],
        category: "correlation",
        difficulty: "advanced",
        context: "Examining relationships between dose/concentration and response"
      },
      answers: [
        {
          analysisId: "CORR1",
          priority: "high",
          reason: "Correlation analysis examines the strength and direction of dose-response relationships.",
          validated: true
        },
        {
          analysisId: "REG1",
          priority: "high",
          reason: "Linear Regression models dose-response relationships and can predict responses at different doses.",
          validated: true
        },
        {
          analysisId: "VIZ4",
          priority: "medium",
          reason: "Scatter plots visualize dose-response curves and help identify linear or non-linear relationships.",
          validated: true
        }
      ]
    },
    {
      question: {
        question: "Analyze diagnostic test performance",
        keywords: ["diagnostic test", "sensitivity", "specificity", "roc curve", "accuracy"],
        patterns: ["diagnostic.*test", "sensitivity.*specificity", "roc.*curve", "test.*accuracy"],
        category: "test",
        difficulty: "advanced",
        context: "Evaluating the performance of diagnostic or screening tests"
      },
      answers: [
        {
          analysisId: "ROC1",
          priority: "high",
          reason: "ROC Analysis evaluates diagnostic test performance including sensitivity, specificity, and AUC.",
          validated: true
        },
        {
          analysisId: "DESC3",
          priority: "medium",
          reason: "Cross-tabulation creates confusion matrices for diagnostic test evaluation.",
          validated: true
        }
      ]
    }
  ];

  additionalQuestions.forEach(data => {
    const questionId = trainingDB.addQuestion(data.question);
    data.answers.forEach(answer => {
      trainingDB.addAnswer({
        ...answer,
        questionId
      });
    });
  });
};

/**
 * Add comprehensive data quality assessment training questions
 */
export const addDataQualityTrainingQuestions = (): void => {
  const existingQuestions = trainingDB.getAllQuestions();
  const hasDataQualityQuestions = existingQuestions.some(q =>
    q.question === "Assess overall data quality" ||
    q.question === "Identify data inconsistencies"
  );

  if (hasDataQualityQuestions) {
    console.log('Data quality training questions already exist, skipping addition');
    return;
  }

  console.log('Adding data quality assessment training questions...');
  const dataQualityQuestions: InitialTrainingData[] = [
    {
      question: {
        question: "Assess overall data quality",
        keywords: ["data quality", "quality assessment", "data validation", "data integrity"],
        patterns: ["assess.*quality", "data.*quality.*assessment", "quality.*check"],
        category: "descriptive",
        difficulty: "intermediate",
        context: "Comprehensive evaluation of dataset quality and reliability"
      },
      answers: [
        {
          analysisId: "DESC1",
          priority: "high",
          reason: "Descriptive Analysis provides comprehensive data quality assessment including missing values, outliers, and distribution analysis.",
          validated: true
        }
      ]
    },
    {
      question: {
        question: "Identify data inconsistencies",
        keywords: ["inconsistencies", "data errors", "data validation", "consistency check"],
        patterns: ["inconsistenc", "data.*errors", "validation.*check", "consistency.*check"],
        category: "descriptive",
        difficulty: "intermediate",
        context: "Finding and documenting data inconsistencies and errors"
      },
      answers: [
        {
          analysisId: "DESC1",
          priority: "high",
          reason: "Descriptive Analysis includes consistency checks and identifies potential data entry errors and inconsistencies.",
          validated: true
        }
      ]
    },
    {
      question: {
        question: "Analyze missing data patterns",
        keywords: ["missing data patterns", "missing data analysis", "missingness", "data completeness"],
        patterns: ["missing.*patterns", "missing.*data.*analysis", "data.*completeness"],
        category: "descriptive",
        difficulty: "intermediate",
        context: "Understanding patterns and mechanisms of missing data"
      },
      answers: [
        {
          analysisId: "DESC1",
          priority: "high",
          reason: "Descriptive Analysis provides detailed missing data analysis including patterns, percentages, and recommendations for handling missing values.",
          validated: true
        }
      ]
    },
    {
      question: {
        question: "Validate data types and formats",
        keywords: ["data types", "data validation", "format validation", "type checking"],
        patterns: ["data.*types.*validation", "format.*validation", "type.*checking"],
        category: "descriptive",
        difficulty: "basic",
        context: "Ensuring data types are appropriate and consistent"
      },
      answers: [
        {
          analysisId: "DESC1",
          priority: "high",
          reason: "Descriptive Analysis includes data type validation and identifies variables that may need type conversion or cleaning.",
          validated: true
        }
      ]
    },
    {
      question: {
        question: "Check for duplicate records",
        keywords: ["duplicate records", "duplicates", "duplicate detection", "data deduplication"],
        patterns: ["duplicate.*records", "duplicate.*detection", "data.*deduplication"],
        category: "descriptive",
        difficulty: "basic",
        context: "Identifying and handling duplicate observations in the dataset"
      },
      answers: [
        {
          analysisId: "DESC1",
          priority: "high",
          reason: "Descriptive Analysis includes duplicate detection and provides recommendations for handling duplicate records.",
          validated: true
        }
      ]
    }
  ];

  dataQualityQuestions.forEach(data => {
    const questionId = trainingDB.addQuestion(data.question);
    data.answers.forEach(answer => {
      trainingDB.addAnswer({
        ...answer,
        questionId
      });
    });
  });
};

/**
 * Generate curated suggestions from all training data
 */
export const generateInitialCuratedSuggestions = (): void => {
  const existingCurated = trainingDB.getCuratedSuggestions();

  // Force regeneration if we have training questions but no curated suggestions
  // or if we have significantly fewer curated suggestions than training questions
  const allQuestions = trainingDB.getAllQuestions();
  const shouldRegenerate = existingCurated.length === 0 ||
                          existingCurated.length < allQuestions.length * 0.8;

  if (!shouldRegenerate) {
    console.log(`Curated suggestions exist (${existingCurated.length}), skipping generation`);
    return;
  }

  if (existingCurated.length > 0) {
    console.log(`Regenerating curated suggestions (${existingCurated.length} existing, ${allQuestions.length} questions)`);
    trainingDB.clearCuratedSuggestions();
  }

  console.log('Generating curated suggestions from training data...');
  const questions = trainingDB.getAllQuestions();

  questions.forEach(question => {
    const answers = trainingDB.getAnswersForQuestion(question.id);
    const validatedAnswers = answers.filter(a => a.validated);

    if (validatedAnswers.length > 0) {
      const suggestions = validatedAnswers.map(answer => ({
        analysisId: answer.analysisId,
        priority: answer.priority === 'high' ? 9 : answer.priority === 'medium' ? 6 : 3,
        reason: answer.reason,
        confidence: 0.95
      }));

      const regexPatterns = question.patterns?.map(p => {
        try {
          return new RegExp(p, 'i');
        } catch {
          return new RegExp(p.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
        }
      }) || [];

      trainingDB.addCuratedSuggestion({
        questionPattern: question.question,
        keywords: question.keywords,
        regexPatterns,
        suggestions,
        category: question.category,
        validated: true
      });
    }
  });

  const finalCuratedCount = trainingDB.getCuratedSuggestions().length;
  console.log(`Generated ${finalCuratedCount} curated suggestions`);

  // Debug: Check if survival analysis suggestion was created
  const survivalSuggestions = trainingDB.getCuratedSuggestions().filter(s =>
    s.questionPattern.toLowerCase().includes('survival') ||
    s.keywords.some(k => k.toLowerCase().includes('survival'))
  );
  console.log(`Survival analysis suggestions: ${survivalSuggestions.length}`);
  if (survivalSuggestions.length > 0) {
    survivalSuggestions.forEach(s => {
      console.log(`- Pattern: "${s.questionPattern}" -> Analysis IDs: ${s.suggestions.map(sg => sg.analysisId).join(', ')}`);
    });
  }
};

/**
 * Complete initialization process with comprehensive coverage
 */
export const initializeCompleteTrainingSystem = (): void => {
  console.log('🚀 Initializing comprehensive Analysis Assistant training system...');

  // Initialize core training data
  initializeTrainingData();
  console.log('✅ Core training data initialized');

  // Add specific scenario training
  addSpecificTrainingQuestions();
  console.log('✅ Specific scenario training added');

  // Add data quality assessment training
  addDataQualityTrainingQuestions();
  console.log('✅ Data quality assessment training added');

  // Generate curated suggestions
  generateInitialCuratedSuggestions();
  console.log('✅ Curated suggestions generated');

  const stats = trainingDB.getStats();
  console.log(`🎯 Analysis Assistant training system fully initialized with ${stats.totalQuestions} questions and ${stats.totalAnswers} answers`);

  // Test survival analysis query
  console.log('🧪 Testing survival analysis query...');
  const testQuery = "Analyze survival data";
  const matches = trainingDB.findMatchingSuggestions(testQuery);
  console.log(`Query "${testQuery}" returned ${matches.length} matches:`);
  matches.forEach((match, index) => {
    console.log(`  ${index + 1}. Pattern: "${match.questionPattern}" -> ${match.suggestions.map(s => s.analysisId).join(', ')}`);
  });

  // Log coverage summary
  console.log('📊 Training Coverage Summary:');
  console.log('- Basic Statistics: Descriptive, Frequency, Correlation');
  console.log('- Inferential Statistics: T-tests, ANOVA, Chi-square');
  console.log('- Non-parametric Tests: Mann-Whitney, Wilcoxon, Kruskal-Wallis');
  console.log('- Advanced Analysis: Survival, Factor Analysis, Reliability');
  console.log('- Epidemiology: Case-Control, Cohort Studies');
  console.log('- Study Design: Sample Size, Power Analysis');
  console.log('- Publication Tools: Table 1, Regression Tables, SMD');
  console.log('- Data Quality: Missing Data, Outliers, Assumptions');
  console.log('- Context-Aware: Assumption detection, Pattern recognition');
};
