/**
 * Training Database for Analysis Assistant
 * Stores curated question-answer mappings to improve suggestion accuracy
 */

export interface TrainingQuestion {
  id: string;
  question: string;
  keywords: string[];
  patterns?: string[]; // Regex patterns as strings
  category: 'correlation' | 'comparison' | 'categorical' | 'descriptive' | 'prediction' | 'test' | 'visualization' | 'other';
  difficulty: 'basic' | 'intermediate' | 'advanced';
  context?: string; // Additional context about when this applies
  createdAt: Date;
  updatedAt: Date;
}

export interface TrainingAnswer {
  id: string;
  questionId: string;
  analysisId: string; // References analysis from allAnalyses
  priority: 'high' | 'medium' | 'low';
  reason: string; // Why this analysis is recommended
  prerequisites?: string[]; // What conditions must be met
  alternatives?: string[]; // Alternative analysis IDs
  createdAt: Date;
  validated: boolean; // Whether this mapping has been validated
}

export interface TrainingSession {
  id: string;
  name: string;
  description: string;
  questions: string[]; // Array of question IDs
  completedQuestions: string[];
  createdAt: Date;
  updatedAt: Date;
  status: 'active' | 'completed' | 'paused';
}

export interface TrainingStats {
  totalQuestions: number;
  totalAnswers: number;
  validatedAnswers: number;
  accuracyScore: number; // Percentage of correct suggestions
  lastTrainingDate: Date;
  sessionsCompleted: number;
}

export interface CuratedSuggestion {
  questionPattern: string;
  keywords: string[];
  regexPatterns: RegExp[];
  suggestions: {
    analysisId: string;
    priority: number; // 1-10 scale
    reason: string;
    confidence: number; // 0-1 scale
  }[];
  category: string;
  validated: boolean;
  usage_count: number;
  success_rate: number; // How often users select this suggestion
}

/**
 * Training Database Manager
 * Handles storage and retrieval of training data
 */
export class TrainingDatabaseManager {
  private static instance: TrainingDatabaseManager;
  private questions: Map<string, TrainingQuestion> = new Map();
  private answers: Map<string, TrainingAnswer[]> = new Map();
  private sessions: Map<string, TrainingSession> = new Map();
  private curatedSuggestions: CuratedSuggestion[] = [];
  private stats: TrainingStats;

  private constructor() {
    this.stats = {
      totalQuestions: 0,
      totalAnswers: 0,
      validatedAnswers: 0,
      accuracyScore: 0,
      lastTrainingDate: new Date(),
      sessionsCompleted: 0
    };
    this.loadFromStorage();
  }

  public static getInstance(): TrainingDatabaseManager {
    if (!TrainingDatabaseManager.instance) {
      TrainingDatabaseManager.instance = new TrainingDatabaseManager();
    }
    return TrainingDatabaseManager.instance;
  }

  // Question Management
  public addQuestion(question: Omit<TrainingQuestion, 'id' | 'createdAt' | 'updatedAt'>): string {
    const id = this.generateId();
    const trainingQuestion: TrainingQuestion = {
      ...question,
      id,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.questions.set(id, trainingQuestion);
    this.stats.totalQuestions++;
    this.saveToStorage();
    return id;
  }

  public getQuestion(id: string): TrainingQuestion | undefined {
    return this.questions.get(id);
  }

  public getAllQuestions(): TrainingQuestion[] {
    return Array.from(this.questions.values());
  }

  public updateQuestion(id: string, updates: Partial<TrainingQuestion>): boolean {
    const question = this.questions.get(id);
    if (!question) return false;

    const updatedQuestion = {
      ...question,
      ...updates,
      updatedAt: new Date()
    };
    
    this.questions.set(id, updatedQuestion);
    this.saveToStorage();
    return true;
  }

  // Answer Management
  public addAnswer(answer: Omit<TrainingAnswer, 'id' | 'createdAt'>): string {
    const id = this.generateId();
    const trainingAnswer: TrainingAnswer = {
      ...answer,
      id,
      createdAt: new Date()
    };

    const questionAnswers = this.answers.get(answer.questionId) || [];
    questionAnswers.push(trainingAnswer);
    this.answers.set(answer.questionId, questionAnswers);
    
    this.stats.totalAnswers++;
    if (trainingAnswer.validated) {
      this.stats.validatedAnswers++;
    }
    
    this.saveToStorage();
    return id;
  }

  public getAnswersForQuestion(questionId: string): TrainingAnswer[] {
    return this.answers.get(questionId) || [];
  }

  public updateAnswer(answerId: string, updates: Partial<TrainingAnswer>): boolean {
    // Find the answer across all questions
    for (const [questionId, answers] of this.answers.entries()) {
      const answerIndex = answers.findIndex(a => a.id === answerId);
      if (answerIndex !== -1) {
        const updatedAnswer = {
          ...answers[answerIndex],
          ...updates,
          id: answerId, // Preserve the original ID
          createdAt: answers[answerIndex].createdAt // Preserve creation date
        };

        answers[answerIndex] = updatedAnswer;
        this.answers.set(questionId, answers);
        this.saveToStorage();
        return true;
      }
    }
    return false;
  }

  public deleteAnswer(answerId: string): boolean {
    // Find and remove the answer across all questions
    for (const [questionId, answers] of this.answers.entries()) {
      const answerIndex = answers.findIndex(a => a.id === answerId);
      if (answerIndex !== -1) {
        const removedAnswer = answers[answerIndex];
        answers.splice(answerIndex, 1);

        // Update stats
        this.stats.totalAnswers--;
        if (removedAnswer.validated) {
          this.stats.validatedAnswers--;
        }

        this.answers.set(questionId, answers);
        this.saveToStorage();
        return true;
      }
    }
    return false;
  }

  public validateAnswer(questionId: string, answerId: string): boolean {
    const answers = this.answers.get(questionId);
    if (!answers) return false;

    const answer = answers.find(a => a.id === answerId);
    if (!answer) return false;

    if (!answer.validated) {
      answer.validated = true;
      this.stats.validatedAnswers++;
      this.saveToStorage();
    }

    return true;
  }

  // Session Management
  public createSession(name: string, description: string): string {
    const id = this.generateId();
    const session: TrainingSession = {
      id,
      name,
      description,
      questions: [],
      completedQuestions: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'active'
    };

    this.sessions.set(id, session);
    this.saveToStorage();
    return id;
  }

  public getSession(id: string): TrainingSession | undefined {
    return this.sessions.get(id);
  }

  public getAllSessions(): TrainingSession[] {
    return Array.from(this.sessions.values());
  }

  public addQuestionToSession(sessionId: string, questionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    if (!session.questions.includes(questionId)) {
      session.questions.push(questionId);
      session.updatedAt = new Date();
      this.saveToStorage();
    }
    
    return true;
  }

  public markQuestionCompleted(sessionId: string, questionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    if (!session.completedQuestions.includes(questionId)) {
      session.completedQuestions.push(questionId);
      session.updatedAt = new Date();
      
      // Check if session is completed
      if (session.completedQuestions.length === session.questions.length) {
        session.status = 'completed';
        this.stats.sessionsCompleted++;
      }
      
      this.saveToStorage();
    }
    
    return true;
  }

  // Curated Suggestions Management
  public addCuratedSuggestion(suggestion: Omit<CuratedSuggestion, 'usage_count' | 'success_rate'>): void {
    // Check if a suggestion with the same question pattern already exists
    const existingSuggestion = this.curatedSuggestions.find(
      s => s.questionPattern === suggestion.questionPattern
    );

    if (existingSuggestion) {
      console.log(`Curated suggestion already exists for: "${suggestion.questionPattern}"`);
      return;
    }

    const curatedSuggestion: CuratedSuggestion = {
      ...suggestion,
      usage_count: 0,
      success_rate: 0
    };

    this.curatedSuggestions.push(curatedSuggestion);
    this.saveToStorage();
  }

  public getCuratedSuggestions(): CuratedSuggestion[] {
    return this.curatedSuggestions;
  }

  public clearCuratedSuggestions(): void {
    this.curatedSuggestions = [];
    this.saveToStorage();
  }

  public getDuplicateCuratedSuggestions(): { [key: string]: number } {
    const counts: { [key: string]: number } = {};

    this.curatedSuggestions.forEach(suggestion => {
      const pattern = suggestion.questionPattern;
      counts[pattern] = (counts[pattern] || 0) + 1;
    });

    // Return only duplicates (count > 1)
    const duplicates: { [key: string]: number } = {};
    Object.entries(counts).forEach(([pattern, count]) => {
      if (count > 1) {
        duplicates[pattern] = count;
      }
    });

    return duplicates;
  }

  public removeDuplicateCuratedSuggestions(): number {
    const seen = new Set<string>();
    const uniqueSuggestions: CuratedSuggestion[] = [];
    let removedCount = 0;

    this.curatedSuggestions.forEach(suggestion => {
      if (!seen.has(suggestion.questionPattern)) {
        seen.add(suggestion.questionPattern);
        uniqueSuggestions.push(suggestion);
      } else {
        removedCount++;
      }
    });

    this.curatedSuggestions = uniqueSuggestions;
    this.saveToStorage();

    return removedCount;
  }

  // Enhanced pattern matching with advanced recognition capabilities
  public findMatchingSuggestions(query: string): CuratedSuggestion[] {
    const queryLower = query.toLowerCase();
    const matches: { suggestion: CuratedSuggestion; score: number; context: string }[] = [];

    this.curatedSuggestions.forEach(suggestion => {
      let score = 0;
      let contextInfo = '';

      // Enhanced keyword matching with partial matches and synonyms
      suggestion.keywords.forEach(keyword => {
        const keywordLower = keyword.toLowerCase();
        if (queryLower.includes(keywordLower)) {
          score += 3; // Increased weight for exact keyword matches
          contextInfo += `Keyword match: ${keyword}; `;
        } else if (this.checkPartialMatch(queryLower, keywordLower)) {
          score += 2; // Partial matches get lower score
          contextInfo += `Partial match: ${keyword}; `;
        } else if (this.checkSynonymMatch(queryLower, keywordLower)) {
          score += 2; // Synonym matches
          contextInfo += `Synonym match: ${keyword}; `;
        }
      });

      // Enhanced regex pattern matches with error handling
      suggestion.regexPatterns.forEach(pattern => {
        try {
          if (pattern && typeof pattern.test === 'function' && pattern.test(query)) {
            score += 4; // Higher weight for pattern matches
            contextInfo += `Pattern match; `;
          }
        } catch (error) {
          console.warn('Invalid regex pattern encountered:', pattern, error);
        }
      });

      // Enhanced question pattern similarity with fuzzy matching
      const questionLower = suggestion.questionPattern.toLowerCase();
      if (queryLower.includes(questionLower) || questionLower.includes(queryLower)) {
        score += 2;
        contextInfo += `Question similarity; `;
      } else if (this.calculateSimilarity(queryLower, questionLower) > 0.6) {
        score += 1; // Fuzzy similarity match
        contextInfo += `Fuzzy match; `;
      }

      // Variable naming pattern detection
      const varPatternScore = this.detectVariablePatterns(query);
      if (varPatternScore > 0) {
        score += varPatternScore;
        contextInfo += `Variable pattern detected; `;
      }

      // Data structure pattern detection with assumption awareness
      const structureScore = this.detectDataStructurePatterns(query);
      if (structureScore > 0) {
        score += structureScore;
        contextInfo += `Data structure/assumption pattern detected; `;
      }

      // Context-sensitive scoring adjustments
      score = this.adjustScoreForContext(query, suggestion, score);

      if (score > 0) {
        matches.push({ suggestion, score, context: contextInfo });
      }
    });

    // Sort by score and return top matches
    return matches
      .sort((a, b) => b.score - a.score)
      .slice(0, 10)
      .map(match => match.suggestion);
  }

  // Context-sensitive scoring adjustments
  private adjustScoreForContext(query: string, suggestion: CuratedSuggestion, baseScore: number): number {
    let adjustedScore = baseScore;

    // Boost non-parametric alternatives when assumption violations are mentioned
    if (/\b(non.?normal|not.?normal|skewed|non.?parametric)\b/i.test(query)) {
      if (suggestion.suggestions.some(s => ['NONPAR1', 'NONPAR2', 'NONPAR3', 'NONPAR4'].includes(s.analysisId))) {
        adjustedScore += 5; // Strong boost for non-parametric tests
      }
      // Reduce score for parametric tests when assumptions are violated
      if (suggestion.suggestions.some(s => ['TTEST1', 'TTEST2', 'TTEST3', 'ANOVA1', 'ANOVA2', 'ANOVA3'].includes(s.analysisId))) {
        adjustedScore -= 2;
      }
    }

    // Boost parametric tests when normality is confirmed
    if (/\b(normal|gaussian|parametric|normally.?distributed)\b/i.test(query)) {
      if (suggestion.suggestions.some(s => ['TTEST1', 'TTEST2', 'TTEST3', 'ANOVA1', 'ANOVA2', 'ANOVA3'].includes(s.analysisId))) {
        adjustedScore += 3;
      }
    }

    // Boost advanced analyses when specific keywords are present
    if (/\b(survival|time.?to.?event|kaplan.?meier|cox)\b/i.test(query)) {
      if (suggestion.suggestions.some(s => s.analysisId === 'ADV4')) {
        adjustedScore += 4;
      }
    }

    if (/\b(factor.?analysis|reliability|cronbach|construct)\b/i.test(query)) {
      if (suggestion.suggestions.some(s => ['ADV1', 'ADV3'].includes(s.analysisId))) {
        adjustedScore += 4;
      }
    }

    return adjustedScore;
  }

  public updateSuggestionUsage(questionPattern: string, selected: boolean): void {
    const suggestion = this.curatedSuggestions.find(s => s.questionPattern === questionPattern);
    if (suggestion) {
      suggestion.usage_count++;
      if (selected) {
        suggestion.success_rate = (suggestion.success_rate * (suggestion.usage_count - 1) + 1) / suggestion.usage_count;
      } else {
        suggestion.success_rate = (suggestion.success_rate * (suggestion.usage_count - 1)) / suggestion.usage_count;
      }
      this.saveToStorage();
    }
  }

  // Helper method for partial keyword matching
  private checkPartialMatch(query: string, keyword: string): boolean {
    const queryWords = query.split(/\s+/);
    const keywordWords = keyword.split(/\s+/);

    return keywordWords.some(kw =>
      queryWords.some(qw =>
        qw.length > 3 && kw.length > 3 &&
        (qw.includes(kw) || kw.includes(qw))
      )
    );
  }

  // Helper method for synonym matching with enhanced assumption detection
  private checkSynonymMatch(query: string, keyword: string): boolean {
    const synonyms: { [key: string]: string[] } = {
      'correlation': ['relationship', 'association', 'connection'],
      'comparison': ['compare', 'difference', 'versus', 'vs'],
      'prediction': ['predict', 'forecast', 'model'],
      'test': ['analysis', 'examine', 'check'],
      'categorical': ['nominal', 'ordinal', 'factor'],
      'continuous': ['numeric', 'quantitative', 'interval', 'ratio'],
      'missing': ['na', 'null', 'empty', 'blank'],
      'outlier': ['extreme', 'anomaly', 'unusual'],
      // Enhanced assumption-related synonyms
      'non normal': ['not normal', 'non-normal', 'skewed', 'non-parametric', 'nonparametric'],
      'not normal': ['non normal', 'non-normal', 'skewed', 'non-parametric', 'nonparametric'],
      'skewed': ['non normal', 'not normal', 'non-parametric', 'asymmetric'],
      'parametric': ['normal', 'gaussian', 'normally distributed'],
      'non-parametric': ['non normal', 'not normal', 'skewed', 'rank-based'],
      'survival': ['time to event', 'kaplan meier', 'cox regression', 'hazard'],
      'factor analysis': ['EFA', 'construct', 'latent variables', 'dimension reduction'],
      'reliability': ['cronbach', 'alpha', 'internal consistency', 'scale reliability']
    };

    for (const [key, syns] of Object.entries(synonyms)) {
      if (keyword.includes(key) && syns.some(syn => query.includes(syn))) {
        return true;
      }
      if (query.includes(key) && syns.some(syn => keyword.includes(syn))) {
        return true;
      }
    }
    return false;
  }

  // Calculate string similarity using Jaccard index
  private calculateSimilarity(str1: string, str2: string): number {
    const words1 = new Set(str1.split(/\s+/));
    const words2 = new Set(str2.split(/\s+/));

    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }

  // Detect variable naming patterns
  private detectVariablePatterns(query: string): number {
    let score = 0;

    // Scale item patterns (item1, item2, q1, q2, etc.)
    if (/\b(item|q|question|var)\d+\b/i.test(query)) {
      score += 2;
    }

    // Pre/post patterns
    if (/\b(pre|post|before|after|time[12]|t[12]|baseline|followup)\b/i.test(query)) {
      score += 2;
    }

    // Treatment/control patterns
    if (/\b(treatment|control|intervention|placebo|group[abc])\b/i.test(query)) {
      score += 2;
    }

    // Demographic patterns
    if (/\b(age|gender|sex|education|income|race|ethnicity)\b/i.test(query)) {
      score += 1;
    }

    return score;
  }

  // Detect data structure patterns with enhanced assumption detection
  private detectDataStructurePatterns(query: string): number {
    let score = 0;

    // Experimental design patterns
    if (/\b(randomized|controlled|trial|experiment|rct)\b/i.test(query)) {
      score += 2;
    }

    // Survey/questionnaire patterns
    if (/\b(survey|questionnaire|likert|scale|rating)\b/i.test(query)) {
      score += 2;
    }

    // Longitudinal patterns
    if (/\b(longitudinal|repeated|panel|time series|cohort)\b/i.test(query)) {
      score += 2;
    }

    // Medical/clinical patterns
    if (/\b(patient|clinical|medical|diagnosis|treatment|outcome)\b/i.test(query)) {
      score += 1;
    }

    // Statistical assumption patterns (high priority)
    if (/\b(non.?normal|not.?normal|skewed|non.?parametric|nonparametric)\b/i.test(query)) {
      score += 3; // High score for assumption violations
    }

    // Normality-related patterns
    if (/\b(normal|gaussian|parametric|normally.?distributed)\b/i.test(query)) {
      score += 2;
    }

    // Advanced analysis patterns
    if (/\b(survival|time.?to.?event|kaplan.?meier|cox|hazard)\b/i.test(query)) {
      score += 3;
    }

    if (/\b(factor.?analysis|reliability|cronbach|alpha|construct)\b/i.test(query)) {
      score += 3;
    }

    if (/\b(cluster|grouping|k.?means|hierarchical)\b/i.test(query)) {
      score += 2;
    }

    return score;
  }

  // Statistics
  public getStats(): TrainingStats {
    this.stats.accuracyScore = this.stats.totalAnswers > 0 
      ? (this.stats.validatedAnswers / this.stats.totalAnswers) * 100 
      : 0;
    return { ...this.stats };
  }

  // Storage Management
  private saveToStorage(): void {
    try {
      const data = {
        questions: Array.from(this.questions.entries()),
        answers: Array.from(this.answers.entries()),
        sessions: Array.from(this.sessions.entries()),
        curatedSuggestions: this.curatedSuggestions,
        stats: this.stats
      };
      
      localStorage.setItem('analysisAssistantTrainingData', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save training data:', error);
    }
  }

  private loadFromStorage(): void {
    try {
      const data = localStorage.getItem('analysisAssistantTrainingData');
      if (data) {
        const parsed = JSON.parse(data);

        this.questions = new Map(parsed.questions || []);
        this.answers = new Map(parsed.answers || []);
        this.sessions = new Map(parsed.sessions || []);

        // Reconstruct RegExp objects for curated suggestions
        this.curatedSuggestions = (parsed.curatedSuggestions || []).map((suggestion: any) => ({
          ...suggestion,
          regexPatterns: (suggestion.regexPatterns || []).map((pattern: any) => {
            try {
              // If it's already a RegExp object, return it
              if (pattern instanceof RegExp) {
                return pattern;
              }
              // If it's a serialized RegExp object, reconstruct it
              if (typeof pattern === 'object' && pattern.source) {
                return new RegExp(pattern.source, pattern.flags || 'i');
              }
              // If it's a string, create a RegExp from it
              if (typeof pattern === 'string') {
                return new RegExp(pattern, 'i');
              }
              // Fallback: return a safe RegExp that won't match anything
              return new RegExp('(?!)', 'i');
            } catch (error) {
              console.warn('Failed to reconstruct regex pattern:', pattern, error);
              // Return a safe RegExp that won't match anything
              return new RegExp('(?!)', 'i');
            }
          })
        }));

        this.stats = parsed.stats || this.stats;
      }
    } catch (error) {
      console.error('Failed to load training data:', error);
    }
  }

  private generateId(): string {
    return 'train_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  // Export/Import functionality
  public exportTrainingData(): string {
    const data = {
      questions: Array.from(this.questions.entries()),
      answers: Array.from(this.answers.entries()),
      sessions: Array.from(this.sessions.entries()),
      curatedSuggestions: this.curatedSuggestions,
      stats: this.stats,
      exportDate: new Date().toISOString()
    };
    
    return JSON.stringify(data, null, 2);
  }

  public importTrainingData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);

      this.questions = new Map(data.questions || []);
      this.answers = new Map(data.answers || []);
      this.sessions = new Map(data.sessions || []);

      // Reconstruct RegExp objects for curated suggestions
      this.curatedSuggestions = (data.curatedSuggestions || []).map((suggestion: any) => ({
        ...suggestion,
        regexPatterns: (suggestion.regexPatterns || []).map((pattern: any) => {
          try {
            // If it's already a RegExp object, return it
            if (pattern instanceof RegExp) {
              return pattern;
            }
            // If it's a serialized RegExp object, reconstruct it
            if (typeof pattern === 'object' && pattern.source) {
              return new RegExp(pattern.source, pattern.flags || 'i');
            }
            // If it's a string, create a RegExp from it
            if (typeof pattern === 'string') {
              return new RegExp(pattern, 'i');
            }
            // Fallback: return a safe RegExp that won't match anything
            return new RegExp('(?!)', 'i');
          } catch (error) {
            console.warn('Failed to reconstruct regex pattern:', pattern, error);
            // Return a safe RegExp that won't match anything
            return new RegExp('(?!)', 'i');
          }
        })
      }));

      this.stats = data.stats || this.stats;

      this.saveToStorage();
      return true;
    } catch (error) {
      console.error('Failed to import training data:', error);
      return false;
    }
  }

  public clearAllData(): void {
    this.questions.clear();
    this.answers.clear();
    this.sessions.clear();
    this.curatedSuggestions = [];
    this.stats = {
      totalQuestions: 0,
      totalAnswers: 0,
      validatedAnswers: 0,
      accuracyScore: 0,
      lastTrainingDate: new Date(),
      sessionsCompleted: 0
    };
    this.saveToStorage();
  }
}

// Export singleton instance
export const trainingDB = TrainingDatabaseManager.getInstance();
