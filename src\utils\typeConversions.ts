import { DataValue, Column, DataRow } from '../types';
import { extractNumericValuesWithMissingCodes, extractCategoricalValuesWithMissingCodes } from './missingDataUtils';

/**
 * Type-safe data conversion utilities for statistical operations
 * These functions safely convert DataValue arrays to specific types needed by statistical functions
 */

/**
 * Extracts numeric values from a DataValue array, filtering out null, undefined, and non-numeric values
 * @param data Array of DataValue items
 * @returns Array of valid numbers
 */
export const extractNumericValues = (data: DataValue[]): number[] => {
  return data.filter((val): val is number => 
    typeof val === 'number' && !isNaN(val) && isFinite(val)
  );
};

/**
 * Safely sorts DataValue array, handling null/undefined values and mixed types
 * @param data Array of DataValue items
 * @returns Sorted array with null/undefined values filtered out
 */
export const safeSort = (data: DataValue[]): DataValue[] => {
  return data
    .filter((val): val is NonNullable<DataValue> => val != null)
    .sort((a, b) => {
      // Handle numeric sorting
      if (typeof a === 'number' && typeof b === 'number') {
        return a - b;
      }
      
      // Handle date sorting
      if (a instanceof Date && b instanceof Date) {
        return a.getTime() - b.getTime();
      }
      
      // Handle boolean sorting
      if (typeof a === 'boolean' && typeof b === 'boolean') {
        return a === b ? 0 : a ? 1 : -1;
      }
      
      // Default to string comparison
      return String(a).localeCompare(String(b));
    });
};

/**
 * Safely sorts numeric values from DataValue array
 * @param data Array of DataValue items
 * @returns Sorted array of numbers
 */
export const safeSortNumeric = (data: DataValue[]): number[] => {
  const numericValues = extractNumericValues(data);
  return numericValues.sort((a, b) => a - b);
};

/**
 * Converts DataValue to number with fallback
 * @param value DataValue to convert
 * @param fallback Default value if conversion fails
 * @returns Number or fallback value
 */
export const toNumber = (value: DataValue, fallback: number = 0): number => {
  if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
    return value;
  }
  
  if (typeof value === 'string') {
    const parsed = Number(value);
    if (!isNaN(parsed) && isFinite(parsed)) {
      return parsed;
    }
  }
  
  if (typeof value === 'boolean') {
    return value ? 1 : 0;
  }
  
  return fallback;
};

/**
 * Converts DataValue to string with fallback
 * @param value DataValue to convert
 * @param fallback Default value if conversion fails
 * @returns String representation or fallback value
 */
export const toString = (value: DataValue, fallback: string = ''): string => {
  if (value == null) {
    return fallback;
  }
  
  if (value instanceof Date) {
    return value.toISOString();
  }
  
  return String(value);
};

/**
 * Checks if a DataValue is numeric
 * @param value DataValue to check
 * @returns True if value is a valid number
 */
export const isNumeric = (value: DataValue): value is number => {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
};

/**
 * Checks if a DataValue array contains any numeric values
 * @param data Array of DataValue items
 * @returns True if array contains at least one valid number
 */
export const hasNumericValues = (data: DataValue[]): boolean => {
  return data.some(isNumeric);
};

/**
 * Safely calculates statistics with proper error handling
 * @param values DataValue array
 * @param calculator Function that performs the calculation on number array
 * @returns Calculation result or null if no valid data
 */
export const safeCalculate = <T>(
  values: DataValue[], 
  calculator: (nums: number[]) => T
): T | null => {
  const numericValues = extractNumericValues(values);
  
  if (numericValues.length === 0) {
    return null;
  }
  
  try {
    return calculator(numericValues);
  } catch (error) {
    console.warn('Statistical calculation failed:', error);
    return null;
  }
};

/**
 * Filters out outliers from numeric data using IQR method
 * @param data DataValue array
 * @param multiplier IQR multiplier (default 1.5)
 * @returns Object with filtered data and outlier information
 */
export const filterOutliers = (
  data: DataValue[],
  multiplier: number = 1.5
): {
  filtered: number[];
  outliers: number[];
  bounds: { lower: number; upper: number };
} => {
  const numericValues = safeSortNumeric(data);

  if (numericValues.length < 4) {
    return {
      filtered: numericValues,
      outliers: [],
      bounds: { lower: -Infinity, upper: Infinity }
    };
  }

  const q1Index = Math.floor((numericValues.length - 1) * 0.25);
  const q3Index = Math.floor((numericValues.length - 1) * 0.75);

  const q1 = numericValues[q1Index];
  const q3 = numericValues[q3Index];
  const iqr = q3 - q1;

  const lowerBound = q1 - multiplier * iqr;
  const upperBound = q3 + multiplier * iqr;

  const filtered: number[] = [];
  const outliers: number[] = [];

  numericValues.forEach(value => {
    if (value >= lowerBound && value <= upperBound) {
      filtered.push(value);
    } else {
      outliers.push(value);
    }
  });

  return {
    filtered,
    outliers,
    bounds: { lower: lowerBound, upper: upperBound }
  };
};

/**
 * Enhanced numeric value extraction that respects user-defined missing value codes
 * @param data Array of data rows
 * @param column Column definition with missing value codes
 * @returns Array of valid numbers
 */
export const extractNumericValuesEnhanced = (
  data: DataRow[],
  column: Column
): number[] => {
  return extractNumericValuesWithMissingCodes(data, column);
};

/**
 * Enhanced categorical value extraction that respects user-defined missing value codes
 * @param data Array of data rows
 * @param column Column definition with missing value codes
 * @returns Array of valid categorical values
 */
export const extractCategoricalValuesEnhanced = (
  data: DataRow[],
  column: Column
): string[] => {
  return extractCategoricalValuesWithMissingCodes(data, column);
};

/**
 * Groups numeric values by categories
 * @param values DataValue array of numeric values
 * @param categories DataValue array of category labels
 * @returns Object mapping category names to numeric arrays
 */
export const groupNumericByCategory = (
  values: DataValue[], 
  categories: DataValue[]
): Record<string, number[]> => {
  if (values.length !== categories.length) {
    throw new Error('Values and categories arrays must have the same length');
  }
  
  const groups: Record<string, number[]> = {};
  
  for (let i = 0; i < values.length; i++) {
    const value = values[i];
    const category = categories[i];
    
    if (isNumeric(value) && category != null) {
      const categoryKey = toString(category);
      
      if (!groups[categoryKey]) {
        groups[categoryKey] = [];
      }
      
      groups[categoryKey].push(value);
    }
  }
  
  return groups;
};
