-- Migration to update the profiles table structure
-- Remove gender field and ensure country field is properly configured

-- First, check if the gender column exists and remove it if it does
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns 
             WHERE table_schema = 'public' 
             AND table_name = 'profiles' 
             AND column_name = 'gender') THEN
    ALTER TABLE public.profiles DROP COLUMN gender;
  END IF;
END $$;

-- Next, check if the country column exists, if not add it
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_schema = 'public' 
                 AND table_name = 'profiles' 
                 AND column_name = 'country') THEN
    ALTER TABLE public.profiles ADD COLUMN country text;
  END IF;
END $$;

-- Update the handle_new_user function to match the new structure
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    username, 
    full_name, 
    institution, 
    country, 
    avatar_url, 
    updated_at
  ) VALUES (
    new.id, 
    new.email, -- Default username to email
    new.raw_user_meta_data->>'full_name', 
    new.raw_user_meta_data->>'institution', 
    new.raw_user_meta_data->>'country',
    null, -- Default avatar_url to null
    now()
  );
  RETURN new;
END;
$$;

-- Ensure the trigger exists
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'on_auth_user_created') THEN
    CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
  END IF;
END $$;

-- Add comment to explain the migration
COMMENT ON TABLE public.profiles IS 'Stores user profile information with updated fields (removed gender, ensured country field)';