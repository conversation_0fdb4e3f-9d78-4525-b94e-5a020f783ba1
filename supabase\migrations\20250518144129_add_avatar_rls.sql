-- Enable RLS for the 'avatars' bucket
ALTER STORAGE.buckets enable_row_level_security for avatars;

-- Create policy for inserting avatars
CREATE POLICY "Allow authenticated users to upload avatars"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'avatars' AND auth.uid() IS NOT NULL AND (storage.path_metadata(new.name)).size <= 100 * 1024 -- 100KB size limit
);

-- Create policy for updating avatars
CREATE POLICY "Allow authenticated users to update their own avatar"
ON storage.objects FOR UPDATE
TO authenticated
USING ( bucket_id = 'avatars' AND auth.uid() = owner )
WITH CHECK ( (storage.path_metadata(new.name)).size <= 100 * 1024 ); -- 100KB size limit
