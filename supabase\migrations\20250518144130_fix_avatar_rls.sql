-- Fix RLS policies for the 'avatars' bucket

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated users to upload avatars" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update their own avatar" ON storage.objects;

-- Create policy for uploading avatars
CREATE POLICY "Allow users to upload their own avatars"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'avatars' AND 
  auth.uid()::text = (storage.foldername(name))[1] AND
  (storage.foldername(name))[1] IS NOT NULL AND
  (OCTET_LENGTH(COALESCE(content, '')) <= 100 * 1024) -- 100KB size limit
);

-- Create policy for viewing avatars (public read access)
CREATE POLICY "Allow public to view avatars"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'avatars');

-- Create policy for updating avatars
CREATE POLICY "Allow users to update their own avatars"
ON storage.objects FOR UPDATE
TO authenticated
USING (
  bucket_id = 'avatars' AND 
  auth.uid()::text = (storage.foldername(name))[1]
)
WITH CHECK (
  (OCTET_LENGTH(COALESCE(content, '')) <= 100 * 1024) -- 100KB size limit
);

-- Create policy for deleting avatars
CREATE POLICY "Allow users to delete their own avatars"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'avatars' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);