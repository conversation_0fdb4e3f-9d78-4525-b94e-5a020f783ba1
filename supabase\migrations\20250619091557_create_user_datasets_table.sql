CREATE TABLE user_datasets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  dataset_name VARCHAR(255) NOT NULL,
  dataset_content JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,

  CONSTRAINT unique_user_dataset_name UNIQUE (user_id, dataset_name)
);

ALTER TABLE user_datasets ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can manage their own datasets"
ON user_datasets FOR ALL
TO authenticated
USING (auth.uid() = user_id);
