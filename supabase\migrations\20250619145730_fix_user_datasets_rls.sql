-- Drop the existing policy if it exists
DROP POLICY IF EXISTS "Authenticated users can manage their own datasets" ON user_datasets;

-- Create a more permissive policy for INSERT
CREATE POLICY "Users can insert their own datasets"
ON user_datasets FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Create a policy for SELECT, UPDATE, DELETE
CREATE POLICY "Users can view and modify their own datasets"
ON user_datasets FOR ALL
TO authenticated
USING (auth.uid() = user_id);

-- Enable RLS on the table (in case it was disabled)
ALTER TABLE user_datasets ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions to authenticated users
GRANT ALL ON user_datasets TO authenticated;