-- Fix RLS policies for the user_datasets table
-- Drop the existing policy if it exists
DROP POLICY IF EXISTS "Authenticated users can manage their own datasets" ON user_datasets;
DROP POLICY IF EXISTS "Users can insert their own datasets" ON user_datasets;
DROP POLICY IF EXISTS "Users can view and modify their own datasets" ON user_datasets;

-- Create a more permissive policy for INSERT
CREATE POLICY "Users can insert their own datasets"
ON user_datasets FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Create a policy for SELECT, UPDATE, DELETE
CREATE POLICY "Users can view and modify their own datasets"
ON user_datasets FOR ALL
TO authenticated
USING (auth.uid() = user_id);

-- Enable RLS on the table (in case it was disabled)
ALTER TABLE user_datasets ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions to authenticated users
GRANT ALL ON user_datasets TO authenticated;

-- Fix RLS policies for the userdatasets storage bucket
-- Note: We don't need to explicitly enable <PERSON><PERSON> on the bucket
-- Instead, we create policies on storage.objects for the specific bucket

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can upload their own datasets" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own datasets" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own datasets" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own datasets" ON storage.objects;

-- Create policy for uploading datasets
CREATE POLICY "Users can upload their own datasets"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'userdatasets' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy for viewing datasets
CREATE POLICY "Users can view their own datasets"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'userdatasets' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy for updating datasets
CREATE POLICY "Users can update their own datasets"
ON storage.objects FOR UPDATE
TO authenticated
USING (
  bucket_id = 'userdatasets' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy for deleting datasets
CREATE POLICY "Users can delete their own datasets"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'userdatasets' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Grant necessary permissions to authenticated users for the userdatasets bucket
GRANT ALL ON storage.objects TO authenticated;