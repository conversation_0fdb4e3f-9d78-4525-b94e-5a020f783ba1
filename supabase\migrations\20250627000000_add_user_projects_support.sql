-- Migration: Add user projects support for Results Manager organization
-- This migration creates the infrastructure for Pro users to organize their analysis results into projects

-- Create user_projects table for storing project metadata
CREATE TABLE IF NOT EXISTS public.user_projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  project_name VARCHAR(255) NOT NULL,
  file_path TEXT, -- Path to the project data file in storage
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,

  -- Ensure unique project names per user
  CONSTRAINT unique_user_project_name UNIQUE (user_id, project_name)
);

-- Enable RLS on the user_projects table
ALTER TABLE public.user_projects ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_projects table
-- Policy for inserting projects (users can only create their own projects)
CREATE POLICY "Users can insert their own projects"
ON user_projects FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Policy for viewing, updating, and deleting projects (users can only access their own projects)
CREATE POLICY "Users can view and modify their own projects"
ON user_projects FOR ALL
TO authenticated
USING (auth.uid() = user_id);

-- Grant necessary permissions to authenticated users
GRANT ALL ON user_projects TO authenticated;

-- Create storage bucket for user projects if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'userprojects',
  'userprojects',
  false,
  2097152, -- 2MB limit (same as datasets)
  ARRAY['application/json']
)
ON CONFLICT (id) DO NOTHING;

-- Enable RLS for the userprojects bucket
ALTER STORAGE.buckets enable_row_level_security for userprojects;

-- Drop existing policies if they exist (for clean migration)
DROP POLICY IF EXISTS "Users can upload their own projects" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own projects" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own projects" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own projects" ON storage.objects;

-- Create storage policies for userprojects bucket
-- Policy for uploading project files
CREATE POLICY "Users can upload their own projects"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'userprojects' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy for viewing project files
CREATE POLICY "Users can view their own projects"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'userprojects' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy for updating project files
CREATE POLICY "Users can update their own projects"
ON storage.objects FOR UPDATE
TO authenticated
USING (
  bucket_id = 'userprojects' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy for deleting project files
CREATE POLICY "Users can delete their own projects"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'userprojects' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Grant necessary permissions to authenticated users for the userprojects bucket
GRANT ALL ON storage.objects TO authenticated;

-- Add comment to explain the migration
COMMENT ON TABLE public.user_projects IS 'Stores user project metadata for Results Manager organization. Pro users can save up to 2 projects to cloud storage.';

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_user_projects_user_id ON public.user_projects(user_id);
CREATE INDEX IF NOT EXISTS idx_user_projects_created_at ON public.user_projects(created_at DESC);
