-- Migration: Add Stripe integration support and fix accounttype handling
-- This migration adds subscription tracking and fixes educational account detection

-- Ensure accounttype field exists with proper constraints
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_schema = 'public' 
                 AND table_name = 'profiles' 
                 AND column_name = 'accounttype') THEN
    ALTER TABLE public.profiles ADD COLUMN accounttype character varying(20) DEFAULT 'standard';
  END IF;
END $$;

-- Add constraint for accounttype values
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints 
                 WHERE constraint_name = 'profiles_accounttype_check') THEN
    ALTER TABLE public.profiles 
    ADD CONSTRAINT profiles_accounttype_check 
    CHECK (accounttype IN ('standard', 'pro', 'edu'));
  END IF;
END $$;

-- Add Stripe customer ID field
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_schema = 'public' 
                 AND table_name = 'profiles' 
                 AND column_name = 'stripe_customer_id') THEN
    ALTER TABLE public.profiles ADD COLUMN stripe_customer_id text;
  END IF;
END $$;

-- Create subscriptions table for Stripe integration
CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  stripe_customer_id TEXT NOT NULL,
  stripe_subscription_id TEXT UNIQUE,
  stripe_price_id TEXT NOT NULL,
  status TEXT NOT NULL, -- active, canceled, past_due, unpaid, incomplete, etc.
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  billing_cycle TEXT NOT NULL DEFAULT 'monthly', -- monthly, yearly
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Ensure one active subscription per user
  CONSTRAINT unique_active_subscription_per_user UNIQUE (user_id, status) 
    DEFERRABLE INITIALLY DEFERRED
);

-- Enable RLS for subscriptions table
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for subscriptions
CREATE POLICY "Users can manage their own subscriptions"
ON public.subscriptions FOR ALL
TO authenticated
USING (auth.uid() = user_id);

-- Function to detect educational email domains
CREATE OR REPLACE FUNCTION public.is_educational_email(email_address text)
RETURNS boolean
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check for common educational domains
  RETURN email_address ~* '\.(edu|ac\.edu|edu\.au|ac\.uk|edu\.sg|edu\.my|ac\.in|edu\.pk|edu\.sa|uni-.*\.de|.*\.edu\..*|.*\.ac\..*|.*\.university\..*|.*\.college\..*)$';
END;
$$;

-- Update the handle_new_user function to include accounttype and educational detection
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
DECLARE
  detected_account_type text := 'standard';
BEGIN
  -- Detect educational account based on email domain
  IF public.is_educational_email(new.email) THEN
    detected_account_type := 'edu';
  END IF;

  INSERT INTO public.profiles (
    id, 
    username, 
    full_name, 
    institution, 
    country, 
    avatar_url, 
    updated_at,
    accounttype
  ) VALUES (
    new.id, 
    new.email, -- Default username to email
    new.raw_user_meta_data->>'full_name', 
    new.raw_user_meta_data->>'institution', 
    new.raw_user_meta_data->>'country',
    null, -- Default avatar_url to null
    now(),
    detected_account_type
  );
  RETURN new;
END;
$$;

-- Create function to sync subscription status with accounttype
CREATE OR REPLACE FUNCTION public.sync_account_type_from_subscription()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
DECLARE
  target_account_type text;
BEGIN
  -- Determine account type based on subscription status and price
  IF NEW.status = 'active' THEN
    -- Check if this is an educational price (will be set based on Stripe price IDs)
    IF NEW.stripe_price_id LIKE '%edu%' OR NEW.stripe_price_id LIKE '%educational%' THEN
      target_account_type := 'edu';
    ELSE
      target_account_type := 'pro';
    END IF;
  ELSE
    -- For inactive subscriptions, check if user has educational email
    SELECT CASE 
      WHEN public.is_educational_email(u.email) THEN 'edu'
      ELSE 'standard'
    END INTO target_account_type
    FROM auth.users u
    WHERE u.id = NEW.user_id;
  END IF;

  -- Update the user's account type
  UPDATE public.profiles 
  SET accounttype = target_account_type,
      updated_at = now()
  WHERE id = NEW.user_id;

  RETURN NEW;
END;
$$;

-- Create trigger to sync account type when subscription changes
DROP TRIGGER IF EXISTS sync_account_type_on_subscription_change ON public.subscriptions;
CREATE TRIGGER sync_account_type_on_subscription_change
  AFTER INSERT OR UPDATE ON public.subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_account_type_from_subscription();

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer_id ON public.subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON public.subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_profiles_accounttype ON public.profiles(accounttype);
CREATE INDEX IF NOT EXISTS idx_profiles_stripe_customer_id ON public.profiles(stripe_customer_id);

-- Add comments
COMMENT ON TABLE public.subscriptions IS 'Stores Stripe subscription information for Pro and Educational accounts';
COMMENT ON FUNCTION public.is_educational_email(text) IS 'Detects educational email domains for automatic edu account type assignment';
COMMENT ON FUNCTION public.sync_account_type_from_subscription() IS 'Automatically syncs user account type based on active subscription status';
