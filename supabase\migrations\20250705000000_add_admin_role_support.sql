-- Admin Role Support Migration
-- This migration adds admin role support to the profiles table and creates necessary functions and policies

-- Add admin role field to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT false;

-- Create index for faster admin role queries
CREATE INDEX IF NOT EXISTS idx_profiles_is_admin ON public.profiles(is_admin) WHERE is_admin = true;

-- Create function to check if a user is admin
CREATE OR REPLACE FUNCTION public.is_user_admin(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND is_admin = true
  );
END;
$$;

-- Create function to get admin users (for admin management)
CREATE OR REPLACE FUNCTION public.get_admin_users()
RETURNS TABLE (
  id UUID,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT p.id, p.username, p.full_name, p.institution, p.country, p.avatar_url, p.updated_at, p.is_admin
  FROM public.profiles p
  WHERE p.is_admin = true
  ORDER BY p.full_name, p.username;
END;
$$;

-- Create function to get all users with pagination (admin only)
CREATE OR REPLACE FUNCTION public.get_all_users(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0,
  search_term TEXT DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  email TEXT,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN,
  accounttype TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  last_sign_in_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    p.id, 
    au.email,
    p.username, 
    p.full_name, 
    p.institution, 
    p.country, 
    p.avatar_url, 
    p.updated_at, 
    p.is_admin,
    p.accounttype,
    au.created_at,
    au.last_sign_in_at
  FROM public.profiles p
  JOIN auth.users au ON p.id = au.id
  WHERE (search_term IS NULL OR 
         p.full_name ILIKE '%' || search_term || '%' OR
         p.username ILIKE '%' || search_term || '%' OR
         au.email ILIKE '%' || search_term || '%' OR
         p.institution ILIKE '%' || search_term || '%')
  ORDER BY au.created_at DESC
  LIMIT page_size
  OFFSET page_offset;
END;
$$;

-- Create function to get user statistics (admin only)
CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  SELECT json_build_object(
    'total_users', (SELECT COUNT(*) FROM auth.users),
    'total_profiles', (SELECT COUNT(*) FROM public.profiles),
    'admin_users', (SELECT COUNT(*) FROM public.profiles WHERE is_admin = true),
    'standard_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'standard' OR accounttype IS NULL),
    'pro_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'pro'),
    'edu_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'edu'),
    'edu_pro_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'edu_pro'),
    'users_with_datasets', (SELECT COUNT(DISTINCT user_id) FROM public.user_datasets),
    'total_datasets', (SELECT COUNT(*) FROM public.user_datasets),
    'users_last_7_days', (SELECT COUNT(*) FROM auth.users WHERE created_at >= NOW() - INTERVAL '7 days'),
    'users_last_30_days', (SELECT COUNT(*) FROM auth.users WHERE created_at >= NOW() - INTERVAL '30 days'),
    'active_users_last_7_days', (SELECT COUNT(*) FROM auth.users WHERE last_sign_in_at >= NOW() - INTERVAL '7 days'),
    'active_users_last_30_days', (SELECT COUNT(*) FROM auth.users WHERE last_sign_in_at >= NOW() - INTERVAL '30 days')
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Create function to update user admin status (admin only)
CREATE OR REPLACE FUNCTION public.update_user_admin_status(
  target_user_id UUID,
  new_admin_status BOOLEAN
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Prevent users from removing their own admin status (safety measure)
  IF target_user_id = auth.uid() AND new_admin_status = false THEN
    RAISE EXCEPTION 'Cannot remove your own admin privileges.';
  END IF;
  
  UPDATE public.profiles 
  SET is_admin = new_admin_status, updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$;

-- Create function to update user account type (admin only)
CREATE OR REPLACE FUNCTION public.update_user_account_type(
  target_user_id UUID,
  new_account_type TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Validate account type
  IF new_account_type NOT IN ('standard', 'pro', 'edu', 'edu_pro') THEN
    RAISE EXCEPTION 'Invalid account type. Must be one of: standard, pro, edu, edu_pro';
  END IF;
  
  UPDATE public.profiles 
  SET accounttype = new_account_type, updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$;

-- Update RLS policies for admin access
-- Allow admins to read all profiles
CREATE POLICY "Admins can read all profiles" ON public.profiles
  FOR SELECT TO authenticated
  USING (public.is_user_admin(auth.uid()));

-- Allow admins to update user profiles (except their own admin status)
CREATE POLICY "Admins can update user profiles" ON public.profiles
  FOR UPDATE TO authenticated
  USING (public.is_user_admin(auth.uid()))
  WITH CHECK (public.is_user_admin(auth.uid()));

-- Update notifications table RLS to restrict admin operations to actual admins
DROP POLICY IF EXISTS "Authenticated users can manage notifications" ON public.notifications;
CREATE POLICY "Only admins can manage notifications" ON public.notifications
  FOR ALL TO authenticated
  USING (public.is_user_admin(auth.uid()));

-- Grant execute permissions on admin functions to authenticated users
-- (The functions themselves check for admin status)
GRANT EXECUTE ON FUNCTION public.is_user_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_admin_users() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_admin_status(UUID, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_account_type(UUID, TEXT) TO authenticated;

-- Create a comment to document the admin setup
COMMENT ON COLUMN public.profiles.is_admin IS 'Indicates if the user has admin privileges. Default is false.';
COMMENT ON FUNCTION public.is_user_admin(UUID) IS 'Checks if a given user ID has admin privileges.';
COMMENT ON FUNCTION public.get_admin_users() IS 'Returns all admin users. Only callable by admins.';
COMMENT ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) IS 'Returns paginated list of all users with search. Only callable by admins.';
COMMENT ON FUNCTION public.get_user_statistics() IS 'Returns comprehensive user statistics. Only callable by admins.';
COMMENT ON FUNCTION public.update_user_admin_status(UUID, BOOLEAN) IS 'Updates admin status for a user. Only callable by admins.';
COMMENT ON FUNCTION public.update_user_account_type(UUID, TEXT) IS 'Updates account type for a user. Only callable by admins.';
