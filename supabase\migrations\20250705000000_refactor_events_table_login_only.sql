-- Migration: Refactor events table to focus only on login tracking
-- This migration cleans up the events table and optimizes it for essential login tracking only
-- Date: 2025-07-05

-- Step 1: Create the events table if it doesn't exist (with proper structure)
CREATE TABLE IF NOT EXISTS public.events (
  id UUID NOT NULL DEFAULT extensions.uuid_generate_v4(),
  user_id UUID NULL,
  event_type TEXT NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  details JSONB NULL,
  CONSTRAINT events_pkey PRIMARY KEY (id),
  CONSTRAINT events_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users (id) ON DELETE CASCADE
);

-- Step 2: Clean up existing non-login events to reduce database bloat
-- Keep only login-related events (app_open, login, signin, etc.)
DELETE FROM public.events 
WHERE event_type NOT IN ('app_open', 'login', 'signin', 'sign_in', 'signed_in');

-- Step 3: Add constraint to only allow login-related event types
-- This prevents future insertion of non-login events
ALTER TABLE public.events 
ADD CONSTRAINT events_event_type_check 
CHECK (event_type IN ('app_open', 'login', 'signin', 'sign_in', 'signed_in'));

-- Step 4: Add indexes for better performance
-- Index on user_id for faster user-specific queries
CREATE INDEX IF NOT EXISTS idx_events_user_id ON public.events(user_id);

-- Index on event_type for faster event type filtering
CREATE INDEX IF NOT EXISTS idx_events_event_type ON public.events(event_type);

-- Composite index on user_id and timestamp for user activity queries
CREATE INDEX IF NOT EXISTS idx_events_user_timestamp ON public.events(user_id, timestamp DESC);

-- Step 5: Enable Row Level Security
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;

-- Step 6: Create RLS policies for the events table
-- Users can only read their own events
DROP POLICY IF EXISTS "Users can read own events" ON public.events;
CREATE POLICY "Users can read own events" ON public.events
  FOR SELECT USING (auth.uid() = user_id);

-- Only authenticated users can insert login events
DROP POLICY IF EXISTS "Authenticated users can insert login events" ON public.events;
CREATE POLICY "Authenticated users can insert login events" ON public.events
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND 
    event_type IN ('app_open', 'login', 'signin', 'sign_in', 'signed_in')
  );

-- Step 7: Grant necessary permissions
GRANT SELECT, INSERT ON public.events TO authenticated;

-- Step 8: Add helpful comments
COMMENT ON TABLE public.events IS 'Stores essential user login events only. Refactored 2025-07-05 to eliminate page visit tracking and reduce database bloat.';
COMMENT ON COLUMN public.events.event_type IS 'Restricted to login-related events only: app_open, login, signin, sign_in, signed_in';
COMMENT ON COLUMN public.events.details IS 'Optional JSON details for login events (e.g., login method, device info)';

-- Step 9: Create a function to get user login statistics (optional utility)
CREATE OR REPLACE FUNCTION public.get_user_login_stats(target_user_id UUID)
RETURNS TABLE (
  total_logins BIGINT,
  first_login TIMESTAMP WITH TIME ZONE,
  last_login TIMESTAMP WITH TIME ZONE,
  logins_last_30_days BIGINT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_logins,
    MIN(timestamp) as first_login,
    MAX(timestamp) as last_login,
    COUNT(*) FILTER (WHERE timestamp >= NOW() - INTERVAL '30 days') as logins_last_30_days
  FROM public.events 
  WHERE user_id = target_user_id 
    AND event_type IN ('app_open', 'login', 'signin', 'sign_in', 'signed_in');
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.get_user_login_stats(UUID) TO authenticated;
