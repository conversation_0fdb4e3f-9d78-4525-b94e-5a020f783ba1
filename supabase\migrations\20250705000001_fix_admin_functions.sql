-- Fix Admin Functions Migration
-- This migration fixes the admin functions to work properly with Supabase client access

-- Drop and recreate the get_all_users function with proper structure
DROP FUNCTION IF EXISTS public.get_all_users(INTEGER, INTEGER, TEXT);

-- Create a function that matches the actual profiles table structure
CREATE OR REPLACE FUNCTION public.get_all_users(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0,
  search_term TEXT DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  email TEXT,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN,
  accounttype TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  last_sign_in_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  -- Return query with actual profile data and safe defaults for missing fields
  RETURN QUERY
  SELECT
    p.id,
    COALESCE(p.username, 'No email available'::TEXT) as email, -- Username is typically email
    p.username,
    p.full_name,
    p.institution,
    p.country,
    p.avatar_url,
    p.updated_at,
    COALESCE(p.is_admin, false) as is_admin,
    COALESCE(p.accounttype, 'standard'::TEXT) as accounttype,
    COALESCE(p.updated_at, NOW()) as created_at, -- Use updated_at as fallback
    NULL::TIMESTAMP WITH TIME ZONE as last_sign_in_at -- Not available in profiles
  FROM public.profiles p
  WHERE (search_term IS NULL OR
         COALESCE(p.full_name, '') ILIKE '%' || COALESCE(search_term, '') || '%' OR
         COALESCE(p.username, '') ILIKE '%' || COALESCE(search_term, '') || '%' OR
         COALESCE(p.institution, '') ILIKE '%' || COALESCE(search_term, '') || '%')
  ORDER BY p.updated_at DESC NULLS LAST
  LIMIT page_size
  OFFSET page_offset;
END;
$$;

-- Update user statistics function to be more robust
DROP FUNCTION IF EXISTS public.get_user_statistics();

CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  total_users_count INTEGER;
  total_profiles_count INTEGER;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  -- Get counts from profiles table (more reliable than auth.users)
  SELECT COUNT(*) INTO total_profiles_count FROM public.profiles;

  -- Use profiles count as total users since we can't access auth.users reliably
  total_users_count := total_profiles_count;

  SELECT json_build_object(
    'total_users', total_users_count,
    'total_profiles', total_profiles_count,
    'admin_users', (SELECT COUNT(*) FROM public.profiles WHERE is_admin = true),
    'standard_users', (SELECT COUNT(*) FROM public.profiles WHERE COALESCE(accounttype, 'standard') = 'standard'),
    'pro_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'pro'),
    'edu_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'edu'),
    'edu_pro_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'edu_pro'),
    'users_with_datasets', (SELECT COUNT(DISTINCT user_id) FROM public.user_datasets WHERE user_datasets.user_id IS NOT NULL),
    'total_datasets', (SELECT COUNT(*) FROM public.user_datasets),
    'users_last_7_days', 0, -- Placeholder since we don't have created_at in profiles
    'users_last_30_days', 0, -- Placeholder since we don't have created_at in profiles
    'active_users_last_7_days', 0, -- Placeholder since we don't have last_sign_in_at in profiles
    'active_users_last_30_days', 0 -- Placeholder since we don't have last_sign_in_at in profiles
  ) INTO result;

  RETURN result;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_statistics() TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) IS 'Returns paginated list of all users with search. Fixed to work with profile data only.';
COMMENT ON FUNCTION public.get_user_statistics() IS 'Returns comprehensive user statistics. Updated to work with profile data.';
