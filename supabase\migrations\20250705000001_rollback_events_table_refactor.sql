-- Rollback Migration: Revert events table refactoring
-- This migration reverts the events table back to its original unrestricted state
-- Use this ONLY if issues arise with the login-only events table
-- Date: 2025-07-05

-- Step 1: Drop the constraint that restricts event types
ALTER TABLE public.events DROP CONSTRAINT IF EXISTS events_event_type_check;

-- Step 2: Drop the login statistics function
DROP FUNCTION IF EXISTS public.get_user_login_stats(UUID);

-- Step 3: Drop the restrictive RLS policies
DROP POLICY IF EXISTS "Users can read own events" ON public.events;
DROP POLICY IF EXISTS "Authenticated users can insert login events" ON public.events;

-- Step 4: Create more permissive RLS policies (original behavior)
CREATE POLICY "Authenticated users can manage their own events" ON public.events
  FOR ALL USING (auth.uid() = user_id);

-- Step 5: Remove comments that reference the refactoring
COMMENT ON TABLE public.events IS 'Stores user events and activity tracking';
COMMENT ON COLUMN public.events.event_type IS 'Type of event being logged';
COMMENT ON COLUMN public.events.details IS 'Optional JSON details for the event';

-- Step 6: Grant broader permissions (original behavior)
GRANT ALL ON public.events TO authenticated;

-- Note: We keep the indexes as they improve performance regardless
-- Note: We don't restore deleted page_view events as they were the source of the problem
-- Note: The application code changes must also be reverted separately
