-- Final Admin Dashboard Fix Migration
-- This migration ensures all admin functions work correctly with the actual database structure

-- First, ensure the is_admin and accounttype columns exist
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS accounttype TEXT DEFAULT 'standard';

-- Create index for admin queries if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_profiles_is_admin ON public.profiles(is_admin) WHERE is_admin = true;

-- First, drop all policies that depend on admin functions
DROP POLICY IF EXISTS "Admins can read all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Admins can update user profiles" ON public.profiles;
DROP POLICY IF EXISTS "Only admins can manage notifications" ON public.notifications;

-- Now drop and recreate all admin functions
DROP FUNCTION IF EXISTS public.get_all_users(INTEGER, INTEGER, TEXT);
DROP FUNCTION IF EXISTS public.get_user_statistics();
DROP FUNCTION IF EXISTS public.get_admin_users();
DROP FUNCTION IF EXISTS public.update_user_admin_status(UUID, BO<PERSON>EAN);
DROP FUNCTION IF EXISTS public.update_user_account_type(UUID, TEXT);

-- Drop is_user_admin function last since other functions might depend on it
DROP FUNCTION IF EXISTS public.is_user_admin(UUID);

-- Recreate is_user_admin function
CREATE OR REPLACE FUNCTION public.is_user_admin(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND COALESCE(is_admin, false) = true
  );
END;
$$;

-- Recreate get_all_users function with exact type matching
CREATE OR REPLACE FUNCTION public.get_all_users(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0,
  search_term TEXT DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  email TEXT,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN,
  accounttype TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  last_sign_in_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Return query with safe type casting and null handling
  RETURN QUERY
  SELECT 
    p.id::UUID, 
    COALESCE(p.username, 'No email available')::TEXT as email,
    p.username::TEXT, 
    p.full_name::TEXT, 
    p.institution::TEXT, 
    p.country::TEXT, 
    p.avatar_url::TEXT, 
    p.updated_at::TIMESTAMP WITH TIME ZONE, 
    COALESCE(p.is_admin, false)::BOOLEAN as is_admin,
    COALESCE(p.accounttype, 'standard')::TEXT as accounttype,
    COALESCE(p.updated_at, NOW())::TIMESTAMP WITH TIME ZONE as created_at,
    NULL::TIMESTAMP WITH TIME ZONE as last_sign_in_at
  FROM public.profiles p
  WHERE (search_term IS NULL OR 
         COALESCE(p.full_name, '') ILIKE '%' || COALESCE(search_term, '') || '%' OR
         COALESCE(p.username, '') ILIKE '%' || COALESCE(search_term, '') || '%' OR
         COALESCE(p.institution, '') ILIKE '%' || COALESCE(search_term, '') || '%')
  ORDER BY COALESCE(p.updated_at, NOW()) DESC
  LIMIT page_size
  OFFSET page_offset;
END;
$$;

-- Recreate get_admin_users function
CREATE OR REPLACE FUNCTION public.get_admin_users()
RETURNS TABLE (
  id UUID,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT p.id, p.username, p.full_name, p.institution, p.country, p.avatar_url, p.updated_at, p.is_admin
  FROM public.profiles p
  WHERE COALESCE(p.is_admin, false) = true
  ORDER BY p.full_name, p.username;
END;
$$;

-- Recreate get_user_statistics function
CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  total_users_count INTEGER;
  total_profiles_count INTEGER;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Get counts from profiles table
  SELECT COUNT(*) INTO total_profiles_count FROM public.profiles;
  total_users_count := total_profiles_count;
  
  SELECT json_build_object(
    'total_users', total_users_count,
    'total_profiles', total_profiles_count,
    'admin_users', (SELECT COUNT(*) FROM public.profiles WHERE COALESCE(is_admin, false) = true),
    'standard_users', (SELECT COUNT(*) FROM public.profiles WHERE COALESCE(accounttype, 'standard') = 'standard'),
    'pro_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'pro'),
    'edu_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'edu'),
    'edu_pro_users', (SELECT COUNT(*) FROM public.profiles WHERE accounttype = 'edu_pro'),
    'users_with_datasets', COALESCE((SELECT COUNT(DISTINCT user_id) FROM public.user_datasets WHERE user_datasets.user_id IS NOT NULL), 0),
    'total_datasets', COALESCE((SELECT COUNT(*) FROM public.user_datasets), 0),
    'users_last_7_days', 0,
    'users_last_30_days', 0,
    'active_users_last_7_days', 0,
    'active_users_last_30_days', 0
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Recreate update_user_admin_status function
CREATE OR REPLACE FUNCTION public.update_user_admin_status(
  target_user_id UUID,
  new_admin_status BOOLEAN
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Prevent users from removing their own admin status
  IF target_user_id = auth.uid() AND new_admin_status = false THEN
    RAISE EXCEPTION 'Cannot remove your own admin privileges.';
  END IF;
  
  UPDATE public.profiles 
  SET is_admin = new_admin_status, updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$;

-- Recreate update_user_account_type function
CREATE OR REPLACE FUNCTION public.update_user_account_type(
  target_user_id UUID,
  new_account_type TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Validate account type
  IF new_account_type NOT IN ('standard', 'pro', 'edu', 'edu_pro') THEN
    RAISE EXCEPTION 'Invalid account type. Must be one of: standard, pro, edu, edu_pro';
  END IF;
  
  UPDATE public.profiles 
  SET accounttype = new_account_type, updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.is_user_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_admin_users() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_admin_status(UUID, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_account_type(UUID, TEXT) TO authenticated;

-- Create RLS policies for admin access (policies were dropped at the beginning)
CREATE POLICY "Admins can read all profiles" ON public.profiles
  FOR SELECT TO authenticated
  USING (public.is_user_admin(auth.uid()));

CREATE POLICY "Admins can update user profiles" ON public.profiles
  FOR UPDATE TO authenticated
  USING (public.is_user_admin(auth.uid()))
  WITH CHECK (public.is_user_admin(auth.uid()));

-- Recreate the notifications policy
CREATE POLICY "Only admins can manage notifications" ON public.notifications
  FOR ALL TO authenticated
  USING (public.is_user_admin(auth.uid()));

-- Add helpful comments
COMMENT ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) IS 'Returns paginated list of all users with search. Fixed for exact type matching.';
COMMENT ON FUNCTION public.get_user_statistics() IS 'Returns comprehensive user statistics with safe null handling.';
COMMENT ON FUNCTION public.is_user_admin(UUID) IS 'Checks if a user has admin privileges with safe null handling.';

-- Final verification query (commented out for production)
-- SELECT 'Admin functions created successfully' as status;
