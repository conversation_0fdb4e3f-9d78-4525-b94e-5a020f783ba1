# Supabase Database Migrations

This directory contains SQL migration scripts for the Supabase database used in the Statistica application.

## Latest Migration: Update Profiles Table Structure

**File:** `20240510_update_profiles_table.sql`

### Changes Made

1. Removed the `gender` field from the `profiles` table as it's no longer used in the registration form
2. Ensured the `country` field exists and is properly configured to store values from the country dropdown
3. Updated the `handle_new_user()` function to match the new structure and properly capture registration metadata

### How to Apply

To apply this migration to your Supabase database:

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Navigate to your project
3. Go to the SQL Editor
4. Copy the contents of the migration file
5. Paste into the SQL Editor and run the script

### Verification

After applying the migration, you can verify the changes by:

1. Checking the structure of the `profiles` table in the Table Editor
2. Creating a new user account to ensure the trigger correctly creates a profile entry
3. Confirming that existing user profiles remain intact

## Database Schema Overview

The application uses the following key tables:

- `auth.users` - Managed by Supabase Auth, contains user authentication information
- `public.profiles` - Custom table that stores additional user profile information

The `profiles` table is linked to `auth.users` via a foreign key on the `id` column, with `ON DELETE CASCADE` to maintain referential integrity.

A database trigger (`on_auth_user_created`) automatically creates a profile record whenever a new user signs up, using metadata provided during registration.