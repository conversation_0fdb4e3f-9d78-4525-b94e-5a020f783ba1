# Fix for User Datasets Row-Level Security Issues

## Problem

Users are encountering the following errors when trying to save datasets to their accounts:

1. `GET https://ghzibvkqmdlpyaidfbah.supabase.co/rest/v1/user_datasets?select=id%2Cfile_path&user_id=eq.96646f1d-d68e-4a31-ace8-3720be4988e3&dataset_name=eq.sample_meta_analysis_dataset 406 (Not Acceptable)`

2. `POST https://ghzibvkqmdlpyaidfbah.supabase.co/storage/v1/object/userdatasets/96646f1d-d68e-4a31-ace8-3720be4988e3/sample_meta_analysis_dataset.json 400 (Bad Request)`

3. `Error uploading new dataset file: {statusCode: '403', error: 'Unauthorized', message: 'new row violates row-level security policy'}`

These errors occur because:

1. The Row-Level Security (RLS) policies for the `user_datasets` table are not properly configured
2. The RLS policies for the `userdatasets` storage bucket are missing or improperly configured

## Solution

We've created a new migration file `20250621160000_fix_user_datasets_and_storage_rls.sql` that:

1. Fixes the RLS policies for the `user_datasets` table
2. Adds proper RLS policies for the `userdatasets` storage bucket

### How to Apply the Fix

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of the `20250621160000_fix_user_datasets_and_storage_rls.sql` file
4. Paste it into the SQL Editor
5. Run the SQL script

Alternatively, you can use the Supabase CLI to apply the migration:

```bash
supabase db push
```

### What the Fix Does

1. **For the `user_datasets` table**:
   - Drops any existing policies
   - Creates a policy for INSERT operations
   - Creates a policy for SELECT, UPDATE, DELETE operations
   - Enables RLS on the table
   - Grants necessary permissions to authenticated users

2. **For the `userdatasets` storage bucket**:
   - Creates policies on the storage.objects table for the userdatasets bucket
   - Defines separate policies for INSERT, SELECT, UPDATE, DELETE operations
   - Each policy ensures users can only access their own files
   - Grants necessary permissions to authenticated users

## Verification

After applying the fix, users should be able to:

1. Save datasets to their accounts
2. View their saved datasets
3. Update their saved datasets
4. Delete their saved datasets

If you continue to experience issues, please check the browser console for error messages and report them to the development team.