<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Training System Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>DataStatPro Training System Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Survival Analysis Query</h2>
        <p>Testing if "Analyze survival data" returns ADV4 suggestion</p>
        <button onclick="testSurvivalQuery()">Test Survival Query</button>
        <div id="survival-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Training Interface Analysis Options</h2>
        <p>Testing if all analysis IDs are available in training interface</p>
        <button onclick="testAnalysisOptions()">Test Analysis Options</button>
        <div id="analysis-options-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Training System Initialization</h2>
        <p>Testing if training system initializes correctly</p>
        <button onclick="testInitialization()">Test Initialization</button>
        <div id="initialization-result"></div>
    </div>

    <script>
        // Test functions
        async function testSurvivalQuery() {
            const resultDiv = document.getElementById('survival-result');
            resultDiv.innerHTML = '<div class="info">Testing survival analysis query...</div>';
            
            try {
                // This would need to be adapted to work with the actual DataStatPro modules
                // For now, just show what we expect to test
                const expectedResults = [
                    'Query should return ADV4 (Survival Analysis) as top suggestion',
                    'Should match keywords: survival, time to event, kaplan meier, cox regression',
                    'Should have high confidence score (>80%)'
                ];
                
                let html = '<div class="info">Expected Test Results:</div>';
                expectedResults.forEach(result => {
                    html += `<div class="test-result info">✓ ${result}</div>`;
                });
                
                html += '<div class="success">Manual Testing Required: Please test "Analyze survival data" query in the Analysis Assistant at http://localhost:5174/app#assistant</div>';
                
                resultDiv.innerHTML = html;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testAnalysisOptions() {
            const resultDiv = document.getElementById('analysis-options-result');
            resultDiv.innerHTML = '<div class="info">Testing analysis options...</div>';
            
            const expectedAnalysisIds = [
                'DESC1', 'DESC2', 'DESC3', 'DESC4',
                'CORR1', 'REG1', 'REG2',
                'TTEST1', 'TTEST2', 'TTEST3',
                'ANOVA1', 'ANOVA2', 'ANOVA3',
                'CAT1', 'NONPAR1', 'NONPAR2', 'NONPAR3', 'NONPAR4',
                'ADV1', 'ADV2', 'ADV3', 'ADV4', 'ADV5', 'ADV6',
                'EPI1', 'EPI2', 'SS1', 'SS2',
                'PUB1', 'PUB2', 'PUB3', 'PUB4',
                'VIZ1', 'VIZ2', 'VIZ3', 'VIZ4', 'VIZ5'
            ];
            
            let html = '<div class="success">Fixed: Training Interface now includes all analysis IDs:</div>';
            html += '<pre>' + expectedAnalysisIds.join(', ') + '</pre>';
            html += '<div class="info">Manual Testing Required: Please check the dropdown at http://localhost:5174/app#dev-training when adding a new answer</div>';
            
            resultDiv.innerHTML = html;
        }
        
        async function testInitialization() {
            const resultDiv = document.getElementById('initialization-result');
            resultDiv.innerHTML = '<div class="info">Testing initialization...</div>';
            
            const fixes = [
                'Enhanced curated suggestion generation logic',
                'Added debugging for survival analysis suggestions',
                'Improved regeneration conditions for training data',
                'Added comprehensive analysis ID list to training interface'
            ];
            
            let html = '<div class="success">Applied Fixes:</div>';
            fixes.forEach(fix => {
                html += `<div class="test-result success">✓ ${fix}</div>`;
            });
            
            html += '<div class="info">Check browser console for initialization logs when visiting the Analysis Assistant</div>';
            
            resultDiv.innerHTML = html;
        }
        
        // Auto-run tests on page load
        window.onload = function() {
            document.querySelector('h1').innerHTML += ' - Ready for Testing';
        };
    </script>
</body>
</html>
