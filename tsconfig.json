{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "outDir": "./dist", "rootDir": "./src", "types": ["vite/client", "jest", "@testing-library/jest-dom"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src", "src/types"], "references": [{"path": "./tsconfig.node.json"}]}