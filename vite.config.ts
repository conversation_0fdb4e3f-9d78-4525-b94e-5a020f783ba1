import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { nodePolyfills } from 'vite-plugin-node-polyfills';
import { VitePWA } from 'vite-plugin-pwa';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    nodePolyfills(),
    VitePWA({
      registerType: 'prompt', // Changed from 'autoUpdate' to 'prompt' for better user control
      injectRegister: 'auto',
      devOptions: {
        enabled: true, // Enable PWA in development for testing
      },
      // Enhanced update strategies
      strategies: 'generateSW',
      injectManifest: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,webmanifest}']
      },
      manifest: {
        name: 'DataStatPro',
        short_name: 'DataStatPro',
        description: 'Comprehensive statistical analysis web application with data visualization capabilities',
        start_url: './',
        display: 'standalone',
        background_color: '#ffffff',
        theme_color: '#1976d2',
        icons: [
          {
            src: './logo.png',
            sizes: '192x192',
            type: 'image/png',
            purpose: 'any maskable'
          },
          {
            src: './logo.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'any maskable'
          }
        ],
        categories: ["education", "productivity", "statistics", "data analysis"],
        screenshots: [
          {
            src: "./assets/screenshots/statistica-dashboard.png",
            sizes: "1280x720",
            type: "image/png"
          }
        ],
        related_applications: [],
        prefer_related_applications: false
      },
      workbox: { // Enhanced workbox configuration for better update handling
        globPatterns: ['**/*.{js,css,html,ico,png,svg,webmanifest}'],
        skipWaiting: false, // Changed to false to allow user control over updates
        clientsClaim: false, // Changed to false for better update control
        maximumFileSizeToCacheInBytes: 50 * 1024 * 1024,
        // Clean up old caches
        cleanupOutdatedCaches: true,
        // Add cache busting for critical resources
        dontCacheBustURLsMatching: /\.\w{8}\./,
        // Enhanced update strategies
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'google-fonts-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
              }
            }
          },
          {
            // Cache API calls with network first strategy
            urlPattern: /^https:\/\/.*\.supabase\.co\/.*$/i,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 60 * 5 // 5 minutes
              },
              networkTimeoutSeconds: 10
            }
          },
          {
            // Cache static assets with cache first strategy
            urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp|ico)$/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'images-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
              }
            }
          },
          {
            // Handle navigation requests with network first, fallback to cache
            urlPattern: /^https?:\/\/[^\/]+\/(?:app|auth|pricing|knowledge-base|tutorials|video-tutorials|which-test|statistical-methods|visualization-guide|privacy-policy|terms-of-service).*$/i,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'pages-cache',
              expiration: {
                maxEntries: 20,
                maxAgeSeconds: 60 * 60 * 24 // 24 hours
              },
              networkTimeoutSeconds: 3
            }
          }
        ],
        // Enhanced navigation fallback - CRITICAL FIX for SPA routing
        navigateFallback: 'index.html',
        navigateFallbackAllowlist: [
          // Allow all routes to fallback to index.html for SPA routing
          /^(?!\/__).*/
        ],
        navigateFallbackDenylist: [
          // Exclude service worker files and API endpoints
          /^\/__/,
          /^\/api\//,
          /\.(?:js|css|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot|json|xml|txt)$/
        ],
      },
    }),
  ],
  publicDir: 'public', // Treat the root directory as the public directory
  // Define global constants
  define: {
    // Buffer polyfill will be handled by alias or other mechanisms if needed
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __BUILD_DATE__: JSON.stringify(new Date().toISOString()),
  },
  server: {
    hmr: {
      overlay: false
    }
  },
  resolve: {
    alias: [
      { find: '@', replacement: '/src' },
      { find: 'buffer', replacement: 'buffer/' }, // Standard alias for the buffer package
      { find: 'stream', replacement: 'stream-browserify' }, // Polyfill for stream
      { find: 'css-globalThis-keywords', replacement: '/src/utils/empty-module.js' }, // Shim for css-globalThis-keywords
    ],
  },
  base: './', // Use relative paths for assets to work in subdirectories
  // Add this for proper routing support
  preview: {
    port: 4173,
    host: true
  },
  
  // Ensure proper fallback for SPA routing
  build: {
    outDir: 'deploy_assets', // Output directory
    assetsDir: 'assets', // Assets directory
    emptyOutDir: true, // Clean the output directory before build
    sourcemap: false, // Disable sourcemaps for production
    minify: 'esbuild', // Use esbuild for minification
    cssCodeSplit: true, // Split CSS into chunks
    // Skip TypeScript checking during build
    commonjsOptions: {
      transformMixedEsModules: true,
    },
    rollupOptions: {
      output: {
        manualChunks: {
          'react-libs': ['react', 'react-dom'],
          'mui-libs': ['@mui/material', '@mui/icons-material', '@emotion/react', '@emotion/styled'],
          'charts-recharts-d3': ['recharts', 'd3'],
          'charts-plotly': ['plotly.js', 'react-plotly.js'],
          'ml-tensorflow': ['@tensorflow/tfjs'],
          'math-lib': ['mathjs'],
          'supabase-lib': ['@supabase/supabase-js'],
          'other-utils': [
            'jstat',
            'papaparse',
            'ml-matrix',
            'ml-regression-simple-linear',
            'uuid',
            'html2canvas',
            'globalthis'
          ]
        }
      }
    }
  }
});
